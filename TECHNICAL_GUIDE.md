# Technical Guide: Data Flow and Kafka Integration

This document provides detailed technical information about how data flows through the system, Kafka message formats, and Flink SQL processing.

## 📡 Kafka Data Flow Architecture

### Overview
```
Data Sources → Kafka Topics → Flink SQL Jobs → ClickHouse Tables
```

## 🔄 Data Sources and Kafka Topics

### 1. IoT Station Status Messages

**Source**: Python IoT Generator (`fake-iot-generator-python/main.py`)

**Kafka Topic**: `cf.iot.station.status`

**Message Format**:
```json
{
  "id": "6043580e-8a25-3389-9548-247eddefddf5",
  "status": "on"
}
```

**Python Code Sending to Kafka**:
```python
def random_payload_generator_situation(connection: psycopg2.extensions.connection, 
                                     producer: KafkaProducer) -> None:
    # Fetch station IDs from database
    cursor = connection.cursor()
    cursor.execute("SELECT id FROM stations LIMIT 100")
    station_ids = [row[0] for row in cursor.fetchall()]
    
    topic = os.getenv("KAFKA_TOPIC", "cf.iot.station.status")
    
    for station_id in station_ids:
        # Create status message
        station_status = StationStatus(
            station_id=str(station_id),
            status="on" if twenty_five_percent_is_true() else "off"
        )
        
        # Send to Kafka
        message = json.dumps(station_status.to_dict())
        producer.send(topic, value=message)
```

**Key Configuration**:
```python
# Kafka Producer Setup
producer_config = {
    'bootstrap_servers': f'{kafka_host}:{kafka_port}',
    'key_serializer': lambda x: x.encode('utf-8') if x else None,
    'value_serializer': lambda x: x.encode('utf-8')
}
producer = KafkaProducer(**producer_config)
```

### 2. CDC (Change Data Capture) Messages

**Source**: Debezium PostgreSQL Connector

**Kafka Topics**: 
- `postgres.public.stations`
- `postgres.public.addresses`

**Message Format** (Debezium CDC):
```json
{
  "before": null,
  "after": {
    "id": "station-uuid",
    "imei": "device-imei",
    "serial_number": "device-serial",
    "address_id": "address-uuid"
  },
  "source": {
    "version": "2.4.0.Final",
    "connector": "postgresql",
    "name": "postgres",
    "ts_ms": 1756724300000
  },
  "op": "c",
  "ts_ms": 1756724300000
}
```

## 🔧 Flink SQL Job Processing

### 1. IoT Data Processing Job

**Flink Job**: `countOffline()` method in `DataStreamJob.java`

**How Flink Reads Kafka**:
```java
// Create Kafka source table
tEnv.executeSql("CREATE TABLE station_status (\n" +
    "    id STRING,\n" +
    "    status STRING,\n" +
    "    ts AS PROCTIME()\n" +
    ") WITH (\n" +
    "    'connector' = 'kafka',\n" +
    "    'topic' = 'cf.iot.station.status',\n" +
    "    'properties.bootstrap.servers' = 'redpanda:9092',\n" +
    "    'properties.group.id' = 'test-group',\n" +
    "    'scan.startup.mode' = 'latest-offset',\n" +
    "    'format' = 'json'\n" +
    ")");
```

**Window Aggregation Processing**:
```java
// Create view for filtering offline status
tEnv.executeSql("CREATE VIEW station_status_view AS SELECT id, status, ts FROM station_status WHERE status = 'off'");

// Tumbling window aggregation
Table offlineResult = tEnv.sqlQuery(
    "SELECT window_start, window_end, id as station_id, COUNT(1) as the_number_of_offline_status\n" +
    "from table(\n" +
    "   TUMBLE(TABLE station_status_view, DESCRIPTOR(ts), INTERVAL '" + interval + "' SECONDS))\n" +
    " WHERE status = 'off'\n" +
    " GROUP BY window_start, window_end, GROUPING SETS ((id), ());"
);
```

**ClickHouse Sink Configuration**:
```java
// Create ClickHouse sink table
tEnv.executeSql("CREATE TABLE station_offline_status (\n" +
    "    station_id STRING,\n" +
    "    event_date TIMESTAMP(3),\n" +
    "    offline_count INT\n" +
    ") WITH (\n" +
    "    'connector' = 'clickhouse',\n" +
    "    'url' = 'clickhouse://clickhouse:8123',\n" +
    "    'database-name' = 'cf',\n" +
    "    'table-name' = 'station_status_merge_tree'\n" +
    ")");
```

### 2. CDC Processing Job

**Flink Job**: `debeziumPostgresqlJob()` method

**Reading CDC Data from Kafka**:
```java
// Create CDC source tables
tEnv.executeSql("CREATE TABLE stations (\n" +
    "    id STRING,\n" +
    "    imei STRING,\n" +
    "    serial_number STRING,\n" +
    "    address_id STRING\n" +
    ") WITH (\n" +
    "    'connector' = 'kafka',\n" +
    "    'topic' = 'postgres.public.stations',\n" +
    "    'properties.bootstrap.servers' = 'redpanda:9092',\n" +
    "    'properties.group.id' = 'test-group',\n" +
    "    'scan.startup.mode' = 'earliest-offset',\n" +
    "    'format' = 'debezium-json'\n" +
    ")");
```

**Geospatial Data Processing**:
```java
// Join stations with addresses and convert PostGIS geometry
Table station_address = tEnv.sqlQuery(
    "SELECT stations.id, stations.imei, stations.serial_number, " +
    "ConvertStringToGeoPoint(view_addresses.location) \n" +
    "FROM stations LEFT JOIN view_addresses ON stations.address_id = view_addresses.id"
);
```

## 🗄️ ClickHouse Table Structures

### 1. Station Status Data

**Table**: `cf.station_status_merge_tree`

**Schema**:
```sql
CREATE TABLE cf.station_status_merge_tree (
    station_id String,
    event_date DateTime,
    offline_count UInt32
) ENGINE = MergeTree()
ORDER BY (station_id, event_date);
```

**Sample Data**:
```
station_id                              event_date           offline_count
0fe8d0c8-2076-3de6-95c8-ee9672c192bd   2025-09-01 11:09:40  2
1217a8be-112f-36f2-b58b-f432bb8b03c4   2025-09-01 11:09:40  1
```

### 2. Supporting Tables

**Stream Table**: `cf.station_status` (Kafka connector)
**View**: `cf.station_status_view` (Materialized view)

## 🔍 Message Flow Tracing

### 1. Trace IoT Messages

```bash
# Monitor Python generator output
docker-compose logs fake-iot-generator -f

# Check Kafka topic
docker-compose exec redpanda rpk topic consume cf.iot.station.status --num 10

# Verify Flink processing
docker-compose logs jobmanager | grep "station_status"

# Check ClickHouse results
docker-compose exec clickhouse clickhouse-client --query "SELECT * FROM cf.station_status_merge_tree ORDER BY event_date DESC LIMIT 5"
```

### 2. Trace CDC Messages

```bash
# Check CDC topics
docker-compose exec redpanda rpk topic list | grep postgres

# Monitor CDC messages
docker-compose exec redpanda rpk topic consume postgres.public.stations --num 5

# Verify Debezium connector
curl -s http://localhost:8083/connectors/postgres-connector/status
```

## ⚙️ Configuration Parameters

### Kafka Configuration

**Bootstrap Servers**: `redpanda:9092` (internal), `localhost:19092` (external)

**Consumer Groups**:
- `test-group`: Flink SQL jobs
- Auto-generated: Python producer

**Topics**:
- `cf.iot.station.status`: IoT status messages
- `postgres.public.stations`: Station CDC
- `postgres.public.addresses`: Address CDC

### Flink Configuration

**Parallelism**: Configured via TaskManager slots (20 slots)

**Checkpointing**: Default configuration

**State Backend**: Memory (for demo purposes)

**Window Configuration**:
- Tumbling windows: 5 seconds (configurable)
- Processing time semantics
- Late data handling: Default

## 🚨 Error Handling and Monitoring

### Common Data Flow Issues

1. **Kafka Connection Failures**:
   ```bash
   # Check Kafka connectivity
   docker-compose exec fake-iot-generator python -c "from kafka import KafkaProducer; print('Kafka OK')"
   ```

2. **Flink Job Failures**:
   ```bash
   # Check job status
   curl -s http://localhost:8081/jobs | jq '.jobs[].status'
   ```

3. **ClickHouse Write Issues**:
   ```bash
   # Check ClickHouse logs
   docker-compose logs clickhouse | grep ERROR
   ```

### Performance Monitoring

**Metrics to Watch**:
- Kafka lag: `docker-compose exec redpanda rpk group describe test-group`
- Flink throughput: Check Web UI at http://localhost:8081
- ClickHouse ingestion rate: Query `system.parts` table

## 🔧 Customization Guide

### Modify Message Format

1. **Update Python Generator**:
   ```python
   # In main.py, modify StationStatus class
   def to_dict(self):
       return {
           "id": self.id,
           "status": self.status,
           "timestamp": datetime.now().isoformat(),  # Add timestamp
           "location": {"lat": 0.0, "lon": 0.0}     # Add location
       }
   ```

2. **Update Flink SQL Schema**:
   ```java
   // Add new fields to table definition
   "CREATE TABLE station_status (\n" +
   "    id STRING,\n" +
   "    status STRING,\n" +
   "    timestamp TIMESTAMP(3),\n" +
   "    location ROW<lat DOUBLE, lon DOUBLE>,\n" +
   "    ts AS PROCTIME()\n" +
   ") WITH (..."
   ```

### Add New Processing Logic

1. **Create new Flink job method**
2. **Define source and sink tables**
3. **Implement SQL transformation logic**
4. **Submit job with new parameters**

This technical guide provides the foundation for understanding and extending the data pipeline architecture.
