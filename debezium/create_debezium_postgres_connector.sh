#! /bin/bash

docker compose exec debezium curl -H 'Content-Type: application/json' debezium:8083/connectors --data '
{
  "name": "postgres-connector",
  "config": {
    "connector.class": "io.debezium.connector.postgresql.PostgresConnector",
    "plugin.name": "pgoutput",
    "database.hostname": "postgres",
    "database.port": "5432",
    "database.user": "admin",
    "database.password": "admin",
    "database.dbname" : "service-rental-demo",
    "database.server.name": "postgres",
    "skipped.operations": "d,r",
    "table.include.list": "public.stations,public.addresses",
    "topic.prefix" : "cf.debezium",
    "tombstones.on.delete": "false",
    "value.converter": "org.apache.kafka.connect.json.JsonConverter",
    "value.converter.schemas.enable": false
  }
}'
