#/bin/bash
#Cancels all the flink jobs. Run in the $FLINK_DIR/bin
#@Author: <PERSON><PERSON>g

echo -e "Cancelling all flink jobs"
echo -e "*************************"

JOB_LIST=`docker exec -it jobmanager flink list | awk {'print $4'} | egrep  '^\w+$'`

for i in $JOB_LIST
    do
        echo -e Cancelling $i
	docker exec -it jobmanager flink cancel $i
    done

echo -e "*************************"
echo -e "All jobs cancelled successfully"
