version: '3.7'
volumes:
  redpanda: null
services:
  # Flink cluster
  jobmanager:
    container_name: jobmanager
    build:
      context: ./flink-docker-file
      dockerfile: Dockerfile
    ports:
      - 8081:8081
    command: jobmanager
    volumes:
      - ./quickstart-0.1.jar:/opt/flink/output/flink-job.jar
    environment:
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: jobmanager        

  taskmanager:
    container_name: taskmanager
    build:
      context: ./flink-docker-file
      dockerfile: Dockerfile
    depends_on:
      - jobmanager
    command: taskmanager
    scale: 1
    environment:
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: jobmanager
        taskmanager.numberOfTaskSlots: 20
       
  sql-client:
    container_name: sql-client
    build:
      context: ./flink-docker-file
      dockerfile: Dockerfile
    command:
      - /opt/flink/bin/sql-client.sh
      - embedded
      - -l
      - /opt/sql-client/lib
    depends_on:
      - jobmanager
    environment:
      - |
        FLINK_PROPERTIES=
        jobmanager.rpc.address: jobmanager
        rest.address: jobmanager
    volumes:
      - ./data/sql:/etc/sql

  postgres:
    image: debezium/postgres:16
    container_name: postgres
    ports:
      - 5432:5432
    environment:
      - POSTGRES_USER=admin
      - POSTGRES_PASSWORD=admin
      - POSTGRES_DB=service-rental-demo
      - PGPASSWORD=postgrespw
    volumes:
      - ./data:/docker-entrypoint-initdb.d

  redpanda:
    image: docker.redpanda.com/redpandadata/redpanda:v23.3.9
    container_name: redpanda
    command:
      - redpanda start
      # Mode dev-container uses well-known configuration properties for development in containers.
      - --mode dev-container
      # Tells Seastar (the framework Redpanda uses under the hood) to use 1 core on the system.
      - --smp 1
      - --kafka-addr internal://0.0.0.0:9092,external://0.0.0.0:19092
      # Address the broker advertises to clients that connect to the Kafka API.
      # Use the internal addresses to connect to the Redpanda brokers
      # from inside the same Docker network.
      # Use the external addresses to connect to the Redpanda brokers
      # from outside the Docker network.
      - --advertise-kafka-addr internal://redpanda:9092,external://localhost:19092
      - --pandaproxy-addr internal://0.0.0.0:8082,external://0.0.0.0:18082
      # Address the broker advertises to clients that connect to the HTTP Proxy.
      - --advertise-pandaproxy-addr internal://redpanda:8082,external://localhost:18082
      - --schema-registry-addr internal://0.0.0.0:8081,external://0.0.0.0:18081
      # Redpanda brokers use the RPC API to communicate with each other internally.
      - --rpc-addr redpanda:33145
      - --advertise-rpc-addr redpanda:33145
    ports:
      - 18081:18081
      - 18082:18082
      - 19092:19092
      - 19644:9644
    volumes:
      - redpanda:/var/lib/redpanda/data
    healthcheck:
      test: ["CMD-SHELL", "rpk cluster health | grep -E 'Healthy:.+true' || exit 1"]
      interval: 15s
      timeout: 3s
      retries: 5
      start_period: 5s

  debezium:
    image: debezium/connect:2.4
    container_name: debezium
    environment:
      BOOTSTRAP_SERVERS: redpanda:9092
      GROUP_ID: 1
      CONFIG_STORAGE_TOPIC: connect_configs
      OFFSET_STORAGE_TOPIC: connect_offsets
      KEY_CONVERTER: org.apache.kafka.connect.storage.StringConverter
      VALUE_CONVERTER: org.apache.kafka.connect.storage.StringConverter
    depends_on: [postgres, redpanda]
    ports:
      - 8083:8083
      
  # Simulate data from iot generator
  fake-iot-generator:
    container_name: fake-iot-generator
    build:
      context: ./fake-iot-generator
      dockerfile: Dockerfile
    command: java -jar /app/fake-iot-generator-1.0-SNAPSHOT-all.jar   
    environment:
      KAFKA_HOST: redpanda
      KAFKA_PORT: 9092
      KAFKA_TOPIC: cf.iot.station.status
      DATABASE_HOST: postgres
      DATABASE_PORT: 5432
      DATABASE_NAME: service-rental-demo
      DATABASE_USERNAME: admin
      DATABASE_PASSWORD: admin
      DELAY: 200
      MODE: RANDOM_GEN

  clickhouse:
    image: clickhouse/clickhouse-server
    container_name: clickhouse
    ports:
      - "8123:8123" # HTTP interface
      - "9000:9000" # Native client interface (used by clickhouse-client)
      - "9009:9009" # GRPC interface (optional)
    volumes:
      - ./clickhouse-data:/docker-entrypoint-initdb.d

  grafana:
    image: grafana/grafana
    container_name: grafana
    depends_on:
      - clickhouse
    ports:
      - "3000:3000"
    volumes:
      - ./grafana_data/grafana:/var/lib/grafana
    environment:
      GF_SECURITY_ADMIN_PASSWORD: "secret" # Change this!
      GF_INSTALL_PLUGINS: "grafana-clickhouse-datasource"  # ClickHouse plugin for Grafana
