CREATE DATABASE IF NOT EXISTS cf;

CREATE TABLE IF NOT EXISTS cf.station_status
(
    station_id String,
    window_start DateTime,
    window_end DateTime,
    the_number_of_offline_status UInt32
) ENGINE = Kafka()
SETTINGS
    kafka_broker_list = 'redpanda:9092',
    kafka_topic_list = 'cf.data_platform.station.status',
    kafka_group_name = 'clickhouse-group',
    kafka_format = 'JSONEachRow';

CREATE TABLE cf.station_status_merge_tree (
    station_id String,
    event_date DateTime,
    offline_count UInt32
) ENGINE = MergeTree()
ORDER BY (event_date, station_id);

CREATE MATERIALIZED VIEW cf.station_status_view TO cf.station_status_merge_tree AS
SELECT
    station_id,
    window_end as event_date,
    the_number_of_offline_status as offline_count
FROM cf.station_status
SETTINGS
stream_like_engine_allow_direct_select = 1;


CREATE TABLE IF NOT EXISTS  cf.stations  
Engine = MergeTree()
Order by id
as select * 
FROM postgresql('postgres','service-rental-demo','stations','admin','admin')
