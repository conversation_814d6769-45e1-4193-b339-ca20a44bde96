package org.example

import com.google.gson.Gson
import org.apache.kafka.clients.producer.KafkaProducer
import org.apache.kafka.clients.producer.ProducerConfig
import org.apache.kafka.clients.producer.ProducerRecord
import java.sql.Connection
import java.sql.DriverManager
import java.time.OffsetTime
import java.util.*
import kotlin.random.Random

data class StationStatus(
    /**
     * The unique identifier of the station entity. Defaults to a random UUID if not specified.
     */
    val id: UUID = UUID.randomUUID(),

    val status: String
)

fun `25PercentageIsTrue`(): Boolean {
    return !(Random.nextBoolean() && Random.nextBoolean())
}

fun prepareProducerConfig(
    kafkaHost: String,
    kafkaPort: String
): KafkaProducer<String, String> {

    val producerProps = mapOf<String, String>(
        ProducerConfig.BOOTSTRAP_SERVERS_CONFIG to "${kafkaHost}:${kafkaPort}",
        ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG to "org.apache.kafka.common.serialization.StringSerializer",
        ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG to "org.apache.kafka.common.serialization.StringSerializer",
    )
    return KafkaProducer<String, String>(producerProps)
}

fun prepareDBConnection(): Pair<Connection, String> {
    val dbHost = System.getenv("DATABASE_HOST") ?: "localhost"
    val dbPort = System.getenv("DATABASE_PORT") ?: "5432"
    val dbName = System.getenv("DATABASE_NAME") ?: "service-rental-demo"
    val dbUsername = System.getenv("DATABASE_USERNAME") ?: "admin"
    val dbPassword = System.getenv("DATABASE_PASSWORD") ?: "admin"

    val jdbcUrl = "jdbc:postgresql://${dbHost}:${dbPort}/${dbName}"
    // get the connection
    return DriverManager
        .getConnection(jdbcUrl, dbUsername, dbPassword) to jdbcUrl
}

fun randomPayloadGeneratorSituation(
    connection: Connection,
    producer: KafkaProducer<String, String>
) {
    // an empty list for holding the results
    val query = connection.prepareStatement("SELECT * FROM stations limit 100")
    val result = query.executeQuery()
    val stationIds = mutableListOf<String>()
    while (result.next()) {
        val id = result.getString("id")
        stationIds.add(id)
    }

    val topic = System.getenv("KAFKA_TOPIC") ?: "cf.iot.station.status"
    val delay = System.getenv("DELAY")?.toLongOrNull() ?: 300L

    println("Start sending station log with [topic=${topic}, delay=${delay}ms]")
    val gson = Gson()
    repeat(1000) {
        stationIds.shuffle()
        for (stationId in stationIds) {
            println("Start sending data to topic ${topic}")
            val stationStatus = StationStatus(
                id = UUID.fromString(stationId),
                status = if (`25PercentageIsTrue`()) "on" else "off"
            )
            producer.send(
                ProducerRecord(
                    topic,
                    gson.toJson(stationStatus)
                )
            )
            println("Send station[id=${stationStatus.id},status=${stationStatus.status}] successfully")
            Thread.sleep(delay)
        }
    }
    println("End sending station log")
    producer.flush()
    producer.close()
}

fun oneStationPayloadGeneratorSituation(
    connection: Connection,
    producer: KafkaProducer<String, String>
) {
    // an empty list for holding the results
    val stationId = connection.prepareStatement("SELECT * FROM stations limit 1")
        .executeQuery()
        .let {
            it.next()
            it.getString("id")
        }!!

    val topic = System.getenv("KAFKA_TOPIC") ?: "cf.iot.station.status"
    val delay = System.getenv("DELAY")?.toLongOrNull() ?: 300L

    println("Start sending station log with [topic=${topic}, delay=${delay}ms]")
    val gson = Gson()
    repeat(10) {
        println("Start sending data to topic ${topic}")
        val stationStatus = StationStatus(
            id = UUID.fromString(stationId),
            status = "off"
        )
        producer.send(
            ProducerRecord(
                topic,
                gson.toJson(stationStatus)
            )
        )
        println("Send station[id=${stationStatus.id},status=${stationStatus.status}], timestamp=${OffsetTime.now()} successfully")
        Thread.sleep(500)
    }
    println("End sending station log")
    producer.flush()
    producer.close()
}

private const val RANDOM_GEN = "RANDOM_GEN"
private const val ONE_GEN = "ONE_GEN"


fun main() {
    val kafkaHost = System.getenv("KAFKA_HOST") ?: "localhost"
    val kafkaPort = System.getenv("KAFKA_PORT") ?: "19092"
    val producer = prepareProducerConfig(
        kafkaHost = kafkaHost,
        kafkaPort = kafkaPort
    )
    val (connection, jdbcUrl) = prepareDBConnection()


    // prints true if the connection is valid
    println("Connect ${jdbcUrl} successfully")
    println("Connect ${kafkaHost}:${kafkaPort} successfully")

    // the query is only prepared not executed
    val mode = System.getenv("MODE") ?: RANDOM_GEN
    if (mode == RANDOM_GEN) {
        randomPayloadGeneratorSituation(
            producer = producer,
            connection = connection
        )
    } else if (mode == ONE_GEN)  {
        oneStationPayloadGeneratorSituation(
            producer = producer,
            connection = connection
        )
    } else {
        println("We do not have mode ${mode}")
    }
}