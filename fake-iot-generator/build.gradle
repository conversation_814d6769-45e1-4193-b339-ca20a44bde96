

plugins {
    id 'java'
    id 'application'
    // shadow plugin to produce fat JARs
    id 'com.github.johnrengelman.shadow' version '7.1.2'
    id 'org.jetbrains.kotlin.jvm' version '1.9.21'
}

repositories {
    mavenCentral()
}

group = 'org.example'
version = '1.0-SNAPSHOT'
mainClassName = 'org.example.MainKt'


dependencies {
    testImplementation 'org.jetbrains.kotlin:kotlin-test'
    // https://mvnrepository.com/artifact/org.jetbrains.kotlin/kotlin-stdlib
    implementation "org.postgresql:postgresql:42.3.1"
    implementation "org.apache.kafka:kafka-clients:3.4.0"
    implementation "com.google.code.gson:gson:2.10.1"
}

jar {
    manifest {
        attributes 'Built-By': System.getProperty('user.name'),
                'Build-Jdk': System.getProperty('java.version')
    }
}

test {
    useJUnitPlatform()
}
kotlin {
    jvmToolchain(17)
}