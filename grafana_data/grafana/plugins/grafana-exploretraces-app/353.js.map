{"version": 3, "file": "353.js", "mappings": "mKAAA,IAAIA,EAAgB,CACnB,iBAAkB,CAClBC,WAAY,CACX,oBAAqB,CACpB,uBAAwB,wBACxB,iBAAkB,mBAClB,yBAA0B,yBAE3B,yBAA0B,CACzB,sBAAuB,0BACvB,mBAAoB,8BAErB,gBAAiB,CAChBC,QAAS,+CACTC,SAAU,2BACVC,MAAO,WAER,wBAAyB,CACxB,wBAAyB,QACzB,sBAAuB,SACvB,sBAAuB,SAExB,iBAAkB,CACjB,iBAAkB,WAClB,cAAe,SACf,uBAAwB,UAEzB,iBAAkB,CACjB,eAAgB,OAChB,aAAc,SAEf,oCAAqC,CACpC,eAAgB,KAChB,iBAAkB,gBAEnBC,SAAU,CACT,gCAAiC,cAElC,YAAa,CACZD,MAAO,CACNA,MAAO,OAGT,2BAA4B,CAC3BE,QAAS,MAEV,qBAAsB,CACrB,uBAAwB,kBACxB,sCAAuC,4BAExC,yBAA0B,CACzB,2DAA4D,4DAC5D,kBAAmB,+BAGrBC,MAAO,CACN,iBAAkB,CACjB,iBAAkB,MAEnB,oBAAqB,CACpB,uBAAwB,UAG1BC,UAAW,CACV,kBAAmB,CAClB,kCAAmC,aAEpC,wBAAyB,CACxB,wBAAyB,QACzB,mBAAoB,SAErB,yBAA0B,CACzB,2BAA4B,QAC5B,aAAc,CACb,2BAA4B,UAE7B,qBAAsB,SACtB,sBAAuB,QACvB,eAAgB,CACf,2BAA4B,SAG9B,uBAAwB,CACvBC,MAAO,CACN,QAAW,QAGb,8CAA+C,CAC9C,mBAAoB,MACpBC,QAAS,6CACT,gDAAiD,8BAElD,kBAAmB,CAClBC,QAAS,CACRC,YAAa,CACZ,wBAAyB,YACzB,uBAAwB,cACxB,gCAAiC,oBACjC,oDAAqD,kDACrD,0BAA2B,gBAC3B,uBAAwB,cACxB,mBAAoB,YACpB,mDAAoD,uCACpD,uBAAwB,eACxB,kDAAmD,iCACnD,iCAAkC,kBAClC,oCAAqC,oBAIxC,6BAA8B,CAC7B,+BAAgC,gBAChC,6BAA8B,iBAE/B,oBAAqB,CACpB,2BAA4B,QAE7B,8BAA+B,CAC9B,kBAAmB,cAEpB,2BAA4B,CAC3BC,MAAO,MAER,yBAA0B,CACzB,mBAAoB,YAErB,4BAA6B,CAC5B,6CAA8C,sCAE/C,uCAAwC,CACvCT,MAAO,CACNU,MAAO,UAGT,gBAAiB,CAChBC,KAAM,CACLA,KAAM,QAGR,uBAAwB,CACvB,0BAA2B,QAE5B,wBAAyB,CACxB,2BAA4B,U", "sources": ["webpack://grafana-exploretraces-app/../node_modules/@grafana/scenes/dist/esm/locales/ko-KR/grafana-scenes.json.js"], "sourcesContent": ["var grafanaScenes = {\n\t\"grafana-scenes\": {\n\tcomponents: {\n\t\t\"adhoc-filter-pill\": {\n\t\t\t\"edit-filter-with-key\": \"{{keyLabel}} 키로 필터 편집\",\n\t\t\t\"managed-filter\": \"{{origin}} 관리 필터\",\n\t\t\t\"remove-filter-with-key\": \"{{keyLabel}} 키로 필터 제거\"\n\t\t},\n\t\t\"adhoc-filters-combobox\": {\n\t\t\t\"remove-filter-value\": \"필터 값 제거 - {{itemLabel}}\",\n\t\t\t\"use-custom-value\": \"사용자 지정 값 사용: {{itemLabel}}\"\n\t\t},\n\t\t\"fallback-page\": {\n\t\t\tcontent: \"링크를 사용하여 여기로 이동한 경우 이 애플리케이션에 버그가 있을 수 있습니다.\",\n\t\t\tsubTitle: \"URL이 어떤 페이지와도 일치하지 않습니다.\",\n\t\t\ttitle: \"찾을 수 없음\"\n\t\t},\n\t\t\"nested-scene-renderer\": {\n\t\t\t\"collapse-button-label\": \"장면 접기\",\n\t\t\t\"expand-button-label\": \"장면 펼치기\",\n\t\t\t\"remove-button-label\": \"장면 제거\"\n\t\t},\n\t\t\"scene-debugger\": {\n\t\t\t\"object-details\": \"객체 상세 정보\",\n\t\t\t\"scene-graph\": \"장면 그래프\",\n\t\t\t\"title-scene-debugger\": \"장면 디버거\"\n\t\t},\n\t\t\"scene-grid-row\": {\n\t\t\t\"collapse-row\": \"행 접기\",\n\t\t\t\"expand-row\": \"행 펼치기\"\n\t\t},\n\t\t\"scene-time-range-compare-renderer\": {\n\t\t\t\"button-label\": \"비교\",\n\t\t\t\"button-tooltip\": \"시간 범위 비교 활성화\"\n\t\t},\n\t\tsplitter: {\n\t\t\t\"aria-label-pane-resize-widget\": \"창 크기 조정 위젯\"\n\t\t},\n\t\t\"viz-panel\": {\n\t\t\ttitle: {\n\t\t\t\ttitle: \"제목\"\n\t\t\t}\n\t\t},\n\t\t\"viz-panel-explore-button\": {\n\t\t\texplore: \"탐색\"\n\t\t},\n\t\t\"viz-panel-renderer\": {\n\t\t\t\"loading-plugin-panel\": \"플러그인 패널 로딩 중...\",\n\t\t\t\"panel-plugin-has-no-panel-component\": \"패널 플러그인에 패널 구성 요소가 없습니다.\"\n\t\t},\n\t\t\"viz-panel-series-limit\": {\n\t\t\t\"content-rendering-series-single-panel-impact-performance\": \"하나의 패널에 너무 많은 시리즈를 렌더링하면 성능에 영향을 주고 데이터가 읽기 어려워질 수 있습니다. \",\n\t\t\t\"warning-message\": \"{{seriesLimit}}개 시계열만 표시 중\"\n\t\t}\n\t},\n\tutils: {\n\t\t\"controls-label\": {\n\t\t\t\"tooltip-remove\": \"제거\"\n\t\t},\n\t\t\"loading-indicator\": {\n\t\t\t\"content-cancel-query\": \"쿼리 취소\"\n\t\t}\n\t},\n\tvariables: {\n\t\t\"ad-hoc-combobox\": {\n\t\t\t\"aria-label-edit-filter-operator\": \"필터 연산자 편집\"\n\t\t},\n\t\t\"ad-hoc-filter-builder\": {\n\t\t\t\"aria-label-add-filter\": \"필터 추가\",\n\t\t\t\"title-add-filter\": \"필터 추가\"\n\t\t},\n\t\t\"ad-hoc-filter-renderer\": {\n\t\t\t\"aria-label-remove-filter\": \"필터 제거\",\n\t\t\t\"key-select\": {\n\t\t\t\t\"placeholder-select-label\": \"레이블 선택\"\n\t\t\t},\n\t\t\t\"label-select-label\": \"레이블 선택\",\n\t\t\t\"title-remove-filter\": \"필터 제거\",\n\t\t\t\"value-select\": {\n\t\t\t\t\"placeholder-select-value\": \"값 선택\"\n\t\t\t}\n\t\t},\n\t\t\"data-source-variable\": {\n\t\t\tlabel: {\n\t\t\t\t\"default\": \"기본값\"\n\t\t\t}\n\t\t},\n\t\t\"default-group-by-custom-indicator-container\": {\n\t\t\t\"aria-label-clear\": \"지우기\",\n\t\t\ttooltip: \"이 대시보드에서 기본적으로 적용됩니다. 편집하면 다른 대시보드로 이전됩니다.\",\n\t\t\t\"tooltip-restore-groupby-set-by-this-dashboard\": \"이 대시보드에서 설정한 '그룹별'을 복원합니다.\"\n\t\t},\n\t\t\"format-registry\": {\n\t\t\tformats: {\n\t\t\t\tdescription: {\n\t\t\t\t\t\"commaseparated-values\": \"쉼표로 구분된 값\",\n\t\t\t\t\t\"double-quoted-values\": \"큰 따옴표로 묶인 값\",\n\t\t\t\t\t\"format-date-in-different-ways\": \"다양한 방식으로 날짜 형식 지정\",\n\t\t\t\t\t\"format-multivalued-variables-using-syntax-example\": \"glob 구문을 사용하여 다중 값 변수 형식 지정, 예: {value1,value2}\",\n\t\t\t\t\t\"html-escaping-of-values\": \"값의 HTML 이스케이프\",\n\t\t\t\t\t\"json-stringify-value\": \"JSON 문자열화 값\",\n\t\t\t\t\t\"keep-value-as-is\": \"값을 그대로 유지\",\n\t\t\t\t\t\"multiple-values-are-formatted-like-variablevalue\": \"여러 값은 variable=value와 같은 형식으로 지정됩니다.\",\n\t\t\t\t\t\"single-quoted-values\": \"작은 따옴표로 묶인 값\",\n\t\t\t\t\t\"useful-escaping-values-taking-syntax-characters\": \"URI 구문 문자를 고려한 URL 이스케이프 값에 유용\",\n\t\t\t\t\t\"useful-for-url-escaping-values\": \"URL 이스케이프 값에 유용\",\n\t\t\t\t\t\"values-are-separated-by-character\": \"값은 | 문자로 구분됩니다\"\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\"group-by-variable-renderer\": {\n\t\t\t\"aria-label-group-by-selector\": \"선택기를 기준으로 그룹화\",\n\t\t\t\"placeholder-group-by-label\": \"레이블을 기준으로 그룹화\"\n\t\t},\n\t\t\"interval-variable\": {\n\t\t\t\"placeholder-select-value\": \"값 선택\"\n\t\t},\n\t\t\"loading-options-placeholder\": {\n\t\t\t\"loading-options\": \"옵션 로딩 중...\"\n\t\t},\n\t\t\"multi-value-apply-button\": {\n\t\t\tapply: \"적용\"\n\t\t},\n\t\t\"no-options-placeholder\": {\n\t\t\t\"no-options-found\": \"찾은 옵션 없음\"\n\t\t},\n\t\t\"options-error-placeholder\": {\n\t\t\t\"error-occurred-fetching-labels-click-retry\": \"라벨을 가져오는 동안 오류가 발생했습니다. 클릭하여 다시 시도\"\n\t\t},\n\t\t\"test-object-with-variable-dependency\": {\n\t\t\ttitle: {\n\t\t\t\thello: \"안녕하세요\"\n\t\t\t}\n\t\t},\n\t\t\"test-variable\": {\n\t\t\ttext: {\n\t\t\t\ttext: \"텍스트\"\n\t\t\t}\n\t\t},\n\t\t\"variable-value-input\": {\n\t\t\t\"placeholder-enter-value\": \"값 입력\"\n\t\t},\n\t\t\"variable-value-select\": {\n\t\t\t\"placeholder-select-value\": \"값 선택\"\n\t\t}\n\t}\n}\n};\n\nexport { grafanaScenes as default };\n//# sourceMappingURL=grafana-scenes.json.js.map\n"], "names": ["grafanaScenes", "components", "content", "subTitle", "title", "splitter", "explore", "utils", "variables", "label", "tooltip", "formats", "description", "apply", "hello", "text"], "sourceRoot": ""}