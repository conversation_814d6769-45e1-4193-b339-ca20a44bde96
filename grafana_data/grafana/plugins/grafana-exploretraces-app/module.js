define(["react","@grafana/data","@grafana/ui","@grafana/runtime","@emotion/css","rxjs","react-dom","react-router","lodash","moment","@emotion/react"],(e,r,t,a,n,o,i,s,c,l,p)=>(()=>{"use strict";var d,u,f,m,g={1159:e=>{e.exports=s},1269:e=>{e.exports=o},1454:(e,r,t)=>{t.d(r,{$U:()=>l,$V:()=>L,$d:()=>C,Ao:()=>b,BS:()=>A,CE:()=>h,D5:()=>s,EY:()=>g,Ld:()=>T,MV:()=>d,PL:()=>u,PU:()=>P,Sr:()=>p,V2:()=>j,Vl:()=>$,W5:()=>_,X0:()=>R,ZM:()=>U,ZV:()=>z,a5:()=>f,bD:()=>D,bw:()=>i,cd:()=>c,gP:()=>S,gR:()=>v,jx:()=>V,nr:()=>I,pf:()=>k,s9:()=>G,sv:()=>W,u0:()=>B,uK:()=>q,ui:()=>y,vR:()=>F,x5:()=>m,xT:()=>E,xc:()=>O,y2:()=>N,z:()=>w,zM:()=>x,zd:()=>M});var a=t(7781),n=t(2533);function o(e,r,t){return r in e?Object.defineProperty(e,r,{value:t,enumerable:!0,configurable:!0,writable:!0}):e[r]=t,e}var i=function(e){return e.Explore="explore",e.Home="home",e}({});const s=`/a/${n.id}/explore`,c="grafana.drilldown.traces.datasource",l="grafana.drilldown.traces.homepage.filters",p="grafana.drilldown.traces.bookmarks",d="repeat(auto-fit, minmax(400px, 1fr))",u="No data for selected query",f="Please try removing some filters or changing your query.",m=" && ",g="ds",v="${ds}",h="primarySignal",b="filters",y="${primarySignal} && ${filters}",x="homeFilter",w="groupBy",S="spanListColumns",P="metric",k="latencyThreshold",E="${latencyThreshold}",O="partialLatencyThreshold",T="${partialLatencyThreshold}",$={uid:v},j="actionView",_="primarySignal",L="selection",N="All",D="Resource",A="Span",C="resource.",M="span.",R="event.",z="event:",B=["resource.service.name","resource.service.namespace","resource.service.version","resource.cluster","resource.environment","resource.namespace","resource.deployment.environment","resource.k8s.namespace.name","resource.k8s.pod.name","resource.k8s.container.name","resource.k8s.node.name"],V=["name","kind","rootName","rootServiceName","status","statusMessage","span.http.status_code"],q=["duration","event:name","nestedSetLeft","nestedSetParent","nestedSetRight","span:duration","span:id","trace:duration","trace:id","traceDuration"],U=["status","span:status","rootName","rootService","rootServiceName","trace:rootName","trace:rootService","trace:rootServiceName"],I=1e3;class W extends a.BusEventWithPayload{}o(W,"type","timeseries-data-received");class F extends a.BusEventWithPayload{}o(F,"type","trace-opened");const G=[{id:"filterByRefId",options:{exclude:"streaming-progress"}}]},2007:e=>{e.exports=t},2468:e=>{e.exports=l},2533:e=>{e.exports=JSON.parse('{"id":"grafana-exploretraces-app"}')},3241:e=>{e.exports=c},5959:r=>{r.exports=e},6089:e=>{e.exports=n},7781:e=>{e.exports=r},8398:e=>{e.exports=i},8531:e=>{e.exports=a},9089:e=>{e.exports=p}},v={};function h(e){var r=v[e];if(void 0!==r)return r.exports;var t=v[e]={id:e,loaded:!1,exports:{}};return g[e].call(t.exports,t,t.exports,h),t.loaded=!0,t.exports}h.m=g,h.n=e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return h.d(r,{a:r}),r},u=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__,h.t=function(e,r){if(1&r&&(e=this(e)),8&r)return e;if("object"==typeof e&&e){if(4&r&&e.__esModule)return e;if(16&r&&"function"==typeof e.then)return e}var t=Object.create(null);h.r(t);var a={};d=d||[null,u({}),u([]),u(u)];for(var n=2&r&&e;("object"==typeof n||"function"==typeof n)&&!~d.indexOf(n);n=u(n))Object.getOwnPropertyNames(n).forEach(r=>a[r]=()=>e[r]);return a.default=()=>e,h.d(t,a),t},h.d=(e,r)=>{for(var t in r)h.o(r,t)&&!h.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:r[t]})},h.f={},h.e=e=>Promise.all(Object.keys(h.f).reduce((r,t)=>(h.f[t](e,r),r),[])),h.u=e=>e+".js",h.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),h.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),f={},m="grafana-exploretraces-app:",h.l=(e,r,t,a)=>{if(f[e])f[e].push(r);else{var n,o;if(void 0!==t)for(var i=document.getElementsByTagName("script"),s=0;s<i.length;s++){var c=i[s];if(c.getAttribute("src")==e||c.getAttribute("data-webpack")==m+t){n=c;break}}n||(o=!0,(n=document.createElement("script")).charset="utf-8",n.timeout=120,h.nc&&n.setAttribute("nonce",h.nc),n.setAttribute("data-webpack",m+t),n.src=e),f[e]=[r];var l=(r,t)=>{n.onerror=n.onload=null,clearTimeout(p);var a=f[e];if(delete f[e],n.parentNode&&n.parentNode.removeChild(n),a&&a.forEach(e=>e(t)),r)return r(t)},p=setTimeout(l.bind(null,void 0,{type:"timeout",target:n}),12e4);n.onerror=l.bind(null,n.onerror),n.onload=l.bind(null,n.onload),o&&document.head.appendChild(n)}},h.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},h.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),h.p="public/plugins/grafana-exploretraces-app/",(()=>{var e={231:0};h.f.j=(r,t)=>{var a=h.o(e,r)?e[r]:void 0;if(0!==a)if(a)t.push(a[2]);else{var n=new Promise((t,n)=>a=e[r]=[t,n]);t.push(a[2]=n);var o=h.p+h.u(r),i=new Error;h.l(o,t=>{if(h.o(e,r)&&(0!==(a=e[r])&&(e[r]=void 0),a)){var n=t&&("load"===t.type?"missing":t.type),o=t&&t.target&&t.target.src;i.message="Loading chunk "+r+" failed.\n("+n+": "+o+")",i.name="ChunkLoadError",i.type=n,i.request=o,a[1](i)}},"chunk-"+r,r)}};var r=(r,t)=>{var a,n,[o,i,s]=t,c=0;if(o.some(r=>0!==e[r])){for(a in i)h.o(i,a)&&(h.m[a]=i[a]);s&&s(h)}for(r&&r(t);c<o.length;c++)n=o[c],h.o(e,n)&&e[n]&&e[n][0](),e[n]=0},t=self.webpackChunkgrafana_exploretraces_app=self.webpackChunkgrafana_exploretraces_app||[];t.forEach(r.bind(null,0)),t.push=r.bind(null,t.push.bind(t))})(),h.nc=void 0;var b={};h.r(b),h.d(b,{plugin:()=>N});var y=h(5959),x=h.n(y),w=h(7781),S=h(2007);const P=(0,y.lazy)(()=>h.e(67).then(h.bind(h,67))),k=(0,y.lazy)(()=>Promise.all([h.e(223),h.e(712),h.e(397)]).then(h.bind(h,397)));var E=h(1454);const O=[{targets:w.PluginExtensionPoints.DashboardPanelMenu,title:"Open in Traces Drilldown",description:"Open current query in the Traces Drilldown app",path:$(),configure:e=>T(e)},{targets:w.PluginExtensionPoints.ExploreToolbarAction,title:"Open in Grafana Traces Drilldown",description:"Try our new queryless experience for traces",path:$(),configure:e=>T(e)}];function T(e){var r,t,a;if(!e)return;const n=e.targets.find(e=>{var r;return"tempo"===(null===(r=e.datasource)||void 0===r?void 0:r.type)});if(!n||!(null===(r=n.datasource)||void 0===r?void 0:r.uid))return;const o=null===(t=n.filters)||void 0===t?void 0:t.filter(e=>e.scope&&e.tag&&e.operator&&e.value&&e.value.length);if(!o||0===o.length)return;const i=new URLSearchParams;i.append(`var-${E.EY}`,(null===(a=n.datasource)||void 0===a?void 0:a.uid)||"");const s=(0,w.toURLRange)(e.timeRange);i.append("from",String(s.from)),i.append("to",String(s.to));const c=o.find(e=>"status"===e.tag);return c&&i.append(`var-${E.PU}`,"error"===c.value?"errors":"rate"),i.append("var-primarySignal","true"),(e=>e.filter(e=>"status"!==e.tag).map(e=>`${e.scope}${function(e){return function(e){return j.some(r=>r.tag===e.tag&&r.scope===e.scope)}(e)?":":"."}(e)}${e.tag}|${e.operator}|${e.value}`))(o).forEach(e=>i.append(`var-${E.Ao}`,e)),{path:`${$(i)}`}}function $(e){return`${E.D5}${e?`?${e.toString()}`:""}`}const j=["event:name","event:timeSinceStart","instrumentation:name","instrumentation:version","link:spanID","link:traceID","span:duration","span:id","span:kind","span:name","span:status","span:statusMessage","trace:duration","trace:id","trace:rootName","trace:rootService"].map(e=>{const[r,t]=e.split(":");return{scope:r,tag:t}}),_=(0,y.lazy)(()=>h.e(142).then(h.bind(h,3142))),L=(0,y.lazy)(()=>h.e(708).then(h.bind(h,8327))),N=(new w.AppPlugin).setRootPage(_).addConfigPage({title:"Configuration",icon:"cog",body:L,id:"configuration"}).exposeComponent({id:"grafana-exploretraces-app/open-in-explore-traces-button/v1",title:"Open in Traces Drilldown button",description:"A button that opens a traces view in the Traces drilldown app.",component:function(e){return x().createElement(y.Suspense,{fallback:x().createElement(S.LinkButton,{variant:"secondary",disabled:!0},"Open in Traces Drilldown")},x().createElement(P,e))}}).exposeComponent({id:"grafana-exploretraces-app/embedded-trace-exploration/v1",title:"Embedded Trace Exploration",description:"A component that renders a trace exploration view that can be embedded in other parts of Grafana.",component:function(e){return x().createElement(y.Suspense,{fallback:x().createElement("div",null,"Loading...")},x().createElement(k,e))}});for(const e of O)N.addLink(e);return b})());
//# sourceMappingURL=module.js.map