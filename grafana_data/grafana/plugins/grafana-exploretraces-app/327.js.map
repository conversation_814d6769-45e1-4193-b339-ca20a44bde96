{"version": 3, "file": "327.js", "mappings": "mKAAA,IAAIA,EAAgB,CACnB,iBAAkB,CAClBC,WAAY,CACX,oBAAqB,CACpB,uBAAwB,2BACxB,iBAAkB,oBAClB,yBAA0B,4BAE3B,yBAA0B,CACzB,sBAAuB,4BACvB,mBAAoB,2BAErB,gBAAiB,CAChBC,QAAS,8CACTC,SAAU,oBACVC,MAAO,WAER,wBAAyB,CACxB,wBAAyB,YACzB,sBAAuB,SACvB,sBAAuB,UAExB,iBAAkB,CACjB,iBAAkB,YAClB,cAAe,SACf,uBAAwB,YAEzB,iBAAkB,CACjB,eAAgB,UAChB,aAAc,QAEf,oCAAqC,CACpC,eAAgB,KAChB,iBAAkB,eAEnBC,SAAU,CACT,gCAAiC,iBAElC,YAAa,CACZD,MAAO,CACNA,MAAO,SAGT,2BAA4B,CAC3BE,QAAS,MAEV,qBAAsB,CACrB,uBAAwB,oBACxB,sCAAuC,6BAExC,yBAA0B,CACzB,2DAA4D,oDAC5D,kBAAmB,0BAGrBC,MAAO,CACN,iBAAkB,CACjB,iBAAkB,MAEnB,oBAAqB,CACpB,uBAAwB,cAG1BC,UAAW,CACV,kBAAmB,CAClB,kCAAmC,eAEpC,wBAAyB,CACxB,wBAAyB,WACzB,mBAAoB,YAErB,yBAA0B,CACzB,2BAA4B,WAC5B,aAAc,CACb,2BAA4B,UAE7B,qBAAsB,SACtB,sBAAuB,WACvB,eAAgB,CACf,2BAA4B,SAG9B,uBAAwB,CACvBC,MAAO,CACN,QAAW,UAGb,8CAA+C,CAC9C,mBAAoB,MACpBC,QAAS,mDACT,gDAAiD,iCAElD,kBAAmB,CAClBC,QAAS,CACRC,YAAa,CACZ,wBAAyB,UACzB,uBAAwB,cACxB,gCAAiC,kBACjC,oDAAqD,8CACrD,0BAA2B,cAC3B,uBAAwB,YACxB,mBAAoB,WACpB,mDAAoD,0BACpD,uBAAwB,cACxB,kDAAmD,2BACnD,iCAAkC,eAClC,oCAAqC,kBAIxC,6BAA8B,CAC7B,+BAAgC,cAChC,6BAA8B,aAE/B,oBAAqB,CACpB,2BAA4B,QAE7B,8BAA+B,CAC9B,kBAAmB,kBAEpB,2BAA4B,CAC3BC,MAAO,MAER,yBAA0B,CACzB,mBAAoB,iBAErB,4BAA6B,CAC5B,6CAA8C,gCAE/C,uCAAwC,CACvCT,MAAO,CACNU,MAAO,UAGT,gBAAiB,CAChBC,KAAM,CACLA,KAAM,SAGR,uBAAwB,CACvB,0BAA2B,QAE5B,wBAAyB,CACxB,2BAA4B,U", "sources": ["webpack://grafana-exploretraces-app/../node_modules/@grafana/scenes/dist/esm/locales/ja-JP/grafana-scenes.json.js"], "sourcesContent": ["var grafanaScenes = {\n\t\"grafana-scenes\": {\n\tcomponents: {\n\t\t\"adhoc-filter-pill\": {\n\t\t\t\"edit-filter-with-key\": \"{{keyLabel}}キーでフィルターを編集 \",\n\t\t\t\"managed-filter\": \"{{origin}}管理フィルター\",\n\t\t\t\"remove-filter-with-key\": \"{{keyLabel}}キーでフィルターを削除 \"\n\t\t},\n\t\t\"adhoc-filters-combobox\": {\n\t\t\t\"remove-filter-value\": \"フィルター値を削除 - {{itemLabel}}\",\n\t\t\t\"use-custom-value\": \"カスタム値を使用：{{itemLabel}} \"\n\t\t},\n\t\t\"fallback-page\": {\n\t\t\tcontent: \"リンクからこのページにアクセスした場合、アプリケーションにバグがある可能性があります。\",\n\t\t\tsubTitle: \"URLがどのページにも一致しません\",\n\t\t\ttitle: \"見つかりません\"\n\t\t},\n\t\t\"nested-scene-renderer\": {\n\t\t\t\"collapse-button-label\": \"シーンを折りたたむ\",\n\t\t\t\"expand-button-label\": \"シーンを展開\",\n\t\t\t\"remove-button-label\": \"シーンを削除\"\n\t\t},\n\t\t\"scene-debugger\": {\n\t\t\t\"object-details\": \"オブジェクトの詳細\",\n\t\t\t\"scene-graph\": \"シーングラフ\",\n\t\t\t\"title-scene-debugger\": \"シーンデバッガー\"\n\t\t},\n\t\t\"scene-grid-row\": {\n\t\t\t\"collapse-row\": \"行を折りたたむ\",\n\t\t\t\"expand-row\": \"行を展開\"\n\t\t},\n\t\t\"scene-time-range-compare-renderer\": {\n\t\t\t\"button-label\": \"比較\",\n\t\t\t\"button-tooltip\": \"時間枠比較を有効にする\"\n\t\t},\n\t\tsplitter: {\n\t\t\t\"aria-label-pane-resize-widget\": \"ペインリサイズウィジェット\"\n\t\t},\n\t\t\"viz-panel\": {\n\t\t\ttitle: {\n\t\t\t\ttitle: \"タイトル\"\n\t\t\t}\n\t\t},\n\t\t\"viz-panel-explore-button\": {\n\t\t\texplore: \"探検\"\n\t\t},\n\t\t\"viz-panel-renderer\": {\n\t\t\t\"loading-plugin-panel\": \"プラグインパネルを読み込み中...\",\n\t\t\t\"panel-plugin-has-no-panel-component\": \"パネルプラグインにパネルコンポーネントがありません\"\n\t\t},\n\t\t\"viz-panel-series-limit\": {\n\t\t\t\"content-rendering-series-single-panel-impact-performance\": \"単一パネルで多数の系列を表示すると、パフォーマンスに影響し、データが読みにくくなる場合があります。\",\n\t\t\t\"warning-message\": \"{{seriesLimit}}系列のみ表示\"\n\t\t}\n\t},\n\tutils: {\n\t\t\"controls-label\": {\n\t\t\t\"tooltip-remove\": \"削除\"\n\t\t},\n\t\t\"loading-indicator\": {\n\t\t\t\"content-cancel-query\": \"クエリをキャンセル\"\n\t\t}\n\t},\n\tvariables: {\n\t\t\"ad-hoc-combobox\": {\n\t\t\t\"aria-label-edit-filter-operator\": \"フィルター演算子を編集\"\n\t\t},\n\t\t\"ad-hoc-filter-builder\": {\n\t\t\t\"aria-label-add-filter\": \"フィルターを追加\",\n\t\t\t\"title-add-filter\": \"フィルターを追加\"\n\t\t},\n\t\t\"ad-hoc-filter-renderer\": {\n\t\t\t\"aria-label-remove-filter\": \"フィルターを削除\",\n\t\t\t\"key-select\": {\n\t\t\t\t\"placeholder-select-label\": \"ラベルを選択\"\n\t\t\t},\n\t\t\t\"label-select-label\": \"ラベルを選択\",\n\t\t\t\"title-remove-filter\": \"フィルターを削除\",\n\t\t\t\"value-select\": {\n\t\t\t\t\"placeholder-select-value\": \"値を選択\"\n\t\t\t}\n\t\t},\n\t\t\"data-source-variable\": {\n\t\t\tlabel: {\n\t\t\t\t\"default\": \"デフォルト\"\n\t\t\t}\n\t\t},\n\t\t\"default-group-by-custom-indicator-container\": {\n\t\t\t\"aria-label-clear\": \"クリア\",\n\t\t\ttooltip: \"このダッシュボードでデフォルトで適用されます。編集した場合、他のダッシュボードに引き継がれます。\",\n\t\t\t\"tooltip-restore-groupby-set-by-this-dashboard\": \"このダッシュボードで設定されたgroupbyを復元します。\"\n\t\t},\n\t\t\"format-registry\": {\n\t\t\tformats: {\n\t\t\t\tdescription: {\n\t\t\t\t\t\"commaseparated-values\": \"カンマ区切り値\",\n\t\t\t\t\t\"double-quoted-values\": \"二重引用符で囲まれた値\",\n\t\t\t\t\t\"format-date-in-different-ways\": \"日付を様々な形式でフォーマット\",\n\t\t\t\t\t\"format-multivalued-variables-using-syntax-example\": \"glob構文を使用して複数値変数をフォーマット（例: {value1,value2}）\",\n\t\t\t\t\t\"html-escaping-of-values\": \"値のHTMLエスケープ\",\n\t\t\t\t\t\"json-stringify-value\": \"JSON文字列化値\",\n\t\t\t\t\t\"keep-value-as-is\": \"値をそのまま保持\",\n\t\t\t\t\t\"multiple-values-are-formatted-like-variablevalue\": \"複数の値は変数=値の形式でフォーマットされます\",\n\t\t\t\t\t\"single-quoted-values\": \"一重引用符で囲まれた値\",\n\t\t\t\t\t\"useful-escaping-values-taking-syntax-characters\": \"URI構文文字を考慮したURLエスケープ値に便利\",\n\t\t\t\t\t\"useful-for-url-escaping-values\": \"URLエスケープ値に便利\",\n\t\t\t\t\t\"values-are-separated-by-character\": \"値は|文字で区切られます\"\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\"group-by-variable-renderer\": {\n\t\t\t\"aria-label-group-by-selector\": \"セレクターでグループ化\",\n\t\t\t\"placeholder-group-by-label\": \"ラベルでグループ化\"\n\t\t},\n\t\t\"interval-variable\": {\n\t\t\t\"placeholder-select-value\": \"値を選択\"\n\t\t},\n\t\t\"loading-options-placeholder\": {\n\t\t\t\"loading-options\": \"オプションを読み込み中...\"\n\t\t},\n\t\t\"multi-value-apply-button\": {\n\t\t\tapply: \"適用\"\n\t\t},\n\t\t\"no-options-placeholder\": {\n\t\t\t\"no-options-found\": \"オプションが見つかりません\"\n\t\t},\n\t\t\"options-error-placeholder\": {\n\t\t\t\"error-occurred-fetching-labels-click-retry\": \"ラベルの取得中にエラーが発生しました。クリックして再試行\"\n\t\t},\n\t\t\"test-object-with-variable-dependency\": {\n\t\t\ttitle: {\n\t\t\t\thello: \"こんにちは\"\n\t\t\t}\n\t\t},\n\t\t\"test-variable\": {\n\t\t\ttext: {\n\t\t\t\ttext: \"テキスト\"\n\t\t\t}\n\t\t},\n\t\t\"variable-value-input\": {\n\t\t\t\"placeholder-enter-value\": \"値を入力\"\n\t\t},\n\t\t\"variable-value-select\": {\n\t\t\t\"placeholder-select-value\": \"値を選択\"\n\t\t}\n\t}\n}\n};\n\nexport { grafanaScenes as default };\n//# sourceMappingURL=grafana-scenes.json.js.map\n"], "names": ["grafanaScenes", "components", "content", "subTitle", "title", "splitter", "explore", "utils", "variables", "label", "tooltip", "formats", "description", "apply", "hello", "text"], "sourceRoot": ""}