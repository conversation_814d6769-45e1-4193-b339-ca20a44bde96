{"version": 3, "file": "220.js", "mappings": "mKAAA,IAAIA,EAAgB,CACnB,iBAAkB,CAClBC,WAAY,CACX,oBAAqB,CACpB,uBAAwB,0CACxB,iBAAkB,gCAClB,yBAA0B,2CAE3B,yBAA0B,CACzB,sBAAuB,0CACvB,mBAAoB,0CAErB,gBAAiB,CAChBC,QAAS,+FACTC,SAAU,gDACVC,MAAO,iBAER,wBAAyB,CACxB,wBAAyB,uBACzB,sBAAuB,oBACvB,sBAAuB,wBAExB,iBAAkB,CACjB,iBAAkB,wBAClB,cAAe,iBACf,uBAAwB,sBAEzB,iBAAkB,CACjB,eAAgB,mBAChB,aAAc,iBAEf,oCAAqC,CACpC,eAAgB,kBAChB,iBAAkB,0CAEnBC,SAAU,CACT,gCAAiC,kCAElC,YAAa,CACZD,MAAO,CACNA,MAAO,QAGT,2BAA4B,CAC3BE,QAAS,WAEV,qBAAsB,CACrB,uBAAwB,4BACxB,sCAAuC,8CAExC,yBAA0B,CACzB,2DAA4D,gIAC5D,kBAAmB,+CAGrBC,MAAO,CACN,iBAAkB,CACjB,iBAAkB,eAEnB,oBAAqB,CACpB,uBAAwB,8BAG1BC,UAAW,CACV,kBAAmB,CAClB,kCAAmC,8BAEpC,wBAAyB,CACxB,wBAAyB,mBACzB,mBAAoB,oBAErB,yBAA0B,CACzB,2BAA4B,qBAC5B,aAAc,CACb,2BAA4B,sBAE7B,qBAAsB,qBACtB,sBAAuB,qBACvB,eAAgB,CACf,2BAA4B,uBAG9B,uBAAwB,CACvBC,MAAO,CACN,QAAW,mBAGb,8CAA+C,CAC9C,mBAAoB,SACpBC,QAAS,yGACT,gDAAiD,iFAElD,kBAAmB,CAClBC,QAAS,CACRC,YAAa,CACZ,wBAAyB,iCACzB,uBAAwB,2BACxB,gCAAiC,oCACjC,oDAAqD,4EACrD,0BAA2B,sCAC3B,uBAAwB,0BACxB,mBAAoB,oCACpB,mDAAoD,iDACpD,uBAAwB,+BACxB,kDAAmD,uGACnD,iCAAkC,mDAClC,oCAAqC,0CAIxC,6BAA8B,CAC7B,+BAAgC,kCAChC,6BAA8B,mCAE/B,oBAAqB,CACpB,2BAA4B,sBAE7B,8BAA+B,CAC9B,kBAAmB,0BAEpB,2BAA4B,CAC3BC,MAAO,YAER,yBAA0B,CACzB,mBAAoB,2BAErB,4BAA6B,CAC5B,6CAA8C,0EAE/C,uCAAwC,CACvCT,MAAO,CACNU,MAAO,QAGT,gBAAiB,CAChBC,KAAM,CACLA,KAAM,WAGR,uBAAwB,CACvB,0BAA2B,kBAE5B,wBAAyB,CACxB,2BAA4B,wB", "sources": ["webpack://grafana-exploretraces-app/../node_modules/@grafana/scenes/dist/esm/locales/hu-HU/grafana-scenes.json.js"], "sourcesContent": ["var grafanaScenes = {\n\t\"grafana-scenes\": {\n\tcomponents: {\n\t\t\"adhoc-filter-pill\": {\n\t\t\t\"edit-filter-with-key\": \"{{keyLabel}} kulcsos szűrő szerkesztése\",\n\t\t\t\"managed-filter\": \"{{origin}} által kezelt szű<PERSON>\",\n\t\t\t\"remove-filter-with-key\": \"{{keyLabel}} kulcsos szűrő eltávolítása\"\n\t\t},\n\t\t\"adhoc-filters-combobox\": {\n\t\t\t\"remove-filter-value\": \"Szűrőérték eltávolítása – {{itemLabel}}\",\n\t\t\t\"use-custom-value\": \"Egyéni érték használata: {{itemLabel}}\"\n\t\t},\n\t\t\"fallback-page\": {\n\t\t\tcontent: \"Ha egy hivatkozáson keresztül lépett ide, akkor lehet, hogy hiba van ebben az alkalmazásban.\",\n\t\t\tsubTitle: \"Az URL-cím nem egyezett egyetlen oldallal sem\",\n\t\t\ttitle: \"<PERSON>em ta<PERSON>\"\n\t\t},\n\t\t\"nested-scene-renderer\": {\n\t\t\t\"collapse-button-label\": \"Jelenet összecsuk<PERSON>a\",\n\t\t\t\"expand-button-label\": \"Jelenet kibontása\",\n\t\t\t\"remove-button-label\": \"Jelenet eltávolítása\"\n\t\t},\n\t\t\"scene-debugger\": {\n\t\t\t\"object-details\": \"Az objektum részletei\",\n\t\t\t\"scene-graph\": \"Jelenetdiagram\",\n\t\t\t\"title-scene-debugger\": \"Jelenet-hibakereső\"\n\t\t},\n\t\t\"scene-grid-row\": {\n\t\t\t\"collapse-row\": \"Sor összecsukása\",\n\t\t\t\"expand-row\": \"Sor kibontása\"\n\t\t},\n\t\t\"scene-time-range-compare-renderer\": {\n\t\t\t\"button-label\": \"Összehasonlítás\",\n\t\t\t\"button-tooltip\": \"Időkeret-összehasonlítás engedélyezése\"\n\t\t},\n\t\tsplitter: {\n\t\t\t\"aria-label-pane-resize-widget\": \"Ablaktábla-átméretezési widget\"\n\t\t},\n\t\t\"viz-panel\": {\n\t\t\ttitle: {\n\t\t\t\ttitle: \"Cím\"\n\t\t\t}\n\t\t},\n\t\t\"viz-panel-explore-button\": {\n\t\t\texplore: \"Explore\"\n\t\t},\n\t\t\"viz-panel-renderer\": {\n\t\t\t\"loading-plugin-panel\": \"Bővítménypanel betöltése…\",\n\t\t\t\"panel-plugin-has-no-panel-component\": \"A panelbővítménynek nincs panelösszetevője\"\n\t\t},\n\t\t\"viz-panel-series-limit\": {\n\t\t\t\"content-rendering-series-single-panel-impact-performance\": \"Ha túl sok sorozatot jelenít meg egyetlen panelen, az hatással lehet a teljesítményre, és megnehezítheti az adatok olvasását.\",\n\t\t\t\"warning-message\": \"Csak {{seriesLimit}} sorozat megjelenítése\"\n\t\t}\n\t},\n\tutils: {\n\t\t\"controls-label\": {\n\t\t\t\"tooltip-remove\": \"Eltávolítás\"\n\t\t},\n\t\t\"loading-indicator\": {\n\t\t\t\"content-cancel-query\": \"A lekérdezés megszakítása\"\n\t\t}\n\t},\n\tvariables: {\n\t\t\"ad-hoc-combobox\": {\n\t\t\t\"aria-label-edit-filter-operator\": \"Szűrőoperátor szerkesztése\"\n\t\t},\n\t\t\"ad-hoc-filter-builder\": {\n\t\t\t\"aria-label-add-filter\": \"Szűrő hozzáadása\",\n\t\t\t\"title-add-filter\": \"Szűrő hozzáadása\"\n\t\t},\n\t\t\"ad-hoc-filter-renderer\": {\n\t\t\t\"aria-label-remove-filter\": \"Szűrő eltávolítása\",\n\t\t\t\"key-select\": {\n\t\t\t\t\"placeholder-select-label\": \"Címke kiválasztása\"\n\t\t\t},\n\t\t\t\"label-select-label\": \"Címke kiválasztása\",\n\t\t\t\"title-remove-filter\": \"Szűrő eltávolítása\",\n\t\t\t\"value-select\": {\n\t\t\t\t\"placeholder-select-value\": \"Érték kiválasztása\"\n\t\t\t}\n\t\t},\n\t\t\"data-source-variable\": {\n\t\t\tlabel: {\n\t\t\t\t\"default\": \"alapértelmezés\"\n\t\t\t}\n\t\t},\n\t\t\"default-group-by-custom-indicator-container\": {\n\t\t\t\"aria-label-clear\": \"törlés\",\n\t\t\ttooltip: \"Alapértelmezés szerint alkalmazva ezen az irányítópulton. A szerkesztést átviszi más irányítópultokra.\",\n\t\t\t\"tooltip-restore-groupby-set-by-this-dashboard\": \"A jelen irányítópult által beállított csoportosítási szempont visszaállítása.\"\n\t\t},\n\t\t\"format-registry\": {\n\t\t\tformats: {\n\t\t\t\tdescription: {\n\t\t\t\t\t\"commaseparated-values\": \"Vesszővel elválasztott értékek\",\n\t\t\t\t\t\"double-quoted-values\": \"Dupla idézőjeles értékek\",\n\t\t\t\t\t\"format-date-in-different-ways\": \"Dátum formázása különböző módokon\",\n\t\t\t\t\t\"format-multivalued-variables-using-syntax-example\": \"Többértékű változók formázása glob szintaxissal, például: {érték1,érték2}\",\n\t\t\t\t\t\"html-escaping-of-values\": \"Értékek módosított HTML-értelmezése\",\n\t\t\t\t\t\"json-stringify-value\": \"A JSON stringify értéke\",\n\t\t\t\t\t\"keep-value-as-is\": \"Érték megtartása adott állapotban\",\n\t\t\t\t\t\"multiple-values-are-formatted-like-variablevalue\": \"Több érték formázása változó=érték formátumban\",\n\t\t\t\t\t\"single-quoted-values\": \"Egyszeres idézőjeles értékek\",\n\t\t\t\t\t\"useful-escaping-values-taking-syntax-characters\": \"Hasznos az értékek módosított URL-értelmezéséhez, az URI-szintaktikai karakterek figyelembevételével\",\n\t\t\t\t\t\"useful-for-url-escaping-values\": \"Hasznos az értékek módosított URL-értelmezéséhez\",\n\t\t\t\t\t\"values-are-separated-by-character\": \"Az értékeket | karakter választja el\"\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\"group-by-variable-renderer\": {\n\t\t\t\"aria-label-group-by-selector\": \"Csoportosításiszempont-választó\",\n\t\t\t\"placeholder-group-by-label\": \"Csoportosítási szempont címkéje\"\n\t\t},\n\t\t\"interval-variable\": {\n\t\t\t\"placeholder-select-value\": \"Érték kiválasztása\"\n\t\t},\n\t\t\"loading-options-placeholder\": {\n\t\t\t\"loading-options\": \"Beállítások betöltése…\"\n\t\t},\n\t\t\"multi-value-apply-button\": {\n\t\t\tapply: \"Alkalmaz\"\n\t\t},\n\t\t\"no-options-placeholder\": {\n\t\t\t\"no-options-found\": \"Nem található beállítás\"\n\t\t},\n\t\t\"options-error-placeholder\": {\n\t\t\t\"error-occurred-fetching-labels-click-retry\": \"Hiba történt a címkék lekérése során. Kattintson az újrapróbálkozáshoz\"\n\t\t},\n\t\t\"test-object-with-variable-dependency\": {\n\t\t\ttitle: {\n\t\t\t\thello: \"Üdv\"\n\t\t\t}\n\t\t},\n\t\t\"test-variable\": {\n\t\t\ttext: {\n\t\t\t\ttext: \"Szöveg\"\n\t\t\t}\n\t\t},\n\t\t\"variable-value-input\": {\n\t\t\t\"placeholder-enter-value\": \"Érték megadása\"\n\t\t},\n\t\t\"variable-value-select\": {\n\t\t\t\"placeholder-select-value\": \"Érték kiválasztása\"\n\t\t}\n\t}\n}\n};\n\nexport { grafanaScenes as default };\n//# sourceMappingURL=grafana-scenes.json.js.map\n"], "names": ["grafanaScenes", "components", "content", "subTitle", "title", "splitter", "explore", "utils", "variables", "label", "tooltip", "formats", "description", "apply", "hello", "text"], "sourceRoot": ""}