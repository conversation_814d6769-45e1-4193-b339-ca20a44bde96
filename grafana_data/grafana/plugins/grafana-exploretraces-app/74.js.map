{"version": 3, "file": "74.js", "mappings": "kKAAA,IAAIA,EAAgB,CACnB,iBAAkB,CAClBC,WAAY,CACX,oBAAqB,CACpB,uBAAwB,2CACxB,iBAAkB,6BAClB,yBAA0B,+CAE3B,yBAA0B,CACzB,sBAAuB,2CACvB,mBAAoB,8CAErB,gBAAiB,CAChBC,QAAS,qFACTC,SAAU,6CACVC,MAAO,iBAER,wBAAyB,CACxB,wBAAyB,oBACzB,sBAAuB,kBACvB,sBAAuB,qBAExB,iBAAkB,CACjB,iBAAkB,gBAClB,cAAe,eACf,uBAAwB,kBAEzB,iBAAkB,CACjB,eAAgB,kBAChB,aAAc,iBAEf,oCAAqC,CACpC,eAAgB,eAChB,iBAAkB,uCAEnBC,SAAU,CACT,gCAAiC,2BAElC,YAAa,CACZD,MAAO,CACNA,MAAO,UAGT,2BAA4B,CAC3BE,QAAS,aAEV,qBAAsB,CACrB,uBAAwB,yBACxB,sCAAuC,4CAExC,yBAA0B,CACzB,2DAA4D,gIAC5D,kBAAmB,4CAGrBC,MAAO,CACN,iBAAkB,CACjB,iBAAkB,eAEnB,oBAAqB,CACpB,uBAAwB,oBAG1BC,UAAW,CACV,kBAAmB,CAClB,kCAAmC,2BAEpC,wBAAyB,CACxB,wBAAyB,mBACzB,mBAAoB,oBAErB,yBAA0B,CACzB,2BAA4B,qBAC5B,aAAc,CACb,2BAA4B,mBAE7B,qBAAsB,kBACtB,sBAAuB,qBACvB,eAAgB,CACf,2BAA4B,sBAG9B,uBAAwB,CACvBC,MAAO,CACN,QAAW,cAGb,8CAA+C,CAC9C,mBAAoB,SACpBC,QAAS,6GACT,gDAAiD,iDAElD,kBAAmB,CAClBC,QAAS,CACRC,YAAa,CACZ,wBAAyB,0BACzB,uBAAwB,4BACxB,gCAAiC,0CACjC,oDAAqD,wGACrD,0BAA2B,4BAC3B,uBAAwB,wBACxB,mBAAoB,0BACpB,mDAAoD,uDACpD,uBAAwB,2BACxB,kDAAmD,4EACnD,iCAAkC,mCAClC,oCAAqC,4CAIxC,6BAA8B,CAC7B,+BAAgC,sBAChC,6BAA8B,sBAE/B,oBAAqB,CACpB,2BAA4B,qBAE7B,8BAA+B,CAC9B,kBAAmB,iBAEpB,2BAA4B,CAC3BC,MAAO,aAER,yBAA0B,CACzB,mBAAoB,wBAErB,4BAA6B,CAC5B,6CAA8C,qFAE/C,uCAAwC,CACvCT,MAAO,CACNU,MAAO,UAGT,gBAAiB,CAChBC,KAAM,CACLA,KAAM,UAGR,uBAAwB,CACvB,0BAA2B,kBAE5B,wBAAyB,CACxB,2BAA4B,uB", "sources": ["webpack://grafana-exploretraces-app/../node_modules/@grafana/scenes/dist/esm/locales/nl-NL/grafana-scenes.json.js"], "sourcesContent": ["var grafanaScenes = {\n\t\"grafana-scenes\": {\n\tcomponents: {\n\t\t\"adhoc-filter-pill\": {\n\t\t\t\"edit-filter-with-key\": \"Filter bewerken met sleutel {{keyLabel}}\",\n\t\t\t\"managed-filter\": \"{{origin}} beheerde filter\",\n\t\t\t\"remove-filter-with-key\": \"Filter verwijderen met sleutel {{keyLabel}}\"\n\t\t},\n\t\t\"adhoc-filters-combobox\": {\n\t\t\t\"remove-filter-value\": \"Filterwaarde verwijderen - {{itemLabel}}\",\n\t\t\t\"use-custom-value\": \"Aangepaste waarde gebruiken: {{itemLabel}}\"\n\t\t},\n\t\t\"fallback-page\": {\n\t\t\tcontent: \"Als je hier bent gekomen via een link, dan kan er een bug in deze applicatie zijn.\",\n\t\t\tsubTitle: \"De URL kwam met geen enkele pagina overeen\",\n\t\t\ttitle: \"Niet gevonden\"\n\t\t},\n\t\t\"nested-scene-renderer\": {\n\t\t\t\"collapse-button-label\": \"<PERSON><PERSON> samenvouwen\",\n\t\t\t\"expand-button-label\": \"<PERSON>ène uitvouwen\",\n\t\t\t\"remove-button-label\": \"Scène verwijderen\"\n\t\t},\n\t\t\"scene-debugger\": {\n\t\t\t\"object-details\": \"Objectdetails\",\n\t\t\t\"scene-graph\": \"Scènegrafiek\",\n\t\t\t\"title-scene-debugger\": \"Scène-debugger\"\n\t\t},\n\t\t\"scene-grid-row\": {\n\t\t\t\"collapse-row\": \"Rij samenvouwen\",\n\t\t\t\"expand-row\": \"Rij uitvouwen\"\n\t\t},\n\t\t\"scene-time-range-compare-renderer\": {\n\t\t\t\"button-label\": \"Vergelijking\",\n\t\t\t\"button-tooltip\": \"Tijdsbestekvergelijking inschakelen\"\n\t\t},\n\t\tsplitter: {\n\t\t\t\"aria-label-pane-resize-widget\": \"Grootte widget wijzigen\"\n\t\t},\n\t\t\"viz-panel\": {\n\t\t\ttitle: {\n\t\t\t\ttitle: \"Titel\"\n\t\t\t}\n\t\t},\n\t\t\"viz-panel-explore-button\": {\n\t\t\texplore: \"Verkennen\"\n\t\t},\n\t\t\"viz-panel-renderer\": {\n\t\t\t\"loading-plugin-panel\": \"Plug-inpaneel laden...\",\n\t\t\t\"panel-plugin-has-no-panel-component\": \"Paneelplug-in heeft geen paneelcomponent\"\n\t\t},\n\t\t\"viz-panel-series-limit\": {\n\t\t\t\"content-rendering-series-single-panel-impact-performance\": \"Het renderen van te veel reeksen in één paneel kan de prestaties beïnvloeden en de leesbaarheid van de gegevens verminderen. \",\n\t\t\t\"warning-message\": \"Alleen {{seriesLimit}}-series weergeven\"\n\t\t}\n\t},\n\tutils: {\n\t\t\"controls-label\": {\n\t\t\t\"tooltip-remove\": \"Verwijderen\"\n\t\t},\n\t\t\"loading-indicator\": {\n\t\t\t\"content-cancel-query\": \"Query annuleren\"\n\t\t}\n\t},\n\tvariables: {\n\t\t\"ad-hoc-combobox\": {\n\t\t\t\"aria-label-edit-filter-operator\": \"Filteroperator bewerken\"\n\t\t},\n\t\t\"ad-hoc-filter-builder\": {\n\t\t\t\"aria-label-add-filter\": \"Filter toevoegen\",\n\t\t\t\"title-add-filter\": \"Filter toevoegen\"\n\t\t},\n\t\t\"ad-hoc-filter-renderer\": {\n\t\t\t\"aria-label-remove-filter\": \"Filter verwijderen\",\n\t\t\t\"key-select\": {\n\t\t\t\t\"placeholder-select-label\": \"Selecteer label\"\n\t\t\t},\n\t\t\t\"label-select-label\": \"Selecteer label\",\n\t\t\t\"title-remove-filter\": \"Filter verwijderen\",\n\t\t\t\"value-select\": {\n\t\t\t\t\"placeholder-select-value\": \"Waarde selecteren\"\n\t\t\t}\n\t\t},\n\t\t\"data-source-variable\": {\n\t\t\tlabel: {\n\t\t\t\t\"default\": \"standaard\"\n\t\t\t}\n\t\t},\n\t\t\"default-group-by-custom-indicator-container\": {\n\t\t\t\"aria-label-clear\": \"wissen\",\n\t\t\ttooltip: \"Standaard toegepast in dit dashboard. Als het wordt bewerkt, wordt het overgenomen naar andere dashboards.\",\n\t\t\t\"tooltip-restore-groupby-set-by-this-dashboard\": \"Herstel groupby ingesteld door dit dashboard.\"\n\t\t},\n\t\t\"format-registry\": {\n\t\t\tformats: {\n\t\t\t\tdescription: {\n\t\t\t\t\t\"commaseparated-values\": \"Kommagescheiden waarden\",\n\t\t\t\t\t\"double-quoted-values\": \"Dubbel geciteerde waarden\",\n\t\t\t\t\t\"format-date-in-different-ways\": \"Datum op verschillende manieren opmaken\",\n\t\t\t\t\t\"format-multivalued-variables-using-syntax-example\": \"Formatteer variabelen met meerdere waarden met behulp van glob-syntaxis, bijvoorbeeld {value1,value2}\",\n\t\t\t\t\t\"html-escaping-of-values\": \"HTML-escaping van waarden\",\n\t\t\t\t\t\"json-stringify-value\": \"JSON-stringify-waarde\",\n\t\t\t\t\t\"keep-value-as-is\": \"Huidige waarde behouden\",\n\t\t\t\t\t\"multiple-values-are-formatted-like-variablevalue\": \"Meerdere waarden zijn opgemaakt als variabele=waarde\",\n\t\t\t\t\t\"single-quoted-values\": \"Enkel geciteerde waarden\",\n\t\t\t\t\t\"useful-escaping-values-taking-syntax-characters\": \"Handig voor URL-escapingwaarden, rekening houdend met URI-syntaxis tekens\",\n\t\t\t\t\t\"useful-for-url-escaping-values\": \"Handig voor URL-escaping-waarden\",\n\t\t\t\t\t\"values-are-separated-by-character\": \"Waarden worden gescheiden door | teken\"\n\t\t\t\t}\n\t\t\t}\n\t\t},\n\t\t\"group-by-variable-renderer\": {\n\t\t\t\"aria-label-group-by-selector\": \"Groeperen op kiezer\",\n\t\t\t\"placeholder-group-by-label\": \"Groeperen op label\"\n\t\t},\n\t\t\"interval-variable\": {\n\t\t\t\"placeholder-select-value\": \"Waarde selecteren\"\n\t\t},\n\t\t\"loading-options-placeholder\": {\n\t\t\t\"loading-options\": \"Opties laden…\"\n\t\t},\n\t\t\"multi-value-apply-button\": {\n\t\t\tapply: \"Toepassen\"\n\t\t},\n\t\t\"no-options-placeholder\": {\n\t\t\t\"no-options-found\": \"Geen opties gevonden\"\n\t\t},\n\t\t\"options-error-placeholder\": {\n\t\t\t\"error-occurred-fetching-labels-click-retry\": \"Er is een fout opgetreden bij het ophalen van labels. Klik om opnieuw te proberen\"\n\t\t},\n\t\t\"test-object-with-variable-dependency\": {\n\t\t\ttitle: {\n\t\t\t\thello: \"Hallo\"\n\t\t\t}\n\t\t},\n\t\t\"test-variable\": {\n\t\t\ttext: {\n\t\t\t\ttext: \"Tekst\"\n\t\t\t}\n\t\t},\n\t\t\"variable-value-input\": {\n\t\t\t\"placeholder-enter-value\": \"Voer waarde in\"\n\t\t},\n\t\t\"variable-value-select\": {\n\t\t\t\"placeholder-select-value\": \"Waarde selecteren\"\n\t\t}\n\t}\n}\n};\n\nexport { grafanaScenes as default };\n//# sourceMappingURL=grafana-scenes.json.js.map\n"], "names": ["grafanaScenes", "components", "content", "subTitle", "title", "splitter", "explore", "utils", "variables", "label", "tooltip", "formats", "description", "apply", "hello", "text"], "sourceRoot": ""}