"use strict";(self.webpackChunkgrafana_exploretraces_app=self.webpackChunkgrafana_exploretraces_app||[]).push([[927],{1927:(e,t,a)=>{a.r(t),a.d(t,{TraceExplorationView:()=>i,default:()=>s});var n=a(5959),r=a.n(n),o=a(9262),c=a(1454),l=a(675),p=a(8010);const s=()=>{const e=localStorage.getItem(c.cd)||"",[t]=(0,n.useState)((0,o.em)(e));return r().createElement(i,{exploration:t})};function i({exploration:e}){const[t,a]=r().useState(!1);return(0,n.useEffect)(()=>{t||(a(!0),(0,l.EE)(l.NO.common,l.ir.common.app_initialized))},[e,t]),t?r().createElement(p.$L,{scene:e,updateUrlOnInit:!0,createBrowserHistorySteps:!0},r().createElement(e.Component,{model:e})):null}}}]);
//# sourceMappingURL=927.js.map