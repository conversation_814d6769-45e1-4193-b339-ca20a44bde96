"use strict";(self.webpackChunkgrafana_exploretraces_app=self.webpackChunkgrafana_exploretraces_app||[]).push([[190],{2190:(e,a,l)=>{l.r(a),l.d(a,{default:()=>r});var r={"grafana-scenes":{components:{"adhoc-filter-pill":{"edit-filter-with-key":"Editar filtro con la clave {{keyLabel}}","managed-filter":"Filtro gestionado de {{origin}}","remove-filter-with-key":"Eliminar filtro con la clave {{keyLabel}}"},"adhoc-filters-combobox":{"remove-filter-value":"Eliminar valor del filtro: {{itemLabel}}","use-custom-value":"Usar valor personalizado: {{itemLabel}}"},"fallback-page":{content:"Si ha llegado hasta aquí mediante un enlace, es posible que haya un error en esta aplicación.",subTitle:"La URL no coincide con ninguna página",title:"No se ha encontrado"},"nested-scene-renderer":{"collapse-button-label":"Contraer escena","expand-button-label":"Expandir escena","remove-button-label":"Eliminar escena"},"scene-debugger":{"object-details":"Detalles del objeto","scene-graph":"Gráfico de la escena","title-scene-debugger":"Depurador de escenas"},"scene-grid-row":{"collapse-row":"Contraer fila","expand-row":"Expandir fila"},"scene-time-range-compare-renderer":{"button-label":"Comparación","button-tooltip":"Habilitar comparación de intervalos de tiempo"},splitter:{"aria-label-pane-resize-widget":"Widget de cambio de tamaño del panel"},"viz-panel":{title:{title:"Título"}},"viz-panel-explore-button":{explore:"Explorar"},"viz-panel-renderer":{"loading-plugin-panel":"Cargando panel de plugins...","panel-plugin-has-no-panel-component":"El plugin del panel no tiene ningún componente de panel"},"viz-panel-series-limit":{"content-rendering-series-single-panel-impact-performance":"Representar demasiadas series en un solo panel puede afectar al rendimiento y dificultar la lectura de los datos.","warning-message":"Mostrando solo {{seriesLimit}} serie(s)"}},utils:{"controls-label":{"tooltip-remove":"Eliminar"},"loading-indicator":{"content-cancel-query":"Cancelar consulta"}},variables:{"ad-hoc-combobox":{"aria-label-edit-filter-operator":"Editar operador de filtro"},"ad-hoc-filter-builder":{"aria-label-add-filter":"Añadir filtro","title-add-filter":"Añadir filtro"},"ad-hoc-filter-renderer":{"aria-label-remove-filter":"Eliminar filtro","key-select":{"placeholder-select-label":"Seleccionar etiqueta"},"label-select-label":"Seleccionar etiqueta","title-remove-filter":"Eliminar filtro","value-select":{"placeholder-select-value":"Seleccionar valor"}},"data-source-variable":{label:{default:"predeterminada"}},"default-group-by-custom-indicator-container":{"aria-label-clear":"borrar",tooltip:"Aplicado de forma predeterminada en este dashboard. Si se edita, se transfiere a otros dashboards.","tooltip-restore-groupby-set-by-this-dashboard":"Restaura la función groupby definida por este dashboard."},"format-registry":{formats:{description:{"commaseparated-values":"Valores separados por comas","double-quoted-values":"Valores entre comillas dobles","format-date-in-different-ways":"Dar formato a la fecha de diferentes maneras","format-multivalued-variables-using-syntax-example":"Dar formato a las variables de múltiples valores con la sintaxis glob, por ejemplo, {value1,value2}","html-escaping-of-values":"Escape HTML de valores","json-stringify-value":"Valor de JSON stringify","keep-value-as-is":"Mantener el valor tal cual","multiple-values-are-formatted-like-variablevalue":"Los valores múltiples tienen el formato variable=valor","single-quoted-values":"Valores entre comillas simples","useful-escaping-values-taking-syntax-characters":"Útil para valores de escape URL, utilizando caracteres de sintaxis URI","useful-for-url-escaping-values":"Útil para valores de escape URL","values-are-separated-by-character":"Los valores están separados por el carácter |"}}},"group-by-variable-renderer":{"aria-label-group-by-selector":"Agrupar por selector","placeholder-group-by-label":"Agrupar por etiqueta"},"interval-variable":{"placeholder-select-value":"Seleccionar valor"},"loading-options-placeholder":{"loading-options":"Cargando opciones..."},"multi-value-apply-button":{apply:"Aplicar"},"no-options-placeholder":{"no-options-found":"No se han encontrado opciones"},"options-error-placeholder":{"error-occurred-fetching-labels-click-retry":"Se ha producido un error al recuperar las etiquetas. Haga clic para volver a intentarlo"},"test-object-with-variable-dependency":{title:{hello:"Hola"}},"test-variable":{text:{text:"Texto"}},"variable-value-input":{"placeholder-enter-value":"Introducir valor"},"variable-value-select":{"placeholder-select-value":"Seleccionar valor"}}}}}}]);
//# sourceMappingURL=190.js.map