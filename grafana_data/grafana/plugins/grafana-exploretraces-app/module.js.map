{"version": 3, "file": "module.js", "mappings": "sMACIA,EADAC,ECAAC,EACAC,E,YCDJC,EAAOC,QAAUC,C,WCAjBF,EAAOC,QAAUE,C,0hBCKV,eAAKC,G,yCAAAA,C,CAAL,C,IAKA,MAEMC,EAAqB,MAFTC,EAAAA,aAIZC,EAAoB,sCACpBC,EAA0B,4CAC1BC,EAAmB,qCAEnBC,EAAwB,uCAExBC,EAA4B,6BAC5BC,EAAmC,2DAEnCC,EAAmB,OAEnBC,EAAiB,KACjBC,EAAsB,QACtBC,EAAqB,gBACrBC,EAAc,UACdC,EAAmB,iCACnBC,EAAkB,aAClBC,EAAc,UACdC,EAAwB,kBACxBC,EAAa,SACbC,EAAwB,mBACxBC,EAA6B,sBAC7BC,EAAgC,0BAChCC,EAAqC,6BACrCC,EAAgB,CAAEC,IAAKb,GAEvBc,EAAc,aACdC,EAAiB,gBACjBC,EAAY,YAEZC,EAAM,MACNC,EAAW,WACXC,EAAO,OACPC,EAAgB,YAChBC,EAAY,QACZC,EAAa,SACbC,EAAkB,SAElBC,EAA0B,CAErC,wBACA,6BACA,2BAEA,mBACA,uBACA,qBAEA,kCAEA,8BACA,wBACA,8BACA,0BAEWC,EAAsB,CACjC,OACA,OACA,WACA,kBACA,SACA,gBACA,yBAEWC,EAAoB,CAC/B,WACA,aACA,gBACA,kBACA,iBACA,gBACA,UACA,iBACA,WACA,iBAEWC,EAA8B,CACzC,SACA,cACA,WACA,cACA,kBACA,iBACA,oBACA,yBAGWC,EAAa,IAgBnB,MAAMC,UAAoCC,EAAAA,qBAC/C,EADWD,EACGE,OAAO,4BAMhB,MAAMC,UAAyBF,EAAAA,qBACpC,EADWE,EACGD,OAAO,gBAGhB,MAAME,EAAyC,CACpD,CACEC,GAAI,gBACJC,QAAS,CACPC,QAAS,uB,WCpIfnD,EAAOC,QAAUmD,C,WCAjBpD,EAAOC,QAAUoD,C,gFCAjBrD,EAAOC,QAAUqD,C,WCAjBtD,EAAOC,QAAUsD,C,WCAjBvD,EAAOC,QAAUuD,C,WCAjBxD,EAAOC,QAAUwD,C,WCAjBzD,EAAOC,QAAUyD,C,WCAjB1D,EAAOC,QAAU0D,C,WCAjB3D,EAAOC,QAAU2D,C,GCCbC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBE,IAAjBD,EACH,OAAOA,EAAa/D,QAGrB,IAAID,EAAS6D,EAAyBE,GAAY,CACjDd,GAAIc,EACJG,QAAQ,EACRjE,QAAS,CAAC,GAUX,OANAkE,EAAoBJ,GAAUK,KAAKpE,EAAOC,QAASD,EAAQA,EAAOC,QAAS6D,GAG3E9D,EAAOkE,QAAS,EAGTlE,EAAOC,OACf,CAGA6D,EAAoBO,EAAIF,EC3BxBL,EAAoBQ,EAAKtE,IACxB,IAAIuE,EAASvE,GAAUA,EAAOwE,WAC7B,IAAOxE,EAAiB,QACxB,IAAM,EAEP,OADA8D,EAAoBW,EAAEF,EAAQ,CAAEG,EAAGH,IAC5BA,GfNJ1E,EAAW8E,OAAOC,eAAkBC,GAASF,OAAOC,eAAeC,GAASA,GAASA,EAAa,UAQtGf,EAAoBgB,EAAI,SAASC,EAAOC,GAEvC,GADU,EAAPA,IAAUD,EAAQE,KAAKF,IAChB,EAAPC,EAAU,OAAOD,EACpB,GAAoB,iBAAVA,GAAsBA,EAAO,CACtC,GAAW,EAAPC,GAAaD,EAAMP,WAAY,OAAOO,EAC1C,GAAW,GAAPC,GAAoC,mBAAfD,EAAMG,KAAqB,OAAOH,CAC5D,CACA,IAAII,EAAKR,OAAOS,OAAO,MACvBtB,EAAoBuB,EAAEF,GACtB,IAAIG,EAAM,CAAC,EACX1F,EAAiBA,GAAkB,CAAC,KAAMC,EAAS,CAAC,GAAIA,EAAS,IAAKA,EAASA,IAC/E,IAAI,IAAI0F,EAAiB,EAAPP,GAAYD,GAA0B,iBAAXQ,GAAyC,mBAAXA,MAA4B3F,EAAe4F,QAAQD,GAAUA,EAAU1F,EAAS0F,GAC1JZ,OAAOc,oBAAoBF,GAASG,QAASC,GAASL,EAAIK,GAAO,IAAOZ,EAAMY,IAI/E,OAFAL,EAAa,QAAI,IAAM,EACvBxB,EAAoBW,EAAEU,EAAIG,GACnBH,CACR,EgBxBArB,EAAoBW,EAAI,CAACxE,EAAS2F,KACjC,IAAI,IAAID,KAAOC,EACX9B,EAAoB+B,EAAED,EAAYD,KAAS7B,EAAoB+B,EAAE5F,EAAS0F,IAC5EhB,OAAOmB,eAAe7F,EAAS0F,EAAK,CAAEI,YAAY,EAAMC,IAAKJ,EAAWD,MCJ3E7B,EAAoBmC,EAAI,CAAC,EAGzBnC,EAAoBoC,EAAKC,GACjBC,QAAQC,IAAI1B,OAAO2B,KAAKxC,EAAoBmC,GAAGM,OAAO,CAACC,EAAUb,KACvE7B,EAAoBmC,EAAEN,GAAKQ,EAASK,GAC7BA,GACL,KCNJ1C,EAAoB2C,EAAKN,GAEZA,EAAU,MCHvBrC,EAAoB4C,EAAI,WACvB,GAA0B,iBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAO1B,MAAQ,IAAI2B,SAAS,cAAb,EAChB,CAAE,MAAOV,GACR,GAAsB,iBAAXW,OAAqB,OAAOA,MACxC,CACA,CAPuB,GCAxB/C,EAAoB+B,EAAI,CAAChB,EAAKiC,IAAUnC,OAAOoC,UAAUC,eAAe5C,KAAKS,EAAKiC,GnBA9EhH,EAAa,CAAC,EACdC,EAAoB,6BAExB+D,EAAoBmD,EAAI,CAACC,EAAKC,EAAMxB,EAAKQ,KACxC,GAAGrG,EAAWoH,GAAQpH,EAAWoH,GAAKE,KAAKD,OAA3C,CACA,IAAIE,EAAQC,EACZ,QAAWrD,IAAR0B,EAEF,IADA,IAAI4B,EAAUC,SAASC,qBAAqB,UACpCC,EAAI,EAAGA,EAAIH,EAAQI,OAAQD,IAAK,CACvC,IAAIE,EAAIL,EAAQG,GAChB,GAAGE,EAAEC,aAAa,QAAUX,GAAOU,EAAEC,aAAa,iBAAmB9H,EAAoB4F,EAAK,CAAE0B,EAASO,EAAG,KAAO,CACpH,CAEGP,IACHC,GAAa,GACbD,EAASG,SAASM,cAAc,WAEzBC,QAAU,QACjBV,EAAOW,QAAU,IACblE,EAAoBmE,IACvBZ,EAAOa,aAAa,QAASpE,EAAoBmE,IAElDZ,EAAOa,aAAa,eAAgBnI,EAAoB4F,GAExD0B,EAAOc,IAAMjB,GAEdpH,EAAWoH,GAAO,CAACC,GACnB,IAAIiB,EAAmB,CAACC,EAAMC,KAE7BjB,EAAOkB,QAAUlB,EAAOmB,OAAS,KACjCC,aAAaT,GACb,IAAIU,EAAU5I,EAAWoH,GAIzB,UAHOpH,EAAWoH,GAClBG,EAAOsB,YAActB,EAAOsB,WAAWC,YAAYvB,GACnDqB,GAAWA,EAAQhD,QAASmD,GAAQA,EAAGP,IACpCD,EAAM,OAAOA,EAAKC,IAElBN,EAAUc,WAAWV,EAAiBW,KAAK,UAAM9E,EAAW,CAAEnB,KAAM,UAAWkG,OAAQ3B,IAAW,MACtGA,EAAOkB,QAAUH,EAAiBW,KAAK,KAAM1B,EAAOkB,SACpDlB,EAAOmB,OAASJ,EAAiBW,KAAK,KAAM1B,EAAOmB,QACnDlB,GAAcE,SAASyB,KAAKC,YAAY7B,EApCkB,GoBH3DvD,EAAoBuB,EAAKpF,IACH,oBAAXkJ,QAA0BA,OAAOC,aAC1CzE,OAAOmB,eAAe7F,EAASkJ,OAAOC,YAAa,CAAErE,MAAO,WAE7DJ,OAAOmB,eAAe7F,EAAS,aAAc,CAAE8E,OAAO,KCLvDjB,EAAoBuF,IAAOrJ,IAC1BA,EAAOsJ,MAAQ,GACVtJ,EAAOuJ,WAAUvJ,EAAOuJ,SAAW,IACjCvJ,GCHR8D,EAAoB0F,EAAI,4C,MCKxB,IAAIC,EAAkB,CACrB,IAAK,GAGN3F,EAAoBmC,EAAEyD,EAAI,CAACvD,EAASK,KAElC,IAAImD,EAAqB7F,EAAoB+B,EAAE4D,EAAiBtD,GAAWsD,EAAgBtD,QAAWlC,EACtG,GAA0B,IAAvB0F,EAGF,GAAGA,EACFnD,EAASY,KAAKuC,EAAmB,QAC3B,CAGL,IAAIC,EAAU,IAAIxD,QAAQ,CAACyD,EAASC,IAAYH,EAAqBF,EAAgBtD,GAAW,CAAC0D,EAASC,IAC1GtD,EAASY,KAAKuC,EAAmB,GAAKC,GAGtC,IAAI1C,EAAMpD,EAAoB0F,EAAI1F,EAAoB2C,EAAEN,GAEpD4D,EAAQ,IAAIC,MAgBhBlG,EAAoBmD,EAAEC,EAfFoB,IACnB,GAAGxE,EAAoB+B,EAAE4D,EAAiBtD,KAEf,KAD1BwD,EAAqBF,EAAgBtD,MACRsD,EAAgBtD,QAAWlC,GACrD0F,GAAoB,CACtB,IAAIM,EAAY3B,IAAyB,SAAfA,EAAMxF,KAAkB,UAAYwF,EAAMxF,MAChEoH,EAAU5B,GAASA,EAAMU,QAAUV,EAAMU,OAAOb,IACpD4B,EAAMI,QAAU,iBAAmBhE,EAAU,cAAgB8D,EAAY,KAAOC,EAAU,IAC1FH,EAAMK,KAAO,iBACbL,EAAMjH,KAAOmH,EACbF,EAAMM,QAAUH,EAChBP,EAAmB,GAAGI,EACvB,GAGuC,SAAW5D,EAASA,EAE/D,GAeH,IAAImE,EAAuB,CAACC,EAA4BC,KACvD,IAGIzG,EAAUoC,GAHTsE,EAAUC,EAAaC,GAAWH,EAGhB9C,EAAI,EAC3B,GAAG+C,EAASG,KAAM3H,GAAgC,IAAxBwG,EAAgBxG,IAAa,CACtD,IAAIc,KAAY2G,EACZ5G,EAAoB+B,EAAE6E,EAAa3G,KACrCD,EAAoBO,EAAEN,GAAY2G,EAAY3G,IAG7C4G,GAAsBA,EAAQ7G,EAClC,CAEA,IADGyG,GAA4BA,EAA2BC,GACrD9C,EAAI+C,EAAS9C,OAAQD,IACzBvB,EAAUsE,EAAS/C,GAChB5D,EAAoB+B,EAAE4D,EAAiBtD,IAAYsD,EAAgBtD,IACrEsD,EAAgBtD,GAAS,KAE1BsD,EAAgBtD,GAAW,GAKzB0E,EAAqBC,KAA4C,sCAAIA,KAA4C,uCAAK,GAC1HD,EAAmBnF,QAAQ4E,EAAqBvB,KAAK,KAAM,IAC3D8B,EAAmBzD,KAAOkD,EAAqBvB,KAAK,KAAM8B,EAAmBzD,KAAK2B,KAAK8B,G,KCrFvF/G,EAAoBmE,QAAKhE,E,iFCGzB,MAAM8G,GAA4BC,EAAAA,EAAAA,MAChC,IAAM,4BAEFC,GAA2BD,EAAAA,EAAAA,MAC/B,IAAM,+D,cCeD,MAAME,EAAkF,CAC7F,CACEC,QAASC,EAAAA,sBAAsBC,mBAC/BC,MAAO,2BACPC,YAAa,iDACbC,KAAMC,IACNC,UAAYC,GAA0CC,EAAcD,IAEtE,CACER,QAASC,EAAAA,sBAAsBS,qBAC/BP,MAAO,mCACPC,YAAa,8CACbC,KAAMC,IACNC,UAAYC,GAA0CC,EAAcD,KAIjE,SAASC,EAAcD,G,IAMRG,EAIJA,EAQuBA,EAjBvC,IAAKH,EACH,OAGF,MAAMG,EAAaH,EAAQR,QAAQY,KAAM/C,I,IAAWA,E,MAA4B,WAAX,QAAjBA,EAAAA,EAAOgD,kBAAPhD,IAAAA,OAAAA,EAAAA,EAAmBlG,QACvE,IAAKgJ,KAAoC,QAArBA,EAAAA,EAAWE,kBAAXF,IAAAA,OAAAA,EAAAA,EAAuBlK,KACzC,OAGF,MAAMqK,EAA4B,QAAlBH,EAAAA,EAAWG,eAAXH,IAAAA,OAAAA,EAAAA,EAAoBI,OACjCA,GAAWA,EAAOC,OAASD,EAAOE,KAAOF,EAAOG,UAAYH,EAAOnH,OAASmH,EAAOnH,MAAM4C,QAE5F,IAAKsE,GAA8B,IAAnBA,EAAQtE,OACtB,OAGF,MAAM2E,EAAS,IAAIC,gBACnBD,EAAOE,OAAO,OAAO1L,EAAAA,MAAuC,QAArBgL,EAAAA,EAAWE,kBAAXF,IAAAA,OAAAA,EAAAA,EAAuBlK,MAAO,IAErE,MAAM6K,GAAkBC,EAAAA,EAAAA,YAAWf,EAAQgB,WAC3CL,EAAOE,OAAO,OAAQI,OAAOH,EAAgBI,OAC7CP,EAAOE,OAAO,KAAMI,OAAOH,EAAgBK,KAE3C,MAAMC,EAAed,EAAQF,KAAMG,GAA0B,WAAfA,EAAOE,KAerD,OAdIW,GACFT,EAAOE,OAAO,OAAOlL,EAAAA,KAAqC,UAAvByL,EAAahI,MAAoB,SAAW,QAGjFuH,EAAOE,OAAO,oBAAqB,QAEhB,CAACP,GACXA,EACJC,OAAQA,GAA0B,WAAfA,EAAOE,KAC1BY,IAAKd,GAAW,GAAGA,EAAOC,QA2CjC,SAA2BD,GACzB,OALF,SAAqBA,GACnB,OAAOe,EAAWrC,KAAMsC,GAAcA,EAAUd,MAAQF,EAAOE,KAAOc,EAAUf,QAAUD,EAAOC,MACnG,CAGSgB,CAAYjB,GAAU,IAAM,GACrC,CA7CyCkB,CAAkBlB,KAAUA,EAAOE,OAAOF,EAAOG,YAAYH,EAAOnH,SAE3GsI,CAAWpB,GAASvG,QAASwG,GAAWI,EAAOE,OAAO,OAAOvL,EAAAA,KAAeiL,IAGrE,CACLV,KAAM,GAFIC,EAAaa,KAI3B,CAEA,SAASb,EAAa6B,GACpB,MAAO,GAAGjN,EAAAA,KAAqBiN,EAAY,IAAIA,EAAUC,aAAe,IAC1E,CAEA,MAAMN,EAAa,CACjB,aACA,uBACA,uBACA,0BACA,cACA,eACA,gBACA,UACA,YACA,YACA,cACA,qBACA,iBACA,WACA,iBACA,qBACAD,IAAKQ,IACL,MAAOrB,EAAOC,GAAOoB,EAASC,MAAM,KACpC,MAAO,CACLtB,QACAC,SCrGEsB,GAAM1C,EAAAA,EAAAA,MAAK,IAAM,+BACjB2C,GAAY3C,EAAAA,EAAAA,MAAK,IAAM,+BAEhB4C,GAAS,IAAIC,EAAAA,WACvBC,YAAYJ,GACZK,cAAc,CACbzC,MAAO,gBACP0C,KAAM,MACNC,KAAMN,EACN1K,GAAI,kBAELiL,gBAAgB,CACfjL,GAAI,6DACJqI,MAAO,kCACPC,YAAa,iEACb4C,UFZG,SAA4CC,GACjD,OACE,kBAACC,EAAAA,SAAQA,CACPC,SACE,kBAACC,EAAAA,WAAUA,CAACC,QAAQ,YAAYC,UAAAA,GAAS,6BAK3C,kBAAC1D,EAA8BqD,GAGrC,IEEGF,gBAAgB,CACfjL,GAAI,0DACJqI,MAAO,6BACPC,YAAa,oGACb4C,UFJG,SAA2CC,GAChD,OACE,kBAACC,EAAAA,SAAQA,CAACC,SAAU,kBAACI,MAAAA,KAAI,eACvB,kBAACzD,EAA6BmD,GAGpC,IECA,IAAK,MAAMO,KAAczD,EACvB0C,EAAOgB,QAAQD,G", "sources": ["webpack://grafana-exploretraces-app/webpack/runtime/create fake namespace object", "webpack://grafana-exploretraces-app/webpack/runtime/load script", "webpack://grafana-exploretraces-app/external amd \"react-router\"", "webpack://grafana-exploretraces-app/external amd \"rxjs\"", "webpack://grafana-exploretraces-app/./utils/shared.ts", "webpack://grafana-exploretraces-app/external amd \"@grafana/ui\"", "webpack://grafana-exploretraces-app/external amd \"moment\"", "webpack://grafana-exploretraces-app/external amd \"lodash\"", "webpack://grafana-exploretraces-app/external amd \"react\"", "webpack://grafana-exploretraces-app/external amd \"@emotion/css\"", "webpack://grafana-exploretraces-app/external amd \"@grafana/data\"", "webpack://grafana-exploretraces-app/external amd \"react-dom\"", "webpack://grafana-exploretraces-app/external amd \"@grafana/runtime\"", "webpack://grafana-exploretraces-app/external amd \"@emotion/react\"", "webpack://grafana-exploretraces-app/webpack/bootstrap", "webpack://grafana-exploretraces-app/webpack/runtime/compat get default export", "webpack://grafana-exploretraces-app/webpack/runtime/define property getters", "webpack://grafana-exploretraces-app/webpack/runtime/ensure chunk", "webpack://grafana-exploretraces-app/webpack/runtime/get javascript chunk filename", "webpack://grafana-exploretraces-app/webpack/runtime/global", "webpack://grafana-exploretraces-app/webpack/runtime/hasOwnProperty shorthand", "webpack://grafana-exploretraces-app/webpack/runtime/make namespace object", "webpack://grafana-exploretraces-app/webpack/runtime/node module decorator", "webpack://grafana-exploretraces-app/webpack/runtime/publicPath", "webpack://grafana-exploretraces-app/webpack/runtime/jsonp chunk loading", "webpack://grafana-exploretraces-app/webpack/runtime/nonce", "webpack://grafana-exploretraces-app/./exposedComponents/index.tsx", "webpack://grafana-exploretraces-app/./utils/links.ts", "webpack://grafana-exploretraces-app/./module.tsx"], "sourcesContent": ["var getProto = Object.getPrototypeOf ? (obj) => (Object.getPrototypeOf(obj)) : (obj) => (obj.__proto__);\nvar leafPrototypes;\n// create a fake namespace object\n// mode & 1: value is a module id, require it\n// mode & 2: merge all properties of value into the ns\n// mode & 4: return value when already ns object\n// mode & 16: return value when it's Promise-like\n// mode & 8|1: behave like require\n__webpack_require__.t = function(value, mode) {\n\tif(mode & 1) value = this(value);\n\tif(mode & 8) return value;\n\tif(typeof value === 'object' && value) {\n\t\tif((mode & 4) && value.__esModule) return value;\n\t\tif((mode & 16) && typeof value.then === 'function') return value;\n\t}\n\tvar ns = Object.create(null);\n\t__webpack_require__.r(ns);\n\tvar def = {};\n\tleafPrototypes = leafPrototypes || [null, getProto({}), getProto([]), getProto(getProto)];\n\tfor(var current = mode & 2 && value; (typeof current == 'object' || typeof current == 'function') && !~leafPrototypes.indexOf(current); current = getProto(current)) {\n\t\tObject.getOwnPropertyNames(current).forEach((key) => (def[key] = () => (value[key])));\n\t}\n\tdef['default'] = () => (value);\n\t__webpack_require__.d(ns, def);\n\treturn ns;\n};", "var inProgress = {};\nvar dataWebpackPrefix = \"grafana-exploretraces-app:\";\n// loadScript function to load a script via script tag\n__webpack_require__.l = (url, done, key, chunkId) => {\n\tif(inProgress[url]) { inProgress[url].push(done); return; }\n\tvar script, needAttach;\n\tif(key !== undefined) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tfor(var i = 0; i < scripts.length; i++) {\n\t\t\tvar s = scripts[i];\n\t\t\tif(s.getAttribute(\"src\") == url || s.getAttribute(\"data-webpack\") == dataWebpackPrefix + key) { script = s; break; }\n\t\t}\n\t}\n\tif(!script) {\n\t\tneedAttach = true;\n\t\tscript = document.createElement('script');\n\n\t\tscript.charset = 'utf-8';\n\t\tscript.timeout = 120;\n\t\tif (__webpack_require__.nc) {\n\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n\t\t}\n\t\tscript.setAttribute(\"data-webpack\", dataWebpackPrefix + key);\n\n\t\tscript.src = url;\n\t}\n\tinProgress[url] = [done];\n\tvar onScriptComplete = (prev, event) => {\n\t\t// avoid mem leaks in IE.\n\t\tscript.onerror = script.onload = null;\n\t\tclearTimeout(timeout);\n\t\tvar doneFns = inProgress[url];\n\t\tdelete inProgress[url];\n\t\tscript.parentNode && script.parentNode.removeChild(script);\n\t\tdoneFns && doneFns.forEach((fn) => (fn(event)));\n\t\tif(prev) return prev(event);\n\t}\n\tvar timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);\n\tscript.onerror = onScriptComplete.bind(null, script.onerror);\n\tscript.onload = onScriptComplete.bind(null, script.onload);\n\tneedAttach && document.head.appendChild(script);\n};", "module.exports = __WEBPACK_EXTERNAL_MODULE__1159__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__1269__;", "import { BusEventWithPayload, DataFrame } from '@grafana/data';\nimport pluginJson from '../plugin.json';\n\nexport type MetricFunction = 'rate' | 'errors' | 'duration';\n\nexport enum ROUTES {\n  Explore = 'explore',\n  Home = 'home',\n}\n\nexport const PLUGIN_ID = pluginJson.id;\nexport const PLUGIN_BASE_URL = `/a/${PLUGIN_ID}`;\nexport const EXPLORATIONS_ROUTE = `${PLUGIN_BASE_URL}/${ROUTES.Explore}`;\n\nexport const DATASOURCE_LS_KEY = 'grafana.drilldown.traces.datasource';\nexport const HOMEPAGE_FILTERS_LS_KEY = 'grafana.drilldown.traces.homepage.filters';\nexport const BOOKMARKS_LS_KEY = 'grafana.drilldown.traces.bookmarks';\n\nexport const GRID_TEMPLATE_COLUMNS = 'repeat(auto-fit, minmax(400px, 1fr))';\n\nexport const EMPTY_STATE_ERROR_MESSAGE = 'No data for selected query';\nexport const EMPTY_STATE_ERROR_REMEDY_MESSAGE = 'Please try removing some filters or changing your query.';\n\nexport const FILTER_SEPARATOR = ' && ';\n\nexport const VAR_DATASOURCE = 'ds';\nexport const VAR_DATASOURCE_EXPR = '${ds}';\nexport const VAR_PRIMARY_SIGNAL = 'primarySignal';\nexport const VAR_FILTERS = 'filters';\nexport const VAR_FILTERS_EXPR = '${primarySignal} && ${filters}';\nexport const VAR_HOME_FILTER = 'homeFilter';\nexport const VAR_GROUPBY = 'groupBy';\nexport const VAR_SPAN_LIST_COLUMNS = 'spanListColumns';\nexport const VAR_METRIC = 'metric';\nexport const VAR_LATENCY_THRESHOLD = 'latencyThreshold';\nexport const VAR_LATENCY_THRESHOLD_EXPR = '${latencyThreshold}';\nexport const VAR_LATENCY_PARTIAL_THRESHOLD = 'partialLatencyThreshold';\nexport const VAR_LATENCY_PARTIAL_THRESHOLD_EXPR = '${partialLatencyThreshold}';\nexport const explorationDS = { uid: VAR_DATASOURCE_EXPR };\n\nexport const ACTION_VIEW = 'actionView';\nexport const PRIMARY_SIGNAL = 'primarySignal';\nexport const SELECTION = 'selection';\n\nexport const ALL = 'All';\nexport const RESOURCE = 'Resource';\nexport const SPAN = 'Span';\nexport const RESOURCE_ATTR = 'resource.';\nexport const SPAN_ATTR = 'span.';\nexport const EVENT_ATTR = 'event.';\nexport const EVENT_INTRINSIC = 'event:';\n\nexport const radioAttributesResource = [\n  // https://opentelemetry.io/docs/specs/semconv/resource/\n  'resource.service.name',\n  'resource.service.namespace',\n  'resource.service.version',\n  // custom\n  'resource.cluster',\n  'resource.environment',\n  'resource.namespace',\n  // https://opentelemetry.io/docs/specs/semconv/resource/deployment-environment/\n  'resource.deployment.environment',\n  // https://opentelemetry.io/docs/specs/semconv/resource/k8s/\n  'resource.k8s.namespace.name',\n  'resource.k8s.pod.name',\n  'resource.k8s.container.name',\n  'resource.k8s.node.name',\n];\nexport const radioAttributesSpan = [\n  'name',\n  'kind',\n  'rootName',\n  'rootServiceName',\n  'status',\n  'statusMessage',\n  'span.http.status_code',\n];\nexport const ignoredAttributes = [\n  'duration',\n  'event:name',\n  'nestedSetLeft',\n  'nestedSetParent',\n  'nestedSetRight',\n  'span:duration',\n  'span:id',\n  'trace:duration',\n  'trace:id',\n  'traceDuration',\n];\nexport const ignoredAttributesHomeFilter = [\n  'status',\n  'span:status',\n  'rootName',\n  'rootService',\n  'rootServiceName',\n  'trace:rootName',\n  'trace:rootService',\n  'trace:rootServiceName',\n];\n// Limit maximum options in select dropdowns for performance reasons\nexport const maxOptions = 1000;\n\nexport type MakeOptional<T, K extends keyof T> = Pick<Partial<T>, K> & Omit<T, K>;\n\nexport interface ComparisonSelection {\n  type: 'auto' | 'manual';\n  raw?: { x: { from: number; to: number }; y: { from: number; to: number } };\n  timeRange?: { from: number; to: number };\n  duration?: { from: string; to: string };\n  query?: string;\n}\n\nexport interface EventTimeseriesDataReceivedPayload {\n  series?: DataFrame[];\n}\n\nexport class EventTimeseriesDataReceived extends BusEventWithPayload<EventTimeseriesDataReceivedPayload> {\n  public static type = 'timeseries-data-received';\n}\nexport interface EventTraceOpenedPayload {\n  traceId: string;\n  spanId?: string;\n}\nexport class EventTraceOpened extends BusEventWithPayload<EventTraceOpenedPayload> {\n  public static type = 'trace-opened';\n}\n\nexport const filterStreamingProgressTransformations = [\n  {\n    id: 'filterByRefId',\n    options: {\n      exclude: 'streaming-progress',\n    },\n  },\n];\n", "module.exports = __WEBPACK_EXTERNAL_MODULE__2007__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__2468__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__3241__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__5959__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__6089__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__7781__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__8398__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__8531__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__9089__;", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\tloaded: false,\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Flag the module as loaded\n\tmodule.loaded = true;\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.f = {};\n// This file contains only the entry chunk.\n// The chunk loading function for additional chunks\n__webpack_require__.e = (chunkId) => {\n\treturn Promise.all(Object.keys(__webpack_require__.f).reduce((promises, key) => {\n\t\t__webpack_require__.f[key](chunkId, promises);\n\t\treturn promises;\n\t}, []));\n};", "// This function allow to reference async chunks\n__webpack_require__.u = (chunkId) => {\n\t// return url for filenames based on template\n\treturn \"\" + chunkId + \".js\";\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.nmd = (module) => {\n\tmodule.paths = [];\n\tif (!module.children) module.children = [];\n\treturn module;\n};", "__webpack_require__.p = \"public/plugins/grafana-exploretraces-app/\";", "// no baseURI\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t231: 0\n};\n\n__webpack_require__.f.j = (chunkId, promises) => {\n\t\t// JSONP chunk loading for javascript\n\t\tvar installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;\n\t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n\t\t\t// a Promise means \"currently loading\".\n\t\t\tif(installedChunkData) {\n\t\t\t\tpromises.push(installedChunkData[2]);\n\t\t\t} else {\n\t\t\t\tif(true) { // all chunks have JS\n\t\t\t\t\t// setup Promise in chunk cache\n\t\t\t\t\tvar promise = new Promise((resolve, reject) => (installedChunkData = installedChunks[chunkId] = [resolve, reject]));\n\t\t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n\t\t\t\t\t// start chunk loading\n\t\t\t\t\tvar url = __webpack_require__.p + __webpack_require__.u(chunkId);\n\t\t\t\t\t// create error before stack unwound to get useful stacktrace later\n\t\t\t\t\tvar error = new Error();\n\t\t\t\t\tvar loadingEnded = (event) => {\n\t\t\t\t\t\tif(__webpack_require__.o(installedChunks, chunkId)) {\n\t\t\t\t\t\t\tinstalledChunkData = installedChunks[chunkId];\n\t\t\t\t\t\t\tif(installedChunkData !== 0) installedChunks[chunkId] = undefined;\n\t\t\t\t\t\t\tif(installedChunkData) {\n\t\t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n\t\t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n\t\t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n\t\t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n\t\t\t\t\t\t\t\terror.type = errorType;\n\t\t\t\t\t\t\t\terror.request = realSrc;\n\t\t\t\t\t\t\t\tinstalledChunkData[1](error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\t__webpack_require__.l(url, loadingEnded, \"chunk-\" + chunkId, chunkId);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n};\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n// no on chunks loaded\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkgrafana_exploretraces_app\"] = self[\"webpackChunkgrafana_exploretraces_app\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "__webpack_require__.nc = undefined;", "import { LinkButton } from '@grafana/ui';\nimport { OpenInExploreTracesButtonProps, EmbeddedTraceExplorationState } from 'exposedComponents/types';\nimport React, { lazy, Suspense } from 'react';\nconst OpenInExploreTracesButton = lazy(\n  () => import('exposedComponents/OpenInExploreTracesButton/OpenInExploreTracesButton')\n);\nconst EmbeddedTraceExploration = lazy(\n  () => import('exposedComponents/EmbeddedTraceExploration/EmbeddedTraceExploration')\n);\n\nexport function SuspendedOpenInExploreTracesButton(props: OpenInExploreTracesButtonProps) {\n  return (\n    <Suspense\n      fallback={\n        <LinkButton variant=\"secondary\" disabled>\n          Open in Traces Drilldown\n        </LinkButton>\n      }\n    >\n      <OpenInExploreTracesButton {...props} />\n    </Suspense>\n  );\n}\n\nexport function SuspendedEmbeddedTraceExploration(props: EmbeddedTraceExplorationState) {\n  return (\n    <Suspense fallback={<div>Loading...</div>}>\n      <EmbeddedTraceExploration {...props} />\n    </Suspense>\n  );\n}\n", "import {\n  PluginExtensionAddedLinkConfig,\n  PluginExtensionPanelContext,\n  PluginExtensionPoints,\n  toURLRange,\n} from '@grafana/data';\n\nimport { DataSourceRef } from '@grafana/schema';\nimport { EXPLORATIONS_ROUTE, VAR_DATASOURCE, VAR_FILTERS, VAR_METRIC } from './shared';\n\ntype TempoQuery = {\n  filters?: TraceqlFilter[];\n  datasource?: DataSourceRef;\n};\n\nexport interface TraceqlFilter {\n  scope?: string;\n  tag?: string;\n  operator?: string;\n  value?: string | string[];\n}\n\nexport const linkConfigs: Array<PluginExtensionAddedLinkConfig<PluginExtensionPanelContext>> = [\n  {\n    targets: PluginExtensionPoints.DashboardPanelMenu,\n    title: 'Open in Traces Drilldown',\n    description: 'Open current query in the Traces Drilldown app',\n    path: createAppUrl(),\n    configure: (context?: PluginExtensionPanelContext) => contextToLink(context),\n  } as PluginExtensionAddedLinkConfig,\n  {\n    targets: PluginExtensionPoints.ExploreToolbarAction,\n    title: 'Open in Grafana Traces Drilldown',\n    description: 'Try our new queryless experience for traces',\n    path: createAppUrl(),\n    configure: (context?: PluginExtensionPanelContext) => contextToLink(context),\n  } as PluginExtensionAddedLinkConfig,\n];\n\nexport function contextToLink(context?: PluginExtensionPanelContext) {\n  if (!context) {\n    return undefined;\n  }\n\n  const tempoQuery = context.targets.find((target) => target.datasource?.type === 'tempo') as TempoQuery | undefined;\n  if (!tempoQuery || !tempoQuery.datasource?.uid) {\n    return undefined;\n  }\n\n  const filters = tempoQuery.filters?.filter(\n    (filter) => filter.scope && filter.tag && filter.operator && filter.value && filter.value.length\n  );\n  if (!filters || filters.length === 0) {\n    return undefined;\n  }\n\n  const params = new URLSearchParams();\n  params.append(`var-${VAR_DATASOURCE}`, tempoQuery.datasource?.uid || '');\n\n  const timeRangeParams = toURLRange(context.timeRange);\n  params.append(`from`, String(timeRangeParams.from));\n  params.append(`to`, String(timeRangeParams.to));\n\n  const statusFilter = filters.find((filter) => filter.tag === 'status');\n  if (statusFilter) {\n    params.append(`var-${VAR_METRIC}`, statusFilter.value === 'error' ? 'errors' : 'rate');\n  }\n\n  params.append('var-primarySignal', 'true');\n\n  const getFilters = (filters: TraceqlFilter[]) => {\n    return filters\n      .filter((filter) => filter.tag !== 'status')\n      .map((filter) => `${filter.scope}${getScopeSeparator(filter)}${filter.tag}|${filter.operator}|${filter.value}`);\n  };\n  getFilters(filters).forEach((filter) => params.append(`var-${VAR_FILTERS}`, filter));\n\n  const url = createAppUrl(params);\n  return {\n    path: `${url}`,\n  };\n}\n\nfunction createAppUrl(urlParams?: URLSearchParams): string {\n  return `${EXPLORATIONS_ROUTE}${urlParams ? `?${urlParams.toString()}` : ''}`;\n}\n\nconst intrinsics = [\n  'event:name',\n  'event:timeSinceStart',\n  'instrumentation:name',\n  'instrumentation:version',\n  'link:spanID',\n  'link:traceID',\n  'span:duration',\n  'span:id',\n  'span:kind',\n  'span:name',\n  'span:status',\n  'span:statusMessage',\n  'trace:duration',\n  'trace:id',\n  'trace:rootName',\n  'trace:rootService',\n].map((fullName) => {\n  const [scope, tag] = fullName.split(':');\n  return {\n    scope,\n    tag,\n  };\n});\n\nfunction isIntrinsic(filter: TraceqlFilter) {\n  return intrinsics.some((intrinsic) => intrinsic.tag === filter.tag && intrinsic.scope === filter.scope);\n}\n\nfunction getScopeSeparator(filter: TraceqlFilter) {\n  return isIntrinsic(filter) ? ':' : '.';\n}\n", "import { lazy } from 'react';\nimport { AppPlugin } from '@grafana/data';\n\nimport { EmbeddedTraceExplorationState, OpenInExploreTracesButtonProps } from 'exposedComponents/types';\nimport { SuspendedEmbeddedTraceExploration, SuspendedOpenInExploreTracesButton } from 'exposedComponents';\nimport { linkConfigs } from 'utils/links';\n\nconst App = lazy(() => import('./components/App/App'));\nconst AppConfig = lazy(() => import('./components/AppConfig/AppConfig'));\n\nexport const plugin = new AppPlugin<{}>()\n  .setRootPage(App)\n  .addConfigPage({\n    title: 'Configuration',\n    icon: 'cog',\n    body: AppConfig,\n    id: 'configuration',\n  })\n  .exposeComponent({\n    id: 'grafana-exploretraces-app/open-in-explore-traces-button/v1',\n    title: 'Open in Traces Drilldown button',\n    description: 'A button that opens a traces view in the Traces drilldown app.',\n    component: SuspendedOpenInExploreTracesButton as React.ComponentType<OpenInExploreTracesButtonProps>,\n  })\n  .exposeComponent({\n    id: 'grafana-exploretraces-app/embedded-trace-exploration/v1',\n    title: 'Embedded Trace Exploration',\n    description: 'A component that renders a trace exploration view that can be embedded in other parts of Grafana.',\n    component: SuspendedEmbeddedTraceExploration as React.ComponentType<EmbeddedTraceExplorationState>,\n  });\n\nfor (const linkConfig of linkConfigs) {\n  plugin.addLink(linkConfig);\n}\n"], "names": ["leafPrototypes", "getProto", "inProgress", "dataWebpackPrefix", "module", "exports", "__WEBPACK_EXTERNAL_MODULE__1159__", "__WEBPACK_EXTERNAL_MODULE__1269__", "ROUTES", "EXPLORATIONS_ROUTE", "pluginJson", "DATASOURCE_LS_KEY", "HOMEPAGE_FILTERS_LS_KEY", "BOOKMARKS_LS_KEY", "GRID_TEMPLATE_COLUMNS", "EMPTY_STATE_ERROR_MESSAGE", "EMPTY_STATE_ERROR_REMEDY_MESSAGE", "FILTER_SEPARATOR", "VAR_DATASOURCE", "VAR_DATASOURCE_EXPR", "VAR_PRIMARY_SIGNAL", "VAR_FILTERS", "VAR_FILTERS_EXPR", "VAR_HOME_FILTER", "VAR_GROUPBY", "VAR_SPAN_LIST_COLUMNS", "VAR_METRIC", "VAR_LATENCY_THRESHOLD", "VAR_LATENCY_THRESHOLD_EXPR", "VAR_LATENCY_PARTIAL_THRESHOLD", "VAR_LATENCY_PARTIAL_THRESHOLD_EXPR", "explorationDS", "uid", "ACTION_VIEW", "PRIMARY_SIGNAL", "SELECTION", "ALL", "RESOURCE", "SPAN", "RESOURCE_ATTR", "SPAN_ATTR", "EVENT_ATTR", "EVENT_INTRINSIC", "radioAttributesResource", "radioAttributesSpan", "ignoredAttributes", "ignoredAttributesHomeFilter", "maxOptions", "EventTimeseriesDataReceived", "BusEventWithPayload", "type", "EventTraceOpened", "filterStreamingProgressTransformations", "id", "options", "exclude", "__WEBPACK_EXTERNAL_MODULE__2007__", "__WEBPACK_EXTERNAL_MODULE__2468__", "__WEBPACK_EXTERNAL_MODULE__3241__", "__WEBPACK_EXTERNAL_MODULE__5959__", "__WEBPACK_EXTERNAL_MODULE__6089__", "__WEBPACK_EXTERNAL_MODULE__7781__", "__WEBPACK_EXTERNAL_MODULE__8398__", "__WEBPACK_EXTERNAL_MODULE__8531__", "__WEBPACK_EXTERNAL_MODULE__9089__", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "undefined", "loaded", "__webpack_modules__", "call", "m", "n", "getter", "__esModule", "d", "a", "Object", "getPrototypeOf", "obj", "t", "value", "mode", "this", "then", "ns", "create", "r", "def", "current", "indexOf", "getOwnPropertyNames", "for<PERSON>ach", "key", "definition", "o", "defineProperty", "enumerable", "get", "f", "e", "chunkId", "Promise", "all", "keys", "reduce", "promises", "u", "g", "globalThis", "Function", "window", "prop", "prototype", "hasOwnProperty", "l", "url", "done", "push", "script", "<PERSON><PERSON><PERSON><PERSON>", "scripts", "document", "getElementsByTagName", "i", "length", "s", "getAttribute", "createElement", "charset", "timeout", "nc", "setAttribute", "src", "onScriptComplete", "prev", "event", "onerror", "onload", "clearTimeout", "doneFns", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "fn", "setTimeout", "bind", "target", "head", "append<PERSON><PERSON><PERSON>", "Symbol", "toStringTag", "nmd", "paths", "children", "p", "installedChunks", "j", "installedChunkData", "promise", "resolve", "reject", "error", "Error", "errorType", "realSrc", "message", "name", "request", "webpackJsonpCallback", "parentChunkLoadingFunction", "data", "chunkIds", "moreModules", "runtime", "some", "chunkLoadingGlobal", "self", "OpenInExploreTracesButton", "lazy", "EmbeddedTraceExploration", "linkConfigs", "targets", "PluginExtensionPoints", "DashboardPanelMenu", "title", "description", "path", "createAppUrl", "configure", "context", "contextToLink", "ExploreToolbarAction", "tempo<PERSON>uery", "find", "datasource", "filters", "filter", "scope", "tag", "operator", "params", "URLSearchParams", "append", "timeRangeParams", "toURLRange", "timeRange", "String", "from", "to", "statusFilter", "map", "intrinsics", "intrinsic", "isIntrinsic", "getScopeSeparator", "getFilters", "urlParams", "toString", "fullName", "split", "App", "AppConfig", "plugin", "AppPlugin", "setRootPage", "addConfigPage", "icon", "body", "exposeComponent", "component", "props", "Suspense", "fallback", "LinkButton", "variant", "disabled", "div", "linkConfig", "addLink"], "sourceRoot": ""}