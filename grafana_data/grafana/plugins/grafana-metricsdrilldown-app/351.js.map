{"version": 3, "file": "351.js?_cache=e38667dbb2a5d05e8764", "mappings": "0RAMO,SAASA,IACd,MAAMC,GAASC,EAAAA,EAAAA,YAAWC,GACpBC,GAAQC,EAAAA,EAAAA,aACd,OACE,kBAACC,MAAAA,CAAIC,UAAWN,EAAOO,MACrB,kBAACF,MAAAA,CAAIC,UAAWN,EAAOQ,kBACrB,kBAACC,EAAAA,EAAGA,CACFC,KAEEP,EAAMQ,OACF,gEAKV,kBAACN,MAAAA,CAAIC,UAAWN,EAAOY,MACrB,kBAACC,KAAAA,CAAGP,UAAWN,EAAOc,OAAO,wCAE7B,kBAACC,IAAAA,KAAE,2DAED,kBAACC,KAAAA,MAAK,QACA,IACN,kBAACC,IAAAA,CAAEX,UAAU,gBAAgBY,KAAMC,EAAAA,aAAaC,cAAc,iCAAiC,yBAE1F,IAAI,oBAIX,kBAACJ,KAAAA,MAED,kBAACD,IAAAA,KAAE,QACK,IACN,kBAACE,IAAAA,CACCC,KAAK,kFACLG,OAAO,SACPf,UAAU,gBACVgB,IAAI,cACL,YAEI,IAAI,mBAET,kBAACN,KAAAA,MACD,kBAACC,IAAAA,CACCC,KAAK,oEACLG,OAAO,SACPf,UAAU,gBACVgB,IAAI,cACL,iBAEI,IAAI,qBAMnB,CAEA,MAAMpB,EAAaC,IACV,CACLK,kBAAkBe,EAAAA,EAAAA,KAAI,CACpB,CAACpB,EAAMqB,YAAYC,GAAG,OAAQ,CAC5BC,UAAW,WACXC,OAAQ,OACRC,QAASzB,EAAM0B,QAAQ,GACvBC,MAAO,SAET,CAAC3B,EAAMqB,YAAYC,GAAG,OAAQ,CAC5BC,UAAW,WACXC,OAAQ,OACRC,QAASzB,EAAM0B,QAAQ,GACvBC,MAAO,SAETC,QAAS,OACTJ,OAAQ,QACRK,eAAgB,SAChBC,OAAQ,SACRL,QAASzB,EAAM0B,QAAQ,GACvBC,MAAO,UAGTlB,MAAMW,EAAAA,EAAAA,KAAI,CACRW,WAAY,SACZH,QAAS,OACTI,cAAe,SACfH,eAAgB,WAElBlB,OAAOS,EAAAA,EAAAA,KAAI,CACTa,aAAc,WAEhB7B,MAAMgB,EAAAA,EAAAA,KAAI,CACR,CAACpB,EAAMqB,YAAYC,GAAG,OAAQ,CAC5BU,cAAe,MACfF,OAAQ,uBAEVC,WAAY,SACZH,QAAS,OACTI,cAAe,SACfF,OAAQ,mBACRL,QAAS,OACTS,UAAW,a,0DCrGV,MAAMC,GAAqBC,EAAAA,EAAAA,eAAmC,OCerEC,EAAAA,EAAAA,MAEA,MAAMC,EAAwBC,OAAOC,OAAOC,EAAAA,OAAOC,aAAaC,OAAOC,EAAAA,IAEvE,IACEC,EAAAA,EAAYC,SACd,CAAE,MAAOC,GACPC,EAAAA,EAAOD,MAAMA,EAAgB,CAAEE,MAAO,8BACxC,CAEe,SAASC,EAAIC,GAC1B,MAAMtD,GAASC,EAAAA,EAAAA,YAAWC,IACnBgD,IAASK,EAAAA,EAAAA,MACV,MAAEC,EAAK,gBAAEC,IAAoBC,EAAAA,EAAAA,KAInC,OChCK,WACL,MAAMC,GAAUC,EAAAA,EAAAA,SAAO,IAEvBC,EAAAA,EAAAA,WAAU,KACR,IAAKF,EAAQG,QAAS,CACpBH,EAAQG,SAAU,EAElB,MAAMC,EAAM,IAAIC,IAAIC,OAAOC,SAAShD,MAC9BiD,EAAiBJ,EAAIK,aAAaC,IAAI,UACxC,iBACA,kB,IACaN,EAAjB,MAAMO,EAAgC,QAArBP,EAAAA,EAAIK,aAAaC,IAAI,mBAArBN,IAAAA,EAAAA,EAAoC,IAErDQ,EAAAA,EAAAA,GAAqB,kBAAmB,CAAEJ,OAAMG,YAClD,GACC,GACL,CDcEE,GAEItB,EAEA,kBAAC7C,MAAAA,CAAIC,UAAWN,EAAOyE,aAAcC,cAAY,yBAC/C,kBAACC,EAAAA,EAASA,CAACzB,MAAOA,KAKnBT,EAAsBmC,OAKzB,kBAACvE,MAAAA,CAAIC,UAAWN,EAAOyE,aAAcC,cAAY,yBAC/C,kBAACpC,EAAmBuC,SAAQ,CAACC,MAAOxB,GAClC,kBAACyB,EAAAA,EAAeF,SAAQ,CAACC,MAAO,CAAEtB,QAAOC,oBACvC,kBAACuB,EAAAA,EAASA,SAPT,kBAACjF,EAAUA,KAYtB,CAEA,SAASG,EAAUC,GACjB,MAAO,CACLsE,cAAclD,EAAAA,EAAAA,KAAI,CAChBQ,QAAS,OACTI,cAAe,SACfR,OAAQ,OACRsD,gBAAiB9E,EAAM+E,OAAOC,WAAWC,UAG/C,C", "sources": ["webpack://grafana-metricsdrilldown-app/./App/Onboarding.tsx", "webpack://grafana-metricsdrilldown-app/./utils/utils.plugin.ts", "webpack://grafana-metricsdrilldown-app/./App/App.tsx", "webpack://grafana-metricsdrilldown-app/./App/useReportAppInitialized.ts"], "sourcesContent": ["import { css } from '@emotion/css';\nimport { locationUtil, type GrafanaTheme2 } from '@grafana/data';\nimport { useStyles2, useTheme2 } from '@grafana/ui';\nimport React from 'react';\nimport SVG from 'react-inlinesvg';\n\nexport function Onboarding() {\n  const styles = useStyles2(getStyles);\n  const theme = useTheme2();\n  return (\n    <div className={styles.wrap}>\n      <div className={styles.graphicContainer}>\n        <SVG\n          src={\n            // eslint-disable-next-line sonarjs/no-all-duplicated-branches\n            theme.isDark\n              ? `/public/plugins/grafana-metricsdrilldown-app/img/logo.svg`\n              : `/public/plugins/grafana-metricsdrilldown-app/img/logo.svg`\n          }\n        />\n      </div>\n      <div className={styles.text}>\n        <h3 className={styles.title}>Welcome to Grafana Metrics Drilldown</h3>\n\n        <p>\n          We noticed there is no Prometheus datasource configured.\n          <br />\n          Add a{' '}\n          <a className=\"external-link\" href={locationUtil.assureBaseUrl('/connections/datasources/new')}>\n            Prometheus datasource\n          </a>{' '}\n          to view metrics.\n        </p>\n\n        <br />\n\n        <p>\n          Check{' '}\n          <a\n            href=\"https://grafana.com/docs/grafana/latest/explore/simplified-exploration/metrics/\"\n            target=\"_blank\"\n            className=\"external-link\"\n            rel=\"noreferrer\"\n          >\n            our docs\n          </a>{' '}\n          to learn more or\n          <br />\n          <a\n            href=\"https://play.grafana.org/a/grafana-metricsdrilldown-app/drilldown\"\n            target=\"_blank\"\n            className=\"external-link\"\n            rel=\"noreferrer\"\n          >\n            try it online\n          </a>{' '}\n          in Grafana Play!\n        </p>\n      </div>\n    </div>\n  );\n}\n\nconst getStyles = (theme: GrafanaTheme2) => {\n  return {\n    graphicContainer: css({\n      [theme.breakpoints.up('md')]: {\n        alignSelf: 'flex-end',\n        height: 'auto',\n        padding: theme.spacing(1),\n        width: '300px',\n      },\n      [theme.breakpoints.up('lg')]: {\n        alignSelf: 'flex-end',\n        height: 'auto',\n        padding: theme.spacing(1),\n        width: '400px',\n      },\n      display: 'flex',\n      height: '250px',\n      justifyContent: 'center',\n      margin: '0 auto',\n      padding: theme.spacing(1),\n      width: '200px',\n    }),\n\n    text: css({\n      alignItems: 'center',\n      display: 'flex',\n      flexDirection: 'column',\n      justifyContent: 'center',\n    }),\n    title: css({\n      marginBottom: '1.5rem',\n    }),\n    wrap: css({\n      [theme.breakpoints.up('md')]: {\n        flexDirection: 'row',\n        margin: '4rem auto auto auto',\n      },\n      alignItems: 'center',\n      display: 'flex',\n      flexDirection: 'column',\n      margin: '0 auto auto auto',\n      padding: '2rem',\n      textAlign: 'center',\n    }),\n  };\n};\n", "import { type AppRootProps } from '@grafana/data';\nimport { createContext } from 'react';\n\n// This is used to be able to retrieve the root plugin props anywhere inside the app.\nexport const PluginPropsContext = createContext<AppRootProps | null>(null);\n\n// eslint-disable-next-line sonarjs/no-commented-code\n// export const usePluginProps = () => {\n//   const pluginProps = useContext(PluginPropsContext);\n//   return pluginProps;\n// };\n", "import { css } from '@emotion/css';\nimport { type AppRootProps, type GrafanaTheme2 } from '@grafana/data';\nimport { config } from '@grafana/runtime';\nimport { useStyles2 } from '@grafana/ui';\nimport React from 'react';\n\nimport { initFaro } from 'tracking/faro/faro';\nimport { logger } from 'tracking/logger/logger';\nimport { userStorage } from 'UserPreferences/userStorage';\n\nimport { ErrorView } from './ErrorView';\nimport { Onboarding } from './Onboarding';\nimport { AppRoutes } from './Routes';\nimport { useCatchExceptions } from './useCatchExceptions';\nimport { useReportAppInitialized } from './useReportAppInitialized';\nimport { MetricsContext, useTrail } from './useTrail';\nimport { isPrometheusDataSource } from '../utils/utils.datasource';\nimport { PluginPropsContext } from '../utils/utils.plugin';\n\ninitFaro();\n\nconst prometheusDatasources = Object.values(config.datasources).filter(isPrometheusDataSource);\n\ntry {\n  userStorage.migrate();\n} catch (error) {\n  logger.error(error as Error, { cause: 'User preferences migration' });\n}\n\nexport default function App(props: Readonly<AppRootProps>) {\n  const styles = useStyles2(getStyles);\n  const [error] = useCatchExceptions();\n  const { trail, goToUrlForTrail } = useTrail();\n\n  useReportAppInitialized();\n\n  if (error) {\n    return (\n      <div className={styles.appContainer} data-testid=\"metrics-drilldown-app\">\n        <ErrorView error={error} />\n      </div>\n    );\n  }\n\n  if (!prometheusDatasources.length) {\n    return <Onboarding />;\n  }\n\n  return (\n    <div className={styles.appContainer} data-testid=\"metrics-drilldown-app\">\n      <PluginPropsContext.Provider value={props}>\n        <MetricsContext.Provider value={{ trail, goToUrlForTrail }}>\n          <AppRoutes />\n        </MetricsContext.Provider>\n      </PluginPropsContext.Provider>\n    </div>\n  );\n}\n\nfunction getStyles(theme: GrafanaTheme2) {\n  return {\n    appContainer: css({\n      display: 'flex',\n      flexDirection: 'column',\n      height: '100%',\n      backgroundColor: theme.colors.background.primary,\n    }),\n  };\n}\n", "import { useEffect, useRef } from 'react';\n\nimport { reportExploreMetrics, type ViewName } from 'interactions';\n\nexport function useReportAppInitialized() {\n  const initRef = useRef(false);\n\n  useEffect(() => {\n    if (!initRef.current) {\n      initRef.current = true;\n\n      const url = new URL(window.location.href);\n      const view: ViewName = url.searchParams.get('metric')\n        ? 'metric-details'\n        : 'metrics-reducer';\n      const uel_epid = url.searchParams.get('uel_epid') ?? '';\n\n      reportExploreMetrics('app_initialized', { view, uel_epid });\n    }\n  }, []);\n}\n"], "names": ["Onboarding", "styles", "useStyles2", "getStyles", "theme", "useTheme2", "div", "className", "wrap", "graphicContainer", "SVG", "src", "isDark", "text", "h3", "title", "p", "br", "a", "href", "locationUtil", "assureBaseUrl", "target", "rel", "css", "breakpoints", "up", "alignSelf", "height", "padding", "spacing", "width", "display", "justifyContent", "margin", "alignItems", "flexDirection", "marginBottom", "textAlign", "PluginPropsContext", "createContext", "initFaro", "prometheusDatasources", "Object", "values", "config", "datasources", "filter", "isPrometheusDataSource", "userStorage", "migrate", "error", "logger", "cause", "App", "props", "useCatchExceptions", "trail", "goToUrlForTrail", "useTrail", "initRef", "useRef", "useEffect", "current", "url", "URL", "window", "location", "view", "searchParams", "get", "uel_epid", "reportExploreMetrics", "useReportAppInitialized", "appContainer", "data-testid", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "Provider", "value", "MetricsContext", "AppRoutes", "backgroundColor", "colors", "background", "primary"], "sourceRoot": ""}