{"version": 3, "file": "78.js?_cache=b47d06db0be36dfd8cc3", "mappings": "mNAWe,SAASA,GAAM,MAAEC,IAC9B,MAAOC,EAAeC,IAAoBC,EAAAA,EAAAA,WAAS,GAWnD,OATAC,EAAAA,EAAAA,WAAU,KACHH,IACCD,EAAMK,MAAMC,SACdC,EAAAA,EAAAA,MAAgBC,eAAeR,GAEjCE,GAAiB,KAElB,CAACF,EAAOC,IAENA,EAKH,kBAACQ,EAAAA,GAAsBA,CACrBC,MAAOV,EACPW,2BAA2B,EAC3BC,iBAAiB,EACjBC,UAAWb,EAAMK,MAAMS,cAEvB,kBAACd,EAAMe,UAAS,CAACC,MAAOhB,KAVnB,IAaX,C", "sources": ["webpack://grafana-metricsdrilldown-app/./pages/TrailWingman.tsx"], "sourcesContent": ["import { UrlSyncContextProvider } from '@grafana/scenes';\nimport React, { useEffect, useState } from 'react';\n\nimport { getTrailStore } from 'TrailStore/TrailStore';\n\nimport type { DataTrail } from 'DataTrail';\n\ntype TrailProps = {\n  trail: DataTrail;\n};\n\nexport default function Trail({ trail }: Readonly<TrailProps>) {\n  const [isInitialized, setIsInitialized] = useState(false);\n\n  useEffect(() => {\n    if (!isInitialized) {\n      if (trail.state.metric) {\n        getTrailStore().setRecentTrail(trail);\n      }\n      setIsInitialized(true);\n    }\n  }, [trail, isInitialized]);\n\n  if (!isInitialized) {\n    return null;\n  }\n\n  return (\n    <UrlSyncContextProvider\n      scene={trail}\n      createBrowserHistorySteps={true}\n      updateUrlOnInit={true}\n      namespace={trail.state.urlNamespace}\n    >\n      <trail.Component model={trail} />\n    </UrlSyncContextProvider>\n  );\n}\n"], "names": ["Trail", "trail", "isInitialized", "setIsInitialized", "useState", "useEffect", "state", "metric", "getTrailStore", "setRecentTrail", "UrlSyncContextProvider", "scene", "createBrowserHistorySteps", "updateUrlOnInit", "namespace", "urlNamespace", "Component", "model"], "sourceRoot": ""}