"use strict";(self.webpackChunkgrafana_metricsdrilldown_app=self.webpackChunkgrafana_metricsdrilldown_app||[]).push([[619],{619:(e,t,r)=>{r.r(t),r.d(t,{sortSeries:()=>d,wasmSupported:()=>v});var n=r(6944),o=r(7781),a=r(3241),s=r(3616),l=r(4964),c=r(3347);function i(e){var t;const r=null===(t=e.fields[1])||void 0===t?void 0:t.labels;if(!r)return null;const n=Object.keys(r);return 0===n.length?null:r[n[0]]}const u=(e,t="asc")=>{const r="asc"===t?(e,t)=>(0,l._)(e,t):(e,t)=>(0,l._)(t,e);return e.sort((e,t)=>{const n=i(e);if(!n)return 0;const o=i(t);return o?r(n,o):0})},p=(e,t,r="asc")=>{const n=o.fieldReducers.get(t),a=e.map(e=>{var r;const a=e.fields[1];if(!a)return{value:0,dataFrame:e};var s;var l;return{value:null!==(l=(null!==(s=null===(r=n.reduce)||void 0===r?void 0:r.call(n,a,!0,!0))&&void 0!==s?s:(0,o.doStandardCalcs)(a,!0,!0))[t])&&void 0!==l?l:0,dataFrame:e}});return a.sort("asc"===r?(e,t)=>e.value-t.value:(e,t)=>t.value-e.value),a.map(({dataFrame:e})=>e)},f=e=>{const t=(0,o.outerJoinDataFrames)({frames:e});if(!t)throw new Error("Error while joining frames into a single one");const r=t.fields.filter(e=>e.type===o.FieldType.number).map(e=>new Float64Array(e.values));return n.OutlierDetector.dbscan({sensitivity:.9}).detect(r)},b=(e,t)=>e.seriesResults[t].isOutlier?-e.seriesResults[t].outlierIntervals.length:0,d=(0,a.memoize)((e,t,r="asc")=>{if(!e.length)return[];const n=[...e];if("alphabetical"===t)return u(n,"asc");if("alphabetical-reversed"===t)return u(n,"desc");if("outliers"===t)try{return((e,t="asc")=>{if(!v())throw new Error("WASM not supported");const r=f(e),n=e.map((e,t)=>({value:b(r,t),dataFrame:e}));return n.sort("asc"===t?(e,t)=>e.value-t.value:(e,t)=>t.value-e.value),n.map(({dataFrame:e})=>e)})(n,r)}catch(e){const t=`Error while sorting by outlying series: "${e.toString()}"!`;return(0,s.HA)([t,"Falling back to standard deviation to identify the most variable series."]),p(n,o.ReducerID.stdDev,r)}return p(n,t,r)},(e,t,r="asc")=>{const n=y(e)?e[0].fields[0].values[0]:0,o=y(e)?e[e.length-1].fields[0].values[e[e.length-1].fields[0].values.length-1]:0;return`${e.length>0?i(e[0]):""}_${e.length>0?i(e[e.length-1]):""}_${n}_${o}_${e.length}_${t}_${r}`});function y(e){return e.length>0&&e[0].fields.length>0&&e[0].fields[0].values.length>0}const v=()=>{const e="object"==typeof WebAssembly;return e||(0,c.z)("wasm_not_supported",{}),e}},3347:(e,t,r)=>{r.d(t,{h:()=>p,z:()=>i});var n=r(8531),o=r(4137),a=r(5176);function s(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function l(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}const c="grafana_explore_metrics_";function i(e,t){(0,n.reportInteraction)(`${c}${e}`,l(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){s(e,t,r[t])})}return e}({},t),{meta:{appRelease:n.config.apps[o.s_].version,appVersion:a.t}}))}function u(e,t){i("label_filter_changed",{label:e,action:t,cause:"adhoc_filter"})}function p(e,t){e.length===t.length?function(e,t){for(const r of t)for(const t of e)r.key===t.key&&r.value!==t.value&&u(r.key,"changed")}(e,t):e.length<t.length?function(e,t){for(const r of t)e.some(e=>e.key===r.key)||u(r.key,"removed")}(e,t):function(e,t){for(const r of e)!t.some(e=>e.key===r.key)&&u(r.key,"added")}(e,t)}},3616:(e,t,r)=>{r.d(t,{HA:()=>i,jx:()=>c,qq:()=>u});var n=r(7781),o=r(8531),a=r(2445);function s(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function l(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}function c(e,t){const r=t.reduce((e,t,r)=>l(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){s(e,t,r[t])})}return e}({},e),{[`info${r+1}`]:t}),{handheldBy:"displayError"});a.v.error(e,r),(0,o.getAppEvents)().publish({type:n.AppEvents.alertError.name,payload:t})}function i(e){a.v.warn(e),(0,o.getAppEvents)().publish({type:n.AppEvents.alertWarning.name,payload:e})}function u(e){(0,o.getAppEvents)().publish({type:n.AppEvents.alertSuccess.name,payload:e})}},4964:(e,t,r)=>{r.d(t,{_:()=>n});const n=new Intl.Collator("en",{sensitivity:"base"}).compare}}]);
//# sourceMappingURL=619.js.map?_cache=245ef7bb6c23fdf7b9e5