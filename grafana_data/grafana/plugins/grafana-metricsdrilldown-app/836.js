"use strict";(self.webpackChunkgrafana_metricsdrilldown_app=self.webpackChunkgrafana_metricsdrilldown_app||[]).push([[619,836],{350:(e,t,n)=>{n.d(t,{b:()=>yo});var r,i,a,o=n(6089),s=n(7781),l=n(1932),c=n(8531),u=n(7985),d=n(2007),p=n(5959),m=n.n(p),h=n(416),f=n(2728);class g extends s.BusEventWithPayload{}function b(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}a="configure-panel",(i="type")in(r=g)?Object.defineProperty(r,i,{value:a,enumerable:!0,configurable:!0,writable:!0}):r[i]=a;class y extends u.Bs{constructor({metric:e,disabled:t}){var n;const r=f.x.getItem(h.V.METRIC_PREFS)||{};super({metric:e,disabled:void 0!==t&&t,isAlreadyConfigured:Boolean(null===(n=r[e])||void 0===n?void 0:n.config)}),b(this,"onClick",()=>{this.publishEvent(new g({metric:this.state.metric}),!0)})}}b(y,"Component",({model:e})=>{const t=(0,d.useStyles2)(v),{isAlreadyConfigured:n,disabled:r}=e.useState(),i=n?"Reconfigure Prometheus function":"Configure Prometheus function";return m().createElement(d.Button,{className:(0,o.cx)(t.selectButton,n&&t.active),"aria-label":i,variant:"secondary",size:"sm",fill:"text",onClick:e.onClick,icon:"cog",tooltip:i,tooltipPlacement:"top",disabled:r,"data-testid":"configure-panel"})});const v=e=>({selectButton:o.css`
    margin: 0;
    padding: 0;
  `,active:o.css`
    color: ${e.colors.text.maxContrast};
  `});var w=n(3241);var S=n(3347),O=n(3616);const E={TIMESERIES_AVG:"timeseries-avg",TIMESERIES_SUM:"timeseries-sum",TIMESERIES_STDDEV:"timeseries-stddev",TIMESERIES_PERCENTILES:"timeseries-percentiles",TIMESERIES_MIN_MAX:"timeseries-minmax",TIMESERIES_AGE_TIME_MINUS_AVG:"timeseries-age-time-minus-avg",TIMESERIES_AGE_TIME_MINUS_MIN_MAX:"timeseries-age-time-minus-min-max",HISTOGRAM_HEATMAP:"histogram-heatmap",HISTOGRAM_PERCENTILES:"histogram-percentiles",STATUS_UPDOWN_HISTORY:"status-updown-history",STATUS_UPDOWN_STAT:"status-updown-stat"};var x=n(8162);const k=new Map([...["avg","sum","stddev","quantile","min","max"].map(e=>[e,{name:e,fn:t=>x.GH[e](t)}]),["histogram_quantile",{name:"histogram_quantile",fn:({expr:e,parameter:t})=>`histogram_quantile(${t},${e})`}],["time-avg(metric)",{name:"time-avg(metric)",fn:({expr:e})=>`time()-avg(${e})`}],["time-min(metric)",{name:"time-min(metric)",fn:({expr:e})=>`time()-min(${e})`}],["time-max(metric)",{name:"time-max(metric)",fn:({expr:e})=>`time()-max(${e})`}]]),P=new Set(Object.values(E)),C=new Set(["heatmap","percentiles","stat","statushistory","timeseries"]);function j(e){var t;const n=f.x.getItem(h.V.METRIC_PREFS)||{},r=null===(t=n[e])||void 0===t?void 0:t.config;var i;if(r)return function(e){if(!["id","panelOptions","queryOptions"].every(t=>t in e))return!1;if("string"!=typeof e.id||!P.has(e.id))return!1;if("string"!=typeof e.panelOptions.type||!C.has(e.panelOptions.type))return!1;if(!Array.isArray(e.queryOptions.queries))return!1;if(!e.queryOptions.queries.every(e=>{var t;return!!k.has(e.fn)&&(!["quantile","histogram_quantile"].includes(e.fn)||!(!Array.isArray(null===(t=e.params)||void 0===t?void 0:t.percentiles)||!e.params.percentiles.length)&&e.params.percentiles.every(e=>e>=1&&e<=99))}))return!1;return!0}(r)?r:((0,S.z)("invalid_metric_config",{metricConfig:r}),null===(i=n[e])||void 0===i||delete i.config,f.x.setItem(h.V.METRIC_PREFS,n),void(0,O.HA)([`Invalid configuration found for metric ${e}!`,"The configuration has been deleted and will not be applied."]))}var _=function(e){return e[e.S=160]="S",e[e.M=220]="M",e[e.L=260]="L",e[e.XL=280]="XL",e}({});const T={[E.TIMESERIES_AGE_TIME_MINUS_AVG]:{id:E.TIMESERIES_AGE_TIME_MINUS_AVG,name:"Average age",panelOptions:{type:"timeseries",description:'Suitable only for metrics that store unix timestamps (usually containing "timestamp_seconds" in their name) to calculate an average age. Calculates the age by subtracting the average timestamp value from current time.'},queryOptions:{queries:[{fn:"time-avg(metric)"}]}},[E.TIMESERIES_AGE_TIME_MINUS_MIN_MAX]:{id:E.TIMESERIES_AGE_TIME_MINUS_MIN_MAX,name:"Minimum and maximum ages",panelOptions:{type:"timeseries",description:'Suitable only for metrics that store unix timestamps (usually containing "timestamp_seconds" in their name) to calculate a minimum and a maximum age. Calculates the ages by subtracting the min and the max timestamp values from current time.'},queryOptions:{queries:[{fn:"time-min(metric)"},{fn:"time-max(metric)"}]}}},I={[E.HISTOGRAM_HEATMAP]:{id:E.HISTOGRAM_HEATMAP,name:"Heatmap (default)",panelOptions:{type:"heatmap",description:"Visualizes the full distribution of histogram data over time using color intensity. Perfect for spotting patterns, identifying performance degradation, and understanding latency distribution changes. Shows density of values across different buckets."},queryOptions:{queries:[]}},[E.HISTOGRAM_PERCENTILES]:{id:E.HISTOGRAM_PERCENTILES,name:"Percentiles",panelOptions:{type:"percentiles",description:"Extracts specific percentile values from histogram data. Essential for SLA monitoring and performance analysis, showing how response times or other metrics behave for different user experience tiers."},queryOptions:{queries:[{fn:"histogram_quantile",params:{percentiles:[99,90,50]}}]}}},A={[E.STATUS_UPDOWN_HISTORY]:{id:E.STATUS_UPDOWN_HISTORY,name:"Status History (default)",panelOptions:{type:"statushistory",description:"Displays binary status changes over time as colored bars (green=up, red=down). Perfect for monitoring service availability, health checks, or any binary state metrics. Shows patterns in uptime/downtime and helps identify recurring issues."},queryOptions:{queries:[{fn:"min"}]}},[E.STATUS_UPDOWN_STAT]:{id:E.STATUS_UPDOWN_STAT,name:"Stat with latest value",panelOptions:{type:"stat",description:'Shows the current status as a single value display with color coding (green=up, red=down). Ideal for dashboards where you need an at-a-glance view of service health or binary state. Uses minimum value to ensure any "down" status is highlighted.'},queryOptions:{queries:[{fn:"min"}]}}};function D(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function L(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){D(e,t,n[t])})}return e}function N(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const M={[E.TIMESERIES_AVG]:{id:E.TIMESERIES_AVG,name:"Average (default)",panelOptions:{type:"timeseries",description:"Shows the average value across all time series. Ideal for understanding typical behavior and smoothing out variations between different targets. For rate queries, displays average throughput per target."},queryOptions:{queries:[{fn:"avg"}]}},[E.TIMESERIES_SUM]:{id:E.TIMESERIES_SUM,name:"Sum",panelOptions:{type:"timeseries",description:"Aggregates total values across all time series. Perfect for measuring overall system throughput, total resource consumption, or fleet-wide capacity. Essential for rate queries showing total request rates."},queryOptions:{queries:[{fn:"sum"}]}},[E.TIMESERIES_STDDEV]:{id:E.TIMESERIES_STDDEV,name:"Standard deviation",panelOptions:{type:"timeseries",description:"Measures variability and consistency across time series. High values indicate uneven load distribution or inconsistent behavior. Useful for detecting load balancing issues or identifying when some targets behave differently."},queryOptions:{queries:[{fn:"stddev"}]}},[E.TIMESERIES_PERCENTILES]:{id:E.TIMESERIES_PERCENTILES,name:"Percentiles",panelOptions:{type:"percentiles",description:"Displays percentiles to show value distribution. Excellent for SLA monitoring and understanding outlier behavior without being skewed by extreme values. Critical for performance analysis."},queryOptions:{queries:[{fn:"quantile",params:{percentiles:[99,90,50]}}]}},[E.TIMESERIES_MIN_MAX]:{id:E.TIMESERIES_MIN_MAX,name:"Minimum and maximum",panelOptions:{type:"timeseries",description:"Shows the range between lowest and highest values across time series. Useful for capacity planning, identifying idle resources (min), and spotting overloaded targets (max). Helps detect outliers and resource utilization patterns."},queryOptions:{queries:[{fn:"min"},{fn:"max"}]}}},B={[E.TIMESERIES_SUM]:N(L({},M[E.TIMESERIES_SUM]),{name:"Sum (default)",id:E.TIMESERIES_SUM}),[E.TIMESERIES_AVG]:N(L({},M[E.TIMESERIES_AVG]),{name:"Average"}),[E.TIMESERIES_STDDEV]:M[E.TIMESERIES_STDDEV],[E.TIMESERIES_PERCENTILES]:M[E.TIMESERIES_PERCENTILES],[E.TIMESERIES_MIN_MAX]:M[E.TIMESERIES_MIN_MAX]},R=new Set(["count","total","sum"]);function $(e){const t=e.split("_").at(-1);return!!t&&R.has(t)}const F=e=>e.endsWith("_bucket"),V=e=>e.endsWith("_timestamp_seconds"),q=e=>"up"===e||e.endsWith("_up");var H=n(4796);class z extends s.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(z,"type","panel-type-changed");var U=n(2127);function G(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class K extends u.Bs{constructor({metric:e,variant:t,fill:n}){super({key:`select-action-${e}`,metric:e,variant:t||"primary",fill:n||"outline"}),G(this,"onClick",()=>{this.publishEvent(new U.OO(this.state.metric),!0)})}}G(K,"Component",({model:e})=>{const{variant:t,fill:n}=e.useState();return m().createElement(d.Button,{variant:t,fill:n,size:"sm",onClick:e.onClick,"data-testid":`select-action-${e.state.metric}`},"Select")});var W=function(e){return e.HIGH="HIGH",e.MEDIUM="MEDIUM",e}({}),Q=n(5938);function Y(e){return e.toString().replaceAll('="__REMOVE__"',"")}function X(e){const{metric:t,labelMatchers:n=[],addIgnoreUsageFilter:r=!0}=e,i=n.map(e=>({label:(0,l.Nc)(e.key),operator:e.operator,value:e.value}));r&&i.push({label:"__ignore_usage__",operator:x.md.equal,value:""});return!(0,l.Rq)(t)&&i.push({label:(0,l.Nc)(t),operator:x.md.equal,value:"__REMOVE__"}),i.push({label:U.ui,operator:x.md.equal,value:"__REMOVE__"}),new x.r4({metric:t,values:{},defaultOperator:x.md.equal,defaultSelectors:i})}const J="none",Z="bytes",ee="seconds",te="percent",ne="count",re={[Z]:Z,[ee]:"s",[te]:te,[ne]:J},ie=Object.keys(re),ae={[Z]:"Bps",[ee]:J,[ne]:"cps",[te]:te};function oe(e){const t=e.toLowerCase().split("_").slice(-2);for(let e=t.length-1;e>=Math.max(0,t.length-2);e--){const n=t[e];if(ie.includes(n))return n}return null}function se(e){const t=oe(e);return t&&re[t.toLowerCase()]||J}function le(e){const t=oe(e);return t&&ae[t]||"cps"}function ce(e){var t;const{metric:n,histogramType:r,panelConfig:i,queryConfig:a}=e,o=function(e){const{metric:t,isNativeHistogram:n,queryConfig:r}=e,i=X({metric:t,labelMatchers:r.labelMatchers,addIgnoreUsageFilter:r.addIgnoreUsageFilter}),a=x.GH.rate({expr:Y(i)}),o=n?x.GH.sum({expr:a}):x.GH.sum({expr:a,by:["le"]});return{maxDataPoints:r.resolution===W.HIGH?500:250,queries:[{refId:`${t}-heatmap`,expr:o,format:"heatmap",fromExploreMetrics:!0}]}}({metric:n,isNativeHistogram:"native"===r,queryConfig:a}),s=se(n),l=new u.dt({datasource:U.GH,maxDataPoints:o.maxDataPoints,queries:o.queries});return u.d0.heatmap().setTitle(i.title).setDescription(i.description).setHeaderActions(i.headerActions({metric:n,panelConfig:i})).setMenu(null===(t=i.menu)||void 0===t?void 0:t.clone()).setShowMenuAlways(Boolean(i.menu)).setData(l).setUnit(s).setOption("calculate",!1).setOption("color",{mode:Q.P7.Scheme,exponent:.5,scheme:"Spectral",steps:32,reverse:!1}).setOption("legend",i.legend).build()}var ue=n(1625);const de=[99,90,50];function pe(e){const{metric:t,histogramType:n,queryConfig:r}=e,i=$(t),a=Y(X({metric:t,labelMatchers:r.labelMatchers,addIgnoreUsageFilter:r.addIgnoreUsageFilter})),o="none"===n?function({metric:e,queryConfig:t,isRateQuery:n,expr:r}){var i;const a=(null===(i=t.queries)||void 0===i?void 0:i.length)?t.queries:[{fn:"quantile",params:{percentiles:[99,90,50]}}],o=[],s=n?x.GH.rate({expr:r}):r;for(const{fn:t,params:r}of a){const i=k.get(t),a=n?`${i.name}(rate)`:i.name;for(const t of r.percentiles){const n=t/100,r=i.fn({expr:s,parameter:n});o.push({refId:`${e}-p${t}-${a}`,expr:r,legendFormat:`${t}th Percentile`,fromExploreMetrics:!0})}}return o}({metric:t,queryConfig:r,isRateQuery:i,expr:a}):function({metric:e,isNativeHistogram:t,queryConfig:n,expr:r}){var i;const a=(null===(i=n.queries)||void 0===i?void 0:i.length)?n.queries:[{fn:"histogram_quantile",params:{percentiles:de}}],o=[],s=t?x.GH.sum({expr:x.GH.rate({expr:r})}):x.GH.sum({expr:x.GH.rate({expr:r}),by:["le"]});for(const{fn:t,params:n}of a){const r=k.get(t),i=r.name,a=(null==n?void 0:n.percentiles)||de;for(const t of a){const n=t/100,a=r.fn({expr:s,parameter:n});o.push({refId:`${e}-p${t}-${i}`,expr:a,legendFormat:`${t}th Percentile`,fromExploreMetrics:!0})}}return o}({metric:t,isNativeHistogram:"native"===n,queryConfig:r,expr:a});return{isRateQuery:"none"!==n||i,maxDataPoints:r.resolution===W.HIGH?500:250,queries:o}}function me(e){var t;const{metric:n,histogramType:r,panelConfig:i,queryConfig:a}=e,o=pe({metric:n,histogramType:r,queryConfig:a}),s=o.isRateQuery?le(n):se(n),l=new u.dt({datasource:U.GH,maxDataPoints:o.maxDataPoints,queries:o.queries}),c=i.fixedColorIndex||0;return u.d0.timeseries().setTitle(i.title).setDescription(i.description).setHeaderActions(i.headerActions({metric:n,panelConfig:i})).setMenu(null===(t=i.menu)||void 0===t?void 0:t.clone()).setShowMenuAlways(Boolean(i.menu)).setData(l).setUnit(s).setOption("tooltip",{mode:ue.$N.Multi,sort:ue.xB.Descending}).setCustomFieldConfig("fillOpacity",9).setOverrides(e=>{o.queries.forEach((t,n)=>{e.matchFieldsByQuery(t.refId).overrideColor({mode:"fixed",fixedColor:(0,H.Vy)(c+n)})})}).build()}function he({metric:e,queryConfig:t,isRateQuery:n,expr:r}){var i;const a=n?"sum":"avg",o=(null===(i=t.queries)||void 0===i?void 0:i.length)?t.queries:[{fn:a}],s=[];for(const{fn:t}of o){const i=k.get(t),a=i.fn({expr:r}),o=n?`${i.name}(rate)`:i.name;s.push({refId:`${e}-${o}`,expr:a,legendFormat:o,fromExploreMetrics:!0})}return s}var fe=n(6145);const ge=[{type:fe.dM.ValueToText,options:{0:{color:"red",text:"down"},1:{color:"green",text:"up"}}}];function be(e){var t;const{metric:n,panelConfig:r,queryConfig:i}=e,a=function(e){const{metric:t,queryConfig:n}=e,r=$(t);let i=Y(X({metric:t,labelMatchers:n.labelMatchers,addIgnoreUsageFilter:n.addIgnoreUsageFilter}));return r&&(i=x.GH.rate({expr:i,interval:"$__rate_interval"})),{isRateQuery:r,maxDataPoints:n.resolution===W.HIGH?500:250,queries:he({metric:t,queryConfig:n,isRateQuery:r,expr:i})}}({metric:n,queryConfig:i}),o=new u.dt({datasource:U.GH,maxDataPoints:a.maxDataPoints,queries:a.queries});return u.d0.stat().setTitle(r.title).setDescription(r.description).setHeaderActions(r.headerActions({metric:n,panelConfig:r})).setMenu(null===(t=r.menu)||void 0===t?void 0:t.clone()).setShowMenuAlways(Boolean(r.menu)).setData(o).setUnit("none").setColor({mode:"fixed",fixedColor:(0,H.Vy)(r.fixedColorIndex||0)}).setMappings(ge).build()}function ye(e){var t;const{metric:n,panelConfig:r,queryConfig:i}=e,a=function(e){const{metric:t,queryConfig:n}=e,r=X({metric:t,labelMatchers:n.labelMatchers,addIgnoreUsageFilter:n.addIgnoreUsageFilter}),i=x.GH.min({expr:Y(r)});return{maxDataPoints:n.resolution===W.HIGH?200:100,queries:[{refId:`${t}-status`,expr:i,legendFormat:"status",fromExploreMetrics:!0}]}}({metric:n,queryConfig:i}),o=new u.dt({datasource:U.GH,maxDataPoints:a.maxDataPoints,queries:a.queries});return u.d0.statushistory().setTitle(r.title).setDescription(r.description).setHeaderActions(r.headerActions({metric:n,panelConfig:r})).setMenu(null===(t=r.menu)||void 0===t?void 0:t.clone()).setShowMenuAlways(Boolean(r.menu)).setData(o).setUnit("none").setColor({mode:"palette-classic"}).setOption("showValue",ue.yL.Never).setOption("legend",r.legend).setOption("perPage",0).setMappings(ge).build()}var ve=n(1269);function we(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Se(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const Oe=e=>()=>t=>t.pipe((0,ve.map)(t=>null==t?void 0:t.map(t=>{var n;return(null==t?void 0:t.fields[1])?((null===(n=t.fields[1].labels)||void 0===n?void 0:n[e])||(t.fields[1].labels=Se(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){we(e,t,n[t])})}return e}({},t.fields[1].labels),{[e]:`<unspecified ${e}>`})),t):t})));var Ee=n(9851);function xe(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const ke=new Set(["avg","min","max"]);function Pe({metric:e,filters:t,isRateQuery:n,groupings:r,ignoreUsage:i=!1,nonRateQueryFunction:a="avg",filterExtremeValues:o=!1}){const s=!(0,l.Rq)(e),c=new x.r4({metric:s?"":e,values:{},defaultOperator:x.md.equal,defaultSelectors:[...s?[{label:(0,l.Nc)(e),operator:x.md.equal,value:"__REMOVE__"}]:[],...i?[{label:"__ignore_usage__",operator:x.md.equal,value:""}]:[],...t.filter(e=>"__name__"!==e.key).map(({key:e,value:t,operator:n})=>({label:(0,l.Nc)(e),operator:n,value:t}))]});let u=c.toString();if(s&&(u=u.replace('="__REMOVE__"',"")),o){u=x.GH.and({left:u,right:`${u} > -Inf`})}return n&&(u=x.GH.rate({expr:u,interval:"$__rate_interval"})),x.GH[function(e,t="avg"){return e?"sum":t}(n,a)](function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){xe(e,t,n[t])})}return e}({expr:u},(null==r?void 0:r.length)?{by:r}:{}))}var Ce=n(2445),je=n(3510);function _e(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Te(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){_e(e,t,n[t])})}return e}function Ie(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function Ae(e){const[t]=u.jh.findDescendents(e,u.dt),n=null==t?void 0:t.subscribeToState(r=>{var i,a;if((null===(i=r.data)||void 0===i?void 0:i.state)===s.LoadingState.Done&&(null===(a=r.data)||void 0===a?void 0:a.series)){const{series:i}=r.data;if(0===i.length||!function(e){return e.every(e=>function(e){const t=e.fields.find(e=>"Value"===e.name);if(!t||(n=t,!("entities"in n)||!Array.isArray(null===(r=n.entities)||void 0===r?void 0:r.NaN)))return!1;var n,r;return t.entities.NaN.length===e.length}(e))}(i))return;(0,S.z)("extreme_value_filter_behavior_triggered",{expression:u.jh.interpolate(t,t.state.queries[0].expr)});const a=function(e,t,n){const r=e.state.queries;if(!r||0===r.length)return{success:!1,issue:"No queries found in query runner"};const i=function(e,t){let n=e;(e.includes(U.Rp)||e.includes(U.ui))&&(n=u.jh.interpolate(t,e));try{const e=Ee.K3.parse(n);let t="";const r=[];let i,a,o=!1;return e.iterate({enter:e=>{var s;if("FunctionCall"===e.name){let t="";for(let r=e.node.firstChild;r;r=r.nextSibling)if("Identifier"===r.type.name){t=n.slice(r.from,r.to);break}"rate"===t?o=!0:(e=>ke.has(e))(t)&&(a=t,o=!1)}t||"Identifier"!==e.name||"VectorSelector"!==(null===(s=e.node.parent)||void 0===s?void 0:s.type.name)||(t=n.slice(e.from,e.to));const l=(0,je.W)(e,n);l&&r.push({key:l.label,operator:l.op,value:l.value}),"GroupingLabels"===e.name&&a&&(i=function(e,t){const n=[];for(let r=e.node.firstChild;r;r=r.nextSibling)if("LabelName"===r.type.name){const e=t.slice(r.from,r.to);e&&n.push(e)}return n}(e,n))}}),t?{success:!0,queryParts:{metric:t,filters:r,isRateQuery:o,groupings:i,nonRateQueryFunction:a}}:{success:!1,issue:`Could not parse the metric name from the query: ${n}`}}catch(e){return{success:!1,issue:`Unexpected error during query parsing to handle extreme values: ${e instanceof Error?e.message:String(e)}`}}}(r[0].expr,e);if(!i.success)return{success:!1,issue:i.issue};const a=Pe(Ie(Te({},i.queryParts),{filterExtremeValues:!0}));n&&n.unsubscribe();const o=e.clone({queries:[Ie(Te({},r[0]),{expr:a})]});return t.setState({$data:o,titleItems:m().createElement(De,{level:"info",message:"Panel data was re-fetched with a more complex query to handle extremely small values in the series"})}),o.runQueries(),{success:!0}}(t,e,n);a.success||(e.setState({titleItems:m().createElement(De,{level:"warning",message:"Extreme values detected, but could not re-run the query with extreme value filtering"})}),Ce.v.warn("ExtremeValueFilterBehavior: Failed to remove extreme values:",a.issue))}});return()=>{n&&n.unsubscribe()}}function De({message:e,level:t}){const n=(0,d.useStyles2)(Le,t);return m().createElement("div",{className:n.extremeValuedisclaimer},m().createElement(d.Tooltip,{content:e},m().createElement("span",{className:n.warningMessage},m().createElement(d.Icon,{name:"warning"===t?"exclamation-triangle":"info-circle","aria-hidden":"true"}))))}const Le=(e,t)=>({extremeValuedisclaimer:(0,o.css)({label:"extreme-value-disclaimer",display:"flex",alignItems:"center",gap:e.spacing(1)}),warningMessage:(0,o.css)({display:"flex",alignItems:"center",gap:e.spacing(.5),color:"warning"===t?e.colors.warning.main:e.colors.info.main,fontSize:e.typography.bodySmall.fontSize})});function Ne(e){const{metric:t,queryConfig:n}=e,r=$(t);let i=Y(X({metric:t,labelMatchers:n.labelMatchers,addIgnoreUsageFilter:n.addIgnoreUsageFilter}));return r&&(i=x.GH.rate({expr:i,interval:"$__rate_interval"})),{isRateQuery:r,maxDataPoints:n.resolution===W.HIGH?500:250,queries:n.groupBy?Me({metric:t,queryConfig:n,isRateQuery:r,expr:i}):Be({metric:t,queryConfig:n,isRateQuery:r,expr:i})}}function Me({metric:e,queryConfig:t,isRateQuery:n,expr:r}){return[{refId:`${e}-by-${t.groupBy}`,expr:n?x.GH.sum({expr:r,by:[t.groupBy]}):x.GH.avg({expr:r,by:[t.groupBy]}),legendFormat:`{{${t.groupBy}}}`,fromExploreMetrics:!0}]}function Be({metric:e,queryConfig:t,isRateQuery:n,expr:r}){var i;const a=n?"sum":"avg",o=(null===(i=t.queries)||void 0===i?void 0:i.length)?t.queries:[{fn:a}],s=[];for(const{fn:t}of o){const i=k.get(t),a=i.fn({expr:r}),o=n?`${i.name}(rate)`:i.name;s.push({refId:`${e}-${o}`,expr:a,legendFormat:o,fromExploreMetrics:!0})}return s}function Re(e){var t,n;if(null===(t=e.queryConfig)||void 0===t?void 0:t.groupBy)return function(e){var t;const{metric:n,panelConfig:r,queryConfig:i}=e,a=Ne({metric:n,queryConfig:i}),o=a.isRateQuery?le(n):se(n),s=new u.Es({$data:new u.dt({datasource:U.GH,maxDataPoints:a.maxDataPoints,queries:a.queries}),transformations:[Oe(i.groupBy)]});return u.d0.timeseries().setTitle(r.title).setDescription(r.description).setHeaderActions(r.headerActions({metric:n,panelConfig:r})).setMenu(null===(t=r.menu)||void 0===t?void 0:t.clone()).setShowMenuAlways(Boolean(r.menu)).setData(s).setUnit(o).setOption("legend",r.legend||{showLegend:!0,placement:"right"}).setOption("tooltip",{mode:ue.$N.Multi,sort:ue.xB.Descending}).build()}(e);const{metric:r,panelConfig:i,queryConfig:a}=e,o=Ne({metric:r,queryConfig:a}),s=o.isRateQuery?le(r):se(r),l=new u.dt({datasource:U.GH,maxDataPoints:o.maxDataPoints,queries:o.queries}),c=u.d0.timeseries().setTitle(i.title).setDescription(i.description).setHeaderActions(i.headerActions({metric:r,panelConfig:i})).setMenu(null===(n=i.menu)||void 0===n?void 0:n.clone()).setShowMenuAlways(Boolean(i.menu)).setData(l).setUnit(s).setOption("legend",{showLegend:!0,placement:"bottom"}).setCustomFieldConfig("fillOpacity",9).setBehaviors([Ae]);if(o.queries.length>1){const e=i.fixedColorIndex||0;c.setOverrides(t=>{o.queries.forEach((n,r)=>{t.matchFieldsByQuery(n.refId).overrideColor({mode:"fixed",fixedColor:(0,H.Vy)(e+r)})})})}else c.setColor(i.fixedColorIndex?{mode:"fixed",fixedColor:(0,H.Vy)(i.fixedColorIndex)}:void 0);return c.build()}function $e(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function Fe(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ve(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Fe(e,t,n[t])})}return e}function qe(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class He extends u.Bs{onActivate(e){return(t=function*(){const{metric:t,panelConfig:n}=this.state;(yield(0,H.kj)(this).isNativeHistogram(t))&&this.setState({histogramType:"native",panelConfig:e?n:qe(Ve({description:"Native Histogram "},n),{type:"heatmap"})}),this.updateBody(),this.subscribeToStateChanges(),this.subscribeToEvents()},function(){var e=this,n=arguments;return new Promise(function(r,i){var a=t.apply(e,n);function o(e){$e(a,r,i,o,s,"next",e)}function s(e){$e(a,r,i,o,s,"throw",e)}o(void 0)})}).call(this);var t}static getDefaultPanelTypeForMetric(e,t){return q(e)?"statushistory":"classic"===t||"native"===t?"heatmap":"timeseries"}subscribeToStateChanges(){this.subscribeToState((e,t)=>{e.histogramType===t.histogramType&&(0,w.isEqual)(e.panelConfig,t.panelConfig)&&(0,w.isEqual)(e.queryConfig,t.queryConfig)||this.updateBody()})}subscribeToEvents(){this.subscribeToEvent(z,e=>{this.setState({panelConfig:qe(Ve({},this.state.panelConfig),{type:e.payload.panelType})})})}updateBody(){const{metric:e,panelConfig:t,queryConfig:n,histogramType:r}=this.state;switch(t.type){case"timeseries":return void this.setState({body:Re({metric:e,panelConfig:t,queryConfig:n})});case"heatmap":return void this.setState({body:ce({metric:e,histogramType:r,panelConfig:t,queryConfig:n})});case"percentiles":return void this.setState({body:me({metric:e,histogramType:r,panelConfig:t,queryConfig:n})});case"statushistory":return void this.setState({body:ye({metric:e,panelConfig:t,queryConfig:n})});case"stat":return void this.setState({body:be({metric:e,panelConfig:t,queryConfig:n})});default:throw new TypeError(`Unsupported panel type "${t.type}"!`)}}update(e,t){const{panelConfig:n,queryConfig:r}=this.state;this.setState({panelConfig:Ve({},n,e),queryConfig:Ve({},r,t)})}constructor({key:e,metric:t,panelOptions:n,queryOptions:r,discardUserPrefs:i}){const a=F(t)?"classic":"none",o=i?void 0:j(t);super({key:e,metric:t,histogramType:a,panelConfig:Ve({type:(null==n?void 0:n.type)||He.getDefaultPanelTypeForMetric(t,a),title:t,height:_.M,headerActions:({metric:e})=>[new K({metric:e})]},n,null==o?void 0:o.panelOptions),queryConfig:Ve({resolution:W.MEDIUM,labelMatchers:[],addIgnoreUsageFilter:!0},r,null==o?void 0:o.queryOptions),body:void 0}),this.addActivationHandler(()=>{this.onActivate(Boolean((null==n?void 0:n.type)||(null==o?void 0:o.panelOptions.type)))})}}function ze(e,t){return{container:o.css`
      width: 100%;
      height: ${t}px;
    `}}Fe(He,"Component",({model:e})=>{const{body:t,panelConfig:n}=e.useState(),r=(0,d.useStyles2)(ze,n.height);return m().createElement("div",{className:r.container,"data-testid":"gmd-vizpanel"},t&&m().createElement(t.Component,{model:t}))});var Ue=n(6503);function Ge(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}var Ke=function(e){return e.GRID="grid",e.ROWS="rows",e.SINGLE="single",e}({});class We extends u.Bs{getUrlState(){return{[this.state.urlSearchParamName]:this.state.layout}}updateFromUrl(e){const t={},n=e[this.state.urlSearchParamName];n!==this.state.layout&&(t.layout=this.state.options.find(e=>e.value===n)?n:We.DEFAULT_LAYOUT),this.setState(t)}constructor({urlSearchParamName:e,options:t}){super({key:"layout-switcher",urlSearchParamName:e||"layout",options:t||We.DEFAULT_OPTIONS,layout:We.DEFAULT_LAYOUT}),Ge(this,"_urlSync",new u.So(this,{keys:[this.state.urlSearchParamName]})),Ge(this,"onChange",e=>{this.setState({layout:e})})}}function Qe(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}Ge(We,"DEFAULT_OPTIONS",[{label:"Grid",value:"grid"},{label:"Rows",value:"rows"}]),Ge(We,"DEFAULT_LAYOUT","grid"),Ge(We,"Component",({model:e})=>{const{options:t,layout:n}=e.useState();return m().createElement(d.RadioButtonGroup,{"aria-label":"Layout switcher",options:t,value:n,onChange:e.onChange,fullWidth:!1})});class Ye extends u.Bs{performRepeat(){var e,t;if(this._variableDependency.hasDependencyInLoadingState())return void this.setState({loadingLayout:null===(e=(t=this.state).getLayoutLoading)||void 0===e?void 0:e.call(t),errorLayout:void 0,emptyLayout:void 0,currentBatchSize:0});const n=u.jh.lookupVariable(this.state.variableName,this);if(!(n instanceof u.n8)){const e=new Error("SceneByVariableRepeater: variable is not a MultiValueVariable!");return void Ce.v.error(e)}var r,i;if(n.state.error)return void this.setState({errorLayout:null===(r=(i=this.state).getLayoutError)||void 0===r?void 0:r.call(i,n.state.error),loadingLayout:void 0,emptyLayout:void 0,currentBatchSize:0});const a=Xe(n);var o,s;if(!a.length)return void this.setState({emptyLayout:null===(o=(s=this.state).getLayoutEmpty)||void 0===o?void 0:o.call(s),errorLayout:void 0,loadingLayout:void 0,currentBatchSize:0});this.setState({loadingLayout:void 0,errorLayout:void 0,emptyLayout:void 0,currentBatchSize:this.state.initialPageSize});const l=a.slice(0,this.state.initialPageSize).map((e,t)=>this.state.getLayoutChild(e,t,a)).filter(Boolean);this.state.body.setState({children:l})}increaseBatchSize(){const e=Xe(u.jh.lookupVariable(this.state.variableName,this)),t=this.state.currentBatchSize+this.state.pageSizeIncrement,n=e.slice(this.state.currentBatchSize,t).map((t,n)=>this.state.getLayoutChild(t,this.state.currentBatchSize+n,e)).filter(Boolean);this.state.body.setState({children:[...this.state.body.state.children,...n]}),this.setState({currentBatchSize:t})}useSizes(){const{currentBatchSize:e,pageSizeIncrement:t}=this.useState(),n=u.jh.lookupVariable(this.state.variableName,this).state.options.length,r=n-e;return{increment:r<t?r:t,current:e,total:n}}constructor({variableName:e,body:t,getLayoutChild:n,getLayoutLoading:r,getLayoutError:i,getLayoutEmpty:a,initialPageSize:o,pageSizeIncrement:s}){super({variableName:e,body:t,getLayoutChild:n,getLayoutLoading:r,getLayoutError:i,getLayoutEmpty:a,currentBatchSize:0,initialPageSize:o||6,pageSizeIncrement:s||9,loadingLayout:void 0,errorLayout:void 0,emptyLayout:void 0}),Qe(this,"_variableDependency",new u.Sh(this,{variableNames:[this.state.variableName],onVariableUpdateCompleted:()=>this.performRepeat()})),this.addActivationHandler(()=>this.performRepeat())}}function Xe(e){const{value:t,text:n,options:r}=e.state;return e.hasAllValue()?r:Array.isArray(t)&&Array.isArray(n)?t.map((e,t)=>({value:e,label:n[t]})):[{value:t,label:n}]}function Je({label:e,batchSizes:t,onClick:n,tooltip:r}){return m().createElement(d.Button,{variant:"secondary",fill:"outline",onClick:n,tooltip:r,tooltipPlacement:"top"},"Show ",t.increment," more ",1===t.increment?e:`${e}s`," (",t.current,"/",t.total,")")}Qe(Ye,"Component",({model:e})=>{const{body:t,loadingLayout:n,errorLayout:r,emptyLayout:i}=e.useState();return n?m().createElement(n.Component,{model:n}):r?m().createElement(r.Component,{model:r}):i?m().createElement(i.Component,{model:i}):m().createElement(t.Component,{model:t})});var Ze=n(384),et=n(4964);class tt extends s.BusEventWithPayload{}function nt(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(tt,"type","sort-by-changed");const rt={showSuccessAlert:!1,showErrorAlert:!1};function it(){return(e=function*(){try{return function(e){const t={};for(const n in e)t[n]={usageType:"alerting-usage",count:e[n]};return t}(function(e){const t={},n=e.filter(e=>(null==e?void 0:e.data.length)>0);for(const e of n){const n=e.data.filter(e=>{var t;return"string"==typeof(null===(t=e.model)||void 0===t?void 0:t.expr)&&"__expr__"!==e.datasourceUid});for(const r of n)try{const e=(0,je.M)(r.model.expr);for(const n of e)t[n]=(t[n]||0)+1}catch(t){Ce.v.warn(t,{message:`Failed to parse PromQL expression in alert rule ${e.title}`})}}return t}(yield(0,c.getBackendSrv)().get("/api/v1/provisioning/alert-rules",void 0,"grafana-metricsdrilldown-app-alert-rule-metric-usage",rt)))}catch(e){const t="string"==typeof e?new Error(e):e;return Ce.v.error(t,{message:"Failed to fetch alerting rules"}),{}}},function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){nt(a,r,i,o,s,"next",e)}function s(e){nt(a,r,i,o,s,"throw",e)}o(void 0)})})();var e}var at=n(7348),ot=n(7476);function st(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function lt(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){st(a,r,i,o,s,"next",e)}function s(e){st(a,r,i,o,s,"throw",e)}o(void 0)})}}function ct(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ut(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const dt={showSuccessAlert:!1,showErrorAlert:!1},pt=new Map,mt=(0,at.g)((e,t,n)=>lt(function*(){let r=pt.get(e);return r||(r=(0,c.getBackendSrv)().get(`/api/dashboards/uid/${e}`,void 0,`grafana-metricsdrilldown-app-dashboard-metric-usage-${e}`,dt).then(({dashboard:e})=>ut(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){ct(e,t,n[t])})}return e}({},e),{url:t})).catch(t=>(n<=5&&Ce.v.error(t,{dashboardUid:e}),n++,Promise.resolve(null))).finally(()=>{pt.delete(e)}),pt.set(e,r)),r})(),{concurrency:50});function ht(){return lt(function*(){try{const e=yield(0,c.getBackendSrv)().get("/api/search",{type:"dash-db",limit:500},"grafana-metricsdrilldown-app-dashboard-search",dt);let t=0;return yield Promise.all(e.map(({uid:e,url:n})=>mt(e,n,t))).then(gt)}catch(e){const t="string"==typeof e?new Error(e):e;return Ce.v.error(t,{message:"Failed to fetch dashboard metrics"}),{}}})()}function ft(e,t,n,r,i){var a;(i[e]||(i[e]={usageType:"dashboard-usage",count:0,dashboards:{}}),i[e].count++,"dashboard-usage"===i[e].usageType)&&(i[e].dashboards[t]={count:((null===(a=i[e].dashboards[t])||void 0===a?void 0:a.count)||0)+1,uid:n||"unknown",url:r})}function gt(e){const t={},n=e.filter(e=>{var t;return e&&(null==e||null===(t=e.panels)||void 0===t?void 0:t.length)});for(const e of n){const n=e.title||`Dashboard ${e.uid}`,r=e.panels.filter(e=>{var t;return(0,ot.aQ)(e.datasource)&&"targets"in e&&(null===(t=e.targets)||void 0===t?void 0:t.length)});for(const i of r)for(const r of i.targets){const i="string"==typeof r.expr?r.expr:"",a=(0,je.M)(i);for(const r of a)ft(r,n,e.uid||"unknown",e.url,t)}}return t}class bt{getUsageMetrics(e){return this._usageState[e].metrics&&Object.keys(this._usageState[e].metrics).length>0?Promise.resolve(this._usageState[e].metrics):(this._usageState[e].metricsPromise||(this._usageState[e].metricsPromise=this._usageState[e].fetcher().then(t=>(this._usageState[e].metrics=t,this._usageState[e].metricsPromise=void 0,t))),this._usageState[e].metricsPromise)}getUsageForMetric(e,t){return this.getUsageMetrics(t).then(t=>{var n,r;return null!==(r=null===(n=t[e])||void 0===n?void 0:n.count)&&void 0!==r?r:0})}getUsageDetailsForMetric(e,t){return this.getUsageMetrics(t).then(n=>{var r;return null!==(r=n[e])&&void 0!==r?r:"dashboard-usage"===t?{usageType:"dashboard-usage",count:0,dashboards:{}}:{usageType:"alerting-usage",count:0}})}constructor(){!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(this,"_usageState",{"dashboard-usage":{metrics:{},metricsPromise:void 0,fetcher:ht},"alerting-usage":{metrics:{},metricsPromise:void 0,fetcher:it}})}}function yt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function vt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){yt(e,t,n[t])})}return e}function wt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function St(){try{const e=f.x.getItem(h.V.RECENT_METRICS)||[];if(!e.length)return[];const t=Date.now()-2592e6,n=e.filter(e=>e.timestamp>t);return n.length!==e.length&&f.x.setItem(h.V.RECENT_METRICS,n),n}catch(e){return Ce.v.error(e,{message:"Failed to get recent metrics:"}),[]}}const Ot=[{label:"Default",value:"default"},{label:"Dashboard Usage",value:"dashboard-usage"},{label:"Alerting Usage",value:"alerting-usage"}],Et="metrics-reducer-sort-by";class xt extends u.Bs{activationHandler(){const e=u.jh.getVariables(this).getByName(Et);this.supportedSortByOptions.has(e.getValue())||e.changeValueTo("default"),this._subs.add(e.subscribeToState((e,t)=>{e.value!==t.value&&this.publishEvent(new tt({sortBy:e.value}),!0)}))}getUsageDetailsForMetric(e,t){return this.usageFetcher.getUsageDetailsForMetric(e,t)}getUsageMetrics(e){return this.usageFetcher.getUsageMetrics(e).then(e=>{const t={};for(const n in e)t[n]=e[n].count;return t})}constructor(e){super(wt(vt({},e),{key:"metrics-sorter",$variables:new u.Pj({variables:[new u.yP({name:Et,label:"Sort by",value:"default",query:Ot.map(e=>`${e.label} : ${e.value}`).join(","),description:"Sort metrics by default (alphabetically, with recently-selected metrics first), by prevalence in dashboard panel queries, or by prevalence in alerting rules"})]}),inputControls:new u.K8({layout:"horizontal"})})),yt(this,"initialized",!1),yt(this,"supportedSortByOptions",new Set(["default","dashboard-usage","alerting-usage"])),yt(this,"usageFetcher",new bt),this.addActivationHandler(()=>this.activationHandler())}}function kt(e){const t=St().map(e=>e.name),n=new Set(t),[r,i]=e.reduce(([e,t],r)=>(n.has(r)?e.push(r):t.push(r),[e,t]),[[],[]]),a=function(e){return[...e].sort((e,t)=>(0,et._)(e,t))}(i);return[...t.filter(e=>r.includes(e)),...a]}yt(xt,"Component",({model:e})=>{const{inputControls:t}=e.useState();return m().createElement("div",{"data-testid":"sort-by-select"},m().createElement(t.Component,{model:t}))});class Pt extends u.mI{onActivate(){this.setState({skipUrlSync:!1}),this.subscribeToState((e,t)=>{e.value&&e.value!==t.value&&f.x.setItem(h.V.DATASOURCE,e.value)})}static getCurrentDataSource(){const e=Object.values(c.config.datasources).filter(e=>(0,ot.aQ)(e)),t=new URL(window.location.href).searchParams.get(`var-${U.EY}`),n=f.x.getItem(h.V.DATASOURCE),r=e.find(e=>e.uid===t)||e.find(e=>e.uid===n)||e.find(e=>e.isDefault)||e[0];return r?r.uid:(Ce.v.warn("Cannot find any Prometheus data source!"),"no-data-source-configured")}constructor({initialDS:e}){super({key:U.EY,name:U.EY,pluginId:"prometheus",label:"Data source",description:"Only prometheus data sources are supported",skipUrlSync:!e,value:e||Pt.getCurrentDataSource()}),this.addActivationHandler(this.onActivate.bind(this))}}const Ct=(e,t)=>e.length===t.length&&(0,w.isEqual)(e,t);function jt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _t(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const Tt="metrics-wingman";class It extends u.fS{constructor(e){super(_t(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){jt(e,t,n[t])})}return e}({key:Tt,name:Tt,label:"Metrics"},e),{datasource:U.GH,query:`label_values({$${U.Ao}}, __name__)`,includeAll:!0,value:"$__all",skipUrlSync:!0,refresh:s.VariableRefresh.onTimeRangeChanged,sort:s.VariableSort.alphabeticalAsc,hide:s.VariableHide.hideVariable}))}}const At={"11.6.x":function(e){const t=e.languageProvider;return"function"==typeof t.fetchLabelValues&&1===t.fetchLabelValues.length},"12.0.0":function(e){const t=e.languageProvider;return"function"==typeof t.fetchLabelValues&&t.fetchLabelValues.length>1},"12.1.0-plus":function(e){return"function"==typeof e.languageProvider.queryLabelKeys}};function Dt(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function Lt(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){Dt(a,r,i,o,s,"next",e)}function s(e){Dt(a,r,i,o,s,"throw",e)}o(void 0)})}}function Nt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Mt{getRuntimeDatasource(){return Lt(function*(){if(!this.datasource){const e=yield(0,c.getDataSourceSrv)().get(U.gR,{__sceneObject:{value:this.trail}});this.datasource=(0,ot.aQ)(e)?e:void 0}return this.datasource}).call(this)}init(){return Lt(function*(){for(const e of this.subs)e.unsubscribe();const e=u.jh.findByKeyAndType(this.trail,Tt,It);this.subs.push(e.subscribeToState((e,t)=>{Ct(e.options,t.options)||this.initializeClassicHistograms(e.options)}));const t=u.jh.findByKeyAndType(this.trail,U.EY,Pt);this.subs.push(t.subscribeToState((e,t)=>Lt(function*(){e.value!==t.value&&this.reset()}).call(this))),this.initializeClassicHistograms(e.state.options)}).call(this)}reset(){this.datasource=void 0,this.metricsMetadata=void 0,this.metadataFetchP=void 0,this.classicHistograms=void 0}initializeClassicHistograms(e){this.classicHistograms=new Set;for(const t of e){const e=t.value;e.endsWith("_bucket")&&this.classicHistograms.add(e)}}isNativeHistogram(e){return Lt(function*(){var t;if(F(e))return!1;if(null===(t=this.classicHistograms)||void 0===t?void 0:t.has(`${e}_bucket`))return!0;const n=yield this.getMetadataForMetric(e);return"histogram"===(null==n?void 0:n.type)}).call(this)}ensureMetricsMetadata(){return Lt(function*(){this.metadataFetchP||(this.metadataFetchP=this.getMetricsMetadata());try{yield this.metadataFetchP}catch(e){(0,O.HA)(["Error while initializing histograms!",e.toString(),"Native histogram metrics might not be properly displayed."])}}).call(this)}getMetricsMetadata(){return Lt(function*(){const e=yield this.getRuntimeDatasource();if(!e)return;const t=function(e){if(At["12.1.0-plus"](e))return e.languageProvider.queryMetricsMetadata;if(At["12.0.0"](e)||At["11.6.x"](e))return()=>Promise.resolve(e.languageProvider.metricsMetadata);throw new Error("Unsupported language provider version")}(e);let n=yield t();if(!n){const r=function(e,t){if(At["12.1.0-plus"](e))return e.languageProvider.retrieveMetricsMetadata;if(At["12.0.0"](e)||At["11.6.x"](e))return()=>{var n,r,i;return(null!==(i=null===(n=(r=e.languageProvider).loadMetricsMetadata)||void 0===n?void 0:n.call(r))&&void 0!==i?i:Promise.resolve()).then(()=>t())};throw new Error("Unsupported language provider version")}(e,t);n=yield r()}this.metricsMetadata=n?new Map(Object.entries(n)):void 0}).call(this)}getMetadataForMetric(e){return Lt(function*(){var t;return yield this.ensureMetricsMetadata(),null===(t=this.metricsMetadata)||void 0===t?void 0:t.get(e)}).call(this)}getTagKeys(e){return Lt(function*(){const t=yield this.getRuntimeDatasource();if(!t)return[];return yield t.getTagKeys(e)}).call(this)}getTagValues(e){return Lt(function*(){const t=yield this.getRuntimeDatasource();if(!t)return[];e.key=function(e){if(""===e||!function(e){return/^".*"$/.test(e)}(e))return e;return e.slice(1,-1)}(e.key);return yield t.getTagValues(e)}).call(this)}static fetchLabels(e){const{timeRange:t,matcher:n}=e,r=e.ds;if(At["12.1.0-plus"](r))return r.languageProvider.queryLabelKeys(t,n);if(At["12.0.0"](r))return r.languageProvider.fetchLabelsWithMatch(t,n).then(e=>Object.keys(e));if(At["11.6.x"](r))return r.languageProvider.fetchLabelsWithMatch(n).then(e=>Object.keys(e));throw new Error("Unsupported language provider version")}static fetchLabelValues(e){const{labelName:t,timeRange:n,matcher:r=""}=e,i=e.ds;if(At["12.1.0-plus"](i))return i.languageProvider.queryLabelValues(n,t,r);if(At["12.0.0"](i)){return(r?i.languageProvider.fetchSeriesValuesWithMatch:i.languageProvider.fetchLabelValues)(n,t,r)}if(At["11.6.x"](i)){return(r?i.languageProvider.fetchSeriesValuesWithMatch:i.languageProvider.fetchLabelValues)(t,r)}throw new Error("Unsupported language provider version")}static getPrometheusDataSourceForScene(e){return Lt(function*(){try{const n=u.jh.findByKey(e,U.EY);var t;const r=null!==(t=null==n?void 0:n.state.value)&&void 0!==t?t:"";return yield(0,c.getDataSourceSrv)().get({uid:r})}catch(e){return void(0,O.jx)(e,["Error while getting the Prometheus data source!"])}})()}constructor(e){Nt(this,"trail",void 0),Nt(this,"datasource",void 0),Nt(this,"subs",void 0),Nt(this,"metricsMetadata",void 0),Nt(this,"metadataFetchP",void 0),Nt(this,"classicHistograms",void 0),this.trail=e,this.subs=[]}}function Bt(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function Rt(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){Bt(a,r,i,o,s,"next",e)}function s(e){Bt(a,r,i,o,s,"throw",e)}o(void 0)})}}const $t="(none)";class Ft extends u.UU{query(){return Rt(function*(){return{state:s.LoadingState.Done,data:[{name:"Labels",fields:[{name:null,type:s.FieldType.other,values:[],config:{}}],length:0}]}})()}metricFindQuery(e,t){return Rt(function*(){var n,r;const i=null===(r=t.scopedVars)||void 0===r||null===(n=r.__sceneObject)||void 0===n?void 0:n.valueOf(),a=yield Mt.getPrometheusDataSourceForScene(i);if(!a)return[];var o;const[,s]=null!==(o=e.match(/valuesOf\((.+)\)/))&&void 0!==o?o:[];if(s){return(yield Ft.fetchLabelValues(s,i)).map(e=>({value:e,text:e}))}let l=[];try{l=yield this.fetchLabels(a,i,e)}catch(e){(0,O.HA)(["Error while fetching labels! Defaulting to an empty array.",e.toString()])}return[{value:$t,text:"(none)"},...l]}).call(this)}fetchLabels(e,t,n){return Rt(function*(){if(!Ft.getLabelsMatchAPISupport(e)){const n=Ft.getFiltersFromVariable(t),r=yield e.getTagKeys(n);return this.processLabelOptions(r.map(({text:e})=>({value:e,text:e})))}const r=yield Mt.fetchLabels({ds:e,timeRange:u.jh.getTimeRange(t).state.value,matcher:n});return this.processLabelOptions(r.map(e=>({value:e,text:e})))}).call(this)}static getLabelsMatchAPISupport(e){try{return e.hasLabelsMatchAPISupport()}catch(e){return(0,O.HA)(["Error while checking if the current data source supports the labels match API! Defaulting to false.",e.toString()]),!1}}static getFiltersFromVariable(e){const t=u.jh.lookupVariable(U.Ao,e);return(0,Ze.BE)(t)?{filters:t.state.filters}:{filters:[]}}processLabelOptions(e){return e.filter(({value:e})=>!e.startsWith("__")).sort((e,t)=>(0,et._)(e.value,t.value))}static fetchLabelValues(e,t){return Rt(function*(){const n=yield Mt.getPrometheusDataSourceForScene(t);if(!n)return[];try{return yield Mt.fetchLabelValues({ds:n,labelName:e,timeRange:u.jh.getTimeRange(t).state.value})}catch(t){return(0,O.HA)([`Error while retrieving label "${e}" values! Defaulting to an empty array.`,t.toString()]),[]}})()}testDatasource(){return Rt(function*(){return{status:"success",message:"OK"}})()}constructor(){super(Ft.uid,Ft.uid)}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Ft,"uid","grafana-prometheus-labels-datasource");const Vt="wingmanLabelValues";class qt extends u.fS{constructor({labelName:e}){super({name:Vt,datasource:{uid:Ft.uid},query:`valuesOf(${e})`,isMulti:!1,allowCustomValue:!1,refresh:s.VariableRefresh.onTimeRangeChanged,hide:s.VariableHide.hideVariable,value:"$__all",includeAll:!0})}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(qt,"Component",()=>m().createElement(m().Fragment,null));const Ht="labelsWingman";class zt extends u.fS{onActivate(){this._subs.add(this.subscribeToState((e,t)=>{e.query!==t.query&&(t.query&&this.setState({value:$t}),this.refreshOptions())})),this._subs.add(u.jh.findByKeyAndType(this,U.EY,u.mI).subscribeToState((e,t)=>{e.value!==t.value&&(this.setState({value:$t}),this.refreshOptions())})),this._subs.add(u.jh.findByKeyAndType(this,U.Ao,u.H9).subscribeToState((e,t)=>{e.filterExpression!==t.filterExpression&&this.updateQuery()})),this.updateQuery()}updateQuery(){const e=u.jh.interpolate(this,U.ui,{});this.setState({query:`{__name__=~".+",${e}}`})}constructor(){super({name:Ht,label:"Group by label",placeholder:"Group by label...",datasource:{uid:Ft.uid},query:"",includeAll:!1,isMulti:!1,allowCustomValue:!1,refresh:s.VariableRefresh.onTimeRangeChanged,hide:s.VariableHide.hideVariable}),this.addActivationHandler(this.onActivate.bind(this))}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(zt,"Component",({model:e})=>{const t=(0,d.useStyles2)(Ut),{label:n}=e.useState();return m().createElement("div",{className:t.container},m().createElement(d.Label,{className:t.label},n),m().createElement(u.fS.Component,{model:e}))});const Ut=e=>({container:o.css`
    display: flex;
    align-items: center;
    gap: 0;

    [class*='input-wrapper'] {
      width: 240px;
    }
  `,label:o.css`
    height: 32px;
    white-space: nowrap;
    margin: 0;
    background-color: ${e.colors.background.primary};
    padding: ${e.spacing(1)};
    border-radius: ${e.shape.radius.default};
    border: 1px solid ${e.colors.border.weak};
    border-right: none;
  `});function Gt(){return m().createElement("svg",{stroke:"currentColor",width:"17",height:"16",viewBox:"0 0 17 16",fill:"none"},m().createElement("circle",{cx:"8.92688",cy:"3.63132",r:"2.375",strokeWidth:"1.5"}),m().createElement("path",{d:"M13.6469 4.37965C14.6813 4.76699 15.3235 7.03139 14.9362 8.06582",strokeWidth:"1.5"}),m().createElement("path",{d:"M4.35309 4.37965C3.31866 4.76699 2.67651 7.03139 3.06384 8.06582",strokeWidth:"1.5"}),m().createElement("path",{d:"M10.3408 14.2531C9.75237 14.8415 8.11813 14.7799 7.50903 14.1708",strokeWidth:"1.5"}),m().createElement("circle",{cx:"4.00195",cy:"12.251",r:"2.375",strokeWidth:"1.5"}),m().createElement("circle",{cx:"13.8478",cy:"12.251",r:"2.375",strokeWidth:"1.5"}))}class Kt extends s.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Kt,"type","metrics-variable-activated");class Wt extends s.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Wt,"type","metrics-variable-deactivated");class Qt extends s.BusEventWithPayload{}function Yt(e){const t=e.state.key;if(!t)throw new TypeError(`Variable "${e.state.name}" has no key. Please provide a key in order to publish its lifecycle events.`);return e.addActivationHandler(()=>(e.publishEvent(new Kt({key:t}),!0),!e.state.loading&&e.state.options.length&&e.publishEvent(new Qt({key:t,options:e.state.options}),!0),e.subscribeToState((n,r)=>{!n.loading&&r.loading&&e.publishEvent(new Qt({key:t,options:n.options}),!0)}),()=>{e.publishEvent(new Wt({key:t}),!0)})),e}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Qt,"type","metrics-variable-loaded");function Xt(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function Jt(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){Xt(a,r,i,o,s,"next",e)}function s(e){Xt(a,r,i,o,s,"throw",e)}o(void 0)})}}class Zt extends u.UU{query(){return Jt(function*(){return{state:s.LoadingState.Done,data:[{name:"Labels",fields:[{name:null,type:s.FieldType.other,values:[],config:{}}],length:0}]}})()}metricFindQuery(e,t){return Jt(function*(){var n,r;const i=null===(r=t.scopedVars)||void 0===r||null===(n=r.__sceneObject)||void 0===n?void 0:n.valueOf(),a=yield Mt.getPrometheusDataSourceForScene(i);if(!a)return[];const o=u.jh.getTimeRange(i).state.value;let s=[];const l=e.startsWith("removeRules"),c=l?e.replace("removeRules",""):e;return s=yield Mt.fetchLabelValues({ds:a,labelName:"__name__",matcher:c,timeRange:o}),l&&(s=s.filter(e=>!(e=>"ALERTS"===e||"ALERTS_FOR_STATE"===e||e.includes(":"))(e))),s.map(e=>({value:e,text:e}))})()}testDatasource(){return Jt(function*(){return{status:"success",message:"OK"}})()}constructor(){super(Zt.uid,Zt.uid)}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Zt,"uid","grafana-prometheus-metrics-with-label-values-datasource");const en="metrics-with-label-value";class tn extends u.fS{onActivate(e,t,n){const r=u.jh.lookupVariable(U.Ao,this);(null==r?void 0:r.state.hide)!==s.VariableHide.hideVariable&&this.setState({query:tn.buildQuery(e,t,n)})}static buildQuery(e,t,n){return n?`removeRules{${e}="${t}",${U.ui}}`:`{${e}="${t}",${U.ui}}`}constructor({labelName:e,labelValue:t,removeRules:n}){return super({key:`${en}-${e}-${t}`,name:en,datasource:{uid:Zt.uid},query:tn.buildQuery(e,t,n),isMulti:!1,allowCustomValue:!1,refresh:s.VariableRefresh.onTimeRangeChanged,hide:s.VariableHide.hideVariable,skipUrlSync:!0,value:"$__all",includeAll:!0}),this.addActivationHandler(this.onActivate.bind(this,e,t,n)),Yt(this)}}class nn extends u.Bs{onActivate(){this.subscribeToLayoutChange()}subscribeToLayoutChange(){const e=u.jh.findByKeyAndType(this,"layout-switcher",We),t=this.state.body.state.body,n=(e,n)=>{e.layout!==(null==n?void 0:n.layout)&&t.setState({templateColumns:e.layout===Ke.ROWS?Or:Sr})};n(e.state),this._subs.add(e.subscribeToState(n))}constructor({index:e,labelName:t,labelValue:n,labelCardinality:r}){super({index:e,labelName:t,labelValue:n,labelCardinality:r,key:`${t||""}-${n||""}`,$variables:new u.Pj({variables:[new tn({labelName:t,labelValue:n})]}),body:new Ye({variableName:en,initialPageSize:3,body:new u.gF({children:[],isLazy:!0,templateColumns:Sr,autoRows:vr,$behaviors:[new u.Gg.K2({key:"metricCrosshairSync",sync:s.DashboardCursorSync.Crosshair})]}),getLayoutLoading:()=>new u.dM({reactNode:m().createElement(d.Spinner,{inline:!0})}),getLayoutEmpty:()=>new u.dM({reactNode:m().createElement(Ue._,{title:"",severity:"info"},"No metrics found for the current filters and time range.")}),getLayoutError:e=>new u.dM({reactNode:m().createElement(Ue._,{severity:"error",title:"Error while loading metrics!",error:e})}),getLayoutChild:(e,r)=>{const i=e.value;return new u.xK({body:new wr({metric:i,vizPanelInGridItem:new He({metric:i,panelOptions:{fixedColorIndex:r,headerActions:()=>[new K({metric:i}),new y({metric:i})]},queryOptions:{labelMatchers:[{key:t,operator:"=",value:n}]}})})})}})}),this.addActivationHandler(this.onActivate.bind(this))}}function rn(e){return{container:(0,o.css)({background:e.colors.background.canvas,margin:e.spacing(1,0,0,0),"& div:focus-within":{boxShadow:"none !important"}}),containerHeader:(0,o.css)({display:"flex",alignItems:"center",gap:"8px",marginBottom:"-36px",paddingBottom:e.spacing(1.5),borderBottom:`1px solid ${e.colors.border.medium}`}),headerButtons:(0,o.css)({position:"relative",top:"3px",marginLeft:"auto",marginRight:"30px",zIndex:100}),selectButton:(0,o.css)({height:"28px"}),collapsableSectionBody:(0,o.css)({display:"flex",flexDirection:"column",gap:"24px",padding:e.spacing(1)}),groupName:(0,o.css)({display:"flex",alignItems:"center",fontSize:"1.3rem",lineHeight:"1.3rem"}),labelValue:(0,o.css)({fontSize:"16px",marginLeft:"8px"}),index:(0,o.css)({fontSize:"12px",color:e.colors.text.secondary,marginLeft:"8px"}),footer:(0,o.css)({display:"flex",justifyContent:"center",alignItems:"center",marginTop:e.spacing(1),"& button":{height:"40px"}}),variable:(0,o.css)({display:"none"})}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(nn,"Component",({model:e})=>{const[t,n]=(0,p.useState)(!1),r=(0,d.useStyles2)(rn),{index:i,labelName:a,labelValue:o,labelCardinality:s,$variables:l,body:c}=e.useState(),h=l.state.variables[0],{loading:f,error:g}=h.useState(),b=c.useSizes(),y=!f&&!g&&b.total>0&&b.current<b.total;return m().createElement("div",{className:r.container,"data-testid":`${a}-${o}-metrics-group`},m().createElement("div",{className:r.containerHeader},m().createElement("div",{className:r.headerButtons},m().createElement(d.Button,{className:r.selectButton,variant:"secondary",onClick:()=>{var t;const n=u.jh.lookupVariable(U.Ao,e);n.setState({filters:[...n.state.filters,{key:a,operator:"=",value:o}]}),null===(t=u.jh.lookupVariable(Ht,e))||void 0===t||t.changeValueTo($t)},tooltip:`See metrics with ${a}=${o}`,tooltipPlacement:"top"},"Select"))),m().createElement(d.CollapsableSection,{isOpen:!t,onToggle:()=>n(!t),label:m().createElement("div",{className:r.groupName},m().createElement(Gt,null),m().createElement("div",{className:r.labelValue},o),s>1&&m().createElement("div",{className:r.index},"(",i+1,"/",s,")"))},m().createElement("div",{className:r.collapsableSectionBody},m().createElement(c.Component,{model:c})),y&&m().createElement("div",{className:r.footer},m().createElement(Je,{label:"metric",batchSizes:b,onClick:()=>{c.increaseBatchSize()},tooltip:`Show more metrics for ${a}="${o}"`}))),m().createElement("div",{className:r.variable},m().createElement(h.Component,{key:h.state.name,model:h})))});class an extends u.Bs{constructor({labelName:e}){super({key:"metrics-group-list",labelName:e,$variables:new u.Pj({variables:[new qt({labelName:e})]}),body:new Ye({variableName:Vt,initialPageSize:20,pageSizeIncrement:10,body:new u.gF({children:[],isLazy:!0,templateColumns:"1fr",autoRows:"auto",rowGap:1}),getLayoutLoading:()=>new u.dM({reactNode:m().createElement(d.Spinner,{inline:!0})}),getLayoutEmpty:()=>new u.dM({reactNode:m().createElement(Ue._,{title:"",severity:"info"},'No label values found for label "',e,'".')}),getLayoutError:t=>new u.dM({reactNode:m().createElement(Ue._,{severity:"error",title:`Error while loading label "${e}" values!`,error:t})}),getLayoutChild:(t,n,r)=>new u.xK({body:new nn({index:n,labelName:e,labelValue:t.value,labelCardinality:r.length})})})})}}function on(e){return{footer:(0,o.css)({display:"flex",justifyContent:"center",alignItems:"center",margin:e.spacing(3,0,1,0),"& button":{height:"40px"}}),variable:(0,o.css)({display:"none"})}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(an,"Component",({model:e})=>{const t=(0,d.useStyles2)(on),{body:n,$variables:r,labelName:i}=e.useState(),a=r.state.variables[0],{loading:o,error:s}=a.useState(),l=n.useSizes(),c=!o&&!s&&l.total>0&&l.current<l.total;return m().createElement("div",{"data-testid":"metrics-groupby-list"},m().createElement(n.Component,{model:n}),c&&m().createElement("div",{className:t.footer},m().createElement(Je,{label:`"${i}" value`,batchSizes:l,onClick:()=>{n.increaseBatchSize()}})),m().createElement("div",{className:t.variable},m().createElement(a.Component,{key:a.state.name,model:a})))});const sn="filtered-metrics-wingman";class ln extends u.yP{onActivate(){const e=u.jh.findByKeyAndType(this,Tt,It),{loading:t,error:n,options:r}=e.state;this.setState({loading:t,error:n,options:r}),this._subs.add(e.subscribeToState(e=>{this.setState({loading:e.loading,error:e.error,options:e.options})}))}constructor(){return super({key:sn,name:sn,label:"Filtered Metrics",loading:!1,error:null,options:[],includeAll:!0,value:"$__all",skipUrlSync:!0}),this.addActivationHandler(this.onActivate.bind(this)),Yt(this)}}function cn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function un(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class dn extends u.Bs{useCounts(){return this.useState().counts}constructor(e){super(un(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){cn(e,t,n[t])})}return e}({},e),{counts:{current:0,total:0}}))}}class pn extends dn{onActivate(){const e=u.jh.lookupVariable(Tt,this),t=u.jh.lookupVariable(sn,this);this.setInitCounts(e,t),this._subs.add(e.subscribeToState((e,n)=>{Ct(e.options,n.options)||this.setState({counts:{current:t.state.options.length,total:e.options.length}})})),this._subs.add(t.subscribeToState((t,n)=>{t.loading||n.loading||Ct(t.options,n.options)||this.setState({counts:{current:t.options.length,total:e.state.options.length}})}))}setInitCounts(e,t){const n={current:0,total:0};!e.state.loading&&e.state.options.length&&(n.total=e.state.options.length),!t.state.loading&&t.state.options.length&&(n.current=t.state.options.length),this.setState({counts:n})}constructor(){super({key:"MetricVariableCountsProvider"}),this.addActivationHandler(this.onActivate.bind(this))}}class mn extends s.BusEventWithPayload{}function hn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(mn,"type","quick-search-changed");class fn extends u.Bs{getUrlState(){return{[this.state.urlSearchParamName]:this.state.value}}updateFromUrl(e){const t=e[this.state.urlSearchParamName]||"";t!==this.state.value&&this.setState({value:t})}toggleCountsDisplay(e){this.setState({displayCounts:e})}updateValue(e){""===this.state.value&&""!==e&&(0,S.z)("quick_search_used",{}),this.setState({value:e}),this.notifyValueChange(e)}useHumanFriendlyCountsMessage(){const{targetName:e,countsProvider:t,displayCounts:n}=this.state,r=t.useCounts();return n?r.current===r.total?{tagName:`${r.current}`,tooltipContent:1!==r.current?`${r.current} ${e}s in total`:`1 ${e} in total`}:{tagName:`${r.current}/${r.total}`,tooltipContent:1!==r.current?`${r.current} out of ${r.total} ${e}s in total`:`1 out of ${r.total} ${e}s in total`}:{tagName:"",tooltipContent:""}}constructor({urlSearchParamName:e,targetName:t,countsProvider:n,displayCounts:r}){super({key:"quick-search",urlSearchParamName:e,targetName:t,countsProvider:n,displayCounts:Boolean(r),value:""}),hn(this,"_variableDependency",new u.Sh(this,{variableNames:[U.EY],onReferencedVariableValueChanged:()=>{this.setState({value:""})}})),hn(this,"_urlSync",new u.So(this,{keys:[this.state.urlSearchParamName]})),hn(this,"notifyValueChange",(0,w.debounce)(e=>{this.publishEvent(new mn({searchText:e}),!0)},250)),hn(this,"onChange",e=>{this.updateValue(e.currentTarget.value)}),hn(this,"clear",()=>{this.updateValue("")}),hn(this,"onKeyDown",e=>{"Escape"===e.key&&(e.preventDefault(),this.clear())})}}hn(fn,"Component",({model:e})=>{const t=(0,d.useStyles2)(gn),{targetName:n,value:r,countsProvider:i}=e.useState(),{tagName:a,tooltipContent:o}=e.useHumanFriendlyCountsMessage();return m().createElement(d.Input,{value:r,onChange:e.onChange,onKeyDown:e.onKeyDown,placeholder:`Quick search ${n}s`,prefix:m().createElement("i",{className:"fa fa-search"}),suffix:m().createElement(m().Fragment,null,m().createElement(i.Component,{model:i}),a&&m().createElement(d.Tooltip,{content:o,placement:"top"},m().createElement(d.Tag,{className:t.counts,name:a,colorIndex:9})),m().createElement(d.IconButton,{name:"times",variant:"secondary",tooltip:"Clear search",onClick:e.clear,disabled:!r}))})});const gn=e=>({counts:o.css`
    margin-right: ${e.spacing(1)};
    border-radius: 11px;
    padding: 2px ${e.spacing(1)};
    color: ${e.colors.text.primary};
    background-color: ${e.colors.background.secondary};
  `});function bn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function yn(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class vn extends u.P1{constructor(e){super(yn(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){bn(e,t,n[t])})}return e}({},e),{key:"list-controls",body:new u.G1({direction:"row",width:"100%",maxHeight:"32px",children:[new u.vA({body:new fn({urlSearchParamName:"search_txt",targetName:"metric",countsProvider:new pn})}),new u.vA({width:"auto",body:new xt({})}),new u.vA({width:"auto",body:new We({})})]})}))}}function wn(){return{headerWrapper:(0,o.css)({display:"flex",alignItems:"center","& > div":{display:"flex",alignItems:"center","& > div":{display:"flex",alignItems:"center"}}})}}function Sn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}bn(vn,"Component",({model:e})=>{const t=(0,d.useStyles2)(wn),{body:n}=e.useState();return m().createElement("div",{className:t.headerWrapper},m().createElement(n.Component,{model:n}))});class On{setInitOptions(e){this.initOptions=(0,w.cloneDeep)(e)}getFilters(){return this.filters}static getFilteredOptions(e,t){let n=e;return t.categories.length>0&&(n=On.applyCategoryFilters(n,t.categories)),t.prefixes.length>0&&(n=On.applyPrefixFilters(n,t.prefixes)),t.suffixes.length>0&&(n=On.applySuffixFilters(n,t.suffixes)),t.names.length>0&&(n=On.applyNameFilters(n,t.names)),n}applyFilters(e=this.filters,t={forceUpdate:!1,notify:!0}){const n=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Sn(e,t,n[t])})}return e}({},this.filters,e);if(!t.forceUpdate&&(0,w.isEqual)(this.filters,n))return;if(!(n.categories.length||n.prefixes.length||n.suffixes.length||n.names.length))return this.filters=n,this.variable.setState({options:this.initOptions}),void(t.notify&&this.notifyUpdate());this.filters=n;const r=On.getFilteredOptions(this.initOptions,this.filters);this.variable.setState({options:r}),t.notify&&this.notifyUpdate()}static applyCategoryFilters(e,t){let n=[];for(const r of t){const t=On.buildRegex(r,"i");n=n.concat(e.filter(e=>t.test(e.value)))}return n}static applyPrefixFilters(e,t){const n=t.map(e=>e.includes("|")?`${e.split("|").map(e=>`^${e}([^a-z0-9]|$)`).join("|")}`:`^${e}([^a-z0-9]|$)`).join("|"),r=On.buildRegex(`(${n})`);return e.filter(e=>r.test(e.value))}static applySuffixFilters(e,t){const n=t.map(e=>e.includes("|")?`${e.split("|").map(e=>`(^|[^a-z0-9])${e}$`).join("|")}`:`(^|[^a-z0-9])${e}$`).join("|"),r=On.buildRegex(`(${n})`);return e.filter(e=>r.test(e.value))}static applyNameFilters(e,t){const[n]=t,r=n.split(",").map(e=>e.trim()).filter(Boolean).map(e=>{try{return new RegExp(e)}catch(e){return null}}).filter(Boolean);return e.filter(e=>r.some(t=>t.test(e.value)))}static buildRegex(e,t){try{return new RegExp(e,t)}catch(e){return new RegExp(".*")}}notifyUpdate(){this.variable.publishEvent(new u.oh(this.variable),!0)}constructor(e){Sn(this,"variable",void 0),Sn(this,"initOptions",[]),Sn(this,"filters",{categories:[],prefixes:[],suffixes:[],names:[]}),this.variable=e}}var En=n(902);const xn=new Map;function kn(e,t){let n=xn.get(e);n||(n=new Map,xn.set(e,n));let r=n.get(t);if(!r){const i=e.split("_"),a=i.slice(0,i.length/2).join("_");r={halfLeven:(0,En.A)(a,t)||0,wholeLeven:(0,En.A)(e,t)||0},n.set(t,r)}return r}function Pn(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function Cn(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){Pn(a,r,i,o,s,"next",e)}function s(e){Pn(a,r,i,o,s,"throw",e)}o(void 0)})}}function jn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class _n{sort(){return Cn(function*(e=this.sortBy,t={}){const n=this.variable.state.options.map(e=>e.value);if(e===this.sortBy&&Ct(n,this.lastMetrics))return;let r;switch(e){case"dashboard-usage":case"alerting-usage":r=yield this.sortByUsage(n,e);break;case"related":i=n,a=t.metric,r=i.sort((e,t)=>{const n=kn(e,a),r=kn(t,a);return n.halfLeven+n.wholeLeven-(r.halfLeven+r.wholeLeven)});break;default:r=kt(n)}var i,a;this.sortBy=e,this.lastMetrics=r,this.variable.setState({options:r.map(e=>({label:e,value:e}))}),this.notifyUpdate()}).apply(this,arguments)}sortByUsage(e,t){return Cn(function*(){try{const n=u.jh.findByKeyAndType(this.variable,"metrics-sorter",xt);if(!n)return Ce.v.warn("Metrics sorter not found. Returning unsorted metrics.",{usageType:t}),e;const r=yield n.getUsageMetrics(t);return function(e,t){return[...e].sort((e,n)=>{const r=t[e]||0,i=t[n]||0;return i!==r?i-r:(0,et._)(e,n)})}(e,r)}catch(n){const r="string"==typeof n?new Error(n):n;return Ce.v.error(r,{usageType:t}),e}}).call(this)}notifyUpdate(){this.variable.publishEvent(new u.oh(this.variable),!0)}constructor(e){jn(this,"variable",void 0),jn(this,"lastMetrics",void 0),jn(this,"sortBy",void 0),this.variable=e,this.sortBy=void 0,this.lastMetrics=[]}}class Tn extends s.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Tn,"type","filters-changed");const In="Non-rules metrics",An="Recording rules";class Dn extends s.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Dn,"type","section-value-changed");var Ln=n(1053);const Nn=({label:e,count:t,checked:n,onChange:r})=>{const i=(0,d.useStyles2)(Mn);return m().createElement("div",{className:i.checkboxWrapper,title:e},m().createElement(d.Checkbox,{label:e,value:n,onChange:r}),m().createElement("span",{className:i.count},"(",t,")"))};function Mn(e){return{checkboxWrapper:(0,o.css)({display:"flex",alignItems:"center",width:"100%","& label *":{fontSize:"14px !important",whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"}}),count:(0,o.css)({color:e.colors.text.secondary,marginLeft:e.spacing(.5),display:"inline-block"})}}function Bn({groups:e,selectedGroups:t,onSelectionChange:n}){const r=(0,d.useStyles2)(Rn);return m().createElement(m().Fragment,null,m().createElement("div",{className:r.checkboxListHeader},m().createElement("div",null,t.length," selected"),m().createElement(d.Button,{variant:"secondary",fill:"text",onClick:()=>n([]),disabled:!t.length},"clear")),!e.length&&m().createElement("div",{className:r.noResults},"No results."),e.length>0&&m().createElement("ul",{className:r.checkboxList,"data-testid":"checkbox-filters-list"},e.map(e=>m().createElement("li",{key:e.value,className:r.checkboxItem},m().createElement(Nn,{label:e.label,count:e.count,checked:t.some(t=>t.value===e.value),onChange:r=>{const i=r.currentTarget.checked?[...t,{label:e.label,value:e.value}]:t.filter(t=>t.value!==e.value);n(i)}})))))}function Rn(e){return{checkboxListHeader:(0,o.css)({display:"flex",justifyContent:"space-between",alignItems:"center",color:e.colors.text.secondary,margin:e.spacing(0),padding:e.spacing(0,0,0,1)}),checkboxList:(0,o.css)({height:"100%",margin:0,padding:e.spacing(0,1,1,1),overflowY:"auto","& .css-1n4u71h-Label":{fontSize:"14px !important"},"&::-webkit-scrollbar":{"-webkit-appearance":"none",width:"7px"},"&::-webkit-scrollbar-thumb":{borderRadius:"4px",backgroundColor:e.colors.secondary.main,"-webkit-box-shadow":`0 0 1px ${e.colors.secondary.shade}`}}),checkboxItem:(0,o.css)({display:"flex",alignItems:"center",width:"100%",padding:e.spacing(.5,0)}),noResults:(0,o.css)({fontStyle:"italic",padding:e.spacing(0,1,1,1)})}}function $n(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Fn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){$n(e,t,n[t])})}return e}function Vn(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class qn extends u.Bs{getUrlState(){return{[this.state.key]:this.state.selectedGroups.map(e=>e.value).join(",")}}updateFromUrl(e){const t={};"string"==typeof e[this.state.key]&&e[this.state.key]!==this.state.selectedGroups.map(e=>e.value).join(",")&&(t.selectedGroups=e[this.state.key].split(",").map(e=>({label:e,value:e}))),this.setState(t)}onActivate(){const e=u.jh.lookupVariable(Tt,this),t=u.jh.lookupVariable(sn,this);this.updateLists(e.state.options),this.updateCounts();const{selectedGroups:n}=this.state;this.setState({loading:t.state.loading,active:n.length>0})}updateLists(e){this.setState({groups:this.state.computeGroups(e),loading:!1})}updateCounts(){var e;const{groups:t,computeGroups:n,type:r}=this.state,i=u.jh.lookupVariable(Tt,this).state.options,a=null===(e=u.jh.getAncestor(this,ur).state.enginesMap.get(sn))||void 0===e?void 0:e.filterEngine;if(!a)return void Ce.v.warn("MetricsFilterSection: No filter engine found");const o=Vn(Fn({},a.getFilters()),{[r]:[]}),s=On.getFilteredOptions(i,o),l=new Map(n(s).map(e=>[e.label,e.count])),c=t.map(e=>{var t;return Vn(Fn({},e),{count:null!==(t=l.get(e.label))&&void 0!==t?t:0})});this.setState({groups:c,loading:!1})}constructor({key:e,type:t,title:n,description:r,icon:i,computeGroups:a,showHideEmpty:o,showSearch:s,disabled:l,active:c}){super({key:e,type:t,title:n,description:r,icon:i,groups:[],computeGroups:a,selectedGroups:[],loading:!0,showHideEmpty:null==o||o,showSearch:null==s||s,disabled:null!=l&&l,active:null!=c&&c}),$n(this,"_variableDependency",new u.Sh(this,{variableNames:[Tt,sn],onReferencedVariableValueChanged:e=>{const{name:t,options:n}=e.state;t!==Tt?t===sn&&this.updateCounts():this.updateLists(n)}})),$n(this,"_urlSync",new u.So(this,{keys:[this.state.key]})),$n(this,"onSelectionChange",e=>{this.setState({selectedGroups:e,active:e.length>0}),this.publishEvent(new Tn({type:this.state.type,filters:e.map(e=>e.value)}),!0),this.publishEvent(new Dn({key:this.state.key,values:e.map(e=>e.label)}),!0),"prefixes"===this.state.type?(0,S.z)("sidebar_prefix_filter_applied",{filter_count:e.length}):"suffixes"===this.state.type&&(0,S.z)("sidebar_suffix_filter_applied",{filter_count:e.length}),"filters-rule"===this.state.key&&e.length>0&&e.forEach(e=>{let t;switch(e.label){case In:t="non_rules_metrics";break;case An:t="recording_rules";break;default:return}(0,S.z)("sidebar_rules_filter_selected",{filter_type:t})})}),this.addActivationHandler(this.onActivate.bind(this))}}function Hn(e){return{container:(0,o.css)({display:"flex",flexDirection:"column",gap:e.spacing(1),height:"100%",overflowY:"hidden"}),switchContainer:(0,o.css)({display:"flex",alignItems:"center",justifyContent:"flex-end",gap:e.spacing(1)}),switchLabel:(0,o.css)({fontSize:"12px",color:e.colors.text.primary}),searchInput:(0,o.css)({flexBasis:"32px",flexShrink:0,marginBottom:e.spacing(1),padding:e.spacing(0,.5)})}}$n(qn,"Component",({model:e})=>{const t=(0,d.useStyles2)(Hn),{groups:n,selectedGroups:r,loading:i,title:a,description:o,showHideEmpty:s,showSearch:l}=e.useState(),[c,u]=(0,p.useState)(!1),[h,f]=(0,p.useState)(""),g=(0,p.useMemo)(()=>{const e=[];return c&&e.push(e=>e.count>0),e.push(e=>e.label.toLowerCase().includes(h.toLowerCase())),n.filter(t=>e.every(e=>e(t)))},[c,n,h]);return m().createElement("div",{className:t.container},m().createElement(Ln._,{title:a,description:o}),s&&m().createElement("div",{className:t.switchContainer},m().createElement("span",{className:t.switchLabel},"Hide empty"),m().createElement(d.Switch,{value:c,onChange:e=>u(e.currentTarget.checked)})),l&&m().createElement(d.Input,{className:t.searchInput,prefix:m().createElement(d.Icon,{name:"search"}),placeholder:"Search...",value:h,onChange:e=>f(e.currentTarget.value),onKeyDown:e=>{"Escape"===e.key&&(e.preventDefault(),f(""))},suffix:m().createElement(d.IconButton,{name:"times",variant:"secondary",tooltip:"Clear search",onClick:()=>f("")})}),i&&m().createElement(d.Spinner,{inline:!0}),!i&&m().createElement(Bn,{groups:g,selectedGroups:r,onSelectionChange:e.onSelectionChange}))});function zn(e){const t=new Map;for(const r of e){const e=r.value.split(/[^a-z0-9]/i),i=e.length<=1?r.value:e[0];var n;const a=null!==(n=t.get(i))&&void 0!==n?n:[];a.push(r.value),t.set(i||"<none>",a)}const r=new Map;for(const[e,n]of t)r.set(e,n.length);return Array.from(r.entries()).sort((e,t)=>e[1]!==t[1]?t[1]-e[1]:(0,et._)(e[0],t[0])).map(([e,t])=>({value:e,count:t,label:e}))}function Un(e){const t=new Map;for(const r of e){const e=r.value.split(/[^a-z0-9]/i),i=e.length<=1?r.value:e[e.length-1];var n;const a=null!==(n=t.get(i))&&void 0!==n?n:[];a.push(r.value),t.set(i||"<none>",a)}const r=new Map;for(const[e,n]of t)r.set(e,n.length);return Array.from(r.entries()).sort((e,t)=>e[1]!==t[1]?t[1]-e[1]:(0,et._)(e[0],t[0])).map(([e,t])=>({value:e,count:t,label:e}))}function Gn(e){return/^\w*:.*?(?::\w+)?$/.test(e)}function Kn(e){const t=new Map([["metrics",[]],["rules",[]]]);for(const r of e){const{value:e}=r,i=Gn(e)?"rules":"metrics";var n;const a=null!==(n=t.get(i))&&void 0!==n?n:[];a.push(e),t.set(i,a)}return[{value:"^(?!.*:.*)",label:In,count:t.get("metrics").length},{value:":",label:An,count:t.get("rules").length}]}var Wn=n(5521);function Qn({labels:e,selectedLabel:t,onClickLabel:n,onClickClearSelection:r}){const i=(0,d.useStyles2)(Yn);return m().createElement(m().Fragment,null,m().createElement("div",{className:i.listHeader},m().createElement("div",{className:i.selected},t===$t?"No selection":`Selected: "${t}"`),m().createElement(d.Button,{variant:"secondary",fill:"text",onClick:r,disabled:t===$t},"clear")),!e.length&&m().createElement("div",{className:i.noResults},"No results."),e.length>0&&m().createElement("div",{className:i.list,"data-testid":"labels-list"},m().createElement(d.RadioButtonList,{name:"labels-list",options:e,onChange:n,value:t})))}function Yn(e){return{listHeader:(0,o.css)({display:"flex",justifyContent:"space-between",alignItems:"center",color:e.colors.text.secondary,margin:e.spacing(0),padding:e.spacing(0,0,0,1)}),selected:(0,o.css)({overflow:"hidden",whiteSpace:"nowrap",textOverflow:"ellipsis"}),list:(0,o.css)({display:"flex",flex:1,flexDirection:"column",gap:0,overflowY:"auto",'& [role="radiogroup"]':{gap:0},"& label":{cursor:"pointer",padding:e.spacing(.5,1),"&:hover":{background:e.colors.background.secondary}},"& label div":{whiteSpace:"nowrap",overflow:"hidden",textOverflow:"ellipsis"}}),noResults:(0,o.css)({fontStyle:"italic",padding:e.spacing(0,1,1,1)})}}function Xn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Jn extends u.Bs{onActivate(){const e=u.jh.lookupVariable(this.state.variableName,this),t=e.state.value;this.setState({active:Boolean(t&&t!==$t)}),this._subs.add(e.subscribeToState(e=>{const t=Boolean(e.value&&e.value!==$t);this.setState({active:t}),this.publishEvent(new Dn({key:this.state.key,values:t?[e.value]:[]}),!0)}))}selectLabel(e){u.jh.lookupVariable(this.state.variableName,this).changeValueTo(e);const t=Boolean(e&&e!==$t);this.setState({active:t}),this.publishEvent(new Dn({key:this.state.key,values:t?[e]:[]}),!0)}constructor({key:e,variableName:t,title:n,description:r,icon:i,disabled:a,active:o}){super({key:e,variableName:t,title:n,description:r,icon:i,disabled:null!=a&&a,active:null!=o&&o}),Xn(this,"onClickLabel",e=>{(0,S.z)("sidebar_group_by_label_filter_applied",{label:e}),this.selectLabel(e)}),Xn(this,"onClickClearSelection",()=>{this.selectLabel($t)}),Xn(this,"useLabelsBrowser",()=>{const{variableName:e,title:t,description:n}=this.useState(),r=u.jh.lookupVariable(e,this),{loading:i,options:a,value:o}=r.useState(),[s,l]=(0,p.useState)("");return{title:t,description:n,loading:i,selectedLabel:o,labelsList:(0,p.useMemo)(()=>{const e=[e=>e!==$t,e=>e.toLowerCase().includes(s.toLowerCase())];return a.filter(t=>e.every(e=>e(t.value)))},[a,s]),searchValue:s,onInputChange:e=>{l(e.currentTarget.value)},onInputKeyDown:e=>{"Escape"===e.key&&(e.preventDefault(),l(""))},onInputClear:()=>{l("")}}}),this.addActivationHandler(this.onActivate.bind(this))}}function Zn(e){return{container:(0,o.css)({display:"flex",flexDirection:"column",gap:e.spacing(1),height:"100%",overflowY:"hidden"}),search:(0,o.css)({marginBottom:e.spacing(1),padding:e.spacing(0,.5)})}}Xn(Jn,"Component",({model:e})=>{const t=(0,d.useStyles2)(Zn),{title:n,description:r,loading:i,labelsList:a,selectedLabel:o,searchValue:s,onInputChange:l,onInputKeyDown:c,onInputClear:u}=e.useLabelsBrowser();return m().createElement("div",{className:t.container,"data-testid":"labels-browser"},m().createElement(Ln._,{title:n,description:r}),m().createElement(d.Input,{className:t.search,prefix:m().createElement(d.Icon,{name:"search"}),placeholder:"Search...",value:s,onChange:l,onKeyDown:c,suffix:m().createElement(d.IconButton,{name:"times",variant:"secondary",tooltip:"Clear search",onClick:u})}),i&&m().createElement(d.Spinner,{inline:!0}),!i&&m().createElement(Qn,{labels:a,selectedLabel:o,onClickLabel:e.onClickLabel,onClickClearSelection:e.onClickClearSelection}))});class er extends u.Bs{onActivate(){}constructor({key:e,title:t,description:n,icon:r,disabled:i}){super({key:e,title:t,description:n,icon:r,disabled:null!=i&&i,active:!1}),this.addActivationHandler(this.onActivate.bind(this))}}function tr(e){return{container:(0,o.css)({display:"flex",flexDirection:"column",gap:e.spacing(1),height:"100%",overflowY:"hidden"})}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(er,"Component",({model:e})=>{const t=(0,d.useStyles2)(tr),{title:n,description:r}=e.useState();return m().createElement("div",{className:t.container},m().createElement(Ln._,{title:n,description:r}))});const nr=new Map([["rules",function(){return m().createElement("svg",{stroke:"currentColor",width:"16",height:"16",viewBox:"0 0 16 16",fill:"none"},m().createElement("rect",{x:"1.25",y:"1.625",width:"5.25",height:"5.25",rx:"1",strokeWidth:"1.5"}),m().createElement("circle",{cx:"12.25",cy:"4.25",r:"2.75",strokeWidth:"1.5"}),m().createElement("circle",{cx:"3.75",cy:"11.75",r:"2.75",strokeWidth:"1.5"}),m().createElement("rect",{x:"9.5",y:"9.125",width:"5.25",height:"5.25",rx:"1",strokeWidth:"1.5"}))}],["groups",Gt]]);function rr({ariaLabel:e,disabled:t,visible:n,active:r,tooltip:i,iconOrText:a,onClick:l}){const c=(0,d.useStyles2)(ir);let u,p;return a in s.availableIconsIndex?u=a:p=nr.has(a)?nr.get(a):function(){return m().createElement(m().Fragment,null,a)},m().createElement(d.Button,{className:(0,o.cx)(c.button,t&&"disabled",n&&"visible",r&&"active"),size:"md",variant:"secondary",fill:"text",icon:u,"aria-label":e,tooltip:i,tooltipPlacement:"right",onClick:l,disabled:t},p&&m().createElement(p,null))}function ir(e){return{button:(0,o.css)({margin:0,color:e.colors.text.secondary,"&:hover":{color:e.colors.text.maxContrast,background:"transparent"},"&.disabled:hover":{color:e.colors.text.secondary},"&.visible":{color:e.colors.text.maxContrast},"&.active":{color:e.colors.text.maxContrast}})}}function ar(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const or=["filters-rule","filters-prefix","filters-suffix"];class sr extends u.Bs{onActivate(){const e=this.initOtherMetricsVar();return this._subs.add(this.subscribeToEvent(Dn,e=>{const{key:t,values:n}=e.payload,{sectionValues:r}=this.state,i=new Map(r).set(t,n);this.setOtherMetricFilters(i),this.setState({sectionValues:i})})),()=>{e()}}setOtherMetricFilters(e){const t=u.jh.lookupVariable(U.hc,this);if(!(0,Ze.BE)(t))return;const n={"filters-rule":"rule group","filters-prefix":"prefix","filters-suffix":"suffix"},r=Array.from(e.entries()).reduce((e,[t,r])=>(r.length&&or.includes(t)&&e.push({key:t,operator:"=",value:r.join(", "),keyLabel:n[t]}),e),[]);t.setState({filters:r,hide:r.length?s.VariableHide.hideLabel:s.VariableHide.hideVariable})}initOtherMetricsVar(){const e=(0,H.kj)(this).state.$variables;if(!e)return()=>{};const t=new u.H9({name:U.hc,readOnly:!0,skipUrlSync:!0,datasource:null,hide:s.VariableHide.hideVariable,layout:"combobox",applyMode:"manual",allowCustomValue:!0});return e.setState({variables:[...e.state.variables,t]}),this.setOtherMetricFilters(this.state.sectionValues),()=>{e.setState({variables:[...e.state.variables.filter(e=>e!==t)]})}}static getSectionValuesFromUrl(){const e=new URLSearchParams(window.location.search),t=new Map;for(const n of or){const r=e.get(n);t.set(n,r?r.split(",").map(e=>e.trim()):[])}const n=e.get(`var-${Ht}`);return Boolean(n&&n!==$t)&&t.set("groupby-labels",[n]),t}setActiveSection(e){const{visibleSection:t,sections:n}=this.state;if(!e||e===(null==t?void 0:t.state.key))return(0,S.z)("metrics_sidebar_toggled",{action:"closed",section:null==t?void 0:t.state.key}),void this.setState({visibleSection:null});var r;(0,S.z)("metrics_sidebar_toggled",{action:"opened",section:e}),"filters-prefix"===e?(0,S.z)("sidebar_prefix_filter_section_clicked",{}):"filters-suffix"===e&&(0,S.z)("sidebar_suffix_filter_section_clicked",{}),this.setState({visibleSection:null!==(r=n.find(t=>t.state.key===e))&&void 0!==r?r:null})}constructor(e){var t,n,r;const i=sr.getSectionValuesFromUrl();super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){ar(e,t,n[t])})}return e}({key:"sidebar",visibleSection:null,sections:[new qn({key:"filters-rule",type:"categories",title:"Rules filters",description:"Filter metrics and recording rules",icon:"rules",computeGroups:Kn,showHideEmpty:!1,showSearch:!1,active:Boolean(null===(t=i.get("filters-rule"))||void 0===t?void 0:t.length)}),new qn({key:"filters-prefix",type:"prefixes",title:"Prefix filters",description:"Filter metrics based on their name prefix (Prometheus namespace)",icon:"A_",computeGroups:zn,active:Boolean(null===(n=i.get("filters-prefix"))||void 0===n?void 0:n.length)}),new qn({key:"filters-suffix",type:"suffixes",title:"Suffix filters",description:"Filter metrics based on their name suffix",icon:"_Z",computeGroups:Un,active:Boolean(null===(r=i.get("filters-suffix"))||void 0===r?void 0:r.length)}),new Jn({key:"groupby-labels",variableName:Ht,title:"Group by labels",description:"Group metrics by their label values",icon:"groups",active:i.has("groupby-labels")}),new Wn.O({key:"bookmarks",title:"Bookmarks",description:"Access your saved metrics for quick reference",icon:"star"}),new er({key:"settings",title:"Settings",description:"Settings",icon:"cog",disabled:!0})],sectionValues:i},e)),i.set("filters-rule",[]),this.addActivationHandler(this.onActivate.bind(this))}}function lr(e){return{container:(0,o.css)({position:"relative",display:"flex",flexDirection:"row",height:"100%",overflow:"hidden"}),buttonsBar:(0,o.css)({display:"flex",flexDirection:"column",alignItems:"center",gap:0,width:"42px",padding:0,margin:0,boxSizing:"border-box",border:`1px solid ${e.colors.border.weak}`,borderRadius:e.shape.radius.default,backgroundColor:e.colors.background.primary,borderTopLeftRadius:0,borderBottomLeftRadius:0,position:"relative"}),buttonContainer:(0,o.css)({marginTop:e.spacing(1),"&::before":{transition:"0.5s ease",content:'""',position:"absolute",left:0,height:"32px",borderLeft:`2px solid ${e.colors.action.selectedBorder}`,boxSizing:"border-box",opacity:0,visibility:"hidden"},"&:hover::before":{opacity:1,visibility:"visible"},"&.visible::before":{opacity:1,visibility:"visible"},"&.disabled::before":{opacity:0,visibility:"hidden"},"&.active::after":{content:'""',position:"absolute",right:0,width:"8px",height:"8px",backgroundColor:e.colors.action.selectedBorder,borderRadius:"50%",margin:"2px 4px 0 0"}}),content:(0,o.css)({width:"calc(300px - 42px)",boxSizing:"border-box",border:`1px solid ${e.colors.border.weak}`,borderLeft:"none",borderRadius:e.shape.radius.default,backgroundColor:e.colors.background.canvas,padding:e.spacing(1.5)}),closeButton:(0,o.css)({position:"absolute",top:e.spacing(1.5),right:e.spacing(1),margin:0})}}function cr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}ar(sr,"Component",({model:e})=>{const t=(0,d.useStyles2)(lr),{sections:n,visibleSection:r,sectionValues:i}=e.useState();return m().createElement("div",{className:t.container},m().createElement("div",{className:t.buttonsBar,"data-testid":"sidebar-buttons"},n.map(n=>{var a,s;const{key:l,title:c,icon:u,disabled:d,active:p}=n.state,h=(null==r?void 0:r.state.key)===l,f=(null===(a=i.get(l))||void 0===a?void 0:a.length)?`${c}: ${null===(s=i.get(l))||void 0===s?void 0:s.join(", ")}`:c;return m().createElement("div",{key:l,className:(0,o.cx)(t.buttonContainer,h&&"visible",p&&"active",d&&"disabled")},m().createElement(rr,{ariaLabel:c,disabled:d,visible:h,active:p,tooltip:f,onClick:()=>e.setActiveSection(l),iconOrText:u}))})),r&&m().createElement("div",{className:t.content,"data-testid":"sidebar-content"},m().createElement(d.IconButton,{className:t.closeButton,name:"times","aria-label":"Close",tooltip:"Close",tooltipPlacement:"top",onClick:()=>e.setActiveSection("")}),r instanceof qn&&m().createElement(r.Component,{model:r}),r instanceof Jn&&m().createElement(r.Component,{model:r}),r instanceof Wn.O&&m().createElement(r.Component,{model:r}),r instanceof er&&m().createElement(r.Component,{model:r})))});class ur extends u.Bs{onActivate(){const e=u.jh.lookupVariable(Ht,this).state.value;this.updateBasedOnGroupBy(e),this.subscribeToEvents()}updateBasedOnGroupBy(e){const t=Boolean(e&&e!==$t);u.jh.findByKeyAndType(this,"quick-search",fn).toggleCountsDisplay(!t),this.setState({body:t?new an({labelName:e}):new Er({variableName:sn})})}subscribeToEvents(){this.initVariablesFilteringAndSorting(),this._subs.add(this.subscribeToEvent(U.OO,e=>{void 0!==e.payload&&function(e){try{const t=St(),n=Date.now(),r=t.filter(t=>t.name!==e);r.unshift({name:e,timestamp:n});const i=r.slice(0,6);f.x.setItem(h.V.RECENT_METRICS,i)}catch(t){const n=t instanceof Error?t:new Error(String(t));Ce.v.error(n,wt(vt({},n.cause||{}),{metricName:e}))}}(e.payload)}))}initVariablesFilteringAndSorting(){this._subs.add(this.subscribeToEvent(Kt,e=>{const{key:t}=e.payload,n=u.jh.findByKey(this,t);this.state.enginesMap.set(t,{filterEngine:new On(n),sortEngine:new _n(n)})})),this._subs.add(this.subscribeToEvent(Wt,e=>{this.state.enginesMap.delete(e.payload.key)}));const e=u.jh.findByKeyAndType(this,"quick-search",fn),t=u.jh.findAllObjects(this,e=>e instanceof qn),n=u.jh.findByKeyAndType(this,"metrics-sorter",xt).state.$variables.getByName(Et);this._subs.add(this.subscribeToEvent(Qt,r=>{const{key:i,options:a}=r.payload,{filterEngine:o,sortEngine:s}=this.state.enginesMap.get(i);o.setInitOptions(a);const l={names:e.state.value?[e.state.value]:[]};for(const e of t)l[e.state.type]=e.state.selectedGroups.map(e=>e.value);o.applyFilters(l,{forceUpdate:!0,notify:!1}),s.sort(n.state.value)})),this._subs.add(this.subscribeToEvent(mn,e=>{const{searchText:t}=e.payload;for(const[,{filterEngine:e,sortEngine:r}]of this.state.enginesMap)e.applyFilters({names:t?[t]:[]}),r.sort(n.state.value)})),this._subs.add(this.subscribeToEvent(Tn,e=>{const{type:t,filters:r}=e.payload;for(const[,{filterEngine:e,sortEngine:i}]of this.state.enginesMap)e.applyFilters({[t]:r}),i.sort(n.state.value)})),this._subs.add(this.subscribeToEvent(tt,e=>{const{sortBy:t}=e.payload;(0,S.z)("sorting_changed",{from:"metrics-reducer",sortBy:t});for(const[,{sortEngine:e}]of this.state.enginesMap)e.sort(t)}))}constructor(){super({$variables:new u.Pj({variables:[new ln,new zt]}),listControls:new vn({}),sidebar:new sr({}),body:new Er({variableName:sn}),enginesMap:new Map}),cr(this,"_variableDependency",new u.Sh(this,{variableNames:[Ht],onReferencedVariableValueChanged:e=>{this.updateBasedOnGroupBy(e.state.value)}})),function(e){try{for(const t of e)(0,u.pY)({dataSource:t})}catch(e){const{message:t}=e;/A runtime data source with uid (.+) has already been registered/.test(t)||(0,O.jx)(e,["Fail to register all the runtime data sources!","The application cannot work as expected, please try reloading the page or if the problem persists, contact your organization admin."])}}([new Ft,new Zt]),this.addActivationHandler(this.onActivate.bind(this))}}cr(ur,"Component",({model:e})=>{var t;const n=null!==(t=(0,c.useChromeHeaderHeight)())&&void 0!==t?t:0,r=(0,d.useStyles2)(pr,n),{$variables:i,body:a,listControls:o,sidebar:s}=e.useState();return m().createElement(m().Fragment,null,m().createElement("div",{className:r.listControls,"data-testid":"list-controls"},m().createElement(o.Component,{model:o})),m().createElement("div",{className:r.body},m().createElement("div",{className:r.sidebar,"data-testid":"sidebar"},m().createElement(s.Component,{model:s})),m().createElement("div",{className:r.list},m().createElement(a.Component,{model:a}))),m().createElement("div",{className:r.variables},null==i?void 0:i.state.variables.map(e=>m().createElement(e.Component,{key:e.state.name,model:e}))))});const dr=144;function pr(e,t){return{listControls:(0,o.css)({marginBottom:e.spacing(1.5)}),body:(0,o.css)({display:"flex",flexDirection:"row",gap:e.spacing(1),height:`calc(100vh - ${t+dr}px)`}),list:(0,o.css)({width:"100%",overflowY:"auto"}),sidebar:(0,o.css)({flex:"0 0 auto",overflowY:"auto"}),variables:(0,o.css)({display:"none"})}}function mr({usageType:e,usageCount:t,singularUsageType:n,pluralUsageType:r,icon:i,dashboardItems:a}){const s=(0,d.useStyles2)(hr);return m().createElement("div",{className:s.usageContainer,"data-testid":"usage-data-panel"},"dashboard-usage"===e?m().createElement(m().Fragment,null,m().createElement(d.Dropdown,{placement:"right-start",overlay:m().createElement(d.Menu,{style:{maxWidth:"240px",maxHeight:"245px",overflowY:"auto"}},a.map(e=>m().createElement(d.Menu.Item,{key:e.id,label:"",url:e.url,target:"_blank",className:s.menuItem,component:()=>m().createElement(d.Tooltip,{content:`Used ${e.count} ${1===e.count?"time":"times"} in ${e.label}`,placement:"right"},m().createElement("div",{className:s.menuItemContent},m().createElement(d.Icon,{name:"external-link-alt"})," ",e.label," (",e.count,")"))})))},m().createElement(d.Button,{variant:"secondary",size:"sm",tooltip:`Metric used ${t} ${1===t?"time":"times"} in dashboard queries. Click to view the dashboards.`,className:(0,o.cx)(s.usageItem,s.clickableUsageItem)},m().createElement("span",{"data-testid":e},m().createElement(d.Icon,{name:i,style:{marginRight:"4px"}})," ",t)))):m().createElement(d.Tooltip,{content:`Metric is used in ${t} ${1===t?n:r}`,placement:"top"},m().createElement("span",{className:s.usageItem,"data-testid":e},m().createElement(d.Icon,{name:i})," ",t)))}function hr(e){return{usageContainer:(0,o.css)({display:"flex",flexDirection:"row",justifyContent:"flex-start",gap:"17px",padding:"8px 12px",border:`1px solid ${e.colors.border.weak}`,borderTopWidth:0,backgroundColor:e.colors.background.primary,alignItems:"center"}),usageItem:(0,o.css)({display:"flex",alignItems:"center",gap:"4px",color:e.colors.text.secondary,opacity:"65%"}),clickableUsageItem:(0,o.css)({backgroundColor:"transparent",border:"none"}),menuItem:(0,o.css)({color:e.colors.text.primary,textDecoration:"none","&:hover":{color:e.colors.text.link}}),menuItemContent:(0,o.css)({overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",color:e.colors.text.primary,"&:hover":{color:e.colors.text.link}})}}function fr(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function gr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function br(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const yr="220px",vr="260px";class wr extends u.Bs{_onActivate(){let e;try{e=u.jh.getAncestor(this,ur)}catch(e){return}if(!e.state.enginesMap.get(sn))return;const t=u.jh.findByKeyAndType(this,"metrics-sorter",xt),n=u.jh.getVariables(t).getByName(Et);(0,Ze.UG)(n)&&(this.updateSortBy(t,n.getValue()),this._subs.add(n.subscribeToState(({value:e})=>{this.updateSortBy(t,e)})))}updateSortBy(e,t){return(n=function*(){if(this.setState({sortBy:t}),this.updateLayout(t),"default"===t)return;const n=yield e.getUsageDetailsForMetric(this.state.metric,t);switch(t){case"dashboard-usage":if("dashboard-usage"!==n.usageType)return;const{dashboards:e}=n;this.setState({usageCount:n.count,singularUsageType:"dashboard panel query",pluralUsageType:"dashboard panel queries",icon:"apps",dashboardItems:Object.entries(e).map(([e,t])=>({id:t.uid,label:e,count:t.count,url:t.url})).sort((e,t)=>t.count-e.count)});break;case"alerting-usage":this.setState({usageCount:n.count,singularUsageType:"alert rule",pluralUsageType:"alert rules",icon:"bell"})}},function(){var e=this,t=arguments;return new Promise(function(r,i){var a=n.apply(e,t);function o(e){fr(a,r,i,o,s,"next",e)}function s(e){fr(a,r,i,o,s,"throw",e)}o(void 0)})}).call(this);var n}updateLayout(e){const t=u.jh.getAncestor(this,u.gF),n=null==t?void 0:t.state.autoRows,r="default"===e?yr:vr;n!==r&&t.setState({autoRows:r})}constructor(e){super(br(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){gr(e,t,n[t])})}return e}({},e),{sortBy:"default",usageCount:0,singularUsageType:"",pluralUsageType:"",icon:"",dashboardItems:[]})),this.addActivationHandler(this._onActivate.bind(this))}}gr(wr,"Component",({model:e})=>{const{vizPanelInGridItem:t,sortBy:n,usageCount:r,singularUsageType:i,pluralUsageType:a,icon:o,dashboardItems:s}=e.useState();if(t)return m().createElement("div",{"data-testid":"with-usage-data-preview-panel"},m().createElement(t.Component,{model:t}),"default"!==n&&m().createElement(mr,{usageType:n,usageCount:r,singularUsageType:i,pluralUsageType:a,icon:o,dashboardItems:s}));Ce.v.log("no viz panel")});const Sr="repeat(auto-fit, minmax(400px, 1fr))",Or="1fr";class Er extends u.Bs{onActivate(){this.subscribeToLayoutChange()}subscribeToLayoutChange(){const e=u.jh.findByKeyAndType(this,"layout-switcher",We),t=this.state.body.state.body,n=(e,n)=>{e.layout!==(null==n?void 0:n.layout)&&t.setState({templateColumns:e.layout===Ke.ROWS?Or:Sr})};n(e.state),this._subs.add(e.subscribeToState(n))}constructor({variableName:e}){super({key:"metrics-list",variableName:e,body:new Ye({variableName:e,initialPageSize:120,pageSizeIncrement:9,body:new u.gF({children:[],isLazy:!0,templateColumns:Sr,autoRows:yr,$behaviors:[new u.Gg.K2({key:"metricCrosshairSync",sync:fe.yV.Crosshair})]}),getLayoutLoading:()=>new u.dM({reactNode:m().createElement(d.Spinner,{inline:!0})}),getLayoutEmpty:()=>new u.dM({reactNode:m().createElement(Ue._,{title:"",severity:"info"},"No metrics found for the current filters and time range.")}),getLayoutError:e=>new u.dM({reactNode:m().createElement(Ue._,{severity:"error",title:"Error while loading metrics!",error:e})}),getLayoutChild:(e,t)=>{const n=e.value;return new u.xK({body:new wr({metric:e.value,vizPanelInGridItem:new He({metric:n,panelOptions:{fixedColorIndex:t,headerActions:()=>[new K({metric:n}),new y({metric:n})]}})})})}})}),this.addActivationHandler(this.onActivate.bind(this))}}function xr(e){return{container:(0,o.css)({}),footer:(0,o.css)({display:"flex",justifyContent:"center",alignItems:"center",marginTop:e.spacing(4),"& button":{height:"40px",borderRadius:"8px"}})}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Er,"Component",({model:e})=>{const{variableName:t,body:n}=e.useState(),r=(0,d.useStyles2)(xr),i=u.jh.lookupVariable(t,e),{loading:a,error:o}=i.useState(),s=n.useSizes(),l=!a&&!o&&s.total>0&&s.current<s.total;return m().createElement("div",{"data-testid":"metrics-list"},m().createElement("div",{className:r.container},m().createElement(n.Component,{model:n})),l&&m().createElement("div",{className:r.footer},m().createElement(Je,{label:"metric",batchSizes:s,onClick:()=>{n.increaseBatchSize()}})))});class kr extends s.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(kr,"type","apply-panel-config");class Pr extends s.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Pr,"type","cancel-configure-panel");const Cr=[{value:99,label:"P99"},{value:95,label:"P95"},{value:90,label:"P90"},{value:75,label:"P75"},{value:50,label:"P50"}];function jr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function _r(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class Tr extends u.Bs{onActivate(){this.initPercentilesParams()}initPercentilesParams(){var e,t,n;const r=this.state.body.state.queryConfig,i=new Set((null===(n=r.queries)||void 0===n||null===(t=n.find(e=>{var t;return null===(t=e.params)||void 0===t?void 0:t.percentiles}))||void 0===t||null===(e=t.params)||void 0===e?void 0:e.percentiles)||[]),a=i.size>0?Cr.map(e=>_r(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){jr(e,t,n[t])})}return e}({},e),{checked:i.has(e.value)})):[];this.setState({queryParams:{show:a.length>0,options:a}})}constructor({body:e,presetId:t,isSelected:n,onSelect:r}){super({presetId:t,body:e,isSelected:n,onSelect:r,queryParams:{show:!1,options:[]}}),jr(this,"onTogglePercentile",e=>{var t;const{queryParams:n,body:r}=this.state,i=Number(e.target.value),a=n.options.find(e=>e.value===i);if(!a)return;a.checked=!a.checked;const o=n.options.filter(e=>e.checked);if(!o.length)return;const s=(0,w.cloneDeep)(r.state.queryConfig);null===(t=s.queries)||void 0===t||t.some(e=>{var t;return!!(null===(t=e.params)||void 0===t?void 0:t.percentiles)&&(e.params.percentiles=o.map(e=>e.value),!0)}),r.update({},s),this.setState({queryParams:n})}),jr(this,"onClickPreset",()=>{this.state.onSelect(this.state.presetId)}),this.addActivationHandler(this.onActivate.bind(this))}}function Ir(e){return{container:o.css`
      display: flex;
      flex-direction: column;
      gap: ${e.spacing(1)};
      padding: ${e.spacing(1,1,1.25,1)};
      border: 1px solid transparent;
      transition: all 0.2s ease-in-out;

      &:hover {
        border: 1px solid ${e.colors.border.weak};
        border-color: ${e.colors.primary.border};
      }
      &:focus {
        border: 1px solid ${e.colors.border.weak};
        outline: 1px solid ${e.colors.primary.main};
        outline-offset: 1px;
      }
    `,selected:o.css`
      cursor: default;
      border: 1px solid ${e.colors.border.weak};
      border-color: ${e.colors.primary.border};
    `,bodyAndParams:o.css`
      display: flex;
      flex-direction: row;
      gap: ${e.spacing(1.25)};
      width: 100%;
    `,paramsContainer:o.css`
      margin-top: ${e.spacing(1)};
    `,param:o.css`
      display: flex;
      align-items: center;
      gap: ${e.spacing(.5)};
      margin-bottom: ${e.spacing(.5)};
      font-size: 12px;
      cursor: pointer;

      & [type='checkbox'] {
        cursor: pointer;
      }
    `,radioContainer:o.css`
      display: flex;
      align-items: center;
      justify-content: center;

      & [type='radio'] {
        cursor: pointer;
      }
    `}}function Ar(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function Dr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Lr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Dr(e,t,n[t])})}return e}function Nr(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}jr(Tr,"Component",({model:e})=>{const t=(0,d.useStyles2)(Ir),{body:n,isSelected:r,queryParams:i}=e.useState();return m().createElement("div",{className:(0,o.cx)(t.container,r&&t.selected),onClick:r?void 0:e.onClickPreset},m().createElement("div",{className:(0,o.cx)(t.bodyAndParams)},m().createElement(n.Component,{model:n}),i.show&&m().createElement("div",{className:t.paramsContainer},i.options.map(n=>m().createElement("label",{key:n.value,className:(0,o.cx)("param",t.param),htmlFor:`checkbox-${n.value}`},m().createElement("input",{id:`checkbox-${n.value}`,type:"checkbox",value:n.value,checked:n.checked,onChange:e.onTogglePercentile}),m().createElement("span",null,n.label))))),m().createElement("div",{className:t.radioContainer},m().createElement(d.Tooltip,{content:r?"Current configuration":"Click to select this configuration",placement:"top"},m().createElement("input",{type:"radio",name:"select-config",checked:r}))))});class Mr extends u.Bs{onActivate(){this.syncTimeRange(),this.buildBody(),this.subscribeToEvents()}syncTimeRange(){const e=u.jh.getAncestor(this,yo),{from:t,to:n,timeZone:r,value:i}=u.jh.getTimeRange(e).state;u.jh.getTimeRange(this).setState({from:t,to:n,timeZone:r,value:i})}buildBody(){return(e=function*(){const{metric:e}=this.state,t=j(e),n=yield(0,H.kj)(this).isNativeHistogram(e),r=function(e,t){return q(e)?Object.values(A):t||F(e)?Object.values(I):V(e)?[Object.values(M)[0],...Object.values(T)]:$(e)?Object.values(B):Object.values(M)}(e,n),i=(t||r[0]).id,a=new u.gF({templateColumns:Sr,autoRows:_.M+46,isLazy:!0,$behaviors:[new u.Gg.K2({key:"metricCrosshairSync",sync:s.DashboardCursorSync.Crosshair})],children:r.map((n,r)=>new u.xK({body:new Tr({presetId:n.id,isSelected:i===n.id,onSelect:e=>this.onSelectPreset(e),body:new He({key:`panel-${n.id}`,discardUserPrefs:n.id!==(null==t?void 0:t.id),metric:e,panelOptions:Nr(Lr({},n.panelOptions),{title:n.name,fixedColorIndex:r,headerActions:()=>[]}),queryOptions:n.queryOptions})})}))});this.setState({presets:r,selectedPresetId:i,body:a})},function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){Ar(a,r,i,o,s,"next",e)}function s(e){Ar(a,r,i,o,s,"throw",e)}o(void 0)})}).call(this);var e}subscribeToEvents(){const{metric:e}=this.state;this._subs.add(this.subscribeToEvent(kr,t=>{const{config:n,restoreDefault:r}=t.payload,i=f.x.getItem(h.V.METRIC_PREFS)||{},a=i[e];r&&a?delete i[e].config:i[e]=Nr(Lr({},a),{config:n}),f.x.setItem(h.V.METRIC_PREFS,i)}))}static getPanelConfigFromPreset(e){return(0,w.omit)(e,["name","panelOptions.description"])}constructor({metric:e}){super({metric:e,$timeRange:new u.JZ({}),controls:[new u.KE({}),new u.WM({})],isConfirmModalOpen:!1,presets:[],selectedPresetId:void 0,body:void 0}),Dr(this,"onSelectPreset",e=>{for(const t of u.jh.findDescendents(this,Tr))t.setState({isSelected:t.state.presetId===e});this.setState({selectedPresetId:e})}),Dr(this,"onClickRestoreDefault",()=>{this.setState({isConfirmModalOpen:!0})}),Dr(this,"onClickConfirmRestoreDefault",()=>{const{metric:e,presets:t}=this.state,[n]=t;n?(this.publishEvent(new kr({metric:e,config:Mr.getPanelConfigFromPreset(n),restoreDefault:!0}),!0),this.closeConfirmModal()):(0,O.jx)(new Error(`No default config found for metric ${e}!`),["Cannot restore default configuration."])}),Dr(this,"closeConfirmModal",()=>{this.setState({isConfirmModalOpen:!1})}),Dr(this,"onClickCancel",()=>{this.publishEvent(new Pr({metric:this.state.metric}),!0)}),Dr(this,"onClickApplyConfig",()=>{const{metric:e,presets:t,selectedPresetId:n}=this.state,r=u.jh.findByKeyAndType(this,`panel-${n}`,He);if(!r)throw new Error(`Panel not found for preset id="${n}"!`);const i=t.find(e=>e.id===n);if(!i)throw new Error(`Preset with id="${n}" not found!`);const a=(0,w.cloneDeep)(i);a.queryOptions.queries=r.state.queryConfig.queries,this.publishEvent(new kr({metric:e,config:Mr.getPanelConfigFromPreset(a)}),!0)}),this.addActivationHandler(this.onActivate.bind(this))}}function Br(e){return{controlsContainer:o.css`
      display: flex;
      justify-content: flex-end;
      gap: ${e.spacing(1)};
      margin-bottom: ${e.spacing(2)};
    `,messageContainer:o.css`
      margin: ${e.spacing(2.5,0,1,0)};
    `,controls:o.css`
      display: flex;
    `,formButtonsContainer:o.css`
      display: flex;
      justify-content: center;
      gap: ${e.spacing(2)};
      position: sticky;
      bottom: 0;
      background: ${e.colors.background.primary};
      padding: ${e.spacing(2,0)};
      border-top: 1px solid ${e.colors.border.weak};
    `}}function Rr(e){return q(e)?"status-updown":F(e)?"histogram":V(e)?"age":$(e)?"counter":"gauge"}Dr(Mr,"Component",({model:e})=>{const t=(0,d.useStyles2)(Br),{metric:n,body:r,controls:i,isConfirmModalOpen:a}=e.useState();return m().createElement("div",null,m().createElement("div",{className:t.controlsContainer},m().createElement(d.Button,{variant:"secondary",size:"md",onClick:e.onClickRestoreDefault},"Restore default config"),m().createElement("div",{className:t.controls},i.map(e=>m().createElement(e.Component,{key:e.state.key,model:e})))),m().createElement("div",{className:t.messageContainer},m().createElement("p",null,"Select a Prometheus function that will be used by default to display the ",n," metric.")),r&&m().createElement(r.Component,{model:r}),m().createElement("div",{className:t.formButtonsContainer},m().createElement(d.Button,{variant:"primary",size:"md",onClick:e.onClickApplyConfig},"Apply"),m().createElement(d.Button,{variant:"secondary",size:"md",onClick:e.onClickCancel},"Cancel")),m().createElement(d.ConfirmModal,{isOpen:a,title:"Restore default configuration",body:`Are you sure you want to restore the default configuration for the ${n} metric?`,confirmText:"Restore",onConfirm:e.onClickConfirmRestoreDefault,onDismiss:e.closeConfirmModal}))});const $r=(0,p.memo)(function({size:e}){const t=(0,d.useStyles2)(Fr);return m().createElement("img",{className:(0,o.cx)(t.logo,e),src:"public/plugins/grafana-metricsdrilldown-app/img/logo.svg"})}),Fr=()=>({logo:o.css`
    &.small {
      width: 24px;
      height: 24px;
      margin-right: 4px;
      position: relative;
      top: -2px;
    }

    &.large {
      width: 40px;
      height: 40px;
    }
  `});const Vr=n(5176).t,qr=`https://github.com/grafana/metrics-drilldown/commit/${Vr}`,{buildInfo:Hr}=c.config;function zr(){const e=(0,d.useStyles2)(Kr),{meta:{info:{version:t,updated:n}}}=(0,s.usePluginContext)()||{meta:{info:{version:"?.?.?",updated:"?"}}};return m().createElement("div",{className:e.menuHeader},m().createElement("h5",null,m().createElement($r,{size:"small"}),"Grafana Metrics Drilldown v",t),m().createElement("div",{className:e.subTitle},"Last update: ",n))}function Ur(){const e="dev"===Vr,t=e?Vr:Vr.slice(0,8);return m().createElement(d.Menu,{header:m().createElement(zr,null)},m().createElement(d.Menu.Item,{label:`Commit SHA: ${t}`,icon:"github",onClick:()=>window.open(qr),disabled:e}),m().createElement(d.Menu.Item,{label:"Changelog",icon:"list-ul",onClick:()=>window.open("https://github.com/grafana/metrics-drilldown/blob/main/CHANGELOG.md","_blank","noopener,noreferrer")}),m().createElement(d.Menu.Item,{label:"Contribute",icon:"external-link-alt",onClick:()=>window.open("https://github.com/grafana/metrics-drilldown/blob/main/docs/contributing.md","_blank","noopener,noreferrer")}),m().createElement(d.Menu.Item,{label:"Documentation",icon:"document-info",onClick:()=>window.open("https://grafana.com/docs/grafana/latest/explore/simplified-exploration/metrics","_blank","noopener,noreferrer")}),m().createElement(d.Menu.Item,{label:"Report an issue",icon:"bug",onClick:()=>window.open("https://github.com/grafana/metrics-drilldown/issues/new?template=bug_report.md","_blank","noopener,noreferrer")}),m().createElement(d.Menu.Divider,null),m().createElement(d.Menu.Item,{label:`Grafana ${Hr.edition} v${Hr.version} (${Hr.env})`,icon:"grafana",onClick:()=>window.open(`https://github.com/grafana/grafana/commit/${Hr.commit}`,"_blank","noopener,noreferrer")}))}function Gr(){return m().createElement(d.Dropdown,{overlay:()=>m().createElement(Ur,null),placement:"bottom-end"},m().createElement(d.Button,{icon:"info-circle",variant:"secondary",tooltip:"Plugin info",tooltipPlacement:"top",title:"Plugin info","data-testid":"plugin-info-button"}))}const Kr=e=>({button:o.css`
    position: relative;
    display: flex;
    align-items: center;
    width: 32px;
    height: 32px;
    line-height: 30px;
    border: 1px solid ${e.colors.border.weak};
    border-radius: 2px;
    border-left: 0;
    color: ${e.colors.text.primary};
    background: ${e.colors.background.secondary};

    &:hover {
      border-color: ${e.colors.border.medium};
      background-color: ${e.colors.background.canvas};
    }
  `,menuHeader:o.css`
    padding: ${e.spacing(.5,1)};
    white-space: nowrap;
  `,subTitle:o.css`
    color: ${e.colors.text.secondary};
    font-size: ${e.typography.bodySmall.fontSize};
  `});function Wr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Qr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Wr(e,t,n[t])})}return e}function Yr(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}class Xr extends u.Bs{constructor(e){super(Qr({key:"drawer",isOpen:!1},e)),Wr(this,"open",({title:e,subTitle:t,body:n})=>{this.setState(Yr(Qr({},this.state),{isOpen:!0,title:e,subTitle:t,body:n}))}),Wr(this,"close",()=>{this.setState({isOpen:!1})})}}Wr(Xr,"Component",({model:e})=>{const{isOpen:t,title:n,subTitle:r,body:i}=e.useState();return m().createElement(m().Fragment,null,i&&t&&m().createElement(d.Drawer,{size:"lg",title:n,subtitle:r,closeOnMaskClick:!0,onClose:e.close},m().createElement(i.Component,{model:i})))});class Jr extends u.fS{onActivate(){this.subscribeToState((e,t)=>{e.value&&e.value!==t.value?(0,S.z)("groupby_label_changed",{label:String(e.value)}):e.options!==t.options&&e.options.find(e=>"le"===e.value)&&this.setState({options:e.options.filter(e=>"le"!==e.value)})});const e=u.jh.lookupVariable(U.Ao,this);(0,Ze.BE)(e)&&e.subscribeToState((e,t)=>{e.filterExpression!==t.filterExpression&&this.changeValueTo("$__all")})}constructor(){super({name:U.yr,label:"Group by",datasource:U.GH,includeAll:!0,defaultToAll:!0,query:`label_names(${U.Rp})`,value:"",text:""}),this.addActivationHandler(this.onActivate.bind(this))}}function Zr(e){return{select:o.css`
      width: ${e.spacing(16)};
      & > div {
        width: 100%;
      }
    `}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Jr,"Component",({model:e})=>{const t=(0,d.useStyles2)(Zr);return m().createElement("div",{className:t.select,"data-testid":"breakdown-label-selector"},m().createElement(u.fS.Component,{model:e}))});const ei={OPEN_EXPLORE_LABEL:"Open in explore",COPY_URL_LABEL:"Copy url",BOOKMARK_LABEL:"Bookmark",SELECT_NEW_METRIC_TOOLTIP:"Remove existing metric and choose a new metric"};var ti=n(7597);function ni(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class ri extends u.Bs{onActivate(){const e=u.jh.getAncestor(this,He);this.setState({currentPanelType:e.state.panelConfig.type}),this._subs.add(e.subscribeToState((e,t)=>{e.panelConfig.type!==t.panelConfig.type&&this.setState({currentPanelType:e.panelConfig.type})}))}constructor({metric:e}){super({metric:e,options:[{value:"percentiles",label:"percentiles"},{value:"heatmap",label:"heatmap"}],currentPanelType:void 0}),ni(this,"onChange",e=>{(0,S.z)("histogram_panel_type_changed",{panelType:e}),this.publishEvent(new z({panelType:e}),!0)}),this.addActivationHandler(this.onActivate.bind(this))}}ni(ri,"Component",({model:e})=>{const{options:t,currentPanelType:n}=e.useState();return t.length?m().createElement(d.RadioButtonGroup,{size:"sm",options:t,value:n,onChange:e.onChange}):null});const ii=n.p+"ac01ecbc64128d2f3e68.svg";var ai=n(2533);function oi(e){var t,n;if(!e)return;const r=null!==(n=e.state.$data)&&void 0!==n?n:null===(t=e.parent)||void 0===t?void 0:t.state.$data;return si(r)?r:null!=(i=r)&&"state"in i&&"transformations"in i.state?oi(r):void 0;var i}function si(e){return null!=e&&"state"in e&&"runQueries"in e}function li(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ci(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){li(e,t,n[t])})}return e}function ui(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const di=`${ai.id}/investigation/v1`;class pi extends u.Bs{getPanelConfigAndDataFrames(){var e;const t=(0,H.UX)(this,e=>e instanceof u.Eb,u.Eb),n=u.jh.getData(this);return{fieldConfig:null==t?void 0:t.state.fieldConfig,frames:null==n||null===(e=n.state.data)||void 0===e?void 0:e.series}}constructor(e){super(ui(ci({},e),{queries:[]})),li(this,"_onActivate",()=>{this._subs.add(this.subscribeToState(()=>{this.getQueries(),this.getContext()}));const e=u.jh.interpolate(this,U.gR);this.setState({dsUid:e})}),li(this,"getQueries",()=>{const e=u.jh.getData(this),t=u.jh.findObject(e,si);if(si(t)){const e=this.state.frame?mi(this.state.frame):null,n=t.state.queries.map(n=>ui(ci({},n),{expr:u.jh.interpolate(t,n.expr),legendFormat:(null==e?void 0:e.name)?`{{ ${e.name} }}`:u.jh.interpolate(t,n.legendFormat)}));JSON.stringify(n)!==JSON.stringify(this.state.queries)&&this.setState({queries:n})}}),li(this,"updateFieldConfigOverrides",()=>{const{fieldConfig:e,frames:t}=this.getPanelConfigAndDataFrames();if(e&&(null==t?void 0:t.length)){for(const i of t)for(const t of i.fields){const i=Object.keys(t.config).map(e=>({id:e,value:t.config[e]})),a=e.overrides.find(e=>{var n,r;return e.matcher.options===(null!==(r=null!==(n=t.config.displayNameFromDS)&&void 0!==n?n:t.config.displayName)&&void 0!==r?r:t.name)&&"byName"===e.matcher.id});var n,r;if(!a)e.overrides.unshift({matcher:{id:"byName",options:null!==(r=null!==(n=t.config.displayNameFromDS)&&void 0!==n?n:t.config.displayName)&&void 0!==r?r:t.name},properties:i});a&&JSON.stringify(a.properties)!==JSON.stringify(i)&&(a.properties=i)}return e}}),li(this,"getContext",()=>{const e=this.updateFieldConfigOverrides(),{queries:t,dsUid:n,labelName:r,fieldName:i}=this.state,a=u.jh.getTimeRange(this);if(!a||!t||!n)return;const o={origin:"Metrics Drilldown",type:"timeseries",queries:t,timeRange:ci({},a.state.value),datasource:{uid:n},url:window.location.href,id:`${JSON.stringify(t)}${r}${i}`,title:r+(i?` > ${i}`:""),logoPath:ii,drillDownLabel:i,fieldConfig:e};JSON.stringify(o)!==JSON.stringify(this.state.context)&&this.setState({context:o})}),this.addActivationHandler(this._onActivate.bind(this))}}li(pi,"Component",({model:e})=>{const{context:t}=e.useState(),{links:n}=(0,c.usePluginLinks)({extensionPointId:di,context:t,limitPerPlugin:1}),r=n.find(e=>"grafana-investigations-app"===e.pluginId);return r?m().createElement(d.IconButton,{tooltip:r.description,"aria-label":"add panel to exploration",key:r.id,name:null!==(i=r.icon)&&void 0!==i?i:"panel-add",onClick:e=>{r.onClick&&r.onClick(e)}}):null;var i});const mi=e=>{var t,n;const r=null!==(n=null===(t=e.fields[1])||void 0===t?void 0:t.labels)&&void 0!==n?n:{},i=Object.keys(r);if(1!==i.length)return;const a=i[0];return{name:a,value:r[a]}};function hi(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function fi(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){hi(a,r,i,o,s,"next",e)}function s(e){hi(a,r,i,o,s,"throw",e)}o(void 0)})}}function gi(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function bi(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){gi(e,t,n[t])})}return e}function yi(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const vi="Add to investigation",wi="investigations_divider",Si="Investigations";class Oi extends u.Bs{addItem(e){this.state.body&&this.state.body.addItem(e)}setItems(e){this.state.body&&this.state.body.setItems(e)}constructor(e){var t,n;super(yi(bi({},e),{addExplorationsLink:null===(n=e.addExplorationsLink)||void 0===n||n})),(t=this).addActivationHandler(()=>{let e;try{const r=u.jh.getAncestor(t,u.Eb),i=u.jh.getData(r).state.data;if(!i)throw new Error("Cannot get link to explore, no panel data found");const a=oi(r);var n;(null!==(n=null==a?void 0:a.state.queries)&&void 0!==n?n:[]).forEach(e=>{delete e.legendFormat}),e=(0,u.pN)(i,t,i.timeRange,e=>"expr"in e&&"string"==typeof e.expr&&e.expr.includes("__ignore_usage__")?yi(bi({},e),{expr:e.expr.replace(/,?__ignore_usage__="",?/,"")}):e)}catch(e){}const r=[{text:"Navigation",type:"group"},{text:"Explore",iconClassName:"compass",onClick:()=>null==e?void 0:e.then(e=>e&&window.open(e,"_blank")),shortcut:"p x"}];t.setState({body:new u.Lw({items:r})});const i=new pi({labelName:t.state.labelName,fieldName:t.state.fieldName,frame:t.state.frame});var a;(t._subs.add(null==i?void 0:i.subscribeToState(()=>fi(function*(){var e;yield(e=t,fi(function*(){const t=e.state.explorationsButton;if(t){var n;const l=yield Ei(t);var r;const c=null!==(r=null===(n=e.state.body)||void 0===n?void 0:n.state.items)&&void 0!==r?r:[],u=c.find(e=>e.text===vi);var i,a,o,s;l&&(u?u&&(null===(i=e.state.body)||void 0===i||i.setItems(c.filter(e=>!1===[wi,Si,vi].includes(e.text)))):(null===(a=e.state.body)||void 0===a||a.addItem({text:wi,type:"divider"}),null===(o=e.state.body)||void 0===o||o.addItem({text:Si,type:"group"}),null===(s=e.state.body)||void 0===s||s.addItem({text:vi,iconClassName:"plus-square",onClick:e=>l.onClick&&l.onClick(e)})))}})())})())),t.setState({explorationsButton:i}),t.state.addExplorationsLink)&&(null===(a=t.state.explorationsButton)||void 0===a||a.activate())})}}gi(Oi,"Component",({model:e})=>{const{body:t}=e.useState();return t?m().createElement(t.Component,{model:t}):m().createElement(m().Fragment,null)});const Ei=e=>fi(function*(){const t=e.state.context;if(c.config.buildInfo.version.startsWith("11."))try{const e=(yield Promise.resolve().then(n.t.bind(n,8531,23))).getPluginLinkExtensions;if(void 0!==e){return e({extensionPointId:di,context:t}).extensions[0]}}catch(e){Ce.v.error(e,{message:"Error importing getPluginLinkExtensions"})}if("function"==typeof c.getObservablePluginLinks){return(yield(0,ve.firstValueFrom)((0,c.getObservablePluginLinks)({extensionPointId:di,context:t})))[0]}})();function xi(e,t){return(null==t?void 0:t.state.embedded)||e.isLight?e.colors.background.primary:e.colors.background.canvas}function ki(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}const Pi=_.XL,Ci="topview";class ji extends u.Bs{onActivate(){return(e=function*(){const{metric:e,topView:t}=this.state,n=(0,H.kj)(this),r=function(e){if(!e)return;const{type:t,help:n,unit:r}=e;return[n,t&&`**Type:** *${t}*`,r&&`**Unit:** ${r}`].join("\n\n")}(yield n.getMetadataForMetric(e)),i=F(e)||(yield n.isNativeHistogram(e));t.setState({children:[new u.vA({key:Ci,minHeight:Pi,maxHeight:"40%",body:new He({metric:e,panelOptions:{height:_.XL,description:r,headerActions:i?()=>[new ri({metric:e}),new y({metric:e})]:()=>[new y({metric:e})],menu:new Oi({labelName:e})},queryOptions:{resolution:W.HIGH}})}),new u.vA({ySizing:"content",body:new Na({})})]})},function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){ki(a,r,i,o,s,"next",e)}function s(e){ki(a,r,i,o,s,"throw",e)}o(void 0)})}).call(this);var e}constructor(e){super({metric:e.metric,topView:new u.G1({direction:"column",$behaviors:[new u.Gg.K2({key:"metricCrosshairSync",sync:s.DashboardCursorSync.Crosshair})],children:[new u.vA({minHeight:Pi,maxHeight:"40%",body:new u.dM({reactNode:m().createElement("div",null)})})]}),selectedTab:void 0}),this.addActivationHandler(()=>{this.onActivate()})}}function _i(e,t,n){return{container:(0,o.css)({display:"flex",flexDirection:"column",position:"relative",flexGrow:1}),topView:(0,o.css)({}),sticky:(0,o.css)({display:"flex",flexDirection:"row",background:xi(e,n),position:"sticky",paddingTop:e.spacing(1),marginTop:`-${e.spacing(1)}`,zIndex:10,top:`calc(var(--app-controls-height, 0px) + ${t}px)`}),nonSticky:(0,o.css)({display:"flex",flexDirection:"row"})}}function Ti(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ii(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(ji,"Component",({model:e})=>{const{topView:t,selectedTab:n}=e.useState(),{stickyMainGraph:r}=(0,H.KE)(e).useState(),i=(0,c.useChromeHeaderHeight)(),a=(0,H.kj)(e),s=(0,d.useStyles2)(_i,a.state.embedded?0:null!=i?i:0,a);return m().createElement("div",{className:s.container},m().createElement("div",{className:r?(0,o.cx)(s.topView,s.sticky):(0,o.cx)(s.topView,s.nonSticky),"data-testid":"top-view"},m().createElement(t.Component,{model:t})),n&&m().createElement("div",{"data-testid":"tab-content"},m().createElement(n.Component,{model:n})))});const Ai={label:"All metric names",value:"all"};class Di extends u.Bs{getUrlState(){return{metricPrefix:this.state.value}}updateFromUrl(e){"string"!=typeof e.metricPrefix?this.setState({value:Ai.value}):this.state.value!==e.metricPrefix&&this.setState({value:e.metricPrefix})}onActivate(){this.parseMetricPrefixes()}parseMetricPrefixes(){if(this._variableDependency.hasDependencyInLoadingState())return void this.setState({error:void 0,loading:!0});const e=u.jh.lookupVariable(Tt,this);if(e.state.error)return void this.setState({error:e.state.error,loading:!1,options:[]});const t=zn(Xe(e)),n=[Ai,...t.map(e=>({value:e.value,label:`${e.label} (${e.count})`}))],{value:r}=this.state,i=n.find(e=>e.value===r)?r:Ai.value;this.setState({error:null,loading:!1,options:n}),this.selectOption({value:i,label:i})}constructor(e){super(Ii(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Ti(e,t,n[t])})}return e}({},e),{key:"related-prefix-filter",loading:!0,error:null,options:[Ai],value:Ai.value})),Ti(this,"_variableDependency",new u.Sh(this,{variableNames:[Tt],onVariableUpdateCompleted:()=>this.parseMetricPrefixes()})),Ti(this,"_urlSync",new u.So(this,{keys:["metricPrefix"]})),Ti(this,"selectOption",e=>{const t=null===e?Ai.value:e.value;this.setState({value:t}),this.publishEvent(new Tn({type:"prefixes",filters:t===Ai.value?[]:[t]}),!0)}),this.addActivationHandler(this.onActivate.bind(this))}}function Li(e){return{container:o.css`
      display: flex;

      & > div {
        margin: 0;
      }
    `,label:o.css`
      margin-right: 0;
      background-color: ${e.colors.background.primary};
      border: 1px solid ${e.colors.border.medium};
      border-right: 0 none;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
    `,tooltipIcon:o.css`
      margin-left: ${e.spacing(.5)};
    `}}function Ni(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Mi(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}Ti(Di,"Component",({model:e})=>{const t=(0,d.useStyles2)(Li),{loading:n,options:r,value:i,error:a}=e.useState();return m().createElement("div",{className:t.container,"data-testid":"prefix-filter-selector"},m().createElement(d.InlineField,{disabled:n,error:a&&a.toString(),label:m().createElement(d.InlineLabel,{width:"auto",className:t.label},m().createElement("span",null,"View by"),m().createElement(d.Tooltip,{content:"View by the metric prefix. A metric prefix is a single word at the beginning of the metric name, relevant to the domain the metric belongs to.",placement:"top"},m().createElement(d.Icon,{className:t.tooltipIcon,name:"info-circle",size:"sm"})))},m().createElement(d.Combobox,{value:i,onChange:e.selectOption,options:r})))});class Bi extends u.P1{constructor(e){super(Mi(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Ni(e,t,n[t])})}return e}({},e),{key:"related-list-controls",body:new u.G1({direction:"row",width:"100%",maxHeight:"32px",children:[new u.vA({width:"auto",body:new Di({})}),new u.vA({body:new fn({urlSearchParamName:"gmd-relatedSearchText",targetName:"related metric",countsProvider:new pn,displayCounts:!0})}),new u.vA({width:"auto",body:new We({})})]})}))}}function Ri(){return{headerWrapper:(0,o.css)({display:"flex",alignItems:"center","& > div":{display:"flex",alignItems:"center","& > div":{display:"flex",alignItems:"center"}}})}}Ni(Bi,"Component",({model:e})=>{const t=(0,d.useStyles2)(Ri),{body:n}=e.useState();return m().createElement("div",{className:t.headerWrapper,"data-testid":"related-list-controls"},m().createElement(n.Component,{model:n}))});class $i extends u.Bs{onActivate(){this.subscribeToEvents()}subscribeToEvents(){this.initVariablesFilteringAndSorting()}initVariablesFilteringAndSorting(){const{metric:e}=this.state,t=new Map;this._subs.add(this.subscribeToEvent(Kt,e=>{const{key:n}=e.payload,r=u.jh.findByKey(this,n);t.set(n,{filterEngine:new On(r),sortEngine:new _n(r)})})),this._subs.add(this.subscribeToEvent(Wt,e=>{t.delete(e.payload.key)}));const n=u.jh.findByKeyAndType(this,"quick-search",fn);this._subs.add(this.subscribeToEvent(Qt,r=>{const{key:i,options:a}=r.payload,{filterEngine:o,sortEngine:s}=t.get(i);o.setInitOptions(a);const l={names:n.state.value?[n.state.value]:[]};o.applyFilters(l,{forceUpdate:!0,notify:!1}),s.sort("related",{metric:e})})),this._subs.add(this.subscribeToEvent(mn,n=>{const{searchText:r}=n.payload;for(const[,{filterEngine:n,sortEngine:i}]of t)n.applyFilters({names:r?[r]:[]}),i.sort("related",{metric:e})})),this._subs.add(this.subscribeToEvent(Tn,n=>{const{type:r,filters:i}=n.payload;for(const[,{filterEngine:n,sortEngine:a}]of t)n.applyFilters({[r]:i}),a.sort("related",{metric:e})}))}constructor({metric:e}){super({metric:e,$variables:new u.Pj({variables:[new ln]}),key:"RelatedMetricsScene",body:new Er({variableName:sn}),listControls:new Bi({})}),this.addActivationHandler(this.onActivate.bind(this))}}function Fi(e){return{body:(0,o.css)({}),list:(0,o.css)({}),listControls:(0,o.css)({margin:e.spacing(1,0,1.5,0)}),variables:(0,o.css)({display:"none"})}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}($i,"Component",({model:e})=>{const t=(0,d.useStyles2)(Fi),{$variables:n,body:r,listControls:i}=e.useState();return m().createElement(m().Fragment,null,m().createElement("div",{className:t.listControls},m().createElement(i.Component,{model:i})),m().createElement("div",{className:t.body},m().createElement("div",{className:t.list,"data-testid":"panels-list"},m().createElement(r.Component,{model:r}))),m().createElement("div",{className:t.variables},null==n?void 0:n.state.variables.map(e=>m().createElement(e.Component,{key:e.state.name,model:e}))))});var Vi=n(4137);const qi=ei.COPY_URL_LABEL,Hi=({trail:e})=>{const[t,n]=(0,p.useState)(qi);return m().createElement(d.ToolbarButton,{variant:"canvas",icon:"share-alt",tooltip:t,onClick:()=>{if(navigator.clipboard){(0,S.z)("selected_metric_action_clicked",{action:"share_url"});const t=`${c.config.appUrl.endsWith("/")?c.config.appUrl.slice(0,-1):c.config.appUrl}${Vi.Gy}/${(0,H.xi)(e)}`;navigator.clipboard.writeText(t),n("Copied!"),setTimeout(()=>{n(qi)},2e3)}}})};var zi=n(2425);class Ui extends s.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Ui,"type","timeseries-data-received");class Gi extends s.BusEventWithPayload{}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Gi,"type","force-sync-y-axis");class Ki extends s.BusEventWithPayload{}function Wi(){return e=>{let t=Number.NEGATIVE_INFINITY,n=Number.POSITIVE_INFINITY;const r=u.jh.getTimeRange(e).subscribeToState(()=>{t=Number.NEGATIVE_INFINITY,n=Number.POSITIVE_INFINITY}),i=e.subscribeToEvent(Ki,()=>{t=Number.NEGATIVE_INFINITY,n=Number.POSITIVE_INFINITY}),a=e.subscribeToEvent(Gi,()=>{let[r,i]=[t,n];const a=Yi(e).filter(e=>{var t;const{fieldConfig:n,$data:a}=e.state;return(!("min"in n.defaults)||!("max"in n.defaults))&&([r,i]=Qi((null==a||null===(t=a.state.data)||void 0===t?void 0:t.series)||[],r,i),!0)});r===t&&i===n?Xi(e,t,n,a):([t,n]=[r,i],Xi(e,r,i))}),o=e.subscribeToEvent(Ui,r=>{const[i,a]=Qi(r.payload.series||[],t,n);i===a||i===Number.NEGATIVE_INFINITY||a===Number.POSITIVE_INFINITY||i===t&&a===n||([t,n]=[i,a],Xi(e,i,a))});return()=>{o.unsubscribe(),a.unsubscribe(),i.unsubscribe(),r.unsubscribe()}}}function Qi(e,t,n){let[r,i]=[t,n];for(const t of e||[]){var a;const e=null===(a=t.fields[1])||void 0===a?void 0:a.values.filter(Boolean);e&&(r=Math.max(r,...e),i=Math.min(i,...e))}return[r,i]}function Yi(e){return u.jh.findAllObjects(e,e=>e instanceof u.Eb&&"timeseries"===e.state.pluginId)}function Xi(e,t,n,r){for(const i of r||Yi(e))i.clearFieldConfigCache(),i.setState({fieldConfig:(0,w.merge)((0,w.cloneDeep)(i.state.fieldConfig),{defaults:{min:n,max:t}})})}function Ji(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Ki,"type","reset-sync-y-axis");class Zi extends u.Bs{constructor(...e){super(...e),Ji(this,"onClick",()=>{const{label:e}=this.state;(0,S.z)("breakdown_panel_selected",{label:e});const t=u.jh.lookupVariable(U.yr,this);if(!(0,Ze.bA)(t))throw new Error("Group by variable not found");t.changeValueTo(e)})}}function ea(){return e=>{var t;const n=null===(t=e.state.$data)||void 0===t?void 0:t.state.data;(null==n?void 0:n.state)===s.LoadingState.Done&&e.publishEvent(new Ui({series:n.series}),!0),e.state.$data.subscribeToState((t,n)=>{var r,i,a;(null===(r=t.data)||void 0===r?void 0:r.state)===s.LoadingState.Done&&(null===(i=t.data)||void 0===i?void 0:i.series)!==(null===(a=n.data)||void 0===a?void 0:a.series)&&e.publishEvent(new Ui({series:t.data.series}),!0)})}}Ji(Zi,"Component",({model:e})=>m().createElement(d.Button,{variant:"secondary",size:"sm",fill:"outline",onClick:e.onClick},"Select"));const ta=()=>e=>e.pipe((0,ve.map)(e=>null==e?void 0:e.map((e,t)=>(e.refId=`${e.refId}-${t}`,e))));function na(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const ra="seriesCount",ia=(e,t)=>()=>n=>n.pipe((0,ve.map)(n=>null==n?void 0:n.slice(e,t).map(e=>{var t;return e.meta=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){na(e,t,n[t])})}return e}({},e.meta),(t=e.meta).stats||(t.stats=[]),e.meta.stats.unshift({displayName:ra,value:n.length}),e})));const aa=_.M;class oa extends u.Bs{static buildVizPanel({metric:e,label:t,query:n,unit:r}){const i=new u.Es({$data:new u.dt({datasource:U.GH,maxDataPoints:250,queries:[{refId:`${e}-by-${t}`,expr:n,legendFormat:`{{${t}}}`,fromExploreMetrics:!0}]}),transformations:[ia(0,20),Oe(t),ta]});return u.d0.timeseries().setTitle(t).setUnit(r).setData(i).setBehaviors([ea()]).setOption("tooltip",{mode:d.TooltipDisplayMode.Multi,sort:ue.xB.Descending}).setHeaderActions([new Zi({label:t})]).setMenu(new Oi({labelName:t})).setShowMenuAlways(!0).build()}onActivate(){const{body:e,label:t}=this.state;this._subs.add(e.state.$data.subscribeToState(n=>{var r;if((null===(r=n.data)||void 0===r?void 0:r.state)!==s.LoadingState.Done)return;const{series:i}=n.data;if(!(null==i?void 0:i.length))return;const a=i.every(e=>!e.length)?null:[new Zi({label:t})],o=this.getAllValuesConfig(i);e.setState((0,w.merge)({},e.state,{headerActions:a},o))}))}getAllValuesConfig(e){var t,n;const{label:r}=this.state,i=null===(n=e[0].meta)||void 0===n||null===(t=n.stats)||void 0===t?void 0:t.find(e=>e.displayName===ra),a=i?i.value:e.length;return{title:`${r} (${a})`,description:e.length<a?`Showing only ${e.length} series out of ${a} to keep the data easy to read. Click on "Select" on this panel to view a breakdown of all the label's values.`:"",fieldConfig:{overrides:this.getOverrides(e)}}}getOverrides(e){const{startColorIndex:t}=this.state;return e.map((e,n)=>({matcher:{id:s.FieldMatcherID.byFrameRefID,options:e.refId},properties:[{id:"color",value:{mode:"fixed",fixedColor:(0,H.Vy)(t+n)}}]}))}constructor({metric:e,label:t,query:n,unit:r,startColorIndex:i}){super({key:`label-viz-panel-${t}`,metric:e,label:t,query:n,unit:r,startColorIndex:i,body:oa.buildVizPanel({metric:e,label:t,query:n,unit:r})}),this.addActivationHandler(this.onActivate.bind(this))}}function sa(){return{container:(0,o.css)({height:aa})}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(oa,"Component",({model:e})=>{const{body:t}=e.useState(),n=(0,d.useStyles2)(sa);return m().createElement("div",{className:n.container},m().createElement(t.Component,{model:t}))});class la extends u.Bs{onActivate(){this.subscribeToLayoutChange()}subscribeToLayoutChange(){const e=u.jh.findByKeyAndType(this,"layout-switcher",We),t=this.state.body.state.body,n=(e,n)=>{e.layout!==(null==n?void 0:n.layout)&&t.setState({templateColumns:e.layout===Ke.ROWS?Or:Sr})};u.Go.syncStateFromSearchParams(e,new URLSearchParams(window.location.search)),n(e.state),this._subs.add(e.subscribeToState(n))}Controls({model:e}){const{layoutSwitcher:t}=e.useState();return m().createElement(d.Field,{label:"View"},m().createElement(t.Component,{model:t}))}constructor({metric:e}){const t=$(e)?le(e):se(e);super({key:"metric-labels-list",metric:e,layoutSwitcher:new We({}),body:new Ye({variableName:U.yr,initialPageSize:60,pageSizeIncrement:9,body:new u.gF({children:[],isLazy:!0,templateColumns:Sr,autoRows:aa,$behaviors:[new u.Gg.K2({key:"metricCrosshairSync",sync:s.DashboardCursorSync.Crosshair}),Wi()]}),getLayoutLoading:()=>new u.dM({reactNode:m().createElement(d.Spinner,{inline:!0})}),getLayoutEmpty:()=>new u.dM({reactNode:m().createElement(Ue._,{title:"",severity:"info"},"No labels found for the current filters and time range.")}),getLayoutError:e=>new u.dM({reactNode:m().createElement(Ue._,{severity:"error",title:"Error while loading labels!",error:e})}),getLayoutChild:(n,r)=>{const{queries:i}=Ne({metric:e,queryConfig:{resolution:W.MEDIUM,labelMatchers:[],addIgnoreUsageFilter:!0,groupBy:n.value}});return new u.xK({body:new oa({metric:e,label:n.value,query:i[0].expr,unit:t,startColorIndex:r})})}})}),this.addActivationHandler(this.onActivate.bind(this))}}function ca(e){return{container:(0,o.css)({width:"100%"}),footer:(0,o.css)({display:"flex",justifyContent:"center",alignItems:"center",marginTop:e.spacing(4),"& button":{height:"40px",borderRadius:"8px"}})}}function ua(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(la,"Component",({model:e})=>{const{body:t}=e.useState(),n=(0,d.useStyles2)(ca),r=u.jh.lookupVariable(U.yr,e),{loading:i,error:a}=r.useState(),o=t.useSizes(),s=!i&&!a&&o.total>0&&o.current<o.total;return m().createElement("div",{"data-testid":"labels-list"},m().createElement("div",{className:n.container},m().createElement(t.Component,{model:t})),s&&m().createElement("div",{className:n.footer},m().createElement(Je,{label:"label",batchSizes:o,onClick:()=>{t.increaseBatchSize()}})))});class da extends u.Bs{constructor(...e){super(...e),ua(this,"onClick",()=>{const e=u.jh.lookupVariable(U.Ao,this);if(!(0,Ze.BE)(e))return;const{labelName:t,labelValue:n}=this.state;(0,S.z)("label_filter_changed",{label:t,action:"added",cause:"breakdown"}),(0,H.kj)(this).addFilterWithoutReportingInteraction({key:t,operator:"=",value:n})})}}function pa(e){var t;const n=(null===(t=e.fields[1])||void 0===t?void 0:t.labels)||{},r=Object.keys(n);return 0===r.length?"<unspecified>":n[r[0]]}ua(da,"Component",({model:e})=>m().createElement(d.Button,{variant:"secondary",size:"sm",fill:"outline",onClick:e.onClick},"Add to filters"));var ma=n(619);function ha(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class fa extends u.Bs{constructor(e){const t=f.x.getItem(h.V.BREAKDOWN_SORTBY);super({key:"breakdown-sort-by",target:e.target,options:fa.DEFAULT_OPTIONS,value:t&&fa.DEFAULT_OPTIONS.find(e=>e.value===t)||fa.DEFAULT_OPTIONS[0]}),ha(this,"onChange",e=>{this.setState({value:e}),f.x.setItem(h.V.BREAKDOWN_SORTBY,e.value)})}}function ga(e){return{sortByTooltip:(0,o.css)({display:"flex",gap:e.spacing(1)})}}function ba(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}ha(fa,"DEFAULT_OPTIONS",[{value:"outliers",label:"Outlying series",description:"Prioritizes values that show distinct behavior from others within the same label"},{value:"alphabetical",label:"Name [A-Z]",description:"Alphabetical order"},{value:"alphabetical-reversed",label:"Name [Z-A]",description:"Reversed alphabetical order"}]),ha(fa,"Component",({model:e})=>{const t=(0,d.useStyles2)(ga),{value:n,options:r}=e.useState();return m().createElement(d.Field,{"data-testid":"sort-by-select",htmlFor:"sort-by-criteria",label:m().createElement("div",{className:t.sortByTooltip},"Sort by",m().createElement(d.IconButton,{name:"info-circle",size:"sm",variant:"secondary",tooltip:"Sorts values using standard or smart time series calculations."}))},m().createElement(d.Combobox,{id:"sort-by-criteria",placeholder:"Choose criteria",width:20,options:r,value:n,onChange:e.onChange,isClearable:!1}))});class ya extends u.Bs{performRepeat(e){var t,n,r,i;if(e.state===s.LoadingState.Loading)return void this.setState({loadingLayout:null===(t=(n=this.state).getLayoutLoading)||void 0===t?void 0:t.call(n),errorLayout:void 0,emptyLayout:void 0,currentBatchSize:0});if(e.state===s.LoadingState.Error)return void this.setState({errorLayout:null===(r=(i=this.state).getLayoutError)||void 0===r?void 0:r.call(i,e),loadingLayout:void 0,emptyLayout:void 0,currentBatchSize:0});const a=this.filterAndSort(e.series);var o,l;if(!a.length)return void this.setState({emptyLayout:null===(o=(l=this.state).getLayoutEmpty)||void 0===o?void 0:o.call(l),errorLayout:void 0,loadingLayout:void 0,currentBatchSize:0,counts:{current:0,total:e.series.length}});this.setState({loadingLayout:void 0,errorLayout:void 0,emptyLayout:void 0,currentBatchSize:this.state.initialPageSize,counts:{current:a.length,total:e.series.length}});const c=a.slice(0,this.state.initialPageSize).map((t,n)=>this.state.getLayoutChild(e,t,n)).filter(Boolean);this.state.body.setState({children:c})}initFilterAndSort(){this.searchText=u.jh.findByKeyAndType(this,"quick-search",fn).state.value,this.sortBy=u.jh.findByKeyAndType(this,"breakdown-sort-by",fa).state.value.value}filterAndSort(e){let t=[];if(this.searchText){const n=this.searchText.split(",").map(e=>e.trim()).filter(Boolean).map(e=>{try{return new RegExp(e)}catch(e){return null}}).filter(Boolean);for(let r=0;r<e.length;r+=1){const i=e[r];n.some(e=>e.test(pa(i)))&&t.push(i)}}else t=e;return this.sortBy&&(t=(0,ma.sortSeries)(t,this.sortBy)),t}filter(e){this.searchText=e;const{data:t}=u.jh.getData(this).state;t&&(this.publishEvent(new Ki({}),!0),this.performRepeat(t))}sort(e){this.sortBy=e;const{data:t}=u.jh.getData(this).state;t&&(this.publishEvent(new Ki({}),!0),this.performRepeat(t))}increaseBatchSize(){const{data:e}=u.jh.getData(this).state;if(!e)return;const t=this.state.currentBatchSize+this.state.pageSizeIncrement,n=this.filterAndSort(e.series).slice(this.state.currentBatchSize,t).map((t,n)=>this.state.getLayoutChild(e,t,n)).filter(Boolean);this.state.body.setState({children:[...this.state.body.state.children,...n]}),this.setState({currentBatchSize:t}),this.publishEvent(new Gi({}),!0)}useSizes(){const{currentBatchSize:e,pageSizeIncrement:t}=this.useState(),{data:n}=u.jh.getData(this).state,r=n?this.filterAndSort(n.series).length:0,i=r-e;return{increment:i<t?i:t,current:e,total:r}}getCounts(){const{data:e}=u.jh.getData(this).state;return{current:0,total:e?e.series.length:0}}constructor({$behaviors:e,body:t,getLayoutChild:n,getLayoutLoading:r,getLayoutError:i,getLayoutEmpty:a,initialPageSize:o,pageSizeIncrement:s,$data:l}){super({key:"breakdown-by-frame-repeater",$behaviors:e,body:t,getLayoutChild:n,getLayoutLoading:r,getLayoutError:i,getLayoutEmpty:a,currentBatchSize:0,initialPageSize:o||120,pageSizeIncrement:s||9,loadingLayout:void 0,errorLayout:void 0,emptyLayout:void 0,counts:{current:0,total:0},$data:l}),ba(this,"searchText",""),ba(this,"sortBy",void 0),this.addActivationHandler(()=>{const e=u.jh.getData(this);if(!e)throw new Error("No data provider found!");this.initFilterAndSort(),this._subs.add(e.subscribeToState(e=>{e.data&&this.performRepeat(e.data)})),e.state.data&&this.performRepeat(e.state.data)})}}ba(ya,"Component",({model:e})=>{const{body:t,loadingLayout:n,errorLayout:r,emptyLayout:i}=e.useState();return n?m().createElement(n.Component,{model:n}):r?m().createElement(r.Component,{model:r}):i?m().createElement(i.Component,{model:i}):m().createElement(t.Component,{model:t})});class va extends dn{constructor(){super({key:"LabelValuesCountsProvider"}),this.addActivationHandler(()=>{u.jh.findByKeyAndType(this,"breakdown-by-frame-repeater",ya).subscribeToState((e,t)=>{e.counts!==t.counts&&this.setState({counts:e.counts})})})}}const wa=_.M;class Sa extends u.Bs{static buildVizPanel({labelValue:e,data:t,unit:n,fixedColor:r,headerActions:i,menu:a}){return u.d0.timeseries().setTitle(e).setBehaviors([ea()]).setData(t).setUnit(n).setColor({mode:"fixed",fixedColor:r}).setCustomFieldConfig("fillOpacity",9).setHeaderActions(i).setOption("legend",{showLegend:!1}).setShowMenuAlways(!0).setMenu(a).build()}constructor({labelValue:e,data:t,unit:n,fixedColor:r,headerActions:i,menu:a}){super({key:`label-value-viz-panel-${e}`,labelValue:e,unit:n,fixedColor:r,body:Sa.buildVizPanel({labelValue:e,data:t,unit:n,fixedColor:r,headerActions:i,menu:a})})}}function Oa(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ea(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){Oa(e,t,n[t])})}return e}function xa(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Sa,"Component",({model:e})=>{const{body:t}=e.useState();return m().createElement(t.Component,{model:t})});class ka extends u.Bs{onActivate(){this.subscribeToLayoutChange()}subscribeToQuickSearchChange(){u.Go.syncStateFromSearchParams(this.state.quickSearch,new URLSearchParams(window.location.search)),this._subs.add(this.subscribeToEvent(mn,e=>{const t=u.jh.findDescendents(this,ya)[0];t&&t.filter(e.payload.searchText)}))}subscribeToSortByChange(){const{sortBySelector:e}=this.state;this._subs.add(e.subscribeToState((e,t)=>{if(e.value.value!==(null==t?void 0:t.value.value)){const t=u.jh.findDescendents(this,ya)[0];t&&t.sort(e.value.value)}}))}subscribeToLayoutChange(){const{layoutSwitcher:e}=this.state;u.Go.syncStateFromSearchParams(e,new URLSearchParams(window.location.search));const t=(e,t)=>{e.layout!==(null==t?void 0:t.layout)&&this.updateBody(e.layout)};t(e.state),this._subs.add(e.subscribeToState(t))}updateBody(e){if(e===Ke.SINGLE)return void this.setState({body:this.buildSinglePanel()});const t=u.jh.findDescendents(this,ya)[0],n=t||this.buildByFrameRepeater();n.state.body.setState({templateColumns:e===Ke.ROWS?Or:Sr}),this.setState({body:n}),t||(this.subscribeToQuickSearchChange(),this.subscribeToSortByChange())}buildSinglePanel(){const{metric:e,label:t}=this.state;return new He({metric:e,discardUserPrefs:!0,panelOptions:{type:"timeseries",height:_.XL,headerActions:()=>[]},queryOptions:{groupBy:t}})}buildByFrameRepeater(){const{metric:e,label:t}=this.state,n=Ne({metric:e,queryConfig:{resolution:W.MEDIUM,labelMatchers:[],addIgnoreUsageFilter:!0,groupBy:t}}),r=n.isRateQuery?le(e):se(e);return new ya({$data:new u.Es({$data:new u.dt({datasource:U.GH,maxDataPoints:n.maxDataPoints,queries:n.queries}),transformations:[Oe(t)]}),$behaviors:[Wi(),new u.Gg.K2({key:"metricCrosshairSync",sync:s.DashboardCursorSync.Crosshair})],body:new u.gF({children:[],isLazy:!0,templateColumns:Sr,autoRows:wa}),getLayoutLoading:()=>new u.dM({reactNode:m().createElement(d.Spinner,{inline:!0})}),getLayoutEmpty:()=>new u.dM({reactNode:m().createElement(Ue._,{title:"",severity:"info"},"No label values found for the current filters and time range.")}),getLayoutError:e=>new u.dM({reactNode:m().createElement(Ue._,{severity:"error",title:"Error while loading metrics!",error:e.errors[0]})}),getLayoutChild:(e,n,i)=>{if(n.length<2)return null;const a=pa(n),o=!a.startsWith("<unspecified")?[new da({labelName:t,labelValue:a})]:[],s=new Sa({labelValue:a,data:new u.Zv({data:xa(Ea({},e),{series:[n]})}),unit:r,fixedColor:(0,H.Vy)(i),headerActions:o,menu:new Oi({labelName:a})});return new u.xK({body:s})}})}Controls({model:e}){const t=(0,d.useStyles2)(Pa),{body:n,quickSearch:r,layoutSwitcher:i,sortBySelector:a}=e.useState();return m().createElement(m().Fragment,null,n instanceof ya&&m().createElement(m().Fragment,null,m().createElement(d.Field,{className:t.quickSearchField,label:"Search"},m().createElement(r.Component,{model:r})),m().createElement(a.Component,{model:a})),m().createElement(d.Field,{label:"View"},m().createElement(i.Component,{model:i})))}constructor({metric:e,label:t}){super({key:"metric-label-values-list",metric:e,label:t,layoutSwitcher:new We({urlSearchParamName:"breakdownLayout",options:[{label:"Single",value:Ke.SINGLE},{label:"Grid",value:Ke.GRID},{label:"Rows",value:Ke.ROWS}]}),quickSearch:new fn({urlSearchParamName:"breakdownSearchText",targetName:"label value",countsProvider:new va,displayCounts:!0}),sortBySelector:new fa({target:"labels"}),body:void 0}),this.addActivationHandler(this.onActivate.bind(this))}}function Pa(e){return{singlePanelContainer:(0,o.css)({width:"100%",height:"300px"}),listContainer:(0,o.css)({width:"100%"}),listFooter:(0,o.css)({display:"flex",justifyContent:"center",alignItems:"center",marginTop:e.spacing(4),"& button":{height:"40px",borderRadius:"8px"}}),quickSearchField:(0,o.css)({flexGrow:1})}}Oa(ka,"Component",({model:e})=>{const{body:t}=e.useState();return m().createElement(m().Fragment,null,t instanceof He&&m().createElement(ka.SingleMetricPanelComponent,{model:e}),t instanceof ya&&m().createElement(ka.ByFrameRepeaterComponent,{model:e}))}),Oa(ka,"SingleMetricPanelComponent",({model:e})=>{const t=(0,d.useStyles2)(Pa),{body:n}=e.useState();return m().createElement("div",{"data-testid":"single-metric-panel"},m().createElement("div",{className:t.singlePanelContainer},n instanceof He&&m().createElement(n.Component,{model:n})))}),Oa(ka,"ByFrameRepeaterComponent",({model:e})=>{const t=(0,d.useStyles2)(Pa),{body:n}=e.useState(),r=u.jh.getData(e),{state:i,errors:a}=r.useState().data||{},o=n,l=o.useSizes(),c=i!==s.LoadingState.Loading&&!(null==a?void 0:a.length)&&l.total>0&&l.current<l.total;return m().createElement("div",{"data-testid":"label-values-list"},m().createElement("div",{className:t.listContainer},n instanceof ya&&m().createElement(n.Component,{model:n})),c&&m().createElement("div",{className:t.listFooter},m().createElement(Je,{label:"label value",batchSizes:l,onClick:()=>{o.increaseBatchSize()}})))});class Ca extends u.Bs{onActivate(){const e=this.getVariable();e.subscribeToState((t,n)=>{t.value!==n.value&&this.updateBody(e)}),c.config.featureToggles.enableScopesInMetricsExplore&&this._subs.add(this.subscribeToEvent(U.H0,()=>{this.updateBody(e)})),this.updateBody(e)}getVariable(){const e=u.jh.lookupVariable(U.yr,this);if(!(0,Ze.bA)(e))throw new Error("Group by variable not found");return e}updateBody(e){const{metric:t}=this.state;this.setState({body:e.hasAllValue()?new la({metric:t}):new ka({metric:t,label:e.state.value})})}constructor({metric:e}){super({metric:e,body:void 0}),this.addActivationHandler(this.onActivate.bind(this))}}function ja(e){return{container:(0,o.css)({flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column",paddingTop:e.spacing(1)}),controls:(0,o.css)({flexGrow:0,display:"flex",gap:e.spacing(2),height:"70px",justifyContent:"space-between",alignItems:"end"}),searchField:(0,o.css)({flexGrow:1})}}function _a(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function Ta(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){_a(a,r,i,o,s,"next",e)}function s(e){_a(a,r,i,o,s,"throw",e)}o(void 0)})}}function Ia(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(Ca,"Component",({model:e})=>{const t=(0,d.useStyles2)(ja),{body:n}=e.useState(),r=e.getVariable();return m().createElement("div",{className:t.container},m().createElement("div",{className:t.controls},m().createElement(d.Field,{label:"By label"},m().createElement(r.Component,{model:r})),n instanceof la&&m().createElement(n.Controls,{model:n}),n instanceof ka&&m().createElement(n.Controls,{model:n})),m().createElement("div",{"data-testid":"panels-list"},n instanceof la&&m().createElement(n.Component,{model:n}),n instanceof ka&&m().createElement(n.Component,{model:n})))});const Aa="breakdown",Da="logs",La=[{displayName:"Breakdown",value:Aa,getScene:e=>new Ca({metric:e.state.metric})},{displayName:"Related metrics",value:"related",getScene:e=>new $i({metric:e.state.metric}),description:"Relevant metrics based on current label filters"},{displayName:"Related logs",value:Da,getScene:e=>e.createRelatedLogsScene(),description:"Relevant logs based on current label filters and time range"}];class Na extends u.Bs{constructor(...e){var t;super(...e),Ia(t=this,"getLinkToExplore",()=>Ta(function*(){const e=u.jh.findByKey(t,Ci),n=u.jh.findDescendents(e,He)[0],r=u.jh.findDescendents(n,u.dt)[0].state.data;if(!r)throw new Error("Cannot get link to explore, no panel data found");const i=u.jh.getAncestor(t,so);return(0,u.pN)(r,i,r.timeRange)})()),Ia(t,"openExploreLink",()=>Ta(function*(){(0,S.z)("selected_metric_action_clicked",{action:"open_in_explore"}),t.getLinkToExplore().then(e=>{window.open(e,"_blank")})})())}}function Ma(e){return{actions:(0,o.css)({[e.breakpoints.up(e.breakpoints.values.md)]:{position:"absolute",right:0,top:16,zIndex:2}}),customTabsBar:(0,o.css)({paddingBottom:e.spacing(1)})}}function Ba(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function Ra(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){Ba(a,r,i,o,s,"next",e)}function s(e){Ba(a,r,i,o,s,"throw",e)}o(void 0)})}}Ia(Na,"Component",({model:e})=>{const t=u.jh.getAncestor(e,so),n=(0,d.useStyles2)(Ma),r=(0,H.kj)(e),[i,a]=function(e){const t=()=>(0,zi._r)().getBookmarkIndex(e),n=t(),[r,i]=(0,p.useState)(n);(0,p.useEffect)(()=>{const t=e.subscribeToEvent(u.bZ,()=>{i((0,zi._r)().getBookmarkIndex(e))});return()=>t.unsubscribe()},[e]),n!==r&&i(n);const a=null!=r;return[a,()=>{if((0,S.z)("bookmark_changed",{action:a?"toggled_off":"toggled_on"}),a){let e=t();for(;null!=e;)(0,zi._r)().removeBookmark(e),e=t()}else(0,zi._r)().addBookmark(e);i(t())}]}(r),{actionView:o}=t.useState();return m().createElement(d.Box,{paddingY:1,"data-testid":"action-bar"},m().createElement("div",{className:n.actions},m().createElement(d.Stack,{gap:1},r.state.embedded?m().createElement(d.LinkButton,{href:(0,ti.Rk)((0,H.xi)(r)),variant:"secondary",icon:"arrow-right",tooltip:"Open in Metrics Drilldown",onClick:()=>(0,S.z)("selected_metric_action_clicked",{action:"open_from_embedded"})},"Metrics Drilldown"):m().createElement(d.ToolbarButton,{variant:"canvas",tooltip:ei.SELECT_NEW_METRIC_TOOLTIP,onClick:()=>{(0,S.z)("selected_metric_action_clicked",{action:"unselect"}),r.publishEvent(new U.OO(void 0))}},"Select new metric"),m().createElement(d.ToolbarButton,{variant:"canvas",icon:"compass",tooltip:ei.OPEN_EXPLORE_LABEL,onClick:e.openExploreLink}),m().createElement(Hi,{trail:r}),m().createElement(d.ToolbarButton,{variant:"canvas",icon:i?m().createElement(d.Icon,{name:"favorite",type:"mono",size:"lg"}):m().createElement(d.Icon,{name:"star",type:"default",size:"lg"}),tooltip:ei.BOOKMARK_LABEL,onClick:a}))),m().createElement(d.TabsBar,{className:n.customTabsBar},La.map((e,n)=>{const r=e.displayName,i=e.value===Da?t.state.relatedLogsCount:void 0,a=o===e.value,s=m().createElement(d.Tab,{key:n,label:r,counter:i,active:a,onChangeTab:()=>{a||((0,S.z)("metric_action_view_changed",{view:e.value,related_logs_count:t.relatedLogsOrchestrator.checkConditionsMetForRelatedLogs()?i:void 0}),t.setActionView(e.value))}});return e.description?m().createElement(d.Tooltip,{key:n,content:e.description,placement:"top",theme:"info"},s):s})))});const $a={job:"service_name",instance:"service_instance_id"};function Fa(e){return e in $a?$a[e]:e}const Va=e=>{let t=!1;return{name:"labelsCrossReference",checkConditionsMetForRelatedLogs:()=>t,getDataSources:()=>Ra(function*(){var n;const r=(0,H.kj)(e),i=u.jh.lookupVariable(U.Ao,r);if(!(0,Ze.BE)(i)||!i.state.filters.length)return t=!1,[];t=!0;const a=i.state.filters.map(({key:e,operator:t,value:n})=>({key:e,operator:t,value:n})),o=null===(n=e.state.$timeRange)||void 0===n?void 0:n.state.value,s=yield(0,ot.tS)().getHealthyDataSources("loki"),l=yield Promise.all(s.map(({uid:e,name:t})=>Ra(function*(){const n=yield function(e,t,n){return Ra(function*(){var r;const i=yield(0,c.getDataSourceSrv)().get(e),a=yield null===(r=i.getTagKeys)||void 0===r?void 0:r.call(i,{timeRange:n,filters:t.map(({key:e,operator:t,value:n})=>({key:Fa(e),operator:t,value:n}))});if(!Array.isArray(a))return!1;const o=new Set(a.map(e=>e.text));return!!t.map(e=>Fa(e.key)).every(e=>o.has(e))&&(yield Promise.all(t.map(e=>Ra(function*(){var r;const a=Fa(e.key),o=yield null===(r=i.getTagValues)||void 0===r?void 0:r.call(i,{key:a,timeRange:n,filters:t});return!!Array.isArray(o)&&o.some(t=>t.text===e.value)})()))).every(Boolean)})()}(e,a,o);return n?{uid:e,name:t}:null})()));return l.filter(e=>null!==e)})(),getLokiQueryExpr(){const t=(0,H.kj)(e),n=u.jh.lookupVariable(U.Ao,t);if(!(0,Ze.BE)(n)||!n.state.filters.length)return"";return`{${n.state.filters.map(e=>`${Fa(e.key)}${e.operator}"${e.value}"`).join(",")}}`}}};var qa=n(6365);function Ha(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function za(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){Ha(a,r,i,o,s,"next",e)}function s(e){Ha(a,r,i,o,s,"throw",e)}o(void 0)})}}function Ua(e,t,n){if(!t||!n[t])return"";const r=n[t].find(t=>t.name===e);if(!r)return"";return function(e){if(function(e){if(e.trim().length<=2)return!1;let t=!1;const n=qa.K3.parse(e);return n.iterate({enter:({type:e})=>{if(e.id===qa.Yw)return t=!0,!1}}),!t}(e))return e;const t=Wa(e,qa.MD);if(!t)return"";const n=e.substring(t.from,t.to),r=Wa(e,qa.AL),i=r?e.substring(r.from,r.to):"";return`${n} ${i}`.trim()}(r.query)}function Ga(){return za(function*(){const e=yield(0,ot.tS)().getHealthyDataSources("loki"),t={};return yield Promise.all(e.map(e=>za(function*(){try{const r=function(e,t){if(0===e.length)return[];const n=new Map;return e.forEach(e=>{e.rules.filter(e=>"recording"===e.type).forEach(({type:e,name:r,query:i})=>{if(n.has(r)){const e=n.get(r);e&&(e.hasMultipleOccurrences=!0,n.set(r,e))}else n.set(r,{type:e,name:r,query:i,datasource:{name:t.name,uid:t.uid},hasMultipleOccurrences:!1})})}),Array.from(n.values())}(yield(n=e,za(function*(){const e={url:`api/prometheus/${n.uid}/api/v1/rules`,showErrorAlert:!1,showSuccessAlert:!1},t=yield(0,ve.lastValueFrom)((0,c.getBackendSrv)().fetch(e));return t.ok?t.data.data.groups:(Ce.v.warn(`Failed to fetch recording rules from Loki data source: ${n.name}`),[])})()),e);t[e.uid]=r}catch(e){Ce.v.warn(e)}var n})())),t})()}const Ka=()=>{let e={},t=!1;return{name:"lokiRecordingRules",checkConditionsMetForRelatedLogs:()=>t,getDataSources:n=>za(function*(){e=yield Ga();const r=function(e,t){const n=[];return Object.values(t).forEach(t=>{t.filter(t=>t.name===e).forEach(e=>{n.push(e.datasource)})}),n}(n,e);return t=Boolean(r.length),r})(),getLokiQueryExpr:(t,n)=>Ua(t,n,e)}};function Wa(e,t){let n;return qa.K3.parse(e).iterate({enter:e=>{if(e.type.id===t)return n=e.node,!1}}),n}function Qa(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function Ya(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Xa{get lokiDataSources(){return this._internalState.lokiDataSources}set lokiDataSources(e){const t=this._internalState.lokiDataSources.map(e=>e.uid).join(","),n=e.map(e=>e.uid).join(",");t&&t===n||(this._internalState.lokiDataSources=e,this._changeHandlers.lokiDataSources.forEach(e=>e(this._internalState.lokiDataSources)))}set relatedLogsCount(e){this._internalState.relatedLogsCount=e,this._changeHandlers.relatedLogsCount.forEach(e=>e(this._internalState.relatedLogsCount))}addLokiDataSourcesChangeHandler(e){this._changeHandlers.lokiDataSources.push(e)}addRelatedLogsCountChangeHandler(e){this._changeHandlers.relatedLogsCount.push(e)}handleFiltersChange(){this.lokiDataSources&&(this.lokiDataSources=[],this.relatedLogsCount=0,this.findAndCheckAllDatasources())}findAndCheckAllDatasources(){return(e=function*(){const e=yield this._dataSourceFetcher.getHealthyDataSources("loki");e.length>0?this.checkLogsInDataSources(e):(this.lokiDataSources=[],this.relatedLogsCount=0)},function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){Qa(a,r,i,o,s,"next",e)}function s(e){Qa(a,r,i,o,s,"throw",e)}o(void 0)})}).call(this);var e}getLokiQueries(e,t=100){const{metric:n}=this._metricScene.state,r=this._logsConnectors.reduce((t,r,i)=>{const a=r.getLokiQueryExpr(n,e);var o;a&&(t[null!==(o=r.name)&&void 0!==o?o:`connector-${i}`]=a);return t},{});return Object.keys(r).map(e=>({refId:`RelatedLogs-${e}`,expr:r[e],maxLines:t,supportingQueryType:ai.id}))}checkLogsInDataSources(e){const t=[];let n=0,r=0;if(0===e.length)return this.lokiDataSources=[],void(this.relatedLogsCount=0);e.forEach(i=>{const a=new u.dt({datasource:{uid:i.uid},queries:[],key:`related_logs_check_${i.uid}`});a.setState({queries:this.getLokiQueries(i.uid)}),a.subscribeToState(a=>{var o;if((null===(o=a.data)||void 0===o?void 0:o.state)===s.LoadingState.Done){var l;if(r++,null===(l=a.data)||void 0===l?void 0:l.series){const e=this.countLogsLines(a);e>0&&(t.push(i),n+=e)}r===e.length&&(this.lokiDataSources=t,this.relatedLogsCount=n)}}),a.activate()})}checkConditionsMetForRelatedLogs(){return this._logsConnectors.some(e=>e.checkConditionsMetForRelatedLogs())}countLogsLines(e){var t,n;return null!==(n=null===(t=e.data)||void 0===t?void 0:t.series.reduce((e,t)=>e+t.length,0))&&void 0!==n?n:0}constructor(e){Ya(this,"_logsConnectors",void 0),Ya(this,"_metricScene",void 0),Ya(this,"_dataSourceFetcher",(0,ot.tS)()),Ya(this,"_changeHandlers",{lokiDataSources:[],relatedLogsCount:[]}),Ya(this,"_internalState",{relatedLogsCount:0,lokiDataSources:[]}),this._metricScene=e,this._logsConnectors=[Ka(),Va(e)]}}function Ja(){const e=(0,d.useStyles2)(Za);return m().createElement(d.Stack,{direction:"column",gap:2},m().createElement(d.Alert,{title:"No related logs found",severity:"info"},"We couldn't find any logs related to the current metric with your selected filters."),m().createElement(d.Text,null,"To find related logs, try the following:",m().createElement("ul",{className:e.list},m().createElement("li",null,"Adjust your label filters to include labels that exist in both the current metric and your logs"),m().createElement("li",null,"Select a metric created by a"," ",m().createElement(d.TextLink,{external:!0,href:"https://grafana.com/docs/loki/latest/alert/#recording-rules"},"Loki Recording Rule")),m().createElement("li",null,"Broaden the time range to include more data"))),m().createElement(d.Text,{variant:"bodySmall",color:"secondary"},"Note: Related logs is an experimental feature."))}function Za(e){return{list:(0,o.css)({paddingLeft:e.spacing(2),marginTop:e.spacing(1)})}}function eo({context:e}){const t=(0,p.useMemo)(()=>e,[e]),{links:n,isLoading:r}=(0,c.usePluginLinks)({extensionPointId:"grafana-metricsdrilldown-app/open-in-logs-drilldown/v1",limitPerPlugin:1,context:t}),i=(0,p.useMemo)(()=>n.find(({pluginId:e})=>"grafana-lokiexplore-app"===e),[n]);if(r)return m().createElement(d.LinkButton,{variant:"secondary",size:"sm",disabled:!0},"Loading...");const a=void 0!==i;return m().createElement(d.LinkButton,{href:a?`${c.config.appSubUrl}${i.path}`:`${c.config.appSubUrl}/a/grafana-lokiexplore-app`,target:"_blank",tooltip:a?"Use the Logs Drilldown app to explore these logs":"Navigate to the Logs Drilldown app",variant:"secondary",size:"sm",onClick:()=>(0,S.z)("related_logs_action_clicked",{action:"open_logs_drilldown"})},a?"Open in Logs Drilldown":"Open Logs Drilldown")}function to(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function no(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ro(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const io="related_logs/logs_panel_container";class ao extends u.Bs{_onActivate(){return(e=function*(){this.state.orchestrator.addLokiDataSourcesChangeHandler(()=>this.setupLogsPanel()),this.state.orchestrator.lokiDataSources.length?this.setupLogsPanel():(this.setState({loading:!0}),yield this.state.orchestrator.findAndCheckAllDatasources(),this.setState({loading:!1}))},function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){to(a,r,i,o,s,"next",e)}function s(e){to(a,r,i,o,s,"throw",e)}o(void 0)})}).call(this);var e}showNoLogsFound(){u.jh.findByKeyAndType(this,io,u.xK).setState({body:new u.dM({component:Ja})}),this.setState({controls:void 0}),this.state.orchestrator.relatedLogsCount=0}_buildQueryRunner(){this._queryRunner=new u.dt({datasource:{uid:U.Kf},queries:[],key:"related_logs/logs_query"}),this._constructLogsDrilldownLinkContext(this._queryRunner.state),this._subs.add(this._queryRunner.subscribeToState(e=>{var t;if((null===(t=e.data)||void 0===t?void 0:t.state)!==s.LoadingState.Done)return;0===this.state.orchestrator.countLogsLines(e)&&this.showNoLogsFound(),this._constructLogsDrilldownLinkContext(e)}))}setupLogsPanel(){if(this._buildQueryRunner(),!this.state.orchestrator.lokiDataSources.length)return void this.showNoLogsFound();u.jh.findByKeyAndType(this,io,u.xK).setState({body:u.d0.logs().setTitle("Logs").setData(this._queryRunner).build()});const e=new u.yP({name:U.Az,label:"Logs data source",query:this.state.orchestrator.lokiDataSources.map(e=>`${e.name} : ${e.uid}`).join(",")});this.setState({$variables:new u.Pj({variables:[e]}),controls:[new u.K8({layout:"vertical"})]}),this._subs.add(e.subscribeToState((e,t)=>{e.value!==t.value&&(0,S.z)("related_logs_action_clicked",{action:"logs_data_source_changed"})})),this.updateLokiQuery()}_constructLogsDrilldownLinkContext(e){var t,n;const r=null!==(n=null===(t=u.jh.lookupVariable(U.Az,this))||void 0===t?void 0:t.getValue())&&void 0!==n?n:"",i=e.queries,a=[];r&&i.length&&i.forEach(e=>{a.push(ro(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){no(e,t,n[t])})}return e}({},e),{datasource:{uid:r,type:"loki"}}))}),this.setState({logsDrilldownLinkContext:{targets:a,timeRange:u.jh.getTimeRange(this).state}})}updateLokiQuery(){if(!this._queryRunner)return;const e=u.jh.lookupVariable(U.Az,this);let t;if((0,Ze.UG)(e)&&(t=e.getValue()),!t)return;const n=this.state.orchestrator.getLokiQueries(t);0!==n.length?this._queryRunner.setState({queries:n}):this.showNoLogsFound()}constructor(e){super({loading:!1,controls:[],body:new u.gF({templateColumns:"1fr",autoRows:"minmax(300px, 1fr)",children:[new u.xK({key:io,body:void 0})]}),orchestrator:e.orchestrator,logsDrilldownLinkContext:{targets:[]}}),no(this,"_queryRunner",void 0),no(this,"_variableDependency",new u.Sh(this,{variableNames:[U.Az,U.Ao],onReferencedVariableValueChanged:e=>{e.state.name===U.Ao?this.state.orchestrator.handleFiltersChange():e.state.name===U.Az&&this.updateLokiQuery()}})),this.addActivationHandler(()=>{this._onActivate()})}}function oo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}no(ao,"Component",({model:e})=>{const{controls:t,body:n,logsDrilldownLinkContext:r,loading:i}=e.useState();return i?m().createElement(d.Spinner,null):m().createElement(d.Stack,{gap:1,direction:"column",grow:1},m().createElement(d.Stack,{gap:1,direction:"row",justifyContent:"space-between",alignItems:"start"},m().createElement(d.Stack,{gap:1},null==t?void 0:t.map(e=>m().createElement(e.Component,{key:e.state.key,model:e}))),m().createElement(eo,{context:r})),m().createElement(n.Component,{model:n}))});class so extends u.Bs{_onActivate(){void 0===this.state.actionView&&this.setActionView(Aa),this.relatedLogsOrchestrator.findAndCheckAllDatasources(),this.relatedLogsOrchestrator.addRelatedLogsCountChangeHandler(e=>{this.setState({relatedLogsCount:e})}),this.subscribeToEvents()}subscribeToEvents(){c.config.featureToggles.enableScopesInMetricsExplore&&this._subs.add(this.subscribeToEvent(U.H0,e=>{var t;null===(t=this.state.body.state.selectedTab)||void 0===t||t.publishEvent(e)}))}getUrlState(){return{actionView:this.state.actionView}}updateFromUrl(e){if("string"==typeof e.actionView){if(this.state.actionView!==e.actionView){const t=La.find(t=>t.value===e.actionView);t&&this.setActionView(t.value)}}else null===e.actionView&&this.setActionView(null)}setActionView(e){const{body:t}=this.state,n=e?La.find(t=>t.value===e):null;n&&n.value!==this.state.actionView?(t.setState({selectedTab:n.getScene(this)}),this.setState({actionView:n.value})):(t.setState({selectedTab:void 0}),this.setState({actionView:void 0}))}createRelatedLogsScene(){return new ao({orchestrator:this.relatedLogsOrchestrator})}constructor(e){var t,n,r;super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){oo(e,t,n[t])})}return e}({$variables:null!==(t=e.$variables)&&void 0!==t?t:(r=e.metric,new u.Pj({variables:[...(0,U.ym)(r),new Jr]})),body:null!==(n=e.body)&&void 0!==n?n:new ji({metric:e.metric})},e)),oo(this,"relatedLogsOrchestrator",new Xa(this)),oo(this,"_urlSync",new u.So(this,{keys:["actionView"]})),oo(this,"_variableDependency",new u.Sh(this,{variableNames:[U.Ao],onReferencedVariableValueChanged:()=>{this.relatedLogsOrchestrator.handleFiltersChange()}})),this.addActivationHandler(this._onActivate.bind(this))}}oo(so,"Component",({model:e})=>{const{body:t}=e.useState(),n=(0,d.useStyles2)(lo);return m().createElement("div",{className:n.container,"data-testid":"metric-scene"},m().createElement(t.Component,{model:t}))});const lo=()=>({container:(0,o.css)({position:"relative",height:"100%",width:"100%",display:"flex",flexDirection:"column"})});function co(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class uo extends u.Bs{constructor(e){var t,n;super({stickyMainGraph:null!==(t=e.stickyMainGraph)&&void 0!==t&&t,isOpen:null!==(n=e.isOpen)&&void 0!==n&&n}),co(this,"onToggleStickyMainGraph",()=>{const e=!this.state.stickyMainGraph;(0,S.z)("settings_changed",{stickyMainGraph:e}),this.setState({stickyMainGraph:e})}),co(this,"onToggleOpen",e=>{this.setState({isOpen:e})})}}function po(e){return{popover:(0,o.css)({display:"flex",padding:e.spacing(2),flexDirection:"column",background:e.colors.background.primary,boxShadow:e.shadows.z3,borderRadius:e.shape.radius.default,border:`1px solid ${e.colors.border.weak}`,zIndex:1,marginRight:e.spacing(2)}),heading:(0,o.css)({fontWeight:e.typography.fontWeightMedium,paddingBottom:e.spacing(2)}),options:(0,o.css)({display:"grid",gridTemplateColumns:"1fr 50px",rowGap:e.spacing(1),columnGap:e.spacing(2)})}}function mo(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function ho(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){mo(a,r,i,o,s,"next",e)}function s(e){mo(a,r,i,o,s,"throw",e)}o(void 0)})}}function fo(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function go(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){fo(e,t,n[t])})}return e}function bo(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}co(uo,"Component",({model:e})=>{const{stickyMainGraph:t,isOpen:n}=e.useState(),r=(0,d.useStyles2)(po),i=(0,H.kj)(e),{topScene:a}=i.useState();if(!(a instanceof so))return null;return m().createElement(d.Dropdown,{overlay:()=>m().createElement("div",{className:r.popover,onClick:e=>e.stopPropagation()},m().createElement("div",{className:r.heading},"Settings"),a instanceof so&&m().createElement("div",{className:r.options},m().createElement("div",null,"Always keep selected metric graph in-view"),m().createElement(d.Switch,{value:t,onChange:e.onToggleStickyMainGraph}))),placement:"bottom",onVisibleChange:e.onToggleOpen},m().createElement(d.ToolbarButton,{icon:"cog",variant:"canvas",isOpen:n,"data-testid":"settings-button"}))});class yo extends u.Bs{getUrlState(){const{metric:e,metricSearch:t,nativeHistogramMetric:n}=this.state;return{metric:e,metricSearch:t,nativeHistogramMetric:n}}updateFromUrl(e){const t={};if("string"==typeof e.metric){if(this.state.metric!==e.metric){let n=!1;"1"===e.nativeHistogramMetric&&(n=!0),Object.assign(t,this.getSceneUpdatesForNewMetricValue(e.metric,n))}}else null!=e.metric||this.state.embedded||(t.metric=void 0,t.topScene=new ur);"string"==typeof e.metricSearch?t.metricSearch=e.metricSearch:null==e.metric&&(t.metricSearch=void 0),this.setState(t)}onActivate(){this.datasourceHelper.init(),this.subscribeToConfigEvents(),this.setState({trailActivated:!0}),this.state.topScene||this.setState({topScene:vo(this.state.metric)}),this.subscribeToEvent(U.OO,this._handleMetricSelectedEvent.bind(this));const e=u.jh.lookupVariable(U.Ao,this);(0,Ze.BE)(e)&&(this._subs.add(null==e?void 0:e.subscribeToState((e,t)=>{this._addingFilterWithoutReportingInteraction||(0,S.h)(e.filters,t.filters)})),null==e||e.setState({useQueriesAsFilterForOptions:Boolean(this.state.metric)}),this.subscribeToState((t,n)=>{t.metric!==n.metric&&(null==e||e.setState({useQueriesAsFilterForOptions:Boolean(t.metric)}),this.state.drawer.close())}));const t=()=>{const e=u.jh.lookupVariable(U.Ao,this),t=(0,Ze.BE)(e)&&e.state.filters.length>0;(this.state.metric||t)&&(0,zi._r)().setRecentTrail(this)};return window.addEventListener("unload",t),()=>{this.state.embedded||t(),window.removeEventListener("unload",t)}}subscribeToConfigEvents(){return ho(function*(){this._subs.add(this.subscribeToEvent(g,e=>ho(function*(){const{metric:t}=e.payload;(0,S.z)("configure_panel_opened",{metricType:Rr(t)});const n=yield this.getMetadataForMetric(t);this.state.drawer.open({title:"Configure the Prometheus function",subTitle:`${t} ${n?` (${n.type})`:""}`,body:new Mr({metric:t})})}).call(this))),this._subs.add(this.subscribeToEvent(Pr,()=>{this.state.drawer.close()})),this._subs.add(this.subscribeToEvent(kr,e=>{const{metric:t,config:n,restoreDefault:r}=e.payload;r?(0,S.z)("default_panel_config_restored",{metricType:Rr(t)}):(0,S.z)("panel_config_applied",{metricType:Rr(t),configId:n.id}),this.state.drawer.close();const i=u.jh.findAllObjects(this.state.topScene||this,e=>e instanceof He&&e.state.metric===t&&Boolean(1===u.jh.findDescendents(e,y).length));for(const e of i)e.update(n.panelOptions,n.queryOptions);(0,O.qq)([`Configuration successfully ${r?"restored":"applied"} for metric ${t}!`])}))}).call(this)}addFilterWithoutReportingInteraction(e){const t=u.jh.lookupVariable(U.Ao,this);(0,Ze.BE)(t)&&(this._addingFilterWithoutReportingInteraction=!0,t.setState({filters:[...t.state.filters,e]}),this._addingFilterWithoutReportingInteraction=!1)}getMetadataForMetric(e){return this.datasourceHelper.getMetadataForMetric(e)}isNativeHistogram(e){return ho(function*(){return this.datasourceHelper.isNativeHistogram(e)}).call(this)}_handleMetricSelectedEvent(e){return ho(function*(){var t;const n=null!==(t=e.payload)&&void 0!==t?t:"",r=u.jh.lookupVariable(U.Ao,this);(0,Ze.BE)(r)&&r.setState({baseFilters:Oo(e.payload)});const i=yield this.isNativeHistogram(n);this._urlSync.performBrowserHistoryAction(()=>{this.setState(this.getSceneUpdatesForNewMetricValue(n,i))})}).call(this)}getSceneUpdatesForNewMetricValue(e,t){const n={};return n.metric=e,n.nativeHistogramMetric=t?"1":"",n.topScene=vo(e,t),n}getQueries(){return u.jh.findAllObjects(this,e=>si(e)).reduce((e,t)=>(e.push(...t.state.queries.map(e=>bo(go({},e),{expr:u.jh.interpolate(t,e.expr)}))),e),[])}constructor(e){var t,n,r,i,a,o,s;super(go({$timeRange:null!==(t=e.$timeRange)&&void 0!==t?t:new u.JZ({}),$variables:null!==(n=e.$variables)&&void 0!==n?n:wo(e.initialDS,e.metric,e.initialFilters),controls:null!==(r=e.controls)&&void 0!==r?r:[new u.K8({layout:"vertical"}),new u.N0,new u.KE({}),new u.WM({})],settings:null!==(i=e.settings)&&void 0!==i?i:new uo({}),pluginInfo:new u.dM({component:Gr}),createdAt:null!==(a=e.createdAt)&&void 0!==a?a:(new Date).getTime(),dashboardMetrics:{},alertingMetrics:{},nativeHistogramMetric:null!==(o=e.nativeHistogramMetric)&&void 0!==o?o:"",trailActivated:null!==(s=e.trailActivated)&&void 0!==s&&s,drawer:new Xr({})},e)),fo(this,"_addingFilterWithoutReportingInteraction",!1),fo(this,"datasourceHelper",new Mt(this)),fo(this,"_urlSync",new u.So(this,{keys:["metric","metricSearch","nativeHistogramMetric"]})),this.addActivationHandler(this.onActivate.bind(this))}}function vo(e,t=!1){return e?new so({metric:e,nativeHistogram:t}):new ur}function wo(e,t,n){let r=[new Pt({initialDS:e}),new It,new u.H9({key:U.Ao,name:U.Ao,label:"Filters",addFilterButtonText:"Add label",datasource:U.GH,hide:s.VariableHide.dontHide,layout:"combobox",filters:null!=n?n:[],baseFilters:Oo(t),applyMode:"manual",allowCustomValue:!0,useQueriesAsFilterForOptions:!1,expressionBuilder:e=>e.filter(e=>"__name__"!==e.key).map(e=>`${(0,l.Nc)(e.key)}${e.operator}"${e.value.replaceAll("=","=")}"`).join(",")})];return Boolean(c.config.featureToggles.scopeFilters&&c.config.featureToggles.enableScopesInMetricsExplore&&!c.config.buildInfo.version.startsWith("11."))&&r.unshift(new u.Kg({enable:!0})),new u.Pj({variables:r})}function So(e,t,n){const r=xi(e,n);return{container:(0,o.css)({flexGrow:1,display:"flex",gap:e.spacing(1),flexDirection:"column",padding:e.spacing(1,2),position:"relative",background:r}),body:(0,o.css)({flexGrow:1,display:"flex",flexDirection:"column",minHeight:0}),controls:(0,o.css)({display:"flex",gap:e.spacing(1),padding:e.spacing(1,0),alignItems:"flex-end",flexWrap:"wrap",position:"sticky",background:r,zIndex:e.zIndex.navbarFixed,top:t,borderBottom:`1px solid ${e.colors.border.weak}`}),settingsInfo:(0,o.css)({display:"flex",gap:e.spacing(.5)})}}function Oo(e){return e?[{key:"__name__",operator:"=",value:e}]:[]}function Eo(){const e=document.querySelector('[data-testid="app-controls"]');if(!e)return;const{height:t}=e.getBoundingClientRect();document.documentElement.style.setProperty("--app-controls-height",`${t}px`)}fo(yo,"Component",({model:e})=>{const{controls:t,topScene:n,settings:r,pluginInfo:i,embedded:a,drawer:o}=e.useState();var s;const l=null!==(s=(0,c.useChromeHeaderHeight)())&&void 0!==s?s:0,h=a?0:l,f=(0,d.useStyles2)(So,h,e);return(0,p.useEffect)(()=>{const t=u.jh.lookupVariable(U.Ao,e),n=e.datasourceHelper;(0,H.FG)(e,t,n)},[e]),(0,p.useEffect)(()=>{Eo();const e=document.querySelector('[data-testid="app-controls"]');if(!e)return;const t=new ResizeObserver(Eo);return t.observe(e),()=>{t.disconnect(),document.documentElement.style.removeProperty("--app-controls-height")}},[a,t]),m().createElement(m().Fragment,null,m().createElement("div",{className:f.container},t&&m().createElement("div",{className:f.controls,"data-testid":"app-controls"},t.map(e=>m().createElement(e.Component,{key:e.state.key,model:e})),m().createElement("div",{className:f.settingsInfo},m().createElement(r.Component,{model:r}),m().createElement(i.Component,{model:i}))),n&&m().createElement(u.$L,{scene:n,createBrowserHistorySteps:!0,updateUrlOnInit:!0,namespace:e.state.urlNamespace},m().createElement("div",{className:f.body},n&&m().createElement(n.Component,{model:n})))),m().createElement(o.Component,{model:o}))})},384:(e,t,n)=>{function r(e){return null!==e&&"adhoc"===(null==e?void 0:e.state.type)}function i(e){return null!==e&&"custom"===(null==e?void 0:e.state.type)}function a(e){return null!==e&&"query"===(null==e?void 0:e.state.type)}n.d(t,{BE:()=>r,UG:()=>i,bA:()=>a})},416:(e,t,n)=>{n.d(t,{V:()=>r});const r={DATASOURCE:"datasource",RECENT_METRICS:"recent-metrics",BOOKMARKS:"bookmarks",METRIC_PREFS:"metric-prefs",BREAKDOWN_SORTBY:"breakdown.sortby"}},619:(e,t,n)=>{n.r(t),n.d(t,{sortSeries:()=>h,wasmSupported:()=>g});var r=n(6944),i=n(7781),a=n(3241),o=n(3616),s=n(4964),l=n(3347);function c(e){var t;const n=null===(t=e.fields[1])||void 0===t?void 0:t.labels;if(!n)return null;const r=Object.keys(n);return 0===r.length?null:n[r[0]]}const u=(e,t="asc")=>{const n="asc"===t?(e,t)=>(0,s._)(e,t):(e,t)=>(0,s._)(t,e);return e.sort((e,t)=>{const r=c(e);if(!r)return 0;const i=c(t);return i?n(r,i):0})},d=(e,t,n="asc")=>{const r=i.fieldReducers.get(t),a=e.map(e=>{var n;const a=e.fields[1];if(!a)return{value:0,dataFrame:e};var o;var s;return{value:null!==(s=(null!==(o=null===(n=r.reduce)||void 0===n?void 0:n.call(r,a,!0,!0))&&void 0!==o?o:(0,i.doStandardCalcs)(a,!0,!0))[t])&&void 0!==s?s:0,dataFrame:e}});return a.sort("asc"===n?(e,t)=>e.value-t.value:(e,t)=>t.value-e.value),a.map(({dataFrame:e})=>e)},p=e=>{const t=(0,i.outerJoinDataFrames)({frames:e});if(!t)throw new Error("Error while joining frames into a single one");const n=t.fields.filter(e=>e.type===i.FieldType.number).map(e=>new Float64Array(e.values));return r.OutlierDetector.dbscan({sensitivity:.9}).detect(n)},m=(e,t)=>e.seriesResults[t].isOutlier?-e.seriesResults[t].outlierIntervals.length:0,h=(0,a.memoize)((e,t,n="asc")=>{if(!e.length)return[];const r=[...e];if("alphabetical"===t)return u(r,"asc");if("alphabetical-reversed"===t)return u(r,"desc");if("outliers"===t)try{return((e,t="asc")=>{if(!g())throw new Error("WASM not supported");const n=p(e),r=e.map((e,t)=>({value:m(n,t),dataFrame:e}));return r.sort("asc"===t?(e,t)=>e.value-t.value:(e,t)=>t.value-e.value),r.map(({dataFrame:e})=>e)})(r,n)}catch(e){const t=`Error while sorting by outlying series: "${e.toString()}"!`;return(0,o.HA)([t,"Falling back to standard deviation to identify the most variable series."]),d(r,i.ReducerID.stdDev,n)}return d(r,t,n)},(e,t,n="asc")=>{const r=f(e)?e[0].fields[0].values[0]:0,i=f(e)?e[e.length-1].fields[0].values[e[e.length-1].fields[0].values.length-1]:0;return`${e.length>0?c(e[0]):""}_${e.length>0?c(e[e.length-1]):""}_${r}_${i}_${e.length}_${t}_${n}`});function f(e){return e.length>0&&e[0].fields.length>0&&e[0].fields[0].values.length>0}const g=()=>{const e="object"==typeof WebAssembly;return e||(0,l.z)("wasm_not_supported",{}),e}},1053:(e,t,n)=>{n.d(t,{_:()=>s});var r=n(6089),i=n(2007),a=n(5959),o=n.n(a);function s({title:e,description:t}){const n=(0,i.useStyles2)(l);return o().createElement("h6",{className:n.title},o().createElement("span",null,e),o().createElement(i.Tooltip,{content:t,placement:"top"},o().createElement(i.Icon,{name:"info-circle",size:"sm",className:n.infoIcon})))}function l(e){return{title:(0,r.css)({fontSize:"15px",fontWeight:e.typography.fontWeightLight,borderBottom:`1px solid ${e.colors.border.weak}`,paddingBottom:e.spacing(.5)}),infoIcon:(0,r.css)({marginLeft:e.spacing(1),cursor:"pointer",color:e.colors.text.secondary,position:"relative",top:"-4px"})}}},1522:(e,t,n)=>{n.d(t,{n:()=>o});var r=n(5959),i=n(2445);function a(e,t){return e instanceof Error?e:"string"==typeof e?new Error(e):"string"==typeof e.message?new Error(e.message):new Error(t)}function o(){const[e,t]=(0,r.useState)();return(0,r.useEffect)(()=>{const e=e=>{(function(e){var t,n,r,a;if(e.filename&&new URL(e.filename).protocol.endsWith("extension:"))return i.v.error(new Error(`Browser extension error: ${e.message}`),{filename:e.filename,lineno:null===(t=e.lineno)||void 0===t?void 0:t.toString(),colno:null===(n=e.colno)||void 0===n?void 0:n.toString()}),!1;return null!==e.error||!e.message||(i.v.error(new Error(`Non-critical error: ${e.message}`),{filename:e.filename,lineno:null===(r=e.lineno)||void 0===r?void 0:r.toString(),colno:null===(a=e.colno)||void 0===a?void 0:a.toString()}),!1)})(e)&&t(a(e.error,"Uncaught exception!"))},n=e=>{"cancelled"!==e.reason.type?t(a(e.reason,"Unhandled rejection!")):t(void 0)};return window.addEventListener("error",e),window.addEventListener("unhandledrejection",n),()=>{window.removeEventListener("unhandledrejection",n),window.removeEventListener("error",e)}},[]),[e,t]}},2127:(e,t,n)=>{n.d(t,{Ao:()=>l,Az:()=>f,EY:()=>m,GH:()=>v,H0:()=>O,Kf:()=>g,OO:()=>S,QX:()=>s,Rp:()=>d,gR:()=>h,hc:()=>b,td:()=>y,ui:()=>c,ym:()=>w,yr:()=>p});var r=n(7781),i=n(7985),a=n(2245);function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const s="/explore/metrics",l="filters",c="${filters}",u="metric",d="${metric}",p="groupby",m="ds",h="${ds}",f="logsDs",g="${logsDs}",b="other_metric_filters",y="$__logs__",v={uid:h};function w(e){return[new i.x0({name:u,value:e,hide:a.zL.hideVariable})]}class S extends r.BusEventWithPayload{}o(S,"type","metric-selected-event");class O extends r.BusEventBase{}o(O,"type","refresh-metrics-event")},2425:(e,t,n)=>{n.d(t,{yn:()=>S,_r:()=>E});var r=n(7781),i=n(7985),a=n(3241),o=n(8531),s=n(2007),l=n(5959),c=n.n(l),u=n(4137),d=n(2127),p=n(4796);var m=n(350),h=n(416),f=n(2728);function g(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const b="grafana.trails.recent";class y{_loadRecentTrailsFromStorage(){const e=[],t=f.x.getItem(b)||[];for(const n of t){const t=this._deserializeTrail(n);e.push(t.getRef())}return e}_loadBookmarksFromStorage(){return(f.x.getItem(h.V.BOOKMARKS)||[]).map(e=>{if(null!=(t=e)&&"object"==typeof t&&"history"in t){const t=null!=e.currentStep?e.currentStep:e.history.length-1;return{urlValues:e.history[t].urlValues,createdAt:e.createdAt||Date.now()}}var t;return e})}_deserializeTrail(e){const t=new m.b({}),n="urlValues"in e;return"history"in e?e.history.forEach(e=>{this._loadFromUrl(t,e.urlValues)}):n&&this._loadFromUrl(t,e.urlValues),t.setState(i.Go.cloneSceneObjectState(t.state,{})),t}_serializeTrail(e){return{urlValues:i.Go.getUrlState(e)}}getTrailForBookmarkIndex(e){const t=this._bookmarks[e];return t?this.getTrailForBookmark(t):(0,p.ef)()}getTrailForBookmark(e){const t=S(e);for(const e of this._recent){const n=e.resolve();if(S(n)===t)return n}const n=new m.b({});return this._loadFromUrl(n,e.urlValues),n}_loadFromUrl(e,t){const n=r.urlUtil.renderUrl("",t);i.Go.syncStateFromSearchParams(e,new URLSearchParams(n))}get recent(){return this._recent}get lastModified(){return this._lastModified}load(){this._recent=this._loadRecentTrailsFromStorage(),this._bookmarks=this._loadBookmarksFromStorage(),this._refreshBookmarkIndexMap(),this._lastModified=Date.now()}setRecentTrail(e){if(!e.state.trailActivated)return;this._recent=this._recent.filter(t=>t!==e.getRef());const t=v(e);this._recent=this._recent.filter(e=>{const n=v(e.resolve());return!(0,a.isEqual)(t,n)}),this._recent.unshift(e.getRef()),this._save()}get bookmarks(){return this._bookmarks}addBookmark(e){const t={urlValues:i.Go.getUrlState(e),createdAt:Date.now()};this._bookmarks.unshift(t),this._refreshBookmarkIndexMap(),this._save(),function(){const e=(0,o.getAppEvents)(),t=(0,p.y)(u.bw.Drilldown),n=t?c().createElement("i",null,"the Metrics Reducer sidebar"):c().createElement("i",null,"Drilldown > Metrics");e.publish({type:r.AppEvents.alertSuccess.name,payload:["Bookmark created",c().createElement(s.Stack,{gap:2,direction:"row",key:"bookmark-notification"},c().createElement("div",null,"You can view bookmarks under ",n),!t&&c().createElement(s.LinkButton,{fill:"solid",variant:"secondary",href:d.QX},"View bookmarks"))]})}()}removeBookmark(e){e<this._bookmarks.length&&(this._bookmarks.splice(e,1),this._refreshBookmarkIndexMap(),this._save())}getBookmarkIndex(e){const t=S(e);return this._bookmarkIndexMap.get(t)}_refreshBookmarkIndexMap(){this._bookmarkIndexMap.clear(),this._bookmarks.forEach((e,t)=>{const n=S(e);this._bookmarkIndexMap.set(n,t)})}constructor(){g(this,"_recent",[]),g(this,"_bookmarks",[]),g(this,"_save",void 0),g(this,"_lastModified",void 0),g(this,"_bookmarkIndexMap",new Map),this.load(),this._lastModified=Date.now();const e=()=>{const e=this._recent.slice(0,20).map(e=>this._serializeTrail(e.resolve()));f.x.setItem(b,e),f.x.setItem(h.V.BOOKMARKS,this._bookmarks),this._lastModified=Date.now()};this._save=(0,a.debounce)(e,1e3),window.addEventListener("beforeunload",()=>{this._save=e})}}function v(e){const t=i.Go.getUrlState(e);return w(t),t}function w(e){var t;(delete e.actionView,delete e.layout,delete e.metricSearch,delete e.refresh,""!==e["var-groupby"]&&void 0!==e["var-groupby"]||(e["var-groupby"]="$__all"),"string"!=typeof e["var-filters"])&&(e["var-filters"]=null===(t=e["var-filters"])||void 0===t?void 0:t.filter(e=>""!==e));return e}function S(e){return e instanceof m.b?JSON.stringify(v(e)):JSON.stringify(w(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){g(e,t,n[t])})}return e}({},e.urlValues)))}let O;function E(){return O||(O=new y),O}},2728:(e,t,n)=>{n.d(t,{x:()=>o});var r=n(3347),i=n(2533),a=n(416);const o=new class{migrate(){let e=!1;const t=[{legacyKey:"metricsDrilldownDataSource",newKey:a.V.DATASOURCE},{legacyKey:"metrics-drilldown-recent-metrics/v1",newKey:a.V.RECENT_METRICS},{legacyKey:"grafana.trails.bookmarks",newKey:a.V.BOOKMARKS},{legacyKey:"grafana.trails.breakdown.sort.labels.by",newKey:a.V.BREAKDOWN_SORTBY}];for(const{legacyKey:n,newKey:r}of t){let t=localStorage.getItem(n);if(null!==t){try{t=JSON.parse(t)}catch(e){}this.setItem(r,t),localStorage.removeItem(n),e=!0}}e&&(0,r.z)("user_preferences_migrated",{})}buildStorageKey(e){return`${this.service}.${e}`}getItem(e){const t=this.buildStorageKey(e),n=localStorage.getItem(t);return null===n?null:JSON.parse(n)}setItem(e,t){const n=this.buildStorageKey(e);localStorage.setItem(n,JSON.stringify(t))}removeItem(e){const t=this.buildStorageKey(e);localStorage.removeItem(t)}clear(){localStorage.clear()}constructor(e){var t,n,r;r=void 0,(n="service")in(t=this)?Object.defineProperty(t,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[n]=r,this.service=e}}(i.id)},2745:(e,t,n)=>{n.d(t,{J:()=>s,w:()=>l});var r=n(8531),i=n(5959),a=n(4796),o=n(5521);const s=(0,i.createContext)({trail:(0,a.ef)(),goToUrlForTrail:()=>{}});function l(){const[e,t]=(0,i.useState)((0,a.ef)()),n=e=>{r.locationService.push((0,a.xi)(e)),t(e)};return(0,i.useEffect)(()=>{const e=o.C.subscribe(e=>{n(e)});return()=>e()},[]),{trail:e,goToUrlForTrail:n}}},2993:(e,t,n)=>{n.d(t,{E:()=>c});var r=n(6089),i=n(2007),a=n(5959),o=n.n(a),s=n(1159),l=n(6503);function c({error:e}){const t=(0,i.useStyles2)(u),n=(0,s.useNavigate)(),{pathname:r,search:c}=(0,s.useLocation)(),d=(0,a.useCallback)(()=>{const e=new URLSearchParams(c),t=new URLSearchParams;["from","to","timezone"].filter(t=>e.has(t)).forEach(n=>t.set(n,e.get(n))),n({pathname:r,search:t.toString()}),window.location.reload()},[n,r,c]),[p,m]=(0,a.useState)(!1);return o().createElement("div",{className:t.container},o().createElement(l._,{severity:"error",title:"Fatal error!",error:e,errorContext:{handheldBy:"React error boundary"},message:o().createElement(o().Fragment,null,o().createElement("p",{className:t.message},"Please"," ",o().createElement(i.TextLink,{href:"#",onClick:d},"try reloading the page")," ","or, if the problem persists, contact your organization admin. Sorry for the inconvenience."),o().createElement("p",null,o().createElement(i.Collapse,{className:t.callStack,collapsible:!0,label:"View stack trace",isOpen:p,onToggle:()=>m(!p)},o().createElement("pre",null,o().createElement("code",null,e.stack)))))}))}function u(e){return{container:(0,r.css)({margin:e.spacing(2)}),message:(0,r.css)({margin:e.spacing(2,0,1,0)}),callStack:(0,r.css)({backgroundColor:"transparent",border:"0 none","& button":(0,r.css)({paddingLeft:e.spacing(1.5)}),"& button:focus":(0,r.css)({outline:"none",boxShadow:"none"}),"& button > svg":(0,r.css)({marginLeft:e.spacing(-2),marginRight:e.spacing(.5)}),'& [class$="collapse__loader"]':(0,r.css)({display:"none"})})}}},3347:(e,t,n)=>{n.d(t,{h:()=>d,z:()=>c});var r=n(8531),i=n(4137),a=n(5176);function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}const l="grafana_explore_metrics_";function c(e,t){(0,r.reportInteraction)(`${l}${e}`,s(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){o(e,t,n[t])})}return e}({},t),{meta:{appRelease:r.config.apps[i.s_].version,appVersion:a.t}}))}function u(e,t){c("label_filter_changed",{label:e,action:t,cause:"adhoc_filter"})}function d(e,t){e.length===t.length?function(e,t){for(const n of t)for(const t of e)n.key===t.key&&n.value!==t.value&&u(n.key,"changed")}(e,t):e.length<t.length?function(e,t){for(const n of t)e.some(e=>e.key===n.key)||u(n.key,"removed")}(e,t):function(e,t){for(const n of e)!t.some(e=>e.key===n.key)&&u(n.key,"added")}(e,t)}},3616:(e,t,n)=>{n.d(t,{HA:()=>c,jx:()=>l,qq:()=>u});var r=n(7781),i=n(8531),a=n(2445);function o(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function s(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function l(e,t){const n=t.reduce((e,t,n)=>s(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){o(e,t,n[t])})}return e}({},e),{[`info${n+1}`]:t}),{handheldBy:"displayError"});a.v.error(e,n),(0,i.getAppEvents)().publish({type:r.AppEvents.alertError.name,payload:t})}function c(e){a.v.warn(e),(0,i.getAppEvents)().publish({type:r.AppEvents.alertWarning.name,payload:e})}function u(e){(0,i.getAppEvents)().publish({type:r.AppEvents.alertSuccess.name,payload:e})}},4796:(e,t,n)=>{n.d(t,{y:()=>S,UX:()=>P,Vy:()=>E,aO:()=>O,kj:()=>b,KE:()=>y,xi:()=>w,FG:()=>k,ef:()=>v});var r=n(7781),i=n(8531),a=n(7985),o=n(2445),s=n(4137),l=n(350),c=n(2127);function u(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function d(){return new p}class p extends a.Bs{getSelectedScopes(){return this.selectedScopes}getSelectedScopesNames(){return this.selectedScopes.map(({scope:e})=>e.metadata.name)}setSelectedScopes(e){this.selectedScopes=e,this.notifySubscribers()}onScopesChange(e){return this.onScopesChangeCallbacks.push(e),()=>{this.onScopesChangeCallbacks=this.onScopesChangeCallbacks.filter(t=>t!==e)}}notifySubscribers(){for(const e of this.onScopesChangeCallbacks)e(this.selectedScopes)}get value(){return[]}constructor(){super({}),u(this,"selectedScopes",[]),u(this,"onScopesChangeCallbacks",[])}}var m=n(384);function h(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function f(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var a=e.apply(t,n);function o(e){h(a,r,i,o,s,"next",e)}function s(e){h(a,r,i,o,s,"throw",e)}o(void 0)})}}function g(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function b(e){return a.jh.getAncestor(e,l.b)}function y(e){return a.jh.getAncestor(e,l.b).state.settings}function v(e){var t,n;return new l.b(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){g(e,t,n[t])})}return e}({initialDS:null==e?void 0:e.initialDS,$timeRange:null!==(t=null==e?void 0:e.$timeRange)&&void 0!==t?t:new a.JZ({from:"now-1h",to:"now"}),embedded:null!==(n=null==e?void 0:e.embedded)&&void 0!==n&&n},e))}function w(e){const t=a.Go.getUrlState(e);return r.urlUtil.renderUrl(s.bw.Drilldown,t)}function S(e){return window.location.pathname.includes(e)}function O(e){return e?e===c.td?"Logs":e:"All metrics"}function E(e){const t=i.config.theme2.visualization;return t.getColorByName(t.palette[e%8])}const x=1e4;function k(e,t,n){(0,m.BE)(t)&&t.setState({getTagKeysProvider:()=>f(function*(){var r;const i={filters:t.state.filters,scopes:null===(r=d())||void 0===r?void 0:r.value,queries:t.state.useQueriesAsFilterForOptions?e.getQueries():[]};return i.queries.length>20&&(i.queries=[]),{replace:!0,values:(yield n.getTagKeys(i)).slice(0,x)}})(),getTagValuesProvider:(r,i)=>f(function*(){var r;const a=t.state.filters.filter(e=>e.key!==i.key),o={key:i.key,filters:a,scopes:null===(r=d())||void 0===r?void 0:r.value,queries:t.state.useQueriesAsFilterForOptions?e.getQueries():[]};o.queries.length>20&&(o.queries=[]);return{replace:!0,values:(yield n.getTagValues(o)).slice(0,x)}})()})}function P(e,t,n){const r=a.jh.findObject(e,t);return r instanceof n?r:(null!==r&&o.v.warn(`invalid return type: ${n.toString()}`),null)}},4964:(e,t,n)=>{n.d(t,{_:()=>r});const r=new Intl.Collator("en",{sensitivity:"base"}).compare},5521:(e,t,n)=>{n.d(t,{O:()=>v,C:()=>y});var r=n(6089),i=n(7985),a=n(2007),o=n(5959),s=n.n(o),l=n(1053),c=n(7781),u=n(2127),d=n(2425),p=n(4796),m=n(384);const h=(e,t,n)=>e.length+2+t.length>n?t.substring(0,n-e.length-5)+"...":t;function f(e){const{onSelect:t,onDelete:n,bookmark:r}=e,l=(0,a.useStyles2)(g),f=(0,o.useMemo)(()=>{let t=e.trail||r&&(0,d._r)().getTrailForBookmark(r);if(!t)return null;const n=i.jh.lookupVariable(u.Ao,t);if(!(0,m.BE)(n))return null;const a=(null==r?void 0:r.createdAt)||t.state.createdAt;return{filters:n.state.filters,metric:t.state.metric,createdAt:a}},[e.trail,r]);if(!f)return null;const{filters:b,metric:y,createdAt:v}=f,w=h("",(0,p.aO)(y),27),S=`${e.compactHeight&&b.length>0?l.cardTall:""}`,O=`${l.card} ${e.wide?l.cardWide:""} ${S}`;return s().createElement("article",{"data-testid":`data-trail-card ${w}`},s().createElement(a.Card,{onClick:t,className:O},s().createElement(a.Card.Heading,null,s().createElement("div",{className:l.metricValue},w)),s().createElement(a.Card.Meta,{className:l.meta},b.map(e=>s().createElement("span",{key:e.key},s().createElement("div",{className:l.secondaryFont},e.key,": "),s().createElement("div",{className:l.primaryFont},h(e.key,e.value,44))))),s().createElement("div",{className:l.deleteButton},n&&s().createElement(a.Card.SecondaryActions,null,s().createElement(a.IconButton,{key:"delete",name:"trash-alt",className:l.secondary,tooltip:"Remove bookmark",onClick:n,"data-testid":"deleteButton"})))),s().createElement("div",{className:l.date},s().createElement("div",{className:l.secondaryFont},"Date created: "),s().createElement("div",{className:l.primaryFont},v>0&&(0,c.dateTimeFormat)(v,{format:"YYYY-MM-DD"}))))}function g(e){return{metricValue:(0,r.css)({display:"inline",color:e.colors.text.primary,fontWeight:500,wordBreak:"break-all"}),card:(0,r.css)({position:"relative",width:"318px",padding:`12px ${e.spacing(2)} ${e.spacing(1)} ${e.spacing(2)}`,alignItems:"start",marginBottom:0,borderTop:`1px solid ${e.colors.border.weak}`,borderRight:`1px solid ${e.colors.border.weak}`,borderLeft:`1px solid ${e.colors.border.weak}`,borderBottom:"none",borderRadius:"2px 2px 0 0"}),cardWide:(0,r.css)({width:"100%"}),cardTall:(0,r.css)({height:"110px"}),secondary:(0,r.css)({color:e.colors.text.secondary,fontSize:"12px"}),date:(0,r.css)({border:`1px solid ${e.colors.border.weak}`,borderRadius:"0 0 2px 2px",padding:`${e.spacing(1)} ${e.spacing(2)}`,backgroundColor:e.colors.background.primary}),meta:(0,r.css)({flexWrap:"wrap",overflow:"hidden",textOverflow:"ellipsis",maxHeight:"36px",margin:0,gridArea:"Meta",color:e.colors.text.secondary,whiteSpace:"nowrap"}),primaryFont:(0,r.css)({display:"inline",color:e.colors.text.primary,fontSize:"12px",fontWeight:"500",letterSpacing:"0.018px"}),secondaryFont:(0,r.css)({display:"inline",color:e.colors.text.secondary,fontSize:"12px",fontWeight:"400",lineHeight:"18px",letterSpacing:"0.018px"}),deleteButton:(0,r.css)({position:"absolute",bottom:e.spacing(1),right:e.spacing(1)})}}var b=n(3347);const y={listeners:new Set,emit:function(e){this.listeners.forEach(t=>t(e))},subscribe:function(e){return this.listeners.add(e),()=>{this.listeners.delete(e)}}};class v extends i.Bs{onActivate(){}constructor({key:e,title:t,description:n,icon:r,disabled:i}){super({key:e,title:t,description:n,icon:r,disabled:null!=i&&i,active:!1}),this.addActivationHandler(this.onActivate.bind(this))}}var w,S,O;function E(e){return{container:(0,r.css)({display:"flex",flexDirection:"column",gap:e.spacing(1),height:"100%"}),bookmarksList:(0,r.css)({display:"flex",flexDirection:"column",gap:e.spacing(1.5),overflowY:"auto",paddingRight:e.spacing(1)}),emptyState:(0,r.css)({display:"flex",justifyContent:"center",alignItems:"center",height:"100px",color:e.colors.text.secondary,fontStyle:"italic"})}}O=({model:e})=>{const t=(0,a.useStyles2)(E),{title:n,description:r}=e.useState(),{bookmarks:i}=(0,d._r)(),[,c]=(0,o.useState)(Date.now()),u=e=>{(0,b.z)("exploration_started",{cause:"bookmark_clicked"});const t=(0,d._r)().getTrailForBookmarkIndex(e);(0,d._r)().setRecentTrail(t),function(e){y.emit(e)}(t)};return s().createElement("div",{className:t.container},s().createElement(l._,{title:n,description:r,"data-testid":"bookmarks-list-sidebar"}),i.length>0?s().createElement("div",{className:t.bookmarksList},i.map((e,t)=>s().createElement(f,{key:(0,d.yn)(e),bookmark:e,onSelect:()=>u(t),onDelete:()=>(e=>{(0,b.z)("bookmark_changed",{action:"deleted"}),(0,d._r)().removeBookmark(e),c(Date.now())})(t),wide:!0,compactHeight:!0}))):s().createElement("div",{className:t.emptyState},"No bookmarks yet"))},(S="Component")in(w=v)?Object.defineProperty(w,S,{value:O,enumerable:!0,configurable:!0,writable:!0}):w[S]=O},6503:(e,t,n)=>{n.d(t,{_:()=>c});var r=n(2007),i=n(5959),a=n.n(i),o=n(2445);function s(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function l(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),n.push.apply(n,r)}return n}(Object(t)).forEach(function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))}),e}function c({severity:e,title:t,message:n,error:i,errorContext:c,children:u}){let d;return i&&(d="string"==typeof i?new Error(i):i,o.v.error(d,l(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter(function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable}))),r.forEach(function(t){s(e,t,n[t])})}return e}({},d.cause||{},c),{bannerTitle:t}))),a().createElement(r.Alert,{title:t,severity:e},d&&a().createElement(a().Fragment,null,d.message||d.toString(),a().createElement("br",null)),n,u)}},7476:(e,t,n)=>{n.d(t,{aQ:()=>c,tS:()=>p});var r=n(8531),i=n(2445);function a(e,t,n,r,i,a,o){try{var s=e[a](o),l=s.value}catch(e){return void n(e)}s.done?t(l):Promise.resolve(l).then(r,i)}function o(e){return function(){var t=this,n=arguments;return new Promise(function(r,i){var o=e.apply(t,n);function s(e){a(o,r,i,s,l,"next",e)}function l(e){a(o,r,i,s,l,"throw",e)}s(void 0)})}}function s(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const l=/^grafana-[0-9a-z]+prometheus-datasource$/;function c(e){return"object"==typeof e&&null!==e&&"type"in e&&"string"==typeof e.type&&("prometheus"===e.type||l.test(e.type))&&"uid"in e&&"string"==typeof e.uid}class u{getHealthyDataSources(e){return o(function*(){const t=this.cache.get(e);if(null==t?void 0:t.length)return t;let n=this.pendingRequests.get(e);n||(n=this.fetchHealthyDataSources(e).finally(()=>{this.pendingRequests.delete(e)}),this.pendingRequests.set(e,n));const r=yield n;return this.cache.set(e,r),r}).call(this)}fetchHealthyDataSources(e){return o(function*(){const t=(0,r.getDataSourceSrv)().getList({logs:!0,type:e,filter:e=>"grafana"!==e.uid}),n=[],a=[];return yield Promise.all(t.map(e=>o(function*(){try{const t=yield(0,r.getBackendSrv)().get(`/api/datasources/uid/${e.uid}/health`,void 0,void 0,{showSuccessAlert:!1,showErrorAlert:!1});"OK"===(null==t?void 0:t.status)?n.push(e):a.push(e)}catch(t){a.push(e)}})())),a.length>0&&i.v.warn(`Found ${a.length} unhealthy ${e} data sources: ${a.map(e=>e.name).join(", ")}`),n})()}constructor(){s(this,"pendingRequests",new Map),s(this,"cache",new Map)}}let d;function p(){return d||(d=new u),d}},8732:(e,t,n)=>{n.d(t,{A:()=>l,S:()=>u});var r=n(5959),i=n.n(r),a=n(1159),o=n(4137),s=n(2745);const l=(0,r.lazy)(()=>n.e(78).then(n.bind(n,9078))),c=()=>{const e=(0,a.useLocation)();return i().createElement(a.Navigate,{to:`${o.bw.Drilldown}${e.search}`,replace:!0})},u=()=>{const{trail:e}=(0,r.useContext)(s.J);return i().createElement(a.Routes,null,i().createElement(a.Route,{path:o.bw.Drilldown,element:i().createElement(l,{trail:e})}),i().createElement(a.Route,{path:o.bw.Trail,element:i().createElement(c,null)}),i().createElement(a.Route,{path:"*",element:i().createElement(a.Navigate,{to:o.bw.Drilldown,replace:!0})}))}}}]);
//# sourceMappingURL=836.js.map?_cache=f761d9106921fb42c0b4