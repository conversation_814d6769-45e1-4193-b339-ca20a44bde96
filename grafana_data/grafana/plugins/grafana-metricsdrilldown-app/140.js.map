{"version": 3, "file": "140.js?_cache=c0ad27c56f2b39e3d31e", "mappings": "gRAqBA,SAASA,EAAYC,GACnB,MAAoB,iBAATA,GAAqBC,EAAAA,SAASC,aAAaF,GAE7CA,EAGFC,EAAAA,SAASE,WAAW,IAAIC,KAAKJ,GAAO,CAAEK,SAAS,IAAUC,aAClE,CCTA,MA8BA,EA9BuB,EAAGC,QAAOC,eAAcC,aAAYC,iBACzD,MAAOC,IAASC,EAAAA,EAAAA,MACV,OAAEC,EAAM,OAAEC,IAAWC,EAAAA,EAAAA,IAAiBR,GACtCS,GAAQC,EAAAA,EAAAA,IAAgB,CAC5BJ,SACAK,UAAWR,EAAWS,IACtBC,eAAgBN,EAAOO,IAAI,EAAGC,QAAOC,KAAIC,YAAa,CACpDC,IAAKH,EACLI,SAAUH,EACVC,WAEFG,YDsB6BC,ECtBApB,EDsBiCqB,ECtBnBpB,EDuBtC,IAAIqB,EAAAA,GAAe,CAAEF,KAAM7B,EAAY6B,GAAOC,GAAI9B,EAAY8B,MCtBnEE,UAAU,IDqBP,IAA0BH,EAAiCC,EClBhE,MAAMG,GAAUC,EAAAA,EAAAA,SAAO,GAQvB,OAPAC,EAAAA,EAAAA,WAAU,KACHF,EAAQG,UACXH,EAAQG,SAAU,GAClBC,EAAAA,EAAAA,GAAqB,2BAA4B,CAAEC,UAAW,sBAE/D,IAGD,kBAACC,MAAAA,CAAIC,cAAY,8CACd5B,EAAQ,kBAAC6B,EAAAA,EAASA,CAAC7B,MAAOA,IAAY,kBAAC8B,EAAAA,EAAOA,CAACzB,MAAOA,K", "sources": ["webpack://grafana-metricsdrilldown-app/./utils/utils.timerange.ts", "webpack://grafana-metricsdrilldown-app/./exposedComponents/LabelBreakdown/LabelBreakdown.tsx"], "sourcesContent": ["import { dateMath } from '@grafana/data';\nimport { SceneTimeRange } from '@grafana/scenes';\n\ntype MathStringOrUnixTimestamp = string | number;\n\n/**\n * Convert a time string or unix timestamp to a SceneTimeRange.\n *\n * @param time - The time string or unix timestamp.\n * @returns The SceneTimeRange.\n *\n * @example\n * ```ts\n * toSceneTime('now-1h')\n * ```\n *\n * @example\n * ```ts\n * toSceneTime(1723756800000)\n * ```\n */\nfunction toSceneTime(time: MathStringOrUnixTimestamp): string {\n  if (typeof time === 'string' && dateMath.isMathString(time)) {\n    // 'now', 'now-1h', etc.\n    return time;\n  }\n\n  return dateMath.toDateTime(new Date(time), { roundUp: false })!.toISOString();\n}\n\n/**\n * Convert a time string or unix timestamp to a SceneTimeRange.\n *\n * @param from - The start time.\n * @param to - The end time.\n * @returns The SceneTimeRange.\n *\n * @example\n * ```ts\n * toSceneTimeRange('now-1h', 'now')\n * ```\n *\n * @example\n * ```ts\n * toSceneTimeRange(1723756800000, 1723756800000)\n * ```\n *\n * @example\n * ```ts\n * toSceneTimeRange('now-1h', 1723756800000)\n * ```\n */\nexport function toSceneTimeRange(from: MathStringOrUnixTimestamp, to: MathStringOrUnixTimestamp): SceneTimeRange {\n  return new SceneTimeRange({ from: toSceneTime(from), to: toSceneTime(to) });\n}\n", "import { type DataSourceApi } from '@grafana/data';\nimport React, { useEffect, useRef } from 'react';\n\nimport { ErrorView } from 'App/ErrorView';\nimport { <PERSON><PERSON> } from 'App/Routes';\nimport { useCatchExceptions } from 'App/useCatchExceptions';\nimport { reportExploreMetrics } from 'interactions';\nimport { newMetricsTrail } from 'utils';\n\nimport { parsePromQLQuery } from '../../extensions/links';\nimport { toSceneTimeRange } from '../../utils/utils.timerange';\n\nexport interface LabelBreakdownProps {\n  query: string;\n  initialStart: string | number;\n  initialEnd: string | number;\n  dataSource: DataSourceApi;\n}\n\nconst LabelBreakdown = ({ query, initialStart, initialEnd, dataSource }: LabelBreakdownProps) => {\n  const [error] = useCatchExceptions();\n  const { metric, labels } = parsePromQLQuery(query);\n  const trail = newMetricsTrail({\n    metric,\n    initialDS: dataSource.uid,\n    initialFilters: labels.map(({ label, op, value }) => ({\n      key: label,\n      operator: op,\n      value,\n    })),\n    $timeRange: toSceneTimeRange(initialStart, initialEnd),\n    embedded: true,\n  });\n\n  const initRef = useRef(false);\n  useEffect(() => {\n    if (!initRef.current) {\n      initRef.current = true;\n      reportExploreMetrics('exposed_component_viewed', { component: 'label_breakdown' });\n    }\n  }, []);\n\n  return (\n    <div data-testid=\"metrics-drilldown-embedded-label-breakdown\">\n      {error ? <ErrorView error={error} /> : <Wingman trail={trail} />}\n    </div>\n  );\n};\n\nexport default LabelBreakdown;\n"], "names": ["toSceneTime", "time", "dateMath", "isMathString", "toDateTime", "Date", "roundUp", "toISOString", "query", "initialStart", "initialEnd", "dataSource", "error", "useCatchExceptions", "metric", "labels", "parsePromQLQuery", "trail", "newMetricsTrail", "initialDS", "uid", "initialFilters", "map", "label", "op", "value", "key", "operator", "$timeRange", "from", "to", "SceneTimeRange", "embedded", "initRef", "useRef", "useEffect", "current", "reportExploreMetrics", "component", "div", "data-testid", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Wingman"], "sourceRoot": ""}