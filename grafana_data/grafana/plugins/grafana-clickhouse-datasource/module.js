/* [create-plugin] version: 5.5.1 */
define(["@emotion/css","@grafana/data","@grafana/runtime","@grafana/ui","lodash","module","react","rxjs"],((e,t,s,r,a,n,o,l)=>(()=>{var i={6775:function(e,t){var s,r=function(){var e=function(e,t,s,r){for(s=s||{},r=e.length;r--;s[e[r]]=t);return s},t=[1,8],s=[1,4],r=[2,4],a=[1,11],n=[1,10],o=[2,16],l=[1,14],i=[1,15],c=[1,16],p=[6,8],m=[2,146],u=[1,19],b=[1,20],y=[16,33,35,36,37,38,39,40,41,42,45,46,50,51,54,55,57,58,60,76,79,81,82,83,84,86,87,88,101,195],d=[16,18,32,33,35,36,37,38,39,40,41,42,45,46,50,51,54,55,57,58,60,76,79,81,82,83,84,86,87,88,101,195],_=[2,160],$=[1,29],h=[6,8,14,17,146,150,152,154],f=[1,42],w=[1,60],x=[1,52],g=[1,59],v=[1,61],k=[1,62],E=[1,63],T=[1,64],O=[1,65],C=[1,58],A=[1,53],S=[1,54],I=[1,55],N=[1,56],L=[1,57],R=[1,43],D=[1,44],q=[1,45],j=[1,34],P=[1,66],B=[16,35,36,37,38,39,40,41,42,45,46,50,51,54,55,57,58,60,76,79,81,82,83,84,86,87,88,101,195],M=[6,8,14,17,150,152,154],F=[2,143],U=[1,75],H=[1,76],G=[6,8,14,17,43,133,138,144,146,150,152,154],V=[1,81],z=[1,78],W=[1,79],K=[1,80],Q=[1,82],Y=[6,8,14,17,36,43,49,50,51,71,72,74,77,89,107,124,125,126,127,129,133,135,138,141,142,144,146,150,152,154,157,164,165,167,168,173,177,179,180,182],X=[6,8,14,17,34,36,43,49,50,51,71,72,74,77,89,107,112,113,114,115,116,117,121,124,125,126,127,129,133,135,138,141,142,144,146,150,152,154,157,164,165,167,168,173,177,179,180,182],J=[1,103],Z=[1,101],ee=[1,102],te=[1,97],se=[1,98],re=[1,99],ae=[1,100],ne=[1,104],oe=[1,105],le=[1,106],ie=[1,107],ce=[1,108],pe=[1,109],me=[2,103],ue=[6,8,14,17,34,36,43,45,49,50,51,71,72,74,77,79,81,89,91,92,93,94,95,96,97,98,99,101,105,106,107,108,109,110,112,113,114,115,116,117,121,124,125,126,127,129,133,135,138,141,142,144,146,150,152,154,157,164,165,167,168,173,177,179,180,182],be=[6,8,14,17,34,36,43,45,49,50,51,71,72,74,77,79,81,89,91,92,93,94,95,96,97,98,99,101,103,105,106,107,108,109,110,112,113,114,115,116,117,121,124,125,126,127,129,133,135,138,141,142,144,146,150,152,154,157,164,165,167,168,173,177,179,180,182],ye=[1,110],de=[1,117],_e=[2,64],$e=[1,118],he=[16,35,37,38,39,40,41,42,45,46,50,51,54,55,57,58,60,76,79,81,82,83,84,86,87,88,101,195],fe=[16,29,35,50,51,54,55,57,58,60,76,79,81,82,83,84,86,87,88,119,195],we=[1,164],xe=[17,43],ge=[2,59],ve=[1,173],ke=[1,171],Ee=[1,172],Te=[6,8,138,146],Oe=[16,35,38,39,40,41,42,45,46,50,51,54,55,57,58,60,76,79,81,82,83,84,86,87,88,101,195],Ce=[6,8,14,17,138,144,146,150,152,154],Ae=[6,8,14,17,36,43,49,50,51,71,72,74,77,89,125,126,127,129,133,135,138,141,142,144,146,150,152,154,157,164,165,167,168,173,177,179,180,182],Se=[6,8,14,17,34,36,43,49,50,51,71,72,74,77,89,91,92,93,94,99,101,105,106,107,108,109,110,112,113,114,115,116,117,121,124,125,126,127,129,133,135,138,141,142,144,146,150,152,154,157,164,165,167,168,173,177,179,180,182],Ie=[6,8,14,17,34,36,43,49,50,51,71,72,74,77,79,81,89,91,92,93,94,99,101,105,106,107,108,109,110,112,113,114,115,116,117,121,124,125,126,127,129,133,135,138,141,142,144,146,150,152,154,157,164,165,167,168,173,177,179,180,182],Ne=[16,35,39,40,41,42,45,46,50,51,54,55,57,58,60,76,79,81,82,83,84,86,87,88,101,195],Le=[16,35,40,41,42,45,46,50,51,54,55,57,58,60,76,79,81,82,83,84,86,87,88,101,195],Re=[16,35,42,45,46,50,51,54,55,57,58,60,76,79,81,82,83,84,86,87,88,101,195],De=[71,74,77],qe=[16,35,45,46,50,51,54,55,57,58,60,76,79,81,82,83,84,86,87,88,101,195],je=[1,233],Pe=[1,234],Be=[6,8,14,17],Me=[6,8,14,17,43,157],Fe=[1,251],Ue=[1,247],He=[2,197],Ge=[1,256],Ve=[1,257],ze=[6,8,14,17,43,129,135,138,144,146,150,152,154,182],We=[1,259],Ke=[1,262],Qe=[1,263],Ye=[1,264],Xe=[1,265],Je=[2,174],Ze=[1,261],et=[6,8,14,17,36,43,89,129,135,138,144,146,150,152,154,164,165,167,168,173,177,179,180,182],tt=[6,8,14,17,36,43,89,129,135,138,144,146,150,152,154,164,165,167,168,173,177,179,180,182,192,193,194],st=[2,199],rt=[1,271],at=[6,8,14,17,135,138,144,146,150,152,154],nt=[1,280],ot=[2,179],lt=[170,173],it=[2,210],ct=[1,290],pt=[1,291],mt=[1,292],ut=[1,302],bt=[1,311],yt=[1,312],dt=[6,8,14,17,138,146,150,152,154],_t=[1,322],$t=[2,204],ht=[1,328],ft=[16,152],wt=[6,8,14,17,152,154],xt=[1,354],gt={trace:function(){},yy:{},symbols_:{error:2,main:3,selectClause:4,semicolonOpt:5,EOF:6,unionClause:7,";":8,unionClauseNotParenthesized:9,unionClauseParenthesized:10,order_by_opt:11,limit_opt:12,selectClauseParenthesized:13,UNION:14,distinctOpt:15,"(":16,")":17,SELECT:18,highPriorityOpt:19,maxStateMentTimeOpt:20,straightJoinOpt:21,sqlSmallResultOpt:22,sqlBigResultOpt:23,sqlBufferResultOpt:24,sqlCacheOpt:25,sqlCalcFoundRowsOpt:26,selectExprList:27,selectDataSetOpt:28,ALL:29,DISTINCT:30,DISTINCTROW:31,HIGH_PRIORITY:32,MAX_STATEMENT_TIME:33,"=":34,NUMERIC:35,STRAIGHT_JOIN:36,SQL_SMALL_RESULT:37,SQL_BIG_RESULT:38,SQL_BUFFER_RESULT:39,SQL_CACHE:40,SQL_NO_CACHE:41,SQL_CALC_FOUND_ROWS:42,",":43,selectExpr:44,"*":45,SELECT_EXPR_STAR:46,expr:47,selectExprAliasOpt:48,AS:49,IDENTIFIER:50,STRING:51,string:52,number:53,EXPONENT_NUMERIC:54,HEX_NUMERIC:55,boolean:56,TRUE:57,FALSE:58,null:59,NULL:60,literal:61,place_holder:62,function_call:63,function_call_param_list:64,function_call_param:65,identifier:66,DOT:67,identifier_list:68,case_expr_opt:69,when_then_list:70,WHEN:71,THEN:72,case_when_else:73,ELSE:74,case_when:75,CASE:76,END:77,simple_expr_prefix:78,"+":79,simple_expr:80,"-":81,"~":82,"!":83,BINARY:84,expr_list:85,ROW:86,EXISTS:87,"{":88,"}":89,bit_expr:90,"|":91,"&":92,"<<":93,">>":94,"/":95,DIV:96,MOD:97,"%":98,"^":99,not_opt:100,NOT:101,escape_opt:102,ESCAPE:103,predicate:104,IN:105,BETWEEN:106,AND:107,SOUNDS:108,LIKE:109,REGEXP:110,comparison_operator:111,">=":112,">":113,"<=":114,"<":115,"<>":116,"!=":117,sub_query_data_set_opt:118,ANY:119,boolean_primary:120,IS:121,boolean_extra:122,UNKNOWN:123,"&&":124,"||":125,OR:126,XOR:127,where_opt:128,WHERE:129,group_by_opt:130,group_by:131,roll_up_opt:132,WITH:133,ROLLUP:134,GROUP_BY:135,group_by_order_by_item_list:136,order_by:137,ORDER_BY:138,group_by_order_by_item:139,sort_opt:140,ASC:141,DESC:142,having_opt:143,HAVING:144,limit:145,LIMIT:146,OFFSET:147,procedure_opt:148,procedure:149,PROCEDURE:150,for_update_lock_in_share_mode_opt:151,FOR:152,UPDATE:153,LOCK:154,SHARE:155,MODE:156,FROM:157,table_references:158,partitionOpt:159,escaped_table_reference:160,table_reference:161,OJ:162,join_inner_cross:163,INNER:164,CROSS:165,left_right:166,LEFT:167,RIGHT:168,out_opt:169,OUTER:170,left_right_out_opt:171,join_table:172,JOIN:173,table_factor:174,join_condition:175,on_join_condition:176,NATURAL:177,join_condition_opt:178,ON:179,USING:180,partition_names:181,PARTITION:182,aliasOpt:183,index_or_key:184,INDEX:185,KEY:186,for_opt:187,identifier_list_opt:188,index_hint_list_opt:189,index_hint_list:190,index_hint:191,USE:192,IGNORE:193,FORCE:194,PLACE_HOLDER:195,$accept:0,$end:1},terminals_:{2:"error",6:"EOF",8:";",14:"UNION",16:"(",17:")",18:"SELECT",29:"ALL",30:"DISTINCT",31:"DISTINCTROW",32:"HIGH_PRIORITY",33:"MAX_STATEMENT_TIME",34:"=",35:"NUMERIC",36:"STRAIGHT_JOIN",37:"SQL_SMALL_RESULT",38:"SQL_BIG_RESULT",39:"SQL_BUFFER_RESULT",40:"SQL_CACHE",41:"SQL_NO_CACHE",42:"SQL_CALC_FOUND_ROWS",43:",",45:"*",46:"SELECT_EXPR_STAR",49:"AS",50:"IDENTIFIER",51:"STRING",54:"EXPONENT_NUMERIC",55:"HEX_NUMERIC",57:"TRUE",58:"FALSE",60:"NULL",67:"DOT",71:"WHEN",72:"THEN",74:"ELSE",76:"CASE",77:"END",79:"+",81:"-",82:"~",83:"!",84:"BINARY",86:"ROW",87:"EXISTS",88:"{",89:"}",91:"|",92:"&",93:"<<",94:">>",95:"/",96:"DIV",97:"MOD",98:"%",99:"^",101:"NOT",103:"ESCAPE",105:"IN",106:"BETWEEN",107:"AND",108:"SOUNDS",109:"LIKE",110:"REGEXP",112:">=",113:">",114:"<=",115:"<",116:"<>",117:"!=",119:"ANY",121:"IS",123:"UNKNOWN",124:"&&",125:"||",126:"OR",127:"XOR",129:"WHERE",133:"WITH",134:"ROLLUP",135:"GROUP_BY",138:"ORDER_BY",141:"ASC",142:"DESC",144:"HAVING",146:"LIMIT",147:"OFFSET",150:"PROCEDURE",152:"FOR",153:"UPDATE",154:"LOCK",155:"SHARE",156:"MODE",157:"FROM",162:"OJ",164:"INNER",165:"CROSS",167:"LEFT",168:"RIGHT",170:"OUTER",173:"JOIN",177:"NATURAL",179:"ON",180:"USING",182:"PARTITION",185:"INDEX",186:"KEY",192:"USE",193:"IGNORE",194:"FORCE",195:"PLACE_HOLDER"},productions_:[0,[3,3],[3,3],[5,1],[5,0],[7,1],[7,3],[10,4],[10,4],[13,3],[9,4],[9,4],[4,12],[15,1],[15,1],[15,1],[15,0],[19,1],[19,0],[20,3],[20,0],[21,1],[21,0],[22,1],[22,0],[23,1],[23,0],[24,1],[24,0],[25,0],[25,1],[25,1],[26,1],[26,0],[27,3],[27,1],[44,1],[44,1],[44,2],[48,0],[48,2],[48,1],[48,2],[48,1],[52,1],[53,1],[53,1],[53,1],[56,1],[56,1],[59,1],[61,1],[61,1],[61,1],[61,1],[61,1],[63,4],[64,3],[64,1],[65,0],[65,1],[65,1],[65,2],[65,1],[66,1],[66,3],[68,1],[68,3],[69,0],[69,1],[70,4],[70,5],[73,0],[73,2],[75,5],[78,2],[78,2],[78,2],[78,2],[78,2],[80,1],[80,1],[80,1],[80,1],[80,3],[80,4],[80,3],[80,4],[80,4],[80,1],[90,1],[90,3],[90,3],[90,3],[90,3],[90,3],[90,3],[90,3],[90,3],[90,3],[90,3],[90,3],[90,3],[100,0],[100,1],[102,0],[102,2],[104,1],[104,6],[104,6],[104,6],[104,4],[104,5],[104,4],[111,1],[111,1],[111,1],[111,1],[111,1],[111,1],[111,1],[118,1],[118,1],[120,1],[120,4],[120,3],[120,6],[122,1],[122,1],[47,1],[47,4],[47,2],[47,3],[47,3],[47,3],[47,3],[47,3],[85,1],[85,3],[128,0],[128,2],[130,0],[130,1],[132,0],[132,2],[131,3],[11,0],[11,1],[137,3],[136,1],[136,3],[139,2],[140,0],[140,1],[140,1],[143,0],[143,2],[145,2],[145,4],[145,4],[12,0],[12,1],[148,0],[148,1],[149,2],[151,0],[151,2],[151,4],[28,0],[28,10],[158,1],[158,3],[160,1],[160,4],[163,0],[163,1],[163,1],[166,1],[166,1],[169,0],[169,1],[171,0],[171,2],[172,4],[172,5],[172,4],[172,6],[172,5],[178,0],[178,1],[176,2],[175,1],[175,4],[161,1],[161,1],[181,1],[181,3],[159,0],[159,4],[183,0],[183,2],[183,1],[184,1],[184,1],[187,0],[187,2],[187,2],[187,2],[188,0],[188,1],[189,0],[189,1],[190,1],[190,3],[191,6],[191,6],[191,6],[174,4],[174,4],[174,3],[174,3],[62,1]],performAction:function(e,t,s,r,a,n,o){var l=n.length-1;switch(a){case 1:case 2:return{nodeType:"Main",value:n[l-2],hasSemicolon:n[l-1]};case 3:case 144:this.$=!0;break;case 4:this.$=!1;break;case 5:case 13:case 14:case 15:case 17:case 19:case 21:case 23:case 25:case 27:case 30:case 31:case 32:case 51:case 52:case 53:case 54:case 55:case 60:case 61:case 63:case 69:case 73:case 80:case 81:case 82:case 83:case 89:case 90:case 104:case 106:case 107:case 114:case 115:case 116:case 117:case 118:case 119:case 120:case 121:case 122:case 123:case 127:case 129:case 140:case 142:case 147:case 153:case 154:case 156:case 161:case 163:case 164:case 175:case 176:case 177:case 178:case 180:case 189:case 191:case 193:case 194:case 202:case 203:case 209:case 211:this.$=n[l];break;case 6:this.$=n[l-2],this.$.orderBy=n[l-1],this.$.limit=n[l];break;case 7:case 8:case 10:case 11:this.$={type:"Union",left:n[l-3],distinctOpt:n[l-1],right:n[l]};break;case 9:this.$={type:"SelectParenthesized",value:n[l-1]};break;case 12:this.$={type:"Select",distinctOpt:n[l-10],highPriorityOpt:n[l-9],maxStateMentTimeOpt:n[l-8],straightJoinOpt:n[l-7],sqlSmallResultOpt:n[l-6],sqlBigResultOpt:n[l-5],sqlBufferResultOpt:n[l-4],sqlCacheOpt:n[l-3],sqlCalcFoundRowsOpt:n[l-2],selectItems:n[l-1],from:n[l].from,partition:n[l].partition,where:n[l].where,groupBy:n[l].groupBy,having:n[l].having,orderBy:n[l].orderBy,limit:n[l].limit,procedure:n[l].procedure,updateLockMode:n[l].updateLockMode};break;case 16:case 18:case 20:case 22:case 24:case 26:case 28:case 29:case 33:case 59:case 68:case 72:case 103:case 105:case 139:case 141:case 143:case 146:case 152:case 155:case 160:case 162:case 165:case 174:case 179:case 188:case 197:case 204:case 208:case 210:this.$=null;break;case 34:n[l-2].value.push(n[l]);break;case 35:this.$={type:"SelectExpr",value:[n[l]]};break;case 36:case 37:case 64:this.$={type:"Identifier",value:n[l]};break;case 38:this.$=n[l-1],this.$.alias=n[l].alias,this.$.hasAs=n[l].hasAs;break;case 39:case 199:this.$={alias:null,hasAs:null};break;case 40:case 42:this.$={alias:n[l],hasAs:!0};break;case 41:this.$={alias:n[l],hasAs:!1};break;case 43:this.$={alias:n[$01],hasAs:!1};break;case 44:this.$={type:"String",value:n[l]};break;case 45:case 46:case 47:this.$={type:"Number",value:n[l]};break;case 48:this.$={type:"Boolean",value:"TRUE"};break;case 49:this.$={type:"Boolean",value:"FALSE"};break;case 50:this.$={type:"Null",value:"null"};break;case 56:this.$={type:"FunctionCall",name:n[l-3],params:n[l-1]};break;case 57:n[l-2].push(n[l]),this.$=n[l-2];break;case 58:case 149:case 195:this.$=[n[l]];break;case 62:this.$={type:"FunctionCallParam",distinctOpt:n[l-1],value:n[l]};break;case 65:this.$=n[l-2],n[l-2].value+="."+n[l];break;case 66:this.$={type:"IdentifierList",value:[n[l]]};break;case 67:case 171:this.$=n[l-2],n[l-2].value.push(n[l]);break;case 70:this.$={type:"WhenThenList",value:[{when:n[l-2],then:n[l]}]};break;case 71:this.$=n[l-4],this.$.value.push({when:n[l-2],then:n[l]});break;case 74:this.$={type:"CaseWhen",caseExprOpt:n[l-3],whenThenList:n[l-2],else:n[l-1]};break;case 75:case 76:case 77:case 78:case 79:this.$={type:"Prefix",prefix:n[l-1],value:n[l]};break;case 84:this.$={type:"SimpleExprParentheses",value:n[l-1]};break;case 85:this.$={type:"SimpleExprParentheses",value:n[l-2],hasRow:!0};break;case 86:this.$={type:"SubQuery",value:n[l-1]};break;case 87:this.$={type:"SubQuery",value:n[l-1],hasExists:!0};break;case 88:this.$={type:"IdentifierExpr",identifier:n[l-2],value:n[l-1]};break;case 91:this.$={type:"BitExpression",operator:"|",left:n[l-2],right:n[l]};break;case 92:this.$={type:"BitExpression",operator:"&",left:n[l-2],right:n[l]};break;case 93:this.$={type:"BitExpression",operator:"<<",left:n[l-2],right:n[l]};break;case 94:this.$={type:"BitExpression",operator:">>",left:n[l-2],right:n[l]};break;case 95:this.$={type:"BitExpression",operator:"+",left:n[l-2],right:n[l]};break;case 96:this.$={type:"BitExpression",operator:"-",left:n[l-2],right:n[l]};break;case 97:this.$={type:"BitExpression",operator:"*",left:n[l-2],right:n[l]};break;case 98:this.$={type:"BitExpression",operator:"/",left:n[l-2],right:n[l]};break;case 99:this.$={type:"BitExpression",operator:"DIV",left:n[l-2],right:n[l]};break;case 100:this.$={type:"BitExpression",operator:"MOD",left:n[l-2],right:n[l]};break;case 101:this.$={type:"BitExpression",operator:"%",left:n[l-2],right:n[l]};break;case 102:this.$={type:"BitExpression",operator:"^",left:n[l-2],right:n[l]};break;case 108:this.$={type:"InSubQueryPredicate",hasNot:n[l-4],left:n[l-5],right:n[l-1]};break;case 109:this.$={type:"InExpressionListPredicate",hasNot:n[l-4],left:n[l-5],right:n[l-1]};break;case 110:this.$={type:"BetweenPredicate",hasNot:n[l-4],left:n[l-5],right:{left:n[l-2],right:n[l]}};break;case 111:this.$={type:"SoundsLikePredicate",hasNot:!1,left:n[l-3],right:n[l]};break;case 112:this.$={type:"LikePredicate",hasNot:n[l-3],left:n[l-4],right:n[l-1],escape:n[l]};break;case 113:this.$={type:"RegexpPredicate",hasNot:n[l-2],left:n[l-3],right:n[l]};break;case 124:this.$={type:"IsNullBooleanPrimary",hasNot:n[l-1],value:n[l-3]};break;case 125:this.$={type:"ComparisonBooleanPrimary",left:n[l-2],operator:n[l-1],right:n[l]};break;case 126:this.$={type:"ComparisonSubQueryBooleanPrimary",operator:n[l-4],subQueryOpt:n[l-3],left:n[l-5],right:n[l-1]};break;case 128:this.$={type:"BooleanExtra",value:n[l]};break;case 130:this.$={type:"IsExpression",hasNot:n[l-1],left:n[l-3],right:n[l]};break;case 131:this.$={type:"NotExpression",value:n[l]};break;case 132:case 135:this.$={type:"AndExpression",operator:n[l-1],left:n[l-2],right:n[l]};break;case 133:case 134:this.$={type:"OrExpression",operator:n[l-1],left:n[l-2],right:n[l]};break;case 136:this.$={type:"XORExpression",left:n[l-2],right:n[l]};break;case 137:this.$={type:"ExpressionList",value:[n[l]]};break;case 138:case 213:this.$=n[l-2],this.$.value.push(n[l]);break;case 145:this.$={type:"GroupBy",value:n[l-1],rollUp:n[l]};break;case 148:this.$={type:"OrderBy",value:n[l-1],rollUp:n[l]};break;case 150:case 196:this.$=n[l-2],n[l-2].push(n[l]);break;case 151:this.$={type:"GroupByOrderByItem",value:n[l-1],sortOpt:n[l]};break;case 157:this.$={type:"Limit",value:[n[l]]};break;case 158:this.$={type:"Limit",value:[n[l-2],n[l]]};break;case 159:this.$={type:"Limit",value:[n[l],n[l-2]],offsetMode:!0};break;case 166:this.$=n[l-1]+" "+n[l];break;case 167:this.$=n[l-3]+" "+n[l-2]+" "+n[l-1]+" "+n[l];break;case 168:this.$={};break;case 169:this.$={from:n[l-8],partition:n[l-7],where:n[l-6],groupBy:n[l-5],having:n[l-4],orderBy:n[l-3],limit:n[l-2],procedure:n[l-1],updateLockMode:n[l]};break;case 170:this.$={type:"TableReferences",value:[n[l]]};break;case 172:this.$={type:"TableReference",value:n[l]};break;case 173:this.$={type:"TableReference",hasOj:!0,value:n[l-1]};break;case 181:this.$={leftRight:null,outOpt:null};break;case 182:this.$={leftRight:n[l-1],outOpt:n[l]};break;case 183:this.$={type:"InnerCrossJoinTable",innerCrossOpt:n[l-2],left:n[l-3],right:n[l],condition:null};break;case 184:this.$={type:"InnerCrossJoinTable",innerCrossOpt:n[l-3],left:n[l-4],right:n[l-1],condition:n[l]};break;case 185:this.$={type:"StraightJoinTable",left:n[l-3],right:n[l-1],condition:n[l]};break;case 186:this.$={type:"LeftRightJoinTable",leftRight:n[l-4],outOpt:n[l-3],left:n[l-5],right:n[l-1],condition:n[l]};break;case 187:this.$={type:"NaturalJoinTable",leftRight:n[l-2].leftRight,outOpt:n[l-2].outOpt,left:n[l-4],right:n[l]};break;case 190:this.$={type:"OnJoinCondition",value:n[l]};break;case 192:this.$={type:"UsingJoinCondition",value:n[l-1]};break;case 198:this.$={type:"Partitions",value:n[l-1]};break;case 200:this.$={hasAs:!0,alias:n[l]};break;case 201:this.$={hasAs:!1,alias:n[l]};break;case 205:case 206:case 207:this.$={type:"ForOptIndexHint",value:n[l]};break;case 212:this.$={type:"IndexHintList",value:[n[l]]};break;case 214:this.$={type:"UseIndexHint",value:n[l-1],forOpt:n[l-3],indexOrKey:n[l-4]};break;case 215:this.$={type:"IgnoreIndexHint",value:n[l-1],forOpt:n[l-3],indexOrKey:n[l-4]};break;case 216:this.$={type:"ForceIndexHint",value:n[l-1],forOpt:n[l-3],indexOrKey:n[l-4]};break;case 217:this.$={type:"TableFactor",value:n[l-3],partition:n[l-2],alias:n[l-1].alias,hasAs:n[l-1].hasAs,indexHintOpt:n[l]};break;case 218:this.$={type:"TableFactor",value:{type:"SubQuery",value:n[l-2]},alias:n[l].alias,hasAs:n[l].hasAs};break;case 219:this.$=n[l-1],this.$.hasParentheses=!0;break;case 220:this.$={type:"TableFactor",value:n[l-2],alias:n[l-1].alias,hasAs:n[l-1].hasAs,indexHintOpt:n[l]};break;case 221:this.$={type:"PlaceHolder",value:n[l],param:n[l].slice(2,-1)}}},table:[{3:1,4:2,7:3,9:5,10:6,13:7,16:t,18:s},{1:[3]},{5:9,6:r,8:a,14:n},{5:12,6:r,8:a},e([16,32,33,35,36,37,38,39,40,41,42,45,46,50,51,54,55,57,58,60,76,79,81,82,83,84,86,87,88,101,195],o,{15:13,29:l,30:i,31:c}),e(p,[2,5]),e([6,8,146],m,{11:17,137:18,138:u}),{14:b},{4:21,18:s},{6:[1,22]},{15:23,18:o,29:l,30:i,31:c},{6:[2,3]},{6:[1,24]},e(y,[2,18],{19:25,32:[1,26]}),e(d,[2,13]),e(d,[2,14]),e(d,[2,15]),e(p,_,{12:27,145:28,146:$}),e(h,[2,147]),{16:f,35:w,47:32,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,101:j,104:35,120:33,136:30,139:31,195:P},{15:67,16:o,29:l,30:i,31:c},{17:[1,68]},{1:[2,1]},{4:69,9:70,18:s},{1:[2,2]},e(B,[2,20],{20:71,33:[1,72]}),e(y,[2,17]),e(p,[2,6]),e(M,[2,161]),{35:[1,73]},e(h,F,{132:74,43:U,133:H}),e(G,[2,149]),e(G,[2,152],{140:77,107:V,124:z,125:W,126:K,127:Q,141:[1,83],142:[1,84]}),e(Y,[2,129],{111:86,34:[1,87],112:[1,88],113:[1,89],114:[1,90],115:[1,91],116:[1,92],117:[1,93],121:[1,85]}),{16:f,35:w,47:94,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,101:j,104:35,120:33,195:P},e(X,[2,123]),e(X,[2,107],{100:95,45:J,79:Z,81:ee,91:te,92:se,93:re,94:ae,95:ne,96:oe,97:le,98:ie,99:ce,101:pe,105:me,106:me,109:me,110:me,108:[1,96]}),e(ue,[2,90]),e(be,[2,80]),e(be,[2,81],{67:ye}),e(be,[2,82]),e(be,[2,83]),{4:112,16:f,18:s,35:w,47:113,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,85:111,86:R,87:D,88:q,90:36,101:j,104:35,120:33,195:P},{16:[1,114]},{16:[1,115]},{50:de,66:116},e(be,[2,89]),e(be,[2,51]),e(be,[2,52]),e(be,[2,53]),e(be,[2,54]),e(be,[2,55]),e([6,8,14,17,34,36,43,45,49,50,51,67,71,72,74,77,79,81,89,91,92,93,94,95,96,97,98,99,101,103,105,106,107,108,109,110,112,113,114,115,116,117,121,124,125,126,127,129,133,135,138,141,142,144,146,150,152,154,157,164,165,167,168,173,177,179,180,182,192,193,194],_e,{16:$e}),{16:f,35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:119,81:S,82:I,83:N,84:L,86:R,87:D,88:q,195:P},{16:f,35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:120,81:S,82:I,83:N,84:L,86:R,87:D,88:q,195:P},{16:f,35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:121,81:S,82:I,83:N,84:L,86:R,87:D,88:q,195:P},{16:f,35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:122,81:S,82:I,83:N,84:L,86:R,87:D,88:q,195:P},{16:f,35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:123,81:S,82:I,83:N,84:L,86:R,87:D,88:q,195:P},{16:f,35:w,47:125,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,69:124,71:[2,68],75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,101:j,104:35,120:33,195:P},e(be,[2,44]),e(be,[2,45]),e(be,[2,46]),e(be,[2,47]),e(be,[2,48]),e(be,[2,49]),e(be,[2,50]),e(be,[2,221]),{10:127,13:126,16:t},e([6,8,14,138,146],[2,9]),e(p,[2,10],{14:n}),e(p,[2,11]),e(he,[2,22],{21:128,36:[1,129]}),{34:[1,130]},e(M,[2,157],{43:[1,131],147:[1,132]}),e(h,[2,148]),{16:f,35:w,47:32,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,101:j,104:35,120:33,139:133,195:P},{134:[1,134]},e(G,[2,151]),{16:f,35:w,47:135,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,101:j,104:35,120:33,195:P},{16:f,35:w,47:136,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,101:j,104:35,120:33,195:P},{16:f,35:w,47:137,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,101:j,104:35,120:33,195:P},{16:f,35:w,47:138,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,101:j,104:35,120:33,195:P},{16:f,35:w,47:139,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,101:j,104:35,120:33,195:P},e(G,[2,153]),e(G,[2,154]),e([57,58,60,123],me,{100:140,101:pe}),{16:f,29:[1,143],35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,104:141,118:142,119:[1,144],195:P},e(fe,[2,114]),e(fe,[2,115]),e(fe,[2,116]),e(fe,[2,117]),e(fe,[2,118]),e(fe,[2,119]),e(fe,[2,120]),e(Y,[2,131]),{105:[1,145],106:[1,146],109:[1,147],110:[1,148]},{109:[1,149]},{16:f,35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:150,195:P},{16:f,35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:151,195:P},{16:f,35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:152,195:P},{16:f,35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:153,195:P},{16:f,35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:154,195:P},{16:f,35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:155,195:P},{16:f,35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:156,195:P},{16:f,35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:157,195:P},{16:f,35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:158,195:P},{16:f,35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:159,195:P},{16:f,35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:160,195:P},{16:f,35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:161,195:P},e([57,58,60,105,106,109,110,123],[2,104]),{50:[1,162]},{17:[1,163],43:we},{17:[1,165]},e(xe,[2,137],{107:V,124:z,125:W,126:K,127:Q}),{16:f,35:w,47:113,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,85:166,86:R,87:D,88:q,90:36,101:j,104:35,120:33,195:P},{4:167,18:s},{16:f,35:w,47:168,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,67:ye,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,101:j,104:35,120:33,195:P},e([6,8,14,16,17,35,36,43,50,51,54,55,57,58,60,67,76,79,81,82,83,84,86,87,88,89,101,129,135,138,144,146,150,152,154,164,165,167,168,173,177,179,180,182,192,193,194,195],_e),e(xe,ge,{120:33,104:35,90:36,80:37,61:38,66:39,63:40,78:41,75:46,52:47,53:48,56:49,59:50,62:51,64:169,65:170,47:174,16:f,30:ve,35:w,45:ke,46:Ee,50:x,51:g,54:v,55:k,57:E,58:T,60:O,76:C,79:A,81:S,82:I,83:N,84:L,86:R,87:D,88:q,101:j,195:P}),e(be,[2,75]),e(be,[2,76]),e(be,[2,77]),e(be,[2,78]),e(be,[2,79]),{70:175,71:[1,176]},{71:[2,69],107:V,124:z,125:W,126:K,127:Q},e(Te,[2,7],{14:b}),e(Te,[2,8]),e(Oe,[2,24],{22:177,37:[1,178]}),e(he,[2,21]),{35:[1,179]},{35:[1,180]},{35:[1,181]},e(G,[2,150]),e(Ce,[2,144]),e(Y,[2,132]),e(Ae,[2,133],{107:V,124:z}),e(Ae,[2,134],{107:V,124:z}),e(Y,[2,135]),e(Ae,[2,136],{107:V,124:z}),{56:184,57:E,58:T,60:[1,183],122:182,123:[1,185]},e(X,[2,125]),{16:[1,186]},{16:[2,121]},{16:[2,122]},{16:[1,187]},{16:f,35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:188,195:P},{16:f,35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:189,81:S,82:I,83:N,84:L,86:R,87:D,88:q,195:P},{16:f,35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:190,195:P},{16:f,35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:191,195:P},e([6,8,14,17,34,36,43,49,50,51,71,72,74,77,89,91,101,105,106,107,108,109,110,112,113,114,115,116,117,121,124,125,126,127,129,133,135,138,141,142,144,146,150,152,154,157,164,165,167,168,173,177,179,180,182],[2,91],{45:J,79:Z,81:ee,92:se,93:re,94:ae,95:ne,96:oe,97:le,98:ie,99:ce}),e([6,8,14,17,34,36,43,49,50,51,71,72,74,77,89,91,92,99,101,105,106,107,108,109,110,112,113,114,115,116,117,121,124,125,126,127,129,133,135,138,141,142,144,146,150,152,154,157,164,165,167,168,173,177,179,180,182],[2,92],{45:J,79:Z,81:ee,93:re,94:ae,95:ne,96:oe,97:le,98:ie}),e(Se,[2,93],{45:J,79:Z,81:ee,95:ne,96:oe,97:le,98:ie}),e(Se,[2,94],{45:J,79:Z,81:ee,95:ne,96:oe,97:le,98:ie}),e(Ie,[2,95],{45:J,95:ne,96:oe,97:le,98:ie}),e(Ie,[2,96],{45:J,95:ne,96:oe,97:le,98:ie}),e(ue,[2,97]),e(ue,[2,98]),e(ue,[2,99]),e(ue,[2,100]),e(ue,[2,101]),e([6,8,14,17,34,36,43,49,50,51,71,72,74,77,89,91,99,101,105,106,107,108,109,110,112,113,114,115,116,117,121,124,125,126,127,129,133,135,138,141,142,144,146,150,152,154,157,164,165,167,168,173,177,179,180,182],[2,102],{45:J,79:Z,81:ee,92:se,93:re,94:ae,95:ne,96:oe,97:le,98:ie}),e([6,8,14,16,17,34,35,36,43,45,49,50,51,54,55,57,58,60,67,71,72,74,76,77,79,81,82,83,84,86,87,88,89,91,92,93,94,95,96,97,98,99,101,103,105,106,107,108,109,110,112,113,114,115,116,117,121,124,125,126,127,129,133,135,138,141,142,144,146,150,152,154,157,164,165,167,168,173,177,179,180,182,192,193,194,195],[2,65]),e(be,[2,84]),{16:f,35:w,47:192,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,101:j,104:35,120:33,195:P},e(be,[2,86]),{17:[1,193],43:we},{17:[1,194]},{89:[1,195],107:V,124:z,125:W,126:K,127:Q},{17:[1,196],43:[1,197]},e(xe,[2,58]),e(xe,[2,60]),e(xe,[2,61]),{16:f,35:w,47:198,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,101:j,104:35,120:33,195:P},e(xe,[2,63],{107:V,124:z,125:W,126:K,127:Q}),{71:[1,200],73:199,74:[1,201],77:[2,72]},{16:f,35:w,47:202,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,101:j,104:35,120:33,195:P},e(Ne,[2,26],{23:203,38:[1,204]}),e(Oe,[2,23]),e(B,[2,19]),e(M,[2,158]),e(M,[2,159]),e(Y,[2,130]),e(X,[2,124]),e(Y,[2,127]),e(Y,[2,128]),{4:205,18:s},{4:206,16:f,18:s,35:w,47:113,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,85:207,86:R,87:D,88:q,90:36,101:j,104:35,120:33,195:P},{45:J,79:Z,81:ee,91:te,92:se,93:re,94:ae,95:ne,96:oe,97:le,98:ie,99:ce,107:[1,208]},e(X,[2,105],{102:209,103:[1,210]}),e(X,[2,113],{45:J,79:Z,81:ee,91:te,92:se,93:re,94:ae,95:ne,96:oe,97:le,98:ie,99:ce}),e(X,[2,111],{45:J,79:Z,81:ee,91:te,92:se,93:re,94:ae,95:ne,96:oe,97:le,98:ie,99:ce}),e(xe,[2,138],{107:V,124:z,125:W,126:K,127:Q}),e(be,[2,85]),e(be,[2,87]),e(be,[2,88]),e([6,8,14,17,34,36,43,45,49,50,51,71,72,74,77,79,81,89,91,92,93,94,95,96,97,98,99,101,103,105,106,107,108,109,110,112,113,114,115,116,117,121,124,125,126,127,129,133,135,138,141,142,144,146,150,152,154,157,164,165,167,168,173,177,179,180,182,192,193,194],[2,56]),e(xe,ge,{120:33,104:35,90:36,80:37,61:38,66:39,63:40,78:41,75:46,52:47,53:48,56:49,59:50,62:51,47:174,65:211,16:f,30:ve,35:w,45:ke,46:Ee,50:x,51:g,54:v,55:k,57:E,58:T,60:O,76:C,79:A,81:S,82:I,83:N,84:L,86:R,87:D,88:q,101:j,195:P}),e(xe,[2,62],{107:V,124:z,125:W,126:K,127:Q}),{77:[1,212]},{16:f,35:w,47:213,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,101:j,104:35,120:33,195:P},{16:f,35:w,47:214,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,101:j,104:35,120:33,195:P},{72:[1,215],107:V,124:z,125:W,126:K,127:Q},e(Le,[2,28],{24:216,39:[1,217]}),e(Ne,[2,25]),{17:[1,218]},{17:[1,219]},{17:[1,220],43:we},{16:f,35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,104:221,195:P},e(X,[2,112]),{16:f,35:w,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:222,81:S,82:I,83:N,84:L,86:R,87:D,88:q,195:P},e(xe,[2,57]),e(be,[2,74]),{72:[1,223],107:V,124:z,125:W,126:K,127:Q},{77:[2,73],107:V,124:z,125:W,126:K,127:Q},{16:f,35:w,47:224,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,101:j,104:35,120:33,195:P},e(Re,[2,29],{25:225,40:[1,226],41:[1,227]}),e(Le,[2,27]),e(X,[2,126]),e(X,[2,108]),e(X,[2,109]),e(X,[2,110]),e(X,[2,106]),{16:f,35:w,47:228,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,101:j,104:35,120:33,195:P},e(De,[2,70],{107:V,124:z,125:W,126:K,127:Q}),e(qe,[2,33],{26:229,42:[1,230]}),e(Re,[2,30]),e(Re,[2,31]),e(De,[2,71],{107:V,124:z,125:W,126:K,127:Q}),{16:f,27:231,35:w,44:232,45:je,46:Pe,47:235,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,101:j,104:35,120:33,195:P},e(qe,[2,32]),e(Be,[2,168],{28:236,43:[1,237],157:[1,238]}),e(Me,[2,35]),e(Me,[2,36]),e(Me,[2,37]),e(Me,[2,39],{48:239,49:[1,240],50:[1,241],51:[1,242],107:V,124:z,125:W,126:K,127:Q}),e(Be,[2,12]),{16:f,35:w,44:243,45:je,46:Pe,47:235,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,101:j,104:35,120:33,195:P},{16:Fe,50:x,63:252,66:250,88:Ue,158:244,160:245,161:246,172:249,174:248},e(Me,[2,38]),{50:[1,253],51:[1,254]},e(Me,[2,41]),e(Me,[2,43]),e(Me,[2,34]),e([6,8,14,17,129,135,138,144,146,150,152,154],He,{159:255,43:Ge,182:Ve}),e(ze,[2,170]),e(ze,[2,172],{163:258,166:260,36:We,164:Ke,165:Qe,167:Ye,168:Xe,173:Je,177:Ze}),{162:[1,266]},e(et,[2,193]),e(et,[2,194]),e([6,8,14,17,36,43,49,50,89,129,135,138,144,146,150,152,154,164,165,167,168,173,177,179,180,192,193,194],He,{159:267,67:ye,182:Ve}),{4:268,16:Fe,18:s,50:x,63:252,66:250,88:Ue,158:269,160:245,161:246,172:249,174:248},e(tt,st,{183:270,66:272,49:rt,50:de}),e(Me,[2,40]),e(Me,[2,42]),e(at,[2,139],{128:273,129:[1,274]}),{16:Fe,50:x,63:252,66:250,88:Ue,160:275,161:246,172:249,174:248},{16:[1,276]},{173:[1,277]},{16:Fe,50:x,63:252,66:250,174:278},{169:279,170:nt,173:ot},{166:282,167:Ye,168:Xe,171:281,173:[2,181]},{173:[2,175]},{173:[2,176]},e(lt,[2,177]),e(lt,[2,178]),{16:Fe,50:x,63:252,66:250,161:283,172:249,174:248},e(tt,st,{66:272,183:284,49:rt,50:de}),{17:[1,285]},{17:[1,286],43:Ge},e(et,it,{189:287,190:288,191:289,192:ct,193:pt,194:mt}),{50:de,66:293},e(tt,[2,201],{67:ye}),e(Ce,[2,141],{130:294,131:295,135:[1,296]}),{16:f,35:w,47:297,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,101:j,104:35,120:33,195:P},e(ze,[2,171]),{50:de,66:299,181:298},{16:Fe,50:x,63:252,66:250,174:300},{176:301,179:ut},{173:[1,303]},{173:[2,180]},{173:[1,304]},{169:305,170:nt,173:ot},{36:We,89:[1,306],163:258,164:Ke,165:Qe,166:260,167:Ye,168:Xe,173:Je,177:Ze},e(et,it,{190:288,191:289,189:307,192:ct,193:pt,194:mt}),e(et,st,{66:272,183:308,49:rt,50:de}),e(et,[2,219]),e(et,[2,220]),e(et,[2,211]),e(et,[2,212]),{184:310,185:bt,186:yt},{184:313,185:bt,186:yt},{184:314,185:bt,186:yt},e(tt,[2,200],{67:ye}),e(dt,[2,155],{143:315,144:[1,316]}),e(Ce,[2,142]),{16:f,35:w,47:32,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,101:j,104:35,120:33,136:317,139:31,195:P},e(at,[2,140],{107:V,124:z,125:W,126:K,127:Q}),{17:[1,318],43:[1,319]},e(xe,[2,195],{67:ye}),e([6,8,14,17,36,43,89,129,135,138,144,146,150,152,154,164,165,167,168,173,177,182],[2,183],{175:320,176:321,179:ut,180:_t}),e(et,[2,185]),{16:f,35:w,47:323,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,101:j,104:35,120:33,195:P},{16:Fe,50:x,63:252,66:250,161:324,172:249,174:248},{16:Fe,50:x,63:252,66:250,174:325},{173:[2,182]},e(ze,[2,173]),e(et,[2,217]),e(et,[2,218]),{191:326,192:ct,193:pt,194:mt},{16:$t,152:ht,187:327},e(ft,[2,202]),e(ft,[2,203]),{16:$t,152:ht,187:329},{16:$t,152:ht,187:330},e(h,m,{137:18,11:331,138:u}),{16:f,35:w,47:332,50:x,51:g,52:47,53:48,54:v,55:k,56:49,57:E,58:T,59:50,60:O,61:38,62:51,63:40,66:39,75:46,76:C,78:41,79:A,80:37,81:S,82:I,83:N,84:L,86:R,87:D,88:q,90:36,101:j,104:35,120:33,195:P},e(Ce,F,{132:333,43:U,133:H}),e([6,8,14,17,36,43,49,50,89,129,135,138,144,146,150,152,154,164,165,167,168,173,177,179,180,182,192,193,194],[2,198]),{50:de,66:334},e(et,[2,184]),e(et,[2,191]),{16:[1,335]},e(et,[2,190],{107:V,124:z,125:W,126:K,127:Q}),{36:We,163:258,164:Ke,165:Qe,166:260,167:Ye,168:Xe,173:Je,175:336,176:321,177:Ze,179:ut,180:_t},e(et,[2,187]),e(et,[2,213]),{16:[1,337]},{135:[1,340],138:[1,339],173:[1,338]},{16:[1,341]},{16:[1,342]},e(M,_,{145:28,12:343,146:$}),e(dt,[2,156],{107:V,124:z,125:W,126:K,127:Q}),e(Ce,[2,145]),e(xe,[2,196],{67:ye}),{50:de,66:345,68:344},e(et,[2,186]),{17:[2,208],50:de,66:345,68:347,188:346},{16:[2,205]},{16:[2,206]},{16:[2,207]},{50:de,66:345,68:348},{50:de,66:345,68:349},e(wt,[2,162],{148:350,149:351,150:[1,352]}),{17:[1,353],43:xt},e(xe,[2,66],{67:ye}),{17:[1,355]},{17:[2,209],43:xt},{17:[1,356],43:xt},{17:[1,357],43:xt},e(Be,[2,165],{151:358,152:[1,359],154:[1,360]}),e(wt,[2,163]),{50:[1,362],63:361},e(et,[2,192]),{50:de,66:363},e(et,[2,214]),e(et,[2,215]),e(et,[2,216]),e(Be,[2,169]),{153:[1,364]},{105:[1,365]},e(wt,[2,164]),{16:$e},e(xe,[2,67],{67:ye}),e(Be,[2,166]),{155:[1,366]},{156:[1,367]},e(Be,[2,167])],defaultActions:{11:[2,3],22:[2,1],24:[2,2],143:[2,121],144:[2,122],262:[2,175],263:[2,176],280:[2,180],305:[2,182],338:[2,205],339:[2,206],340:[2,207]},parseError:function(e,t){if(!t.recoverable){var s=new Error(e);throw s.hash=t,s}this.trace(e)},parse:function(e){var t=this,s=[0],r=[null],a=[],n=this.table,o="",l=0,i=0,c=0,p=a.slice.call(arguments,1),m=Object.create(this.lexer),u={yy:{}};for(var b in this.yy)Object.prototype.hasOwnProperty.call(this.yy,b)&&(u.yy[b]=this.yy[b]);m.setInput(e,u.yy),u.yy.lexer=m,u.yy.parser=this,void 0===m.yylloc&&(m.yylloc={});var y=m.yylloc;a.push(y);var d=m.options&&m.options.ranges;"function"==typeof u.yy.parseError?this.parseError=u.yy.parseError:this.parseError=Object.getPrototypeOf(this).parseError;for(var _,$,h,f,w,x,g,v,k,E=function(){var e;return"number"!=typeof(e=m.lex()||1)&&(e=t.symbols_[e]||e),e},T={};;){if(h=s[s.length-1],this.defaultActions[h]?f=this.defaultActions[h]:(null==_&&(_=E()),f=n[h]&&n[h][_]),void 0===f||!f.length||!f[0]){var O="";for(x in k=[],n[h])this.terminals_[x]&&x>2&&k.push("'"+this.terminals_[x]+"'");O=m.showPosition?"Parse error on line "+(l+1)+":\n"+m.showPosition()+"\nExpecting "+k.join(", ")+", got '"+(this.terminals_[_]||_)+"'":"Parse error on line "+(l+1)+": Unexpected "+(1==_?"end of input":"'"+(this.terminals_[_]||_)+"'"),this.parseError(O,{text:m.match,token:this.terminals_[_]||_,line:m.yylineno,loc:y,expected:k})}if(f[0]instanceof Array&&f.length>1)throw new Error("Parse Error: multiple actions possible at state: "+h+", token: "+_);switch(f[0]){case 1:s.push(_),r.push(m.yytext),a.push(m.yylloc),s.push(f[1]),_=null,$?(_=$,$=null):(i=m.yyleng,o=m.yytext,l=m.yylineno,y=m.yylloc,c>0&&c--);break;case 2:if(g=this.productions_[f[1]][1],T.$=r[r.length-g],T._$={first_line:a[a.length-(g||1)].first_line,last_line:a[a.length-1].last_line,first_column:a[a.length-(g||1)].first_column,last_column:a[a.length-1].last_column},d&&(T._$.range=[a[a.length-(g||1)].range[0],a[a.length-1].range[1]]),void 0!==(w=this.performAction.apply(T,[o,i,l,u.yy,f[1],r,a].concat(p))))return w;g&&(s=s.slice(0,-1*g*2),r=r.slice(0,-1*g),a=a.slice(0,-1*g)),s.push(this.productions_[f[1]][0]),r.push(T.$),a.push(T._$),v=n[s[s.length-2]][s[s.length-1]],s.push(v);break;case 3:return!0}}return!0}},vt={EOF:1,parseError:function(e,t){if(!this.yy.parser)throw new Error(e);this.yy.parser.parseError(e,t)},setInput:function(e,t){return this.yy=t||this.yy||{},this._input=e,this._more=this._backtrack=this.done=!1,this.yylineno=this.yyleng=0,this.yytext=this.matched=this.match="",this.conditionStack=["INITIAL"],this.yylloc={first_line:1,first_column:0,last_line:1,last_column:0},this.options.ranges&&(this.yylloc.range=[0,0]),this.offset=0,this},input:function(){var e=this._input[0];return this.yytext+=e,this.yyleng++,this.offset++,this.match+=e,this.matched+=e,e.match(/(?:\r\n?|\n).*/g)?(this.yylineno++,this.yylloc.last_line++):this.yylloc.last_column++,this.options.ranges&&this.yylloc.range[1]++,this._input=this._input.slice(1),e},unput:function(e){var t=e.length,s=e.split(/(?:\r\n?|\n)/g);this._input=e+this._input,this.yytext=this.yytext.substr(0,this.yytext.length-t),this.offset-=t;var r=this.match.split(/(?:\r\n?|\n)/g);this.match=this.match.substr(0,this.match.length-1),this.matched=this.matched.substr(0,this.matched.length-1),s.length-1&&(this.yylineno-=s.length-1);var a=this.yylloc.range;return this.yylloc={first_line:this.yylloc.first_line,last_line:this.yylineno+1,first_column:this.yylloc.first_column,last_column:s?(s.length===r.length?this.yylloc.first_column:0)+r[r.length-s.length].length-s[0].length:this.yylloc.first_column-t},this.options.ranges&&(this.yylloc.range=[a[0],a[0]+this.yyleng-t]),this.yyleng=this.yytext.length,this},more:function(){return this._more=!0,this},reject:function(){return this.options.backtrack_lexer?(this._backtrack=!0,this):this.parseError("Lexical error on line "+(this.yylineno+1)+". You can only invoke reject() in the lexer when the lexer is of the backtracking persuasion (options.backtrack_lexer = true).\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},less:function(e){this.unput(this.match.slice(e))},pastInput:function(){var e=this.matched.substr(0,this.matched.length-this.match.length);return(e.length>20?"...":"")+e.substr(-20).replace(/\n/g,"")},upcomingInput:function(){var e=this.match;return e.length<20&&(e+=this._input.substr(0,20-e.length)),(e.substr(0,20)+(e.length>20?"...":"")).replace(/\n/g,"")},showPosition:function(){var e=this.pastInput(),t=new Array(e.length+1).join("-");return e+this.upcomingInput()+"\n"+t+"^"},test_match:function(e,t){var s,r,a;if(this.options.backtrack_lexer&&(a={yylineno:this.yylineno,yylloc:{first_line:this.yylloc.first_line,last_line:this.last_line,first_column:this.yylloc.first_column,last_column:this.yylloc.last_column},yytext:this.yytext,match:this.match,matches:this.matches,matched:this.matched,yyleng:this.yyleng,offset:this.offset,_more:this._more,_input:this._input,yy:this.yy,conditionStack:this.conditionStack.slice(0),done:this.done},this.options.ranges&&(a.yylloc.range=this.yylloc.range.slice(0))),(r=e[0].match(/(?:\r\n?|\n).*/g))&&(this.yylineno+=r.length),this.yylloc={first_line:this.yylloc.last_line,last_line:this.yylineno+1,first_column:this.yylloc.last_column,last_column:r?r[r.length-1].length-r[r.length-1].match(/\r?\n?/)[0].length:this.yylloc.last_column+e[0].length},this.yytext+=e[0],this.match+=e[0],this.matches=e,this.yyleng=this.yytext.length,this.options.ranges&&(this.yylloc.range=[this.offset,this.offset+=this.yyleng]),this._more=!1,this._backtrack=!1,this._input=this._input.slice(e[0].length),this.matched+=e[0],s=this.performAction.call(this,this.yy,this,t,this.conditionStack[this.conditionStack.length-1]),this.done&&this._input&&(this.done=!1),s)return s;if(this._backtrack){for(var n in a)this[n]=a[n];return!1}return!1},next:function(){if(this.done)return this.EOF;var e,t,s,r;this._input||(this.done=!0),this._more||(this.yytext="",this.match="");for(var a=this._currentRules(),n=0;n<a.length;n++)if((s=this._input.match(this.rules[a[n]]))&&(!t||s[0].length>t[0].length)){if(t=s,r=n,this.options.backtrack_lexer){if(!1!==(e=this.test_match(s,a[n])))return e;if(this._backtrack){t=!1;continue}return!1}if(!this.options.flex)break}return t?!1!==(e=this.test_match(t,a[r]))&&e:""===this._input?this.EOF:this.parseError("Lexical error on line "+(this.yylineno+1)+". Unrecognized text.\n"+this.showPosition(),{text:"",token:null,line:this.yylineno})},lex:function(){var e=this.next();return e||this.lex()},begin:function(e){this.conditionStack.push(e)},popState:function(){return this.conditionStack.length-1>0?this.conditionStack.pop():this.conditionStack[0]},_currentRules:function(){return this.conditionStack.length&&this.conditionStack[this.conditionStack.length-1]?this.conditions[this.conditionStack[this.conditionStack.length-1]].rules:this.conditions.INITIAL.rules},topState:function(e){return(e=this.conditionStack.length-1-Math.abs(e||0))>=0?this.conditionStack[e]:"INITIAL"},pushState:function(e){this.begin(e)},stateStackSize:function(){return this.conditionStack.length},options:{"case-insensitive":!0},performAction:function(e,t,s,r){switch(s){case 0:case 1:case 2:case 3:break;case 4:return 195;case 5:case 6:case 7:case 115:case 119:return 50;case 8:return 18;case 9:return 29;case 10:return 119;case 11:return 30;case 12:return 31;case 13:return 32;case 14:return 33;case 15:return 36;case 16:return 37;case 17:return 38;case 18:return 39;case 19:return 40;case 20:return 41;case 21:return 42;case 22:return 46;case 23:return 49;case 24:return 57;case 25:return 58;case 26:return 60;case 27:return"COLLATE";case 28:return 84;case 29:return 86;case 30:return 87;case 31:return 76;case 32:return 71;case 33:return 72;case 34:return 74;case 35:return 77;case 36:return 96;case 37:return 97;case 38:return 101;case 39:return 106;case 40:return 105;case 41:return 108;case 42:return 109;case 43:return 103;case 44:return 110;case 45:return 121;case 46:return 123;case 47:return 107;case 48:return 126;case 49:return 127;case 50:return 157;case 51:return 182;case 52:return 192;case 53:return 185;case 54:return 186;case 55:return 152;case 56:return 173;case 57:return 138;case 58:return 135;case 59:return 193;case 60:return 194;case 61:return 164;case 62:return 165;case 63:return 179;case 64:return 180;case 65:return 167;case 66:return 168;case 67:return 170;case 68:return 177;case 69:return 129;case 70:return 141;case 71:return 142;case 72:return 133;case 73:return 134;case 74:return 144;case 75:return 147;case 76:return 150;case 77:return 153;case 78:return 154;case 79:return 155;case 80:return 156;case 81:return 162;case 82:return 146;case 83:return 14;case 84:return 43;case 85:return 34;case 86:return 16;case 87:return 17;case 88:return 82;case 89:return 117;case 90:return 83;case 91:return 91;case 92:return 92;case 93:return 79;case 94:return 81;case 95:return 45;case 96:return 95;case 97:return 98;case 98:return 99;case 99:return 94;case 100:return 112;case 101:return 113;case 102:return 93;case 103:return"<=>";case 104:return 114;case 105:return 116;case 106:return 115;case 107:return 88;case 108:return 89;case 109:return 8;case 110:case 111:case 117:case 118:return 51;case 112:return 55;case 113:return 35;case 114:return 54;case 116:return 67;case 120:return 6;case 121:return"INVALID"}},rules:[/^(?:[/][*](.|\n)*?[*][/])/i,/^(?:[-][-]\s.*\n)/i,/^(?:[#]\s.*\n)/i,/^(?:\s+)/i,/^(?:[$][{](.+?)[}])/i,/^(?:[`][a-zA-Z0-9_\u0080-\uFFFF]*[`])/i,/^(?:[\w]+[\u0080-\uFFFF]+[0-9a-zA-Z_\u0080-\uFFFF]*)/i,/^(?:[\u0080-\uFFFF][0-9a-zA-Z_\u0080-\uFFFF]*)/i,/^(?:SELECT\b)/i,/^(?:ALL\b)/i,/^(?:ANY\b)/i,/^(?:DISTINCT\b)/i,/^(?:DISTINCTROW\b)/i,/^(?:HIGH_PRIORITY\b)/i,/^(?:MAX_STATEMENT_TIME\b)/i,/^(?:STRAIGHT_JOIN\b)/i,/^(?:SQL_SMALL_RESULT\b)/i,/^(?:SQL_BIG_RESULT\b)/i,/^(?:SQL_BUFFER_RESULT\b)/i,/^(?:SQL_CACHE\b)/i,/^(?:SQL_NO_CACHE\b)/i,/^(?:SQL_CALC_FOUND_ROWS\b)/i,/^(?:([a-zA-Z_\u4e00-\u9fa5][a-zA-Z0-9_\u4e00-\u9fa5]*\.){1,2}\*)/i,/^(?:AS\b)/i,/^(?:TRUE\b)/i,/^(?:FALSE\b)/i,/^(?:NULL\b)/i,/^(?:COLLATE\b)/i,/^(?:BINARY\b)/i,/^(?:ROW\b)/i,/^(?:EXISTS\b)/i,/^(?:CASE\b)/i,/^(?:WHEN\b)/i,/^(?:THEN\b)/i,/^(?:ELSE\b)/i,/^(?:END\b)/i,/^(?:DIV\b)/i,/^(?:MOD\b)/i,/^(?:NOT\b)/i,/^(?:BETWEEN\b)/i,/^(?:IN\b)/i,/^(?:SOUNDS\b)/i,/^(?:LIKE\b)/i,/^(?:ESCAPE\b)/i,/^(?:REGEXP\b)/i,/^(?:IS\b)/i,/^(?:UNKNOWN\b)/i,/^(?:AND\b)/i,/^(?:OR\b)/i,/^(?:XOR\b)/i,/^(?:FROM\b)/i,/^(?:PARTITION\b)/i,/^(?:USE\b)/i,/^(?:INDEX\b)/i,/^(?:KEY\b)/i,/^(?:FOR\b)/i,/^(?:JOIN\b)/i,/^(?:ORDER\s+BY\b)/i,/^(?:GROUP\s+BY\b)/i,/^(?:IGNORE\b)/i,/^(?:FORCE\b)/i,/^(?:INNER\b)/i,/^(?:CROSS\b)/i,/^(?:ON\b)/i,/^(?:USING\b)/i,/^(?:LEFT\b)/i,/^(?:RIGHT\b)/i,/^(?:OUTER\b)/i,/^(?:NATURAL\b)/i,/^(?:WHERE\b)/i,/^(?:ASC\b)/i,/^(?:DESC\b)/i,/^(?:WITH\b)/i,/^(?:ROLLUP\b)/i,/^(?:HAVING\b)/i,/^(?:OFFSET\b)/i,/^(?:PROCEDURE\b)/i,/^(?:UPDATE\b)/i,/^(?:LOCK\b)/i,/^(?:SHARE\b)/i,/^(?:MODE\b)/i,/^(?:OJ\b)/i,/^(?:LIMIT\b)/i,/^(?:UNION\b)/i,/^(?:,)/i,/^(?:=)/i,/^(?:\()/i,/^(?:\))/i,/^(?:~)/i,/^(?:!=)/i,/^(?:!)/i,/^(?:\|)/i,/^(?:&)/i,/^(?:\+)/i,/^(?:-)/i,/^(?:\*)/i,/^(?:\/)/i,/^(?:%)/i,/^(?:\^)/i,/^(?:>>)/i,/^(?:>=)/i,/^(?:>)/i,/^(?:<<)/i,/^(?:<=>)/i,/^(?:<=)/i,/^(?:<>)/i,/^(?:<)/i,/^(?:\{)/i,/^(?:\})/i,/^(?:;)/i,/^(?:['](\\.|[^'])*['])/i,/^(?:["](\\.|[^"])*["])/i,/^(?:[0][x][0-9a-fA-F]+)/i,/^(?:[-]?[0-9]+(\.[0-9]+)?)/i,/^(?:[-]?[0-9]+(\.[0-9]+)?[eE][-][0-9]+(\.[0-9]+)?)/i,/^(?:[a-zA-Z_\u0080-\uFFFF][a-zA-Z0-9_\u0080-\uFFFF]*)/i,/^(?:\.)/i,/^(?:["][a-zA-Z_\u4e00-\u9fa5][a-zA-Z0-9_\u4e00-\u9fa5]*["])/i,/^(?:['][a-zA-Z_\u4e00-\u9fa5][a-zA-Z0-9_\u4e00-\u9fa5]*['])/i,/^(?:([`])(?:(?=(\\?))\2.)*?\1)/i,/^(?:$)/i,/^(?:.)/i],conditions:{INITIAL:{rules:[0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121],inclusive:!0}}};function kt(){this.yy={}}return gt.lexer=vt,kt.prototype=gt,gt.Parser=kt,new kt}();function a(){this.buffer=""}r||(r={}),r.stringify=function(e){var t=new a;return t.travelMain(e),t.buffer},a.prototype.travel=function(e){if(e){if("string"==typeof e)return this.append(e);this["travel"+e.type].call(this,e)}};var n=!1;a.prototype.appendKeyword=function(e,t,s){n&&(t=!0,n=!1),this.buffer+=t?e.toUpperCase():" "+e.toUpperCase(),s&&(n=!0)},a.prototype.append=function(e,t,s){n&&(t=!0,n=!1),this.buffer+=t?e:" "+e,s&&(n=!0)},a.prototype.travelMain=function(e){this.travel(e.value),e.hasSemicolon&&this.append(";",!0)},a.prototype.travelSelect=function(e){this.appendKeyword("select"),e.distinctOpt&&this.appendKeyword(e.distinctOpt),e.highPriorityOpt&&this.appendKeyword(e.highPriorityOpt),e.maxStateMentTimeOpt&&this.append("MAX_STATEMENT_TIME = "+e.maxStateMentTimeOpt),e.straightJoinOpt&&this.appendKeyword(e.straightJoinOpt),e.sqlSmallResultOpt&&this.appendKeyword(e.sqlSmallResultOpt),e.sqlBigResultOpt&&this.appendKeyword(e.sqlBigResultOpt),e.sqlBufferResultOpt&&this.appendKeyword(e.sqlBufferResultOpt),e.sqlCacheOpt&&this.appendKeyword(e.sqlCacheOpt),e.sqlCalcFoundRowsOpt&&this.appendKeyword(e.sqlCalcFoundRowsOpt),e.selectItems&&this.travelSelectExpr(e.selectItems),e.from&&(this.appendKeyword("from"),this.travel(e.from)),e.partition&&this.travel(e.partition),e.where&&(this.appendKeyword("where"),this.travel(e.where)),e.groupBy&&this.travel(e.groupBy),e.having&&(this.appendKeyword("having"),this.travel(e.having)),e.orderBy&&this.travel(e.orderBy),e.limit&&this.travel(e.limit),e.procedure&&(this.appendKeyword("procedure"),this.travel(e.procedure)),e.updateLockMode&&this.appendKeyword(e.updateLockMode)},a.prototype.travelSelectExpr=function(e){for(var t=e.value,s=0;s<t.length;s++)"string"==typeof e?this.append(t[s]):(this.travel(t[s]),t[s].alias&&(t[s].hasAs&&this.appendKeyword("as"),this.travel(t[s].alias))),s!==t.length-1&&this.append(",",!0)},a.prototype.travelIsExpression=function(e){this.travel(e.left),this.appendKeyword("in"),e.hasNot&&this.appendKeyword("not"),this.append(e.right)},a.prototype.travelNotExpression=function(e){this.appendKeyword("not"),this.travel(e.value)},a.prototype.travelOrExpression=a.prototype.travelAndExpression=a.prototype.travelXORExpression=function(e){this.travel(e.left),this.appendKeyword(e.operator),this.travel(e.right)},a.prototype.travelNull=a.prototype.travelBoolean=a.prototype.travelBooleanExtra=function(e){this.appendKeyword(e.value)},a.prototype.travelNumber=function(e){this.append(e.value)},a.prototype.travelString=function(e){this.append(e.value)},a.prototype.travelFunctionCall=function(e){this.append(e.name),this.append("(",!0,!0);for(var t=e.params,s=0;s<t.length;s++){var r=t[s];this.travel(r),s!==t.length-1&&this.append(",",!0)}this.append(")",!0)},a.prototype.travelFunctionCallParam=function(e){e.distinctOpt&&this.appendKeyword(e.distinctOpt),this.travel(e.value)},a.prototype.travelIdentifier=function(e){this.append(e.value)},a.prototype.travelIdentifierList=function(e){for(var t=e.value,s=0;s<t.length;s++)this.travel(t[s]),s!==t.length-1&&this.append(",",!0)},a.prototype.travelWhenThenList=function(e){for(var t=e.value,s=0;s<t.length;s++)this.appendKeyword("when"),this.travel(t[s].when),this.appendKeyword("then"),this.travel(t[s].then)},a.prototype.travelCaseWhen=function(e){this.appendKeyword("case"),e.caseExprOpt&&this.travel(e.caseExprOpt),this.travel(e.whenThenList),e.else&&(this.appendKeyword("else"),this.travel(e.else)),this.appendKeyword("end")},a.prototype.travelPrefix=function(e){this.appendKeyword(e.prefix),this.travel(e.value)},a.prototype.travelSimpleExprParentheses=function(e){e.hasRow&&this.appendKeyword("row"),this.append("(",!1,!0),this.travel(e.value),this.append(")",!0)},a.prototype.travelSubQuery=function(e){e.hasExists&&this.appendKeyword("exists"),this.append("(",!1,!0),this.travel(e.value),this.append(")",!0)},a.prototype.travelIdentifierExpr=function(e){this.append("{"),this.travel(e.identifier),this.travel(e.value),this.append("}")},a.prototype.travelBitExpression=function(e){this.travel(e.left),this.appendKeyword(e.operator),this.travel(e.right)},a.prototype.travelInSubQueryPredicate=function(e){this.travel(e.left),e.hasNot&&this.appendKeyword("not"),this.appendKeyword("in"),this.append("(",!1,!0),this.travel(e.right),this.append(")")},a.prototype.travelInExpressionListPredicate=function(e){this.travel(e.left),e.hasNot&&this.appendKeyword("not"),this.appendKeyword("in"),this.append("(",!1,!0),this.travel(e.right),this.append(")")},a.prototype.travelBetweenPredicate=function(e){this.travel(e.left),e.hasNot&&this.appendKeyword("not"),this.appendKeyword("between"),this.travel(e.right.left),this.appendKeyword("and"),this.travel(e.right.right)},a.prototype.travelSoundsLikePredicate=function(e){this.travel(e.left),this.appendKeyword("sounds"),this.appendKeyword("like"),this.travel(e.right)},a.prototype.travelLikePredicate=function(e){this.travel(e.left),e.hasNot&&this.appendKeyword("not"),this.appendKeyword("like"),this.travel(e.right),e.escape&&(this.appendKeyword("escape"),this.travel(e.escape))},a.prototype.travelRegexpPredicate=function(e){this.travel(e.left),e.hasNot&&this.appendKeyword("not"),this.appendKeyword("regexp"),this.travel(e.right)},a.prototype.travelIsNullBooleanPrimary=function(e){this.travel(e.value),this.appendKeyword("is"),e.hasNot&&this.appendKeyword("not"),this.appendKeyword("null")},a.prototype.travelComparisonBooleanPrimary=function(e){this.travel(e.left),this.append(e.operator),this.travel(e.right)},a.prototype.travelComparisonSubQueryBooleanPrimary=function(e){this.travel(e.left),this.append(e.operator),this.appendKeyword(e.subQueryOpt),this.append("(",!1,!0),this.travel(e.right),this.append(")")},a.prototype.travelExpressionList=function(e){for(var t=e.value,s=0;s<t.length;s++)this.travel(t[s]),s!==t.length-1&&this.append(",",!0)},a.prototype.travelGroupBy=function(e){this.appendKeyword("group by");for(var t=e.value,s=0;s<t.length;s++)this.travel(t[s]),s!==t.length-1&&this.append(",",!0)},a.prototype.travelOrderBy=function(e){this.appendKeyword("order by");for(var t=e.value,s=0;s<t.length;s++)this.travel(t[s]),s!==t.length-1&&this.append(",",!0);e.rollUp&&this.appendKeyword("with rollup")},a.prototype.travelGroupByOrderByItem=function(e){this.travel(e.value),e.sortOpt&&this.appendKeyword(e.sortOpt)},a.prototype.travelLimit=function(e){this.appendKeyword("limit");var t=e.value;1===t.length?this.append(t[0]):2===t.length&&(e.offsetMode?(this.append(t[1]),this.append("offset"),this.append(t[0])):(this.append(t[0]),this.append(",",!0),this.append(t[1])))},a.prototype.travelTableReferences=function(e){var t=e.value;e.TableReferences&&this.append("(",!1,!0);for(var s=0;s<t.length;s++)this.travel(t[s]),s!==t.length-1&&this.append(",",!0);e.TableReferences&&this.append(")")},a.prototype.travelTableReference=function(e){e.hasOj?(this.append("{"),this.appendKeyword("oj"),this.travel(e.value),this.append("}")):this.travel(e.value)},a.prototype.travelInnerCrossJoinTable=function(e){this.travel(e.left),e.innerCrossOpt&&this.appendKeyword(e.innerCrossOpt),this.appendKeyword("join"),this.travel(e.right),e.condition&&this.travel(e.condition)},a.prototype.travelStraightJoinTable=function(e){this.travel(e.left),this.appendKeyword("straight_join"),this.travel(e.right),this.travel(e.condition)},a.prototype.travelLeftRightJoinTable=function(e){this.travel(e.left),this.appendKeyword(e.leftRight),e.outOpt&&this.appendKeyword(e.outOpt),this.appendKeyword("join"),this.travel(e.right),this.travel(e.condition)},a.prototype.travelNaturalJoinTable=function(e){this.travel(e.left),this.appendKeyword("natural"),e.leftRight&&this.appendKeyword(e.leftRight),e.outOpt&&this.appendKeyword(e.outOpt),this.appendKeyword("join"),this.travel(e.right)},a.prototype.travelOnJoinCondition=function(e){this.appendKeyword("on"),this.travel(e.value)},a.prototype.travelUsingJoinCondition=function(e){this.appendKeyword("using"),this.appendKeyword("(",!1,!0),this.travel(e.value),this.appendKeyword(")")},a.prototype.travelPartitions=function(e){this.appendKeyword("partition"),this.appendKeyword("(",!1,!0);for(var t=e.value,s=0;s<t.length;s++)this.travel(t[s]),s!==t.length-1&&this.append(",",!0);this.appendKeyword(")")},a.prototype.travelForOptIndexHint=function(e){this.appendKeyword("for"),this.appendKeyword(e.value)},a.prototype.travelIndexList=function(e){for(var t=e.value,s=0;s<t.length;s++)this.travel(t[s]),s!==t.length-1&&this.append(",",!0)},a.prototype.travelUseIndexHint=function(e){this.appendKeyword("use"),this.appendKeyword(e.indexOrKey),e.forOpt&&this.travel(e.forOpt),this.appendKeyword("(",!1,!0),e.value&&this.travel(e.value),this.appendKeyword(")")},a.prototype.travelIgnoreIndexHint=function(e){this.appendKeyword("ignore"),this.appendKeyword(e.indexOrKey),e.forOpt&&this.travel(e.forOpt),this.appendKeyword("(",!1,!0),e.value&&this.travel(e.value),this.appendKeyword(")")},a.prototype.travelForceIndexHint=function(e){this.appendKeyword("force"),this.appendKeyword(e.indexOrKey),e.forOpt&&this.travel(e.forOpt),this.appendKeyword("(",!1,!0),e.value&&this.travel(e.value),this.appendKeyword(")")},a.prototype.travelTableFactor=function(e){this.travel(e.value),e.partition&&this.travel(e.partition),e.alias&&(e.hasAs&&this.appendKeyword("as"),this.travel(e.alias)),e.indexHintOpt&&this.travel(e.indexHintOpt)},a.prototype.travelUnion=function(e){this.travel(e.left),this.appendKeyword("UNION"),e.distinctOpt&&this.appendKeyword(e.distinctOpt),this.travel(e.right)},a.prototype.travelSelectParenthesized=function(e){this.appendKeyword("("),this.travel(e.value),this.appendKeyword(")")},a.prototype.travelPlaceHolder=function(e){e.value&&this.travel(e.value)};void 0===(s=function(){return r}.apply(t,[]))||(e.exports=s)},1251:function(e,t){var s,r,a;r=[],void 0===(a="function"==typeof(s=function(){"use strict";var e=Object.prototype.hasOwnProperty,t=Object.prototype.toString,s="boolean"==typeof(new RegExp).sticky;function r(e){return e&&"[object RegExp]"===t.call(e)}function a(e){return e&&"object"==typeof e&&!r(e)&&!Array.isArray(e)}function n(e){return e.replace(/[-\/\\^$*+?.()|[\]{}]/g,"\\$&")}function o(e){return new RegExp("|"+e).exec("").length-1}function l(e){return"("+e+")"}function i(e){return e.length?"(?:"+e.map((function(e){return"(?:"+e+")"})).join("|")+")":"(?!)"}function c(e){if("string"==typeof e)return"(?:"+n(e)+")";if(r(e)){if(e.ignoreCase)throw new Error("RegExp /i flag not allowed");if(e.global)throw new Error("RegExp /g flag is implied");if(e.sticky)throw new Error("RegExp /y flag is implied");if(e.multiline)throw new Error("RegExp /m flag is implied");return e.source}throw new Error("Not a pattern: "+e)}function p(e,t){return e.length>t?e:Array(t-e.length+1).join(" ")+e}function m(e,t){for(var s=e.length,r=0;;){var a=e.lastIndexOf("\n",s-1);if(-1===a)break;if(s=a,++r===t)break;if(0===s)break}var n=r<t?0:s+1;return e.substring(n).split("\n")}function u(e){for(var t=Object.getOwnPropertyNames(e),s=[],r=0;r<t.length;r++){var n=t[r],o=e[n],l=[].concat(o);if("include"!==n){var i=[];l.forEach((function(e){a(e)?(i.length&&s.push(y(n,i)),s.push(y(n,e)),i=[]):i.push(e)})),i.length&&s.push(y(n,i))}else for(var c=0;c<l.length;c++)s.push({include:l[c]})}return s}function b(e){for(var t=[],s=0;s<e.length;s++){var r=e[s];if(r.include)for(var a=[].concat(r.include),n=0;n<a.length;n++)t.push({include:a[n]});else{if(!r.type)throw new Error("Rule has no type: "+JSON.stringify(r));t.push(y(r.type,r))}}return t}function y(t,s){if(a(s)||(s={match:s}),s.include)throw new Error("Matching rules cannot also include states");var n={defaultType:t,lineBreaks:!!s.error||!!s.fallback,pop:!1,next:null,push:null,error:!1,fallback:!1,value:null,type:null,shouldThrow:!1};for(var o in s)e.call(s,o)&&(n[o]=s[o]);if("string"==typeof n.type&&t!==n.type)throw new Error("Type transform cannot be a string (type '"+n.type+"' for token '"+t+"')");var l=n.match;return n.match=Array.isArray(l)?l:l?[l]:[],n.match.sort((function(e,t){return r(e)&&r(t)?0:r(t)?-1:r(e)?1:t.length-e.length})),n}function d(e){return Array.isArray(e)?b(e):u(e)}var _=y("error",{lineBreaks:!0,shouldThrow:!0});function $(e,t){for(var a=null,n=Object.create(null),p=!0,m=null,u=[],b=[],y=0;y<e.length;y++)e[y].fallback&&(p=!1);for(y=0;y<e.length;y++){var d=e[y];if(d.include)throw new Error("Inheritance is not allowed in stateless lexers");if(d.error||d.fallback){if(a)throw!d.fallback==!a.fallback?new Error("Multiple "+(d.fallback?"fallback":"error")+" rules not allowed (for token '"+d.defaultType+"')"):new Error("fallback and error are mutually exclusive (for token '"+d.defaultType+"')");a=d}var $=d.match.slice();if(p)for(;$.length&&"string"==typeof $[0]&&1===$[0].length;)n[$.shift().charCodeAt(0)]=d;if(d.pop||d.push||d.next){if(!t)throw new Error("State-switching options are not allowed in stateless lexers (for token '"+d.defaultType+"')");if(d.fallback)throw new Error("State-switching options are not allowed on fallback tokens (for token '"+d.defaultType+"')")}if(0!==$.length){p=!1,u.push(d);for(var h=0;h<$.length;h++){var f=$[h];if(r(f))if(null===m)m=f.unicode;else if(m!==f.unicode&&!1===d.fallback)throw new Error("If one rule is /u then all must be")}var w=i($.map(c)),x=new RegExp(w);if(x.test(""))throw new Error("RegExp matches empty string: "+x);if(o(w)>0)throw new Error("RegExp has capture groups: "+x+"\nUse (?: … ) instead");if(!d.lineBreaks&&x.test("\n"))throw new Error("Rule should declare lineBreaks: "+x);b.push(l(w))}}var g=a&&a.fallback,v=s&&!g?"ym":"gm",k=s||g?"":"|";return!0===m&&(v+="u"),{regexp:new RegExp(i(b)+k,v),groups:u,fast:n,error:a||_}}function h(e){var t=$(d(e));return new g({start:t},"start")}function f(e,t,s){var r=e&&(e.push||e.next);if(r&&!s[r])throw new Error("Missing state '"+r+"' (in token '"+e.defaultType+"' of state '"+t+"')");if(e&&e.pop&&1!=+e.pop)throw new Error("pop must be 1 (in token '"+e.defaultType+"' of state '"+t+"')")}function w(e,t){var s=e.$all?d(e.$all):[];delete e.$all;var r=Object.getOwnPropertyNames(e);t||(t=r[0]);for(var a=Object.create(null),n=0;n<r.length;n++)a[_=r[n]]=d(e[_]).concat(s);for(n=0;n<r.length;n++)for(var o=a[_=r[n]],l=Object.create(null),i=0;i<o.length;i++){var c=o[i];if(c.include){var p=[i,1];if(c.include!==_&&!l[c.include]){l[c.include]=!0;var m=a[c.include];if(!m)throw new Error("Cannot include nonexistent state '"+c.include+"' (in state '"+_+"')");for(var u=0;u<m.length;u++){var b=m[u];-1===o.indexOf(b)&&p.push(b)}}o.splice.apply(o,p),i--}}var y=Object.create(null);for(n=0;n<r.length;n++){var _;y[_=r[n]]=$(a[_],!0)}for(n=0;n<r.length;n++){var h=r[n],w=y[h],x=w.groups;for(i=0;i<x.length;i++)f(x[i],h,y);var v=Object.getOwnPropertyNames(w.fast);for(i=0;i<v.length;i++)f(w.fast[v[i]],h,y)}return new g(y,t)}function x(e){for(var t="undefined"!=typeof Map,s=t?new Map:Object.create(null),r=Object.getOwnPropertyNames(e),a=0;a<r.length;a++){var n=r[a],o=e[n];(Array.isArray(o)?o:[o]).forEach((function(e){if("string"!=typeof e)throw new Error("keyword must be string (in keyword '"+n+"')");t?s.set(e,n):s[e]=n}))}return function(e){return t?s.get(e):s[e]}}var g=function(e,t){this.startState=t,this.states=e,this.buffer="",this.stack=[],this.reset()};g.prototype.reset=function(e,t){return this.buffer=e||"",this.index=0,this.line=t?t.line:1,this.col=t?t.col:1,this.queuedToken=t?t.queuedToken:null,this.queuedText=t?t.queuedText:"",this.queuedThrow=t?t.queuedThrow:null,this.setState(t?t.state:this.startState),this.stack=t&&t.stack?t.stack.slice():[],this},g.prototype.save=function(){return{line:this.line,col:this.col,state:this.state,stack:this.stack.slice(),queuedToken:this.queuedToken,queuedText:this.queuedText,queuedThrow:this.queuedThrow}},g.prototype.setState=function(e){if(e&&this.state!==e){this.state=e;var t=this.states[e];this.groups=t.groups,this.error=t.error,this.re=t.regexp,this.fast=t.fast}},g.prototype.popState=function(){this.setState(this.stack.pop())},g.prototype.pushState=function(e){this.stack.push(this.state),this.setState(e)};var v=s?function(e,t){return e.exec(t)}:function(e,t){var s=e.exec(t);return 0===s[0].length?null:s};function k(){return this.value}if(g.prototype._getGroup=function(e){for(var t=this.groups.length,s=0;s<t;s++)if(void 0!==e[s+1])return this.groups[s];throw new Error("Cannot find token type for matched text")},g.prototype.next=function(){var e=this.index;if(this.queuedGroup){var t=this._token(this.queuedGroup,this.queuedText,e);return this.queuedGroup=null,this.queuedText="",t}var s=this.buffer;if(e!==s.length){if(o=this.fast[s.charCodeAt(e)])return this._token(o,s.charAt(e),e);var r=this.re;r.lastIndex=e;var a=v(r,s),n=this.error;if(null==a)return this._token(n,s.slice(e,s.length),e);var o=this._getGroup(a),l=a[0];return n.fallback&&a.index!==e?(this.queuedGroup=o,this.queuedText=l,this._token(n,s.slice(e,a.index),e)):this._token(o,l,e)}},g.prototype._token=function(e,t,s){var r=0;if(e.lineBreaks){var a=/\n/g,n=1;if("\n"===t)r=1;else for(;a.exec(t);)r++,n=a.lastIndex}var o={type:"function"==typeof e.type&&e.type(t)||e.defaultType,value:"function"==typeof e.value?e.value(t):t,text:t,toString:k,offset:s,lineBreaks:r,line:this.line,col:this.col},l=t.length;if(this.index+=l,this.line+=r,0!==r?this.col=l-n+1:this.col+=l,e.shouldThrow)throw new Error(this.formatError(o,"invalid syntax"));return e.pop?this.popState():e.push?this.pushState(e.push):e.next&&this.setState(e.next),o},"undefined"!=typeof Symbol&&Symbol.iterator){var E=function(e){this.lexer=e};E.prototype.next=function(){var e=this.lexer.next();return{value:e,done:!e}},E.prototype[Symbol.iterator]=function(){return this},g.prototype[Symbol.iterator]=function(){return new E(this)}}return g.prototype.formatError=function(e,t){if(null==e){var s=this.buffer.slice(this.index);e={text:s,offset:this.index,lineBreaks:-1===s.indexOf("\n")?0:1,line:this.line,col:this.col}}var r=2,a=Math.max(e.line-r,1),n=e.line+r,o=String(n).length,l=m(this.buffer,this.line-e.line+r+1).slice(0,5),i=[];i.push(t+" at line "+e.line+" col "+e.col+":"),i.push("");for(var c=0;c<l.length;c++){var u=l[c],b=a+c;i.push(p(String(b),o)+"  "+u),b===e.line&&i.push(p("",o+e.col+1)+"^")}return i.join("\n")},g.prototype.clone=function(){return new g(this.states,this.state)},g.prototype.has=function(e){return!0},{compile:h,states:w,error:Object.freeze({error:!0}),fallback:Object.freeze({fallback:!0}),keywords:x}})?s.apply(t,r):s)||(e.exports=a)},8831:function(e){var t,s;t=this,s=function(){function e(t,s,r){return this.id=++e.highestId,this.name=t,this.symbols=s,this.postprocess=r,this}function t(e,t,s,r){this.rule=e,this.dot=t,this.reference=s,this.data=[],this.wantedBy=r,this.isComplete=this.dot===e.symbols.length}function s(e,t){this.grammar=e,this.index=t,this.states=[],this.wants={},this.scannable=[],this.completed={}}function r(e,t){this.rules=e,this.start=t||this.rules[0].name;var s=this.byName={};this.rules.forEach((function(e){s.hasOwnProperty(e.name)||(s[e.name]=[]),s[e.name].push(e)}))}function a(){this.reset("")}function n(e,t,n){if(e instanceof r){var o=e;n=t}else o=r.fromCompiled(e,t);for(var l in this.grammar=o,this.options={keepHistory:!1,lexer:o.lexer||new a},n||{})this.options[l]=n[l];this.lexer=this.options.lexer,this.lexerState=void 0;var i=new s(o,0);this.table=[i],i.wants[o.start]=[],i.predict(o.start),i.process(),this.current=0}function o(e){var t=typeof e;if("string"===t)return e;if("object"===t){if(e.literal)return JSON.stringify(e.literal);if(e instanceof RegExp)return e.toString();if(e.type)return"%"+e.type;if(e.test)return"<"+String(e.test)+">";throw new Error("Unknown symbol type: "+e)}}return e.highestId=0,e.prototype.toString=function(e){var t=void 0===e?this.symbols.map(o).join(" "):this.symbols.slice(0,e).map(o).join(" ")+" ● "+this.symbols.slice(e).map(o).join(" ");return this.name+" → "+t},t.prototype.toString=function(){return"{"+this.rule.toString(this.dot)+"}, from: "+(this.reference||0)},t.prototype.nextState=function(e){var s=new t(this.rule,this.dot+1,this.reference,this.wantedBy);return s.left=this,s.right=e,s.isComplete&&(s.data=s.build(),s.right=void 0),s},t.prototype.build=function(){var e=[],t=this;do{e.push(t.right.data),t=t.left}while(t.left);return e.reverse(),e},t.prototype.finish=function(){this.rule.postprocess&&(this.data=this.rule.postprocess(this.data,this.reference,n.fail))},s.prototype.process=function(e){for(var t=this.states,s=this.wants,r=this.completed,a=0;a<t.length;a++){var o=t[a];if(o.isComplete){if(o.finish(),o.data!==n.fail){for(var l=o.wantedBy,i=l.length;i--;){var c=l[i];this.complete(c,o)}if(o.reference===this.index){var p=o.rule.name;(this.completed[p]=this.completed[p]||[]).push(o)}}}else{if("string"!=typeof(p=o.rule.symbols[o.dot])){this.scannable.push(o);continue}if(s[p]){if(s[p].push(o),r.hasOwnProperty(p)){var m=r[p];for(i=0;i<m.length;i++){var u=m[i];this.complete(o,u)}}}else s[p]=[o],this.predict(p)}}},s.prototype.predict=function(e){for(var s=this.grammar.byName[e]||[],r=0;r<s.length;r++){var a=s[r],n=this.wants[e],o=new t(a,0,this.index,n);this.states.push(o)}},s.prototype.complete=function(e,t){var s=e.nextState(t);this.states.push(s)},r.fromCompiled=function(t,s){var a=t.Lexer;t.ParserStart&&(s=t.ParserStart,t=t.ParserRules);var n=new r(t=t.map((function(t){return new e(t.name,t.symbols,t.postprocess)})),s);return n.lexer=a,n},a.prototype.reset=function(e,t){this.buffer=e,this.index=0,this.line=t?t.line:1,this.lastLineBreak=t?-t.col:0},a.prototype.next=function(){if(this.index<this.buffer.length){var e=this.buffer[this.index++];return"\n"===e&&(this.line+=1,this.lastLineBreak=this.index),{value:e}}},a.prototype.save=function(){return{line:this.line,col:this.index-this.lastLineBreak}},a.prototype.formatError=function(e,t){var s=this.buffer;if("string"==typeof s){var r=s.split("\n").slice(Math.max(0,this.line-5),this.line),a=s.indexOf("\n",this.index);-1===a&&(a=s.length);var n=this.index-this.lastLineBreak,o=String(this.line).length;return t+=" at line "+this.line+" col "+n+":\n\n",t+=r.map((function(e,t){return l(this.line-r.length+t+1,o)+" "+e}),this).join("\n"),t+="\n"+l("",o+n)+"^\n"}return t+" at index "+(this.index-1);function l(e,t){var s=String(e);return Array(t-s.length+1).join(" ")+s}},n.fail={},n.prototype.feed=function(e){var t,r=this.lexer;for(r.reset(e,this.lexerState);;){try{if(!(t=r.next()))break}catch(e){var n=new s(this.grammar,this.current+1);throw this.table.push(n),(i=new Error(this.reportLexerError(e))).offset=this.current,i.token=e.token,i}var o=this.table[this.current];this.options.keepHistory||delete this.table[this.current-1];var l=this.current+1;n=new s(this.grammar,l),this.table.push(n);for(var i,c=void 0!==t.text?t.text:t.value,p=r.constructor===a?t.value:t,m=o.scannable,u=m.length;u--;){var b=m[u],y=b.rule.symbols[b.dot];if(y.test?y.test(p):y.type?y.type===t.type:y.literal===c){var d=b.nextState({data:p,token:t,isToken:!0,reference:l-1});n.states.push(d)}}if(n.process(),0===n.states.length)throw(i=new Error(this.reportError(t))).offset=this.current,i.token=t,i;this.options.keepHistory&&(o.lexerState=r.save()),this.current++}return o&&(this.lexerState=r.save()),this.results=this.finish(),this},n.prototype.reportLexerError=function(e){var t,s,r=e.token;return r?(t="input "+JSON.stringify(r.text[0])+" (lexer error)",s=this.lexer.formatError(r,"Syntax error")):(t="input (lexer error)",s=e.message),this.reportErrorCommon(s,t)},n.prototype.reportError=function(e){var t=(e.type?e.type+" token: ":"")+JSON.stringify(void 0!==e.value?e.value:e),s=this.lexer.formatError(e,"Syntax error");return this.reportErrorCommon(s,t)},n.prototype.reportErrorCommon=function(e,t){var s=[];s.push(e);var r=this.table.length-2,a=this.table[r],n=a.states.filter((function(e){var t=e.rule.symbols[e.dot];return t&&"string"!=typeof t}));return 0===n.length?(s.push("Unexpected "+t+". I did not expect any more input. Here is the state of my parse table:\n"),this.displayStateStack(a.states,s)):(s.push("Unexpected "+t+". Instead, I was expecting to see one of the following:\n"),n.map((function(e){return this.buildFirstStateStack(e,[])||[e]}),this).forEach((function(e){var t=e[0],r=t.rule.symbols[t.dot],a=this.getSymbolDisplay(r);s.push("A "+a+" based on:"),this.displayStateStack(e,s)}),this)),s.push(""),s.join("\n")},n.prototype.displayStateStack=function(e,t){for(var s,r=0,a=0;a<e.length;a++){var n=e[a],o=n.rule.toString(n.dot);o===s?r++:(r>0&&t.push("    ^ "+r+" more lines identical to this"),r=0,t.push("    "+o)),s=o}},n.prototype.getSymbolDisplay=function(e){return function(e){var t=typeof e;if("string"===t)return e;if("object"===t){if(e.literal)return JSON.stringify(e.literal);if(e instanceof RegExp)return"character matching "+e;if(e.type)return e.type+" token";if(e.test)return"token matching "+String(e.test);throw new Error("Unknown symbol type: "+e)}}(e)},n.prototype.buildFirstStateStack=function(e,t){if(-1!==t.indexOf(e))return null;if(0===e.wantedBy.length)return[e];var s=e.wantedBy[0],r=[e].concat(t),a=this.buildFirstStateStack(s,r);return null===a?null:[e].concat(a)},n.prototype.save=function(){var e=this.table[this.current];return e.lexerState=this.lexerState,e},n.prototype.restore=function(e){var t=e.index;this.current=t,this.table[t]=e,this.table.splice(t+1),this.lexerState=e.lexerState,this.results=this.finish()},n.prototype.rewind=function(e){if(!this.options.keepHistory)throw new Error("set option `keepHistory` to enable rewinding");this.restore(this.table[e])},n.prototype.finish=function(){var e=[],t=this.grammar.start;return this.table[this.table.length-1].states.forEach((function(s){s.rule.name===t&&s.dot===s.rule.symbols.length&&0===s.reference&&s.data!==n.fail&&e.push(s)})),e.map((function(e){return e.data}))},{Parser:n,Grammar:r,Rule:e}},e.exports?e.exports=s():t.nearley=s()},7899:(e,t,s)=>{!function(e,t){for(var s in t)e[s]=t[s]}(t,function(e){var t={};function s(r){if(t[r])return t[r].exports;var a=t[r]={i:r,l:!1,exports:{}};return e[r].call(a.exports,a,a.exports,s),a.l=!0,a.exports}return s.m=e,s.c=t,s.d=function(e,t,r){s.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:r})},s.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},s.t=function(e,t){if(1&t&&(e=s(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var r=Object.create(null);if(s.r(r),Object.defineProperty(r,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var a in e)s.d(r,a,function(t){return e[t]}.bind(null,a));return r},s.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return s.d(t,"a",t),t},s.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},s.p="",s(s.s=7)}([function(e,t){e.exports=s(1251)},function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.unbox=t.doubleQuoted=t.box=t.track=t.tracking=t.trackingComments=t.lexerAny=t.lexer=void 0;const r=s(0),a=s(3),n={};for(const e of a.sqlKeywords)n["kw_"+e.toLowerCase()]=e;var o;t.lexer=(0,r.compile)({word:{match:/[eE](?!')[A-Za-z0-9_]*|[a-df-zA-DF-Z_][A-Za-z0-9_]*/,type:(e=>{const t=(0,r.keywords)(e);return e=>t(e.toUpperCase())})(n),value:e=>e.toLowerCase()},wordQuoted:{match:/"(?:[^"\*]|"")+"/,type:()=>"quoted_word",value:e=>e.substring(1,e.length-1)},string:{match:/'(?:[^']|\'\')*'/,value:e=>e.substring(1,e.length-1).replace(/''/g,"'")},eString:{match:/\b(?:e|E)'(?:[^'\\]|[\r\n\s]|(?:\\\s)|(?:\\\n)|(?:\\.)|(?:\'\'))+'/,value:e=>e.substring(2,e.length-1).replace(/''/g,"'").replace(/\\([\s\n])/g,((e,t)=>t)).replace(/\\./g,(e=>JSON.parse('"'+e+'"')))},qparam:{match:/\$\d+/},commentLine:/\-\-.*?$[\s\r\n]*/,commentFullOpen:/\/\*/,commentFullClose:/\*\/[\s\r\n]*/,star:"*",comma:",",space:{match:/[\s\t\n\v\f\r]+/,lineBreaks:!0},int:/\-?\d+(?![\.\d])/,float:/\-?(?:(?:\d*\.\d+)|(?:\d+\.\d*))/,lparen:"(",rparen:")",lbracket:"[",rbracket:"]",semicolon:";",dot:/\.(?!\d)/,op_cast:"::",op_colon:":",op_plus:"+",op_eq:"=",op_neq:{match:/(?:!=)|(?:\<\>)/,value:()=>"!="},op_membertext:"->>",op_member:"->",op_minus:"-",op_div:/\//,op_not_ilike:/\!~~\*/,op_not_like:/\!~~/,op_ilike:/~~\*/,op_like:/~~/,op_mod:"%",op_exp:"^",op_additive:{match:["||","-","#-","&&"]},op_compare:{match:[">",">=","<","<=","@>","<@","?","?|","?&","#>>",">>","<<","~","~*","!~","!~*","@@"]},ops_others:{match:["|","&","^","#"]},codeblock:{match:/\$\$(?:.|[\s\t\n\v\f\r])*?\$\$/s,lineBreaks:!0,value:e=>e.substring(2,e.length-2)}}),t.lexer.next=(o=t.lexer.next,()=>{let e,s=null;for(;e=o.call(t.lexer);){if("commentFullOpen"===e.type){if(null===s){s={nested:0,offset:e.offset,text:e.text};continue}s.nested++}if(null==s){if("space"!==e.type){if("commentLine"!==e.type)break;null==l||l.push(i(e))}}else if(s.text+=e.text,"commentFullClose"===e.type){if(0===s.nested){null==l||l.push(i(s)),s=null;continue}s.nested--}}if(c&&e){const t=e.offset,s={start:t,end:t+e.text.length};e._location=s}return e}),t.lexerAny=t.lexer;let l=null;const i=({offset:e,text:t})=>({_location:{start:e,end:e+t.length},comment:t});t.trackingComments=function(e){if(l)throw new Error("WAT ? Recursive comments tracking 🤔🤨 ?");try{l=[];const t=e();return{comments:l,ast:t}}finally{l=null}};let c=!1;function p(e,t){if(!c||!t||"object"!=typeof t)return t;const s=y(e,!0),r=y(e,!1);if(!s||!r)return t;if(s===r)t._location=s;else{const e={start:s.start,end:r.end};t._location=e}return t}t.tracking=function(e){if(c)return e();try{return c=!0,e()}finally{c=!1}},t.track=p;const m=Symbol("_literal"),u=Symbol("_doublequoted");function b(e){return Array.isArray(e)&&1===e.length&&(e=b(e[0])),Array.isArray(e)&&!e.length?null:e}function y(e,t){if(!e)return null;if(Array.isArray(e)){const s=t?1:-1;for(let r=t?0:e.length-1;r>=0&&r<e.length;r+=s){const s=y(e[r],t);if(s)return s}return null}return"object"!=typeof e?null:e._location}t.box=function(e,t,s){return c||s?p(e,{[m]:t,[u]:s}):t},t.doubleQuoted=function(e){const t=b(e);if("object"==typeof e&&(null==t?void 0:t[u]))return{doubleQuoted:!0}},t.unbox=function(e){var t;return"object"==typeof e&&null!==(t=null==e?void 0:e[m])&&void 0!==t?t:e}},function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.AstDefaultMapper=t.arrayNilMap=t.assignChanged=t.astMapper=void 0;const r=s(6);function a(e,t){if(!e)return e;let s=!1;for(const r of Object.keys(t))if(e[r]!==t[r]){s=!0;break}return s?(0,r.trimNullish)({...e,...t},0):e}function n(e,t){if(!(null==e?void 0:e.length))return e;let s=!1,r=e;for(let a=0;a<e.length;a++){const n=e[a],o=t(n);s||o&&o===n||(s=!0,r=e.slice(0,a)),o&&(s&&r.push(o))}return r}function o(e){switch(null==e?void 0:e.type){case"select":case"delete":case"insert":case"update":case"union":case"union all":case"with":return!0;default:return!1}}t.astMapper=function(e){const t=new l;return t.wrapped=e(t),t},t.assignChanged=a,t.arrayNilMap=n;class l{super(){return new c(this)}statement(e){switch(e.type){case"alter table":return this.alterTable(e);case"alter index":return this.alterIndex(e);case"commit":case"start transaction":case"rollback":return this.transaction(e);case"create index":return this.createIndex(e);case"create table":return this.createTable(e);case"truncate table":return this.truncateTable(e);case"delete":return this.delete(e);case"insert":return this.insert(e);case"with":return this.with(e);case"with recursive":return this.withRecursive(e);case"select":return this.selection(e);case"update":return this.update(e);case"create extension":return this.createExtension(e);case"tablespace":return this.tablespace(e);case"set":return this.setGlobal(e);case"set timezone":return this.setTimezone(e);case"set names":return this.setNames(e);case"create sequence":return this.createSequence(e);case"alter sequence":return this.alterSequence(e);case"begin":return this.begin(e);case"drop table":case"drop index":case"drop sequence":case"drop type":case"drop trigger":return this.drop(e);case"create enum":return this.createEnum(e);case"alter enum":return this.alterEnum(e);case"create composite type":return this.createCompositeType(e);case"union":case"union all":return this.union(e);case"show":return this.show(e);case"prepare":return this.prepare(e);case"deallocate":return this.deallocate(e);case"create view":return this.createView(e);case"create materialized view":return this.createMaterializedView(e);case"refresh materialized view":return this.refreshMaterializedView(e);case"create schema":return this.createSchema(e);case"raise":return this.raise(e);case"comment":return this.comment(e);case"do":return this.do(e);case"create function":return this.createFunction(e);case"drop function":return this.dropFunction(e);case"values":return this.values(e);default:throw r.NotSupported.never(e)}}comment(e){return e}createView(e){const t=this.select(e.query);if(!t)return null;const s=this.tableRef(e.name);return s?a(e,{query:t,name:s}):null}createMaterializedView(e){const t=this.select(e.query);if(!t)return null;const s=this.tableRef(e.name);return s?a(e,{query:t,name:s}):null}refreshMaterializedView(e){return e}do(e){return e}createFunction(e){const t=n(e.arguments,(e=>a(e,{type:this.dataType(e.type)})));let s;if(e.returns)switch(e.returns.kind){case"table":s=a(e.returns,{columns:n(e.returns.columns,(e=>{const t=this.dataType(e.type);return t&&a(e,{type:t})}))});break;case void 0:case null:case"array":s=this.dataType(e.returns);break;default:throw r.NotSupported.never(e.returns)}return a(e,{returns:s,arguments:t})}dropFunction(e){const t=n(e.arguments,(e=>a(e,{type:this.dataType(e.type)})));return a(e,{arguments:t})}show(e){return e}createEnum(e){return e}alterEnum(e){return e}createCompositeType(e){const t=n(e.attributes,(e=>a(e,{dataType:this.dataType(e.dataType)})));return a(e,{attributes:t})}drop(e){return e}alterSequence(e){return"set options"===e.change.type&&e.change.as&&this.dataType(e.change.as),e}begin(e){return e}createSequence(e){return e.options.as&&this.dataType(e.options.as),e}tablespace(e){return e}setGlobal(e){return e}setTimezone(e){return e}setNames(e){return e}update(e){if(!e)return e;const t=this.tableRef(e.table);if(!t)return null;const s=e.from&&this.from(e.from),r=e.where&&this.expr(e.where),o=n(e.sets,(e=>this.set(e)));if(!(null==o?void 0:o.length))return null;return a(e,{table:t,where:r,sets:o,from:s,returning:n(e.returning,(e=>this.selectionColumn(e)))})}insert(e){var t,s;const o=this.tableRef(e.into);if(!o)return null;const l=e.insert&&this.select(e.insert);if(!l)return null;const i=n(e.returning,(e=>this.selectionColumn(e)));let c=null===(t=e.onConflict)||void 0===t?void 0:t.on;switch(null==c?void 0:c.type){case"on constraint":break;case"on expr":c=a(c,{exprs:n(c.exprs,(e=>this.expr(e)))});break;case null:case void 0:break;default:throw r.NotSupported.never(c)}let p=null===(s=e.onConflict)||void 0===s?void 0:s.do;if(p&&"do nothing"!==p){const e=n(p.sets,(e=>this.set(e)));(null==e?void 0:e.length)?p.sets!==e&&(p={sets:e}):p="do nothing"}return a(e,{into:o,insert:l,returning:i,onConflict:p?a(e.onConflict,{do:p,on:c}):e.onConflict})}raise(e){return a(e,{formatExprs:e.formatExprs&&n(e.formatExprs,(e=>this.expr(e))),using:e.using&&n(e.using,(e=>a(e,{value:this.expr(e.value)})))})}delete(e){const t=this.tableRef(e.from);if(!t)return null;return a(e,{where:e.where&&this.expr(e.where),returning:n(e.returning,(e=>this.selectionColumn(e))),from:t})}createSchema(e){return e}createTable(e){const t=n(e.columns,(e=>{switch(e.kind){case"column":return this.createColumn(e);case"like table":return this.likeTable(e);default:throw r.NotSupported.never(e)}}));return(null==t?void 0:t.length)?a(e,{columns:t}):null}likeTable(e){const t=this.tableRef(e.like);return t?a(e,{like:t}):null}truncateTable(e){return e}constraint(e){switch(e.type){case"not null":case"null":case"primary key":case"unique":case"add generated":return e;case"default":{const t=this.expr(e.default);return t?a(e,{default:t}):null}case"check":{const t=this.expr(e.expr);return t?a(e,{expr:t}):null}case"reference":{const t=this.tableRef(e.foreignTable);return t?a(e,{foreignTable:t}):null}default:throw r.NotSupported.never(e)}}set(e){const t=this.expr(e.value);return t?a(e,{value:t}):null}dataType(e){return e}tableRef(e){return e}transaction(e){return e}createExtension(e){return e}createIndex(e){const t=n(e.expressions,(e=>{const t=this.expr(e.expression);return t===e.expression?e:t?{...e,expression:t}:null}));return(null==t?void 0:t.length)?a(e,{expressions:t}):null}prepare(e){const t=this.statement(e.statement);return t?a(e,{args:n(e.args,(e=>this.dataType(e))),statement:t}):null}deallocate(e){return e}alterIndex(e){return e}alterTable(e){var t;const s=this.tableRef(e.table);if(!s)return null;let r=[],n=!1;for(let s=0;s<((null===(t=e.changes)||void 0===t?void 0:t.length)||0);s++){const t=e.changes[s],a=this.tableAlteration(t,e.table);n=n||a!=t,a&&r.push(a)}return r.length?n?a(e,{table:s,changes:r}):e:null}tableAlteration(e,t){switch(e.type){case"add column":return this.addColumn(e,t);case"add constraint":return this.addConstraint(e,t);case"alter column":return this.alterColumn(e,t);case"rename":return this.renameTable(e,t);case"rename column":return this.renameColumn(e,t);case"rename constraint":return this.renameConstraint(e,t);case"drop column":return this.dropColumn(e,t);case"drop constraint":return this.dropConstraint(e,t);case"owner":return this.setTableOwner(e,t);default:throw r.NotSupported.never(e)}}dropColumn(e,t){return e}dropConstraint(e,t){return e}setTableOwner(e,t){return e}renameConstraint(e,t){return e}renameColumn(e,t){return e}renameTable(e,t){return e}alterColumn(e,t){let s;switch(e.alter.type){case"set default":s=this.setColumnDefault(e.alter,t,e.column);break;case"set type":s=this.setColumnType(e.alter,t,e.column);break;case"drop default":case"set not null":case"drop not null":s=this.alterColumnSimple(e.alter,t,e.column);break;case"add generated":s=this.alterColumnAddGenerated(e.alter,t,e.column);break;default:throw r.NotSupported.never(e.alter)}return s?a(e,{alter:s}):null}setColumnType(e,t,s){return a(e,{dataType:this.dataType(e.dataType)})}alterColumnAddGenerated(e,t,s){return e}alterColumnSimple(e,t,s){return e}setColumnDefault(e,t,s){const r=this.expr(e.default);return r?a(e,{default:r}):null}addConstraint(e,t){return e}addColumn(e,t){const s=this.createColumn(e.column);return s?a(e,{column:s}):null}createColumn(e){var t;const s=this.dataType(e.dataType);if(!s)return null;return a(e,{dataType:s,constraints:null!==(t=n(e.constraints,(e=>this.constraint(e))))&&void 0!==t?t:void 0})}select(e){switch(e.type){case"select":return this.selection(e);case"union":case"union all":return this.union(e);case"with":return this.with(e);case"values":return this.values(e);case"with recursive":return this.withRecursive(e);default:throw r.NotSupported.never(e)}}selection(e){var t,s;const r=n(e.from,(e=>this.from(e))),o=n(e.columns,(e=>this.selectionColumn(e))),l=e.where&&this.expr(e.where),i=n(e.groupBy,(e=>this.expr(e))),c=e.having&&this.expr(e.having),p=this.orderBy(e.orderBy),m=a(e.limit,{limit:this.expr(null===(t=e.limit)||void 0===t?void 0:t.limit),offset:this.expr(null===(s=e.limit)||void 0===s?void 0:s.offset)});return a(e,{from:r,columns:o,where:l,groupBy:i,having:c,orderBy:p,limit:m})}orderBy(e){return n(e,(e=>{const t=this.expr(e.by);return t?t===e.by?e:{...e,by:t}:null}))}union(e){const t=this.select(e.left),s=this.select(e.right);return t&&s?a(e,{left:t,right:s}):null!=t?t:s}with(e){const t=n(e.bind,(e=>{const t=this.statement(e.statement);return o(t)?a(e,{statement:t}):null}));if(!t)return null;const s=this.statement(e.in);return o(s)?a(e,{bind:t,in:s}):null}withRecursive(e){const t=this.union(e.bind);if(!t)return null;if("union"!==t.type&&"union all"!==t.type)return null;const s=this.statement(e.in);return o(s)?a(e,{bind:t,in:s}):null}from(e){switch(e.type){case"table":return this.fromTable(e);case"statement":return this.fromStatement(e);case"call":return this.fromCall(e);default:throw r.NotSupported.never(e)}}fromCall(e){const t=this.call(e);return t&&"call"===t.type?a(e,t):null}fromStatement(e){const t=this.select(e.statement);if(!t)return null;return a(e,{statement:t,join:e.join&&this.join(e.join)})}values(e){const t=n(e.values,(e=>n(e,(e=>this.expr(e)))));return(null==t?void 0:t.length)?a(e,{values:t}):null}join(e){const t=e.on&&this.expr(e.on);return t||e.using?a(e,{on:t}):e}fromTable(e){const t=this.tableRef(e.name);if(!t)return null;return a(e,{name:t,join:e.join&&this.join(e.join)})}selectionColumn(e){const t=this.expr(e.expr);return t?a(e,{expr:t}):null}expr(e){if(!e)return e;switch(e.type){case"binary":return this.binary(e);case"unary":return this.unary(e);case"ref":return this.ref(e);case"string":case"numeric":case"integer":case"boolean":case"constant":case"null":return this.constant(e);case"list":case"array":return this.array(e);case"array select":return this.arraySelect(e);case"call":return this.call(e);case"cast":return this.cast(e);case"case":return this.case(e);case"member":return this.member(e);case"arrayIndex":return this.arrayIndex(e);case"ternary":return this.ternary(e);case"select":case"union":case"union all":case"with":case"with recursive":return this.select(e);case"keyword":return this.valueKeyword(e);case"parameter":return this.parameter(e);case"extract":return this.extract(e);case"overlay":return this.callOverlay(e);case"substring":return this.callSubstring(e);case"values":return this.values(e);case"default":return this.default(e);default:throw r.NotSupported.never(e)}}arraySelect(e){const t=this.select(e.select);return t?a(e,{select:t}):null}extract(e){const t=this.expr(e.from);return t?a(e,{from:t}):null}valueKeyword(e){return e}ternary(e){const t=this.expr(e.value),s=this.expr(e.lo),r=this.expr(e.hi);return t&&s&&r?a(e,{value:t,lo:s,hi:r}):null}parameter(e){return e}arrayIndex(e){const t=this.expr(e.array),s=this.expr(e.index);return t&&s?a(e,{array:t,index:s}):null}member(e){const t=this.expr(e.operand);return t?a(e,{operand:t}):null}case(e){const t=e.value&&this.expr(e.value),s=n(e.whens,(e=>{const t=this.expr(e.when),s=this.expr(e.value);return t&&s?a(e,{value:s,when:t}):null}));if(!(null==s?void 0:s.length))return null;return a(e,{value:t,whens:s,else:e.else&&this.expr(e.else)})}cast(e){const t=this.expr(e.operand);return t?a(e,{operand:t}):null}call(e){var t;const s=n(e.args,(e=>this.expr(e)));if(!s)return null;const r=this.orderBy(e.orderBy),o=this.expr(e.filter),l=e.withinGroup?[e.withinGroup]:void 0;return a(e,{args:s,orderBy:r,filter:o,withinGroup:null===(t=this.orderBy(l))||void 0===t?void 0:t[0]})}callSubstring(e){return a(e,{value:this.expr(e.value),from:this.expr(e.from),for:this.expr(e.for)})}callOverlay(e){return a(e,{value:this.expr(e.value),placing:this.expr(e.placing),from:this.expr(e.from),for:this.expr(e.for)})}array(e){const t=n(e.expressions,(e=>this.expr(e)));return t?a(e,{expressions:t}):null}constant(e){return e}default(e){return e}ref(e){return e}unary(e){const t=this.expr(e.operand);return t?a(e,{operand:t}):null}binary(e){const t=this.expr(e.left),s=this.expr(e.right);return t&&s?a(e,{left:t,right:s}):null}}t.AstDefaultMapper=l;const i=l.prototype;for(const e of Object.getOwnPropertyNames(i)){const t=i[e];"constructor"!==e&&"super"!==e&&"function"==typeof t&&Object.defineProperty(i,e,{configurable:!1,get:()=>function(...s){var r;if(this.skipNext)return this.skipNext=!1,t.apply(this,s);const a=null===(r=this.wrapped)||void 0===r?void 0:r[e];return a?a.apply(this.wrapped,s):t.apply(this,s)}})}class c extends l{constructor(e){super(),this.parent=e}}for(const e of Object.getOwnPropertyNames(i)){const t=i[e];"constructor"!==e&&"super"!==e&&"function"==typeof t&&Object.defineProperty(c.prototype,e,{configurable:!1,get:()=>function(...e){return this.parent.skipNext=!0,t.apply(this.parent,e)}})}},function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.sqlKeywords=void 0,t.sqlKeywords=["ALL","ANALYSE","ANALYZE","AND","ANY","ARRAY","AS","ASC","ASYMMETRIC","AUTHORIZATION","BINARY","BOTH","CASE","CAST","CHECK","COLLATE","COLLATION","CONCURRENTLY","CONSTRAINT","CREATE","CROSS","CURRENT_CATALOG","CURRENT_DATE","CURRENT_ROLE","CURRENT_SCHEMA","CURRENT_TIME","CURRENT_TIMESTAMP","CURRENT_USER","DEFAULT","DEFERRABLE","DESC","DISTINCT","DO","ELSE","END","EXCEPT","FALSE","FETCH","FOR","FOREIGN","FREEZE","FROM","FULL","GRANT","GROUP","HAVING","ILIKE","IN","INITIALLY","INNER","INTERSECT","INTO","IS","ISNULL","JOIN","LATERAL","LEADING","LEFT","LIKE","LIMIT","LOCALTIME","LOCALTIMESTAMP","NATURAL","NOT","NOTNULL","NULL","OFFSET","ON","ONLY","OR","ORDER","OUTER","OVERLAPS","PLACING","PRIMARY","REFERENCES","RETURNING","RIGHT","SELECT","SESSION_USER","SIMILAR","SOME","SYMMETRIC","TABLE","TABLESAMPLE","THEN","TO","TRAILING","TRUE","UNION","UNIQUE","USER","USING","VARIADIC","VERBOSE","WHEN","WHERE","WINDOW","WITH","PRECISION"]},function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.intervalToString=t.normalizeInterval=t.buildInterval=void 0;const r=[["years",12],["months",30],["days",24],["hours",60],["minutes",60],["seconds",1e3],["milliseconds",0]];function*a(e){if("number"==typeof e[1])yield e;else for(const t of e)yield*a(t)}function n(e){var t,s,a,n,o,l,i,c,p;const m={...e};for(let e=0;e<r.length;e++){const[n,o]=r[e],l=null!==(t=m[n])&&void 0!==t?t:0,i=l>=0?Math.floor(l):Math.ceil(l);if(!l||i===l)continue;const c=null===(s=r[e+1])||void 0===s?void 0:s[0];c&&(m[c]=(null!==(a=m[c])&&void 0!==a?a:0)+o*(l-i)),m[n]=i}if(m.months||m.years){const e=(null!==(n=m.months)&&void 0!==n?n:0)+12*(null!==(o=m.years)&&void 0!==o?o:0);m.months=e%12,m.years=(e-m.months)/12}let u=3600*(null!==(l=m.hours)&&void 0!==l?l:0)+60*(null!==(i=m.minutes)&&void 0!==i?i:0)+(null!==(c=m.seconds)&&void 0!==c?c:0)+(null!==(p=m.milliseconds)&&void 0!==p?p:0)/1e3,b=1;u<0&&(b=-1,u=-u),u>=3600?(m.hours=b*Math.floor(u/3600),u-=b*m.hours*3600):delete m.hours,u>=60?(m.minutes=b*Math.floor(u/60),u-=b*m.minutes*60):delete m.minutes,u>0?(m.seconds=b*Math.floor(u),u-=b*m.seconds):delete m.seconds,u>0?m.milliseconds=b*Math.round(1e3*u):delete m.milliseconds;for(const[e]of r)m[e]||delete m[e];return m}function o(e){return(e=Math.abs(e))<10?"0"+e:e.toString()}function l(e){return e&&e<0}t.buildInterval=function(e,t){var s;const r={};if("invalid"===t)throw new Error(`invalid input syntax for type interval: "${e}"`);for(const[e,n]of a(t))r[e]=(null!==(s=r[e])&&void 0!==s?s:0)+n;return r},t.normalizeInterval=n,t.intervalToString=function(e){var t,s,r;const a=[];if((e=n(e)).years&&a.push(1===e.years?"1 year":e.years+" years"),e.months&&a.push(1===e.months?"1 month":e.months+" months"),e.days&&a.push(1===e.days?"1 day":e.days+" days"),e.hours||e.minutes||e.seconds||e.milliseconds){let n=`${o(null!==(t=e.hours)&&void 0!==t?t:0)}:${o(null!==(s=e.minutes)&&void 0!==s?s:0)}:${o(null!==(r=e.seconds)&&void 0!==r?r:0)}`;e.milliseconds&&(n+=(e.milliseconds/1e3).toString().substr(1)),(l(e.hours)||l(e.minutes)||l(e.seconds)||l(e.milliseconds))&&(n="-"+n),a.push(n)}return a.join(" ")}},function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.astVisitor=void 0;const r=s(2);class a{super(){return new o(this)}}const n=r.AstDefaultMapper.prototype;for(const e of Object.getOwnPropertyNames(n)){const t=n[e];"constructor"!==e&&"super"!==e&&"function"==typeof t&&Object.defineProperty(a.prototype,e,{configurable:!1,get:()=>function(...s){const r=this.visitor[e];return r?(r.apply(this.visitor,s),s[0]):t.apply(this,s)}})}class o{constructor(e){this.parent=e}}for(const e of Object.getOwnPropertyNames(n)){const t=n[e];"constructor"!==e&&"super"!==e&&"function"==typeof t&&Object.defineProperty(o.prototype,e,{configurable:!1,get:()=>function(...e){return t.apply(this.parent,e)}})}t.astVisitor=function(e){return(0,r.astMapper)((t=>{const s=new a;return s.mapper=t,s.visitor=e(s),s}))}},function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.trimNullish=t.NotSupported=void 0;class r extends Error{constructor(e){super("Not supported"+(e?": "+e:""))}static never(e,t){return new r(`${null!=t?t:""} ${JSON.stringify(e)}`)}}t.NotSupported=r,t.trimNullish=function e(t,s=5){if(s<0)return t;if(t instanceof Array&&t.forEach((t=>e(t,s-1))),"object"!=typeof t||t instanceof Date)return t;if(!t)return t;for(const r of Object.keys(t)){const a=t[r];null==a?delete t[r]:e(a,s-1)}return t}},function(e,t,s){"use strict";var r=this&&this.__createBinding||(Object.create?function(e,t,s,r){void 0===r&&(r=s);var a=Object.getOwnPropertyDescriptor(t,s);a&&!("get"in a?!t.__esModule:a.writable||a.configurable)||(a={enumerable:!0,get:function(){return t[s]}}),Object.defineProperty(e,r,a)}:function(e,t,s,r){void 0===r&&(r=s),e[r]=t[s]}),a=this&&this.__exportStar||function(e,t){for(var s in e)"default"===s||Object.prototype.hasOwnProperty.call(t,s)||r(t,e,s)};Object.defineProperty(t,"__esModule",{value:!0}),t.normalizeInterval=t.intervalToString=t.toSql=t.astMapper=t.assignChanged=t.arrayNilMap=t.astVisitor=t.parseWithComments=t.parseIntervalLiteral=t.parseGeometricLiteral=t.parseArrayLiteral=t.parseFirst=t.parse=void 0;var n=s(8);Object.defineProperty(t,"parse",{enumerable:!0,get:function(){return n.parse}}),Object.defineProperty(t,"parseFirst",{enumerable:!0,get:function(){return n.parseFirst}}),Object.defineProperty(t,"parseArrayLiteral",{enumerable:!0,get:function(){return n.parseArrayLiteral}}),Object.defineProperty(t,"parseGeometricLiteral",{enumerable:!0,get:function(){return n.parseGeometricLiteral}}),Object.defineProperty(t,"parseIntervalLiteral",{enumerable:!0,get:function(){return n.parseIntervalLiteral}}),Object.defineProperty(t,"parseWithComments",{enumerable:!0,get:function(){return n.parseWithComments}});var o=s(5);Object.defineProperty(t,"astVisitor",{enumerable:!0,get:function(){return o.astVisitor}});var l=s(2);Object.defineProperty(t,"arrayNilMap",{enumerable:!0,get:function(){return l.arrayNilMap}}),Object.defineProperty(t,"assignChanged",{enumerable:!0,get:function(){return l.assignChanged}}),Object.defineProperty(t,"astMapper",{enumerable:!0,get:function(){return l.astMapper}});var i=s(19);Object.defineProperty(t,"toSql",{enumerable:!0,get:function(){return i.toSql}}),a(s(21),t);var c=s(4);Object.defineProperty(t,"intervalToString",{enumerable:!0,get:function(){return c.intervalToString}}),Object.defineProperty(t,"normalizeInterval",{enumerable:!0,get:function(){return c.normalizeInterval}})},function(e,t,s){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.parseGeometricLiteral=t.parseIntervalLiteral=t.parseArrayLiteral=t.parse=t.parseWithComments=t.parseFirst=void 0;const a=s(9),n=r(s(10)),o=r(s(11)),l=r(s(13)),i=r(s(15)),c=r(s(17)),p=s(4),m=s(1);let u,b,y,d,_;function $(e,t){u||(u=a.Grammar.fromCompiled(n.default));const s="string"==typeof t?t:null==t?void 0:t.entry,r="string"==typeof t?null:t,o=()=>h(e,u,s);let l=(null==r?void 0:r.locationTracking)?(0,m.tracking)(o):o();return"string"==typeof t||Array.isArray(l)||(l=[l]),l}function h(e,t,s){try{t.start=null!=s?s:"main";const r=new a.Parser(t);r.feed(e);const n=r.finish();if(!n.length)throw new Error("Unexpected end of input");if(1!==n.length)throw new Error(`💀 Ambiguous SQL syntax: Please file an issue stating the request that has failed at https://github.com/oguimbal/pgsql-ast-parser:\n\n        ${e}\n\n        `);return n[0]}catch(e){if("string"!=typeof(null==e?void 0:e.message))throw e;let t=e.message,s=null;const r=[],a=/A (.+) token based on:/g;let n;for(;n=a.exec(t);)s=null!=s?s:t.substr(0,n.index),r.push(`    - A "${n[1]}" token`);throw s&&(t=s+r.join("\n")+"\n\n"),e.message=t,e}}t.parseFirst=function(e){return $(e)[0]},t.parseWithComments=function(e,t){return(0,m.trackingComments)((()=>$(e,t)))},t.parse=$,t.parseArrayLiteral=function(e){return b||(b=a.Grammar.fromCompiled(o.default)),h(e,b)},t.parseIntervalLiteral=function(e){if(e.startsWith("P"))return _||(_=a.Grammar.fromCompiled(c.default)),(0,p.buildInterval)(e,h(e,_));{d||(d=a.Grammar.fromCompiled(i.default));const t=e.toLowerCase();return(0,p.buildInterval)(e,h(t,d))}},t.parseGeometricLiteral=function(e,t){return y||(y=a.Grammar.fromCompiled(l.default)),h(e,y,t)}},function(e,t){e.exports=s(8831)},function(e,t,s){"use strict";function r(e){return e[0]}Object.defineProperty(t,"__esModule",{value:!0});const a=s(1),n=s(1);function o(e){return l(e,void 0)}function l(e,t){const s=d(e);return t&&0!==t.length?(0,n.track)(e,{name:s,columns:t.map((e=>({name:d(e)})))}):(0,n.track)(e,{name:s})}function i(e){const t=d(e);return(0,n.track)(e,{value:t})}function c(e){return Array.isArray(e)&&1===e.length&&(e=c(e[0])),Array.isArray(e)&&!e.length?null:(0,n.unbox)(e)}const p=e=>t=>(0,n.track)(t,t[e]),m=e=>Array.isArray(e)?(0,n.track)(e[e.length-1],e[e.length-1]):e;function u(e){if(Array.isArray(e)){const t=[];for(const s of e)t.push(...u(s));return t}return e?[e]:[]}function b(e){var t;return null!==(t=null==(e=(0,n.unbox)(e))?void 0:e.value)&&void 0!==t?t:e}function y(e){return u((0,n.unbox)(e)).filter((e=>!!e)).map((e=>b(e))).filter((e=>"string"==typeof e)).map((e=>e.trim())).filter((e=>!!e))}function d(e,t){return y(e).join(t||"")}function _(e){const t={};for(const[s,r]of e)t[s]=r;return t}const $={sensitivity:"accent"},h=e=>(t,s,r)=>{const a=b(t[0]);return o=e,0===a.localeCompare(o,void 0,$)?(0,n.box)(t,e):r;var o},f=h,w=(...e)=>{const t=new Set(e);return(e,s,r)=>{const a="string"==typeof e[0]?e[0]:e[0].value;return t.has(a)?a:r}};function x(e,t){const s=new Set,r=t.map(n.unbox);for(const[t,a]of r){if(s.has(t))throw new Error("conflicting or redundant options");s.add(t),e[t]=(0,n.unbox)(a)}}const g={Lexer:a.lexerAny,ParserRules:[{name:"lparen",symbols:[a.lexerAny.has("lparen")?{type:"lparen"}:lparen]},{name:"rparen",symbols:[a.lexerAny.has("rparen")?{type:"rparen"}:rparen]},{name:"number$subexpression$1",symbols:["float"]},{name:"number$subexpression$1",symbols:["int"]},{name:"number",symbols:["number$subexpression$1"],postprocess:c},{name:"dot",symbols:[a.lexerAny.has("dot")?{type:"dot"}:dot],postprocess:r},{name:"float",symbols:[a.lexerAny.has("float")?{type:"float"}:float],postprocess:e=>(0,n.box)(e,parseFloat(c(e)))},{name:"int",symbols:[a.lexerAny.has("int")?{type:"int"}:int],postprocess:e=>(0,n.box)(e,parseInt(c(e),10))},{name:"comma",symbols:[a.lexerAny.has("comma")?{type:"comma"}:comma],postprocess:r},{name:"star",symbols:[a.lexerAny.has("star")?{type:"star"}:star],postprocess:e=>(0,n.box)(e,e[0].value)},{name:"string$subexpression$1",symbols:[a.lexerAny.has("string")?{type:"string"}:string]},{name:"string$subexpression$1",symbols:[a.lexerAny.has("eString")?{type:"eString"}:eString]},{name:"string",symbols:["string$subexpression$1"],postprocess:e=>(0,n.box)(e,c(e[0]).value)},{name:"ident",symbols:["word"],postprocess:p(0)},{name:"word",symbols:[a.lexerAny.has("kw_primary")?{type:"kw_primary"}:kw_primary],postprocess:e=>(0,n.box)(e,"primary")},{name:"word",symbols:[a.lexerAny.has("kw_unique")?{type:"kw_unique"}:kw_unique],postprocess:e=>(0,n.box)(e,"unique")},{name:"word",symbols:[a.lexerAny.has("quoted_word")?{type:"quoted_word"}:quoted_word],postprocess:e=>(0,n.box)(e,e[0].value,!0)},{name:"word",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:e=>(0,n.box)(e,e[0].value)},{name:"collist_paren",symbols:["lparen","collist","rparen"],postprocess:p(1)},{name:"collist$ebnf$1",symbols:[]},{name:"collist$ebnf$1$subexpression$1",symbols:["comma","ident"],postprocess:m},{name:"collist$ebnf$1",symbols:["collist$ebnf$1","collist$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"collist",symbols:["ident","collist$ebnf$1"],postprocess:([e,t])=>[e,...t||[]]},{name:"kw_between",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("between")},{name:"kw_conflict",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("conflict")},{name:"kw_nothing",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("nothing")},{name:"kw_begin",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("begin")},{name:"kw_if",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("if")},{name:"kw_exists",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("exists")},{name:"kw_key",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("key")},{name:"kw_index",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("index")},{name:"kw_extension",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("extension")},{name:"kw_schema",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("schema")},{name:"kw_nulls",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("nulls")},{name:"kw_first",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("first")},{name:"kw_last",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("last")},{name:"kw_start",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("start")},{name:"kw_restart",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("restart")},{name:"kw_filter",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("filter")},{name:"kw_commit",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("commit")},{name:"kw_tablespace",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("tablespace")},{name:"kw_transaction",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("transaction")},{name:"kw_work",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("work")},{name:"kw_read",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("read")},{name:"kw_write",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("write")},{name:"kw_isolation",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("isolation")},{name:"kw_level",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("level")},{name:"kw_serializable",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("serializable")},{name:"kw_rollback",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("rollback")},{name:"kw_insert",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("insert")},{name:"kw_value",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("value")},{name:"kw_values",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("values")},{name:"kw_update",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("update")},{name:"kw_column",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("column")},{name:"kw_set",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("set")},{name:"kw_version",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("version")},{name:"kw_alter",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("alter")},{name:"kw_rename",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("rename")},{name:"kw_sequence",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("sequence")},{name:"kw_temp",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("temp")},{name:"kw_temporary",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("temporary")},{name:"kw_add",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("add")},{name:"kw_owner",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("owner")},{name:"kw_owned",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("owned")},{name:"kw_including",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("including")},{name:"kw_excluding",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("excluding")},{name:"kw_none",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("none")},{name:"kw_drop",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("drop")},{name:"kw_operator",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("operator")},{name:"kw_minvalue",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("minvalue")},{name:"kw_maxvalue",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("maxvalue")},{name:"kw_data",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("data")},{name:"kw_type",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("type")},{name:"kw_trigger",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("trigger")},{name:"kw_delete",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("delete")},{name:"kw_cache",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("cache")},{name:"kw_cascade",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("cascade")},{name:"kw_no",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("no")},{name:"kw_timestamp",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("timestamp")},{name:"kw_cycle",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("cycle")},{name:"kw_function",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("function")},{name:"kw_returns",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("returns")},{name:"kw_language",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("language")},{name:"kw_out",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("out")},{name:"kw_inout",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("inout")},{name:"kw_variadic",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("variadic")},{name:"kw_action",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("action")},{name:"kw_restrict",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("restrict")},{name:"kw_truncate",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("truncate")},{name:"kw_increment",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("increment")},{name:"kw_by",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("by")},{name:"kw_row",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("row")},{name:"kw_rows",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("rows")},{name:"kw_next",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("next")},{name:"kw_match",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("match")},{name:"kw_replace",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("replace")},{name:"kw_recursive",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("recursive")},{name:"kw_view",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("view")},{name:"kw_cascaded",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("cascaded")},{name:"kw_unlogged",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("unlogged")},{name:"kw_global",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("global")},{name:"kw_option",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("option")},{name:"kw_materialized",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("materialized")},{name:"kw_partial",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("partial")},{name:"kw_partition",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("partition")},{name:"kw_simple",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("simple")},{name:"kw_generated",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("generated")},{name:"kw_always",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("always")},{name:"kw_identity",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("identity")},{name:"kw_name",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("name")},{name:"kw_enum",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("enum")},{name:"kw_show",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("show")},{name:"kw_ordinality",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("ordinality")},{name:"kw_overriding",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("overriding")},{name:"kw_over",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("over")},{name:"kw_system",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("system")},{name:"kw_comment",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("comment")},{name:"kw_time",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("time")},{name:"kw_names",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("names")},{name:"kw_at",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("at")},{name:"kw_zone",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("zone")},{name:"kw_interval",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("interval")},{name:"kw_hour",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("hour")},{name:"kw_minute",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("minute")},{name:"kw_local",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("local")},{name:"kw_session",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("session")},{name:"kw_prepare",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("prepare")},{name:"kw_deallocate",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("deallocate")},{name:"kw_raise",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("raise")},{name:"kw_continue",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("continue")},{name:"kw_share",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("share")},{name:"kw_refresh",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("refresh")},{name:"kw_nowait",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("nowait")},{name:"kw_skip",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("skip")},{name:"kw_locked",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("locked")},{name:"kw_within",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:h("within")},{name:"kw_ifnotexists",symbols:["kw_if",a.lexerAny.has("kw_not")?{type:"kw_not"}:kw_not,"kw_exists"]},{name:"kw_ifexists",symbols:["kw_if","kw_exists"]},{name:"kw_withordinality",symbols:[a.lexerAny.has("kw_with")?{type:"kw_with"}:kw_with,"kw_ordinality"]},{name:"kw_not_null",symbols:[a.lexerAny.has("kw_not")?{type:"kw_not"}:kw_not,a.lexerAny.has("kw_null")?{type:"kw_null"}:kw_null]},{name:"kw_primary_key",symbols:[a.lexerAny.has("kw_primary")?{type:"kw_primary"}:kw_primary,"kw_key"]},{name:"data_type$ebnf$1$subexpression$1$macrocall$2",symbols:["int"]},{name:"data_type$ebnf$1$subexpression$1$macrocall$1$ebnf$1",symbols:[]},{name:"data_type$ebnf$1$subexpression$1$macrocall$1$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("comma")?{type:"comma"}:comma,"data_type$ebnf$1$subexpression$1$macrocall$2"],postprocess:m},{name:"data_type$ebnf$1$subexpression$1$macrocall$1$ebnf$1",symbols:["data_type$ebnf$1$subexpression$1$macrocall$1$ebnf$1","data_type$ebnf$1$subexpression$1$macrocall$1$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"data_type$ebnf$1$subexpression$1$macrocall$1",symbols:["data_type$ebnf$1$subexpression$1$macrocall$2","data_type$ebnf$1$subexpression$1$macrocall$1$ebnf$1"],postprocess:([e,t])=>[e,...t||[]]},{name:"data_type$ebnf$1$subexpression$1",symbols:["lparen","data_type$ebnf$1$subexpression$1$macrocall$1","rparen"],postprocess:p(1)},{name:"data_type$ebnf$1",symbols:["data_type$ebnf$1$subexpression$1"],postprocess:r},{name:"data_type$ebnf$1",symbols:[],postprocess:()=>null},{name:"data_type$ebnf$2$subexpression$1",symbols:[a.lexerAny.has("kw_array")?{type:"kw_array"}:kw_array]},{name:"data_type$ebnf$2$subexpression$1$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("lbracket")?{type:"lbracket"}:lbracket,a.lexerAny.has("rbracket")?{type:"rbracket"}:rbracket]},{name:"data_type$ebnf$2$subexpression$1$ebnf$1",symbols:["data_type$ebnf$2$subexpression$1$ebnf$1$subexpression$1"]},{name:"data_type$ebnf$2$subexpression$1$ebnf$1$subexpression$2",symbols:[a.lexerAny.has("lbracket")?{type:"lbracket"}:lbracket,a.lexerAny.has("rbracket")?{type:"rbracket"}:rbracket]},{name:"data_type$ebnf$2$subexpression$1$ebnf$1",symbols:["data_type$ebnf$2$subexpression$1$ebnf$1","data_type$ebnf$2$subexpression$1$ebnf$1$subexpression$2"],postprocess:e=>e[0].concat([e[1]])},{name:"data_type$ebnf$2$subexpression$1",symbols:["data_type$ebnf$2$subexpression$1$ebnf$1"]},{name:"data_type$ebnf$2",symbols:["data_type$ebnf$2$subexpression$1"],postprocess:r},{name:"data_type$ebnf$2",symbols:[],postprocess:()=>null},{name:"data_type",symbols:["data_type_simple","data_type$ebnf$1","data_type$ebnf$2"],postprocess:e=>{let t=e[2];let s;if(s={...c(e[0]),...Array.isArray(e[1])&&e[1].length?{config:e[1].map(c)}:{}},t){"kw_array"===t[0].type&&(t=[["array"]]);for(const e of t[0])s={kind:"array",arrayOf:s}}return(0,n.track)(e,s)}},{name:"data_type_list$ebnf$1",symbols:[]},{name:"data_type_list$ebnf$1$subexpression$1",symbols:["comma","data_type"],postprocess:m},{name:"data_type_list$ebnf$1",symbols:["data_type_list$ebnf$1","data_type_list$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"data_type_list",symbols:["data_type","data_type_list$ebnf$1"],postprocess:([e,t])=>[e,...t||[]]},{name:"data_type_simple",symbols:["data_type_text"],postprocess:e=>(0,n.track)(e,{name:d(e," ")})},{name:"data_type_simple",symbols:["data_type_numeric"],postprocess:e=>(0,n.track)(e,{name:d(e," ")})},{name:"data_type_simple",symbols:["data_type_date"]},{name:"data_type_simple",symbols:["qualified_name_mark_quotes"]},{name:"data_type_numeric$subexpression$1",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:f("double")},{name:"data_type_numeric",symbols:["data_type_numeric$subexpression$1",a.lexerAny.has("kw_precision")?{type:"kw_precision"}:kw_precision]},{name:"data_type_text$subexpression$1",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:w("character","bit")},{name:"data_type_text$subexpression$2",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:f("varying")},{name:"data_type_text",symbols:["data_type_text$subexpression$1","data_type_text$subexpression$2"]},{name:"data_type_date$subexpression$1",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:w("timestamp","time")},{name:"data_type_date$subexpression$2",symbols:[a.lexerAny.has("kw_with")?{type:"kw_with"}:kw_with]},{name:"data_type_date$subexpression$2",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:f("without")},{name:"data_type_date$subexpression$3",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:f("time")},{name:"data_type_date$subexpression$4",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:f("zone")},{name:"data_type_date",symbols:["data_type_date$subexpression$1","data_type_date$subexpression$2","data_type_date$subexpression$3","data_type_date$subexpression$4"],postprocess:e=>(0,n.track)(e,{name:d(e," ")})},{name:"data_type_date$subexpression$5",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:w("timestamp","time")},{name:"data_type_date$subexpression$6",symbols:["lparen","int","rparen"],postprocess:p(1)},{name:"data_type_date$subexpression$7",symbols:[a.lexerAny.has("kw_with")?{type:"kw_with"}:kw_with]},{name:"data_type_date$subexpression$7",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:f("without")},{name:"data_type_date$subexpression$8",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:f("time")},{name:"data_type_date$subexpression$9",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:f("zone")},{name:"data_type_date",symbols:["data_type_date$subexpression$5","data_type_date$subexpression$6","data_type_date$subexpression$7","data_type_date$subexpression$8","data_type_date$subexpression$9"],postprocess:e=>(0,n.track)(e,{name:`timestamp ${d(e[2])} time zone`,config:[(0,n.unbox)(e[1])]})},{name:"ident_aliased$subexpression$1",symbols:[a.lexerAny.has("kw_as")?{type:"kw_as"}:kw_as,"ident"],postprocess:m},{name:"ident_aliased",symbols:["ident_aliased$subexpression$1"]},{name:"ident_aliased",symbols:["ident"],postprocess:c},{name:"table_ref",symbols:["qualified_name"],postprocess:c},{name:"qcolumn$ebnf$1$subexpression$1",symbols:["dot","ident"],postprocess:m},{name:"qcolumn$ebnf$1",symbols:["qcolumn$ebnf$1$subexpression$1"],postprocess:r},{name:"qcolumn$ebnf$1",symbols:[],postprocess:()=>null},{name:"qcolumn",symbols:["ident","dot","ident","qcolumn$ebnf$1"],postprocess:e=>e[3]?(0,n.track)(e,{schema:(0,n.unbox)(e[0]),table:(0,n.unbox)(e[2]),column:(0,n.unbox)(e[3])}):(0,n.track)(e,{table:(0,n.unbox)(e[0]),column:(0,n.unbox)(e[2])})},{name:"table_ref_aliased$ebnf$1",symbols:["ident_aliased"],postprocess:r},{name:"table_ref_aliased$ebnf$1",symbols:[],postprocess:()=>null},{name:"table_ref_aliased",symbols:["table_ref","table_ref_aliased$ebnf$1"],postprocess:e=>{const t=c(e[1]);return(0,n.track)(e,{...c(e[0]),...t?{alias:t}:{}})}},{name:"qualified_name",symbols:["qname_ident"],postprocess:e=>(0,n.track)(e,{name:d(e)})},{name:"qualified_name",symbols:["ident","dot","ident_extended"],postprocess:e=>{const t=d(e[0]),s=d(e[2]);return(0,n.track)(e,{schema:t,name:s})}},{name:"qualified_name",symbols:[a.lexerAny.has("kw_current_schema")?{type:"kw_current_schema"}:kw_current_schema],postprocess:e=>(0,n.track)(e,{name:"current_schema"})},{name:"qualified_name_mark_quotes",symbols:["qname_ident"],postprocess:e=>(0,n.track)(e,{name:d(e),...(0,n.doubleQuoted)(e)})},{name:"qualified_name_mark_quotes",symbols:["ident","dot","ident_extended"],postprocess:e=>{const t=d(e[0]),s=d(e[2]);return(0,n.track)(e,{schema:t,name:s,...(0,n.doubleQuoted)(e[2])})}},{name:"qualified_name_mark_quotes",symbols:[a.lexerAny.has("kw_current_schema")?{type:"kw_current_schema"}:kw_current_schema],postprocess:e=>(0,n.track)(e,{name:"current_schema"})},{name:"qname_ident",symbols:["ident"]},{name:"qname_ident",symbols:[a.lexerAny.has("kw_precision")?{type:"kw_precision"}:kw_precision]},{name:"qname",symbols:["qualified_name"],postprocess:c},{name:"any_keyword",symbols:[a.lexerAny.has("kw_all")?{type:"kw_all"}:kw_all]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_analyse")?{type:"kw_analyse"}:kw_analyse]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_analyze")?{type:"kw_analyze"}:kw_analyze]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_and")?{type:"kw_and"}:kw_and]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_any")?{type:"kw_any"}:kw_any]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_array")?{type:"kw_array"}:kw_array]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_as")?{type:"kw_as"}:kw_as]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_asc")?{type:"kw_asc"}:kw_asc]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_asymmetric")?{type:"kw_asymmetric"}:kw_asymmetric]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_authorization")?{type:"kw_authorization"}:kw_authorization]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_binary")?{type:"kw_binary"}:kw_binary]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_both")?{type:"kw_both"}:kw_both]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_case")?{type:"kw_case"}:kw_case]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_cast")?{type:"kw_cast"}:kw_cast]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_check")?{type:"kw_check"}:kw_check]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_collate")?{type:"kw_collate"}:kw_collate]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_collation")?{type:"kw_collation"}:kw_collation]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_concurrently")?{type:"kw_concurrently"}:kw_concurrently]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_constraint")?{type:"kw_constraint"}:kw_constraint]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_create")?{type:"kw_create"}:kw_create]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_cross")?{type:"kw_cross"}:kw_cross]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_current_catalog")?{type:"kw_current_catalog"}:kw_current_catalog]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_current_date")?{type:"kw_current_date"}:kw_current_date]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_current_role")?{type:"kw_current_role"}:kw_current_role]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_current_schema")?{type:"kw_current_schema"}:kw_current_schema]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_current_time")?{type:"kw_current_time"}:kw_current_time]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_current_timestamp")?{type:"kw_current_timestamp"}:kw_current_timestamp]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_current_user")?{type:"kw_current_user"}:kw_current_user]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_default")?{type:"kw_default"}:kw_default]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_deferrable")?{type:"kw_deferrable"}:kw_deferrable]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_desc")?{type:"kw_desc"}:kw_desc]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_distinct")?{type:"kw_distinct"}:kw_distinct]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_do")?{type:"kw_do"}:kw_do]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_else")?{type:"kw_else"}:kw_else]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_end")?{type:"kw_end"}:kw_end]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_except")?{type:"kw_except"}:kw_except]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_false")?{type:"kw_false"}:kw_false]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_fetch")?{type:"kw_fetch"}:kw_fetch]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_for")?{type:"kw_for"}:kw_for]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_foreign")?{type:"kw_foreign"}:kw_foreign]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_freeze")?{type:"kw_freeze"}:kw_freeze]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_from")?{type:"kw_from"}:kw_from]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_full")?{type:"kw_full"}:kw_full]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_grant")?{type:"kw_grant"}:kw_grant]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_group")?{type:"kw_group"}:kw_group]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_having")?{type:"kw_having"}:kw_having]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_ilike")?{type:"kw_ilike"}:kw_ilike]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_in")?{type:"kw_in"}:kw_in]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_initially")?{type:"kw_initially"}:kw_initially]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_inner")?{type:"kw_inner"}:kw_inner]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_intersect")?{type:"kw_intersect"}:kw_intersect]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_into")?{type:"kw_into"}:kw_into]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_is")?{type:"kw_is"}:kw_is]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_isnull")?{type:"kw_isnull"}:kw_isnull]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_join")?{type:"kw_join"}:kw_join]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_lateral")?{type:"kw_lateral"}:kw_lateral]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_leading")?{type:"kw_leading"}:kw_leading]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_left")?{type:"kw_left"}:kw_left]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_like")?{type:"kw_like"}:kw_like]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_limit")?{type:"kw_limit"}:kw_limit]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_localtime")?{type:"kw_localtime"}:kw_localtime]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_localtimestamp")?{type:"kw_localtimestamp"}:kw_localtimestamp]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_natural")?{type:"kw_natural"}:kw_natural]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_not")?{type:"kw_not"}:kw_not]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_notnull")?{type:"kw_notnull"}:kw_notnull]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_null")?{type:"kw_null"}:kw_null]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_offset")?{type:"kw_offset"}:kw_offset]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_on")?{type:"kw_on"}:kw_on]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_only")?{type:"kw_only"}:kw_only]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_or")?{type:"kw_or"}:kw_or]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_order")?{type:"kw_order"}:kw_order]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_outer")?{type:"kw_outer"}:kw_outer]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_overlaps")?{type:"kw_overlaps"}:kw_overlaps]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_placing")?{type:"kw_placing"}:kw_placing]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_primary")?{type:"kw_primary"}:kw_primary]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_references")?{type:"kw_references"}:kw_references]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_returning")?{type:"kw_returning"}:kw_returning]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_right")?{type:"kw_right"}:kw_right]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_select")?{type:"kw_select"}:kw_select]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_session_user")?{type:"kw_session_user"}:kw_session_user]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_similar")?{type:"kw_similar"}:kw_similar]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_some")?{type:"kw_some"}:kw_some]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_symmetric")?{type:"kw_symmetric"}:kw_symmetric]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_table")?{type:"kw_table"}:kw_table]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_tablesample")?{type:"kw_tablesample"}:kw_tablesample]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_then")?{type:"kw_then"}:kw_then]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_to")?{type:"kw_to"}:kw_to]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_trailing")?{type:"kw_trailing"}:kw_trailing]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_true")?{type:"kw_true"}:kw_true]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_union")?{type:"kw_union"}:kw_union]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_unique")?{type:"kw_unique"}:kw_unique]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_user")?{type:"kw_user"}:kw_user]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_using")?{type:"kw_using"}:kw_using]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_variadic")?{type:"kw_variadic"}:kw_variadic]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_verbose")?{type:"kw_verbose"}:kw_verbose]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_when")?{type:"kw_when"}:kw_when]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_where")?{type:"kw_where"}:kw_where]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_window")?{type:"kw_window"}:kw_window]},{name:"any_keyword",symbols:[a.lexerAny.has("kw_with")?{type:"kw_with"}:kw_with]},{name:"ident_extended",symbols:["ident"]},{name:"ident_extended",symbols:["any_keyword"]},{name:"select_statement$ebnf$1",symbols:["select_from"],postprocess:r},{name:"select_statement$ebnf$1",symbols:[],postprocess:()=>null},{name:"select_statement$ebnf$2",symbols:["select_where"],postprocess:r},{name:"select_statement$ebnf$2",symbols:[],postprocess:()=>null},{name:"select_statement$ebnf$3$subexpression$1$ebnf$1",symbols:["select_having"],postprocess:r},{name:"select_statement$ebnf$3$subexpression$1$ebnf$1",symbols:[],postprocess:()=>null},{name:"select_statement$ebnf$3$subexpression$1",symbols:["select_groupby","select_statement$ebnf$3$subexpression$1$ebnf$1"]},{name:"select_statement$ebnf$3",symbols:["select_statement$ebnf$3$subexpression$1"],postprocess:r},{name:"select_statement$ebnf$3",symbols:[],postprocess:()=>null},{name:"select_statement$ebnf$4",symbols:["select_order_by"],postprocess:r},{name:"select_statement$ebnf$4",symbols:[],postprocess:()=>null},{name:"select_statement$ebnf$5",symbols:["select_limit_offset"],postprocess:r},{name:"select_statement$ebnf$5",symbols:[],postprocess:()=>null},{name:"select_statement$ebnf$6$subexpression$1$ebnf$1",symbols:["select_skip"],postprocess:r},{name:"select_statement$ebnf$6$subexpression$1$ebnf$1",symbols:[],postprocess:()=>null},{name:"select_statement$ebnf$6$subexpression$1",symbols:["select_for","select_statement$ebnf$6$subexpression$1$ebnf$1"]},{name:"select_statement$ebnf$6",symbols:["select_statement$ebnf$6$subexpression$1"],postprocess:r},{name:"select_statement$ebnf$6",symbols:[],postprocess:()=>null},{name:"select_statement",symbols:["select_what","select_statement$ebnf$1","select_statement$ebnf$2","select_statement$ebnf$3","select_statement$ebnf$4","select_statement$ebnf$5","select_statement$ebnf$6"],postprocess:e=>{let[t,s,r,a,o,l,i]=e;s=c(s);let p=a&&a[0],m=a&&a[1];p=p&&(1===p.length&&"list"===p[0].type?p[0].expressions:p),m=m&&c(m);let u=i&&i[0],b=i&&i[1];return b=c(b),(0,n.track)(e,{...t,...s?{from:Array.isArray(s)?s:[s]}:{},...p?{groupBy:p}:{},...m?{having:m}:{},...l?{limit:c(l)}:{},...o?{orderBy:o}:{},...r?{where:r}:{},...u?{for:u[1]}:{},...b?{skip:b}:{},type:"select"})}},{name:"select_from",symbols:[a.lexerAny.has("kw_from")?{type:"kw_from"}:kw_from,"select_from_items"],postprocess:m},{name:"select_from_items$ebnf$1",symbols:[]},{name:"select_from_items$ebnf$1$subexpression$1",symbols:["comma","select_from_item"],postprocess:m},{name:"select_from_items$ebnf$1",symbols:["select_from_items$ebnf$1","select_from_items$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"select_from_items",symbols:["select_from_item","select_from_items$ebnf$1"],postprocess:([e,t])=>[...e,...u(t)||[]]},{name:"select_from_item",symbols:["select_from_subject"]},{name:"select_from_item",symbols:["select_from_item_joins"],postprocess:p(0)},{name:"select_from_item_joins$subexpression$1",symbols:["select_from_item"],postprocess:p(0)},{name:"select_from_item_joins",symbols:["select_from_item_joins$subexpression$1","select_table_join"],postprocess:u},{name:"select_from_item_joins",symbols:["lparen","select_from_item_joins","rparen"],postprocess:p(1)},{name:"select_from_subject",symbols:["stb_table"],postprocess:c},{name:"select_from_subject",symbols:["stb_statement"],postprocess:c},{name:"select_from_subject",symbols:["stb_call"],postprocess:c},{name:"stb_opts$ebnf$1",symbols:["collist_paren"],postprocess:r},{name:"stb_opts$ebnf$1",symbols:[],postprocess:()=>null},{name:"stb_opts",symbols:["ident_aliased","stb_opts$ebnf$1"],postprocess:e=>(0,n.track)(e,{alias:d(e[0]),...e[1]&&{columnNames:(0,n.unbox)(e[1]).map(o)}})},{name:"stb_table$ebnf$1",symbols:["stb_opts"],postprocess:r},{name:"stb_table$ebnf$1",symbols:[],postprocess:()=>null},{name:"stb_table",symbols:["table_ref","stb_table$ebnf$1"],postprocess:e=>(0,n.track)(e,{type:"table",name:(0,n.track)(e,{...e[0],...e[1]})})},{name:"stb_statement$ebnf$1",symbols:[a.lexerAny.has("kw_lateral")?{type:"kw_lateral"}:kw_lateral],postprocess:r},{name:"stb_statement$ebnf$1",symbols:[],postprocess:()=>null},{name:"stb_statement",symbols:["stb_statement$ebnf$1","selection_paren","stb_opts"],postprocess:e=>(0,n.track)(e,{type:"statement",statement:c(e[1]),...e[0]&&{lateral:!0},...e[2]})},{name:"select_values",symbols:["kw_values","insert_values"],postprocess:e=>(0,n.track)(e,{type:"values",values:e[1]})},{name:"stb_call$ebnf$1",symbols:[a.lexerAny.has("kw_lateral")?{type:"kw_lateral"}:kw_lateral],postprocess:r},{name:"stb_call$ebnf$1",symbols:[],postprocess:()=>null},{name:"stb_call$ebnf$2",symbols:["kw_withordinality"],postprocess:r},{name:"stb_call$ebnf$2",symbols:[],postprocess:()=>null},{name:"stb_call$ebnf$3",symbols:["stb_call_alias"],postprocess:r},{name:"stb_call$ebnf$3",symbols:[],postprocess:()=>null},{name:"stb_call",symbols:["stb_call$ebnf$1","expr_function_call","stb_call$ebnf$2","stb_call$ebnf$3"],postprocess:e=>{const t=e[0],s=e[2],r=e[3];return s||r?(0,n.track)(e,{...e[1],...t&&{lateral:!0},...s&&{withOrdinality:!0},alias:r?l(r[0],r[1]):void 0}):e[1]}},{name:"stb_call_alias$subexpression$1$ebnf$1",symbols:[a.lexerAny.has("kw_as")?{type:"kw_as"}:kw_as],postprocess:r},{name:"stb_call_alias$subexpression$1$ebnf$1",symbols:[],postprocess:()=>null},{name:"stb_call_alias$subexpression$1",symbols:["stb_call_alias$subexpression$1$ebnf$1","ident"],postprocess:m},{name:"stb_call_alias$ebnf$1",symbols:["stb_call_alias_list"],postprocess:r},{name:"stb_call_alias$ebnf$1",symbols:[],postprocess:()=>null},{name:"stb_call_alias",symbols:["stb_call_alias$subexpression$1","stb_call_alias$ebnf$1"]},{name:"stb_call_alias_list",symbols:["lparen","stb_call_alias_list_raw","rparen"],postprocess:p(1)},{name:"stb_call_alias_list_raw$ebnf$1",symbols:[]},{name:"stb_call_alias_list_raw$ebnf$1$subexpression$1",symbols:["comma","ident"],postprocess:m},{name:"stb_call_alias_list_raw$ebnf$1",symbols:["stb_call_alias_list_raw$ebnf$1","stb_call_alias_list_raw$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"stb_call_alias_list_raw",symbols:["ident","stb_call_alias_list_raw$ebnf$1"],postprocess:([e,t])=>[e,...t||[]]},{name:"select_table_join$ebnf$1",symbols:["select_table_join_clause"],postprocess:r},{name:"select_table_join$ebnf$1",symbols:[],postprocess:()=>null},{name:"select_table_join",symbols:["select_join_op",a.lexerAny.has("kw_join")?{type:"kw_join"}:kw_join,"select_from_subject","select_table_join$ebnf$1"],postprocess:e=>(0,n.track)(e,{...c(e[2]),join:{type:d(e[0]," "),...e[3]&&c(e[3])}})},{name:"select_table_join_clause",symbols:[a.lexerAny.has("kw_on")?{type:"kw_on"}:kw_on,"expr"],postprocess:e=>(0,n.track)(e,{on:m(e)})},{name:"select_table_join_clause$macrocall$2",symbols:["ident"]},{name:"select_table_join_clause$macrocall$1$ebnf$1",symbols:[]},{name:"select_table_join_clause$macrocall$1$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("comma")?{type:"comma"}:comma,"select_table_join_clause$macrocall$2"],postprocess:m},{name:"select_table_join_clause$macrocall$1$ebnf$1",symbols:["select_table_join_clause$macrocall$1$ebnf$1","select_table_join_clause$macrocall$1$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"select_table_join_clause$macrocall$1",symbols:["select_table_join_clause$macrocall$2","select_table_join_clause$macrocall$1$ebnf$1"],postprocess:([e,t])=>[c(e),...t.map(c)||[]]},{name:"select_table_join_clause",symbols:[a.lexerAny.has("kw_using")?{type:"kw_using"}:kw_using,"lparen","select_table_join_clause$macrocall$1","rparen"],postprocess:e=>(0,n.track)(e,{using:e[2].map(o)})},{name:"select_join_op$subexpression$1$ebnf$1",symbols:[a.lexerAny.has("kw_inner")?{type:"kw_inner"}:kw_inner],postprocess:r},{name:"select_join_op$subexpression$1$ebnf$1",symbols:[],postprocess:()=>null},{name:"select_join_op$subexpression$1",symbols:["select_join_op$subexpression$1$ebnf$1"],postprocess:e=>(0,n.box)(e,"INNER JOIN")},{name:"select_join_op",symbols:["select_join_op$subexpression$1"]},{name:"select_join_op$subexpression$2",symbols:[a.lexerAny.has("kw_cross")?{type:"kw_cross"}:kw_cross],postprocess:e=>(0,n.box)(e,"CROSS JOIN")},{name:"select_join_op",symbols:["select_join_op$subexpression$2"]},{name:"select_join_op$subexpression$3$ebnf$1",symbols:[a.lexerAny.has("kw_outer")?{type:"kw_outer"}:kw_outer],postprocess:r},{name:"select_join_op$subexpression$3$ebnf$1",symbols:[],postprocess:()=>null},{name:"select_join_op$subexpression$3",symbols:[a.lexerAny.has("kw_left")?{type:"kw_left"}:kw_left,"select_join_op$subexpression$3$ebnf$1"],postprocess:e=>(0,n.box)(e,"LEFT JOIN")},{name:"select_join_op",symbols:["select_join_op$subexpression$3"]},{name:"select_join_op$subexpression$4$ebnf$1",symbols:[a.lexerAny.has("kw_outer")?{type:"kw_outer"}:kw_outer],postprocess:r},{name:"select_join_op$subexpression$4$ebnf$1",symbols:[],postprocess:()=>null},{name:"select_join_op$subexpression$4",symbols:[a.lexerAny.has("kw_right")?{type:"kw_right"}:kw_right,"select_join_op$subexpression$4$ebnf$1"],postprocess:e=>(0,n.box)(e,"RIGHT JOIN")},{name:"select_join_op",symbols:["select_join_op$subexpression$4"]},{name:"select_join_op$subexpression$5$ebnf$1",symbols:[a.lexerAny.has("kw_outer")?{type:"kw_outer"}:kw_outer],postprocess:r},{name:"select_join_op$subexpression$5$ebnf$1",symbols:[],postprocess:()=>null},{name:"select_join_op$subexpression$5",symbols:[a.lexerAny.has("kw_full")?{type:"kw_full"}:kw_full,"select_join_op$subexpression$5$ebnf$1"],postprocess:e=>(0,n.box)(e,"FULL JOIN")},{name:"select_join_op",symbols:["select_join_op$subexpression$5"]},{name:"select_what$ebnf$1",symbols:["select_distinct"],postprocess:r},{name:"select_what$ebnf$1",symbols:[],postprocess:()=>null},{name:"select_what$ebnf$2",symbols:["select_expr_list_aliased"],postprocess:r},{name:"select_what$ebnf$2",symbols:[],postprocess:()=>null},{name:"select_what",symbols:[a.lexerAny.has("kw_select")?{type:"kw_select"}:kw_select,"select_what$ebnf$1","select_what$ebnf$2"],postprocess:e=>(0,n.track)(e,{columns:e[2],...e[1]&&{distinct:(0,n.unbox)(e[1])}})},{name:"select_expr_list_aliased$ebnf$1",symbols:[]},{name:"select_expr_list_aliased$ebnf$1$subexpression$1",symbols:["comma","select_expr_list_item"],postprocess:m},{name:"select_expr_list_aliased$ebnf$1",symbols:["select_expr_list_aliased$ebnf$1","select_expr_list_aliased$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"select_expr_list_aliased",symbols:["select_expr_list_item","select_expr_list_aliased$ebnf$1"],postprocess:([e,t])=>[e,...t||[]]},{name:"select_expr_list_item$ebnf$1",symbols:["ident_aliased"],postprocess:r},{name:"select_expr_list_item$ebnf$1",symbols:[],postprocess:()=>null},{name:"select_expr_list_item",symbols:["expr","select_expr_list_item$ebnf$1"],postprocess:e=>(0,n.track)(e,{expr:e[0],...e[1]?{alias:o(e[1])}:{}})},{name:"select_distinct",symbols:[a.lexerAny.has("kw_all")?{type:"kw_all"}:kw_all],postprocess:e=>(0,n.box)(e,"all")},{name:"select_distinct$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("kw_on")?{type:"kw_on"}:kw_on,"lparen","expr_list_raw","rparen"],postprocess:p(2)},{name:"select_distinct$ebnf$1",symbols:["select_distinct$ebnf$1$subexpression$1"],postprocess:r},{name:"select_distinct$ebnf$1",symbols:[],postprocess:()=>null},{name:"select_distinct",symbols:[a.lexerAny.has("kw_distinct")?{type:"kw_distinct"}:kw_distinct,"select_distinct$ebnf$1"],postprocess:e=>(0,n.box)(e,e[1]||"distinct")},{name:"select_where",symbols:[a.lexerAny.has("kw_where")?{type:"kw_where"}:kw_where,"expr"],postprocess:m},{name:"select_groupby",symbols:[a.lexerAny.has("kw_group")?{type:"kw_group"}:kw_group,"kw_by","expr_list_raw"],postprocess:m},{name:"select_having",symbols:[a.lexerAny.has("kw_having")?{type:"kw_having"}:kw_having,"expr"],postprocess:m},{name:"select_limit_offset$ebnf$1$subexpression$1",symbols:["select_offset"]},{name:"select_limit_offset$ebnf$1$subexpression$1",symbols:["select_limit"]},{name:"select_limit_offset$ebnf$1",symbols:["select_limit_offset$ebnf$1$subexpression$1"]},{name:"select_limit_offset$ebnf$1$subexpression$2",symbols:["select_offset"]},{name:"select_limit_offset$ebnf$1$subexpression$2",symbols:["select_limit"]},{name:"select_limit_offset$ebnf$1",symbols:["select_limit_offset$ebnf$1","select_limit_offset$ebnf$1$subexpression$2"],postprocess:e=>e[0].concat([e[1]])},{name:"select_limit_offset",symbols:["select_limit_offset$ebnf$1"],postprocess:(e,t)=>{const s=c(e);if(!Array.isArray(s))return(0,n.track)(e,s);if(2!=s.length)return t;const r=c(s[0]),a=c(s[1]);return r.offset&&a.offset||r.limit&&a.limit?t:(0,n.track)(e,{...r,...a})}},{name:"select_offset$ebnf$1$subexpression$1",symbols:["kw_row"]},{name:"select_offset$ebnf$1$subexpression$1",symbols:["kw_rows"]},{name:"select_offset$ebnf$1",symbols:["select_offset$ebnf$1$subexpression$1"],postprocess:r},{name:"select_offset$ebnf$1",symbols:[],postprocess:()=>null},{name:"select_offset",symbols:[a.lexerAny.has("kw_offset")?{type:"kw_offset"}:kw_offset,"expr_nostar","select_offset$ebnf$1"],postprocess:e=>(0,n.track)(e,{offset:c(e[1])})},{name:"select_limit$subexpression$1",symbols:["select_limit_1"]},{name:"select_limit$subexpression$1",symbols:["select_limit_2"]},{name:"select_limit",symbols:["select_limit$subexpression$1"],postprocess:e=>(0,n.track)(e,{limit:c(e)})},{name:"select_limit_1",symbols:[a.lexerAny.has("kw_limit")?{type:"kw_limit"}:kw_limit,"expr_nostar"],postprocess:m},{name:"select_limit_2$ebnf$1$subexpression$1",symbols:["kw_first"]},{name:"select_limit_2$ebnf$1$subexpression$1",symbols:["kw_next"]},{name:"select_limit_2$ebnf$1",symbols:["select_limit_2$ebnf$1$subexpression$1"],postprocess:r},{name:"select_limit_2$ebnf$1",symbols:[],postprocess:()=>null},{name:"select_limit_2$subexpression$1",symbols:["kw_row"]},{name:"select_limit_2$subexpression$1",symbols:["kw_rows"]},{name:"select_limit_2",symbols:[a.lexerAny.has("kw_fetch")?{type:"kw_fetch"}:kw_fetch,"select_limit_2$ebnf$1","expr_nostar","select_limit_2$subexpression$1",a.lexerAny.has("kw_only")?{type:"kw_only"}:kw_only],postprocess:p(2)},{name:"select_for$subexpression$1",symbols:["kw_update"],postprocess:e=>(0,n.track)(e,{type:"update"})},{name:"select_for$subexpression$1",symbols:["kw_no","kw_key","kw_update"],postprocess:e=>(0,n.track)(e,{type:"no key update"})},{name:"select_for$subexpression$1",symbols:["kw_share"],postprocess:e=>(0,n.track)(e,{type:"share"})},{name:"select_for$subexpression$1",symbols:["kw_key","kw_share"],postprocess:e=>(0,n.track)(e,{type:"key share"})},{name:"select_for",symbols:[a.lexerAny.has("kw_for")?{type:"kw_for"}:kw_for,"select_for$subexpression$1"]},{name:"select_skip$subexpression$1",symbols:["kw_nowait"],postprocess:e=>(0,n.track)(e,{type:"nowait"})},{name:"select_skip$subexpression$1",symbols:["kw_skip","kw_locked"],postprocess:e=>(0,n.track)(e,{type:"skip locked"})},{name:"select_skip",symbols:["select_skip$subexpression$1"]},{name:"select_order_by$subexpression$1",symbols:[a.lexerAny.has("kw_order")?{type:"kw_order"}:kw_order,"kw_by"]},{name:"select_order_by$ebnf$1",symbols:[]},{name:"select_order_by$ebnf$1$subexpression$1",symbols:["comma","select_order_by_expr"],postprocess:m},{name:"select_order_by$ebnf$1",symbols:["select_order_by$ebnf$1","select_order_by$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"select_order_by",symbols:["select_order_by$subexpression$1","select_order_by_expr","select_order_by$ebnf$1"],postprocess:([e,t,s])=>[t,...s||[]]},{name:"select_order_by_expr$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("kw_asc")?{type:"kw_asc"}:kw_asc]},{name:"select_order_by_expr$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("kw_desc")?{type:"kw_desc"}:kw_desc]},{name:"select_order_by_expr$ebnf$1",symbols:["select_order_by_expr$ebnf$1$subexpression$1"],postprocess:r},{name:"select_order_by_expr$ebnf$1",symbols:[],postprocess:()=>null},{name:"select_order_by_expr$ebnf$2$subexpression$1$subexpression$1",symbols:["kw_first"]},{name:"select_order_by_expr$ebnf$2$subexpression$1$subexpression$1",symbols:["kw_last"]},{name:"select_order_by_expr$ebnf$2$subexpression$1",symbols:["kw_nulls","select_order_by_expr$ebnf$2$subexpression$1$subexpression$1"],postprocess:m},{name:"select_order_by_expr$ebnf$2",symbols:["select_order_by_expr$ebnf$2$subexpression$1"],postprocess:r},{name:"select_order_by_expr$ebnf$2",symbols:[],postprocess:()=>null},{name:"select_order_by_expr",symbols:["expr","select_order_by_expr$ebnf$1","select_order_by_expr$ebnf$2"],postprocess:e=>(0,n.track)(e,{by:e[0],...e[1]&&{order:d(e[1]).toUpperCase()},...e[2]&&{nulls:d(e[2]).toUpperCase()}})},{name:"expr",symbols:["expr_nostar"],postprocess:c},{name:"expr",symbols:["expr_star"],postprocess:c},{name:"expr_nostar",symbols:["expr_paren"],postprocess:c},{name:"expr_nostar",symbols:["expr_or"],postprocess:c},{name:"expr_paren$subexpression$1",symbols:["expr_or_select"]},{name:"expr_paren$subexpression$1",symbols:["expr_list_many"]},{name:"expr_paren",symbols:["lparen","expr_paren$subexpression$1","rparen"],postprocess:p(1)},{name:"expr_or$macrocall$2$macrocall$2",symbols:[a.lexerAny.has("kw_or")?{type:"kw_or"}:kw_or]},{name:"expr_or$macrocall$2$macrocall$1",symbols:["expr_or$macrocall$2$macrocall$2"],postprocess:e=>(0,n.track)(e,{op:(d(e," ")||"<error>").toUpperCase()})},{name:"expr_or$macrocall$2",symbols:["expr_or$macrocall$2$macrocall$1"]},{name:"expr_or$macrocall$3",symbols:["expr_or"]},{name:"expr_or$macrocall$4",symbols:["expr_and"]},{name:"expr_or$macrocall$1$subexpression$1",symbols:["expr_or$macrocall$3"]},{name:"expr_or$macrocall$1$subexpression$1",symbols:["expr_paren"]},{name:"expr_or$macrocall$1$subexpression$2",symbols:["expr_or$macrocall$4"]},{name:"expr_or$macrocall$1$subexpression$2",symbols:["expr_paren"]},{name:"expr_or$macrocall$1",symbols:["expr_or$macrocall$1$subexpression$1","expr_or$macrocall$2","expr_or$macrocall$1$subexpression$2"],postprocess:e=>(0,n.track)(e,{type:"binary",left:c(e[0]),right:c(e[2]),...c(e[1])})},{name:"expr_or$macrocall$1",symbols:["expr_or$macrocall$4"],postprocess:c},{name:"expr_or",symbols:["expr_or$macrocall$1"]},{name:"expr_and$macrocall$2$macrocall$2",symbols:[a.lexerAny.has("kw_and")?{type:"kw_and"}:kw_and]},{name:"expr_and$macrocall$2$macrocall$1",symbols:["expr_and$macrocall$2$macrocall$2"],postprocess:e=>(0,n.track)(e,{op:(d(e," ")||"<error>").toUpperCase()})},{name:"expr_and$macrocall$2",symbols:["expr_and$macrocall$2$macrocall$1"]},{name:"expr_and$macrocall$3",symbols:["expr_and"]},{name:"expr_and$macrocall$4",symbols:["expr_not"]},{name:"expr_and$macrocall$1$subexpression$1",symbols:["expr_and$macrocall$3"]},{name:"expr_and$macrocall$1$subexpression$1",symbols:["expr_paren"]},{name:"expr_and$macrocall$1$subexpression$2",symbols:["expr_and$macrocall$4"]},{name:"expr_and$macrocall$1$subexpression$2",symbols:["expr_paren"]},{name:"expr_and$macrocall$1",symbols:["expr_and$macrocall$1$subexpression$1","expr_and$macrocall$2","expr_and$macrocall$1$subexpression$2"],postprocess:e=>(0,n.track)(e,{type:"binary",left:c(e[0]),right:c(e[2]),...c(e[1])})},{name:"expr_and$macrocall$1",symbols:["expr_and$macrocall$4"],postprocess:c},{name:"expr_and",symbols:["expr_and$macrocall$1"]},{name:"expr_not$macrocall$2$macrocall$2",symbols:[a.lexerAny.has("kw_not")?{type:"kw_not"}:kw_not]},{name:"expr_not$macrocall$2$macrocall$1",symbols:["expr_not$macrocall$2$macrocall$2"],postprocess:e=>(0,n.track)(e,{op:(d(e," ")||"<error>").toUpperCase()})},{name:"expr_not$macrocall$2",symbols:["expr_not$macrocall$2$macrocall$1"]},{name:"expr_not$macrocall$3",symbols:["expr_not"]},{name:"expr_not$macrocall$4",symbols:["expr_eq"]},{name:"expr_not$macrocall$1$subexpression$1",symbols:["expr_not$macrocall$3"]},{name:"expr_not$macrocall$1$subexpression$1",symbols:["expr_paren"]},{name:"expr_not$macrocall$1",symbols:["expr_not$macrocall$2","expr_not$macrocall$1$subexpression$1"],postprocess:e=>(0,n.track)(e,{type:"unary",...c(e[0]),operand:c(e[1])})},{name:"expr_not$macrocall$1",symbols:["expr_not$macrocall$4"],postprocess:c},{name:"expr_not",symbols:["expr_not$macrocall$1"]},{name:"expr_eq$macrocall$2$macrocall$2$subexpression$1",symbols:[a.lexerAny.has("op_eq")?{type:"op_eq"}:op_eq]},{name:"expr_eq$macrocall$2$macrocall$2$subexpression$1",symbols:[a.lexerAny.has("op_neq")?{type:"op_neq"}:op_neq]},{name:"expr_eq$macrocall$2$macrocall$2",symbols:["expr_eq$macrocall$2$macrocall$2$subexpression$1"]},{name:"expr_eq$macrocall$2$macrocall$1$macrocall$2",symbols:["expr_eq$macrocall$2$macrocall$2"]},{name:"expr_eq$macrocall$2$macrocall$1$macrocall$1",symbols:["expr_eq$macrocall$2$macrocall$1$macrocall$2"],postprocess:e=>(0,n.track)(e,{op:(d(e," ")||"<error>").toUpperCase()})},{name:"expr_eq$macrocall$2$macrocall$1",symbols:["expr_eq$macrocall$2$macrocall$1$macrocall$1"],postprocess:c},{name:"expr_eq$macrocall$2$macrocall$1",symbols:["kw_operator","lparen","ident","dot","expr_eq$macrocall$2$macrocall$2","rparen"],postprocess:e=>(0,n.track)(e,{op:(d(e[4]," ")||"<error>").toUpperCase(),opSchema:d(e[2])})},{name:"expr_eq$macrocall$2",symbols:["expr_eq$macrocall$2$macrocall$1"]},{name:"expr_eq$macrocall$3",symbols:["expr_eq"]},{name:"expr_eq$macrocall$4",symbols:["expr_is"]},{name:"expr_eq$macrocall$1$subexpression$1",symbols:["expr_eq$macrocall$3"]},{name:"expr_eq$macrocall$1$subexpression$1",symbols:["expr_paren"]},{name:"expr_eq$macrocall$1$subexpression$2",symbols:["expr_eq$macrocall$4"]},{name:"expr_eq$macrocall$1$subexpression$2",symbols:["expr_paren"]},{name:"expr_eq$macrocall$1",symbols:["expr_eq$macrocall$1$subexpression$1","expr_eq$macrocall$2","expr_eq$macrocall$1$subexpression$2"],postprocess:e=>(0,n.track)(e,{type:"binary",left:c(e[0]),right:c(e[2]),...c(e[1])})},{name:"expr_eq$macrocall$1",symbols:["expr_eq$macrocall$4"],postprocess:c},{name:"expr_eq",symbols:["expr_eq$macrocall$1"]},{name:"expr_star",symbols:["star"],postprocess:e=>(0,n.track)(e,{type:"ref",name:"*"})},{name:"expr_is$subexpression$1",symbols:["expr_is"]},{name:"expr_is$subexpression$1",symbols:["expr_paren"]},{name:"expr_is$subexpression$2",symbols:[a.lexerAny.has("kw_isnull")?{type:"kw_isnull"}:kw_isnull]},{name:"expr_is$subexpression$2",symbols:[a.lexerAny.has("kw_is")?{type:"kw_is"}:kw_is,a.lexerAny.has("kw_null")?{type:"kw_null"}:kw_null]},{name:"expr_is",symbols:["expr_is$subexpression$1","expr_is$subexpression$2"],postprocess:e=>(0,n.track)(e,{type:"unary",op:"IS NULL",operand:c(e[0])})},{name:"expr_is$subexpression$3",symbols:["expr_is"]},{name:"expr_is$subexpression$3",symbols:["expr_paren"]},{name:"expr_is$subexpression$4",symbols:[a.lexerAny.has("kw_notnull")?{type:"kw_notnull"}:kw_notnull]},{name:"expr_is$subexpression$4",symbols:[a.lexerAny.has("kw_is")?{type:"kw_is"}:kw_is,"kw_not_null"]},{name:"expr_is",symbols:["expr_is$subexpression$3","expr_is$subexpression$4"],postprocess:e=>(0,n.track)(e,{type:"unary",op:"IS NOT NULL",operand:c(e[0])})},{name:"expr_is$subexpression$5",symbols:["expr_is"]},{name:"expr_is$subexpression$5",symbols:["expr_paren"]},{name:"expr_is$ebnf$1",symbols:[a.lexerAny.has("kw_not")?{type:"kw_not"}:kw_not],postprocess:r},{name:"expr_is$ebnf$1",symbols:[],postprocess:()=>null},{name:"expr_is$subexpression$6",symbols:[a.lexerAny.has("kw_true")?{type:"kw_true"}:kw_true]},{name:"expr_is$subexpression$6",symbols:[a.lexerAny.has("kw_false")?{type:"kw_false"}:kw_false]},{name:"expr_is",symbols:["expr_is$subexpression$5",a.lexerAny.has("kw_is")?{type:"kw_is"}:kw_is,"expr_is$ebnf$1","expr_is$subexpression$6"],postprocess:e=>(0,n.track)(e,{type:"unary",op:"IS "+y([e[2],e[3]]).join(" ").toUpperCase(),operand:c(e[0])})},{name:"expr_is",symbols:["expr_compare"],postprocess:c},{name:"expr_compare$macrocall$2$macrocall$2",symbols:[a.lexerAny.has("op_compare")?{type:"op_compare"}:op_compare]},{name:"expr_compare$macrocall$2$macrocall$1$macrocall$2",symbols:["expr_compare$macrocall$2$macrocall$2"]},{name:"expr_compare$macrocall$2$macrocall$1$macrocall$1",symbols:["expr_compare$macrocall$2$macrocall$1$macrocall$2"],postprocess:e=>(0,n.track)(e,{op:(d(e," ")||"<error>").toUpperCase()})},{name:"expr_compare$macrocall$2$macrocall$1",symbols:["expr_compare$macrocall$2$macrocall$1$macrocall$1"],postprocess:c},{name:"expr_compare$macrocall$2$macrocall$1",symbols:["kw_operator","lparen","ident","dot","expr_compare$macrocall$2$macrocall$2","rparen"],postprocess:e=>(0,n.track)(e,{op:(d(e[4]," ")||"<error>").toUpperCase(),opSchema:d(e[2])})},{name:"expr_compare$macrocall$2",symbols:["expr_compare$macrocall$2$macrocall$1"]},{name:"expr_compare$macrocall$3",symbols:["expr_compare"]},{name:"expr_compare$macrocall$4",symbols:["expr_range"]},{name:"expr_compare$macrocall$1$subexpression$1",symbols:["expr_compare$macrocall$3"]},{name:"expr_compare$macrocall$1$subexpression$1",symbols:["expr_paren"]},{name:"expr_compare$macrocall$1$subexpression$2",symbols:["expr_compare$macrocall$4"]},{name:"expr_compare$macrocall$1$subexpression$2",symbols:["expr_paren"]},{name:"expr_compare$macrocall$1",symbols:["expr_compare$macrocall$1$subexpression$1","expr_compare$macrocall$2","expr_compare$macrocall$1$subexpression$2"],postprocess:e=>(0,n.track)(e,{type:"binary",left:c(e[0]),right:c(e[2]),...c(e[1])})},{name:"expr_compare$macrocall$1",symbols:["expr_compare$macrocall$4"],postprocess:c},{name:"expr_compare",symbols:["expr_compare$macrocall$1"]},{name:"expr_range$macrocall$2",symbols:["ops_between"]},{name:"expr_range$macrocall$3",symbols:[a.lexerAny.has("kw_and")?{type:"kw_and"}:kw_and]},{name:"expr_range$macrocall$4",symbols:["expr_range"]},{name:"expr_range$macrocall$5",symbols:["expr_others"]},{name:"expr_range$macrocall$1$subexpression$1",symbols:["expr_range$macrocall$4"]},{name:"expr_range$macrocall$1$subexpression$1",symbols:["expr_paren"]},{name:"expr_range$macrocall$1$subexpression$2",symbols:["expr_range$macrocall$4"]},{name:"expr_range$macrocall$1$subexpression$2",symbols:["expr_paren"]},{name:"expr_range$macrocall$1$subexpression$3",symbols:["expr_range$macrocall$5"]},{name:"expr_range$macrocall$1$subexpression$3",symbols:["expr_paren"]},{name:"expr_range$macrocall$1",symbols:["expr_range$macrocall$1$subexpression$1","expr_range$macrocall$2","expr_range$macrocall$1$subexpression$2","expr_range$macrocall$3","expr_range$macrocall$1$subexpression$3"],postprocess:e=>(0,n.track)(e,{type:"ternary",value:c(e[0]),lo:c(e[2]),hi:c(e[4]),op:(y(e[1]).join(" ")||"<error>").toUpperCase()})},{name:"expr_range$macrocall$1",symbols:["expr_range$macrocall$5"],postprocess:c},{name:"expr_range",symbols:["expr_range$macrocall$1"]},{name:"expr_others$macrocall$2$macrocall$2",symbols:[a.lexerAny.has("ops_others")?{type:"ops_others"}:ops_others]},{name:"expr_others$macrocall$2$macrocall$1$macrocall$2",symbols:["expr_others$macrocall$2$macrocall$2"]},{name:"expr_others$macrocall$2$macrocall$1$macrocall$1",symbols:["expr_others$macrocall$2$macrocall$1$macrocall$2"],postprocess:e=>(0,n.track)(e,{op:(d(e," ")||"<error>").toUpperCase()})},{name:"expr_others$macrocall$2$macrocall$1",symbols:["expr_others$macrocall$2$macrocall$1$macrocall$1"],postprocess:c},{name:"expr_others$macrocall$2$macrocall$1",symbols:["kw_operator","lparen","ident","dot","expr_others$macrocall$2$macrocall$2","rparen"],postprocess:e=>(0,n.track)(e,{op:(d(e[4]," ")||"<error>").toUpperCase(),opSchema:d(e[2])})},{name:"expr_others$macrocall$2",symbols:["expr_others$macrocall$2$macrocall$1"]},{name:"expr_others$macrocall$3",symbols:["expr_others"]},{name:"expr_others$macrocall$4",symbols:["expr_like"]},{name:"expr_others$macrocall$1$subexpression$1",symbols:["expr_others$macrocall$3"]},{name:"expr_others$macrocall$1$subexpression$1",symbols:["expr_paren"]},{name:"expr_others$macrocall$1$subexpression$2",symbols:["expr_others$macrocall$4"]},{name:"expr_others$macrocall$1$subexpression$2",symbols:["expr_paren"]},{name:"expr_others$macrocall$1",symbols:["expr_others$macrocall$1$subexpression$1","expr_others$macrocall$2","expr_others$macrocall$1$subexpression$2"],postprocess:e=>(0,n.track)(e,{type:"binary",left:c(e[0]),right:c(e[2]),...c(e[1])})},{name:"expr_others$macrocall$1",symbols:["expr_others$macrocall$4"],postprocess:c},{name:"expr_others",symbols:["expr_others$macrocall$1"]},{name:"expr_like$macrocall$2$macrocall$2",symbols:["ops_like"]},{name:"expr_like$macrocall$2$macrocall$1",symbols:["expr_like$macrocall$2$macrocall$2"],postprocess:e=>(0,n.track)(e,{op:(d(e," ")||"<error>").toUpperCase()})},{name:"expr_like$macrocall$2",symbols:["expr_like$macrocall$2$macrocall$1"]},{name:"expr_like$macrocall$3",symbols:["expr_like"]},{name:"expr_like$macrocall$4",symbols:["expr_in"]},{name:"expr_like$macrocall$1$subexpression$1",symbols:["expr_like$macrocall$3"]},{name:"expr_like$macrocall$1$subexpression$1",symbols:["expr_paren"]},{name:"expr_like$macrocall$1$subexpression$2",symbols:["expr_like$macrocall$4"]},{name:"expr_like$macrocall$1$subexpression$2",symbols:["expr_paren"]},{name:"expr_like$macrocall$1",symbols:["expr_like$macrocall$1$subexpression$1","expr_like$macrocall$2","expr_like$macrocall$1$subexpression$2"],postprocess:e=>(0,n.track)(e,{type:"binary",left:c(e[0]),right:c(e[2]),...c(e[1])})},{name:"expr_like$macrocall$1",symbols:["expr_like$macrocall$4"],postprocess:c},{name:"expr_like",symbols:["expr_like$macrocall$1"]},{name:"expr_in$macrocall$2$macrocall$2",symbols:["ops_in"]},{name:"expr_in$macrocall$2$macrocall$1",symbols:["expr_in$macrocall$2$macrocall$2"],postprocess:e=>(0,n.track)(e,{op:(d(e," ")||"<error>").toUpperCase()})},{name:"expr_in$macrocall$2",symbols:["expr_in$macrocall$2$macrocall$1"]},{name:"expr_in$macrocall$3",symbols:["expr_in"]},{name:"expr_in$macrocall$4",symbols:["expr_add"]},{name:"expr_in$macrocall$1$subexpression$1",symbols:["expr_in$macrocall$3"]},{name:"expr_in$macrocall$1$subexpression$1",symbols:["expr_paren"]},{name:"expr_in$macrocall$1$subexpression$2",symbols:["expr_in$macrocall$4"]},{name:"expr_in$macrocall$1$subexpression$2",symbols:["expr_paren"]},{name:"expr_in$macrocall$1",symbols:["expr_in$macrocall$1$subexpression$1","expr_in$macrocall$2","expr_in$macrocall$1$subexpression$2"],postprocess:e=>(0,n.track)(e,{type:"binary",left:c(e[0]),right:c(e[2]),...c(e[1])})},{name:"expr_in$macrocall$1",symbols:["expr_in$macrocall$4"],postprocess:c},{name:"expr_in",symbols:["expr_in$macrocall$1"]},{name:"expr_add$macrocall$2$macrocall$2$subexpression$1",symbols:[a.lexerAny.has("op_plus")?{type:"op_plus"}:op_plus]},{name:"expr_add$macrocall$2$macrocall$2$subexpression$1",symbols:[a.lexerAny.has("op_minus")?{type:"op_minus"}:op_minus]},{name:"expr_add$macrocall$2$macrocall$2$subexpression$1",symbols:[a.lexerAny.has("op_additive")?{type:"op_additive"}:op_additive]},{name:"expr_add$macrocall$2$macrocall$2",symbols:["expr_add$macrocall$2$macrocall$2$subexpression$1"]},{name:"expr_add$macrocall$2$macrocall$1$macrocall$2",symbols:["expr_add$macrocall$2$macrocall$2"]},{name:"expr_add$macrocall$2$macrocall$1$macrocall$1",symbols:["expr_add$macrocall$2$macrocall$1$macrocall$2"],postprocess:e=>(0,n.track)(e,{op:(d(e," ")||"<error>").toUpperCase()})},{name:"expr_add$macrocall$2$macrocall$1",symbols:["expr_add$macrocall$2$macrocall$1$macrocall$1"],postprocess:c},{name:"expr_add$macrocall$2$macrocall$1",symbols:["kw_operator","lparen","ident","dot","expr_add$macrocall$2$macrocall$2","rparen"],postprocess:e=>(0,n.track)(e,{op:(d(e[4]," ")||"<error>").toUpperCase(),opSchema:d(e[2])})},{name:"expr_add$macrocall$2",symbols:["expr_add$macrocall$2$macrocall$1"]},{name:"expr_add$macrocall$3",symbols:["expr_add"]},{name:"expr_add$macrocall$4",symbols:["expr_mult"]},{name:"expr_add$macrocall$1$subexpression$1",symbols:["expr_add$macrocall$3"]},{name:"expr_add$macrocall$1$subexpression$1",symbols:["expr_paren"]},{name:"expr_add$macrocall$1$subexpression$2",symbols:["expr_add$macrocall$4"]},{name:"expr_add$macrocall$1$subexpression$2",symbols:["expr_paren"]},{name:"expr_add$macrocall$1",symbols:["expr_add$macrocall$1$subexpression$1","expr_add$macrocall$2","expr_add$macrocall$1$subexpression$2"],postprocess:e=>(0,n.track)(e,{type:"binary",left:c(e[0]),right:c(e[2]),...c(e[1])})},{name:"expr_add$macrocall$1",symbols:["expr_add$macrocall$4"],postprocess:c},{name:"expr_add",symbols:["expr_add$macrocall$1"]},{name:"expr_mult$macrocall$2$macrocall$2$subexpression$1",symbols:[a.lexerAny.has("star")?{type:"star"}:star]},{name:"expr_mult$macrocall$2$macrocall$2$subexpression$1",symbols:[a.lexerAny.has("op_div")?{type:"op_div"}:op_div]},{name:"expr_mult$macrocall$2$macrocall$2$subexpression$1",symbols:[a.lexerAny.has("op_mod")?{type:"op_mod"}:op_mod]},{name:"expr_mult$macrocall$2$macrocall$2",symbols:["expr_mult$macrocall$2$macrocall$2$subexpression$1"]},{name:"expr_mult$macrocall$2$macrocall$1$macrocall$2",symbols:["expr_mult$macrocall$2$macrocall$2"]},{name:"expr_mult$macrocall$2$macrocall$1$macrocall$1",symbols:["expr_mult$macrocall$2$macrocall$1$macrocall$2"],postprocess:e=>(0,n.track)(e,{op:(d(e," ")||"<error>").toUpperCase()})},{name:"expr_mult$macrocall$2$macrocall$1",symbols:["expr_mult$macrocall$2$macrocall$1$macrocall$1"],postprocess:c},{name:"expr_mult$macrocall$2$macrocall$1",symbols:["kw_operator","lparen","ident","dot","expr_mult$macrocall$2$macrocall$2","rparen"],postprocess:e=>(0,n.track)(e,{op:(d(e[4]," ")||"<error>").toUpperCase(),opSchema:d(e[2])})},{name:"expr_mult$macrocall$2",symbols:["expr_mult$macrocall$2$macrocall$1"]},{name:"expr_mult$macrocall$3",symbols:["expr_mult"]},{name:"expr_mult$macrocall$4",symbols:["expr_exp"]},{name:"expr_mult$macrocall$1$subexpression$1",symbols:["expr_mult$macrocall$3"]},{name:"expr_mult$macrocall$1$subexpression$1",symbols:["expr_paren"]},{name:"expr_mult$macrocall$1$subexpression$2",symbols:["expr_mult$macrocall$4"]},{name:"expr_mult$macrocall$1$subexpression$2",symbols:["expr_paren"]},{name:"expr_mult$macrocall$1",symbols:["expr_mult$macrocall$1$subexpression$1","expr_mult$macrocall$2","expr_mult$macrocall$1$subexpression$2"],postprocess:e=>(0,n.track)(e,{type:"binary",left:c(e[0]),right:c(e[2]),...c(e[1])})},{name:"expr_mult$macrocall$1",symbols:["expr_mult$macrocall$4"],postprocess:c},{name:"expr_mult",symbols:["expr_mult$macrocall$1"]},{name:"expr_exp$macrocall$2$macrocall$2",symbols:[a.lexerAny.has("op_exp")?{type:"op_exp"}:op_exp]},{name:"expr_exp$macrocall$2$macrocall$1$macrocall$2",symbols:["expr_exp$macrocall$2$macrocall$2"]},{name:"expr_exp$macrocall$2$macrocall$1$macrocall$1",symbols:["expr_exp$macrocall$2$macrocall$1$macrocall$2"],postprocess:e=>(0,n.track)(e,{op:(d(e," ")||"<error>").toUpperCase()})},{name:"expr_exp$macrocall$2$macrocall$1",symbols:["expr_exp$macrocall$2$macrocall$1$macrocall$1"],postprocess:c},{name:"expr_exp$macrocall$2$macrocall$1",symbols:["kw_operator","lparen","ident","dot","expr_exp$macrocall$2$macrocall$2","rparen"],postprocess:e=>(0,n.track)(e,{op:(d(e[4]," ")||"<error>").toUpperCase(),opSchema:d(e[2])})},{name:"expr_exp$macrocall$2",symbols:["expr_exp$macrocall$2$macrocall$1"]},{name:"expr_exp$macrocall$3",symbols:["expr_exp"]},{name:"expr_exp$macrocall$4",symbols:["expr_unary_add"]},{name:"expr_exp$macrocall$1$subexpression$1",symbols:["expr_exp$macrocall$3"]},{name:"expr_exp$macrocall$1$subexpression$1",symbols:["expr_paren"]},{name:"expr_exp$macrocall$1$subexpression$2",symbols:["expr_exp$macrocall$4"]},{name:"expr_exp$macrocall$1$subexpression$2",symbols:["expr_paren"]},{name:"expr_exp$macrocall$1",symbols:["expr_exp$macrocall$1$subexpression$1","expr_exp$macrocall$2","expr_exp$macrocall$1$subexpression$2"],postprocess:e=>(0,n.track)(e,{type:"binary",left:c(e[0]),right:c(e[2]),...c(e[1])})},{name:"expr_exp$macrocall$1",symbols:["expr_exp$macrocall$4"],postprocess:c},{name:"expr_exp",symbols:["expr_exp$macrocall$1"]},{name:"expr_unary_add$macrocall$2$macrocall$2$subexpression$1",symbols:[a.lexerAny.has("op_plus")?{type:"op_plus"}:op_plus]},{name:"expr_unary_add$macrocall$2$macrocall$2$subexpression$1",symbols:[a.lexerAny.has("op_minus")?{type:"op_minus"}:op_minus]},{name:"expr_unary_add$macrocall$2$macrocall$2",symbols:["expr_unary_add$macrocall$2$macrocall$2$subexpression$1"]},{name:"expr_unary_add$macrocall$2$macrocall$1$macrocall$2",symbols:["expr_unary_add$macrocall$2$macrocall$2"]},{name:"expr_unary_add$macrocall$2$macrocall$1$macrocall$1",symbols:["expr_unary_add$macrocall$2$macrocall$1$macrocall$2"],postprocess:e=>(0,n.track)(e,{op:(d(e," ")||"<error>").toUpperCase()})},{name:"expr_unary_add$macrocall$2$macrocall$1",symbols:["expr_unary_add$macrocall$2$macrocall$1$macrocall$1"],postprocess:c},{name:"expr_unary_add$macrocall$2$macrocall$1",symbols:["kw_operator","lparen","ident","dot","expr_unary_add$macrocall$2$macrocall$2","rparen"],postprocess:e=>(0,n.track)(e,{op:(d(e[4]," ")||"<error>").toUpperCase(),opSchema:d(e[2])})},{name:"expr_unary_add$macrocall$2",symbols:["expr_unary_add$macrocall$2$macrocall$1"]},{name:"expr_unary_add$macrocall$3",symbols:["expr_unary_add"]},{name:"expr_unary_add$macrocall$4",symbols:["expr_various_constructs"]},{name:"expr_unary_add$macrocall$1$subexpression$1",symbols:["expr_unary_add$macrocall$3"]},{name:"expr_unary_add$macrocall$1$subexpression$1",symbols:["expr_paren"]},{name:"expr_unary_add$macrocall$1",symbols:["expr_unary_add$macrocall$2","expr_unary_add$macrocall$1$subexpression$1"],postprocess:e=>(0,n.track)(e,{type:"unary",...c(e[0]),operand:c(e[1])})},{name:"expr_unary_add$macrocall$1",symbols:["expr_unary_add$macrocall$4"],postprocess:c},{name:"expr_unary_add",symbols:["expr_unary_add$macrocall$1"]},{name:"expr_various_constructs$macrocall$2$macrocall$2",symbols:["various_binaries"]},{name:"expr_various_constructs$macrocall$2$macrocall$1",symbols:["expr_various_constructs$macrocall$2$macrocall$2"],postprocess:e=>(0,n.track)(e,{op:(d(e," ")||"<error>").toUpperCase()})},{name:"expr_various_constructs$macrocall$2",symbols:["expr_various_constructs$macrocall$2$macrocall$1"]},{name:"expr_various_constructs$macrocall$3",symbols:["expr_various_constructs"]},{name:"expr_various_constructs$macrocall$4",symbols:["expr_array_index"]},{name:"expr_various_constructs$macrocall$1$subexpression$1",symbols:["expr_various_constructs$macrocall$3"]},{name:"expr_various_constructs$macrocall$1$subexpression$1",symbols:["expr_paren"]},{name:"expr_various_constructs$macrocall$1$subexpression$2",symbols:["expr_various_constructs$macrocall$4"]},{name:"expr_various_constructs$macrocall$1$subexpression$2",symbols:["expr_paren"]},{name:"expr_various_constructs$macrocall$1",symbols:["expr_various_constructs$macrocall$1$subexpression$1","expr_various_constructs$macrocall$2","expr_various_constructs$macrocall$1$subexpression$2"],postprocess:e=>(0,n.track)(e,{type:"binary",left:c(e[0]),right:c(e[2]),...c(e[1])})},{name:"expr_various_constructs$macrocall$1",symbols:["expr_various_constructs$macrocall$4"],postprocess:c},{name:"expr_various_constructs",symbols:["expr_various_constructs$macrocall$1"]},{name:"expr_array_index$subexpression$1",symbols:["expr_array_index"]},{name:"expr_array_index$subexpression$1",symbols:["expr_paren"]},{name:"expr_array_index",symbols:["expr_array_index$subexpression$1",a.lexerAny.has("lbracket")?{type:"lbracket"}:lbracket,"expr_nostar",a.lexerAny.has("rbracket")?{type:"rbracket"}:rbracket],postprocess:e=>(0,n.track)(e,{type:"arrayIndex",array:c(e[0]),index:c(e[2])})},{name:"expr_array_index",symbols:["expr_member"],postprocess:c},{name:"expr_member$subexpression$1",symbols:["expr_member"]},{name:"expr_member$subexpression$1",symbols:["expr_paren"]},{name:"expr_member$subexpression$2",symbols:["string"]},{name:"expr_member$subexpression$2",symbols:["int"]},{name:"expr_member",symbols:["expr_member$subexpression$1","ops_member","expr_member$subexpression$2"],postprocess:e=>(0,n.track)(e,{type:"member",operand:c(e[0]),op:e[1],member:c(e[2])})},{name:"expr_member$subexpression$3",symbols:["expr_member"]},{name:"expr_member$subexpression$3",symbols:["expr_paren"]},{name:"expr_member",symbols:["expr_member$subexpression$3",a.lexerAny.has("op_cast")?{type:"op_cast"}:op_cast,"data_type"],postprocess:e=>(0,n.track)(e,{type:"cast",operand:c(e[0]),to:e[2]})},{name:"expr_member",symbols:[a.lexerAny.has("kw_cast")?{type:"kw_cast"}:kw_cast,"lparen","expr_nostar",a.lexerAny.has("kw_as")?{type:"kw_as"}:kw_as,"data_type","rparen"],postprocess:e=>(0,n.track)(e,{type:"cast",operand:c(e[2]),to:e[4]})},{name:"expr_member",symbols:["data_type","string"],postprocess:e=>(0,n.track)(e,{type:"cast",operand:(0,n.track)(e[1],{type:"string",value:(0,n.unbox)(e[1])}),to:(0,n.unbox)(e[0])})},{name:"expr_member",symbols:["expr_dot"],postprocess:c},{name:"expr_dot$subexpression$1",symbols:["word"]},{name:"expr_dot$subexpression$1",symbols:["star"]},{name:"expr_dot",symbols:["qname",a.lexerAny.has("dot")?{type:"dot"}:dot,"expr_dot$subexpression$1"],postprocess:e=>(0,n.track)(e,{type:"ref",table:c(e[0]),name:d(e[2])})},{name:"expr_dot",symbols:["expr_final"],postprocess:c},{name:"expr_final",symbols:["expr_basic"]},{name:"expr_final",symbols:["expr_primary"]},{name:"expr_basic",symbols:["expr_special_calls"]},{name:"expr_basic",symbols:["expr_call"]},{name:"expr_basic",symbols:["expr_array"]},{name:"expr_basic",symbols:["expr_case"]},{name:"expr_basic",symbols:["expr_extract"]},{name:"expr_basic",symbols:["word"],postprocess:e=>(0,n.track)(e,{type:"ref",name:c(e[0])})},{name:"expr_array$ebnf$1",symbols:["expr_subarray_items"],postprocess:r},{name:"expr_array$ebnf$1",symbols:[],postprocess:()=>null},{name:"expr_array",symbols:[a.lexerAny.has("kw_array")?{type:"kw_array"}:kw_array,a.lexerAny.has("lbracket")?{type:"lbracket"}:lbracket,"expr_array$ebnf$1",a.lexerAny.has("rbracket")?{type:"rbracket"}:rbracket],postprocess:e=>(0,n.track)(e,{type:"array",expressions:e[2]||[]})},{name:"expr_array",symbols:[a.lexerAny.has("kw_array")?{type:"kw_array"}:kw_array,"lparen","selection","rparen"],postprocess:e=>(0,n.track)(e,{type:"array select",select:c(e[2])})},{name:"expr_subarray$ebnf$1",symbols:["expr_subarray_items"],postprocess:r},{name:"expr_subarray$ebnf$1",symbols:[],postprocess:()=>null},{name:"expr_subarray",symbols:[a.lexerAny.has("lbracket")?{type:"lbracket"}:lbracket,"expr_subarray$ebnf$1",a.lexerAny.has("rbracket")?{type:"rbracket"}:rbracket],postprocess:p(1)},{name:"expr_subarray_items$macrocall$2",symbols:["expr_list_item"]},{name:"expr_subarray_items$macrocall$1$ebnf$1",symbols:[]},{name:"expr_subarray_items$macrocall$1$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("comma")?{type:"comma"}:comma,"expr_subarray_items$macrocall$2"],postprocess:m},{name:"expr_subarray_items$macrocall$1$ebnf$1",symbols:["expr_subarray_items$macrocall$1$ebnf$1","expr_subarray_items$macrocall$1$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"expr_subarray_items$macrocall$1",symbols:["expr_subarray_items$macrocall$2","expr_subarray_items$macrocall$1$ebnf$1"],postprocess:([e,t])=>[e,...t||[]]},{name:"expr_subarray_items",symbols:["expr_subarray_items$macrocall$1"],postprocess:e=>e[0].map(c)},{name:"expr_subarray_items$macrocall$4",symbols:["expr_subarray"]},{name:"expr_subarray_items$macrocall$3$ebnf$1",symbols:[]},{name:"expr_subarray_items$macrocall$3$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("comma")?{type:"comma"}:comma,"expr_subarray_items$macrocall$4"],postprocess:m},{name:"expr_subarray_items$macrocall$3$ebnf$1",symbols:["expr_subarray_items$macrocall$3$ebnf$1","expr_subarray_items$macrocall$3$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"expr_subarray_items$macrocall$3",symbols:["expr_subarray_items$macrocall$4","expr_subarray_items$macrocall$3$ebnf$1"],postprocess:([e,t])=>[e,...t||[]]},{name:"expr_subarray_items",symbols:["expr_subarray_items$macrocall$3"],postprocess:e=>e[0].map((e=>(0,n.track)(e,{type:"array",expressions:e[0].map(c)})))},{name:"expr_function_call$ebnf$1",symbols:["expr_list_raw"],postprocess:r},{name:"expr_function_call$ebnf$1",symbols:[],postprocess:()=>null},{name:"expr_function_call",symbols:["expr_fn_name","lparen","expr_function_call$ebnf$1","rparen"],postprocess:e=>(0,n.track)(e,{type:"call",function:c(e[0]),args:e[2]||[]})},{name:"expr_call$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("kw_all")?{type:"kw_all"}:kw_all]},{name:"expr_call$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("kw_distinct")?{type:"kw_distinct"}:kw_distinct]},{name:"expr_call$ebnf$1",symbols:["expr_call$ebnf$1$subexpression$1"],postprocess:r},{name:"expr_call$ebnf$1",symbols:[],postprocess:()=>null},{name:"expr_call$ebnf$2",symbols:["expr_list_raw"],postprocess:r},{name:"expr_call$ebnf$2",symbols:[],postprocess:()=>null},{name:"expr_call$ebnf$3",symbols:["select_order_by"],postprocess:r},{name:"expr_call$ebnf$3",symbols:[],postprocess:()=>null},{name:"expr_call$ebnf$4$subexpression$1",symbols:["kw_filter","lparen",a.lexerAny.has("kw_where")?{type:"kw_where"}:kw_where,"expr","rparen"],postprocess:p(3)},{name:"expr_call$ebnf$4",symbols:["expr_call$ebnf$4$subexpression$1"],postprocess:r},{name:"expr_call$ebnf$4",symbols:[],postprocess:()=>null},{name:"expr_call$ebnf$5",symbols:["expr_call_within_group"],postprocess:r},{name:"expr_call$ebnf$5",symbols:[],postprocess:()=>null},{name:"expr_call$ebnf$6",symbols:["expr_call_over"],postprocess:r},{name:"expr_call$ebnf$6",symbols:[],postprocess:()=>null},{name:"expr_call",symbols:["expr_fn_name","lparen","expr_call$ebnf$1","expr_call$ebnf$2","expr_call$ebnf$3","rparen","expr_call$ebnf$4","expr_call$ebnf$5","expr_call$ebnf$6"],postprocess:e=>(0,n.track)(e,{type:"call",function:c(e[0]),...e[2]&&{distinct:d(e[2])},args:e[3]||[],...e[4]&&{orderBy:e[4]},...e[6]&&{filter:c(e[6])},...e[7]&&{withinGroup:e[7]},...e[8]&&{over:c(e[8])}})},{name:"expr_call_over$ebnf$1$subexpression$1",symbols:["kw_partition","kw_by","expr_list_raw"],postprocess:m},{name:"expr_call_over$ebnf$1",symbols:["expr_call_over$ebnf$1$subexpression$1"],postprocess:r},{name:"expr_call_over$ebnf$1",symbols:[],postprocess:()=>null},{name:"expr_call_over$ebnf$2",symbols:["select_order_by"],postprocess:r},{name:"expr_call_over$ebnf$2",symbols:[],postprocess:()=>null},{name:"expr_call_over",symbols:["kw_over","lparen","expr_call_over$ebnf$1","expr_call_over$ebnf$2","rparen"],postprocess:e=>(0,n.track)(e,{...e[2]&&{partitionBy:e[2]},...e[3]&&{orderBy:e[3]}})},{name:"expr_call_within_group$subexpression$1",symbols:["kw_within",a.lexerAny.has("kw_group")?{type:"kw_group"}:kw_group]},{name:"expr_call_within_group$subexpression$2",symbols:[a.lexerAny.has("kw_order")?{type:"kw_order"}:kw_order,"kw_by"]},{name:"expr_call_within_group",symbols:["expr_call_within_group$subexpression$1","lparen","expr_call_within_group$subexpression$2","select_order_by_expr","rparen"],postprocess:e=>(0,n.track)(e,e[3])},{name:"expr_extract$subexpression$1",symbols:["word"],postprocess:f("extract")},{name:"expr_extract",symbols:["expr_extract$subexpression$1","lparen","word",a.lexerAny.has("kw_from")?{type:"kw_from"}:kw_from,"expr","rparen"],postprocess:e=>(0,n.track)(e,{type:"extract",field:o(e[2]),from:e[4]})},{name:"expr_primary",symbols:["float"],postprocess:e=>(0,n.track)(e,{type:"numeric",value:(0,n.unbox)(e[0])})},{name:"expr_primary",symbols:["int"],postprocess:e=>(0,n.track)(e,{type:"integer",value:(0,n.unbox)(e[0])})},{name:"expr_primary",symbols:["string"],postprocess:e=>(0,n.track)(e,{type:"string",value:(0,n.unbox)(e[0])})},{name:"expr_primary",symbols:[a.lexerAny.has("kw_true")?{type:"kw_true"}:kw_true],postprocess:e=>(0,n.track)(e,{type:"boolean",value:!0})},{name:"expr_primary",symbols:[a.lexerAny.has("kw_false")?{type:"kw_false"}:kw_false],postprocess:e=>(0,n.track)(e,{type:"boolean",value:!1})},{name:"expr_primary",symbols:[a.lexerAny.has("kw_null")?{type:"kw_null"}:kw_null],postprocess:e=>(0,n.track)(e,{type:"null"})},{name:"expr_primary",symbols:["value_keyword"],postprocess:e=>(0,n.track)(e,{type:"keyword",keyword:d(e)})},{name:"expr_primary",symbols:[a.lexerAny.has("qparam")?{type:"qparam"}:qparam],postprocess:e=>(0,n.track)(e,{type:"parameter",name:d(e[0])})},{name:"expr_primary",symbols:[a.lexerAny.has("kw_default")?{type:"kw_default"}:kw_default],postprocess:e=>(0,n.track)(e,{type:"default"})},{name:"ops_like",symbols:["ops_like_keywors"]},{name:"ops_like",symbols:["ops_like_operators"]},{name:"ops_like_keywors$ebnf$1",symbols:[a.lexerAny.has("kw_not")?{type:"kw_not"}:kw_not],postprocess:r},{name:"ops_like_keywors$ebnf$1",symbols:[],postprocess:()=>null},{name:"ops_like_keywors$subexpression$1",symbols:[a.lexerAny.has("kw_like")?{type:"kw_like"}:kw_like]},{name:"ops_like_keywors$subexpression$1",symbols:[a.lexerAny.has("kw_ilike")?{type:"kw_ilike"}:kw_ilike]},{name:"ops_like_keywors",symbols:["ops_like_keywors$ebnf$1","ops_like_keywors$subexpression$1"]},{name:"ops_like_operators$subexpression$1",symbols:[a.lexerAny.has("op_like")?{type:"op_like"}:op_like],postprocess:()=>"LIKE"},{name:"ops_like_operators",symbols:["ops_like_operators$subexpression$1"]},{name:"ops_like_operators$subexpression$2",symbols:[a.lexerAny.has("op_ilike")?{type:"op_ilike"}:op_ilike],postprocess:()=>"ILIKE"},{name:"ops_like_operators",symbols:["ops_like_operators$subexpression$2"]},{name:"ops_like_operators$subexpression$3",symbols:[a.lexerAny.has("op_not_like")?{type:"op_not_like"}:op_not_like],postprocess:()=>"NOT LIKE"},{name:"ops_like_operators",symbols:["ops_like_operators$subexpression$3"]},{name:"ops_like_operators$subexpression$4",symbols:[a.lexerAny.has("op_not_ilike")?{type:"op_not_ilike"}:op_not_ilike],postprocess:()=>"NOT ILIKE"},{name:"ops_like_operators",symbols:["ops_like_operators$subexpression$4"]},{name:"ops_in$ebnf$1",symbols:[a.lexerAny.has("kw_not")?{type:"kw_not"}:kw_not],postprocess:r},{name:"ops_in$ebnf$1",symbols:[],postprocess:()=>null},{name:"ops_in",symbols:["ops_in$ebnf$1",a.lexerAny.has("kw_in")?{type:"kw_in"}:kw_in]},{name:"ops_between$ebnf$1",symbols:[a.lexerAny.has("kw_not")?{type:"kw_not"}:kw_not],postprocess:r},{name:"ops_between$ebnf$1",symbols:[],postprocess:()=>null},{name:"ops_between",symbols:["ops_between$ebnf$1","kw_between"]},{name:"ops_member$subexpression$1",symbols:[a.lexerAny.has("op_member")?{type:"op_member"}:op_member]},{name:"ops_member$subexpression$1",symbols:[a.lexerAny.has("op_membertext")?{type:"op_membertext"}:op_membertext]},{name:"ops_member",symbols:["ops_member$subexpression$1"],postprocess:e=>{var t;return null===(t=c(e))||void 0===t?void 0:t.value}},{name:"expr_list_item",symbols:["expr_or_select"],postprocess:c},{name:"expr_list_item",symbols:["expr_star"],postprocess:c},{name:"expr_list_raw$macrocall$2",symbols:["expr_list_item"]},{name:"expr_list_raw$macrocall$1$ebnf$1",symbols:[]},{name:"expr_list_raw$macrocall$1$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("comma")?{type:"comma"}:comma,"expr_list_raw$macrocall$2"],postprocess:m},{name:"expr_list_raw$macrocall$1$ebnf$1",symbols:["expr_list_raw$macrocall$1$ebnf$1","expr_list_raw$macrocall$1$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"expr_list_raw$macrocall$1",symbols:["expr_list_raw$macrocall$2","expr_list_raw$macrocall$1$ebnf$1"],postprocess:([e,t])=>[e,...t||[]]},{name:"expr_list_raw",symbols:["expr_list_raw$macrocall$1"],postprocess:([e])=>e.map(c)},{name:"expr_list_raw_many$macrocall$2",symbols:["expr_list_item"]},{name:"expr_list_raw_many$macrocall$1$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("comma")?{type:"comma"}:comma,"expr_list_raw_many$macrocall$2"],postprocess:m},{name:"expr_list_raw_many$macrocall$1$ebnf$1",symbols:["expr_list_raw_many$macrocall$1$ebnf$1$subexpression$1"]},{name:"expr_list_raw_many$macrocall$1$ebnf$1$subexpression$2",symbols:[a.lexerAny.has("comma")?{type:"comma"}:comma,"expr_list_raw_many$macrocall$2"],postprocess:m},{name:"expr_list_raw_many$macrocall$1$ebnf$1",symbols:["expr_list_raw_many$macrocall$1$ebnf$1","expr_list_raw_many$macrocall$1$ebnf$1$subexpression$2"],postprocess:e=>e[0].concat([e[1]])},{name:"expr_list_raw_many$macrocall$1",symbols:["expr_list_raw_many$macrocall$2","expr_list_raw_many$macrocall$1$ebnf$1"],postprocess:([e,t])=>[e,...t||[]]},{name:"expr_list_raw_many",symbols:["expr_list_raw_many$macrocall$1"],postprocess:([e])=>e.map(c)},{name:"expr_or_select",symbols:["expr_nostar"],postprocess:c},{name:"expr_or_select",symbols:["selection"],postprocess:c},{name:"expr_list_many",symbols:["expr_list_raw_many"],postprocess:e=>(0,n.track)(e,{type:"list",expressions:e[0]})},{name:"expr_case$ebnf$1",symbols:["expr_nostar"],postprocess:r},{name:"expr_case$ebnf$1",symbols:[],postprocess:()=>null},{name:"expr_case$ebnf$2",symbols:[]},{name:"expr_case$ebnf$2",symbols:["expr_case$ebnf$2","expr_case_whens"],postprocess:e=>e[0].concat([e[1]])},{name:"expr_case$ebnf$3",symbols:["expr_case_else"],postprocess:r},{name:"expr_case$ebnf$3",symbols:[],postprocess:()=>null},{name:"expr_case",symbols:[a.lexerAny.has("kw_case")?{type:"kw_case"}:kw_case,"expr_case$ebnf$1","expr_case$ebnf$2","expr_case$ebnf$3",a.lexerAny.has("kw_end")?{type:"kw_end"}:kw_end],postprocess:e=>(0,n.track)(e,{type:"case",value:e[1],whens:e[2],else:e[3]})},{name:"expr_case_whens",symbols:[a.lexerAny.has("kw_when")?{type:"kw_when"}:kw_when,"expr_nostar",a.lexerAny.has("kw_then")?{type:"kw_then"}:kw_then,"expr_nostar"],postprocess:e=>(0,n.track)(e,{when:e[1],value:e[3]})},{name:"expr_case_else",symbols:[a.lexerAny.has("kw_else")?{type:"kw_else"}:kw_else,"expr_nostar"],postprocess:m},{name:"expr_fn_name$subexpression$1$ebnf$1$subexpression$1",symbols:["word",a.lexerAny.has("dot")?{type:"dot"}:dot]},{name:"expr_fn_name$subexpression$1$ebnf$1",symbols:["expr_fn_name$subexpression$1$ebnf$1$subexpression$1"],postprocess:r},{name:"expr_fn_name$subexpression$1$ebnf$1",symbols:[],postprocess:()=>null},{name:"expr_fn_name$subexpression$1",symbols:["expr_fn_name$subexpression$1$ebnf$1","word_or_keyword"],postprocess:e=>(0,n.track)(e,{name:(0,n.unbox)(c(e[1])),...e[0]&&{schema:d(e[0][0])}})},{name:"expr_fn_name",symbols:["expr_fn_name$subexpression$1"]},{name:"expr_fn_name$subexpression$2$subexpression$1",symbols:[a.lexerAny.has("kw_any")?{type:"kw_any"}:kw_any]},{name:"expr_fn_name$subexpression$2$subexpression$1",symbols:[a.lexerAny.has("kw_some")?{type:"kw_some"}:kw_some]},{name:"expr_fn_name$subexpression$2$subexpression$1",symbols:[a.lexerAny.has("kw_all")?{type:"kw_all"}:kw_all]},{name:"expr_fn_name$subexpression$2$subexpression$1",symbols:[a.lexerAny.has("kw_left")?{type:"kw_left"}:kw_left]},{name:"expr_fn_name$subexpression$2$subexpression$1",symbols:[a.lexerAny.has("kw_right")?{type:"kw_right"}:kw_right]},{name:"expr_fn_name$subexpression$2",symbols:["expr_fn_name$subexpression$2$subexpression$1"],postprocess:e=>(0,n.track)(e,{name:d(c(e))})},{name:"expr_fn_name",symbols:["expr_fn_name$subexpression$2"]},{name:"word_or_keyword",symbols:["word"]},{name:"word_or_keyword",symbols:["value_keyword"],postprocess:e=>(0,n.box)(e,d(e))},{name:"value_keyword",symbols:[a.lexerAny.has("kw_current_catalog")?{type:"kw_current_catalog"}:kw_current_catalog]},{name:"value_keyword",symbols:[a.lexerAny.has("kw_current_date")?{type:"kw_current_date"}:kw_current_date]},{name:"value_keyword",symbols:[a.lexerAny.has("kw_current_role")?{type:"kw_current_role"}:kw_current_role]},{name:"value_keyword",symbols:[a.lexerAny.has("kw_current_schema")?{type:"kw_current_schema"}:kw_current_schema]},{name:"value_keyword",symbols:[a.lexerAny.has("kw_current_timestamp")?{type:"kw_current_timestamp"}:kw_current_timestamp]},{name:"value_keyword",symbols:[a.lexerAny.has("kw_current_time")?{type:"kw_current_time"}:kw_current_time]},{name:"value_keyword",symbols:[a.lexerAny.has("kw_localtimestamp")?{type:"kw_localtimestamp"}:kw_localtimestamp]},{name:"value_keyword",symbols:[a.lexerAny.has("kw_localtime")?{type:"kw_localtime"}:kw_localtime]},{name:"value_keyword",symbols:[a.lexerAny.has("kw_session_user")?{type:"kw_session_user"}:kw_session_user]},{name:"value_keyword",symbols:[a.lexerAny.has("kw_user")?{type:"kw_user"}:kw_user]},{name:"value_keyword",symbols:[a.lexerAny.has("kw_current_user")?{type:"kw_current_user"}:kw_current_user]},{name:"expr_special_calls",symbols:["spe_overlay"]},{name:"expr_special_calls",symbols:["spe_substring"]},{name:"spe_overlay$subexpression$1",symbols:["word"],postprocess:f("overlay")},{name:"spe_overlay$subexpression$2",symbols:[a.lexerAny.has("lparen")?{type:"lparen"}:lparen,"expr_nostar"]},{name:"spe_overlay$subexpression$3",symbols:[a.lexerAny.has("kw_placing")?{type:"kw_placing"}:kw_placing,"expr_nostar"]},{name:"spe_overlay$subexpression$4",symbols:[a.lexerAny.has("kw_from")?{type:"kw_from"}:kw_from,"expr_nostar"]},{name:"spe_overlay$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("kw_for")?{type:"kw_for"}:kw_for,"expr_nostar"]},{name:"spe_overlay$ebnf$1",symbols:["spe_overlay$ebnf$1$subexpression$1"],postprocess:r},{name:"spe_overlay$ebnf$1",symbols:[],postprocess:()=>null},{name:"spe_overlay",symbols:["spe_overlay$subexpression$1","spe_overlay$subexpression$2","spe_overlay$subexpression$3","spe_overlay$subexpression$4","spe_overlay$ebnf$1",a.lexerAny.has("rparen")?{type:"rparen"}:rparen],postprocess:e=>(0,n.track)(e,{type:"overlay",value:e[1][1],placing:e[2][1],from:e[3][1],...e[4]&&{for:e[4][1]}})},{name:"spe_substring$subexpression$1",symbols:["word"],postprocess:f("substring")},{name:"spe_substring$subexpression$2",symbols:[a.lexerAny.has("lparen")?{type:"lparen"}:lparen,"expr_nostar"]},{name:"spe_substring$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("kw_from")?{type:"kw_from"}:kw_from,"expr_nostar"]},{name:"spe_substring$ebnf$1",symbols:["spe_substring$ebnf$1$subexpression$1"],postprocess:r},{name:"spe_substring$ebnf$1",symbols:[],postprocess:()=>null},{name:"spe_substring$ebnf$2$subexpression$1",symbols:[a.lexerAny.has("kw_for")?{type:"kw_for"}:kw_for,"expr_nostar"]},{name:"spe_substring$ebnf$2",symbols:["spe_substring$ebnf$2$subexpression$1"],postprocess:r},{name:"spe_substring$ebnf$2",symbols:[],postprocess:()=>null},{name:"spe_substring",symbols:["spe_substring$subexpression$1","spe_substring$subexpression$2","spe_substring$ebnf$1","spe_substring$ebnf$2",a.lexerAny.has("rparen")?{type:"rparen"}:rparen],postprocess:e=>(0,n.track)(e,{type:"substring",value:e[1][1],...e[2]&&{from:e[2][1]},...e[3]&&{for:e[3][1]}})},{name:"various_binaries",symbols:["kw_at","kw_time","kw_zone"],postprocess:()=>"AT TIME ZONE"},{name:"createtable_statement$ebnf$1",symbols:["createtable_modifiers"],postprocess:r},{name:"createtable_statement$ebnf$1",symbols:[],postprocess:()=>null},{name:"createtable_statement$ebnf$2",symbols:["kw_ifnotexists"],postprocess:r},{name:"createtable_statement$ebnf$2",symbols:[],postprocess:()=>null},{name:"createtable_statement$ebnf$3",symbols:["createtable_opts"],postprocess:r},{name:"createtable_statement$ebnf$3",symbols:[],postprocess:()=>null},{name:"createtable_statement",symbols:[a.lexerAny.has("kw_create")?{type:"kw_create"}:kw_create,"createtable_statement$ebnf$1",a.lexerAny.has("kw_table")?{type:"kw_table"}:kw_table,"createtable_statement$ebnf$2","qname","lparen","createtable_declarationlist","rparen","createtable_statement$ebnf$3"],postprocess:e=>{const t=e[6].filter((e=>"kind"in e)),s=e[6].filter((e=>!("kind"in e)));return(0,n.track)(e,{type:"create table",...e[3]?{ifNotExists:!0}:{},name:e[4],columns:t,...c(e[1]),...s.length?{constraints:s}:{},...m(e)})}},{name:"createtable_modifiers",symbols:["kw_unlogged"],postprocess:e=>e[0]?{unlogged:!0}:{}},{name:"createtable_modifiers",symbols:["m_locglob"]},{name:"createtable_modifiers",symbols:["m_tmp"]},{name:"createtable_modifiers",symbols:["m_locglob","m_tmp"],postprocess:([e,t])=>({...e,...t})},{name:"m_locglob$subexpression$1",symbols:["kw_local"]},{name:"m_locglob$subexpression$1",symbols:["kw_global"]},{name:"m_locglob",symbols:["m_locglob$subexpression$1"],postprocess:e=>({locality:d(e)})},{name:"m_tmp$subexpression$1",symbols:["kw_temp"]},{name:"m_tmp$subexpression$1",symbols:["kw_temporary"]},{name:"m_tmp",symbols:["m_tmp$subexpression$1"],postprocess:e=>({temporary:!0})},{name:"createtable_declarationlist$ebnf$1",symbols:[]},{name:"createtable_declarationlist$ebnf$1$subexpression$1",symbols:["comma","createtable_declaration"],postprocess:m},{name:"createtable_declarationlist$ebnf$1",symbols:["createtable_declarationlist$ebnf$1","createtable_declarationlist$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"createtable_declarationlist",symbols:["createtable_declaration","createtable_declarationlist$ebnf$1"],postprocess:([e,t])=>[e,...t||[]]},{name:"createtable_declaration$subexpression$1",symbols:["createtable_constraint"]},{name:"createtable_declaration$subexpression$1",symbols:["createtable_column"]},{name:"createtable_declaration$subexpression$1",symbols:["createtable_like"]},{name:"createtable_declaration",symbols:["createtable_declaration$subexpression$1"],postprocess:c},{name:"createtable_constraint$macrocall$2",symbols:["createtable_constraint_def"]},{name:"createtable_constraint$macrocall$1$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("kw_constraint")?{type:"kw_constraint"}:kw_constraint,"word"]},{name:"createtable_constraint$macrocall$1$ebnf$1",symbols:["createtable_constraint$macrocall$1$ebnf$1$subexpression$1"],postprocess:r},{name:"createtable_constraint$macrocall$1$ebnf$1",symbols:[],postprocess:()=>null},{name:"createtable_constraint$macrocall$1",symbols:["createtable_constraint$macrocall$1$ebnf$1","createtable_constraint$macrocall$2"],postprocess:e=>{const t=e[0]&&o(e[0][1]);return t?(0,n.track)(e,{constraintName:t,...c(e[1])}):(0,n.track)(e,c(e[1]))}},{name:"createtable_constraint",symbols:["createtable_constraint$macrocall$1"],postprocess:c},{name:"createtable_constraint_def",symbols:["createtable_constraint_def_unique"]},{name:"createtable_constraint_def",symbols:["createtable_constraint_def_check"]},{name:"createtable_constraint_def",symbols:["createtable_constraint_foreignkey"]},{name:"createtable_constraint_def_unique$subexpression$1",symbols:[a.lexerAny.has("kw_unique")?{type:"kw_unique"}:kw_unique]},{name:"createtable_constraint_def_unique$subexpression$1",symbols:["kw_primary_key"]},{name:"createtable_constraint_def_unique",symbols:["createtable_constraint_def_unique$subexpression$1","lparen","createtable_collist","rparen"],postprocess:e=>(0,n.track)(e,{type:d(e[0]," "),columns:e[2].map(o)})},{name:"createtable_constraint_def_check",symbols:[a.lexerAny.has("kw_check")?{type:"kw_check"}:kw_check,"expr_paren"],postprocess:e=>(0,n.track)(e,{type:"check",expr:c(e[1])})},{name:"createtable_constraint_foreignkey",symbols:[a.lexerAny.has("kw_foreign")?{type:"kw_foreign"}:kw_foreign,"kw_key","collist_paren","createtable_references"],postprocess:e=>(0,n.track)(e,{type:"foreign key",localColumns:e[2].map(o),...e[3]})},{name:"createtable_references$ebnf$1",symbols:[]},{name:"createtable_references$ebnf$1",symbols:["createtable_references$ebnf$1","createtable_constraint_foreignkey_onsometing"],postprocess:e=>e[0].concat([e[1]])},{name:"createtable_references",symbols:[a.lexerAny.has("kw_references")?{type:"kw_references"}:kw_references,"table_ref","collist_paren","createtable_references$ebnf$1"],postprocess:e=>(0,n.track)(e,{foreignTable:c(e[1]),foreignColumns:e[2].map(o),...e[3].reduce(((e,t)=>({...e,...t})),{})})},{name:"createtable_constraint_foreignkey_onsometing",symbols:[a.lexerAny.has("kw_on")?{type:"kw_on"}:kw_on,"kw_delete","createtable_constraint_on_action"],postprocess:e=>(0,n.track)(e,{onDelete:m(e)})},{name:"createtable_constraint_foreignkey_onsometing",symbols:[a.lexerAny.has("kw_on")?{type:"kw_on"}:kw_on,"kw_update","createtable_constraint_on_action"],postprocess:e=>(0,n.track)(e,{onUpdate:m(e)})},{name:"createtable_constraint_foreignkey_onsometing$subexpression$1",symbols:[a.lexerAny.has("kw_full")?{type:"kw_full"}:kw_full]},{name:"createtable_constraint_foreignkey_onsometing$subexpression$1",symbols:["kw_partial"]},{name:"createtable_constraint_foreignkey_onsometing$subexpression$1",symbols:["kw_simple"]},{name:"createtable_constraint_foreignkey_onsometing",symbols:["kw_match","createtable_constraint_foreignkey_onsometing$subexpression$1"],postprocess:e=>(0,n.track)(e,{match:d(m(e))})},{name:"createtable_constraint_on_action$subexpression$1",symbols:["kw_cascade"]},{name:"createtable_constraint_on_action$subexpression$1$subexpression$1",symbols:["kw_no","kw_action"]},{name:"createtable_constraint_on_action$subexpression$1",symbols:["createtable_constraint_on_action$subexpression$1$subexpression$1"]},{name:"createtable_constraint_on_action$subexpression$1",symbols:["kw_restrict"]},{name:"createtable_constraint_on_action$subexpression$1$subexpression$2",symbols:[a.lexerAny.has("kw_null")?{type:"kw_null"}:kw_null]},{name:"createtable_constraint_on_action$subexpression$1$subexpression$2",symbols:[a.lexerAny.has("kw_default")?{type:"kw_default"}:kw_default]},{name:"createtable_constraint_on_action$subexpression$1",symbols:["kw_set","createtable_constraint_on_action$subexpression$1$subexpression$2"]},{name:"createtable_constraint_on_action",symbols:["createtable_constraint_on_action$subexpression$1"],postprocess:e=>d(e," ")},{name:"createtable_collist$ebnf$1",symbols:[]},{name:"createtable_collist$ebnf$1$subexpression$1",symbols:["comma","ident"],postprocess:m},{name:"createtable_collist$ebnf$1",symbols:["createtable_collist$ebnf$1","createtable_collist$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"createtable_collist",symbols:["ident","createtable_collist$ebnf$1"],postprocess:([e,t])=>[e,...t||[]]},{name:"createtable_column$ebnf$1",symbols:["createtable_collate"],postprocess:r},{name:"createtable_column$ebnf$1",symbols:[],postprocess:()=>null},{name:"createtable_column$ebnf$2",symbols:[]},{name:"createtable_column$ebnf$2",symbols:["createtable_column$ebnf$2","createtable_column_constraint"],postprocess:e=>e[0].concat([e[1]])},{name:"createtable_column",symbols:["word","data_type","createtable_column$ebnf$1","createtable_column$ebnf$2"],postprocess:e=>(0,n.track)(e,{kind:"column",name:o(e[0]),dataType:e[1],...e[2]?{collate:e[2][1]}:{},...e[3]&&e[3].length?{constraints:e[3]}:{}})},{name:"createtable_like$ebnf$1",symbols:[]},{name:"createtable_like$ebnf$1",symbols:["createtable_like$ebnf$1","createtable_like_opt"],postprocess:e=>e[0].concat([e[1]])},{name:"createtable_like",symbols:[a.lexerAny.has("kw_like")?{type:"kw_like"}:kw_like,"qname","createtable_like$ebnf$1"],postprocess:e=>(0,n.track)(e,{kind:"like table",like:e[1],options:e[2]})},{name:"createtable_like_opt$subexpression$1",symbols:["kw_including"]},{name:"createtable_like_opt$subexpression$1",symbols:["kw_excluding"]},{name:"createtable_like_opt",symbols:["createtable_like_opt$subexpression$1","createtable_like_opt_val"],postprocess:e=>(0,n.track)(e,{verb:d(e[0]),option:d(e[1])})},{name:"createtable_like_opt_val",symbols:["word"],postprocess:w("defaults","constraints","indexes","storage","comments")},{name:"createtable_like_opt_val",symbols:[a.lexerAny.has("kw_all")?{type:"kw_all"}:kw_all]},{name:"createtable_column_constraint$macrocall$2",symbols:["createtable_column_constraint_def"]},{name:"createtable_column_constraint$macrocall$1$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("kw_constraint")?{type:"kw_constraint"}:kw_constraint,"word"]},{name:"createtable_column_constraint$macrocall$1$ebnf$1",symbols:["createtable_column_constraint$macrocall$1$ebnf$1$subexpression$1"],postprocess:r},{name:"createtable_column_constraint$macrocall$1$ebnf$1",symbols:[],postprocess:()=>null},{name:"createtable_column_constraint$macrocall$1",symbols:["createtable_column_constraint$macrocall$1$ebnf$1","createtable_column_constraint$macrocall$2"],postprocess:e=>{const t=e[0]&&o(e[0][1]);return t?(0,n.track)(e,{constraintName:t,...c(e[1])}):(0,n.track)(e,c(e[1]))}},{name:"createtable_column_constraint",symbols:["createtable_column_constraint$macrocall$1"],postprocess:c},{name:"createtable_column_constraint_def",symbols:[a.lexerAny.has("kw_unique")?{type:"kw_unique"}:kw_unique],postprocess:e=>(0,n.track)(e,{type:"unique"})},{name:"createtable_column_constraint_def",symbols:["kw_primary_key"],postprocess:e=>(0,n.track)(e,{type:"primary key"})},{name:"createtable_column_constraint_def",symbols:["kw_not_null"],postprocess:e=>(0,n.track)(e,{type:"not null"})},{name:"createtable_column_constraint_def",symbols:[a.lexerAny.has("kw_null")?{type:"kw_null"}:kw_null],postprocess:e=>(0,n.track)(e,{type:"null"})},{name:"createtable_column_constraint_def",symbols:[a.lexerAny.has("kw_default")?{type:"kw_default"}:kw_default,"expr"],postprocess:e=>(0,n.track)(e,{type:"default",default:c(e[1])})},{name:"createtable_column_constraint_def",symbols:[a.lexerAny.has("kw_check")?{type:"kw_check"}:kw_check,"expr_paren"],postprocess:e=>(0,n.track)(e,{type:"check",expr:c(e[1])})},{name:"createtable_column_constraint_def",symbols:["createtable_references"],postprocess:e=>(0,n.track)(e,{type:"reference",...c(e)})},{name:"createtable_column_constraint_def",symbols:["altercol_generated"]},{name:"createtable_collate",symbols:[a.lexerAny.has("kw_collate")?{type:"kw_collate"}:kw_collate,"qualified_name"]},{name:"createtable_opts$subexpression$1",symbols:["word"],postprocess:f("inherits")},{name:"createtable_opts$macrocall$2",symbols:["qname"]},{name:"createtable_opts$macrocall$1$ebnf$1",symbols:[]},{name:"createtable_opts$macrocall$1$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("comma")?{type:"comma"}:comma,"createtable_opts$macrocall$2"],postprocess:m},{name:"createtable_opts$macrocall$1$ebnf$1",symbols:["createtable_opts$macrocall$1$ebnf$1","createtable_opts$macrocall$1$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"createtable_opts$macrocall$1",symbols:["createtable_opts$macrocall$2","createtable_opts$macrocall$1$ebnf$1"],postprocess:([e,t])=>[c(e),...t.map(c)||[]]},{name:"createtable_opts",symbols:["createtable_opts$subexpression$1","lparen","createtable_opts$macrocall$1","rparen"],postprocess:e=>(0,n.track)(e,{inherits:e[2]})},{name:"createindex_statement$ebnf$1",symbols:[a.lexerAny.has("kw_unique")?{type:"kw_unique"}:kw_unique],postprocess:r},{name:"createindex_statement$ebnf$1",symbols:[],postprocess:()=>null},{name:"createindex_statement$ebnf$2",symbols:[a.lexerAny.has("kw_concurrently")?{type:"kw_concurrently"}:kw_concurrently],postprocess:r},{name:"createindex_statement$ebnf$2",symbols:[],postprocess:()=>null},{name:"createindex_statement$ebnf$3",symbols:["kw_ifnotexists"],postprocess:r},{name:"createindex_statement$ebnf$3",symbols:[],postprocess:()=>null},{name:"createindex_statement$ebnf$4",symbols:["word"],postprocess:r},{name:"createindex_statement$ebnf$4",symbols:[],postprocess:()=>null},{name:"createindex_statement$ebnf$5$subexpression$1",symbols:[a.lexerAny.has("kw_using")?{type:"kw_using"}:kw_using,"ident"],postprocess:m},{name:"createindex_statement$ebnf$5",symbols:["createindex_statement$ebnf$5$subexpression$1"],postprocess:r},{name:"createindex_statement$ebnf$5",symbols:[],postprocess:()=>null},{name:"createindex_statement$ebnf$6",symbols:["createindex_with"],postprocess:r},{name:"createindex_statement$ebnf$6",symbols:[],postprocess:()=>null},{name:"createindex_statement$ebnf$7",symbols:["createindex_tblspace"],postprocess:r},{name:"createindex_statement$ebnf$7",symbols:[],postprocess:()=>null},{name:"createindex_statement$ebnf$8",symbols:["createindex_predicate"],postprocess:r},{name:"createindex_statement$ebnf$8",symbols:[],postprocess:()=>null},{name:"createindex_statement",symbols:[a.lexerAny.has("kw_create")?{type:"kw_create"}:kw_create,"createindex_statement$ebnf$1","kw_index","createindex_statement$ebnf$2","createindex_statement$ebnf$3","createindex_statement$ebnf$4",a.lexerAny.has("kw_on")?{type:"kw_on"}:kw_on,"table_ref","createindex_statement$ebnf$5","lparen","createindex_expressions","rparen","createindex_statement$ebnf$6","createindex_statement$ebnf$7","createindex_statement$ebnf$8"],postprocess:e=>(0,n.track)(e,{type:"create index",...e[1]&&{unique:!0},...e[3]&&{concurrently:!0},...e[4]&&{ifNotExists:!0},...e[5]&&{indexName:o(e[5])},table:e[7],...e[8]&&{using:o(e[8])},expressions:e[10],...e[12]&&{with:e[12]},...e[13]&&{tablespace:c(e[13])},...e[14]&&{where:c(e[14])}})},{name:"createindex_expressions$ebnf$1",symbols:[]},{name:"createindex_expressions$ebnf$1$subexpression$1",symbols:["comma","createindex_expression"],postprocess:m},{name:"createindex_expressions$ebnf$1",symbols:["createindex_expressions$ebnf$1","createindex_expressions$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"createindex_expressions",symbols:["createindex_expression","createindex_expressions$ebnf$1"],postprocess:([e,t])=>[e,...t||[]]},{name:"createindex_expression$subexpression$1",symbols:["expr_basic"]},{name:"createindex_expression$subexpression$1",symbols:["expr_paren"]},{name:"createindex_expression$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("kw_collate")?{type:"kw_collate"}:kw_collate,"qualified_name"],postprocess:m},{name:"createindex_expression$ebnf$1",symbols:["createindex_expression$ebnf$1$subexpression$1"],postprocess:r},{name:"createindex_expression$ebnf$1",symbols:[],postprocess:()=>null},{name:"createindex_expression$ebnf$2",symbols:["qualified_name"],postprocess:r},{name:"createindex_expression$ebnf$2",symbols:[],postprocess:()=>null},{name:"createindex_expression$ebnf$3$subexpression$1",symbols:[a.lexerAny.has("kw_asc")?{type:"kw_asc"}:kw_asc]},{name:"createindex_expression$ebnf$3$subexpression$1",symbols:[a.lexerAny.has("kw_desc")?{type:"kw_desc"}:kw_desc]},{name:"createindex_expression$ebnf$3",symbols:["createindex_expression$ebnf$3$subexpression$1"],postprocess:r},{name:"createindex_expression$ebnf$3",symbols:[],postprocess:()=>null},{name:"createindex_expression$ebnf$4$subexpression$1$subexpression$1",symbols:["kw_first"]},{name:"createindex_expression$ebnf$4$subexpression$1$subexpression$1",symbols:["kw_last"]},{name:"createindex_expression$ebnf$4$subexpression$1",symbols:["kw_nulls","createindex_expression$ebnf$4$subexpression$1$subexpression$1"],postprocess:m},{name:"createindex_expression$ebnf$4",symbols:["createindex_expression$ebnf$4$subexpression$1"],postprocess:r},{name:"createindex_expression$ebnf$4",symbols:[],postprocess:()=>null},{name:"createindex_expression",symbols:["createindex_expression$subexpression$1","createindex_expression$ebnf$1","createindex_expression$ebnf$2","createindex_expression$ebnf$3","createindex_expression$ebnf$4"],postprocess:e=>(0,n.track)(e,{expression:c(e[0]),...e[1]&&{collate:c(e[1])},...e[2]&&{opclass:c(e[2])},...e[3]&&{order:c(e[3]).value},...e[4]&&{nulls:c(e[4])}})},{name:"createindex_predicate",symbols:[a.lexerAny.has("kw_where")?{type:"kw_where"}:kw_where,"expr"],postprocess:m},{name:"createindex_with$macrocall$2",symbols:["createindex_with_item"]},{name:"createindex_with$macrocall$1$ebnf$1",symbols:[]},{name:"createindex_with$macrocall$1$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("comma")?{type:"comma"}:comma,"createindex_with$macrocall$2"],postprocess:m},{name:"createindex_with$macrocall$1$ebnf$1",symbols:["createindex_with$macrocall$1$ebnf$1","createindex_with$macrocall$1$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"createindex_with$macrocall$1",symbols:["createindex_with$macrocall$2","createindex_with$macrocall$1$ebnf$1"],postprocess:([e,t])=>[c(e),...t.map(c)||[]]},{name:"createindex_with",symbols:[a.lexerAny.has("kw_with")?{type:"kw_with"}:kw_with,"lparen","createindex_with$macrocall$1","rparen"],postprocess:p(2)},{name:"createindex_with_item$subexpression$1",symbols:["string"]},{name:"createindex_with_item$subexpression$1",symbols:["int"]},{name:"createindex_with_item",symbols:["ident",a.lexerAny.has("op_eq")?{type:"op_eq"}:op_eq,"createindex_with_item$subexpression$1"],postprocess:e=>(0,n.track)(e,{parameter:d(e[0]),value:c(e[2]).toString()})},{name:"createindex_tblspace",symbols:["kw_tablespace","ident"],postprocess:m},{name:"createextension_statement$ebnf$1",symbols:["kw_ifnotexists"],postprocess:r},{name:"createextension_statement$ebnf$1",symbols:[],postprocess:()=>null},{name:"createextension_statement$ebnf$2",symbols:[a.lexerAny.has("kw_with")?{type:"kw_with"}:kw_with],postprocess:r},{name:"createextension_statement$ebnf$2",symbols:[],postprocess:()=>null},{name:"createextension_statement$ebnf$3$subexpression$1",symbols:["kw_schema","word"],postprocess:m},{name:"createextension_statement$ebnf$3",symbols:["createextension_statement$ebnf$3$subexpression$1"],postprocess:r},{name:"createextension_statement$ebnf$3",symbols:[],postprocess:()=>null},{name:"createextension_statement$ebnf$4$subexpression$1",symbols:["kw_version","string"],postprocess:m},{name:"createextension_statement$ebnf$4",symbols:["createextension_statement$ebnf$4$subexpression$1"],postprocess:r},{name:"createextension_statement$ebnf$4",symbols:[],postprocess:()=>null},{name:"createextension_statement$ebnf$5$subexpression$1",symbols:[a.lexerAny.has("kw_from")?{type:"kw_from"}:kw_from,"string"],postprocess:m},{name:"createextension_statement$ebnf$5",symbols:["createextension_statement$ebnf$5$subexpression$1"],postprocess:r},{name:"createextension_statement$ebnf$5",symbols:[],postprocess:()=>null},{name:"createextension_statement",symbols:[a.lexerAny.has("kw_create")?{type:"kw_create"}:kw_create,"kw_extension","createextension_statement$ebnf$1","word","createextension_statement$ebnf$2","createextension_statement$ebnf$3","createextension_statement$ebnf$4","createextension_statement$ebnf$5"],postprocess:e=>(0,n.track)(e,{type:"create extension",...e[2]?{ifNotExists:!0}:{},extension:o(e[3]),...e[5]?{schema:o(e[5])}:{},...e[6]?{version:i(e[6])}:{},...e[7]?{from:i(e[7])}:{}})},{name:"simplestatements_all",symbols:["simplestatements_start_transaction"]},{name:"simplestatements_all",symbols:["simplestatements_commit"]},{name:"simplestatements_all",symbols:["simplestatements_rollback"]},{name:"simplestatements_all",symbols:["simplestatements_tablespace"]},{name:"simplestatements_all",symbols:["simplestatements_set"]},{name:"simplestatements_all",symbols:["simplestatements_show"]},{name:"simplestatements_all",symbols:["simplestatements_begin"]},{name:"simplestatements_start_transaction$subexpression$1",symbols:["kw_start","kw_transaction"]},{name:"simplestatements_start_transaction",symbols:["simplestatements_start_transaction$subexpression$1"],postprocess:e=>(0,n.track)(e,{type:"start transaction"})},{name:"simplestatements_commit",symbols:["kw_commit"],postprocess:e=>(0,n.track)(e,{type:"commit"})},{name:"simplestatements_rollback",symbols:["kw_rollback"],postprocess:e=>(0,n.track)(e,{type:"rollback"})},{name:"simplestatements_tablespace",symbols:["kw_tablespace","word"],postprocess:e=>(0,n.track)(e,{type:"tablespace",tablespace:o(e[1])})},{name:"simplestatements_set$subexpression$1",symbols:["simplestatements_set_simple"]},{name:"simplestatements_set$subexpression$1",symbols:["simplestatements_set_timezone"]},{name:"simplestatements_set$subexpression$1",symbols:["simplestatements_set_names"]},{name:"simplestatements_set",symbols:["kw_set","simplestatements_set$subexpression$1"],postprocess:m},{name:"simplestatements_set_timezone",symbols:["kw_time","kw_zone","simplestatements_set_timezone_val"],postprocess:e=>(0,n.track)(e,{type:"set timezone",to:e[2]})},{name:"simplestatements_set_timezone_val$subexpression$1",symbols:["string"]},{name:"simplestatements_set_timezone_val$subexpression$1",symbols:["int"]},{name:"simplestatements_set_timezone_val",symbols:["simplestatements_set_timezone_val$subexpression$1"],postprocess:e=>(0,n.track)(e,{type:"value",value:c(e[0])})},{name:"simplestatements_set_timezone_val",symbols:["kw_local"],postprocess:e=>(0,n.track)(e,{type:"local"})},{name:"simplestatements_set_timezone_val",symbols:[a.lexerAny.has("kw_default")?{type:"kw_default"}:kw_default],postprocess:e=>(0,n.track)(e,{type:"default"})},{name:"simplestatements_set_timezone_val",symbols:["kw_interval","string","kw_hour",a.lexerAny.has("kw_to")?{type:"kw_to"}:kw_to,"kw_minute"],postprocess:e=>(0,n.track)(e,{type:"interval",value:(0,n.unbox)(e[1])})},{name:"simplestatements_set_names",symbols:["kw_names","simplestatements_set_names_val"],postprocess:e=>(0,n.track)(e,{type:"set names",to:e[1]})},{name:"simplestatements_set_names_val$subexpression$1",symbols:["string"]},{name:"simplestatements_set_names_val",symbols:["simplestatements_set_names_val$subexpression$1"],postprocess:e=>(0,n.track)(e,{type:"value",value:c(e[0])})},{name:"simplestatements_set_simple$ebnf$1$subexpression$1",symbols:["kw_local"]},{name:"simplestatements_set_simple$ebnf$1$subexpression$1",symbols:["kw_session"]},{name:"simplestatements_set_simple$ebnf$1",symbols:["simplestatements_set_simple$ebnf$1$subexpression$1"],postprocess:r},{name:"simplestatements_set_simple$ebnf$1",symbols:[],postprocess:()=>null},{name:"simplestatements_set_simple$subexpression$1",symbols:[a.lexerAny.has("op_eq")?{type:"op_eq"}:op_eq]},{name:"simplestatements_set_simple$subexpression$1",symbols:[a.lexerAny.has("kw_to")?{type:"kw_to"}:kw_to]},{name:"simplestatements_set_simple",symbols:["simplestatements_set_simple$ebnf$1","ident","simplestatements_set_simple$subexpression$1","simplestatements_set_val"],postprocess:e=>{var t;return(0,n.track)(e,{type:"set",variable:o(e[1]),scope:null===(t=c(e[0]))||void 0===t?void 0:t.toLowerCase(),set:(0,n.unbox)(e[3])})}},{name:"simplestatements_set_val",symbols:["simplestatements_set_val_raw"],postprocess:c},{name:"simplestatements_set_val",symbols:[a.lexerAny.has("kw_default")?{type:"kw_default"}:kw_default],postprocess:e=>(0,n.track)(e,{type:"default"})},{name:"simplestatements_set_val$ebnf$1$subexpression$1",symbols:["comma","simplestatements_set_val_raw"]},{name:"simplestatements_set_val$ebnf$1",symbols:["simplestatements_set_val$ebnf$1$subexpression$1"]},{name:"simplestatements_set_val$ebnf$1$subexpression$2",symbols:["comma","simplestatements_set_val_raw"]},{name:"simplestatements_set_val$ebnf$1",symbols:["simplestatements_set_val$ebnf$1","simplestatements_set_val$ebnf$1$subexpression$2"],postprocess:e=>e[0].concat([e[1]])},{name:"simplestatements_set_val",symbols:["simplestatements_set_val_raw","simplestatements_set_val$ebnf$1"],postprocess:e=>(0,n.track)(e,{type:"list",values:[e[0],...e[1]||[]]})},{name:"simplestatements_set_val_raw$subexpression$1",symbols:["string"]},{name:"simplestatements_set_val_raw$subexpression$1",symbols:["int"]},{name:"simplestatements_set_val_raw",symbols:["simplestatements_set_val_raw$subexpression$1"],postprocess:e=>(0,n.track)(e,{type:"value",value:c(e)})},{name:"simplestatements_set_val_raw$subexpression$2",symbols:[a.lexerAny.has("word")?{type:"word"}:word]},{name:"simplestatements_set_val_raw$subexpression$2",symbols:[a.lexerAny.has("kw_on")?{type:"kw_on"}:kw_on]},{name:"simplestatements_set_val_raw$subexpression$2",symbols:[a.lexerAny.has("kw_true")?{type:"kw_true"}:kw_true]},{name:"simplestatements_set_val_raw$subexpression$2",symbols:[a.lexerAny.has("kw_false")?{type:"kw_false"}:kw_false]},{name:"simplestatements_set_val_raw",symbols:["simplestatements_set_val_raw$subexpression$2"],postprocess:e=>(0,n.track)(e,{type:"identifier",name:c(e).value})},{name:"simplestatements_set_val_raw",symbols:[a.lexerAny.has("quoted_word")?{type:"quoted_word"}:quoted_word],postprocess:e=>(0,n.track)(e,{type:"identifier",doubleQuoted:!0,name:c(e).value})},{name:"simplestatements_show",symbols:["kw_show","ident"],postprocess:e=>(0,n.track)(e,{type:"show",variable:o(e[1])})},{name:"create_schema$subexpression$1",symbols:[a.lexerAny.has("kw_create")?{type:"kw_create"}:kw_create,"kw_schema"]},{name:"create_schema$ebnf$1",symbols:["kw_ifnotexists"],postprocess:r},{name:"create_schema$ebnf$1",symbols:[],postprocess:()=>null},{name:"create_schema",symbols:["create_schema$subexpression$1","create_schema$ebnf$1","ident"],postprocess:e=>(0,n.track)(e,{type:"create schema",name:o(e[2]),...e[1]?{ifNotExists:!0}:{}})},{name:"raise_statement$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:w("debug","log","info","notice","warning","exception")},{name:"raise_statement$ebnf$1",symbols:["raise_statement$ebnf$1$subexpression$1"],postprocess:r},{name:"raise_statement$ebnf$1",symbols:[],postprocess:()=>null},{name:"raise_statement$ebnf$2$subexpression$1",symbols:["comma","expr_list_raw"],postprocess:m},{name:"raise_statement$ebnf$2",symbols:["raise_statement$ebnf$2$subexpression$1"],postprocess:r},{name:"raise_statement$ebnf$2",symbols:[],postprocess:()=>null},{name:"raise_statement$ebnf$3",symbols:["raise_using"],postprocess:r},{name:"raise_statement$ebnf$3",symbols:[],postprocess:()=>null},{name:"raise_statement",symbols:["kw_raise","raise_statement$ebnf$1","string","raise_statement$ebnf$2","raise_statement$ebnf$3"],postprocess:e=>(0,n.track)(e,{type:"raise",format:d(e[2]),...e[1]&&{level:d(e[1])},...e[3]&&e[3].length&&{formatExprs:e[3]},...e[4]&&e[4].length&&{using:e[4]}})},{name:"raise_using$macrocall$2",symbols:["raise_using_one"]},{name:"raise_using$macrocall$1$ebnf$1",symbols:[]},{name:"raise_using$macrocall$1$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("comma")?{type:"comma"}:comma,"raise_using$macrocall$2"],postprocess:m},{name:"raise_using$macrocall$1$ebnf$1",symbols:["raise_using$macrocall$1$ebnf$1","raise_using$macrocall$1$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"raise_using$macrocall$1",symbols:["raise_using$macrocall$2","raise_using$macrocall$1$ebnf$1"],postprocess:([e,t])=>[c(e),...t.map(c)||[]]},{name:"raise_using",symbols:[a.lexerAny.has("kw_using")?{type:"kw_using"}:kw_using,"raise_using$macrocall$1"],postprocess:m},{name:"raise_using_one",symbols:["raise_using_what",a.lexerAny.has("op_eq")?{type:"op_eq"}:op_eq,"expr"],postprocess:e=>(0,n.track)(e,{type:d(e[0]),value:e[2]})},{name:"raise_using_what",symbols:[a.lexerAny.has("kw_table")?{type:"kw_table"}:kw_table]},{name:"raise_using_what",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:w("message","detail","hint","errcode","column","constraint","datatype","schema")},{name:"comment_statement",symbols:["kw_comment",a.lexerAny.has("kw_on")?{type:"kw_on"}:kw_on,"comment_what",a.lexerAny.has("kw_is")?{type:"kw_is"}:kw_is,"string"],postprocess:e=>(0,n.track)(e,{type:"comment",comment:(0,n.unbox)(m(e)),on:c(e[2])})},{name:"comment_what",symbols:["comment_what_col"]},{name:"comment_what",symbols:["comment_what_nm"]},{name:"comment_what_nm$subexpression$1",symbols:[a.lexerAny.has("kw_table")?{type:"kw_table"}:kw_table]},{name:"comment_what_nm$subexpression$1",symbols:["kw_materialized","kw_view"]},{name:"comment_what_nm$subexpression$1",symbols:[a.lexerAny.has("word")?{type:"word"}:word],postprocess:w("database","index","trigger","type","view")},{name:"comment_what_nm",symbols:["comment_what_nm$subexpression$1","qualified_name"],postprocess:e=>(0,n.track)(e,{type:d(e[0]),name:e[1]})},{name:"comment_what_col",symbols:["kw_column","qcolumn"],postprocess:e=>(0,n.track)(e,{type:"column",column:m(e)})},{name:"simplestatements_begin$ebnf$1$subexpression$1",symbols:["kw_transaction"]},{name:"simplestatements_begin$ebnf$1$subexpression$1",symbols:["kw_work"]},{name:"simplestatements_begin$ebnf$1",symbols:["simplestatements_begin$ebnf$1$subexpression$1"],postprocess:r},{name:"simplestatements_begin$ebnf$1",symbols:[],postprocess:()=>null},{name:"simplestatements_begin$ebnf$2",symbols:[]},{name:"simplestatements_begin$ebnf$2$subexpression$1",symbols:["simplestatements_begin_isol"]},{name:"simplestatements_begin$ebnf$2$subexpression$1",symbols:["simplestatements_begin_writ"]},{name:"simplestatements_begin$ebnf$2$subexpression$1",symbols:["simplestatements_begin_def"]},{name:"simplestatements_begin$ebnf$2",symbols:["simplestatements_begin$ebnf$2","simplestatements_begin$ebnf$2$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"simplestatements_begin",symbols:["kw_begin","simplestatements_begin$ebnf$1","simplestatements_begin$ebnf$2"],postprocess:e=>(0,n.track)(e,{type:"begin",...e[2].reduce(((e,t)=>({...c(e),...c(t)})),{})})},{name:"simplestatements_begin_isol$subexpression$1",symbols:["kw_isolation","kw_level"]},{name:"simplestatements_begin_isol$subexpression$2",symbols:["kw_serializable"]},{name:"simplestatements_begin_isol$subexpression$2$subexpression$1",symbols:["word"],postprocess:f("repeatable")},{name:"simplestatements_begin_isol$subexpression$2",symbols:["simplestatements_begin_isol$subexpression$2$subexpression$1","kw_read"]},{name:"simplestatements_begin_isol$subexpression$2$subexpression$2",symbols:["word"],postprocess:f("committed")},{name:"simplestatements_begin_isol$subexpression$2",symbols:["kw_read","simplestatements_begin_isol$subexpression$2$subexpression$2"]},{name:"simplestatements_begin_isol$subexpression$2$subexpression$3",symbols:["word"],postprocess:f("uncommitted")},{name:"simplestatements_begin_isol$subexpression$2",symbols:["kw_read","simplestatements_begin_isol$subexpression$2$subexpression$3"]},{name:"simplestatements_begin_isol",symbols:["simplestatements_begin_isol$subexpression$1","simplestatements_begin_isol$subexpression$2"],postprocess:e=>(0,n.track)(e,{isolationLevel:d(e[1]," ")})},{name:"simplestatements_begin_writ$subexpression$1",symbols:["kw_read","kw_write"]},{name:"simplestatements_begin_writ$subexpression$1",symbols:["kw_read",a.lexerAny.has("kw_only")?{type:"kw_only"}:kw_only]},{name:"simplestatements_begin_writ",symbols:["simplestatements_begin_writ$subexpression$1"],postprocess:e=>(0,n.track)(e,{writeable:d(e," ")})},{name:"simplestatements_begin_def$ebnf$1",symbols:[a.lexerAny.has("kw_not")?{type:"kw_not"}:kw_not],postprocess:r},{name:"simplestatements_begin_def$ebnf$1",symbols:[],postprocess:()=>null},{name:"simplestatements_begin_def",symbols:["simplestatements_begin_def$ebnf$1",a.lexerAny.has("kw_deferrable")?{type:"kw_deferrable"}:kw_deferrable],postprocess:e=>(0,n.track)(e,{deferrable:!e[0]})},{name:"insert_statement$subexpression$1",symbols:["kw_insert",a.lexerAny.has("kw_into")?{type:"kw_into"}:kw_into]},{name:"insert_statement$ebnf$1",symbols:["collist_paren"],postprocess:r},{name:"insert_statement$ebnf$1",symbols:[],postprocess:()=>null},{name:"insert_statement$ebnf$2$subexpression$1$subexpression$1",symbols:["kw_system"]},{name:"insert_statement$ebnf$2$subexpression$1$subexpression$1",symbols:[a.lexerAny.has("kw_user")?{type:"kw_user"}:kw_user]},{name:"insert_statement$ebnf$2$subexpression$1",symbols:["kw_overriding","insert_statement$ebnf$2$subexpression$1$subexpression$1","kw_value"],postprocess:p(1)},{name:"insert_statement$ebnf$2",symbols:["insert_statement$ebnf$2$subexpression$1"],postprocess:r},{name:"insert_statement$ebnf$2",symbols:[],postprocess:()=>null},{name:"insert_statement$ebnf$3$subexpression$1",symbols:["selection"]},{name:"insert_statement$ebnf$3$subexpression$1",symbols:["selection_paren"]},{name:"insert_statement$ebnf$3",symbols:["insert_statement$ebnf$3$subexpression$1"],postprocess:r},{name:"insert_statement$ebnf$3",symbols:[],postprocess:()=>null},{name:"insert_statement$ebnf$4$subexpression$1",symbols:[a.lexerAny.has("kw_on")?{type:"kw_on"}:kw_on,"kw_conflict","insert_on_conflict"],postprocess:m},{name:"insert_statement$ebnf$4",symbols:["insert_statement$ebnf$4$subexpression$1"],postprocess:r},{name:"insert_statement$ebnf$4",symbols:[],postprocess:()=>null},{name:"insert_statement$ebnf$5$subexpression$1",symbols:[a.lexerAny.has("kw_returning")?{type:"kw_returning"}:kw_returning,"select_expr_list_aliased"],postprocess:m},{name:"insert_statement$ebnf$5",symbols:["insert_statement$ebnf$5$subexpression$1"],postprocess:r},{name:"insert_statement$ebnf$5",symbols:[],postprocess:()=>null},{name:"insert_statement",symbols:["insert_statement$subexpression$1","table_ref_aliased","insert_statement$ebnf$1","insert_statement$ebnf$2","insert_statement$ebnf$3","insert_statement$ebnf$4","insert_statement$ebnf$5"],postprocess:e=>{const t=e[2]&&e[2].map(o),s=d(e[3]),r=c(e[4]),a=e[5],l=e[6];return(0,n.track)(e,{type:"insert",into:c(e[1]),insert:r,...s&&{overriding:s},...t&&{columns:t},...l&&{returning:l},...a&&{onConflict:a}})}},{name:"insert_values$ebnf$1",symbols:[]},{name:"insert_values$ebnf$1$subexpression$1",symbols:["comma","insert_value"],postprocess:m},{name:"insert_values$ebnf$1",symbols:["insert_values$ebnf$1","insert_values$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"insert_values",symbols:["insert_value","insert_values$ebnf$1"],postprocess:([e,t])=>[e,...t||[]]},{name:"insert_value",symbols:["lparen","insert_expr_list_raw","rparen"],postprocess:p(1)},{name:"insert_expr_list_raw$ebnf$1",symbols:[]},{name:"insert_expr_list_raw$ebnf$1$subexpression$1",symbols:["comma","expr_or_select"],postprocess:m},{name:"insert_expr_list_raw$ebnf$1",symbols:["insert_expr_list_raw$ebnf$1","insert_expr_list_raw$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"insert_expr_list_raw",symbols:["expr_or_select","insert_expr_list_raw$ebnf$1"],postprocess:([e,t])=>[e,...t||[]]},{name:"insert_on_conflict$ebnf$1",symbols:["insert_on_conflict_what"],postprocess:r},{name:"insert_on_conflict$ebnf$1",symbols:[],postprocess:()=>null},{name:"insert_on_conflict",symbols:["insert_on_conflict$ebnf$1","insert_on_conflict_do"],postprocess:e=>(0,n.track)(e,{...e[0]?{on:c(e[0])}:{},...e[1]})},{name:"insert_on_conflict_what",symbols:["lparen","expr_list_raw","rparen"],postprocess:e=>(0,n.track)(e,{type:"on expr",exprs:e[1]})},{name:"insert_on_conflict_what",symbols:[a.lexerAny.has("kw_on")?{type:"kw_on"}:kw_on,a.lexerAny.has("kw_constraint")?{type:"kw_constraint"}:kw_constraint,"qname"],postprocess:e=>(0,n.track)(e,{type:"on constraint",constraint:m(e)})},{name:"insert_on_conflict_do",symbols:[a.lexerAny.has("kw_do")?{type:"kw_do"}:kw_do,"kw_nothing"],postprocess:e=>({do:"do nothing"})},{name:"insert_on_conflict_do$subexpression$1",symbols:[a.lexerAny.has("kw_do")?{type:"kw_do"}:kw_do,"kw_update","kw_set"]},{name:"insert_on_conflict_do$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("kw_where")?{type:"kw_where"}:kw_where,"expr"],postprocess:m},{name:"insert_on_conflict_do$ebnf$1",symbols:["insert_on_conflict_do$ebnf$1$subexpression$1"],postprocess:r},{name:"insert_on_conflict_do$ebnf$1",symbols:[],postprocess:()=>null},{name:"insert_on_conflict_do",symbols:["insert_on_conflict_do$subexpression$1","update_set_list","insert_on_conflict_do$ebnf$1"],postprocess:e=>({do:{sets:e[1]},...e[2]&&{where:e[2]}})},{name:"update_statement$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("kw_from")?{type:"kw_from"}:kw_from,"select_from_subject"],postprocess:m},{name:"update_statement$ebnf$1",symbols:["update_statement$ebnf$1$subexpression$1"],postprocess:r},{name:"update_statement$ebnf$1",symbols:[],postprocess:()=>null},{name:"update_statement$ebnf$2",symbols:["select_where"],postprocess:r},{name:"update_statement$ebnf$2",symbols:[],postprocess:()=>null},{name:"update_statement$ebnf$3$subexpression$1",symbols:[a.lexerAny.has("kw_returning")?{type:"kw_returning"}:kw_returning,"select_expr_list_aliased"],postprocess:m},{name:"update_statement$ebnf$3",symbols:["update_statement$ebnf$3$subexpression$1"],postprocess:r},{name:"update_statement$ebnf$3",symbols:[],postprocess:()=>null},{name:"update_statement",symbols:["kw_update","table_ref_aliased","kw_set","update_set_list","update_statement$ebnf$1","update_statement$ebnf$2","update_statement$ebnf$3"],postprocess:e=>{const t=c(e[4]),s=c(e[5]),r=e[6];return(0,n.track)(e,{type:"update",table:c(e[1]),sets:e[3],...s?{where:s}:{},...t?{from:t}:{},...r?{returning:r}:{}})}},{name:"update_set_list$ebnf$1",symbols:[]},{name:"update_set_list$ebnf$1$subexpression$1",symbols:["comma","update_set"],postprocess:m},{name:"update_set_list$ebnf$1",symbols:["update_set_list$ebnf$1","update_set_list$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"update_set_list",symbols:["update_set","update_set_list$ebnf$1"],postprocess:([e,t])=>{const s=[];for(const r of[e,...t||[]]){const e=c(r);Array.isArray(e)?s.push(...e):s.push(e)}return s}},{name:"update_set",symbols:["update_set_one"]},{name:"update_set",symbols:["update_set_multiple"]},{name:"update_set_one",symbols:["ident",a.lexerAny.has("op_eq")?{type:"op_eq"}:op_eq,"expr"],postprocess:e=>(0,n.box)(e,{column:o(e[0]),value:c(e[2])})},{name:"update_set_multiple$subexpression$1",symbols:["lparen","expr_list_raw","rparen"],postprocess:p(1)},{name:"update_set_multiple",symbols:["collist_paren",a.lexerAny.has("op_eq")?{type:"op_eq"}:op_eq,"update_set_multiple$subexpression$1"],postprocess:e=>{const t=e[0],s=e[2];if(t.length!==s.length)throw new Error("number of columns does not match number of values");return(0,n.box)(e,t.map(((e,t)=>({column:o(e),value:c(s[t])}))))}},{name:"altertable_statement$ebnf$1",symbols:["kw_ifexists"],postprocess:r},{name:"altertable_statement$ebnf$1",symbols:[],postprocess:()=>null},{name:"altertable_statement$ebnf$2",symbols:[a.lexerAny.has("kw_only")?{type:"kw_only"}:kw_only],postprocess:r},{name:"altertable_statement$ebnf$2",symbols:[],postprocess:()=>null},{name:"altertable_statement",symbols:["kw_alter",a.lexerAny.has("kw_table")?{type:"kw_table"}:kw_table,"altertable_statement$ebnf$1","altertable_statement$ebnf$2","table_ref","altertable_actions"],postprocess:e=>(0,n.track)(e,{type:"alter table",...e[2]?{ifExists:!0}:{},...e[3]?{only:!0}:{},table:c(e[4]),changes:(0,n.unbox)(e[5]).map(c)})},{name:"altertable_actions$ebnf$1",symbols:[]},{name:"altertable_actions$ebnf$1$subexpression$1",symbols:["comma","altertable_action"],postprocess:m},{name:"altertable_actions$ebnf$1",symbols:["altertable_actions$ebnf$1","altertable_actions$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"altertable_actions",symbols:["altertable_action","altertable_actions$ebnf$1"],postprocess:([e,t])=>[e,...t||[]]},{name:"altertable_action",symbols:["altertable_rename_table"]},{name:"altertable_action",symbols:["altertable_rename_column"]},{name:"altertable_action",symbols:["altertable_rename_constraint"]},{name:"altertable_action",symbols:["altertable_add_column"]},{name:"altertable_action",symbols:["altertable_drop_column"]},{name:"altertable_action",symbols:["altertable_alter_column"]},{name:"altertable_action",symbols:["altertable_add_constraint"]},{name:"altertable_action",symbols:["altertable_drop_constraint"]},{name:"altertable_action",symbols:["altertable_owner"]},{name:"altertable_rename_table",symbols:["kw_rename",a.lexerAny.has("kw_to")?{type:"kw_to"}:kw_to,"word"],postprocess:e=>(0,n.track)(e,{type:"rename",to:o(m(e))})},{name:"altertable_rename_column$ebnf$1",symbols:["kw_column"],postprocess:r},{name:"altertable_rename_column$ebnf$1",symbols:[],postprocess:()=>null},{name:"altertable_rename_column",symbols:["kw_rename","altertable_rename_column$ebnf$1","ident",a.lexerAny.has("kw_to")?{type:"kw_to"}:kw_to,"ident"],postprocess:e=>(0,n.track)(e,{type:"rename column",column:o(e[2]),to:o(m(e))})},{name:"altertable_rename_constraint",symbols:["kw_rename",a.lexerAny.has("kw_constraint")?{type:"kw_constraint"}:kw_constraint,"ident",a.lexerAny.has("kw_to")?{type:"kw_to"}:kw_to,"ident"],postprocess:e=>(0,n.track)(e,{type:"rename constraint",constraint:o(e[2]),to:o(m(e))})},{name:"altertable_add_column$ebnf$1",symbols:["kw_column"],postprocess:r},{name:"altertable_add_column$ebnf$1",symbols:[],postprocess:()=>null},{name:"altertable_add_column$ebnf$2",symbols:["kw_ifnotexists"],postprocess:r},{name:"altertable_add_column$ebnf$2",symbols:[],postprocess:()=>null},{name:"altertable_add_column",symbols:["kw_add","altertable_add_column$ebnf$1","altertable_add_column$ebnf$2","createtable_column"],postprocess:e=>(0,n.track)(e,{type:"add column",...e[2]?{ifNotExists:!0}:{},column:c(e[3])})},{name:"altertable_drop_column$ebnf$1",symbols:["kw_column"],postprocess:r},{name:"altertable_drop_column$ebnf$1",symbols:[],postprocess:()=>null},{name:"altertable_drop_column$ebnf$2",symbols:["kw_ifexists"],postprocess:r},{name:"altertable_drop_column$ebnf$2",symbols:[],postprocess:()=>null},{name:"altertable_drop_column$ebnf$3$subexpression$1",symbols:["kw_restrict"]},{name:"altertable_drop_column$ebnf$3$subexpression$1",symbols:["kw_cascade"]},{name:"altertable_drop_column$ebnf$3",symbols:["altertable_drop_column$ebnf$3$subexpression$1"],postprocess:r},{name:"altertable_drop_column$ebnf$3",symbols:[],postprocess:()=>null},{name:"altertable_drop_column",symbols:["kw_drop","altertable_drop_column$ebnf$1","altertable_drop_column$ebnf$2","ident","altertable_drop_column$ebnf$3"],postprocess:e=>(0,n.track)(e,{type:"drop column",...e[2]?{ifExists:!0}:{},column:o(e[3]),...e[4]?{behaviour:d(e[4]," ")}:{}})},{name:"altertable_alter_column$ebnf$1",symbols:["kw_column"],postprocess:r},{name:"altertable_alter_column$ebnf$1",symbols:[],postprocess:()=>null},{name:"altertable_alter_column",symbols:["kw_alter","altertable_alter_column$ebnf$1","ident","altercol"],postprocess:e=>(0,n.track)(e,{type:"alter column",column:o(e[2]),alter:c(e[3])})},{name:"altercol$ebnf$1$subexpression$1",symbols:["kw_set","kw_data"]},{name:"altercol$ebnf$1",symbols:["altercol$ebnf$1$subexpression$1"],postprocess:r},{name:"altercol$ebnf$1",symbols:[],postprocess:()=>null},{name:"altercol",symbols:["altercol$ebnf$1","kw_type","data_type"],postprocess:e=>(0,n.track)(e,{type:"set type",dataType:c(m(e))})},{name:"altercol",symbols:["kw_set",a.lexerAny.has("kw_default")?{type:"kw_default"}:kw_default,"expr"],postprocess:e=>(0,n.track)(e,{type:"set default",default:c(m(e))})},{name:"altercol",symbols:["kw_drop",a.lexerAny.has("kw_default")?{type:"kw_default"}:kw_default],postprocess:e=>(0,n.track)(e,{type:"drop default"})},{name:"altercol$subexpression$1",symbols:["kw_set"]},{name:"altercol$subexpression$1",symbols:["kw_drop"]},{name:"altercol",symbols:["altercol$subexpression$1","kw_not_null"],postprocess:e=>(0,n.track)(e,{type:d(e," ")})},{name:"altercol",symbols:["altercol_generated_add"],postprocess:c},{name:"altertable_add_constraint",symbols:["kw_add","createtable_constraint"],postprocess:e=>(0,n.track)(e,{type:"add constraint",constraint:c(m(e))})},{name:"altertable_drop_constraint$ebnf$1",symbols:["kw_ifexists"],postprocess:r},{name:"altertable_drop_constraint$ebnf$1",symbols:[],postprocess:()=>null},{name:"altertable_drop_constraint$ebnf$2$subexpression$1",symbols:["kw_restrict"]},{name:"altertable_drop_constraint$ebnf$2$subexpression$1",symbols:["kw_cascade"]},{name:"altertable_drop_constraint$ebnf$2",symbols:["altertable_drop_constraint$ebnf$2$subexpression$1"],postprocess:r},{name:"altertable_drop_constraint$ebnf$2",symbols:[],postprocess:()=>null},{name:"altertable_drop_constraint",symbols:["kw_drop",a.lexerAny.has("kw_constraint")?{type:"kw_constraint"}:kw_constraint,"altertable_drop_constraint$ebnf$1","ident","altertable_drop_constraint$ebnf$2"],postprocess:e=>(0,n.track)(e,{type:"drop constraint",...e[2]?{ifExists:!0}:{},constraint:o(e[3]),...e[4]?{behaviour:d(e[4]," ")}:{}})},{name:"altertable_owner",symbols:["kw_owner",a.lexerAny.has("kw_to")?{type:"kw_to"}:kw_to,"ident"],postprocess:e=>(0,n.track)(e,{type:"owner",to:o(m(e))})},{name:"altercol_generated_add",symbols:["kw_add","altercol_generated"],postprocess:m},{name:"altercol_generated$ebnf$1$subexpression$1",symbols:["kw_always"]},{name:"altercol_generated$ebnf$1$subexpression$1",symbols:["kw_by",a.lexerAny.has("kw_default")?{type:"kw_default"}:kw_default]},{name:"altercol_generated$ebnf$1",symbols:["altercol_generated$ebnf$1$subexpression$1"],postprocess:r},{name:"altercol_generated$ebnf$1",symbols:[],postprocess:()=>null},{name:"altercol_generated$subexpression$1",symbols:[a.lexerAny.has("kw_as")?{type:"kw_as"}:kw_as,"kw_identity"]},{name:"altercol_generated$ebnf$2$subexpression$1",symbols:["lparen","altercol_generated_seq","rparen"],postprocess:p(1)},{name:"altercol_generated$ebnf$2",symbols:["altercol_generated$ebnf$2$subexpression$1"],postprocess:r},{name:"altercol_generated$ebnf$2",symbols:[],postprocess:()=>null},{name:"altercol_generated",symbols:["kw_generated","altercol_generated$ebnf$1","altercol_generated$subexpression$1","altercol_generated$ebnf$2"],postprocess:e=>(0,n.track)(e,{type:"add generated",...e[1]&&{always:d(e[1]," ")},...e[3]&&{sequence:c(e[3])}})},{name:"altercol_generated_seq$ebnf$1$subexpression$1",symbols:["kw_sequence","kw_name","qualified_name"]},{name:"altercol_generated_seq$ebnf$1",symbols:["altercol_generated_seq$ebnf$1$subexpression$1"],postprocess:r},{name:"altercol_generated_seq$ebnf$1",symbols:[],postprocess:()=>null},{name:"altercol_generated_seq$ebnf$2",symbols:[]},{name:"altercol_generated_seq$ebnf$2",symbols:["altercol_generated_seq$ebnf$2","create_sequence_option"],postprocess:e=>e[0].concat([e[1]])},{name:"altercol_generated_seq",symbols:["altercol_generated_seq$ebnf$1","altercol_generated_seq$ebnf$2"],postprocess:e=>{const t={...e[0]&&{name:c(m(e[0]))}};return x(t,e[1]),(0,n.track)(e,t)}},{name:"alterindex_statement$ebnf$1",symbols:["kw_ifexists"],postprocess:r},{name:"alterindex_statement$ebnf$1",symbols:[],postprocess:()=>null},{name:"alterindex_statement",symbols:["kw_alter","kw_index","alterindex_statement$ebnf$1","table_ref","alterindex_action"],postprocess:e=>(0,n.track)(e,{type:"alter index",...e[2]?{ifExists:!0}:{},index:c(e[3]),change:c(e[4])})},{name:"alterindex_action",symbols:["alterindex_rename"]},{name:"alterindex_action",symbols:["alterindex_set_tablespace"]},{name:"alterindex_rename",symbols:["kw_rename",a.lexerAny.has("kw_to")?{type:"kw_to"}:kw_to,"word"],postprocess:e=>(0,n.track)(e,{type:"rename",to:o(m(e))})},{name:"alterindex_set_tablespace",symbols:["kw_set","kw_tablespace","word"],postprocess:e=>(0,n.track)(e,{type:"set tablespace",tablespace:o(m(e))})},{name:"delete_statement",symbols:["delete_delete"]},{name:"delete_statement",symbols:["delete_truncate"]},{name:"delete_delete$subexpression$1",symbols:["kw_delete",a.lexerAny.has("kw_from")?{type:"kw_from"}:kw_from]},{name:"delete_delete$ebnf$1",symbols:["select_where"],postprocess:r},{name:"delete_delete$ebnf$1",symbols:[],postprocess:()=>null},{name:"delete_delete$ebnf$2$subexpression$1",symbols:[a.lexerAny.has("kw_returning")?{type:"kw_returning"}:kw_returning,"select_expr_list_aliased"],postprocess:m},{name:"delete_delete$ebnf$2",symbols:["delete_delete$ebnf$2$subexpression$1"],postprocess:r},{name:"delete_delete$ebnf$2",symbols:[],postprocess:()=>null},{name:"delete_delete",symbols:["delete_delete$subexpression$1","table_ref_aliased","delete_delete$ebnf$1","delete_delete$ebnf$2"],postprocess:e=>{const t=e[2],s=e[3];return(0,n.track)(e,{type:"delete",from:c(e[1]),...t?{where:t}:{},...s?{returning:s}:{}})}},{name:"delete_truncate$subexpression$1$ebnf$1",symbols:[a.lexerAny.has("kw_table")?{type:"kw_table"}:kw_table],postprocess:r},{name:"delete_truncate$subexpression$1$ebnf$1",symbols:[],postprocess:()=>null},{name:"delete_truncate$subexpression$1",symbols:["kw_truncate","delete_truncate$subexpression$1$ebnf$1"]},{name:"delete_truncate$macrocall$2",symbols:["table_ref"]},{name:"delete_truncate$macrocall$1$ebnf$1",symbols:[]},{name:"delete_truncate$macrocall$1$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("comma")?{type:"comma"}:comma,"delete_truncate$macrocall$2"],postprocess:m},{name:"delete_truncate$macrocall$1$ebnf$1",symbols:["delete_truncate$macrocall$1$ebnf$1","delete_truncate$macrocall$1$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"delete_truncate$macrocall$1",symbols:["delete_truncate$macrocall$2","delete_truncate$macrocall$1$ebnf$1"],postprocess:([e,t])=>[c(e),...t.map(c)||[]]},{name:"delete_truncate$ebnf$1$subexpression$1$subexpression$1",symbols:["kw_restart"]},{name:"delete_truncate$ebnf$1$subexpression$1$subexpression$1",symbols:["kw_continue"]},{name:"delete_truncate$ebnf$1$subexpression$1",symbols:["delete_truncate$ebnf$1$subexpression$1$subexpression$1","kw_identity"]},{name:"delete_truncate$ebnf$1",symbols:["delete_truncate$ebnf$1$subexpression$1"],postprocess:r},{name:"delete_truncate$ebnf$1",symbols:[],postprocess:()=>null},{name:"delete_truncate$ebnf$2$subexpression$1",symbols:["kw_restrict"]},{name:"delete_truncate$ebnf$2$subexpression$1",symbols:["kw_cascade"]},{name:"delete_truncate$ebnf$2",symbols:["delete_truncate$ebnf$2$subexpression$1"],postprocess:r},{name:"delete_truncate$ebnf$2",symbols:[],postprocess:()=>null},{name:"delete_truncate",symbols:["delete_truncate$subexpression$1","delete_truncate$macrocall$1","delete_truncate$ebnf$1","delete_truncate$ebnf$2"],postprocess:e=>(0,n.track)(e,{type:"truncate table",tables:e[1],...e[2]&&{identity:d(e[2][0])},...e[3]&&{cascade:d(e[3])}})},{name:"create_sequence_statement$ebnf$1$subexpression$1",symbols:["kw_temp"]},{name:"create_sequence_statement$ebnf$1$subexpression$1",symbols:["kw_temporary"]},{name:"create_sequence_statement$ebnf$1",symbols:["create_sequence_statement$ebnf$1$subexpression$1"],postprocess:r},{name:"create_sequence_statement$ebnf$1",symbols:[],postprocess:()=>null},{name:"create_sequence_statement$ebnf$2",symbols:["kw_ifnotexists"],postprocess:r},{name:"create_sequence_statement$ebnf$2",symbols:[],postprocess:()=>null},{name:"create_sequence_statement$ebnf$3",symbols:[]},{name:"create_sequence_statement$ebnf$3",symbols:["create_sequence_statement$ebnf$3","create_sequence_option"],postprocess:e=>e[0].concat([e[1]])},{name:"create_sequence_statement",symbols:[a.lexerAny.has("kw_create")?{type:"kw_create"}:kw_create,"create_sequence_statement$ebnf$1","kw_sequence","create_sequence_statement$ebnf$2","qualified_name","create_sequence_statement$ebnf$3"],postprocess:e=>{const t={type:"create sequence",...e[1]&&{temp:!0},...e[3]&&{ifNotExists:!0},name:c(e[4]),options:{}};return x(t.options,e[5]),(0,n.track)(e,t)}},{name:"create_sequence_option",symbols:[a.lexerAny.has("kw_as")?{type:"kw_as"}:kw_as,"data_type"],postprocess:e=>(0,n.box)(e,["as",e[1]])},{name:"create_sequence_option$ebnf$1",symbols:["kw_by"],postprocess:r},{name:"create_sequence_option$ebnf$1",symbols:[],postprocess:()=>null},{name:"create_sequence_option",symbols:["kw_increment","create_sequence_option$ebnf$1","int"],postprocess:e=>(0,n.box)(e,["incrementBy",e[2]])},{name:"create_sequence_option",symbols:["create_sequence_minvalue"],postprocess:e=>(0,n.box)(e,["minValue",e[0]])},{name:"create_sequence_option",symbols:["create_sequence_maxvalue"],postprocess:e=>(0,n.box)(e,["maxValue",e[0]])},{name:"create_sequence_option$ebnf$2",symbols:[a.lexerAny.has("kw_with")?{type:"kw_with"}:kw_with],postprocess:r},{name:"create_sequence_option$ebnf$2",symbols:[],postprocess:()=>null},{name:"create_sequence_option",symbols:["kw_start","create_sequence_option$ebnf$2","int"],postprocess:e=>(0,n.box)(e,["startWith",e[2]])},{name:"create_sequence_option",symbols:["kw_cache","int"],postprocess:e=>(0,n.box)(e,["cache",e[1]])},{name:"create_sequence_option$ebnf$3",symbols:["kw_no"],postprocess:r},{name:"create_sequence_option$ebnf$3",symbols:[],postprocess:()=>null},{name:"create_sequence_option",symbols:["create_sequence_option$ebnf$3","kw_cycle"],postprocess:e=>(0,n.box)(e,["cycle",d(e," ")])},{name:"create_sequence_option",symbols:["create_sequence_owned_by"],postprocess:e=>(0,n.box)(e,["ownedBy",c(e)])},{name:"create_sequence_minvalue",symbols:["kw_minvalue","int"],postprocess:m},{name:"create_sequence_minvalue",symbols:["kw_no","kw_minvalue"],postprocess:e=>(0,n.box)(e,"no minvalue")},{name:"create_sequence_maxvalue",symbols:["kw_maxvalue","int"],postprocess:m},{name:"create_sequence_maxvalue",symbols:["kw_no","kw_maxvalue"],postprocess:e=>(0,n.box)(e,"no maxvalue")},{name:"create_sequence_owned_by$subexpression$1",symbols:["kw_none"]},{name:"create_sequence_owned_by$subexpression$1",symbols:["qcolumn"]},{name:"create_sequence_owned_by",symbols:["kw_owned","kw_by","create_sequence_owned_by$subexpression$1"],postprocess:e=>(0,n.box)(e,c(m(e)))},{name:"alter_sequence_statement$ebnf$1",symbols:["kw_ifexists"],postprocess:r},{name:"alter_sequence_statement$ebnf$1",symbols:[],postprocess:()=>null},{name:"alter_sequence_statement",symbols:["kw_alter","kw_sequence","alter_sequence_statement$ebnf$1","qualified_name","alter_sequence_statement_body"],postprocess:e=>{const t={type:"alter sequence",...e[2]&&{ifExists:!0},name:c(e[3]),change:e[4]};return(0,n.track)(e,t)}},{name:"alter_sequence_statement_body$ebnf$1",symbols:["alter_sequence_option"]},{name:"alter_sequence_statement_body$ebnf$1",symbols:["alter_sequence_statement_body$ebnf$1","alter_sequence_option"],postprocess:e=>e[0].concat([e[1]])},{name:"alter_sequence_statement_body",symbols:["alter_sequence_statement_body$ebnf$1"],postprocess:e=>{const t={type:"set options"};return x(t,e[0]),(0,n.track)(e,t)}},{name:"alter_sequence_statement_body$subexpression$1",symbols:["ident"]},{name:"alter_sequence_statement_body$subexpression$1",symbols:[a.lexerAny.has("kw_session_user")?{type:"kw_session_user"}:kw_session_user]},{name:"alter_sequence_statement_body$subexpression$1",symbols:[a.lexerAny.has("kw_current_user")?{type:"kw_current_user"}:kw_current_user]},{name:"alter_sequence_statement_body",symbols:["kw_owner",a.lexerAny.has("kw_to")?{type:"kw_to"}:kw_to,"alter_sequence_statement_body$subexpression$1"],postprocess:e=>(0,n.track)(e,{type:"owner to",owner:o(m(e))})},{name:"alter_sequence_statement_body",symbols:["kw_rename",a.lexerAny.has("kw_to")?{type:"kw_to"}:kw_to,"ident"],postprocess:e=>(0,n.track)(e,{type:"rename",newName:o(m(e))})},{name:"alter_sequence_statement_body",symbols:["kw_set","kw_schema","ident"],postprocess:e=>(0,n.track)(e,{type:"set schema",newSchema:o(m(e))})},{name:"alter_sequence_option",symbols:["create_sequence_option"],postprocess:c},{name:"alter_sequence_option$ebnf$1$subexpression$1$ebnf$1",symbols:[a.lexerAny.has("kw_with")?{type:"kw_with"}:kw_with],postprocess:r},{name:"alter_sequence_option$ebnf$1$subexpression$1$ebnf$1",symbols:[],postprocess:()=>null},{name:"alter_sequence_option$ebnf$1$subexpression$1",symbols:["alter_sequence_option$ebnf$1$subexpression$1$ebnf$1","int"],postprocess:m},{name:"alter_sequence_option$ebnf$1",symbols:["alter_sequence_option$ebnf$1$subexpression$1"],postprocess:r},{name:"alter_sequence_option$ebnf$1",symbols:[],postprocess:()=>null},{name:"alter_sequence_option",symbols:["kw_restart","alter_sequence_option$ebnf$1"],postprocess:e=>(0,n.box)(e,["restart","number"!=typeof(0,n.unbox)(e[1])||(0,n.unbox)(e[1])])},{name:"drop_statement$ebnf$1",symbols:["kw_ifexists"],postprocess:r},{name:"drop_statement$ebnf$1",symbols:[],postprocess:()=>null},{name:"drop_statement$macrocall$2",symbols:["qualified_name"]},{name:"drop_statement$macrocall$1$ebnf$1",symbols:[]},{name:"drop_statement$macrocall$1$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("comma")?{type:"comma"}:comma,"drop_statement$macrocall$2"],postprocess:m},{name:"drop_statement$macrocall$1$ebnf$1",symbols:["drop_statement$macrocall$1$ebnf$1","drop_statement$macrocall$1$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"drop_statement$macrocall$1",symbols:["drop_statement$macrocall$2","drop_statement$macrocall$1$ebnf$1"],postprocess:([e,t])=>[c(e),...t.map(c)||[]]},{name:"drop_statement$ebnf$2$subexpression$1",symbols:["kw_cascade"]},{name:"drop_statement$ebnf$2$subexpression$1",symbols:["kw_restrict"]},{name:"drop_statement$ebnf$2",symbols:["drop_statement$ebnf$2$subexpression$1"],postprocess:r},{name:"drop_statement$ebnf$2",symbols:[],postprocess:()=>null},{name:"drop_statement",symbols:["kw_drop","drop_what","drop_statement$ebnf$1","drop_statement$macrocall$1","drop_statement$ebnf$2"],postprocess:(e,t)=>{const s=c(e[1]);return(0,n.track)(e,{...s,...e[2]&&{ifExists:!0},names:e[3],...e[4]&&{cascade:d(e[4])}})}},{name:"drop_what",symbols:[a.lexerAny.has("kw_table")?{type:"kw_table"}:kw_table],postprocess:e=>(0,n.track)(e,{type:"drop table"})},{name:"drop_what",symbols:["kw_sequence"],postprocess:e=>(0,n.track)(e,{type:"drop sequence"})},{name:"drop_what",symbols:["kw_type"],postprocess:e=>(0,n.track)(e,{type:"drop type"})},{name:"drop_what",symbols:["kw_trigger"],postprocess:e=>(0,n.track)(e,{type:"drop trigger"})},{name:"drop_what$ebnf$1",symbols:[a.lexerAny.has("kw_concurrently")?{type:"kw_concurrently"}:kw_concurrently],postprocess:r},{name:"drop_what$ebnf$1",symbols:[],postprocess:()=>null},{name:"drop_what",symbols:["kw_index","drop_what$ebnf$1"],postprocess:e=>(0,n.track)(e,{type:"drop index",...e[1]&&{concurrently:!0}})},{name:"with_statement",symbols:[a.lexerAny.has("kw_with")?{type:"kw_with"}:kw_with,"with_statement_bindings","with_statement_statement"],postprocess:e=>(0,n.track)(e,{type:"with",bind:e[1],in:c(e[2])})},{name:"with_recursive_statement$subexpression$1",symbols:[a.lexerAny.has("kw_with")?{type:"kw_with"}:kw_with,"kw_recursive"]},{name:"with_recursive_statement",symbols:["with_recursive_statement$subexpression$1","ident","collist_paren",a.lexerAny.has("kw_as")?{type:"kw_as"}:kw_as,"lparen","union_statement","rparen","with_statement_statement"],postprocess:e=>(0,n.track)(e,{type:"with recursive",alias:o(e[1]),columnNames:e[2].map(o),bind:e[5],in:c(e[7])})},{name:"with_statement_bindings$ebnf$1",symbols:[]},{name:"with_statement_bindings$ebnf$1$subexpression$1",symbols:["comma","with_statement_binding"],postprocess:m},{name:"with_statement_bindings$ebnf$1",symbols:["with_statement_bindings$ebnf$1","with_statement_bindings$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"with_statement_bindings",symbols:["with_statement_binding","with_statement_bindings$ebnf$1"],postprocess:([e,t])=>[e,...t||[]]},{name:"with_statement_binding",symbols:["word",a.lexerAny.has("kw_as")?{type:"kw_as"}:kw_as,"lparen","with_statement_statement","rparen"],postprocess:e=>(0,n.track)(e,{alias:o(e[0]),statement:c(e[3])})},{name:"with_statement_statement",symbols:["selection"]},{name:"with_statement_statement",symbols:["insert_statement"]},{name:"with_statement_statement",symbols:["update_statement"]},{name:"with_statement_statement",symbols:["delete_statement"]},{name:"createtype_statement$subexpression$1",symbols:["createtype_enum"]},{name:"createtype_statement$subexpression$1",symbols:["createtype_composite"]},{name:"createtype_statement",symbols:[a.lexerAny.has("kw_create")?{type:"kw_create"}:kw_create,"kw_type","qualified_name","createtype_statement$subexpression$1"],postprocess:e=>(0,n.track)(e,{name:e[2],...c(e[3])})},{name:"createtype_enum$macrocall$2",symbols:["enum_value"]},{name:"createtype_enum$macrocall$1$ebnf$1",symbols:[]},{name:"createtype_enum$macrocall$1$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("comma")?{type:"comma"}:comma,"createtype_enum$macrocall$2"],postprocess:m},{name:"createtype_enum$macrocall$1$ebnf$1",symbols:["createtype_enum$macrocall$1$ebnf$1","createtype_enum$macrocall$1$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"createtype_enum$macrocall$1",symbols:["createtype_enum$macrocall$2","createtype_enum$macrocall$1$ebnf$1"],postprocess:([e,t])=>[c(e),...t.map(c)||[]]},{name:"createtype_enum",symbols:[a.lexerAny.has("kw_as")?{type:"kw_as"}:kw_as,"kw_enum","lparen","createtype_enum$macrocall$1","rparen"],postprocess:e=>(0,n.track)(e,{type:"create enum",values:e[3]})},{name:"enum_value",symbols:["string"],postprocess:e=>(0,n.track)(e,{value:d(e)})},{name:"createtype_composite$macrocall$2",symbols:["createtype_composite_attr"]},{name:"createtype_composite$macrocall$1$ebnf$1",symbols:[]},{name:"createtype_composite$macrocall$1$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("comma")?{type:"comma"}:comma,"createtype_composite$macrocall$2"],postprocess:m},{name:"createtype_composite$macrocall$1$ebnf$1",symbols:["createtype_composite$macrocall$1$ebnf$1","createtype_composite$macrocall$1$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"createtype_composite$macrocall$1",symbols:["createtype_composite$macrocall$2","createtype_composite$macrocall$1$ebnf$1"],postprocess:([e,t])=>[c(e),...t.map(c)||[]]},{name:"createtype_composite",symbols:[a.lexerAny.has("kw_as")?{type:"kw_as"}:kw_as,"lparen","createtype_composite$macrocall$1","rparen"],postprocess:e=>(0,n.track)(e,{type:"create composite type",attributes:e[2]})},{name:"createtype_composite_attr$ebnf$1",symbols:["createtable_collate"],postprocess:r},{name:"createtype_composite_attr$ebnf$1",symbols:[],postprocess:()=>null},{name:"createtype_composite_attr",symbols:["word","data_type","createtype_composite_attr$ebnf$1"],postprocess:e=>(0,n.track)(e,{name:o(e[0]),dataType:e[1],...e[2]?{collate:e[2][1]}:{}})},{name:"altertype_statement$subexpression$1",symbols:["altertype_enum_add_value"]},{name:"altertype_statement$subexpression$1",symbols:["altertype_enum_rename"]},{name:"altertype_statement",symbols:["kw_alter","kw_type","qualified_name","altertype_statement$subexpression$1"],postprocess:e=>(0,n.track)(e,{name:e[2],...c(e[3])})},{name:"altertype_enum_add_value",symbols:["kw_add","kw_value","enum_additional_value"],postprocess:e=>(0,n.track)(e,{type:"alter enum",change:{type:"add value",add:e[2]}})},{name:"enum_additional_value",symbols:["string"],postprocess:e=>(0,n.track)(e,{value:d(e)})},{name:"altertype_enum_rename",symbols:["kw_rename",a.lexerAny.has("kw_to")?{type:"kw_to"}:kw_to,"word"],postprocess:e=>(0,n.track)(e,{type:"alter enum",change:{type:"rename",to:o(m(e))}})},{name:"union_left",symbols:["select_statement"]},{name:"union_left",symbols:["select_values"]},{name:"union_left",symbols:["selection_paren"]},{name:"union_right",symbols:["selection"]},{name:"union_right",symbols:["selection_paren"]},{name:"union_statement$subexpression$1$ebnf$1",symbols:[a.lexerAny.has("kw_all")?{type:"kw_all"}:kw_all],postprocess:r},{name:"union_statement$subexpression$1$ebnf$1",symbols:[],postprocess:()=>null},{name:"union_statement$subexpression$1",symbols:[a.lexerAny.has("kw_union")?{type:"kw_union"}:kw_union,"union_statement$subexpression$1$ebnf$1"]},{name:"union_statement",symbols:["union_left","union_statement$subexpression$1","union_right"],postprocess:e=>(0,n.track)(e,{type:d(e[1]," "),left:c(e[0]),right:c(e[2])})},{name:"prepare$ebnf$1$subexpression$1",symbols:["lparen","data_type_list","rparen"],postprocess:p(1)},{name:"prepare$ebnf$1",symbols:["prepare$ebnf$1$subexpression$1"],postprocess:r},{name:"prepare$ebnf$1",symbols:[],postprocess:()=>null},{name:"prepare",symbols:["kw_prepare","ident","prepare$ebnf$1",a.lexerAny.has("kw_as")?{type:"kw_as"}:kw_as,"statement_noprep"],postprocess:e=>(0,n.track)(e,{type:"prepare",name:o(e[1]),...e[2]&&{args:e[2]},statement:c(m(e))})},{name:"deallocate$ebnf$1",symbols:["kw_prepare"],postprocess:r},{name:"deallocate$ebnf$1",symbols:[],postprocess:()=>null},{name:"deallocate",symbols:["kw_deallocate","deallocate$ebnf$1","deallocate_target"],postprocess:e=>(0,n.track)(e,{type:"deallocate",target:e[2]})},{name:"deallocate_target",symbols:["deallocate_all"],postprocess:c},{name:"deallocate_target",symbols:["deallocate_name"],postprocess:c},{name:"deallocate_name",symbols:["ident"],postprocess:e=>(0,n.track)(e,o(e[0]))},{name:"deallocate_all",symbols:[a.lexerAny.has("kw_all")?{type:"kw_all"}:kw_all],postprocess:e=>(0,n.track)(e,{option:"all"})},{name:"create_view_statements",symbols:["create_view"]},{name:"create_view_statements",symbols:["create_materialized_view"]},{name:"create_view$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("kw_or")?{type:"kw_or"}:kw_or,"kw_replace"]},{name:"create_view$ebnf$1",symbols:["create_view$ebnf$1$subexpression$1"],postprocess:r},{name:"create_view$ebnf$1",symbols:[],postprocess:()=>null},{name:"create_view$ebnf$2$subexpression$1",symbols:["kw_temp"]},{name:"create_view$ebnf$2$subexpression$1",symbols:["kw_temporary"]},{name:"create_view$ebnf$2",symbols:["create_view$ebnf$2$subexpression$1"],postprocess:r},{name:"create_view$ebnf$2",symbols:[],postprocess:()=>null},{name:"create_view$ebnf$3",symbols:["kw_recursive"],postprocess:r},{name:"create_view$ebnf$3",symbols:[],postprocess:()=>null},{name:"create_view$ebnf$4$subexpression$1$macrocall$2",symbols:["ident"]},{name:"create_view$ebnf$4$subexpression$1$macrocall$1$ebnf$1",symbols:[]},{name:"create_view$ebnf$4$subexpression$1$macrocall$1$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("comma")?{type:"comma"}:comma,"create_view$ebnf$4$subexpression$1$macrocall$2"],postprocess:m},{name:"create_view$ebnf$4$subexpression$1$macrocall$1$ebnf$1",symbols:["create_view$ebnf$4$subexpression$1$macrocall$1$ebnf$1","create_view$ebnf$4$subexpression$1$macrocall$1$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"create_view$ebnf$4$subexpression$1$macrocall$1",symbols:["create_view$ebnf$4$subexpression$1$macrocall$2","create_view$ebnf$4$subexpression$1$macrocall$1$ebnf$1"],postprocess:([e,t])=>[c(e),...t.map(c)||[]]},{name:"create_view$ebnf$4$subexpression$1",symbols:["lparen","create_view$ebnf$4$subexpression$1$macrocall$1","rparen"],postprocess:p(1)},{name:"create_view$ebnf$4",symbols:["create_view$ebnf$4$subexpression$1"],postprocess:r},{name:"create_view$ebnf$4",symbols:[],postprocess:()=>null},{name:"create_view$ebnf$5",symbols:["create_view_opts"],postprocess:r},{name:"create_view$ebnf$5",symbols:[],postprocess:()=>null},{name:"create_view$ebnf$6$subexpression$1$subexpression$1",symbols:["kw_local"]},{name:"create_view$ebnf$6$subexpression$1$subexpression$1",symbols:["kw_cascaded"]},{name:"create_view$ebnf$6$subexpression$1",symbols:[a.lexerAny.has("kw_with")?{type:"kw_with"}:kw_with,"create_view$ebnf$6$subexpression$1$subexpression$1",a.lexerAny.has("kw_check")?{type:"kw_check"}:kw_check,"kw_option"],postprocess:p(1)},{name:"create_view$ebnf$6",symbols:["create_view$ebnf$6$subexpression$1"],postprocess:r},{name:"create_view$ebnf$6",symbols:[],postprocess:()=>null},{name:"create_view",symbols:[a.lexerAny.has("kw_create")?{type:"kw_create"}:kw_create,"create_view$ebnf$1","create_view$ebnf$2","create_view$ebnf$3","kw_view","qualified_name","create_view$ebnf$4","create_view$ebnf$5",a.lexerAny.has("kw_as")?{type:"kw_as"}:kw_as,"selection","create_view$ebnf$6"],postprocess:e=>(0,n.track)(e,{type:"create view",...e[1]&&{orReplace:!0},...e[2]&&{temp:!0},...e[3]&&{recursive:!0},name:e[5],...e[6]&&{columnNames:e[6].map(o)},...e[7]&&{parameters:_(e[7])},query:e[9],...e[10]&&{checkOption:d(e[10])}})},{name:"create_view_opt",symbols:["ident",a.lexerAny.has("op_eq")?{type:"op_eq"}:op_eq,"ident"],postprocess:([e,t,s])=>[d(e),d(s)]},{name:"create_view_opts$macrocall$2",symbols:["create_view_opt"]},{name:"create_view_opts$macrocall$1$ebnf$1",symbols:[]},{name:"create_view_opts$macrocall$1$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("comma")?{type:"comma"}:comma,"create_view_opts$macrocall$2"],postprocess:m},{name:"create_view_opts$macrocall$1$ebnf$1",symbols:["create_view_opts$macrocall$1$ebnf$1","create_view_opts$macrocall$1$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"create_view_opts$macrocall$1",symbols:["create_view_opts$macrocall$2","create_view_opts$macrocall$1$ebnf$1"],postprocess:([e,t])=>[c(e),...t.map(c)||[]]},{name:"create_view_opts",symbols:[a.lexerAny.has("kw_with")?{type:"kw_with"}:kw_with,"create_view_opts$macrocall$1"],postprocess:m},{name:"create_materialized_view$ebnf$1",symbols:["kw_ifnotexists"],postprocess:r},{name:"create_materialized_view$ebnf$1",symbols:[],postprocess:()=>null},{name:"create_materialized_view$ebnf$2$subexpression$1$macrocall$2",symbols:["ident"]},{name:"create_materialized_view$ebnf$2$subexpression$1$macrocall$1$ebnf$1",symbols:[]},{name:"create_materialized_view$ebnf$2$subexpression$1$macrocall$1$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("comma")?{type:"comma"}:comma,"create_materialized_view$ebnf$2$subexpression$1$macrocall$2"],postprocess:m},{name:"create_materialized_view$ebnf$2$subexpression$1$macrocall$1$ebnf$1",symbols:["create_materialized_view$ebnf$2$subexpression$1$macrocall$1$ebnf$1","create_materialized_view$ebnf$2$subexpression$1$macrocall$1$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"create_materialized_view$ebnf$2$subexpression$1$macrocall$1",symbols:["create_materialized_view$ebnf$2$subexpression$1$macrocall$2","create_materialized_view$ebnf$2$subexpression$1$macrocall$1$ebnf$1"],postprocess:([e,t])=>[c(e),...t.map(c)||[]]},{name:"create_materialized_view$ebnf$2$subexpression$1",symbols:["lparen","create_materialized_view$ebnf$2$subexpression$1$macrocall$1","rparen"],postprocess:p(1)},{name:"create_materialized_view$ebnf$2",symbols:["create_materialized_view$ebnf$2$subexpression$1"],postprocess:r},{name:"create_materialized_view$ebnf$2",symbols:[],postprocess:()=>null},{name:"create_materialized_view$ebnf$3",symbols:["create_view_opts"],postprocess:r},{name:"create_materialized_view$ebnf$3",symbols:[],postprocess:()=>null},{name:"create_materialized_view$ebnf$4$subexpression$1",symbols:["kw_tablespace","ident"],postprocess:m},{name:"create_materialized_view$ebnf$4",symbols:["create_materialized_view$ebnf$4$subexpression$1"],postprocess:r},{name:"create_materialized_view$ebnf$4",symbols:[],postprocess:()=>null},{name:"create_materialized_view$ebnf$5$subexpression$1$ebnf$1",symbols:["kw_no"],postprocess:r},{name:"create_materialized_view$ebnf$5$subexpression$1$ebnf$1",symbols:[],postprocess:()=>null},{name:"create_materialized_view$ebnf$5$subexpression$1",symbols:[a.lexerAny.has("kw_with")?{type:"kw_with"}:kw_with,"create_materialized_view$ebnf$5$subexpression$1$ebnf$1","kw_data"]},{name:"create_materialized_view$ebnf$5",symbols:["create_materialized_view$ebnf$5$subexpression$1"],postprocess:r},{name:"create_materialized_view$ebnf$5",symbols:[],postprocess:()=>null},{name:"create_materialized_view",symbols:[a.lexerAny.has("kw_create")?{type:"kw_create"}:kw_create,"kw_materialized","kw_view","create_materialized_view$ebnf$1","qualified_name","create_materialized_view$ebnf$2","create_materialized_view$ebnf$3","create_materialized_view$ebnf$4",a.lexerAny.has("kw_as")?{type:"kw_as"}:kw_as,"selection","create_materialized_view$ebnf$5"],postprocess:e=>(0,n.track)(e,{type:"create materialized view",...e[3]&&{ifNotExists:!0},name:e[4],...e[5]&&{columnNames:e[6].map(o)},...e[6]&&{parameters:_(e[6])},...e[7]&&{tablespace:o(e[7])},query:e[9],...e[10]&&{withData:"no"!==d(e[10][1])}})},{name:"refresh_view_statements$ebnf$1",symbols:[a.lexerAny.has("kw_concurrently")?{type:"kw_concurrently"}:kw_concurrently],postprocess:r},{name:"refresh_view_statements$ebnf$1",symbols:[],postprocess:()=>null},{name:"refresh_view_statements$ebnf$2$subexpression$1$ebnf$1",symbols:["kw_no"],postprocess:r},{name:"refresh_view_statements$ebnf$2$subexpression$1$ebnf$1",symbols:[],postprocess:()=>null},{name:"refresh_view_statements$ebnf$2$subexpression$1",symbols:[a.lexerAny.has("kw_with")?{type:"kw_with"}:kw_with,"refresh_view_statements$ebnf$2$subexpression$1$ebnf$1","kw_data"]},{name:"refresh_view_statements$ebnf$2",symbols:["refresh_view_statements$ebnf$2$subexpression$1"],postprocess:r},{name:"refresh_view_statements$ebnf$2",symbols:[],postprocess:()=>null},{name:"refresh_view_statements",symbols:["kw_refresh","kw_materialized","kw_view","refresh_view_statements$ebnf$1","qname","refresh_view_statements$ebnf$2"],postprocess:e=>(0,n.track)(e,{type:"refresh materialized view",...e[3]?{concurrently:!0}:{},name:e[4],...e[5]?{withData:"no"!==d(e[5][1])}:{}})},{name:"functions_statements",symbols:["create_func"]},{name:"functions_statements",symbols:["do_stm"]},{name:"functions_statements",symbols:["drop_func"]},{name:"create_func$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("kw_or")?{type:"kw_or"}:kw_or,"kw_replace"]},{name:"create_func$ebnf$1",symbols:["create_func$ebnf$1$subexpression$1"],postprocess:r},{name:"create_func$ebnf$1",symbols:[],postprocess:()=>null},{name:"create_func$subexpression$1$ebnf$1$macrocall$2",symbols:["func_argdef"]},{name:"create_func$subexpression$1$ebnf$1$macrocall$1$ebnf$1",symbols:[]},{name:"create_func$subexpression$1$ebnf$1$macrocall$1$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("comma")?{type:"comma"}:comma,"create_func$subexpression$1$ebnf$1$macrocall$2"],postprocess:m},{name:"create_func$subexpression$1$ebnf$1$macrocall$1$ebnf$1",symbols:["create_func$subexpression$1$ebnf$1$macrocall$1$ebnf$1","create_func$subexpression$1$ebnf$1$macrocall$1$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"create_func$subexpression$1$ebnf$1$macrocall$1",symbols:["create_func$subexpression$1$ebnf$1$macrocall$2","create_func$subexpression$1$ebnf$1$macrocall$1$ebnf$1"],postprocess:([e,t])=>[c(e),...t.map(c)||[]]},{name:"create_func$subexpression$1$ebnf$1",symbols:["create_func$subexpression$1$ebnf$1$macrocall$1"],postprocess:r},{name:"create_func$subexpression$1$ebnf$1",symbols:[],postprocess:()=>null},{name:"create_func$subexpression$1",symbols:["lparen","create_func$subexpression$1$ebnf$1","rparen"],postprocess:p(1)},{name:"create_func$ebnf$2",symbols:["func_spec"]},{name:"create_func$ebnf$2",symbols:["create_func$ebnf$2","func_spec"],postprocess:e=>e[0].concat([e[1]])},{name:"create_func",symbols:[a.lexerAny.has("kw_create")?{type:"kw_create"}:kw_create,"create_func$ebnf$1","kw_function","qname","create_func$subexpression$1","create_func$ebnf$2"],postprocess:(e,t)=>{var s;const r={};for(const t of e[5]){for(const e in t)if("_"!==e[0]&&e in r)throw new Error("conflicting or redundant options "+e);Object.assign(r,t)}return(0,n.track)(e,{type:"create function",...e[1]&&{orReplace:!0},name:e[3],arguments:null!==(s=e[4])&&void 0!==s?s:[],...r})}},{name:"func_argdef$ebnf$1",symbols:["func_argopts"],postprocess:r},{name:"func_argdef$ebnf$1",symbols:[],postprocess:()=>null},{name:"func_argdef$ebnf$2",symbols:["func_argdefault"],postprocess:r},{name:"func_argdef$ebnf$2",symbols:[],postprocess:()=>null},{name:"func_argdef",symbols:["func_argdef$ebnf$1","data_type","func_argdef$ebnf$2"],postprocess:e=>(0,n.track)(e,{default:e[2],type:e[1],...e[0]})},{name:"func_argdefault",symbols:[a.lexerAny.has("kw_default")?{type:"kw_default"}:kw_default,"expr"],postprocess:e=>e[1]},{name:"func_argdefault",symbols:[a.lexerAny.has("op_eq")?{type:"op_eq"}:op_eq,"expr"],postprocess:e=>e[1]},{name:"func_argopts$ebnf$1",symbols:["word"],postprocess:r},{name:"func_argopts$ebnf$1",symbols:[],postprocess:()=>null},{name:"func_argopts",symbols:["func_argmod","func_argopts$ebnf$1"],postprocess:e=>(0,n.track)(e,{mode:d(e[0]),...e[1]&&{name:o(e[1])}})},{name:"func_argopts",symbols:["word"],postprocess:(e,t)=>{const s=o(e);return"out"===s||"inout"===s||"variadic"===s?t:(0,n.track)(e,{name:s})}},{name:"func_argmod",symbols:[a.lexerAny.has("kw_in")?{type:"kw_in"}:kw_in]},{name:"func_argmod",symbols:["kw_out"]},{name:"func_argmod",symbols:["kw_inout"]},{name:"func_argmod",symbols:["kw_variadic"]},{name:"func_spec",symbols:["kw_language","word"],postprocess:e=>(0,n.track)(e,{language:o(m(e))})},{name:"func_spec",symbols:["func_purity"],postprocess:e=>(0,n.track)(e,{purity:d(e)})},{name:"func_spec$subexpression$1",symbols:[a.lexerAny.has("codeblock")?{type:"codeblock"}:codeblock]},{name:"func_spec$subexpression$1",symbols:["string"]},{name:"func_spec",symbols:[a.lexerAny.has("kw_as")?{type:"kw_as"}:kw_as,"func_spec$subexpression$1"],postprocess:e=>({code:d(m(e))})},{name:"func_spec$ebnf$1",symbols:[a.lexerAny.has("kw_not")?{type:"kw_not"}:kw_not],postprocess:r},{name:"func_spec$ebnf$1",symbols:[],postprocess:()=>null},{name:"func_spec$subexpression$2",symbols:["word"],postprocess:f("leakproof")},{name:"func_spec",symbols:["func_spec$ebnf$1","func_spec$subexpression$2"],postprocess:e=>(0,n.track)(e,{leakproof:!e[0]})},{name:"func_spec",symbols:["func_returns"],postprocess:e=>(0,n.track)(e,{returns:c(e)})},{name:"func_spec$subexpression$3",symbols:["word"],postprocess:f("called")},{name:"func_spec",symbols:["func_spec$subexpression$3","oninp"],postprocess:()=>({onNullInput:"call"})},{name:"func_spec$subexpression$4",symbols:["word"],postprocess:f("returns")},{name:"func_spec",symbols:["func_spec$subexpression$4",a.lexerAny.has("kw_null")?{type:"kw_null"}:kw_null,"oninp"],postprocess:()=>({onNullInput:"null"})},{name:"func_spec$subexpression$5",symbols:["word"],postprocess:f("strict")},{name:"func_spec",symbols:["func_spec$subexpression$5"],postprocess:()=>({onNullInput:"strict"})},{name:"func_purity",symbols:["word"],postprocess:f("immutable")},{name:"func_purity",symbols:["word"],postprocess:f("stable")},{name:"func_purity",symbols:["word"],postprocess:f("volatile")},{name:"oninp$subexpression$1",symbols:["word"],postprocess:f("input")},{name:"oninp",symbols:[a.lexerAny.has("kw_on")?{type:"kw_on"}:kw_on,a.lexerAny.has("kw_null")?{type:"kw_null"}:kw_null,"oninp$subexpression$1"]},{name:"func_returns",symbols:["kw_returns","data_type"],postprocess:m},{name:"func_returns$macrocall$2",symbols:["func_ret_table_col"]},{name:"func_returns$macrocall$1$ebnf$1",symbols:[]},{name:"func_returns$macrocall$1$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("comma")?{type:"comma"}:comma,"func_returns$macrocall$2"],postprocess:m},{name:"func_returns$macrocall$1$ebnf$1",symbols:["func_returns$macrocall$1$ebnf$1","func_returns$macrocall$1$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"func_returns$macrocall$1",symbols:["func_returns$macrocall$2","func_returns$macrocall$1$ebnf$1"],postprocess:([e,t])=>[c(e),...t.map(c)||[]]},{name:"func_returns",symbols:["kw_returns",a.lexerAny.has("kw_table")?{type:"kw_table"}:kw_table,"lparen","func_returns$macrocall$1","rparen"],postprocess:e=>(0,n.track)(e,{kind:"table",columns:e[3]})},{name:"func_ret_table_col",symbols:["word","data_type"],postprocess:e=>(0,n.track)(e,{name:o(e[0]),type:e[1]})},{name:"do_stm$ebnf$1$subexpression$1",symbols:["kw_language","word"],postprocess:m},{name:"do_stm$ebnf$1",symbols:["do_stm$ebnf$1$subexpression$1"],postprocess:r},{name:"do_stm$ebnf$1",symbols:[],postprocess:()=>null},{name:"do_stm",symbols:[a.lexerAny.has("kw_do")?{type:"kw_do"}:kw_do,"do_stm$ebnf$1",a.lexerAny.has("codeblock")?{type:"codeblock"}:codeblock],postprocess:e=>(0,n.track)(e,{type:"do",...e[1]&&{language:o(e[1])},code:e[2].value})},{name:"drop_func$ebnf$1$subexpression$1",symbols:["kw_if","kw_exists"]},{name:"drop_func$ebnf$1",symbols:["drop_func$ebnf$1$subexpression$1"],postprocess:r},{name:"drop_func$ebnf$1",symbols:[],postprocess:()=>null},{name:"drop_func$ebnf$2",symbols:["drop_func_overload"],postprocess:r},{name:"drop_func$ebnf$2",symbols:[],postprocess:()=>null},{name:"drop_func",symbols:["kw_drop","kw_function","drop_func$ebnf$1","qname","drop_func$ebnf$2"],postprocess:e=>(0,n.track)(e,{type:"drop function",...e[2]&&{ifExists:!0},name:e[3],...e[4]&&{arguments:e[4]}})},{name:"drop_func_overload$macrocall$2",symbols:["drop_func_overload_col"]},{name:"drop_func_overload$macrocall$1$ebnf$1",symbols:[]},{name:"drop_func_overload$macrocall$1$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("comma")?{type:"comma"}:comma,"drop_func_overload$macrocall$2"],postprocess:m},{name:"drop_func_overload$macrocall$1$ebnf$1",symbols:["drop_func_overload$macrocall$1$ebnf$1","drop_func_overload$macrocall$1$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"drop_func_overload$macrocall$1",symbols:["drop_func_overload$macrocall$2","drop_func_overload$macrocall$1$ebnf$1"],postprocess:([e,t])=>[c(e),...t.map(c)||[]]},{name:"drop_func_overload",symbols:["lparen","drop_func_overload$macrocall$1","rparen"],postprocess:p(1)},{name:"drop_func_overload_col$ebnf$1",symbols:["word"],postprocess:r},{name:"drop_func_overload_col$ebnf$1",symbols:[],postprocess:()=>null},{name:"drop_func_overload_col",symbols:["drop_func_overload_col$ebnf$1","qname"],postprocess:e=>(0,n.track)(e,{type:e[1],...e[0]&&{name:o(e[0])}})},{name:"main$ebnf$1",symbols:[]},{name:"main$ebnf$1",symbols:["main$ebnf$1","statement_separator"],postprocess:e=>e[0].concat([e[1]])},{name:"main$ebnf$2",symbols:[]},{name:"main$ebnf$2$subexpression$1$ebnf$1",symbols:["statement_separator"]},{name:"main$ebnf$2$subexpression$1$ebnf$1",symbols:["main$ebnf$2$subexpression$1$ebnf$1","statement_separator"],postprocess:e=>e[0].concat([e[1]])},{name:"main$ebnf$2$subexpression$1",symbols:["main$ebnf$2$subexpression$1$ebnf$1","statement"]},{name:"main$ebnf$2",symbols:["main$ebnf$2","main$ebnf$2$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"main$ebnf$3",symbols:[]},{name:"main$ebnf$3",symbols:["main$ebnf$3","statement_separator"],postprocess:e=>e[0].concat([e[1]])},{name:"main",symbols:["main$ebnf$1","statement","main$ebnf$2","main$ebnf$3"],postprocess:([e,t,s])=>{const r=s,a=[c(t),...r.map((e=>c(e[1])))];return 1===a.length?a[0]:a}},{name:"statement_separator",symbols:[a.lexerAny.has("semicolon")?{type:"semicolon"}:semicolon]},{name:"statement",symbols:["statement_noprep"]},{name:"statement",symbols:["prepare"]},{name:"statement",symbols:["deallocate"]},{name:"statement_noprep",symbols:["selection"]},{name:"statement_noprep",symbols:["createtable_statement"]},{name:"statement_noprep",symbols:["createextension_statement"]},{name:"statement_noprep",symbols:["createindex_statement"]},{name:"statement_noprep",symbols:["simplestatements_all"]},{name:"statement_noprep",symbols:["insert_statement"]},{name:"statement_noprep",symbols:["update_statement"]},{name:"statement_noprep",symbols:["altertable_statement"]},{name:"statement_noprep",symbols:["alterindex_statement"]},{name:"statement_noprep",symbols:["delete_statement"]},{name:"statement_noprep",symbols:["create_sequence_statement"]},{name:"statement_noprep",symbols:["alter_sequence_statement"]},{name:"statement_noprep",symbols:["drop_statement"]},{name:"statement_noprep",symbols:["createtype_statement"]},{name:"statement_noprep",symbols:["altertype_statement"]},{name:"statement_noprep",symbols:["create_view_statements"]},{name:"statement_noprep",symbols:["refresh_view_statements"]},{name:"statement_noprep",symbols:["create_schema"]},{name:"statement_noprep",symbols:["raise_statement"]},{name:"statement_noprep",symbols:["comment_statement"]},{name:"statement_noprep",symbols:["functions_statements"]},{name:"selection",symbols:["select_statement"],postprocess:c},{name:"selection",symbols:["select_values"],postprocess:c},{name:"selection",symbols:["with_statement"],postprocess:c},{name:"selection",symbols:["with_recursive_statement"],postprocess:c},{name:"selection",symbols:["union_statement"],postprocess:c},{name:"selection_paren",symbols:["lparen","selection","rparen"],postprocess:p(1)}],ParserStart:"main"};t.default=g},function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=s(12),a={Lexer:r.lexerAny,ParserRules:[{name:"main$ebnf$1",symbols:["elements"],postprocess:function(e){return e[0]}},{name:"main$ebnf$1",symbols:[],postprocess:()=>null},{name:"main",symbols:[r.lexerAny.has("start_list")?{type:"start_list"}:start_list,"main$ebnf$1",r.lexerAny.has("end_list")?{type:"end_list"}:end_list],postprocess:e=>e[1]||[]},{name:"elements$ebnf$1",symbols:[]},{name:"elements$ebnf$1$subexpression$1",symbols:[r.lexerAny.has("comma")?{type:"comma"}:comma,"elt"],postprocess:e=>e&&e[e.length-1]},{name:"elements$ebnf$1",symbols:["elements$ebnf$1","elements$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"elements",symbols:["elt","elements$ebnf$1"],postprocess:([e,t])=>[e,...t||[]]},{name:"elt",symbols:[r.lexerAny.has("value")?{type:"value"}:value],postprocess:e=>e[0].value},{name:"elt",symbols:["main"],postprocess:e=>e[0]}],ParserStart:"main"};t.default=a},function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lexerAny=t.lexer=void 0;const r=s(0);var a;t.lexer=(0,r.compile)({valueString:{match:/"(?:\\["\\]|[^\n"\\])*"/,value:e=>JSON.parse(e),type:e=>"value"},valueRaw:{match:/[^\s,\{\}"](?:[^,\{\}"]*[^\s,\{\}"])?/,type:()=>"value"},comma:",",space:{match:/[\s\t\n\v\f\r]+/,lineBreaks:!0},start_list:"{",end_list:"}"}),t.lexer.next=(a=t.lexer.next,()=>{let e;for(;(e=a.call(t.lexer))&&"space"===e.type;);return e}),t.lexerAny=t.lexer},function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0});const r=s(14),a=e=>t=>t[e],n=e=>e&&e[e.length-1];function o(e){return Array.isArray(e)&&1===e.length&&(e=o(e[0])),Array.isArray(e)&&!e.length?null:e}const l={Lexer:r.lexerAny,ParserRules:[{name:"number$subexpression$1",symbols:["float"]},{name:"number$subexpression$1",symbols:["int"]},{name:"number",symbols:["number$subexpression$1"],postprocess:o},{name:"float",symbols:[r.lexerAny.has("float")?{type:"float"}:float],postprocess:e=>parseFloat(o(e))},{name:"int",symbols:[r.lexerAny.has("int")?{type:"int"}:int],postprocess:e=>parseInt(o(e),10)},{name:"comma",symbols:[r.lexerAny.has("comma")?{type:"comma"}:comma],postprocess:function(e){return e[0]}},{name:"point$macrocall$2",symbols:["point_content"]},{name:"point$macrocall$1$subexpression$1",symbols:["point$macrocall$2"]},{name:"point$macrocall$1$subexpression$1",symbols:[r.lexerAny.has("lparen")?{type:"lparen"}:lparen,"point$macrocall$2",r.lexerAny.has("rparen")?{type:"rparen"}:rparen],postprocess:a(1)},{name:"point$macrocall$1",symbols:["point$macrocall$1$subexpression$1"],postprocess:o},{name:"point",symbols:["point$macrocall$1"],postprocess:o},{name:"point_content",symbols:["number","comma","number"],postprocess:e=>({x:e[0],y:e[2]})},{name:"line",symbols:[r.lexerAny.has("lcurl")?{type:"lcurl"}:lcurl,"number","comma","number","comma","number",r.lexerAny.has("rcurl")?{type:"rcurl"}:rcurl],postprocess:e=>({a:e[1],b:e[3],c:e[5]})},{name:"box",symbols:["closed_path"],postprocess:([e],t)=>2!==e.length?t:e},{name:"lseg",symbols:["path"],postprocess:([e],t)=>2!==e.path.length?t:e.path},{name:"path",symbols:["open_path"],postprocess:([e])=>({closed:!1,path:e})},{name:"path",symbols:["closed_path"],postprocess:([e])=>({closed:!0,path:e})},{name:"open_path$macrocall$2",symbols:[r.lexerAny.has("lbracket")?{type:"lbracket"}:lbracket]},{name:"open_path$macrocall$3",symbols:[r.lexerAny.has("rbracket")?{type:"rbracket"}:rbracket]},{name:"open_path$macrocall$1$macrocall$2",symbols:["point"]},{name:"open_path$macrocall$1$macrocall$1$ebnf$1",symbols:[]},{name:"open_path$macrocall$1$macrocall$1$ebnf$1$subexpression$1",symbols:[r.lexerAny.has("comma")?{type:"comma"}:comma,"open_path$macrocall$1$macrocall$2"],postprocess:n},{name:"open_path$macrocall$1$macrocall$1$ebnf$1",symbols:["open_path$macrocall$1$macrocall$1$ebnf$1","open_path$macrocall$1$macrocall$1$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"open_path$macrocall$1$macrocall$1",symbols:["open_path$macrocall$1$macrocall$2","open_path$macrocall$1$macrocall$1$ebnf$1"],postprocess:([e,t])=>[o(e),...t.map(o)||[]]},{name:"open_path$macrocall$1",symbols:["open_path$macrocall$2","open_path$macrocall$1$macrocall$1","open_path$macrocall$3"],postprocess:a(1)},{name:"open_path",symbols:["open_path$macrocall$1"],postprocess:n},{name:"closed_path$subexpression$1$macrocall$2",symbols:[r.lexerAny.has("lparen")?{type:"lparen"}:lparen]},{name:"closed_path$subexpression$1$macrocall$3",symbols:[r.lexerAny.has("rparen")?{type:"rparen"}:rparen]},{name:"closed_path$subexpression$1$macrocall$1$macrocall$2",symbols:["point"]},{name:"closed_path$subexpression$1$macrocall$1$macrocall$1$ebnf$1",symbols:[]},{name:"closed_path$subexpression$1$macrocall$1$macrocall$1$ebnf$1$subexpression$1",symbols:[r.lexerAny.has("comma")?{type:"comma"}:comma,"closed_path$subexpression$1$macrocall$1$macrocall$2"],postprocess:n},{name:"closed_path$subexpression$1$macrocall$1$macrocall$1$ebnf$1",symbols:["closed_path$subexpression$1$macrocall$1$macrocall$1$ebnf$1","closed_path$subexpression$1$macrocall$1$macrocall$1$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"closed_path$subexpression$1$macrocall$1$macrocall$1",symbols:["closed_path$subexpression$1$macrocall$1$macrocall$2","closed_path$subexpression$1$macrocall$1$macrocall$1$ebnf$1"],postprocess:([e,t])=>[o(e),...t.map(o)||[]]},{name:"closed_path$subexpression$1$macrocall$1",symbols:["closed_path$subexpression$1$macrocall$2","closed_path$subexpression$1$macrocall$1$macrocall$1","closed_path$subexpression$1$macrocall$3"],postprocess:a(1)},{name:"closed_path$subexpression$1",symbols:["closed_path$subexpression$1$macrocall$1"],postprocess:n},{name:"closed_path$subexpression$1$macrocall$5",symbols:["point"]},{name:"closed_path$subexpression$1$macrocall$4$ebnf$1",symbols:[]},{name:"closed_path$subexpression$1$macrocall$4$ebnf$1$subexpression$1",symbols:[r.lexerAny.has("comma")?{type:"comma"}:comma,"closed_path$subexpression$1$macrocall$5"],postprocess:n},{name:"closed_path$subexpression$1$macrocall$4$ebnf$1",symbols:["closed_path$subexpression$1$macrocall$4$ebnf$1","closed_path$subexpression$1$macrocall$4$ebnf$1$subexpression$1"],postprocess:e=>e[0].concat([e[1]])},{name:"closed_path$subexpression$1$macrocall$4",symbols:["closed_path$subexpression$1$macrocall$5","closed_path$subexpression$1$macrocall$4$ebnf$1"],postprocess:([e,t])=>[o(e),...t.map(o)||[]]},{name:"closed_path$subexpression$1",symbols:["closed_path$subexpression$1$macrocall$4"],postprocess:n},{name:"closed_path",symbols:["closed_path$subexpression$1"],postprocess:a(0)},{name:"polygon",symbols:["closed_path"],postprocess:a(0)},{name:"circle_body",symbols:["point","comma","number"],postprocess:e=>({c:e[0],r:e[2]})},{name:"circle$subexpression$1$macrocall$2",symbols:[r.lexerAny.has("lcomp")?{type:"lcomp"}:lcomp]},{name:"circle$subexpression$1$macrocall$3",symbols:[r.lexerAny.has("rcomp")?{type:"rcomp"}:rcomp]},{name:"circle$subexpression$1$macrocall$1",symbols:["circle$subexpression$1$macrocall$2","circle_body","circle$subexpression$1$macrocall$3"],postprocess:a(1)},{name:"circle$subexpression$1",symbols:["circle$subexpression$1$macrocall$1"]},{name:"circle$subexpression$1$macrocall$5",symbols:[r.lexerAny.has("lparen")?{type:"lparen"}:lparen]},{name:"circle$subexpression$1$macrocall$6",symbols:[r.lexerAny.has("rparen")?{type:"rparen"}:rparen]},{name:"circle$subexpression$1$macrocall$4",symbols:["circle$subexpression$1$macrocall$5","circle_body","circle$subexpression$1$macrocall$6"],postprocess:a(1)},{name:"circle$subexpression$1",symbols:["circle$subexpression$1$macrocall$4"]},{name:"circle$subexpression$1",symbols:["circle_body"]},{name:"circle",symbols:["circle$subexpression$1"],postprocess:o}],ParserStart:"number"};t.default=l},function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lexerAny=t.lexer=void 0;const r=s(0);var a;t.lexer=(0,r.compile)({comma:",",space:{match:/[\s\t\n\v\f\r]+/,lineBreaks:!0},int:/\-?\d+(?![\.\d])/,float:/\-?(?:(?:\d*\.\d+)|(?:\d+\.\d*))/,lcurl:"{",rcurl:"}",lparen:"(",rparen:")",lbracket:"[",rbracket:"]",lcomp:"<",rcomp:">"}),t.lexer.next=(a=t.lexer.next,()=>{let e;for(;(e=a.call(t.lexer))&&"space"===e.type;);return e}),t.lexerAny=t.lexer},function(e,t,s){"use strict";function r(e){return e[0]}Object.defineProperty(t,"__esModule",{value:!0});const a=s(16),n={Lexer:a.lexerAny,ParserRules:[{name:"main$ebnf$1",symbols:["elt"]},{name:"main$ebnf$1",symbols:["main$ebnf$1","elt"],postprocess:e=>e[0].concat([e[1]])},{name:"main",symbols:["main$ebnf$1"],postprocess:([e])=>{const t=new Set;for(const s of e){const e="number"==typeof s[1]?s[0]:"time";if(t.has(e))return"invalid";t.add(e)}return e}},{name:"elt",symbols:["time"]},{name:"elt",symbols:["num","unit"],postprocess:([[e],t])=>[t=t[0].type,e]},{name:"unit",symbols:[a.lexerAny.has("years")?{type:"years"}:years]},{name:"unit",symbols:[a.lexerAny.has("months")?{type:"months"}:months]},{name:"unit",symbols:[a.lexerAny.has("days")?{type:"days"}:days]},{name:"unit",symbols:[a.lexerAny.has("hours")?{type:"hours"}:hours]},{name:"unit",symbols:[a.lexerAny.has("minutes")?{type:"minutes"}:minutes]},{name:"unit",symbols:[a.lexerAny.has("seconds")?{type:"seconds"}:seconds]},{name:"unit",symbols:[a.lexerAny.has("milliseconds")?{type:"milliseconds"}:milliseconds]},{name:"num",symbols:["int"]},{name:"num",symbols:["float"]},{name:"uint",symbols:[a.lexerAny.has("int")?{type:"int"}:int],postprocess:([e])=>parseInt(e,10)},{name:"int$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("neg")?{type:"neg"}:neg]},{name:"int$ebnf$1",symbols:["int$ebnf$1$subexpression$1"],postprocess:r},{name:"int$ebnf$1",symbols:[],postprocess:()=>null},{name:"int",symbols:["int$ebnf$1",a.lexerAny.has("int")?{type:"int"}:int],postprocess:([e,t])=>parseInt(t,10)*(e?-1:1)},{name:"float$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("neg")?{type:"neg"}:neg]},{name:"float$ebnf$1",symbols:["float$ebnf$1$subexpression$1"],postprocess:r},{name:"float$ebnf$1",symbols:[],postprocess:()=>null},{name:"float$ebnf$2",symbols:[a.lexerAny.has("int")?{type:"int"}:int],postprocess:r},{name:"float$ebnf$2",symbols:[],postprocess:()=>null},{name:"float",symbols:["float$ebnf$1","float$ebnf$2",a.lexerAny.has("dot")?{type:"dot"}:dot,a.lexerAny.has("int")?{type:"int"}:int],postprocess:([e,...t])=>parseFloat(t.map((e=>e?e.text:"0")).join(""))*(e?-1:1)},{name:"time$ebnf$1$subexpression$1",symbols:[a.lexerAny.has("colon")?{type:"colon"}:colon,"uint"]},{name:"time$ebnf$1",symbols:["time$ebnf$1$subexpression$1"],postprocess:r},{name:"time$ebnf$1",symbols:[],postprocess:()=>null},{name:"time$ebnf$2$subexpression$1",symbols:[a.lexerAny.has("dot")?{type:"dot"}:dot,a.lexerAny.has("int")?{type:"int"}:int]},{name:"time$ebnf$2",symbols:["time$ebnf$2$subexpression$1"],postprocess:r},{name:"time$ebnf$2",symbols:[],postprocess:()=>null},{name:"time",symbols:["uint",a.lexerAny.has("colon")?{type:"colon"}:colon,"uint","time$ebnf$1","time$ebnf$2"],postprocess:([e,t,s,r,a])=>{const n="number"==typeof(r=r&&r[1])?[["hours",e],["minutes",s],["seconds",r]]:[["minutes",e],["seconds",s]];return(a=a&&a[1])&&n.push(["milliseconds",1e3*parseFloat("0."+a)]),n}}],ParserStart:"main"};t.default=n},function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lexerAny=t.lexer=void 0;const r=s(0);var a;t.lexer=(0,r.compile)({int:/\d+/,neg:"-",dot:".",years:/(?:y|yrs?|years?)\b/,months:/(?:mon(?:th)?s?)\b/,days:/(?:d|days?)\b/,hours:/(?:h|hrs?|hours?)\b/,minutes:/(?:m|mins?|minutes?)\b/,seconds:/(?:s|secs?|seconds?)\b/,milliseconds:/(?:ms|milliseconds?)\b/,space:{match:/[\s\t\n\v\f\r]+/,lineBreaks:!0},colon:":"}),t.lexer.next=(a=t.lexer.next,()=>{let e;for(;(e=a.call(t.lexer))&&"space"===e.type;);return e}),t.lexerAny=t.lexer},function(e,t,s){"use strict";function r(e){return e[0]}Object.defineProperty(t,"__esModule",{value:!0});const a=s(18),n={Lexer:a.lexerAny,ParserRules:[{name:"num",symbols:[a.lexerAny.has("int")?{type:"int"}:int]},{name:"num",symbols:[a.lexerAny.has("float")?{type:"float"}:float]},{name:"main$ebnf$1",symbols:[]},{name:"main$ebnf$1",symbols:["main$ebnf$1","long"],postprocess:e=>e[0].concat([e[1]])},{name:"main$ebnf$2$subexpression$1$ebnf$1",symbols:["short"]},{name:"main$ebnf$2$subexpression$1$ebnf$1",symbols:["main$ebnf$2$subexpression$1$ebnf$1","short"],postprocess:e=>e[0].concat([e[1]])},{name:"main$ebnf$2$subexpression$1",symbols:[a.lexerAny.has("T")?{type:"T"}:T,"main$ebnf$2$subexpression$1$ebnf$1"]},{name:"main$ebnf$2",symbols:["main$ebnf$2$subexpression$1"],postprocess:r},{name:"main$ebnf$2",symbols:[],postprocess:()=>null},{name:"main",symbols:[a.lexerAny.has("P")?{type:"P"}:P,"main$ebnf$1","main$ebnf$2"],postprocess:([e,t,s],r)=>(s=s?s[1]:[],t.length||s.length?t.length?s.length?[...t,...s]:t:s:r)},{name:"long$subexpression$1",symbols:[a.lexerAny.has("Y")?{type:"Y"}:Y]},{name:"long$subexpression$1",symbols:[a.lexerAny.has("M")?{type:"M"}:M]},{name:"long$subexpression$1",symbols:[a.lexerAny.has("W")?{type:"W"}:W]},{name:"long$subexpression$1",symbols:[a.lexerAny.has("D")?{type:"D"}:D]},{name:"long",symbols:["num","long$subexpression$1"],postprocess:([e,t])=>{switch(e=parseFloat(e[0].value),t=t[0].type){case"Y":return["years",e];case"M":return["months",e];case"W":return["days",7*e];case"D":return["days",e];default:throw new Error("Unexpected unit "+t)}}},{name:"short$ebnf$1",symbols:[a.lexerAny.has("T")?{type:"T"}:T],postprocess:r},{name:"short$ebnf$1",symbols:[],postprocess:()=>null},{name:"short$subexpression$1",symbols:[a.lexerAny.has("H")?{type:"H"}:H]},{name:"short$subexpression$1",symbols:[a.lexerAny.has("M")?{type:"M"}:M]},{name:"short$subexpression$1",symbols:[a.lexerAny.has("S")?{type:"S"}:S]},{name:"short",symbols:["short$ebnf$1","num","short$subexpression$1"],postprocess:([e,t,s])=>{switch(t=parseFloat(t[0].value),s=s[0].type){case"H":return["hours",t];case"M":return["minutes",t];case"S":return["seconds",t];default:throw new Error("Unexpected unit "+s)}}}],ParserStart:"num"};t.default=n},function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.lexerAny=t.lexer=void 0;const r=s(0);t.lexer=(0,r.compile)({int:/\-?\d+(?![\.\d])/,float:/\-?(?:(?:\d*\.\d+)|(?:\d+\.\d*))/,P:"P",Y:"Y",M:"M",W:"W",D:"D",H:"H",S:"S",T:"T"}),t.lexerAny=t.lexer},function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.toSql=void 0;const r=s(2),a=s(5),n=s(6),o=s(20),l=s(3),i=new Set(l.sqlKeywords.map((e=>e.toLowerCase())));let c=[];function p(e){return m(e.name)}function m(e,t){if(!t){const t=e.toLowerCase();if(t===e&&!i.has(t)&&/^[a-z][a-z0-9_]*$/.test(t))return e}return'"'+e+'"'}function u(e,t,s){s&&c.push("(");let r=!0;for(const s of e)r||c.push(", "),r=!1,t(s);s&&c.push(")")}function b(e,t){switch(e.type){case"foreign key":c.push(" foreign key (",...e.localColumns.map(p).join(", "),")");case"reference":c.push(" REFERENCES "),t.tableRef(e.foreignTable),c.push("(",...e.foreignColumns.map(p).join(", "),") "),e.match&&c.push(" MATCH ",e.match.toUpperCase()),e.onDelete&&c.push(" ON DELETE ",e.onDelete),e.onUpdate&&c.push(" ON UPDATE ",e.onUpdate);break;case"primary key":case"unique":c.push(" ",e.type," "),"columns"in e&&c.push("(",...e.columns.map(p).join(", "),") ");break;case"check":c.push(" check "),t.expr(e.expr);break;case"not null":case"null":c.push(" ",e.type," ");break;case"default":c.push(" default "),t.expr(e.default);break;case"add generated":c.push(" GENERATED "),h(t,e);break;default:throw n.NotSupported.never(e)}c.push(" ")}function y(e,t){e.schema&&c.push(m(e.schema),"."),c.push(m(e.name,t)," ")}function d(e){y(e),e.alias&&c.push(" AS ",m(e.alias)," ")}function _(e,t){c.push(" ORDER BY "),u(t,(t=>{e.expr(t.by),t.order&&c.push(" ",t.order," "),t.nulls&&c.push(" NULLS ",t.nulls," ")}),!1)}function $(e){switch(e.type){case"default":c.push("DEFAULT ");break;case"identifier":c.push(e.name);break;case"list":let t=!0;for(const s of e.values)t||c.push(", "),t=!1,$(s);break;case"value":c.push("number"==typeof e.value?e.value.toString():(0,o.literal)(e.value));break;default:throw n.NotSupported.never(e)}}function h(e,t){t.always&&c.push(t.always.toUpperCase()," "),c.push("AS IDENTITY "),t.sequence&&(c.push("("),t.sequence.name&&(c.push("SEQUENCE NAME "),y(t.sequence.name),c.push(" ")),f(e,t.sequence),c.push(") "))}function f(e,t){t.as&&(c.push("AS "),e.dataType(t.as),c.push(" ")),"number"==typeof t.incrementBy&&c.push("INCREMENT BY ",t.incrementBy.toString()," "),"no minvalue"===t.minValue&&c.push("NO MINVALUE "),"number"==typeof t.minValue&&c.push("MINVALUE ",t.minValue.toString()," "),"no maxvalue"===t.maxValue&&c.push("NO MAXVALUE "),"number"==typeof t.maxValue&&c.push("MAXVALUE ",t.maxValue.toString()," "),"number"==typeof t.startWith&&c.push("START WITH ",t.startWith.toString()," "),"number"==typeof t.cache&&c.push("CACHE ",t.cache.toString()," "),t.cycle&&c.push(t.cycle," "),"none"===t.ownedBy?c.push("OWNED BY NONE "):t.ownedBy&&(c.push("OWNED BY "),w(t.ownedBy)),"restart"in t&&(!0===t.restart?c.push("RESTART "):t.restart&&c.push("RESTART WITH ",t.restart.toString()," "))}function w(e){e.schema&&c.push(m(e.schema),"."),c.push(m(e.table),".",m(e.column)," ")}function x(e,t,s){t?(c.push(t.type," "),s(),t.on&&(c.push("ON "),e.expr(t.on)),t.using&&(c.push("USING ("),u(t.using,(e=>c.push(p(e))),!1),c.push(") ")),c.push(" ")):s()}function g(e){e.opSchema?c.push(" operator(",m(e.opSchema),".",e.op,") "):c.push(" ",e.op," ")}const v=(0,a.astVisitor)((e=>({addColumn:(...t)=>{c.push(" ADD COLUMN "),t[0].ifNotExists&&c.push("IF NOT EXISTS "),e.super().addColumn(...t)},createExtension:e=>{c.push("CREATE EXTENSION "),e.ifNotExists&&c.push(" IF NOT EXISTS "),c.push(p(e.extension)),(e.from||e.version||e.schema)&&(c.push(" WITH"),e.schema&&c.push(" SCHEMA ",p(e.schema)),e.version&&c.push(" VERSION ",(0,o.literal)(e.version.value)),e.from&&c.push(" FROM ",(0,o.literal)(e.from.value)))},tablespace:e=>{c.push("TABLESPACE ",p(e.tablespace))},addConstraint:t=>{c.push(" ADD ");const s=t.constraint.constraintName;s&&c.push(" CONSTRAINT ",p(s)," "),b(t.constraint,e)},alterColumn:(t,s)=>{c.push(" ALTER COLUMN ",p(t.column)," "),e.super().alterColumn(t,s)},setColumnDefault:(t,s,r)=>{if(c.push(" SET DEFAULT "),e.expr(t.default),t.updateExisting)throw new Error("Not implemented: updateExisting on set column default")},createEnum:e=>{c.push("CREATE TYPE "),y(e.name),c.push(" AS ENUM "),u(e.values,(e=>c.push((0,o.literal)(e.value))),!0),c.push(" ")},alterEnum:e=>{c.push("ALTER TYPE "),y(e.name),"rename"===e.change.type?(c.push(" RENAME TO "),y(e.change.to)):c.push(" ADD VALUE ",(0,o.literal)(e.change.add.value))},createCompositeType:t=>{c.push("CREATE TYPE "),y(t.name),c.push(" AS "),u(t.attributes,(t=>{c.push(p(t.name)," "),e.dataType(t.dataType),t.collate&&(c.push("COLLATE "),y(t.collate))}),!0),c.push(" ")},setTableOwner:e=>{c.push(" OWNER TO ",p(e.to))},alterColumnSimple:e=>c.push(e.type),alterColumnAddGenerated:t=>{c.push(" ADD GENERATED "),h(e,t)},setColumnType:t=>{c.push(" SET DATA TYPE "),e.dataType(t.dataType),c.push(" ")},alterTable:t=>{c.push("ALTER TABLE "),t.ifExists&&c.push(" IF EXISTS "),t.only&&c.push(" ONLY "),d(t.table),u(t.changes,(s=>e.tableAlteration(s,t.table)),!1)},alterIndex:e=>{switch(c.push("ALTER INDEX "),e.ifExists&&c.push(" IF EXISTS "),d(e.index),e.change.type){case"rename":c.push(" RENAME TO "),y(e.change.to),c.push(" ");break;case"set tablespace":c.push(" SET TABLESPACE "),y(e.change.tablespace),c.push(" ");break;default:throw n.NotSupported.never(e.change,"Alter index type not supported: ")}},tableAlteration:(t,s)=>{switch(t.type){case"add column":return e.addColumn(t,s);case"add constraint":return e.addConstraint(t,s);case"alter column":return e.alterColumn(t,s);case"rename":return e.renameTable(t,s);case"rename column":return e.renameColumn(t,s);case"rename constraint":return e.renameConstraint(t,s);case"drop column":return e.dropColumn(t,s);case"drop constraint":return e.dropConstraint(t,s);case"owner":return e.setTableOwner(t,s);default:throw n.NotSupported.never(t)}},array:t=>{c.push("array"===t.type?"ARRAY[":"("),u(t.expressions,(t=>e.expr(t)),!1),c.push("array"===t.type?"]":")")},arrayIndex:t=>{e.expr(t.array),c.push("["),e.expr(t.index),c.push("] ")},expr:t=>{"ref"!==t.type?"list"!==t.type?(c.push("("),e.super().expr(t),c.push(")")):e.super().expr(t):e.ref(t)},callOverlay:t=>{c.push("OVERLAY("),e.expr(t.value),c.push(" PLACING "),e.expr(t.placing),c.push(" FROM "),e.expr(t.from),t.for&&(c.push(" FOR "),e.expr(t.for)),c.push(")")},callSubstring:t=>{c.push("SUBSTRING("),e.expr(t.value),t.from&&(c.push(" FROM "),e.expr(t.from)),t.for&&(c.push(" FOR "),e.expr(t.for)),c.push(")")},binary:t=>{e.expr(t.left),g(t),e.expr(t.right)},call:t=>{y(t.function),c.push("("),t.distinct&&c.push(t.distinct," "),u(t.args,(t=>e.expr(t)),!1),t.orderBy&&_(e,t.orderBy),c.push(") "),t.filter&&(c.push("filter (where "),e.expr(t.filter),c.push(") ")),t.withinGroup&&(c.push("WITHIN GROUP ("),_(e,[t.withinGroup]),c.push(") ")),t.over&&(c.push("over ("),t.over.partitionBy&&(c.push("PARTITION BY "),u(t.over.partitionBy,(t=>e.expr(t)),!1),c.push(" ")),t.over.orderBy&&(_(e,t.over.orderBy),c.push(" ")),c.push(") "))},case:t=>{c.push("CASE "),t.value&&e.expr(t.value);for(const s of t.whens)c.push(" WHEN "),e.expr(s.when),c.push(" THEN "),e.expr(s.value);t.else&&(c.push(" ELSE "),e.expr(t.else)),c.push(" END ")},cast:t=>{e.expr(t.operand),c.push("::"),e.dataType(t.to)},constant:e=>{switch(e.type){case"boolean":c.push(e.value?"true":"false");break;case"integer":c.push(e.value.toString(10));break;case"numeric":c.push(e.value.toString()),Number.isInteger(e.value)&&c.push(".");break;case"null":c.push("null");break;case"constant":break;case"string":c.push((0,o.literal)(e.value));break;default:throw n.NotSupported.never(e)}},valueKeyword:e=>{c.push(e.keyword," ")},comment:e=>{if(c.push("COMMENT ON ",e.on.type.toUpperCase()," "),"column"===e.on.type)w(e.on.column);else y(e.on.name);c.push(" IS ",(0,o.literal)(e.comment)," ")},extract:t=>{c.push("EXTRACT (",t.field.name.toUpperCase()," FROM "),e.expr(t.from),c.push(") ")},createColumn:t=>{var s;c.push(p(t.name)," "),e.dataType(t.dataType),c.push(" "),t.collate&&(c.push("COLLATE "),y(t.collate));for(const r of null!==(s=t.constraints)&&void 0!==s?s:[])e.constraint(r)},begin:e=>{c.push("BEGIN "),e.isolationLevel&&c.push("ISOLATION LEVEL ",e.isolationLevel.toUpperCase()," "),e.writeable&&c.push(e.writeable.toUpperCase()," "),"boolean"==typeof e.deferrable&&(e.deferrable||c.push("NOT "),c.push("DEFERRABLE "))},alterSequence:t=>{switch(c.push("ALTER SEQUENCE "),t.ifExists&&c.push("IF EXISTS "),y(t.name),t.change.type){case"set options":f(e,t.change);break;case"rename":c.push("RENAME TO ",p(t.change.newName)," ");break;case"set schema":c.push("SET SCHEMA ",p(t.change.newSchema)," ");break;case"owner to":t.change.owner;c.push("OWNER TO ",p(t.change.owner)," ");break;default:throw n.NotSupported.never(t.change)}},createSequence:t=>{c.push("CREATE "),t.temp&&c.push("TEMPORARY "),c.push("SEQUENCE "),t.ifNotExists&&c.push("IF NOT EXISTS "),y(t.name),f(e,t.options)},drop:t=>{c.push(t.type.toUpperCase()," "),t.concurrently&&c.push("CONCURRENTLY "),t.ifExists&&c.push("IF EXISTS "),u(t.names,(t=>e.tableRef(t)),!1),t.cascade&&c.push(t.cascade.toUpperCase()," ")},constraint:t=>{t.constraintName&&c.push(" CONSTRAINT ",p(t.constraintName)," "),b(t,e)},do:e=>{c.push("DO"),e.language&&c.push(" LANGUAGE ",e.language.name),c.push(" $$",e.code,"$$")},createFunction:t=>{var s;if(c.push(t.orReplace?"CREATE OR REPLACE FUNCTION ":"CREATE FUNCTION "),y(t.name),u(t.arguments,(t=>{t.mode&&c.push(t.mode," "),t.name&&c.push(p(t.name)," "),e.dataType(t.type),t.default&&(c.push(" = "),e.expr(t.default))}),!0),t.returns)switch(t.returns.kind){case"table":c.push(" RETURNS TABLE "),u(t.returns.columns,(t=>{c.push(p(t.name)," "),e.dataType(t.type)}),!0);break;case void 0:case null:case"array":c.push(" RETURNS "),e.dataType(t.returns);break;default:throw n.NotSupported.never(t.returns)}switch(c.push(" AS $$",null!==(s=t.code)&&void 0!==s?s:"","$$"),t.language&&c.push("LANGUAGE ",t.language.name," "),t.purity&&c.push(t.purity.toUpperCase()," "),"boolean"==typeof t.leakproof&&c.push(t.leakproof?"LEAKPROOF ":"NOT LEAKPROOF "),t.onNullInput){case"call":c.push("CALLED ON NULL INPUT ");break;case"null":c.push("RETURNS NULL ON NULL INPUT ");break;case"strict":c.push("STRICT ");break;case null:case void 0:break;default:throw n.NotSupported.never(t.onNullInput)}},dropFunction:t=>{c.push("DROP FUNCTION "),t.ifExists&&c.push("IF EXISTS "),y(t.name),t.arguments&&u(t.arguments,(t=>{t.name&&(y(t.name),c.push(" ")),e.dataType(t.type)}),!0),c.push(" ")},with:t=>{c.push("WITH "),u(t.bind,(t=>{c.push(p(t.alias)," AS ("),e.statement(t.statement),c.push(") ")}),!1),e.statement(t.in)},withRecursive:t=>{c.push("WITH RECURSIVE ",p(t.alias),"(",...t.columnNames.map(p).join(", "),") AS ("),e.union(t.bind),c.push(") "),e.statement(t.in)},setGlobal:e=>{c.push("SET "),e.scope&&c.push(e.scope.toUpperCase()+" "),c.push(p(e.variable)," = "),$(e.set)},setTimezone:e=>{switch(c.push("SET TIME ZONE "),e.to.type){case"default":case"local":c.push(e.to.type.toUpperCase()," ");break;case"value":c.push("string"==typeof e.to.value?(0,o.literal)(e.to.value):e.to.value.toString(10));break;case"interval":c.push("INTERVAL ",(0,o.literal)(e.to.value)," HOUR TO MINUTE");break;default:throw n.NotSupported.never(e.to)}},setNames:e=>{if(c.push("SET NAMES "),"value"===e.to.type)c.push((0,o.literal)(e.to.value))},dataType:t=>{var s,r;if("array"===(null==t?void 0:t.kind))return e.dataType(t.arrayOf),void c.push("[]");if(!(null==t?void 0:t.name))return void c.push("unkown");let a=!0;if(t.schema)y(t,t.doubleQuoted);else if(t.doubleQuoted)y(t,!0);else switch(t.name){case"double precision":case"character varying":case"bit varying":c.push(t.name," ");break;case"time without time zone":case"timestamp without time zone":case"time with time zone":case"timestamp with time zone":const e=t.name.split(" ");c.push(e.shift()),(null===(s=t.config)||void 0===s?void 0:s.length)&&u(t.config,(e=>c.push(e.toString(10))),!0),c.push(" "),c.push(e.join(" ")," "),a=!1;break;default:y(t)}a&&(null===(r=t.config)||void 0===r?void 0:r.length)&&u(t.config,(e=>c.push(e.toString(10))),!0)},createIndex:t=>{c.push(t.unique?"CREATE UNIQUE INDEX ":"CREATE INDEX "),t.concurrently&&c.push("CONCURRENTLY "),t.ifNotExists&&c.push(" IF NOT EXISTS "),t.indexName&&c.push(p(t.indexName)," "),c.push("ON "),e.tableRef(t.table),t.using&&c.push("USING ",p(t.using)," "),u(t.expressions,(t=>{e.expr(t.expression),c.push(" "),t.collate&&(c.push("COLLATE "),y(t.collate)),t.opclass&&y(t.opclass),t.order&&c.push(t.order," "),t.nulls&&c.push("nulls ",t.nulls," ")}),!0),t.with&&(c.push("WITH "),u(t.with,(e=>{c.push(e.parameter," = ",(0,o.literal)(e.value))}),!0)),t.tablespace&&c.push("TABLESPACE ",m(t.tablespace)),t.where&&(c.push(" WHERE "),e.expr(t.where)),c.push(" ")},createTable:t=>{var s;c.push("CREATE "),t.locality&&c.push(t.locality.toUpperCase()," "),t.temporary&&c.push("TEMPORARY "),t.unlogged&&c.push("UNLOGGED "),c.push(t.ifNotExists?"TABLE IF NOT EXISTS ":"TABLE "),e.tableRef(t.name),c.push("("),u(t.columns,(t=>{switch(t.kind){case"column":return e.createColumn(t);case"like table":return e.likeTable(t);default:throw n.NotSupported.never(t)}}),!1),t.constraints&&(c.push(", "),u(t.constraints,(t=>{const s=t.constraintName;s&&c.push("CONSTRAINT ",p(s)," "),b(t,e)}),!1)),c.push(") "),(null===(s=t.inherits)||void 0===s?void 0:s.length)&&(c.push(" INHERITS "),u(t.inherits,(e=>y(e)),!0))},likeTable:t=>{c.push(" LIKE "),e.tableRef(t.like),c.push(" ");for(const{verb:e,option:s}of t.options)c.push(e.toUpperCase()," ",s.toUpperCase()," ")},createSchema:e=>{c.push(e.ifNotExists?"CREATE SCHEMA IF NOT EXISTS ":"CREATE SCHEMA "),c.push(p(e.name))},truncateTable:t=>{c.push("TRUNCATE TABLE ");let s=!0;for(const r of t.tables)s||c.push(", "),s=!1,e.tableRef(r);if(t.identity)switch(t.identity){case"restart":c.push(" RESTART IDENTITY ");break;case"continue":c.push(" CONTINUE IDENTITY ")}t.cascade&&c.push(" ",t.cascade," ")},delete:t=>{c.push("DELETE FROM "),e.tableRef(t.from),t.where&&(c.push(" WHERE "),e.expr(t.where)),t.returning&&(c.push(" RETURNING "),u(t.returning,(t=>e.selectionColumn(t)),!1)),c.push(" ")},dropColumn:e=>{c.push(" DROP COLUMN "),e.ifExists&&c.push(" IF EXISTS "),c.push(p(e.column)),e.behaviour&&c.push(" ",e.behaviour),c.push(" ")},dropConstraint:e=>{c.push(" DROP CONSTRAINT "),e.ifExists&&c.push(" IF EXISTS "),c.push(p(e.constraint)),e.behaviour&&c.push(" ",e.behaviour.toUpperCase()," ")},from:t=>e.super().from(t),fromCall:t=>{x(e,t.join,(()=>{var s,r;if(t.lateral&&c.push("LATERAL "),e.call(t),t.withOrdinality&&c.push(" WITH ORDINALITY"),t.alias){c.push(" AS ",p(t.alias)," ");const e=null!==(r=null===(s=t.alias.columns)||void 0===s?void 0:s.length)&&void 0!==r?r:0;if(e>0){c.push("(");for(let s=0;s<e;++s)0!==s&&c.push(", "),c.push(p(t.alias.columns[s]));c.push(")")}}})),c.push(" ")},fromStatement:t=>{x(e,t.join,(()=>{t.lateral&&c.push("LATERAL "),c.push("("),e.select(t.statement),c.push(") "),t.alias&&(c.push(" AS ",m(t.alias)),t.columnNames&&u(t.columnNames,(e=>c.push(p(e))),!0),c.push(" "))})),c.push(" ")},values:t=>{c.push("VALUES "),u(t.values,(t=>{u(t,(t=>{e.expr(t)}),!0)}),!1)},fromTable:t=>{x(e,t.join,(()=>{if(e.tableRef(t.name),t.name.columnNames){if(!t.name.alias)throw new Error("Cannot specify aliased column names without an alias");u(t.name.columnNames,(e=>c.push(p(e))),!0)}}))},join:e=>{throw new Error("Should not happen 💀")},insert:t=>{if(c.push("INSERT INTO "),e.tableRef(t.into),t.columns&&c.push("(",t.columns.map(p).join(", "),")"),c.push(" "),t.overriding&&c.push("OVERRIDING ",t.overriding.toUpperCase()," VALUE "),e.select(t.insert),c.push(" "),t.onConflict){c.push("ON CONFLICT ");const s=t.onConflict.on;switch(null==s?void 0:s.type){case"on expr":u(s.exprs,(t=>e.expr(t)),!0);break;case"on constraint":c.push("ON CONSTRAINT "),y(s.constraint);case null:case void 0:break;default:throw n.NotSupported.never(s)}"do nothing"===t.onConflict.do?c.push(" DO NOTHING"):(c.push(" DO UPDATE SET "),u(t.onConflict.do.sets,(t=>e.set(t)),!1),t.onConflict.where&&(c.push(" WHERE "),e.expr(t.onConflict.where))),c.push(" ")}t.returning&&(c.push(" RETURNING "),u(t.returning,(t=>e.selectionColumn(t)),!1))},raise:t=>{var s,r;c.push("RAISE "),t.level&&c.push(t.level.toUpperCase()," "),c.push((0,o.literal)(t.format)," "),(null===(s=t.formatExprs)||void 0===s?void 0:s.length)&&(c.push(", "),u(t.formatExprs,(t=>e.expr(t)),!1)),(null===(r=t.using)||void 0===r?void 0:r.length)&&(c.push(" USING "),u(t.using,(({type:t,value:s})=>{c.push(t.toUpperCase(),"="),e.expr(s)}),!1)),c.push(" ")},default:()=>{c.push(" DEFAULT ")},member:t=>{e.expr(t.operand),c.push(t.op),c.push("number"==typeof t.member?t.member.toString(10):(0,o.literal)(t.member))},ref:e=>{e.table&&(y(e.table),c.push(".")),c.push("*"===e.name?"*":m(e.name))},parameter:e=>{c.push(e.name)},renameColumn:e=>{c.push(" RENAME COLUMN ",p(e.column)," TO ",p(e.to))},renameConstraint:e=>{c.push(" RENAME CONSTRAINT ",p(e.constraint)," TO ",p(e.to))},renameTable:e=>{c.push(" RENAME TO ",p(e.to))},createView:t=>{c.push("CREATE "),t.orReplace&&c.push("OR REPLACE "),t.temp&&c.push("TEMP "),t.recursive&&c.push("RECURSIVE "),c.push("VIEW "),e.tableRef(t.name),t.columnNames&&u(t.columnNames,(e=>c.push(p(e))),!0);const s=t.parameters&&Object.entries(t.parameters);(null==s?void 0:s.length)&&(c.push(" WITH "),u(s,(([e,t])=>c.push(e,"=",t)),!1)),c.push(" AS "),e.select(t.query),t.checkOption&&c.push(" WITH ",t.checkOption.toUpperCase()," CHECK OPTION")},createMaterializedView:t=>{c.push("CREATE MATERIALIZED VIEW "),t.ifNotExists&&c.push("IF NOT EXISTS "),e.tableRef(t.name),t.columnNames&&u(t.columnNames,(e=>c.push(p(e))),!0);const s=t.parameters&&Object.entries(t.parameters);(null==s?void 0:s.length)&&(c.push(" WITH "),u(s,(([e,t])=>c.push(e,"=",t)),!1)),t.tablespace&&c.push(" TABLESPACE ",p(t.tablespace)),c.push(" AS "),e.select(t.query),"boolean"==typeof t.withData&&c.push(t.withData?" WITH DATA":" WITH NO DATA")},refreshMaterializedView:t=>{c.push("REFRESH MATERIALIZED VIEW "),t.concurrently&&c.push("CONCURRENTLY "),e.tableRef(t.name),"boolean"==typeof t.withData&&c.push(t.withData?" WITH DATA":" WITH NO DATA")},select:t=>e.super().select(t),selection:t=>{if(c.push("SELECT "),t.distinct&&("string"==typeof t.distinct?c.push(t.distinct.toUpperCase()):(c.push(" DISTINCT ON "),u(t.distinct,(t=>e.expr(t)),!0)),c.push(" ")),t.columns&&u(t.columns,(t=>e.selectionColumn(t)),!1),c.push(" "),t.from){c.push("FROM ");const s=t.from.length;for(let r=0;r<s;r++){const s=t.from[r];r>0&&!s.join&&c.push(","),e.from(s)}c.push(" ")}t.where&&(c.push("WHERE "),e.expr(t.where),c.push(" ")),t.groupBy&&(c.push("GROUP BY "),u(t.groupBy,(t=>e.expr(t)),!1),c.push(" "),t.having&&(c.push(" HAVING "),e.expr(t.having),c.push(" "))),t.orderBy&&(_(e,t.orderBy),c.push(" ")),t.limit&&(t.limit.offset&&(c.push("OFFSET "),e.expr(t.limit.offset)),t.limit.limit&&(c.push("LIMIT "),e.expr(t.limit.limit))),t.for&&(c.push("FOR ",t.for.type.toUpperCase()),t.skip&&c.push(" ",t.skip.type.toUpperCase()))},show:e=>{c.push("SHOW ",p(e.variable))},prepare:t=>{var s;c.push("PREPARE ",p(t.name)),(null===(s=t.args)||void 0===s?void 0:s.length)&&u(t.args,(t=>e.dataType(t)),!0),c.push(" AS "),e.statement(t.statement)},deallocate:e=>{c.push("DEALLOCATE "),"name"in e.target?c.push(e.target.name):c.push("ALL")},arraySelect:t=>{c.push("array("),e.select(t.select),c.push(")")},union:t=>{c.push("("),e.statement(t.left),c.push(") ",t.type.toUpperCase()," "),"union"===t.right.type||"union all"===t.right.type?e.union(t.right):(c.push("("),e.statement(t.right),c.push(")"))},selectionColumn:t=>{e.expr(t.expr),t.alias&&c.push(" AS ",p(t.alias)),c.push(" ")},set:t=>{c.push(p(t.column)," = "),e.expr(t.value),c.push(" ")},statement:t=>e.super().statement(t),tableRef:e=>{y(e),e.alias&&c.push(" AS ",m(e.alias)),c.push(" ")},ternary:t=>{e.expr(t.value),c.push(" ",t.op," "),e.expr(t.lo),c.push(" AND "),e.expr(t.hi),c.push(" ")},transaction:e=>{c.push(e.type)},unary:t=>{switch(t.op){case"+":case"-":g(t),e.expr(t.operand);break;case"NOT":c.push(t.op),c.push(" "),e.expr(t.operand);break;default:e.expr(t.operand),c.push(" "),c.push(t.op)}},update:t=>{c.push("UPDATE "),e.tableRef(t.table),c.push(" SET "),u(t.sets,(t=>e.set(t)),!1),c.push(" "),t.from&&(c.push("FROM "),e.from(t.from),c.push(" ")),t.where&&(c.push("WHERE "),e.expr(t.where),c.push(" ")),t.returning&&(c.push(" RETURNING "),u(t.returning,(t=>e.selectionColumn(t)),!1),c.push(" "))}})));t.toSql={};const k=r.AstDefaultMapper.prototype;for(const e of Object.getOwnPropertyNames(k)){const s=k[e];"constructor"!==e&&"super"!==e&&"function"==typeof s&&(t.toSql[e]=function(...t){try{return v[e].apply(v,t),c.join("").trim()}finally{c=[]}})}},function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.literal=void 0,t.literal=function e(t){return null==t?"NULL":Array.isArray(t)?"("+t.map(e).join(", ")+")":(~t.indexOf("\\")?"E":"")+"'"+(t=(t=t.replace(/'/g,"''")).replace(/\\/g,"\\\\"))+"'"}},function(e,t,s){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.locationOf=void 0,t.locationOf=function(e){const t=e._location;if(!t)throw new Error("This statement has not been parsed using location tracking (which has a small performance hit). ");return t}}]))},9524:(e,t,s)=>{const r=Symbol("SemVer ANY");class a{static get ANY(){return r}constructor(e,t){if(t=n(t),e instanceof a){if(e.loose===!!t.loose)return e;e=e.value}e=e.trim().split(/\s+/).join(" "),c("comparator",e,t),this.options=t,this.loose=!!t.loose,this.parse(e),this.semver===r?this.value="":this.value=this.operator+this.semver.version,c("comp",this)}parse(e){const t=this.options.loose?o[l.COMPARATORLOOSE]:o[l.COMPARATOR],s=e.match(t);if(!s)throw new TypeError(`Invalid comparator: ${e}`);this.operator=void 0!==s[1]?s[1]:"","="===this.operator&&(this.operator=""),s[2]?this.semver=new p(s[2],this.options.loose):this.semver=r}toString(){return this.value}test(e){if(c("Comparator.test",e,this.options.loose),this.semver===r||e===r)return!0;if("string"==typeof e)try{e=new p(e,this.options)}catch(e){return!1}return i(e,this.operator,this.semver,this.options)}intersects(e,t){if(!(e instanceof a))throw new TypeError("a Comparator is required");return""===this.operator?""===this.value||new m(e.value,t).test(this.value):""===e.operator?""===e.value||new m(this.value,t).test(e.semver):(!(t=n(t)).includePrerelease||"<0.0.0-0"!==this.value&&"<0.0.0-0"!==e.value)&&(!(!t.includePrerelease&&(this.value.startsWith("<0.0.0")||e.value.startsWith("<0.0.0")))&&(!(!this.operator.startsWith(">")||!e.operator.startsWith(">"))||(!(!this.operator.startsWith("<")||!e.operator.startsWith("<"))||(!(this.semver.version!==e.semver.version||!this.operator.includes("=")||!e.operator.includes("="))||(!!(i(this.semver,"<",e.semver,t)&&this.operator.startsWith(">")&&e.operator.startsWith("<"))||!!(i(this.semver,">",e.semver,t)&&this.operator.startsWith("<")&&e.operator.startsWith(">")))))))}}e.exports=a;const n=s(7095),{safeRe:o,t:l}=s(6850),i=s(3811),c=s(9796),p=s(7944),m=s(8395)},8395:(e,t,s)=>{const r=/\s+/g;class a{constructor(e,t){if(t=o(t),e instanceof a)return e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease?e:new a(e.raw,t);if(e instanceof l)return this.raw=e.value,this.set=[[e]],this.formatted=void 0,this;if(this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease,this.raw=e.trim().replace(r," "),this.set=this.raw.split("||").map((e=>this.parseRange(e.trim()))).filter((e=>e.length)),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){const e=this.set[0];if(this.set=this.set.filter((e=>!$(e[0]))),0===this.set.length)this.set=[e];else if(this.set.length>1)for(const e of this.set)if(1===e.length&&h(e[0])){this.set=[e];break}}this.formatted=void 0}get range(){if(void 0===this.formatted){this.formatted="";for(let e=0;e<this.set.length;e++){e>0&&(this.formatted+="||");const t=this.set[e];for(let e=0;e<t.length;e++)e>0&&(this.formatted+=" "),this.formatted+=t[e].toString().trim()}}return this.formatted}format(){return this.range}toString(){return this.range}parseRange(e){const t=((this.options.includePrerelease&&d)|(this.options.loose&&_))+":"+e,s=n.get(t);if(s)return s;const r=this.options.loose,a=r?p[m.HYPHENRANGELOOSE]:p[m.HYPHENRANGE];e=e.replace(a,S(this.options.includePrerelease)),i("hyphen replace",e),e=e.replace(p[m.COMPARATORTRIM],u),i("comparator trim",e),e=e.replace(p[m.TILDETRIM],b),i("tilde trim",e),e=e.replace(p[m.CARETTRIM],y),i("caret trim",e);let o=e.split(" ").map((e=>w(e,this.options))).join(" ").split(/\s+/).map((e=>A(e,this.options)));r&&(o=o.filter((e=>(i("loose invalid filter",e,this.options),!!e.match(p[m.COMPARATORLOOSE]))))),i("range list",o);const c=new Map,h=o.map((e=>new l(e,this.options)));for(const e of h){if($(e))return[e];c.set(e.value,e)}c.size>1&&c.has("")&&c.delete("");const f=[...c.values()];return n.set(t,f),f}intersects(e,t){if(!(e instanceof a))throw new TypeError("a Range is required");return this.set.some((s=>f(s,t)&&e.set.some((e=>f(e,t)&&s.every((s=>e.every((e=>s.intersects(e,t)))))))))}test(e){if(!e)return!1;if("string"==typeof e)try{e=new c(e,this.options)}catch(e){return!1}for(let t=0;t<this.set.length;t++)if(I(this.set[t],e,this.options))return!0;return!1}}e.exports=a;const n=new(s(1158)),o=s(7095),l=s(9524),i=s(9796),c=s(7944),{safeRe:p,t:m,comparatorTrimReplace:u,tildeTrimReplace:b,caretTrimReplace:y}=s(6850),{FLAG_INCLUDE_PRERELEASE:d,FLAG_LOOSE:_}=s(9630),$=e=>"<0.0.0-0"===e.value,h=e=>""===e.value,f=(e,t)=>{let s=!0;const r=e.slice();let a=r.pop();for(;s&&r.length;)s=r.every((e=>a.intersects(e,t))),a=r.pop();return s},w=(e,t)=>(i("comp",e,t),e=k(e,t),i("caret",e),e=g(e,t),i("tildes",e),e=T(e,t),i("xrange",e),e=C(e,t),i("stars",e),e),x=e=>!e||"x"===e.toLowerCase()||"*"===e,g=(e,t)=>e.trim().split(/\s+/).map((e=>v(e,t))).join(" "),v=(e,t)=>{const s=t.loose?p[m.TILDELOOSE]:p[m.TILDE];return e.replace(s,((t,s,r,a,n)=>{let o;return i("tilde",e,t,s,r,a,n),x(s)?o="":x(r)?o=`>=${s}.0.0 <${+s+1}.0.0-0`:x(a)?o=`>=${s}.${r}.0 <${s}.${+r+1}.0-0`:n?(i("replaceTilde pr",n),o=`>=${s}.${r}.${a}-${n} <${s}.${+r+1}.0-0`):o=`>=${s}.${r}.${a} <${s}.${+r+1}.0-0`,i("tilde return",o),o}))},k=(e,t)=>e.trim().split(/\s+/).map((e=>E(e,t))).join(" "),E=(e,t)=>{i("caret",e,t);const s=t.loose?p[m.CARETLOOSE]:p[m.CARET],r=t.includePrerelease?"-0":"";return e.replace(s,((t,s,a,n,o)=>{let l;return i("caret",e,t,s,a,n,o),x(s)?l="":x(a)?l=`>=${s}.0.0${r} <${+s+1}.0.0-0`:x(n)?l="0"===s?`>=${s}.${a}.0${r} <${s}.${+a+1}.0-0`:`>=${s}.${a}.0${r} <${+s+1}.0.0-0`:o?(i("replaceCaret pr",o),l="0"===s?"0"===a?`>=${s}.${a}.${n}-${o} <${s}.${a}.${+n+1}-0`:`>=${s}.${a}.${n}-${o} <${s}.${+a+1}.0-0`:`>=${s}.${a}.${n}-${o} <${+s+1}.0.0-0`):(i("no pr"),l="0"===s?"0"===a?`>=${s}.${a}.${n}${r} <${s}.${a}.${+n+1}-0`:`>=${s}.${a}.${n}${r} <${s}.${+a+1}.0-0`:`>=${s}.${a}.${n} <${+s+1}.0.0-0`),i("caret return",l),l}))},T=(e,t)=>(i("replaceXRanges",e,t),e.split(/\s+/).map((e=>O(e,t))).join(" ")),O=(e,t)=>{e=e.trim();const s=t.loose?p[m.XRANGELOOSE]:p[m.XRANGE];return e.replace(s,((s,r,a,n,o,l)=>{i("xRange",e,s,r,a,n,o,l);const c=x(a),p=c||x(n),m=p||x(o),u=m;return"="===r&&u&&(r=""),l=t.includePrerelease?"-0":"",c?s=">"===r||"<"===r?"<0.0.0-0":"*":r&&u?(p&&(n=0),o=0,">"===r?(r=">=",p?(a=+a+1,n=0,o=0):(n=+n+1,o=0)):"<="===r&&(r="<",p?a=+a+1:n=+n+1),"<"===r&&(l="-0"),s=`${r+a}.${n}.${o}${l}`):p?s=`>=${a}.0.0${l} <${+a+1}.0.0-0`:m&&(s=`>=${a}.${n}.0${l} <${a}.${+n+1}.0-0`),i("xRange return",s),s}))},C=(e,t)=>(i("replaceStars",e,t),e.trim().replace(p[m.STAR],"")),A=(e,t)=>(i("replaceGTE0",e,t),e.trim().replace(p[t.includePrerelease?m.GTE0PRE:m.GTE0],"")),S=e=>(t,s,r,a,n,o,l,i,c,p,m,u)=>`${s=x(r)?"":x(a)?`>=${r}.0.0${e?"-0":""}`:x(n)?`>=${r}.${a}.0${e?"-0":""}`:o?`>=${s}`:`>=${s}${e?"-0":""}`} ${i=x(c)?"":x(p)?`<${+c+1}.0.0-0`:x(m)?`<${c}.${+p+1}.0-0`:u?`<=${c}.${p}.${m}-${u}`:e?`<${c}.${p}.${+m+1}-0`:`<=${i}`}`.trim(),I=(e,t,s)=>{for(let s=0;s<e.length;s++)if(!e[s].test(t))return!1;if(t.prerelease.length&&!s.includePrerelease){for(let s=0;s<e.length;s++)if(i(e[s].semver),e[s].semver!==l.ANY&&e[s].semver.prerelease.length>0){const r=e[s].semver;if(r.major===t.major&&r.minor===t.minor&&r.patch===t.patch)return!0}return!1}return!0}},7944:(e,t,s)=>{const r=s(9796),{MAX_LENGTH:a,MAX_SAFE_INTEGER:n}=s(9630),{safeRe:o,t:l}=s(6850),i=s(7095),{compareIdentifiers:c}=s(4031);class p{constructor(e,t){if(t=i(t),e instanceof p){if(e.loose===!!t.loose&&e.includePrerelease===!!t.includePrerelease)return e;e=e.version}else if("string"!=typeof e)throw new TypeError(`Invalid version. Must be a string. Got type "${typeof e}".`);if(e.length>a)throw new TypeError(`version is longer than ${a} characters`);r("SemVer",e,t),this.options=t,this.loose=!!t.loose,this.includePrerelease=!!t.includePrerelease;const s=e.trim().match(t.loose?o[l.LOOSE]:o[l.FULL]);if(!s)throw new TypeError(`Invalid Version: ${e}`);if(this.raw=e,this.major=+s[1],this.minor=+s[2],this.patch=+s[3],this.major>n||this.major<0)throw new TypeError("Invalid major version");if(this.minor>n||this.minor<0)throw new TypeError("Invalid minor version");if(this.patch>n||this.patch<0)throw new TypeError("Invalid patch version");s[4]?this.prerelease=s[4].split(".").map((e=>{if(/^[0-9]+$/.test(e)){const t=+e;if(t>=0&&t<n)return t}return e})):this.prerelease=[],this.build=s[5]?s[5].split("."):[],this.format()}format(){return this.version=`${this.major}.${this.minor}.${this.patch}`,this.prerelease.length&&(this.version+=`-${this.prerelease.join(".")}`),this.version}toString(){return this.version}compare(e){if(r("SemVer.compare",this.version,this.options,e),!(e instanceof p)){if("string"==typeof e&&e===this.version)return 0;e=new p(e,this.options)}return e.version===this.version?0:this.compareMain(e)||this.comparePre(e)}compareMain(e){return e instanceof p||(e=new p(e,this.options)),c(this.major,e.major)||c(this.minor,e.minor)||c(this.patch,e.patch)}comparePre(e){if(e instanceof p||(e=new p(e,this.options)),this.prerelease.length&&!e.prerelease.length)return-1;if(!this.prerelease.length&&e.prerelease.length)return 1;if(!this.prerelease.length&&!e.prerelease.length)return 0;let t=0;do{const s=this.prerelease[t],a=e.prerelease[t];if(r("prerelease compare",t,s,a),void 0===s&&void 0===a)return 0;if(void 0===a)return 1;if(void 0===s)return-1;if(s!==a)return c(s,a)}while(++t)}compareBuild(e){e instanceof p||(e=new p(e,this.options));let t=0;do{const s=this.build[t],a=e.build[t];if(r("build compare",t,s,a),void 0===s&&void 0===a)return 0;if(void 0===a)return 1;if(void 0===s)return-1;if(s!==a)return c(s,a)}while(++t)}inc(e,t,s){switch(e){case"premajor":this.prerelease.length=0,this.patch=0,this.minor=0,this.major++,this.inc("pre",t,s);break;case"preminor":this.prerelease.length=0,this.patch=0,this.minor++,this.inc("pre",t,s);break;case"prepatch":this.prerelease.length=0,this.inc("patch",t,s),this.inc("pre",t,s);break;case"prerelease":0===this.prerelease.length&&this.inc("patch",t,s),this.inc("pre",t,s);break;case"major":0===this.minor&&0===this.patch&&0!==this.prerelease.length||this.major++,this.minor=0,this.patch=0,this.prerelease=[];break;case"minor":0===this.patch&&0!==this.prerelease.length||this.minor++,this.patch=0,this.prerelease=[];break;case"patch":0===this.prerelease.length&&this.patch++,this.prerelease=[];break;case"pre":{const e=Number(s)?1:0;if(!t&&!1===s)throw new Error("invalid increment argument: identifier is empty");if(0===this.prerelease.length)this.prerelease=[e];else{let r=this.prerelease.length;for(;--r>=0;)"number"==typeof this.prerelease[r]&&(this.prerelease[r]++,r=-2);if(-1===r){if(t===this.prerelease.join(".")&&!1===s)throw new Error("invalid increment argument: identifier already exists");this.prerelease.push(e)}}if(t){let r=[t,e];!1===s&&(r=[t]),0===c(this.prerelease[0],t)?isNaN(this.prerelease[1])&&(this.prerelease=r):this.prerelease=r}break}default:throw new Error(`invalid increment argument: ${e}`)}return this.raw=this.format(),this.build.length&&(this.raw+=`+${this.build.join(".")}`),this}}e.exports=p},7994:(e,t,s)=>{const r=s(5860);e.exports=(e,t)=>{const s=r(e.trim().replace(/^[=v]+/,""),t);return s?s.version:null}},3811:(e,t,s)=>{const r=s(6909),a=s(7659),n=s(144),o=s(2077),l=s(599),i=s(7092);e.exports=(e,t,s,c)=>{switch(t){case"===":return"object"==typeof e&&(e=e.version),"object"==typeof s&&(s=s.version),e===s;case"!==":return"object"==typeof e&&(e=e.version),"object"==typeof s&&(s=s.version),e!==s;case"":case"=":case"==":return r(e,s,c);case"!=":return a(e,s,c);case">":return n(e,s,c);case">=":return o(e,s,c);case"<":return l(e,s,c);case"<=":return i(e,s,c);default:throw new TypeError(`Invalid operator: ${t}`)}}},5334:(e,t,s)=>{const r=s(7944),a=s(5860),{safeRe:n,t:o}=s(6850);e.exports=(e,t)=>{if(e instanceof r)return e;if("number"==typeof e&&(e=String(e)),"string"!=typeof e)return null;let s=null;if((t=t||{}).rtl){const r=t.includePrerelease?n[o.COERCERTLFULL]:n[o.COERCERTL];let a;for(;(a=r.exec(e))&&(!s||s.index+s[0].length!==e.length);)s&&a.index+a[0].length===s.index+s[0].length||(s=a),r.lastIndex=a.index+a[1].length+a[2].length;r.lastIndex=-1}else s=e.match(t.includePrerelease?n[o.COERCEFULL]:n[o.COERCE]);if(null===s)return null;const l=s[2],i=s[3]||"0",c=s[4]||"0",p=t.includePrerelease&&s[5]?`-${s[5]}`:"",m=t.includePrerelease&&s[6]?`+${s[6]}`:"";return a(`${l}.${i}.${c}${p}${m}`,t)}},2801:(e,t,s)=>{const r=s(7944);e.exports=(e,t,s)=>{const a=new r(e,s),n=new r(t,s);return a.compare(n)||a.compareBuild(n)}},7023:(e,t,s)=>{const r=s(5380);e.exports=(e,t)=>r(e,t,!0)},5380:(e,t,s)=>{const r=s(7944);e.exports=(e,t,s)=>new r(e,s).compare(new r(t,s))},660:(e,t,s)=>{const r=s(5860);e.exports=(e,t)=>{const s=r(e,null,!0),a=r(t,null,!0),n=s.compare(a);if(0===n)return null;const o=n>0,l=o?s:a,i=o?a:s,c=!!l.prerelease.length;if(!!i.prerelease.length&&!c)return i.patch||i.minor?l.patch?"patch":l.minor?"minor":"major":"major";const p=c?"pre":"";return s.major!==a.major?p+"major":s.minor!==a.minor?p+"minor":s.patch!==a.patch?p+"patch":"prerelease"}},6909:(e,t,s)=>{const r=s(5380);e.exports=(e,t,s)=>0===r(e,t,s)},144:(e,t,s)=>{const r=s(5380);e.exports=(e,t,s)=>r(e,t,s)>0},2077:(e,t,s)=>{const r=s(5380);e.exports=(e,t,s)=>r(e,t,s)>=0},5955:(e,t,s)=>{const r=s(7944);e.exports=(e,t,s,a,n)=>{"string"==typeof s&&(n=a,a=s,s=void 0);try{return new r(e instanceof r?e.version:e,s).inc(t,a,n).version}catch(e){return null}}},599:(e,t,s)=>{const r=s(5380);e.exports=(e,t,s)=>r(e,t,s)<0},7092:(e,t,s)=>{const r=s(5380);e.exports=(e,t,s)=>r(e,t,s)<=0},2966:(e,t,s)=>{const r=s(7944);e.exports=(e,t)=>new r(e,t).major},5986:(e,t,s)=>{const r=s(7944);e.exports=(e,t)=>new r(e,t).minor},7659:(e,t,s)=>{const r=s(5380);e.exports=(e,t,s)=>0!==r(e,t,s)},5860:(e,t,s)=>{const r=s(7944);e.exports=(e,t,s=!1)=>{if(e instanceof r)return e;try{return new r(e,t)}catch(e){if(!s)return null;throw e}}},1617:(e,t,s)=>{const r=s(7944);e.exports=(e,t)=>new r(e,t).patch},9613:(e,t,s)=>{const r=s(5860);e.exports=(e,t)=>{const s=r(e,t);return s&&s.prerelease.length?s.prerelease:null}},2862:(e,t,s)=>{const r=s(5380);e.exports=(e,t,s)=>r(t,e,s)},6033:(e,t,s)=>{const r=s(2801);e.exports=(e,t)=>e.sort(((e,s)=>r(s,e,t)))},1722:(e,t,s)=>{const r=s(8395);e.exports=(e,t,s)=>{try{t=new r(t,s)}catch(e){return!1}return t.test(e)}},603:(e,t,s)=>{const r=s(2801);e.exports=(e,t)=>e.sort(((e,s)=>r(e,s,t)))},7645:(e,t,s)=>{const r=s(5860);e.exports=(e,t)=>{const s=r(e,t);return s?s.version:null}},8449:(e,t,s)=>{const r=s(6850),a=s(9630),n=s(7944),o=s(4031),l=s(5860),i=s(7645),c=s(7994),p=s(5955),m=s(660),u=s(2966),b=s(5986),y=s(1617),d=s(9613),_=s(5380),$=s(2862),h=s(7023),f=s(2801),w=s(603),x=s(6033),g=s(144),v=s(599),k=s(6909),E=s(7659),T=s(2077),O=s(7092),C=s(3811),A=s(5334),S=s(9524),I=s(8395),N=s(1722),L=s(651),R=s(7840),D=s(7930),q=s(3489),j=s(7494),P=s(5463),B=s(7871),M=s(4266),F=s(1112),U=s(1465),H=s(9252);e.exports={parse:l,valid:i,clean:c,inc:p,diff:m,major:u,minor:b,patch:y,prerelease:d,compare:_,rcompare:$,compareLoose:h,compareBuild:f,sort:w,rsort:x,gt:g,lt:v,eq:k,neq:E,gte:T,lte:O,cmp:C,coerce:A,Comparator:S,Range:I,satisfies:N,toComparators:L,maxSatisfying:R,minSatisfying:D,minVersion:q,validRange:j,outside:P,gtr:B,ltr:M,intersects:F,simplifyRange:U,subset:H,SemVer:n,re:r.re,src:r.src,tokens:r.t,SEMVER_SPEC_VERSION:a.SEMVER_SPEC_VERSION,RELEASE_TYPES:a.RELEASE_TYPES,compareIdentifiers:o.compareIdentifiers,rcompareIdentifiers:o.rcompareIdentifiers}},9630:e=>{const t=Number.MAX_SAFE_INTEGER||9007199254740991;e.exports={MAX_LENGTH:256,MAX_SAFE_COMPONENT_LENGTH:16,MAX_SAFE_BUILD_LENGTH:250,MAX_SAFE_INTEGER:t,RELEASE_TYPES:["major","premajor","minor","preminor","patch","prepatch","prerelease"],SEMVER_SPEC_VERSION:"2.0.0",FLAG_INCLUDE_PRERELEASE:1,FLAG_LOOSE:2}},9796:e=>{const t="object"==typeof process&&process.env&&process.env.NODE_DEBUG&&/\bsemver\b/i.test(process.env.NODE_DEBUG)?(...e)=>console.error("SEMVER",...e):()=>{};e.exports=t},4031:e=>{const t=/^[0-9]+$/,s=(e,s)=>{const r=t.test(e),a=t.test(s);return r&&a&&(e=+e,s=+s),e===s?0:r&&!a?-1:a&&!r?1:e<s?-1:1};e.exports={compareIdentifiers:s,rcompareIdentifiers:(e,t)=>s(t,e)}},1158:e=>{e.exports=class{constructor(){this.max=1e3,this.map=new Map}get(e){const t=this.map.get(e);return void 0===t?void 0:(this.map.delete(e),this.map.set(e,t),t)}delete(e){return this.map.delete(e)}set(e,t){if(!this.delete(e)&&void 0!==t){if(this.map.size>=this.max){const e=this.map.keys().next().value;this.delete(e)}this.map.set(e,t)}return this}}},7095:e=>{const t=Object.freeze({loose:!0}),s=Object.freeze({});e.exports=e=>e?"object"!=typeof e?t:e:s},6850:(e,t,s)=>{const{MAX_SAFE_COMPONENT_LENGTH:r,MAX_SAFE_BUILD_LENGTH:a,MAX_LENGTH:n}=s(9630),o=s(9796),l=(t=e.exports={}).re=[],i=t.safeRe=[],c=t.src=[],p=t.t={};let m=0;const u="[a-zA-Z0-9-]",b=[["\\s",1],["\\d",n],[u,a]],y=(e,t,s)=>{const r=(e=>{for(const[t,s]of b)e=e.split(`${t}*`).join(`${t}{0,${s}}`).split(`${t}+`).join(`${t}{1,${s}}`);return e})(t),a=m++;o(e,a,t),p[e]=a,c[a]=t,l[a]=new RegExp(t,s?"g":void 0),i[a]=new RegExp(r,s?"g":void 0)};y("NUMERICIDENTIFIER","0|[1-9]\\d*"),y("NUMERICIDENTIFIERLOOSE","\\d+"),y("NONNUMERICIDENTIFIER",`\\d*[a-zA-Z-]${u}*`),y("MAINVERSION",`(${c[p.NUMERICIDENTIFIER]})\\.(${c[p.NUMERICIDENTIFIER]})\\.(${c[p.NUMERICIDENTIFIER]})`),y("MAINVERSIONLOOSE",`(${c[p.NUMERICIDENTIFIERLOOSE]})\\.(${c[p.NUMERICIDENTIFIERLOOSE]})\\.(${c[p.NUMERICIDENTIFIERLOOSE]})`),y("PRERELEASEIDENTIFIER",`(?:${c[p.NUMERICIDENTIFIER]}|${c[p.NONNUMERICIDENTIFIER]})`),y("PRERELEASEIDENTIFIERLOOSE",`(?:${c[p.NUMERICIDENTIFIERLOOSE]}|${c[p.NONNUMERICIDENTIFIER]})`),y("PRERELEASE",`(?:-(${c[p.PRERELEASEIDENTIFIER]}(?:\\.${c[p.PRERELEASEIDENTIFIER]})*))`),y("PRERELEASELOOSE",`(?:-?(${c[p.PRERELEASEIDENTIFIERLOOSE]}(?:\\.${c[p.PRERELEASEIDENTIFIERLOOSE]})*))`),y("BUILDIDENTIFIER",`${u}+`),y("BUILD",`(?:\\+(${c[p.BUILDIDENTIFIER]}(?:\\.${c[p.BUILDIDENTIFIER]})*))`),y("FULLPLAIN",`v?${c[p.MAINVERSION]}${c[p.PRERELEASE]}?${c[p.BUILD]}?`),y("FULL",`^${c[p.FULLPLAIN]}$`),y("LOOSEPLAIN",`[v=\\s]*${c[p.MAINVERSIONLOOSE]}${c[p.PRERELEASELOOSE]}?${c[p.BUILD]}?`),y("LOOSE",`^${c[p.LOOSEPLAIN]}$`),y("GTLT","((?:<|>)?=?)"),y("XRANGEIDENTIFIERLOOSE",`${c[p.NUMERICIDENTIFIERLOOSE]}|x|X|\\*`),y("XRANGEIDENTIFIER",`${c[p.NUMERICIDENTIFIER]}|x|X|\\*`),y("XRANGEPLAIN",`[v=\\s]*(${c[p.XRANGEIDENTIFIER]})(?:\\.(${c[p.XRANGEIDENTIFIER]})(?:\\.(${c[p.XRANGEIDENTIFIER]})(?:${c[p.PRERELEASE]})?${c[p.BUILD]}?)?)?`),y("XRANGEPLAINLOOSE",`[v=\\s]*(${c[p.XRANGEIDENTIFIERLOOSE]})(?:\\.(${c[p.XRANGEIDENTIFIERLOOSE]})(?:\\.(${c[p.XRANGEIDENTIFIERLOOSE]})(?:${c[p.PRERELEASELOOSE]})?${c[p.BUILD]}?)?)?`),y("XRANGE",`^${c[p.GTLT]}\\s*${c[p.XRANGEPLAIN]}$`),y("XRANGELOOSE",`^${c[p.GTLT]}\\s*${c[p.XRANGEPLAINLOOSE]}$`),y("COERCEPLAIN",`(^|[^\\d])(\\d{1,${r}})(?:\\.(\\d{1,${r}}))?(?:\\.(\\d{1,${r}}))?`),y("COERCE",`${c[p.COERCEPLAIN]}(?:$|[^\\d])`),y("COERCEFULL",c[p.COERCEPLAIN]+`(?:${c[p.PRERELEASE]})?`+`(?:${c[p.BUILD]})?(?:$|[^\\d])`),y("COERCERTL",c[p.COERCE],!0),y("COERCERTLFULL",c[p.COERCEFULL],!0),y("LONETILDE","(?:~>?)"),y("TILDETRIM",`(\\s*)${c[p.LONETILDE]}\\s+`,!0),t.tildeTrimReplace="$1~",y("TILDE",`^${c[p.LONETILDE]}${c[p.XRANGEPLAIN]}$`),y("TILDELOOSE",`^${c[p.LONETILDE]}${c[p.XRANGEPLAINLOOSE]}$`),y("LONECARET","(?:\\^)"),y("CARETTRIM",`(\\s*)${c[p.LONECARET]}\\s+`,!0),t.caretTrimReplace="$1^",y("CARET",`^${c[p.LONECARET]}${c[p.XRANGEPLAIN]}$`),y("CARETLOOSE",`^${c[p.LONECARET]}${c[p.XRANGEPLAINLOOSE]}$`),y("COMPARATORLOOSE",`^${c[p.GTLT]}\\s*(${c[p.LOOSEPLAIN]})$|^$`),y("COMPARATOR",`^${c[p.GTLT]}\\s*(${c[p.FULLPLAIN]})$|^$`),y("COMPARATORTRIM",`(\\s*)${c[p.GTLT]}\\s*(${c[p.LOOSEPLAIN]}|${c[p.XRANGEPLAIN]})`,!0),t.comparatorTrimReplace="$1$2$3",y("HYPHENRANGE",`^\\s*(${c[p.XRANGEPLAIN]})\\s+-\\s+(${c[p.XRANGEPLAIN]})\\s*$`),y("HYPHENRANGELOOSE",`^\\s*(${c[p.XRANGEPLAINLOOSE]})\\s+-\\s+(${c[p.XRANGEPLAINLOOSE]})\\s*$`),y("STAR","(<|>)?=?\\s*\\*"),y("GTE0","^\\s*>=\\s*0\\.0\\.0\\s*$"),y("GTE0PRE","^\\s*>=\\s*0\\.0\\.0-0\\s*$")},7871:(e,t,s)=>{const r=s(5463);e.exports=(e,t,s)=>r(e,t,">",s)},1112:(e,t,s)=>{const r=s(8395);e.exports=(e,t,s)=>(e=new r(e,s),t=new r(t,s),e.intersects(t,s))},4266:(e,t,s)=>{const r=s(5463);e.exports=(e,t,s)=>r(e,t,"<",s)},7840:(e,t,s)=>{const r=s(7944),a=s(8395);e.exports=(e,t,s)=>{let n=null,o=null,l=null;try{l=new a(t,s)}catch(e){return null}return e.forEach((e=>{l.test(e)&&(n&&-1!==o.compare(e)||(n=e,o=new r(n,s)))})),n}},7930:(e,t,s)=>{const r=s(7944),a=s(8395);e.exports=(e,t,s)=>{let n=null,o=null,l=null;try{l=new a(t,s)}catch(e){return null}return e.forEach((e=>{l.test(e)&&(n&&1!==o.compare(e)||(n=e,o=new r(n,s)))})),n}},3489:(e,t,s)=>{const r=s(7944),a=s(8395),n=s(144);e.exports=(e,t)=>{e=new a(e,t);let s=new r("0.0.0");if(e.test(s))return s;if(s=new r("0.0.0-0"),e.test(s))return s;s=null;for(let t=0;t<e.set.length;++t){const a=e.set[t];let o=null;a.forEach((e=>{const t=new r(e.semver.version);switch(e.operator){case">":0===t.prerelease.length?t.patch++:t.prerelease.push(0),t.raw=t.format();case"":case">=":o&&!n(t,o)||(o=t);break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${e.operator}`)}})),!o||s&&!n(s,o)||(s=o)}return s&&e.test(s)?s:null}},5463:(e,t,s)=>{const r=s(7944),a=s(9524),{ANY:n}=a,o=s(8395),l=s(1722),i=s(144),c=s(599),p=s(7092),m=s(2077);e.exports=(e,t,s,u)=>{let b,y,d,_,$;switch(e=new r(e,u),t=new o(t,u),s){case">":b=i,y=p,d=c,_=">",$=">=";break;case"<":b=c,y=m,d=i,_="<",$="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(l(e,t,u))return!1;for(let s=0;s<t.set.length;++s){const r=t.set[s];let o=null,l=null;if(r.forEach((e=>{e.semver===n&&(e=new a(">=0.0.0")),o=o||e,l=l||e,b(e.semver,o.semver,u)?o=e:d(e.semver,l.semver,u)&&(l=e)})),o.operator===_||o.operator===$)return!1;if((!l.operator||l.operator===_)&&y(e,l.semver))return!1;if(l.operator===$&&d(e,l.semver))return!1}return!0}},1465:(e,t,s)=>{const r=s(1722),a=s(5380);e.exports=(e,t,s)=>{const n=[];let o=null,l=null;const i=e.sort(((e,t)=>a(e,t,s)));for(const e of i){r(e,t,s)?(l=e,o||(o=e)):(l&&n.push([o,l]),l=null,o=null)}o&&n.push([o,null]);const c=[];for(const[e,t]of n)e===t?c.push(e):t||e!==i[0]?t?e===i[0]?c.push(`<=${t}`):c.push(`${e} - ${t}`):c.push(`>=${e}`):c.push("*");const p=c.join(" || "),m="string"==typeof t.raw?t.raw:String(t);return p.length<m.length?p:t}},9252:(e,t,s)=>{const r=s(8395),a=s(9524),{ANY:n}=a,o=s(1722),l=s(5380),i=[new a(">=0.0.0-0")],c=[new a(">=0.0.0")],p=(e,t,s)=>{if(e===t)return!0;if(1===e.length&&e[0].semver===n){if(1===t.length&&t[0].semver===n)return!0;e=s.includePrerelease?i:c}if(1===t.length&&t[0].semver===n){if(s.includePrerelease)return!0;t=c}const r=new Set;let a,p,b,y,d,_,$;for(const t of e)">"===t.operator||">="===t.operator?a=m(a,t,s):"<"===t.operator||"<="===t.operator?p=u(p,t,s):r.add(t.semver);if(r.size>1)return null;if(a&&p){if(b=l(a.semver,p.semver,s),b>0)return null;if(0===b&&(">="!==a.operator||"<="!==p.operator))return null}for(const e of r){if(a&&!o(e,String(a),s))return null;if(p&&!o(e,String(p),s))return null;for(const r of t)if(!o(e,String(r),s))return!1;return!0}let h=!(!p||s.includePrerelease||!p.semver.prerelease.length)&&p.semver,f=!(!a||s.includePrerelease||!a.semver.prerelease.length)&&a.semver;h&&1===h.prerelease.length&&"<"===p.operator&&0===h.prerelease[0]&&(h=!1);for(const e of t){if($=$||">"===e.operator||">="===e.operator,_=_||"<"===e.operator||"<="===e.operator,a)if(f&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===f.major&&e.semver.minor===f.minor&&e.semver.patch===f.patch&&(f=!1),">"===e.operator||">="===e.operator){if(y=m(a,e,s),y===e&&y!==a)return!1}else if(">="===a.operator&&!o(a.semver,String(e),s))return!1;if(p)if(h&&e.semver.prerelease&&e.semver.prerelease.length&&e.semver.major===h.major&&e.semver.minor===h.minor&&e.semver.patch===h.patch&&(h=!1),"<"===e.operator||"<="===e.operator){if(d=u(p,e,s),d===e&&d!==p)return!1}else if("<="===p.operator&&!o(p.semver,String(e),s))return!1;if(!e.operator&&(p||a)&&0!==b)return!1}return!(a&&_&&!p&&0!==b)&&(!(p&&$&&!a&&0!==b)&&(!f&&!h))},m=(e,t,s)=>{if(!e)return t;const r=l(e.semver,t.semver,s);return r>0?e:r<0||">"===t.operator&&">="===e.operator?t:e},u=(e,t,s)=>{if(!e)return t;const r=l(e.semver,t.semver,s);return r<0?e:r>0||"<"===t.operator&&"<="===e.operator?t:e};e.exports=(e,t,s={})=>{if(e===t)return!0;e=new r(e,s),t=new r(t,s);let a=!1;e:for(const r of e.set){for(const e of t.set){const t=p(r,e,s);if(a=a||null!==t,t)continue e}if(a)return!1}return!0}},651:(e,t,s)=>{const r=s(8395);e.exports=(e,t)=>new r(e,t).set.map((e=>e.map((e=>e.value)).join(" ").trim().split(" ")))},7494:(e,t,s)=>{const r=s(8395);e.exports=(e,t)=>{try{return new r(e,t).range||"*"}catch(e){return null}}},6089:t=>{"use strict";t.exports=e},7781:e=>{"use strict";e.exports=t},8531:e=>{"use strict";e.exports=s},2007:e=>{"use strict";e.exports=r},3241:e=>{"use strict";e.exports=a},1308:e=>{"use strict";e.exports=n},5959:e=>{"use strict";e.exports=o},1269:e=>{"use strict";e.exports=l}},c={};function p(e){var t=c[e];if(void 0!==t)return t.exports;var s=c[e]={exports:{}};return i[e].call(s.exports,s,s.exports,p),s.exports}p.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return p.d(t,{a:t}),t},p.d=(e,t)=>{for(var s in t)p.o(t,s)&&!p.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},p.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),p.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},p.p="public/plugins/grafana-clickhouse-datasource/";var m={};return(()=>{"use strict";p.r(m),p.d(m,{plugin:()=>ma});var e=p(1308),t=p.n(e);p.p=t()&&t().uri?t().uri.slice(0,t().uri.lastIndexOf("/")+1):"public/plugins/grafana-clickhouse-datasource/";var s,r,a,n,o,l,i,c,u=p(7781),b=p(8531),y=p(1269);!function(e){e.List="list",e.Aggregate="aggregate",e.Trend="trend"}(s||(s={})),function(e){e.Table="table",e.Logs="logs",e.TimeSeries="timeseries",e.Traces="traces"}(r||(r={})),function(e){e.Sum="sum",e.Average="avg",e.Min="min",e.Max="max",e.Count="count",e.Any="any"}(a||(a={})),function(e){e.Time="time",e.LogLevel="log_level",e.LogMessage="log_message",e.LogLabels="log_labels",e.TraceId="trace_id",e.TraceSpanId="trace_span_id",e.TraceParentSpanId="trace_parent_span_id",e.TraceServiceName="trace_service_name",e.TraceOperationName="trace_operation_name",e.TraceDurationTime="trace_duration_time",e.TraceTags="trace_tags",e.TraceServiceTags="trace_service_tags",e.TraceStatusCode="trace_status_code"}(n||(n={})),function(e){e.Seconds="seconds",e.Milliseconds="milliseconds",e.Microseconds="microseconds",e.Nanoseconds="nanoseconds"}(o||(o={})),function(e){e.ASC="ASC",e.DESC="DESC"}(l||(l={})),function(e){e.IsAnything="IS ANYTHING",e.IsEmpty="IS EMPTY",e.IsNotEmpty="IS NOT EMPTY",e.IsNull="IS NULL",e.IsNotNull="IS NOT NULL",e.Equals="=",e.NotEquals="!=",e.LessThan="<",e.LessThanOrEqual="<=",e.GreaterThan=">",e.GreaterThanOrEqual=">=",e.Like="LIKE",e.NotLike="NOT LIKE",e.In="IN",e.NotIn="NOT IN",e.WithInGrafanaTimeRange="WITH IN DASHBOARD TIME RANGE",e.OutsideGrafanaTimeRange="OUTSIDE DASHBOARD TIME RANGE"}(i||(i={})),function(e){e.SQL="sql",e.Builder="builder"}(c||(c={}));const d={pluginVersion:"",editorType:"builder",rawSql:"",builderOptions:{database:"",table:"",queryType:r.Table,mode:s.List,columns:[],meta:{},limit:1e3}};var _=p(7899);function $(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}function h(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{},r=Object.keys(s);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(s).filter((function(e){return Object.getOwnPropertyDescriptor(s,e).enumerable})))),r.forEach((function(t){$(e,t,s[t])}))}return e}function f(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),s.push.apply(s,r)}return s}(Object(t)).forEach((function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})),e}function w(e){return(e?"v":"f")+(Math.random()+1).toString(36).substring(7)}function x(e){const[t,s]=function(e){const t=[],s=/\${[a-zA-Z0-9_:.\w]+}/g;let r;for(;null!==(r=s.exec(e));)t.push({startIndex:r.index,name:r[0],replacementName:""});for(let s=t.length-1;s>=0;s--){const r=t[s].startIndex,a=w(!0);t[s].replacementName=a,e=e.substring(0,r)+a+e.substring(r+t[s].name.length)}return[t,e]}(e),[r,a]=function(e){const t=[],s=/(\$__|\$|default|settings)/gi;let r;for(;null!==(r=s.exec(e));)t.push({startIndex:r.index,name:r[0],replacementName:""});for(let s=t.length-1;s>=0;s--){const r=t[s].startIndex,a=w(!1);t[s].replacementName=a,e="settings"!==t[s].name.toLowerCase()?e.substring(0,r)+a+e.substring(r+t[s].name.length):e.substring(0,r)}return[t,e]}(s),n=t.concat(r);let o;try{o=(0,_.parseFirst)(a)}catch(e){return console.error(`Failed to parse SQL statement into an AST: ${e}`),{}}return(0,_.astMapper)((e=>({tableRef:t=>{const s=n.find((e=>e.replacementName===t.schema));var r;if(s)return f(h({},t),{schema:null===(r=t.schema)||void 0===r?void 0:r.replace(s.replacementName,s.name)});const a=n.find((e=>e.replacementName===t.name));return a?f(h({},t),{name:t.name.replace(a.replacementName,a.name)}):e.super().tableRef(t)},ref:t=>{const s=n.find((e=>t.name.startsWith(e.replacementName)));if(s){const e=t.name.replace(s.replacementName,s.name);return f(h({},t),{name:e})}return e.super().ref(t)},expr:t=>{if(!t||"string"!==t.type)return e.super().expr(t);const s=n.find((e=>t.value.startsWith(e.replacementName)));if(s){const e=t.value.replace(s.replacementName,s.name);return f(h({},t),{value:e})}return e.super().expr(t)},call:t=>{const s=n.find((e=>t.function.name.startsWith(e.replacementName)));return s?f(h({},t),{function:f(h({},t.function),{name:t.function.name.replace(s.replacementName,s.name)})}):e.super().call(t)}}))).statement(o)}function g(e){var t,s;const r=x(e);if("select"!==r.type||!(null===(t=r.from)||void 0===t?void 0:t.length)||(null===(s=r.from)||void 0===s?void 0:s.length)<=0)return"";switch(r.from[0].type){case"table":{const t=r.from[0],s=`${t.name.schema?`${t.name.schema}.`:""}${t.name.name}`,a=new RegExp(`\\b${s}\\b`,"gi").exec(e);return a?a[0]:s}case"statement":{const e=r.from[0];return g(_.toSql.statement(e.statement))}}return""}class v{setTargetTableFromQuery(e){if(this._targetTable=g(e),""===this._targetTable)throw new Error("Failed to get table from adhoc query.")}apply(e,t){if(""===e||!t||0===t.length)return e;if(""!==this._targetTable&&!e.replace(/"/g,"").match(new RegExp(`.*\\b${this._targetTable}\\b.*`,"gi")))return e;if(""===this._targetTable&&(this._targetTable=g(e)),""===this._targetTable)return e;const s=t.filter((e=>{const t=function(e){return void 0!==e.key&&void 0!==e.operator&&void 0!==e.value}(e);return t||console.warn("Invalid adhoc filter will be ignored:",e),t})).map(((e,s)=>{const r=e.key.includes(".")?e.key.split(".")[1]:e.key,a=function(e,t){return"IN"===t?(e.length>2&&"("!==e[0]&&")"!==e[e.length-1]&&(e=`(${e})`),e.replace(/'/g,"\\'")):`\\'${e}\\'`}(e.value,e.operator),n=s!==t.length-1?e.condition?e.condition:"AND":"";return` ${r} ${function(e){if("=~"===e)return"ILIKE";if("!~"===e)return"NOT ILIKE";return e}(e.operator)} ${a} ${n}`})).join("");return""===s?e:`${e=e.replace(";","")} settings additional_table_filters={'${this._targetTable}' : '${s}'}`}constructor(){!function(e,t,s){t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s}(this,"_targetTable","")}}var k=p(3241),E=(e=>(e.Bars="bars",e.Line="line",e.Points="points",e))(E||{}),T=(e=>(e.None="none",e.Normal="normal",e.Percent="percent",e))(T||{}),O=(e=>(e[e.After=1]="After",e[e.Before=-1]="Before",e[e.Center=0]="Center",e))(O||{});var C=p(2007);const A=1e3,S=6e4,I=36e5,N={[u.LogLevel.critical]:C.colors[7],[u.LogLevel.warning]:C.colors[1],[u.LogLevel.error]:C.colors[4],[u.LogLevel.info]:C.colors[0],[u.LogLevel.debug]:C.colors[5],[u.LogLevel.trace]:C.colors[2],[u.LogLevel.unknown]:(L="#8e8e8e",R="#bdc4cd",b.config.bootData.user.lightTheme?R:L)};var L,R;function D(e,t,s){return new y.Observable((r=>{let a=[];r.next({state:u.LoadingState.Loading,error:void 0,data:[]});const n=e.query(t),o=((0,y.isObservable)(n)?n:(0,y.from)(n)).subscribe({complete:()=>{const e=function(e){if(1!==e.length)return[];const[[t],s]=(0,k.partition)(e[0].fields,(e=>e.name===P));if(void 0===t)return[];const r=1===s.length&&s[0].name===B;r&&(s[0].name="logs");const a=t.values.length;return s.map((e=>{const s=u.LogLevel[e.name]||u.LogLevel.unknown,n=new u.MutableDataFrame;return n.addField({name:"Time",type:u.FieldType.time,values:t.values},a),n.addField({name:"Value",type:u.FieldType.number,config:q(s,r),values:e.values}),n}))}(a);e[0]&&(e[0].meta={custom:{targets:s.targets,absoluteRange:{from:s.range.from.valueOf(),to:s.range.to.valueOf()}}}),r.next({state:u.LoadingState.Done,error:void 0,data:e}),r.complete()},next:e=>{const{error:t}=e;void 0!==t?(r.next({state:u.LoadingState.Error,error:t,data:[]}),r.error(t)):a=a.concat(e.data.map(u.toDataFrame))},error:e=>{r.next({state:u.LoadingState.Error,error:e,data:[]}),r.error(e)}});return()=>{null==o||o.unsubscribe()}}))}function q(e,t){const s=t&&e===u.LogLevel.unknown?"logs":e,r=N[e];return{displayNameFromDS:s,color:{mode:u.FieldColorModeId.Fixed,fixedColor:r},custom:{drawStyle:E.Bars,barAlignment:O.Center,lineColor:r,pointColor:r,fillColor:r,lineWidth:1,fillOpacity:100,stacking:{mode:T.Normal,group:"A"}}}}function j(e,t){let s="DAY";if(e.__interval_ms){let t=e.__interval_ms.value;s=t>I?"DAY":t>S?"HOUR":t>A?"MINUTE":"SECOND"}return`toStartOfInterval("${t}", INTERVAL 1 ${s})`}const P="time",B="logs",M=(()=>{const e={critical:["critical","fatal","crit","alert","emerg"],error:["error","err","eror"],warn:["warn","warning"],info:["info","information","informational"],debug:["debug","dbug"],trace:["trace"],unknown:["unknown"]};return Object.keys(e).reduce(((t,s)=>(t[s]=`IN (${[...e[s].map((e=>`'${e}'`)),...e[s].map((e=>`'${e.toUpperCase()}'`)),...e[s].map((e=>`'${e.charAt(0).toUpperCase()+e.slice(1)}'`))].join(",")})`,t)),{})})();function F(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}const U="otel_logs",H="otel_traces",G={name:"1.2.9",version:"1.29.0",specUrl:"https://opentelemetry.io/docs/specs/otel",logsTable:U,logColumnMap:new Map([[n.Time,"Timestamp"],[n.LogMessage,"Body"],[n.LogLevel,"SeverityText"],[n.LogLabels,"LogAttributes"],[n.TraceId,"TraceId"]]),logLevels:["TRACE","DEBUG","INFO","WARN","ERROR","FATAL"],traceTable:H,traceColumnMap:new Map([[n.Time,"Timestamp"],[n.TraceId,"TraceId"],[n.TraceSpanId,"SpanId"],[n.TraceParentSpanId,"ParentSpanId"],[n.TraceServiceName,"ServiceName"],[n.TraceOperationName,"SpanName"],[n.TraceDurationTime,"Duration"],[n.TraceTags,"SpanAttributes"],[n.TraceServiceTags,"ResourceAttributes"],[n.TraceStatusCode,"StatusCode"]]),traceDurationUnit:o.Nanoseconds},V=[(z=function(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{},r=Object.keys(s);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(s).filter((function(e){return Object.getOwnPropertyDescriptor(s,e).enumerable})))),r.forEach((function(t){F(e,t,s[t])}))}return e}({},G),W={name:`latest (${G.name})`,version:"latest"},W=null!=W?W:{},Object.getOwnPropertyDescriptors?Object.defineProperties(z,Object.getOwnPropertyDescriptors(W)):function(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),s.push.apply(s,r)}return s}(Object(W)).forEach((function(e){Object.defineProperty(z,e,Object.getOwnPropertyDescriptor(W,e))})),z),G];var z,W;const K={traceTimestampTableSuffix:"_trace_id_ts",versions:V,getLatestVersion:()=>V[0],getVersion:e=>{if(e)return V.find((t=>t.version===e))}};function Q(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}function Y(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{},r=Object.keys(s);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(s).filter((function(e){return Object.getOwnPropertyDescriptor(s,e).enumerable})))),r.forEach((function(t){Q(e,t,s[t])}))}return e}function X(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),s.push.apply(s,r)}return s}(Object(t)).forEach((function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})),e}const J=e=>{var t,a;const n=(null===(t=e.meta)||void 0===t?void 0:t.isTraceIdMode)&&(null===(a=e.meta)||void 0===a?void 0:a.traceId);return e.queryType===r.Traces&&n?ee(e):e.queryType===r.Traces?Z(e):e.queryType===r.Logs?te(e):e.queryType===r.TimeSeries&&e.mode!==s.Trend?se(e):e.queryType===r.TimeSeries&&e.mode===s.Trend?re(e):e.queryType===r.Table?ae(e):""},Z=e=>{const{database:t,table:s}=e,r=[],a=[],o=ne(e,n.TraceId);void 0!==o&&a.push(`${ie(o.name)} as traceID`);const l=ne(e,n.TraceServiceName);void 0!==l&&a.push(`${ie(l.name)} as serviceName`);const i=ne(e,n.TraceOperationName);void 0!==i&&a.push(`${ie(i.name)} as operationName`);const c=ne(e,n.Time);void 0!==c&&a.push(`${ie(c.name)} as startTime`);const p=ne(e,n.TraceDurationTime);if(void 0!==p){var m;const t=null===(m=e.meta)||void 0===m?void 0:m.traceDurationUnit;a.push(pe(ie(p.name),t))}const u=a.join(", ");r.push("SELECT"),r.push(u),r.push("FROM"),r.push(le(t,s));const b=de(e);b&&(r.push("WHERE"),r.push(b));const y=be(e);y&&(r.push("ORDER BY"),r.push(y));const d=ye(e.limit);return""!==d&&r.push(d),ue(r)},ee=e=>{var t,s,r,a;const{database:o,table:l}=e,i=[],c=[],p=ne(e,n.TraceId);void 0!==p&&c.push(`${ie(p.name)} as traceID`);const m=ne(e,n.TraceSpanId);void 0!==m&&c.push(`${ie(m.name)} as spanID`);const u=ne(e,n.TraceParentSpanId);void 0!==u&&c.push(`${ie(u.name)} as parentSpanID`);const b=ne(e,n.TraceServiceName);void 0!==b&&c.push(`${ie(b.name)} as serviceName`);const y=ne(e,n.TraceOperationName);void 0!==y&&c.push(`${ie(y.name)} as operationName`);const d=ne(e,n.Time);void 0!==d&&c.push(`${me(ie(d.name))} as startTime`);const _=ne(e,n.TraceDurationTime);if(void 0!==_){var $;const t=null===($=e.meta)||void 0===$?void 0:$.traceDurationUnit;c.push(pe(ie(_.name),t))}const h=ne(e,n.TraceTags);void 0!==h&&c.push(`arrayMap(key -> map('key', key, 'value',${ie(h.name)}[key]), mapKeys(${ie(h.name)})) as tags`);const f=ne(e,n.TraceServiceTags);void 0!==f&&c.push(`arrayMap(key -> map('key', key, 'value',${ie(f.name)}[key]), mapKeys(${ie(f.name)})) as serviceTags`);const w=ne(e,n.TraceStatusCode);void 0!==w&&c.push(`if(${ie(w.name)} IN ('Error', 'STATUS_CODE_ERROR'), 2, 0) as statusCode`);const x=c.join(", "),g=(null===(t=e.meta)||void 0===t?void 0:t.isTraceIdMode)&&(null===(s=e.meta)||void 0===s?void 0:s.traceId),v=K.getVersion(null===(r=e.meta)||void 0===r?void 0:r.otelVersion),k=g&&void 0!==d&&(null===(a=e.meta)||void 0===a?void 0:a.otelEnabled)&&v;if(k){const t=e.meta.traceId,s=le(o,l+K.traceTimestampTableSuffix);i.push("WITH"),i.push(`'${t}' as trace_id,`),i.push(`(SELECT min(Start) FROM ${s} WHERE TraceId = trace_id) as trace_start,`),i.push(`(SELECT max(End) + 1 FROM ${s} WHERE TraceId = trace_id) as trace_end`)}i.push("SELECT"),i.push(x),i.push("FROM"),i.push(le(o,l));const E=de(e);if((g||E)&&i.push("WHERE"),k)i.push("traceID = trace_id"),i.push("AND"),i.push(`${ie(d.name)} >= trace_start`),i.push("AND"),i.push(`${ie(d.name)} <= trace_end`);else if(g){const t=e.meta.traceId;i.push(`traceID = '${t}'`)}E&&(g&&i.push("AND"),i.push(E));const T=be(e);T&&(i.push("ORDER BY"),i.push(T));const O=ye(e.limit);return""!==O&&i.push(O),ue(i)},te=e=>{var t,s,r;const a=X(Y({},e),{columns:null===(t=e.columns)||void 0===t?void 0:t.map((e=>Y({},e)))}),{database:o,table:l}=a,i=[],c=[],p=ne(a,n.Time);void 0!==p&&(p.alias=Se.get(n.Time),c.push(oe(p)));const m=ne(a,n.LogMessage);void 0!==m&&(m.alias=Se.get(n.LogMessage),c.push(oe(m)));const u=ne(a,n.LogLevel);void 0!==u&&(u.alias=Se.get(n.LogLevel),c.push(oe(u)));const b=ne(a,n.LogLabels);void 0!==b&&(b.alias=Se.get(n.LogLabels),c.push(oe(b)));const y=ne(a,n.TraceId);void 0!==y&&(y.alias=Se.get(n.TraceId),c.push(oe(y))),null===(s=a.columns)||void 0===s||s.filter((e=>void 0===e.hint)).forEach((e=>c.push(oe(e))));const d=c.join(", ");i.push("SELECT"),i.push(d),i.push("FROM"),i.push(le(o,l));const _=de(a),$=m&&(null===(r=a.meta)||void 0===r?void 0:r.logMessageLike);(_||$)&&i.push("WHERE"),_&&i.push(_),$&&(_&&i.push("AND"),i.push(`(${m.alias||m.name} LIKE '%${a.meta.logMessageLike}%')`));const h=be(a);h&&(i.push("ORDER BY"),i.push(h));const f=ye(a.limit);return""!==f&&i.push(f),ue(i)},se=e=>{var t,s,r,a,o,l,i;const c=X(Y({},e),{columns:null===(t=e.columns)||void 0===t?void 0:t.map((e=>Y({},e)))}),{database:p,table:m}=c,u=[],b=[],y=new Set,d=ne(c,n.Time);void 0!==d&&(d.alias="time",b.push(oe(d)),y.add(d.alias));const _=null===(s=c.columns)||void 0===s?void 0:s.filter((e=>e.hint!==n.Time));null==_||_.forEach((e=>{b.push(oe(e)),y.add(e.alias||e.name)}));const $=[];null===(r=c.aggregates)||void 0===r||r.forEach((e=>{const t=e.alias?` as ${e.alias.replace(/ /g,"_")}`:"",s=`${e.aggregateType}(${e.column})`;$.push(`${s}${t}`),y.add(t?t.substring(4):s)})),null===(a=c.groupBy)||void 0===a||a.forEach((e=>{y.has(e)||b.push(e)})),$.forEach((e=>b.push(e)));const h=b.join(", ");u.push("SELECT"),u.push(h),u.push("FROM"),u.push(le(p,m));const f=de(c);f&&(u.push("WHERE"),u.push(f));const w=(null===(o=c.aggregates)||void 0===o?void 0:o.length)||!1,x=(null===(l=c.groupBy)||void 0===l?void 0:l.length)||!1;if((w||x)&&u.push("GROUP BY"),((null===(i=c.groupBy)||void 0===i?void 0:i.length)||0)>0){const e=void 0!==d?`, ${d.alias}`:"";u.push(`${c.groupBy.join(", ")}${e}`)}else w&&d&&u.push(d.alias);const g=be(c);g&&(u.push("ORDER BY"),u.push(g));const v=ye(c.limit);return""!==v&&u.push(v),ue(u)},re=e=>{var t,s,r,a;const o=X(Y({},e),{columns:null===(t=e.columns)||void 0===t?void 0:t.map((e=>Y({},e)))}),{database:l,table:i}=o,c=[],p=[],m=ne(o,n.Time);void 0!==m&&(m.name=`$__timeInterval(${m.name})`,m.alias="time",p.push(oe(m))),null===(s=o.groupBy)||void 0===s||s.forEach((e=>p.push(e))),null===(r=o.aggregates)||void 0===r||r.forEach((e=>{const t=e.alias?` as ${e.alias.replace(/ /g,"_")}`:"",s=`${e.aggregateType}(${e.column})`;p.push(`${s}${t}`)}));const u=p.join(", ");c.push("SELECT"),c.push(u),c.push("FROM"),c.push(le(l,i));const b=de(o);if(b&&(c.push("WHERE"),c.push(b)),c.push("GROUP BY"),((null===(a=o.groupBy)||void 0===a?void 0:a.length)||0)>0){const e=void 0!==m?`, ${m.alias}`:"";c.push(`${o.groupBy.join(", ")}${e}`)}else m&&c.push(m.alias);const y=be(o);y&&(c.push("ORDER BY"),c.push(y));const d=ye(o.limit);return""!==d&&c.push(d),ue(c)},ae=e=>{var t,r;const{database:a,table:n}=e,o=e.mode===s.Aggregate,l=[],i=[],c=new Set;var p,m;(null===(t=e.columns)||void 0===t||t.forEach((e=>{i.push(oe(e)),c.add(e.alias||e.name)})),o)&&(null===(p=e.aggregates)||void 0===p||p.forEach((e=>{const t=e.alias?` as ${e.alias.replace(/ /g,"_")}`:"",s=`${e.aggregateType}(${e.column})`;i.push(`${s}${t}`),c.add(t?t.substring(4):s)})),null===(m=e.groupBy)||void 0===m||m.forEach((e=>{c.has(e)})));const u=i.join(", ");l.push("SELECT"),l.push(u),l.push("FROM"),l.push(le(a,n));const b=de(e);b&&(l.push("WHERE"),l.push(b)),o&&((null===(r=e.groupBy)||void 0===r?void 0:r.length)||0)>0&&(l.push("GROUP BY"),l.push(e.groupBy.join(", ")));const y=be(e);y&&(l.push("ORDER BY"),l.push(y));const d=ye(e.limit);return""!==d&&l.push(d),ue(l)},ne=(e,t)=>{var s;return null===(s=e.columns)||void 0===s?void 0:s.find((e=>e.hint===t))},oe=e=>{let t=e.name;return t.includes("(")||t.includes(")")||t.includes('"')||t.includes('"')||t.includes(" as ")?t=e.name:t.includes(" ")&&(t=ie(e.name)),e.alias&&e.alias!==e.name&&ie(e.alias)!==t?`${t} as "${e.alias}"`:t},le=(e,t)=>{const s=e&&t?".":"";return`${ie(e)}${s}${ie(t)}`},ie=e=>e?`"${e}"`:"",ce=e=>e.includes("$")||e.includes("(")||e.includes(")")||e.includes("'")||e.includes('"')?e:`'${e}'`,pe=(e,t)=>{const s="duration";switch(t){case o.Seconds:return`multiply(${e}, 1000) as ${s}`;case o.Milliseconds:return`${e} as ${s}`;case o.Microseconds:return`multiply(${e}, 0.001) as ${s}`;case o.Nanoseconds:return`multiply(${e}, 0.000001) as ${s}`;default:return`${e} as ${s}`}},me=e=>`multiply(toUnixTimestamp64Nano(${e}), 0.000001)`,ue=e=>{let t="";for(let s=0;s<e.length;s++){const r=e[s];r&&(t+=r,s!==e.length-1&&(t+=" "))}return t},be=e=>{var t;const s=[];var r;((null===(t=e.orderBy)||void 0===t?void 0:t.length)||0)>0&&(null===(r=e.orderBy)||void 0===r||r.forEach((t=>{let r=t.name;const a=t.hint&&ne(e,t.hint);a&&(r=a.alias||a.name),r&&s.push(`${r} ${t.dir}`)})));return s.join(", ")},ye=e=>(e=Math.max(0,e||0))>0?"LIMIT "+e:"",de=e=>{const t=e.filters||[],s=[];for(const a of t){if(a.operator===i.IsAnything)continue;const t=[];let n=a.key,o=a.type;const l=a.hint&&ne(e,a.hint);if(l&&(n=l.alias||l.name,o=l.type||o),!n)continue;a.mapKey&&(n+=`['${a.mapKey}']`),t.push(n);let c=a.operator,p=!1;if(a.operator===i.IsEmpty||a.operator===i.IsNotEmpty?c="":a.operator===i.NotLike?(c="LIKE",p=!0):a.operator===i.OutsideGrafanaTimeRange?(c="",p=!0):a.operator===i.WithInGrafanaTimeRange&&(c=""),c&&t.push(c),xe(a.operator));else if(a.operator===i.IsEmpty)t.push("= ''");else if(a.operator===i.IsNotEmpty)t.push("!= ''");else if(ge(o))t.push(String(a.value));else if(ve(o))t.push(String(a.value||"0"));else if(Ee(o))if(ke(o,a.operator))fe(o)&&t.push(">=","$__fromTime","AND",n,"<=","$__toTime");else switch(a.value){case"GRAFANA_START_TIME":fe(o)&&t.push("$__fromTime");break;case"GRAFANA_END_TIME":fe(o)&&t.push("$__toTime");break;default:t.push(ce(String(a.value||"TODAY")))}else if(Te(o,a.operator))a.operator===i.Like||a.operator===i.NotLike?t.push(`'%${a.value||""}%'`):t.push(ce(a.value||""));else if(Oe(o,a.operator)){var r;t.push(`(${null===(r=a.value)||void 0===r?void 0:r.map((e=>ce(e.trim()))).join(", ")})`)}else t.push(ce(a.value||""));p&&(t.unshift("NOT","("),t.push(")")),t.unshift("("),s.length>0&&t.unshift(a.condition),t.push(")");const m=ue(t);s.push(m)}return ue(s)},_e=e=>null==e?void 0:e.toLowerCase().startsWith("boolean"),$e=["int","float","decimal"],he=e=>$e.some((t=>null==e?void 0:e.toLowerCase().includes(t))),fe=e=>(null==e?void 0:e.toLowerCase().startsWith("date"))||(null==e?void 0:e.toLowerCase().startsWith("nullable(date")),we=e=>("string"===(e=(e=>e.toLowerCase().replace(/\(/g,"").replace(/\)/g,"").replace(/nullable/g,"").replace(/lowcardinality/g,""))(e.toLowerCase()))||e.startsWith("fixedstring"))&&!(_e(e)||he(e)||fe(e)),xe=e=>e===i.IsNull||e===i.IsNotNull,ge=e=>_e(e),ve=e=>he(e),ke=(e,t)=>fe(e)&&(t===i.WithInGrafanaTimeRange||t===i.OutsideGrafanaTimeRange),Ee=e=>fe(e),Te=(e,t)=>we(e)&&!(t===i.In||t===i.NotIn),Oe=(e,t)=>we(e)&&(t===i.In||t===i.NotIn),Ce=[["timestamp",n.Time],["body",n.LogMessage],["level",n.LogLevel],["labels",n.LogLabels],["traceID",n.TraceId]],Ae=new Map(Ce),Se=new Map(Ce.map((e=>[e[1],e[0]])));var Ie=p(5959),Ne=p.n(Ie);const Le="4.5.1";function Re(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}const De=Le,qe=/^(\d+)(?:\.(\d+))?(?:\.(\d+))?(?:-([0-9A-Za-z\.]+))?/;class je{isGtOrEq(e){const t=new je(e);for(let e=0;e<this.comparable.length;++e){if(this.comparable[e]>t.comparable[e])return!0;if(this.comparable[e]<t.comparable[e])return!1}return!0}isValid(){return(0,k.isNumber)(this.major)}get comparable(){return[this.major,this.minor,this.patch]}constructor(e){Re(this,"major",void 0),Re(this,"minor",void 0),Re(this,"patch",void 0),Re(this,"meta",void 0),this.major=0,this.minor=0,this.patch=0,this.meta="";const t=qe.exec(e);t&&(this.major=Number(t[1]),this.minor=Number(t[2]||0),this.patch=Number(t[3]||0),this.meta=t[4])}}function Pe(e,t){return new je(e).isGtOrEq(t)}function Be(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}function Me(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{},r=Object.keys(s);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(s).filter((function(e){return Object.getOwnPropertyDescriptor(s,e).enumerable})))),r.forEach((function(t){Be(e,t,s[t])}))}return e}function Fe(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),s.push.apply(s,r)}return s}(Object(t)).forEach((function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})),e}const Ue=e=>{var t,s,r,a,n;return((null===(t=e.columns)||void 0===t?void 0:t.length)||0)>0||((null===(s=e.filters)||void 0===s?void 0:s.length)||0)>0||((null===(r=e.orderBy)||void 0===r?void 0:r.length)||0)>0||((null===(a=e.aggregates)||void 0===a?void 0:a.length)||0)>0||((null===(n=e.groupBy)||void 0===n?void 0:n.length)||0)>0},He=e=>{switch(null==e?void 0:e.queryType){case r.Table:return 1;case r.Logs:return 2;case r.TimeSeries:return 0;case r.Traces:var t;return(null===(t=e.meta)||void 0===t?void 0:t.isTraceIdMode)?3:1;default:return 256}},Ge=e=>{switch(e){case r.Table:return 1;case r.Logs:return 2;case r.TimeSeries:return 0;case r.Traces:return 3;default:return 256}},Ve=e=>{switch(e){case 0:return r.TimeSeries;case 1:default:return r.Table;case 2:return r.Logs;case 3:return r.Traces}},ze=(e,t)=>{const s=new Map;t&&t.forEach(((e,t)=>{s.set(e.toLowerCase().trim(),t)}));for(const t of e){var r;if(t.hint)continue;const e=t.name.toLowerCase().trim(),a=(null===(r=t.alias)||void 0===r?void 0:r.toLowerCase().trim())||"",o=s.get(e)||s.get(a);o?t.hint=o:e.includes("time")&&(t.hint=n.Time)}},We=e=>e.toLowerCase().replace(/ /g,"_");var Ke=p(6089);const Qe={QueryEditor:{CodeEditor:{input:()=>".monaco-editor textarea",container:"data-testid-code-editor-container",Expand:"data-testid-code-editor-expand-button"},Format:{label:"Format",tooltip:"Query Type",options:{AUTO:"Auto",TABLE:"Table",TIME_SERIES:"Time Series",LOGS:"Logs",TRACE:"Trace"}},Types:{label:"Query Type",tooltip:"Query Type",options:{SQLEditor:"SQL Editor",QueryBuilder:"Query Builder"},switcher:{title:"Are you sure?",body:"Queries that are too complex for the Query Builder will be altered.",confirmText:"Continue",dismissText:"Cancel"},cannotConvert:{title:"Cannot convert",confirmText:"Yes"}},QueryBuilder:{TYPES:{label:"Show as",tooltip:"Show as",options:{LIST:"Table",AGGREGATE:"Aggregate",TREND:"Time Series"}},DATABASE:{label:"Database",tooltip:"Clickhouse database to query from"},FROM:{label:"Table",tooltip:"Clickhouse table to query from"},SELECT:{label:"Fields",tooltipTable:"List of fields to show",tooltipAggregate:"List of metrics to show. Use any of the given aggregation along with the field",ALIAS:{label:"as",tooltip:"alias"},AddLabel:"Field",RemoveLabel:""},AGGREGATES:{label:"Aggregates",tooltipTable:"Aggregate functions to use",tooltipAggregate:"Aggregate functions to use",ALIAS:{label:"as",tooltip:"alias"},AddLabel:"Aggregate",RemoveLabel:""},WHERE:{label:"Filters",tooltip:"List of filters",AddLabel:"Filter",RemoveLabel:""},GROUP_BY:{label:"Group by",tooltip:"Group the results by specific field"},ORDER_BY:{label:"Order by",tooltip:"Order by field",AddLabel:"Order by",RemoveLabel:""},LIMIT:{label:"Limit",tooltip:"Number of records/results to show."},TIME_FIELD:{label:"Time field",tooltip:"Select the time field for trending over time"},LOGS_VOLUME_TIME_FIELD:{label:"Time field",tooltip:"Select the time field for logs volume histogram. If not selected, the histogram will not be shown"},LOG_LEVEL_FIELD:{label:"Log level field",tooltip:"Select the field to extract log level information from"},PREVIEW:{label:"SQL Preview",tooltip:"SQL Preview. You can safely switch to SQL Editor to customize the generated query"}}},Config:{HttpHeaderConfig:{headerEditor:"config__http-header-config__header-editor",addHeaderButton:"config__http-header-config__add-header-button",removeHeaderButton:"config__http-header-config__remove-header-button",headerSecureSwitch:"config__http-header-config__header-secure-switch",headerNameInput:"config__http-header-config__header-name-input",headerValueInput:"config__http-header-config__header-value-input",forwardGrafanaHeadersSwitch:"config__http-header-config__forward-grafana-headers-switch"},AliasTableConfig:{aliasEditor:"config__alias-table-config__alias-editor",addEntryButton:"config__alias-table-config__add-entry-button",removeEntryButton:"config__alias-table-config__remove-entry-button",targetDatabaseInput:"config__alias-table-config__target-database-input",targetTableInput:"config__alias-table-config__target-table-input",aliasDatabaseInput:"config__alias-table-config__alias-database-input",aliasTableInput:"config__alias-table-config__alias-table-input"}},LogsContextPanel:{alert:"logs-context-panel__alert",LogsContextKey:"logs-context-panel__logs-context-key"},QueryBuilder:{expandBuilderButton:"query-builder__expand-builder-button",LogsQueryBuilder:{LogMessageLikeInput:{input:"query-builder__logs-query-builder__log-message-like-input__input"}},AggregateEditor:{sectionLabel:"query-builder__aggregate-editor__section-label",itemWrapper:"query-builder__aggregate-editor__item-wrapper",itemRemoveButton:"query-builder__aggregate-editor-item-remove-button",addButton:"query-builder__aggregate-editor__add-button"},ColumnsEditor:{multiSelectWrapper:"query-builder__columns-editor__multi-select-wrapper"},GroupByEditor:{multiSelectWrapper:"query-builder__group-by__multi-select-wrapper"},LimitEditor:{input:"query-builder__limit-editor__input"},TraceIdInput:{input:"query-builder__trace-id-input__input"}}},Ye=Qe,Xe=Ke.css`
  display: flex;
  justify-content: flex-start;
  flex-wrap: wrap;
  width: 100%;
`,Je=e=>"db"===(e=e.toLowerCase())||"database"===e||e.includes("data")?"database":e.includes("service")?"building":e.includes("error")||e.includes("warn")||e.includes("critical")||e.includes("fatal")?"exclamation-triangle":e.includes("user")||e.includes("admin")?"user":e.includes("email")?"at":e.includes("file")?"file-alt":e.includes("bug")?"bug":e.includes("search")?"search":e.includes("tag")?"tag-alt":e.includes("span")||e.includes("stack")?"brackets-curly":"host"===e||"hostname"===e||e.includes("host")?"cloud":"url"===e||e.includes("url")?"link":e.includes("container")||e.includes("pod")?"cube":"align-left",Ze=e=>{const{name:t,value:s,primaryColor:r,primaryTextColor:a,secondaryColor:n,secondaryTextColor:o}=e,l={container:Ke.css`
      display: flex;
      justify-content: center;
      align-items: center;
      margin: 0.25em;
      color: ${a}
    `,containerLeft:Ke.css`
      display: flex;
      align-items: center;
      background-color: ${r};
      border-radius: 2px;
      border-top-right-radius: 0;
      border-bottom-right-radius: 0;
  
      padding-top: 0.15em;
      padding-bottom: 0.15em;
      padding-left: 0.25em;
      padding-right: 0.25em;
    `,contextName:Ke.css`
      font-weight: bold;
      padding-left: 0.25em;
      user-select: all;
    `,contextValue:Ke.css`
      background-color: ${n};
      color: ${o};
      border-radius: 2px;
      border-top-left-radius: 0;
      border-bottom-left-radius: 0;
      user-select: all;
      font-family: monospace;

      padding-top: 0.15em;
      padding-bottom: 0.15em;
      padding-left: 0.25em;
      padding-right: 0.25em;
    `};return Ne().createElement("div",{className:l.container,"data-testid":Qe.LogsContextPanel.LogsContextKey},Ne().createElement("div",{className:l.containerLeft},Ne().createElement(C.Icon,{name:Je(t),size:"md"}),Ne().createElement("div",null,"test"),Ne().createElement("span",{className:l.contextName},t)),Ne().createElement("span",{className:l.contextValue},s))},et=e=>{const{columns:t,datasourceUid:s}=e,r=(0,C.useTheme2)();return t&&0!==t.length?Ne().createElement("div",{className:Xe},t.map((e=>Ne().createElement(Ze,{key:e.name,name:e.name,value:e.value,primaryColor:r.colors.secondary.main,primaryTextColor:r.colors.text.primary,secondaryColor:r.colors.background.secondary,secondaryTextColor:r.colors.info.text})))):Ne().createElement(C.Alert,{"data-testid":Qe.LogsContextPanel.alert,title:"",severity:"warning"},Ne().createElement(C.Stack,{direction:"column"},Ne().createElement("div",null,"Unable to match any context columns. Make sure your query returns at least one log context column from your ",Ne().createElement("a",{style:{textDecoration:"underline"},href:`/connections/datasources/edit/${encodeURIComponent(s)}#logs-config`},"ClickHouse Data Source settings"))))};function tt(e,t,s,r,a,n,o){try{var l=e[n](o),i=l.value}catch(e){return void s(e)}l.done?t(i):Promise.resolve(i).then(r,a)}function st(e){return function(){var t=this,s=arguments;return new Promise((function(r,a){var n=e.apply(t,s);function o(e){tt(n,r,a,o,l,"next",e)}function l(e){tt(n,r,a,o,l,"throw",e)}o(void 0)}))}}function rt(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}function at(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{},r=Object.keys(s);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(s).filter((function(e){return Object.getOwnPropertyDescriptor(s,e).enumerable})))),r.forEach((function(t){rt(e,t,s[t])}))}return e}function nt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),s.push.apply(s,r)}return s}(Object(t)).forEach((function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})),e}class ot extends b.DataSourceWithBackend{getDataProvider(e,t){if(this.getSupportedSupplementaryQueryTypes().includes(e)&&e===u.SupplementaryQueryType.LogsVolume){const e=(0,k.cloneDeep)(t),s=function(e){if(e.__interval_ms){let t,s=e.__interval_ms.value;return s>I?(s=864e5,t="1d"):s>S?(s=I,t="1h"):s>A?(s=S,t="1m"):(s=A,t="1s"),{interval:t,intervalMs:s}}return{interval:"$__interval"}}(e.scopedVars);e.interval=s.interval,e.scopedVars.__interval={value:s.interval,text:s.interval},e.hideFromInspector=!0,void 0!==s.intervalMs&&(e.intervalMs=s.intervalMs,e.scopedVars.__interval_ms={value:s.intervalMs,text:s.intervalMs});const r=[];if(e.targets.forEach((t=>{const s=this.getSupplementaryLogsVolumeQuery(e,t);void 0!==s&&r.push(s)})),!r.length)return;return D(this,nt(at({},e),{targets:r}),{range:e.range,targets:e.targets})}}getSupportedSupplementaryQueryTypes(){return[u.SupplementaryQueryType.LogsVolume]}getSupplementaryLogsVolumeQuery(e,t){var o;if(t.editorType!==c.Builder||t.builderOptions.queryType!==r.Logs||t.builderOptions.mode!==s.List||""===t.builderOptions.database||""===t.builderOptions.table)return;const i=ne(t.builderOptions,n.Time);if(void 0===i)return;const p=[],m=[];p.push({name:j(e.scopedVars,i.name),alias:P,hint:n.Time});const u=ne(t.builderOptions,n.LogLevel);if(u){const e=`toString("${u.name}")`;let t;for(t in M)m.push({aggregateType:a.Sum,column:`${e} ${M[t]}`,alias:t})}else m.push({aggregateType:a.Count,column:"*",alias:B});const b=((null===(o=t.builderOptions.filters)||void 0===o?void 0:o.slice())||[]).map((e=>{if(e.hint&&!e.key){const s=ne(t.builderOptions,e.hint);e.key=(null==s?void 0:s.alias)||(null==s?void 0:s.name)||""}return e})),y={database:t.builderOptions.database,table:t.builderOptions.table,queryType:r.TimeSeries,filters:b,columns:p,aggregates:m,orderBy:[{name:"",hint:n.Time,dir:l.ASC}]},d=J(y);return{pluginVersion:De,editorType:c.Builder,builderOptions:y,rawSql:d,refId:""}}getSupplementaryQuery(e,t){}metricFindQuery(e,t){var s=this;return st((function*(){var r,a,n,o;0===s.adHocFiltersStatus&&(s.adHocFiltersStatus=yield s.canUseAdhocFilters());const l=(0,k.isString)(e)?{rawSql:e,editorType:c.SQL}:e;if(l.editorType!==c.SQL&&l.editorType!==c.Builder&&l.editorType)return[];if(!l.rawSql)return[];const i=yield s.runQuery(l,t);if(0===(null===(r=i.fields)||void 0===r?void 0:r.length))return[];var p;if(1===(null==i||null===(a=i.fields)||void 0===a?void 0:a.length))return null==i||null===(p=i.fields[0])||void 0===p?void 0:p.values.map((e=>({text:e,value:e})));const m=null==i||null===(n=i.fields[0])||void 0===n?void 0:n.values;return null==i||null===(o=i.fields[1])||void 0===o?void 0:o.values.map(((e,t)=>({text:e,value:m.get(t)})))}))()}applyTemplateVariables(e,t){let s=e.rawSql||"";const r=(0,b.getTemplateSrv)();if(!this.skipAdHocFilter){const e=null==r?void 0:r.getAdhocFilters(this.name);if(2===this.adHocFiltersStatus&&(null==e?void 0:e.length)>0)throw new Error(`Unable to apply ad hoc filters. Upgrade ClickHouse to >=${this.adHocCHVerReq.major}.${this.adHocCHVerReq.minor} or remove ad hoc filters for the dashboard.`);s=this.adHocFilter.apply(s,e)}return this.skipAdHocFilter=!1,s=this.applyConditionalAll(s,(0,b.getTemplateSrv)().getVariables()),nt(at({},e),{rawSql:this.replace(s,t)||""})}applyConditionalAll(e,t){if(!e)return e;const s="$__conditionalAll(";let r=e.lastIndexOf(s);for(;-1!==r;){const a=this.getMacroArgs(e,r+18-1);if(2!==a.length)return e;const n=a[1].trim(),o=new RegExp(RegExp("(?<=\\$\\{)[\\w\\d]+(?=\\})|(?<=\\$)[\\w\\d]+")).exec(n);let l=a[0];if(o){const e=t.find((e=>e.name===o[0]));let s=null==e?void 0:e.current.value.toString();""!==s&&"$__all"!==s||(l="1=1")}r=(e=e.replace(`${s}${a[0]},${a[1]})`,l)).lastIndexOf(s)}return e}modifyQuery(e,t){var s,r,a;if(e.editorType!==c.Builder||!t.options||!t.options.key||!t.options.value)return e;const o=t.options.key,l=t.frame,p=t.options.value,m=null===(s=e.builderOptions.columns)||void 0===s?void 0:s.find((e=>e.alias===o)),u=null===(r=e.builderOptions.columns)||void 0===r?void 0:r.find((e=>e.name===o)),b=Ae.has(o)?ne(e.builderOptions,Ae.get(o)):void 0,y=((e,t)=>{if(!e||!e.fields||0===e.fields.length)return!1;const s=Se.get(n.LogLabels),r=e.fields.find((e=>e.name===s));if(!r||!r.values||r.values.length<1||!r.values.get(0))return!1;const a=r.values.get(0)||{};return Object.keys(a).includes(t)})(l,o)&&ne(e.builderOptions,n.LogLabels),d=m||u||b||y;let _=(null===(a=e.builderOptions.filters)||void 0===a?void 0:a.slice())||[];"ADD_FILTER"===t.type?(_=_.filter((e=>!("string"===e.type&&(d&&d.hint&&e.hint?e.hint===d.hint:e.key===o)&&(e.operator===i.IsAnything||e.operator===i.Equals||e.operator===i.NotEquals)||e.type.toLowerCase().startsWith("map")&&d&&y&&e.mapKey===o&&(e.operator===i.IsAnything||e.operator===i.Equals||e.operator===i.NotEquals)))),_.push({condition:"AND",key:d&&d.hint?"":o,hint:d&&d.hint?d.hint:void 0,mapKey:y?o:void 0,type:y?"Map(String, String)":"string",filterType:"custom",operator:i.Equals,value:p})):"ADD_FILTER_OUT"===t.type&&(_=_.filter((e=>!("string"===e.type&&(d&&d.hint&&e.hint?e.hint===d.hint:e.key===o)&&"value"in e&&e.value===p&&(e.operator===i.IsAnything||e.operator===i.NotEquals)||"string"===e.type&&(d&&d.hint&&e.hint?e.hint===d.hint:e.key===o)&&(e.operator===i.IsAnything||e.operator===i.Equals)||e.type.toLowerCase().startsWith("map")&&d&&y&&e.mapKey===o&&(e.operator===i.IsAnything||e.operator===i.Equals)))),_.push({condition:"AND",key:d&&d.hint?"":o,hint:d&&d.hint?d.hint:void 0,mapKey:y?o:void 0,type:y?"Map(String, String)":"string",filterType:"custom",operator:i.NotEquals,value:p}));const $=nt(at({},e.builderOptions),{filters:_});return nt(at({},e),{rawSql:J($),builderOptions:$})}getMacroArgs(e,t){const s=[],r=/\(|\)|,/g;let a,n=0,o=1;const l=e.substring(t,e.length);for(;null!==(a=r.exec(l));){const e=a[0];if("("===e?n++:")"===e&&n--,","===e&&1===n&&(s.push(l.substring(o,r.lastIndex-1)),o=r.lastIndex),0===n)return s.push(l.substring(o,r.lastIndex-1)),s}return[]}replace(e,t){return void 0!==e?(0,b.getTemplateSrv)().replace(e,t,this.format):e}format(e){return Array.isArray(e)?`'${e.join("','")}'`:e}getDefaultDatabase(){return this.settings.jsonData.defaultDatabase||"default"}getDefaultTable(){return this.settings.jsonData.defaultTable}getDefaultLogsDatabase(){var e;return null===(e=this.settings.jsonData.logs)||void 0===e?void 0:e.defaultDatabase}getDefaultLogsTable(){var e;return null===(e=this.settings.jsonData.logs)||void 0===e?void 0:e.defaultTable}getDefaultLogsColumns(){const e=new Map,t=this.settings.jsonData.logs;if(!t)return e;const s=t.otelEnabled,r=t.otelVersion,a=K.getVersion(r);return s&&a?a.logColumnMap:(t.timeColumn&&e.set(n.Time,t.timeColumn),t.levelColumn&&e.set(n.LogLevel,t.levelColumn),t.messageColumn&&e.set(n.LogMessage,t.messageColumn),e)}shouldSelectLogContextColumns(){var e;return(null===(e=this.settings.jsonData.logs)||void 0===e?void 0:e.selectContextColumns)||!1}getLogContextColumnNames(){var e;return(null===(e=this.settings.jsonData.logs)||void 0===e?void 0:e.contextColumns)||[]}getLogsOtelVersion(){const e=this.settings.jsonData.logs;return(null==e?void 0:e.otelEnabled)&&e.otelVersion||void 0}getDefaultTraceDatabase(){var e;return null===(e=this.settings.jsonData.traces)||void 0===e?void 0:e.defaultDatabase}getDefaultTraceTable(){var e;return null===(e=this.settings.jsonData.traces)||void 0===e?void 0:e.defaultTable}getDefaultTraceColumns(){const e=new Map,t=this.settings.jsonData.traces;if(!t)return e;const s=t.otelEnabled,r=t.otelVersion,a=K.getVersion(r);return s&&a?a.traceColumnMap:(t.traceIdColumn&&e.set(n.TraceId,t.traceIdColumn),t.spanIdColumn&&e.set(n.TraceSpanId,t.spanIdColumn),t.operationNameColumn&&e.set(n.TraceOperationName,t.operationNameColumn),t.parentSpanIdColumn&&e.set(n.TraceParentSpanId,t.parentSpanIdColumn),t.serviceNameColumn&&e.set(n.TraceServiceName,t.serviceNameColumn),t.durationColumn&&e.set(n.TraceDurationTime,t.durationColumn),t.startTimeColumn&&e.set(n.Time,t.startTimeColumn),t.tagsColumn&&e.set(n.TraceTags,t.tagsColumn),t.serviceTagsColumn&&e.set(n.TraceServiceTags,t.serviceTagsColumn),e)}getTraceOtelVersion(){const e=this.settings.jsonData.traces;return(null==e?void 0:e.otelEnabled)&&e.otelVersion||void 0}getDefaultTraceDurationUnit(){var e;return(null===(e=this.settings.jsonData.traces)||void 0===e?void 0:e.durationUnit)||o.Nanoseconds}fetchDatabases(){var e=this;return st((function*(){return e.fetchData("SHOW DATABASES")}))()}fetchTables(e){var t=this;return st((function*(){const s=e?`SHOW TABLES FROM "${e}"`:"SHOW TABLES";return t.fetchData(s)}))()}fetchUniqueMapKeys(e,t,s){var r=this;return st((function*(){const a=`SELECT DISTINCT arrayJoin(${e}.keys) as keys FROM "${t}"."${s}" LIMIT 1000`;return r.fetchData(a)}))()}fetchEntities(){var e=this;return st((function*(){return e.fetchTables()}))()}fetchFields(e,t){var s=this;return st((function*(){return s.fetchData(`DESC TABLE "${e}"."${t}"`)}))()}fetchColumnsFromTable(e,t){var s=this;return st((function*(){var r;const a=`DESC TABLE ${Boolean(e)?`"${e}".`:""}"${t}"`,n=yield s.runQuery({rawSql:a});if(0===(null===(r=n.fields)||void 0===r?void 0:r.length))return[];return new u.DataFrameView(n).map((e=>({name:e[0],type:e[1],label:e[0],picklistValues:[]})))}))()}fetchColumnsFromAliasTable(e){var t=this;return st((function*(){var s;const r=`SELECT alias, select, "type" FROM ${e}`,a=yield t.runQuery({rawSql:r});if(0===(null===(s=a.fields)||void 0===s?void 0:s.length))return[];return new u.DataFrameView(a).map((e=>({name:e[1],type:e[2],label:e[0],picklistValues:[]})))}))()}getAliasTable(e,t){var s,r;const a=((null===(r=this.settings)||void 0===r||null===(s=r.jsonData)||void 0===s?void 0:s.aliasTables)||[]).find((s=>{const r=!s.targetDatabase||s.targetDatabase===e,a=s.targetTable===t;return r&&a}))||null;if(null===a)return null;const n=a.aliasDatabase||e||null,o=a.aliasTable;return`${Boolean(n)?`"${n}".`:""}"${o}"`}fetchColumns(e,t){var s=this;return st((function*(){const r=s.getAliasTable(e,t);return null!==r?s.fetchColumnsFromAliasTable(r):s.fetchColumnsFromTable(e,t)}))()}fetchData(e){var t=this;return st((function*(){const s=yield t.runQuery({rawSql:e});return t.values(s)}))()}getTimezone(e){if(e.timezone&&"browser"!==e.timezone)return e.timezone;const t=(0,u.getTimeZoneInfo)((0,u.getTimeZone)(),Date.now());return null==t?void 0:t.ianaName}query(e){const t=e.targets.filter((e=>!0!==e.hide)).map((t=>nt(at({},t),{meta:nt(at({},null==t?void 0:t.meta),{timezone:this.getTimezone(e)})})));return super.query(nt(at({},e),{targets:t})).pipe((0,y.map)((t=>((e,t,s)=>(s.data.forEach((s=>{var a,o,p,m;const b=t.targets.find((e=>e.refId===s.refId));if(!b)return;const y=s.fields.find((e=>"traceid"===e.name.toLowerCase()||"trace_id"===e.name.toLowerCase()));if(!y)return;const d={editorType:c.Builder,rawSql:"",builderOptions:{},pluginVersion:De,refId:"Trace ID"};if(b.editorType===c.Builder&&b.builderOptions.queryType===r.Traces)d.builderOptions=Fe(Me({},b.builderOptions),{filters:[],orderBy:[],meta:Fe(Me({},b.builderOptions.meta),{minimized:!0,isTraceIdMode:!0,traceId:"${__value.raw}"})});else{const t=e.getTraceOtelVersion(),s={database:e.getDefaultTraceDatabase()||d.builderOptions.database||e.getDefaultDatabase(),table:e.getDefaultTraceTable()||e.getDefaultTable()||d.builderOptions.table,queryType:r.Traces,columns:[],filters:[],orderBy:[],meta:{minimized:!0,isTraceIdMode:!0,traceId:"${__value.raw}",traceDurationUnit:e.getDefaultTraceDurationUnit(),otelEnabled:Boolean(t),otelVersion:t}},a=e.getDefaultTraceColumns();for(let[e,t]of a)s.columns.push({name:t,hint:e});d.builderOptions=s}const _={editorType:c.Builder,rawSql:"",builderOptions:{},pluginVersion:De,refId:"Trace Logs"};if(b.editorType===c.Builder&&b.builderOptions.queryType===r.Logs)_.builderOptions=Fe(Me({},b.builderOptions),{filters:[{type:"string",operator:i.Equals,filterType:"custom",key:"",hint:n.TraceId,condition:"AND",value:"${__value.raw}"}],orderBy:[{name:"",hint:n.Time,dir:l.ASC}],meta:Fe(Me({},b.builderOptions.meta),{minimized:!0})});else{const t=e.getLogsOtelVersion(),s={database:e.getDefaultLogsDatabase()||_.builderOptions.database||e.getDefaultDatabase(),table:e.getDefaultLogsTable()||e.getDefaultTable()||_.builderOptions.table,queryType:r.Logs,columns:[],orderBy:[{name:"",hint:n.Time,dir:l.ASC}],filters:[{type:"string",operator:i.Equals,filterType:"custom",key:"",hint:n.TraceId,condition:"AND",value:"${__value.raw}"}],meta:{minimized:!0,otelEnabled:Boolean(t),otelVersion:t}},a=e.getDefaultLogsColumns();for(let[e,t]of a)s.columns.push({name:t,hint:e});_.builderOptions=s}const $=t.app!==u.CoreApp.Explore;y.config.links=[],y.config.links.push({title:"View trace",targetBlank:$,url:"",internal:{query:d,datasourceUid:null===(a=d.datasource)||void 0===a?void 0:a.uid,datasourceName:null===(o=d.datasource)||void 0===o?void 0:o.type,panelsState:{trace:{spanId:"${__value.raw}"}}}}),y.config.links.push({title:"View logs",targetBlank:$,url:"",internal:{query:_,datasourceUid:null===(p=_.datasource)||void 0===p?void 0:p.uid,datasourceName:null===(m=_.datasource)||void 0===m?void 0:m.type}})})),s))(this,e,t))))}runQuery(e,t){return new Promise((s=>{const r={targets:[nt(at({},e),{refId:String(Math.random())})],range:t?t.range:(0,b.getTemplateSrv)().timeRange};this.query(r).subscribe((e=>{s(e.data[0]||{fields:[]})}))}))}values(e){var t,s;return 0===(null===(t=e.fields)||void 0===t?void 0:t.length)?[]:null==e||null===(s=e.fields[0])||void 0===s?void 0:s.values.map((e=>e))}getTagKeys(){var e=this;return st((function*(){if((2===e.adHocFiltersStatus||0===e.adHocFiltersStatus)&&(e.adHocFiltersStatus=yield e.canUseAdhocFilters(),2===e.adHocFiltersStatus))return{};const{type:t,frame:s}=yield e.fetchTags();if(0===t)return s.fields.map((e=>({text:e.name})));return new u.DataFrameView(s).map((e=>({text:`${e[2]}.${e[0]}`})))}))()}getTagValues({key:e}){var t=this;return st((function*(){const{type:s}=t.getTagSource();return t.skipAdHocFilter=!0,0===s?t.fetchTagValuesFromQuery(e):t.fetchTagValuesFromSchema(e)}))()}fetchTagValuesFromSchema(e){var t=this;return st((function*(){var s;const{from:r}=t.getTagSource(),[a,n]=e.split("."),o=`select distinct ${n} from ${(null==r?void 0:r.includes("."))?`${r.split(".")[0]}.${a}`:a} limit 1000`,l=yield t.runQuery({rawSql:o});if(0===(null===(s=l.fields)||void 0===s?void 0:s.length))return[];return l.fields[0].values.filter((e=>null!==e)).map((e=>({text:String(e)})))}))()}fetchTagValuesFromQuery(e){var t=this;return st((function*(){const{frame:s}=yield t.fetchTags(),r=s.fields.find((t=>t.name===e));return r?r.values.filter((e=>null!==e)).map((e=>({text:String(e)}))):[]}))()}fetchTags(){var e=this;return st((function*(){const t=e.getTagSource();if(e.skipAdHocFilter=!0,void 0===t.source){const t="SELECT name, type, table FROM system.columns";return{type:1,frame:yield e.runQuery({rawSql:t})}}0===t.type&&e.adHocFilter.setTargetTableFromQuery(t.source);const s=yield e.runQuery({rawSql:t.source});return{type:t.type,frame:s}}))()}getTagSource(){const e="$clickhouse_adhoc_query",t=this.getDefaultDatabase();let s=(0,b.getTemplateSrv)().replace(e);if(s===e&&(0,k.isEmpty)(t))return{type:1,source:void 0};if(s=s===e?t:s,s.toLowerCase().startsWith("select"))return{type:0,source:s};if(!s.includes(".")){return{type:1,source:`SELECT name, type, table FROM system.columns WHERE database IN ('${s}')`,from:s}}const[r,a]=s.split(".");return{type:1,source:`SELECT name, type, table FROM system.columns WHERE database IN ('${r}') AND table = '${a}'`,from:s}}canUseAdhocFilters(){var e=this;return st((function*(){e.skipAdHocFilter=!0;const t=yield e.fetchData("SELECT version()");try{const s=t[0].split("."),r={major:Number.parseInt(s[0],10),minor:Number.parseInt(s[1],10)};return r.major>e.adHocCHVerReq.major||r.major===e.adHocCHVerReq.major&&r.minor>=e.adHocCHVerReq.minor?1:2}catch(e){throw console.error(`Unable to parse ClickHouse version: ${e}`),e}}))()}getLogContextColumnsFromLogRow(e){const t=this.getLogContextColumnNames(),s=[];for(let r of t){const t=r.includes("['")&&r.includes("']");let a="",n="";t&&(a=r.substring(0,r.indexOf("[")),n=r.substring(r.indexOf("['")+2,r.lastIndexOf("']")));const o=e.dataFrame.fields.find((e=>e.name===r||t&&(e.name===a||e.name===`arrayElement(${a}, '${n}')`)));if(!o)continue;let l,i=o.values.get(e.rowIndex);(i&&"other"===o.type&&t&&(i=i[n]),i)&&(l=t?`${a}['${n}']`:r,s.push({name:l,value:i}))}return s}getLogRowContext(e,t,s,r){var a=this;return st((function*(){if(!s)throw new Error("Missing query for log context");if(!t||!t.direction||void 0===t.limit)throw new Error("Missing log context options for query");if(s.editorType===c.SQL||!s.builderOptions)throw new Error("Log context feature only works for builder queries");const r=(0,k.cloneDeep)(s);r.refId="";const o=r.builderOptions;if(o.limit=t.limit,!ne(o,n.Time))throw new Error("Missing time column for log context");o.orderBy=[],o.orderBy.push({name:"",hint:n.Time,dir:t.direction===u.LogRowContextQueryDirection.Forward?l.ASC:l.DESC}),o.filters=[],o.filters.push({operator:t.direction===u.LogRowContextQueryDirection.Forward?i.GreaterThanOrEqual:i.LessThanOrEqual,filterType:"custom",hint:n.Time,key:"",value:`fromUnixTimestamp64Nano(${e.timeEpochNs})`,type:"datetime",condition:"AND"});const p=a.getLogContextColumnsFromLogRow(e);if(p.length<1)throw new Error("Unable to match any log context columns");const m=p.map((e=>({operator:i.Equals,filterType:"custom",key:e.name,value:e.value,type:"string",condition:"AND"})));o.filters.push(...m),r.rawSql=J(o);const b={targets:[r]};return yield(0,y.firstValueFrom)(a.query(b))}))()}showContextToggle(e){return!0}getLogRowContextUi(e,t,s){const r=this.getLogContextColumnsFromLogRow(e);return(0,Ie.createElement)(et,{columns:r,datasourceUid:this.uid})}constructor(e){super(e),rt(this,"annotations",{}),rt(this,"settings",void 0),rt(this,"adHocFilter",void 0),rt(this,"skipAdHocFilter",!1),rt(this,"adHocFiltersStatus",0),rt(this,"adHocCHVerReq",{major:22,minor:7}),this.settings=e,this.adHocFilter=new v}}var lt,it;!function(e){e[e.query=0]="query",e[e.schema=1]="schema"}(lt||(lt={})),function(e){e[e.none=0]="none",e[e.enabled=1]="enabled",e[e.disabled=2]="disabled"}(it||(it={}));const ct=({hasCert:e,label:t,onChange:s,onClick:r,placeholder:a})=>Ne().createElement(C.Field,{label:t},e?Ne().createElement(Ne().Fragment,null,Ne().createElement(C.Input,{type:"text",disabled:!0,value:"configured",width:24}),Ne().createElement(C.Button,{variant:"secondary",onClick:r,style:{marginLeft:4}},"Reset")):Ne().createElement(C.TextArea,{rows:7,onChange:s,placeholder:a,required:!0}));var pt;!function(e){e.Native="native",e.Http="http"}(pt||(pt={}));var mt=p(8449);function ut(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}function bt(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{},r=Object.keys(s);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(s).filter((function(e){return Object.getOwnPropertyDescriptor(s,e).enumerable})))),r.forEach((function(t){ut(e,t,s[t])}))}return e}function yt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),s.push.apply(s,r)}return s}(Object(t)).forEach((function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})),e}const dt=({children:e,title:t,description:s,isCollapsible:r=!1,isInitiallyOpen:a=!0,kind:n="section",className:o})=>{const{colors:l,typography:i,spacing:c}=(0,C.useTheme2)(),[p,m]=(0,Ie.useState)(!r||a),u=p?"angle-up":"angle-down",b="sub-section"===n,y=`${p?"Collapse":"Expand"} section ${t}`,d={header:(0,Ke.css)({display:"flex",justifyContent:"space-between",alignItems:"center"}),title:(0,Ke.css)({margin:0}),subtitle:(0,Ke.css)({margin:0,fontWeight:i.fontWeightRegular}),descriptionText:(0,Ke.css)(yt(bt({marginTop:c(b?.25:.5),marginBottom:0},i.bodySmall),{color:l.text.secondary})),content:(0,Ke.css)({marginTop:c(2)})};return Ne().createElement("div",{className:o},Ne().createElement("div",{className:d.header},"section"===n?Ne().createElement("h3",{className:d.title},t):Ne().createElement("h6",{className:d.subtitle},t),r&&Ne().createElement(C.IconButton,{name:u,onClick:()=>m(!p),type:"button",size:"xl","aria-label":y})),s&&Ne().createElement("p",{className:d.descriptionText},s),p&&Ne().createElement("div",{className:d.content},e))};function _t(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}function $t(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),s.push.apply(s,r)}return s}(Object(t)).forEach((function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})),e}function ht(e,t){if(null==e)return{};var s,r,a=function(e,t){if(null==e)return{};var s,r,a={},n=Object.keys(e);for(r=0;r<n.length;r++)s=n[r],t.indexOf(s)>=0||(a[s]=e[s]);return a}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(r=0;r<n.length;r++)s=n[r],t.indexOf(s)>=0||Object.prototype.propertyIsEnumerable.call(e,s)&&(a[s]=e[s])}return a}const ft=e=>{var{children:t}=e,s=ht(e,["children"]);return Ne().createElement(dt,$t(function(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{},r=Object.keys(s);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(s).filter((function(e){return Object.getOwnPropertyDescriptor(s,e).enumerable})))),r.forEach((function(t){_t(e,t,s[t])}))}return e}({},s),{kind:"section"}),t)};function wt(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}function xt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),s.push.apply(s,r)}return s}(Object(t)).forEach((function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})),e}function gt(e,t){if(null==e)return{};var s,r,a=function(e,t){if(null==e)return{};var s,r,a={},n=Object.keys(e);for(r=0;r<n.length;r++)s=n[r],t.indexOf(s)>=0||(a[s]=e[s]);return a}(e,t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);for(r=0;r<n.length;r++)s=n[r],t.indexOf(s)>=0||Object.prototype.propertyIsEnumerable.call(e,s)&&(a[s]=e[s])}return a}const vt=e=>{var{children:t}=e,s=gt(e,["children"]);return Ne().createElement(dt,xt(function(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{},r=Object.keys(s);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(s).filter((function(e){return Object.getOwnPropertyDescriptor(s,e).enumerable})))),r.forEach((function(t){wt(e,t,s[t])}))}return e}({},s),{kind:"sub-section"}),t)};function kt(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}function Et(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{},r=Object.keys(s);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(s).filter((function(e){return Object.getOwnPropertyDescriptor(s,e).enumerable})))),r.forEach((function(t){kt(e,t,s[t])}))}return e}function Tt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),s.push.apply(s,r)}return s}(Object(t)).forEach((function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})),e}const Ot=({dataSourceName:e,docsLink:t,hasRequiredFields:s=!0,className:r})=>{const a=(0,C.useTheme2)(),n={container:(0,Ke.css)({p:{margin:0},"p + p":{marginTop:a.spacing(2)}}),text:(0,Ke.css)(Tt(Et({},a.typography.body),{color:a.colors.text.secondary,a:(0,Ke.css)({color:a.colors.text.link,textDecoration:"underline","&:hover":{textDecoration:"none"}})}))};return Ne().createElement("div",{className:(0,Ke.cx)(n.container,r)},Ne().createElement("p",{className:n.text},"Before you can use the ",e," data source, you must configure it below or in the config file. For detailed instructions,"," ",Ne().createElement("a",{href:t,target:"_blank",rel:"noreferrer"},"view the documentation"),"."),s&&Ne().createElement("p",{className:n.text},Ne().createElement("i",null,"Fields marked with * are required")))};function Ct(){const e=(0,C.useTheme2)();return Pe(b.config.buildInfo.version,"10.1.0")?Ne().createElement(C.Divider,null):Ne().createElement("div",{style:{borderTop:`1px solid ${e.colors.border.weak}`,margin:e.spacing(2,0),width:"100%"}})}const At={components:{Config:{ConfigEditor:{serverAddress:{label:"Server address",placeholder:"Server address",tooltip:"ClickHouse host address",error:"Server address required"},serverPort:{label:"Server port",insecureNativePort:"9000",insecureHttpPort:"8123",secureNativePort:"9440",secureHttpPort:"8443",tooltip:"ClickHouse server port",error:"Port is required"},path:{label:"HTTP URL Path",tooltip:"Additional URL path for HTTP requests",placeholder:"additional-path"},protocol:{label:"Protocol",tooltip:"Native or HTTP for server protocol"},username:{label:"Username",placeholder:"default",tooltip:"ClickHouse username"},password:{label:"Password",placeholder:"password",tooltip:"ClickHouse password"},tlsSkipVerify:{label:"Skip TLS Verify",tooltip:"Skip TLS Verify"},tlsClientAuth:{label:"TLS Client Auth",tooltip:"TLS Client Auth"},tlsAuthWithCACert:{label:"With CA Cert",tooltip:"Needed for verifying self-signed TLS Certs"},tlsCACert:{label:"CA Cert",placeholder:"CA Cert. Begins with -----BEGIN CERTIFICATE-----"},tlsClientCert:{label:"Client Cert",placeholder:"Client Cert. Begins with -----BEGIN CERTIFICATE-----"},tlsClientKey:{label:"Client Key",placeholder:"Client Key. Begins with -----BEGIN RSA PRIVATE KEY-----"},secure:{label:"Secure Connection",tooltip:"Toggle on if the connection is secure"},secureSocksProxy:{label:"Enable Secure Socks Proxy",tooltip:"Enable proxying the datasource connection through the secure socks proxy to a different network."}},HttpHeadersConfig:{title:"HTTP Headers",label:"Custom HTTP Headers",description:"Add Custom HTTP headers when querying the database",headerNameLabel:"Header Name",headerNamePlaceholder:"X-Custom-Header",insecureHeaderValueLabel:"Header Value",secureHeaderValueLabel:"Secure Header Value",secureLabel:"Secure",addHeaderLabel:"Add Header",forwardGrafanaHeaders:{label:"Forward Grafana HTTP Headers",tooltip:"Forward Grafana HTTP Headers to datasource."}},AliasTableConfig:{title:"Column Alias Tables",descriptionParts:["Provide alias tables with a","(`alias` String, `select` String, `type` String)","schema to use as a source for column selection."],addTableLabel:"Add Table",targetDatabaseLabel:"Target Database",targetDatabasePlaceholder:"(optional)",targetTableLabel:"Target Table",aliasDatabaseLabel:"Alias Database",aliasDatabasePlaceholder:"(optional)",aliasTableLabel:"Alias Table"},DefaultDatabaseTableConfig:{title:"Default DB and table",database:{label:"Default database",description:"the default database used by the query builder",name:"defaultDatabase",placeholder:"default"},table:{label:"Default table",description:"the default table used by the query builder",name:"defaultTable",placeholder:"table"}},QuerySettingsConfig:{title:"Query settings",dialTimeout:{label:"Dial Timeout (seconds)",tooltip:"Timeout in seconds for connection",name:"dialTimeout",placeholder:"10"},queryTimeout:{label:"Query Timeout (seconds)",tooltip:"Timeout in seconds for read queries",name:"queryTimeout",placeholder:"60"},validateSql:{label:"Validate SQL",tooltip:"Validate SQL in the editor."}},TracesConfig:{title:"Traces configuration",description:"(Optional) Default settings for trace queries",defaultDatabase:{label:"Default trace database",description:"the default database used by the trace query builder",name:"defaultDatabase",placeholder:"default"},defaultTable:{label:"Default trace table",description:"the default table used by the trace query builder",name:"defaultTable"},columns:{title:"Default columns",description:"Default columns for trace queries. Leave empty to disable.",traceId:{label:"Trace ID column",tooltip:"Column for the trace ID"},spanId:{label:"Span ID column",tooltip:"Column for the span ID"},parentSpanId:{label:"Parent Span ID column",tooltip:"Column for the parent span ID"},serviceName:{label:"Service Name column",tooltip:"Column for the service name"},operationName:{label:"Operation Name column",tooltip:"Column for the operation name"},startTime:{label:"Start Time column",tooltip:"Column for the start time"},durationTime:{label:"Duration Time column",tooltip:"Column for the duration time"},tags:{label:"Tags column",tooltip:"Column for the trace tags"},serviceTags:{label:"Service Tags column",tooltip:"Column for the service tags"}}},LogsConfig:{title:"Logs configuration",description:"(Optional) default settings for log queries",defaultDatabase:{label:"Default log database",description:"the default database used by the logs query builder",name:"defaultDatabase",placeholder:"default"},defaultTable:{label:"Default log table",description:"the default table used by the logs query builder",name:"defaultTable"},columns:{title:"Default columns",description:"Default columns for log queries. Leave empty to disable.",time:{label:"Time column",tooltip:"Column for the log timestamp"},level:{label:"Log Level column",tooltip:"Column for the log level"},message:{label:"Log Message column",tooltip:"Column for log message"}},contextColumns:{title:"Context columns",description:"These columns are used to narrow down a single log row to its original service/container/pod source. At least one is required for the log context feature to work.",selectContextColumns:{label:"Auto-Select Columns",tooltip:"When enabled, will always include context columns in log queries"},columns:{label:"Context Columns",tooltip:"Comma separated list of column names to use for identifying a log's source",placeholder:"Column name (enter key to add)"}}}},EditorTypeSwitcher:{label:"Editor Type",tooltip:"Switches between the raw SQL Editor and the Query Builder.",switcher:{title:"Are you sure?",body:"Queries that are too complex for the Query Builder will be altered.",confirmText:"Continue",dismissText:"Cancel"},cannotConvert:{title:"Cannot convert",message:"Do you want to delete your current query and use the query builder?",confirmText:"Yes"}},expandBuilderButton:{label:"Show full query",tooltip:"Shows the full query builder"},QueryTypeSwitcher:{label:"Query Type",tooltip:"Sets the layout for the query builder",sqlTooltip:"Sets the panel type for explore view"},DatabaseSelect:{label:"Database",tooltip:"ClickHouse database to query from",empty:"<select database>"},TableSelect:{label:"Table",tooltip:"ClickHouse table to query from",empty:"<select table>"},ColumnsEditor:{label:"Columns",tooltip:"A list of columns to include in the query"},OtelVersionSelect:{label:"Use OTel",tooltip:"Enables Open Telemetry schema versioning"},LimitEditor:{label:"Limit",tooltip:"Limits the number of rows returned by the query"},SqlPreview:{label:"SQL Preview",tooltip:"Preview of the generated SQL. You can safely switch to SQL Editor to customize the generated query"},AggregatesEditor:{label:"Aggregates",tooltip:"Aggregate functions to use",aliasLabel:"as",aliasTooltip:"alias for this aggregate function",addLabel:"Aggregate"},OrderByEditor:{label:"Order By",tooltip:"Order by column",addLabel:"Order By"},FilterEditor:{label:"Filters",tooltip:"List of filters",addLabel:"Filter",mapKeyPlaceholder:"map key"},GroupByEditor:{label:"Group By",tooltip:"Group the results by specific column"},LogsQueryBuilder:{logTimeColumn:{label:"Time",tooltip:"Column that contains the log timestamp"},logLevelColumn:{label:"Log Level",tooltip:"Column that contains the log level"},logMessageColumn:{label:"Message",tooltip:"Column that contains the log message"},logLabelsColumn:{label:"Labels",tooltip:"A column with a key/value structure for log labels"},liveView:{label:"Live View",tooltip:"Enable to update logs in real time"},logMessageFilter:{label:"Message Filter",tooltip:"Applies a LIKE filter to the log message body",clearButton:"Clear"},logLevelFilter:{label:"Level Filter",tooltip:"Applies a filter to the log level"}},TimeSeriesQueryBuilder:{simpleQueryModeLabel:"Simple",aggregateQueryModeLabel:"Aggregate",builderModeLabel:"Builder Mode",builderModeTooltip:"Switches the query builder between the simple and aggregate modes",timeColumn:{label:"Time",tooltip:"Column to use for the time series"}},TableQueryBuilder:{simpleQueryModeLabel:"Simple",aggregateQueryModeLabel:"Aggregate",builderModeLabel:"Builder Mode",builderModeTooltip:"Switches the query builder between the simple and aggregate modes"},TraceQueryBuilder:{traceIdModeLabel:"Trace ID",traceSearchModeLabel:"Trace Search",traceModeLabel:"Trace Mode",traceModeTooltip:"Switches between trace ID and trace search mode",columnsSection:"Columns",filtersSection:"Filters",columns:{traceId:{label:"Trace ID Column",tooltip:"Column that contains the trace ID"},spanId:{label:"Span ID Column",tooltip:"Column that contains the span ID"},parentSpanId:{label:"Parent Span ID Column",tooltip:"Column that contains the parent span ID"},serviceName:{label:"Service Name Column",tooltip:"Column that contains the service name"},operationName:{label:"Operation Name Column",tooltip:"Column that contains the operation name"},startTime:{label:"Start Time Column",tooltip:"Column that contains the start time"},durationTime:{label:"Duration Time Column",tooltip:"Column that contains the duration time"},durationUnit:{label:"Duration Unit",tooltip:"The unit of time used for the duration time"},tags:{label:"Tags Column",tooltip:"Column that contains the trace tags"},serviceTags:{label:"Service Tags Column",tooltip:"Column that contains the service tags"},traceIdFilter:{label:"Trace ID",tooltip:"filter by a specific trace ID"}}}},types:{EditorType:{sql:"SQL Editor",builder:"Query Builder"},QueryType:{table:"Table",logs:"Logs",timeseries:"Time Series",traces:"Traces"},ColumnHint:{[n.Time]:"Time",[n.LogLevel]:"Level",[n.LogMessage]:"Message",[n.LogLabels]:"Labels",[n.TraceId]:"Trace ID",[n.TraceSpanId]:"Span ID",[n.TraceParentSpanId]:"Parent Span ID",[n.TraceServiceName]:"Service Name",[n.TraceOperationName]:"Operation Name",[n.TraceDurationTime]:"Duration Time",[n.TraceTags]:"Tags",[n.TraceServiceTags]:"Service Tags",[n.TraceStatusCode]:"Status Code"}}},St=e=>{const{defaultDatabase:t,defaultTable:s,onDefaultDatabaseChange:r,onDefaultTableChange:a}=e,n=At.components.Config.DefaultDatabaseTableConfig;return Ne().createElement(ft,{title:n.title},Ne().createElement(C.Field,{label:n.database.label,description:n.database.description},Ne().createElement(C.Input,{name:n.database.name,width:40,value:t||"",onChange:r,label:n.database.label,"aria-label":n.database.label,placeholder:n.database.placeholder,type:"text"})),Ne().createElement(C.Field,{label:n.table.label,description:n.table.description},Ne().createElement(C.Input,{name:n.table.name,width:40,value:s||"",onChange:a,label:n.table.label,"aria-label":n.table.label,placeholder:n.table.placeholder,type:"text"})))},It=e=>{const{dialTimeout:t,queryTimeout:s,validateSql:r,onDialTimeoutChange:a,onQueryTimeoutChange:n,onValidateSqlChange:o}=e,l=At.components.Config.QuerySettingsConfig;return Ne().createElement(ft,{title:l.title},Ne().createElement(C.Field,{label:l.dialTimeout.label,description:l.dialTimeout.tooltip},Ne().createElement(C.Input,{name:l.dialTimeout.name,width:40,value:t||"",onChange:a,label:l.dialTimeout.label,"aria-label":l.dialTimeout.label,placeholder:l.dialTimeout.placeholder,type:"number"})),Ne().createElement(C.Field,{label:l.queryTimeout.label,description:l.queryTimeout.tooltip},Ne().createElement(C.Input,{name:l.queryTimeout.name,width:40,value:s||"",onChange:n,label:l.queryTimeout.label,"aria-label":l.queryTimeout.label,placeholder:l.queryTimeout.placeholder,type:"number"})),Ne().createElement(C.Field,{label:l.validateSql.label,description:l.validateSql.tooltip},Ne().createElement(C.Switch,{className:"gf-form",value:r||!1,onChange:o,role:"checkbox"})))},Nt=e=>{const{enabled:t,onEnabledChange:s,selectedVersion:r,onVersionChange:a,wide:n}=e,{label:o,tooltip:l}=At.components.OtelVersionSelect,i=K.versions.map((e=>({label:e.name,value:e.version})));(0,Ie.useEffect)((()=>{""!==r&&K.getVersion(r)||a(K.getLatestVersion().version)}),[r,a]);const c=(0,C.useTheme)(),p={padding:`0 ${c.spacing.sm}`,height:`${c.spacing.formInputHeight}px`,display:"flex",alignItems:"center"};return Ne().createElement("div",{className:"gf-form"},Ne().createElement(C.InlineFormLabel,{width:n?12:8,className:"query-keyword",tooltip:l},o),Ne().createElement("div",{style:p},Ne().createElement(C.Switch,{className:"gf-form",value:t,onChange:e=>s(e.currentTarget.checked),role:"checkbox"})),Ne().createElement(C.Select,{disabled:!t,options:i,width:20,onChange:e=>a(e.value),value:r,menuPlacement:"bottom"}))};function Lt(e){const{label:t,tooltip:s,placeholder:r,disabled:a,value:n,onChange:o}=e;return Ne().createElement("div",{className:"gf-form"},Ne().createElement(C.InlineFormLabel,{width:12,className:"query-keyword",tooltip:s||t},t),Ne().createElement(C.Input,{disabled:a,width:30,value:n,onChange:e=>o(e.currentTarget.value),placeholder:r}))}const Rt={Common:{check:Ke.css`
      margin-top: 5px;
    `,wrapper:Ke.css`
      position: relative;
      width: 100%;
    `,smallBtn:Ke.css`
      margin-top: 5px;
      margin-inline: 5px;
    `,selectWrapper:Ke.css`
      width: 100%;
    `,inlineSelect:Ke.css`
      margin-right: 5px;
    `,firstLabel:Ke.css`
      margin-right: 5px;
    `,expand:Ke.css`
      position: absolute;
      top: 2px;
      left: 6px;
      z-index: 100;
      color: gray;
    `},ConfigEditor:{container:Ke.css`
      justify-content: space-between;
      h5 {
        line-height: 34px;
        margin-bottom: 5px;
      }
      button {
        margin-right: 5px;
      }
    `,wide:Ke.css`
      width: 75%;
    `,subHeader:Ke.css`
      padding: 5px 0 5px 0;
    `},QueryEditor:{queryType:Ke.css`
      justify-content: space-between;
      span {
        display: flex;
      }
    `,inlineField:Ke.css`
      margin-left: 7px;
    `},FormatSelector:{formatSelector:Ke.css`
      display: flex;
    `},VariablesEditor:{}},Dt=e=>{const{value:t,onChange:s,label:r,tooltip:a,inline:n,wide:o}=e,l=(0,C.useTheme)(),i={padding:`0 ${l.spacing.sm}`,height:`${l.spacing.formInputHeight}px`,display:"flex",alignItems:"center"},c="query-keyword "+(n?Rt.QueryEditor.inlineField:"");return Ne().createElement("div",{className:"gf-form"},Ne().createElement(C.InlineFormLabel,{width:o?12:8,className:c,tooltip:a},r),Ne().createElement("div",{style:i},Ne().createElement(C.Switch,{className:"gf-form",value:t,onChange:e=>s(e.currentTarget.checked)})))},qt=e=>{const{onDefaultDatabaseChange:t,onDefaultTableChange:s,onOtelEnabledChange:r,onOtelVersionChange:a,onTimeColumnChange:o,onLevelColumnChange:l,onMessageColumnChange:i,onSelectContextColumnsChange:c,onContextColumnsChange:p}=e;let{defaultDatabase:m,defaultTable:u,otelEnabled:b,otelVersion:y,timeColumn:d,levelColumn:_,messageColumn:$,selectContextColumns:h,contextColumns:f}=e.logsConfig||{};const w=At.components.Config.LogsConfig,x=K.getVersion(y);b&&x&&(d=x.logColumnMap.get(n.Time),_=x.logColumnMap.get(n.LogLevel),$=x.logColumnMap.get(n.LogMessage));return Ne().createElement(ft,{title:w.title,description:w.description},Ne().createElement("div",{id:"logs-config"}),Ne().createElement(C.Field,{label:w.defaultDatabase.label,description:w.defaultDatabase.description},Ne().createElement(C.Input,{name:w.defaultDatabase.name,width:40,value:m||"",onChange:e=>t(e.currentTarget.value),label:w.defaultDatabase.label,"aria-label":w.defaultDatabase.label,placeholder:w.defaultDatabase.placeholder})),Ne().createElement(C.Field,{label:w.defaultTable.label,description:w.defaultTable.description},Ne().createElement(C.Input,{name:w.defaultTable.name,width:40,value:u||"",onChange:e=>s(e.currentTarget.value),label:w.defaultTable.label,"aria-label":w.defaultTable.label,placeholder:U})),Ne().createElement(vt,{title:w.columns.title,description:w.columns.description},Ne().createElement(Nt,{enabled:b||!1,selectedVersion:y||"",onEnabledChange:r,onVersionChange:a,wide:!0}),Ne().createElement(Lt,{disabled:b,label:w.columns.time.label,placeholder:We(w.columns.time.label),tooltip:w.columns.time.tooltip,value:d||"",onChange:o}),Ne().createElement(Lt,{disabled:b,label:w.columns.level.label,placeholder:We(w.columns.level.label),tooltip:w.columns.level.tooltip,value:_||"",onChange:l}),Ne().createElement(Lt,{disabled:b,label:w.columns.message.label,placeholder:We(w.columns.message.label),tooltip:w.columns.message.tooltip,value:$||"",onChange:i})),Ne().createElement("br",null),Ne().createElement(vt,{title:w.contextColumns.title,description:w.contextColumns.description},Ne().createElement(Dt,{label:w.contextColumns.selectContextColumns.label,tooltip:w.contextColumns.selectContextColumns.tooltip,value:h||!1,onChange:c,wide:!0}),Ne().createElement("div",{className:"gf-form"},Ne().createElement(C.InlineFormLabel,{width:12,className:"query-keyword",tooltip:w.contextColumns.columns.tooltip},w.contextColumns.columns.label),Ne().createElement(C.TagsInput,{placeholder:w.contextColumns.columns.placeholder,tags:f||[],onChange:e=>p(e.map((e=>e.trim())).filter((e=>e))),width:60}))))},jt=[{label:o.Seconds,value:o.Seconds},{label:o.Milliseconds,value:o.Milliseconds},{label:o.Microseconds,value:o.Microseconds},{label:o.Nanoseconds,value:o.Nanoseconds}],Pt=e=>{const{unit:t,onChange:s,disabled:r,inline:a}=e,{label:n,tooltip:o}=At.components.TraceQueryBuilder.columns.durationUnit;return Ne().createElement("div",{className:"gf-form"},Ne().createElement(C.InlineFormLabel,{width:12,className:`query-keyword ${a?Rt.QueryEditor.inlineField:""}`,tooltip:o},n),Ne().createElement(C.Select,{disabled:r,options:jt,value:t,onChange:e=>s(e.value),width:a?25:30,menuPlacement:"bottom"}))},Bt=e=>{const{onDefaultDatabaseChange:t,onDefaultTableChange:s,onOtelEnabledChange:r,onOtelVersionChange:a,onTraceIdColumnChange:l,onSpanIdColumnChange:i,onOperationNameColumnChange:c,onParentSpanIdColumnChange:p,onServiceNameColumnChange:m,onDurationColumnChange:u,onDurationUnitChange:b,onStartTimeColumnChange:y,onTagsColumnChange:d,onServiceTagsColumnChange:_}=e;let{defaultDatabase:$,defaultTable:h,otelEnabled:f,otelVersion:w,traceIdColumn:x,spanIdColumn:g,operationNameColumn:v,parentSpanIdColumn:k,serviceNameColumn:E,durationColumn:T,durationUnit:O,startTimeColumn:A,tagsColumn:S,serviceTagsColumn:I}=e.tracesConfig||{};const N=At.components.Config.TracesConfig,L=K.getVersion(w);return f&&L&&(A=L.traceColumnMap.get(n.Time),x=L.traceColumnMap.get(n.TraceId),g=L.traceColumnMap.get(n.TraceSpanId),k=L.traceColumnMap.get(n.TraceParentSpanId),E=L.traceColumnMap.get(n.TraceServiceName),v=L.traceColumnMap.get(n.TraceOperationName),T=L.traceColumnMap.get(n.TraceDurationTime),S=L.traceColumnMap.get(n.TraceTags),I=L.traceColumnMap.get(n.TraceServiceTags),O=L.traceDurationUnit.toString()),Ne().createElement(ft,{title:N.title,description:N.description},Ne().createElement("div",{id:"traces-config"}),Ne().createElement(C.Field,{label:N.defaultDatabase.label,description:N.defaultDatabase.description},Ne().createElement(C.Input,{name:N.defaultDatabase.name,width:40,value:$||"",onChange:e=>t(e.currentTarget.value),label:N.defaultDatabase.label,"aria-label":N.defaultDatabase.label,placeholder:N.defaultDatabase.placeholder})),Ne().createElement(C.Field,{label:N.defaultTable.label,description:N.defaultTable.description},Ne().createElement(C.Input,{name:N.defaultTable.name,width:40,value:h||"",onChange:e=>s(e.currentTarget.value),label:N.defaultTable.label,"aria-label":N.defaultTable.label,placeholder:H})),Ne().createElement(vt,{title:N.columns.title,description:N.columns.description},Ne().createElement(Nt,{enabled:f||!1,selectedVersion:w||"",onEnabledChange:r,onVersionChange:a,wide:!0}),Ne().createElement(Lt,{disabled:f,label:N.columns.traceId.label,placeholder:We(N.columns.traceId.label),tooltip:N.columns.traceId.tooltip,value:x||"",onChange:l}),Ne().createElement(Lt,{disabled:f,label:N.columns.spanId.label,placeholder:We(N.columns.spanId.label),tooltip:N.columns.spanId.tooltip,value:g||"",onChange:i}),Ne().createElement(Lt,{disabled:f,label:N.columns.operationName.label,placeholder:We(N.columns.operationName.label),tooltip:N.columns.operationName.tooltip,value:v||"",onChange:c}),Ne().createElement(Lt,{disabled:f,label:N.columns.parentSpanId.label,placeholder:We(N.columns.parentSpanId.label),tooltip:N.columns.parentSpanId.tooltip,value:k||"",onChange:p}),Ne().createElement(Lt,{disabled:f,label:N.columns.serviceName.label,placeholder:We(N.columns.serviceName.label),tooltip:N.columns.serviceName.tooltip,value:E||"",onChange:m}),Ne().createElement(Lt,{disabled:f,label:N.columns.durationTime.label,placeholder:We(N.columns.durationTime.label),tooltip:N.columns.durationTime.tooltip,value:T||"",onChange:u}),Ne().createElement(Pt,{disabled:f,unit:O||o.Nanoseconds,onChange:b}),Ne().createElement(Lt,{disabled:f,label:N.columns.startTime.label,placeholder:We(N.columns.startTime.label),tooltip:N.columns.startTime.tooltip,value:A||"",onChange:y}),Ne().createElement(Lt,{disabled:f,label:N.columns.tags.label,placeholder:We(N.columns.tags.label),tooltip:N.columns.tags.tooltip,value:S||"",onChange:d}),Ne().createElement(Lt,{disabled:f,label:N.columns.serviceTags.label,placeholder:We(N.columns.serviceTags.label),tooltip:N.columns.serviceTags.tooltip,value:I||"",onChange:_})))},Mt=e=>{const{secureFields:t,onHttpHeadersChange:s}=e,r=Ut(t),[a,n]=(0,Ie.useState)(e.headers||[]),[o,l]=(0,Ie.useState)(e.forwardGrafanaHeaders||!1),i=At.components.Config.HttpHeadersConfig,c=Ye.Config.HttpHeaderConfig;return Ne().createElement(ft,{title:i.title},Ne().createElement(C.Field,{label:i.label,description:i.description},Ne().createElement(Ne().Fragment,null,a.map(((e,t)=>Ne().createElement(Ft,{key:e.name+t,name:e.name,value:e.value,secure:e.secure,isSecureConfigured:r.has(e.name),onHeaderChange:e=>((e,t)=>{const r=a.slice();t.name=t.name.trim(),r[e]=t,n(r),s(r)})(t,e),onRemove:()=>(e=>{const t=a.slice();t.splice(e,1),n(t),s(t)})(t)}))),Ne().createElement(C.Button,{"data-testid":c.addHeaderButton,icon:"plus-circle",variant:"secondary",size:"sm",onClick:()=>n([...a,{name:"",value:"",secure:!1}]),className:Rt.Common.smallBtn},i.addHeaderLabel))),Ne().createElement(C.Field,{label:i.forwardGrafanaHeaders.label,description:i.forwardGrafanaHeaders.tooltip},Ne().createElement(C.Switch,{"data-testid":c.forwardGrafanaHeadersSwitch,className:"gf-form",value:o,onChange:t=>(t=>{l(t),e.onForwardGrafanaHeadersChange(t)})(t.currentTarget.checked)})))},Ft=e=>{const{onHeaderChange:t,onRemove:s}=e,[r,a]=(0,Ie.useState)(e.name),[n,o]=(0,Ie.useState)(e.value),[l,i]=(0,Ie.useState)(e.secure),[c,p]=(0,Ie.useState)(e.isSecureConfigured),m=At.components.Config.HttpHeadersConfig,u=Ye.Config.HttpHeaderConfig,b=()=>{t({name:r,value:n,secure:l})};let y;y=l?Ne().createElement(C.SecretInput,{"data-testid":u.headerValueInput,width:65,label:"","aria-label":"",placeholder:m.secureHeaderValueLabel,value:n,isConfigured:c,onReset:()=>p(!1),onChange:e=>o(e.target.value),onBlur:()=>b()}):Ne().createElement(C.Input,{"data-testid":u.headerValueInput,width:65,value:n,placeholder:m.insecureHeaderValueLabel,onChange:e=>o(e.target.value),onBlur:()=>b()});const d=l?m.secureHeaderValueLabel:m.insecureHeaderValueLabel;return Ne().createElement("div",{"data-testid":u.headerEditor},Ne().createElement(C.HorizontalGroup,null,Ne().createElement(C.Field,{label:m.headerNameLabel,"aria-label":m.headerNameLabel},Ne().createElement(C.Input,{"data-testid":u.headerNameInput,value:r,disabled:c,placeholder:m.headerNamePlaceholder,onChange:e=>a(e.target.value),onBlur:()=>b()})),Ne().createElement(C.Field,{label:d,"aria-label":d},y),!c&&Ne().createElement(C.Field,{label:m.secureLabel},Ne().createElement(C.Switch,{"data-testid":u.headerSecureSwitch,className:"gf-form",value:l,onChange:e=>i(e.currentTarget.checked),onBlur:()=>b()})),s&&Ne().createElement(C.Button,{"data-testid":u.removeHeaderButton,className:Rt.Common.smallBtn,variant:"destructive",size:"sm",icon:"trash-alt",onClick:s})))},Ut=e=>(0,Ie.useMemo)((()=>{const t=new Set;for(let s in e)s.startsWith("secureHttpHeaders.")&&e[s]&&t.add(s.substring(s.indexOf(".")+1));return t}),[e]);function Ht(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}function Gt(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{},r=Object.keys(s);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(s).filter((function(e){return Object.getOwnPropertyDescriptor(s,e).enumerable})))),r.forEach((function(t){Ht(e,t,s[t])}))}return e}function Vt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),s.push.apply(s,r)}return s}(Object(t)).forEach((function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})),e}const zt=e=>{const{onAliasTablesChange:t}=e,[s,r]=(0,Ie.useState)(e.aliasTables||[]),a=At.components.Config.AliasTableConfig,n=Ye.Config.AliasTableConfig,o=e=>`"${e.targetDatabase}"."${e.targetTable}":"${e.aliasDatabase}"."${e.aliasTable}"`,l=e=>{const t=new Set;return e.filter((e=>{const s=o(e);return!t.has(s)&&(t.add(s),!0)}))};return Ne().createElement(ft,{title:a.title},Ne().createElement("div",null,Ne().createElement("span",null,a.descriptionParts[0]),Ne().createElement("code",null,a.descriptionParts[1]),Ne().createElement("span",null,a.descriptionParts[2])),Ne().createElement("br",null),s.map(((e,a)=>Ne().createElement(Wt,{key:o(e),targetDatabase:e.targetDatabase,targetTable:e.targetTable,aliasDatabase:e.aliasDatabase,aliasTable:e.aliasTable,onEntryChange:e=>((e,a)=>{let n=s.slice();a.targetDatabase=a.targetDatabase.trim(),a.targetTable=a.targetTable.trim(),a.aliasDatabase=a.aliasDatabase.trim(),a.aliasTable=a.aliasTable.trim(),n[e]=a,n=l(n),r(n),t(n)})(a,e),onRemove:()=>(e=>{let a=s.slice();a.splice(e,1),a=l(a),r(a),t(a)})(a)}))),Ne().createElement(C.Button,{"data-testid":n.addEntryButton,icon:"plus-circle",variant:"secondary",size:"sm",onClick:()=>{r(l([...s,{targetDatabase:"",targetTable:"",aliasDatabase:"",aliasTable:""}]))},className:Rt.Common.smallBtn},a.addTableLabel))},Wt=e=>{const{onEntryChange:t,onRemove:s}=e,[r,a]=(0,Ie.useState)(e.targetDatabase),[n,o]=(0,Ie.useState)(e.targetTable),[l,i]=(0,Ie.useState)(e.aliasDatabase),[c,p]=(0,Ie.useState)(e.aliasTable),m=At.components.Config.AliasTableConfig,u=Ye.Config.AliasTableConfig,b=()=>{t({targetDatabase:r,targetTable:n,aliasDatabase:l,aliasTable:c})};return Ne().createElement("div",{"data-testid":u.aliasEditor},Ne().createElement(C.HorizontalGroup,null,Ne().createElement(C.Field,{label:m.targetDatabaseLabel,"aria-label":m.targetDatabaseLabel},Ne().createElement(C.Input,{"data-testid":u.targetDatabaseInput,value:r,placeholder:m.targetDatabasePlaceholder,onChange:e=>a(e.target.value),onBlur:()=>b()})),Ne().createElement(C.Field,{label:m.targetTableLabel,"aria-label":m.targetTableLabel},Ne().createElement(C.Input,{"data-testid":u.targetTableInput,value:n,placeholder:m.targetTableLabel,onChange:e=>o(e.target.value),onBlur:()=>b()})),Ne().createElement(C.Field,{label:m.aliasDatabaseLabel,"aria-label":m.aliasDatabaseLabel},Ne().createElement(C.Input,{"data-testid":u.aliasDatabaseInput,value:l,placeholder:m.aliasDatabasePlaceholder,onChange:e=>i(e.target.value),onBlur:()=>b()})),Ne().createElement(C.Field,{label:m.aliasTableLabel,"aria-label":m.aliasTableLabel},Ne().createElement(C.Input,{"data-testid":u.aliasTableInput,value:c,placeholder:m.aliasTableLabel,onChange:e=>p(e.target.value),onBlur:()=>b()})),s&&Ne().createElement(C.Button,{"data-testid":u.removeEntryButton,className:Rt.Common.smallBtn,variant:"destructive",size:"sm",icon:"trash-alt",onClick:s})))};function Kt(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}function Qt(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{},r=Object.keys(s);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(s).filter((function(e){return Object.getOwnPropertyDescriptor(s,e).enumerable})))),r.forEach((function(t){Kt(e,t,s[t])}))}return e}function Yt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),s.push.apply(s,r)}return s}(Object(t)).forEach((function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})),e}function Xt(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}function Jt(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{},r=Object.keys(s);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(s).filter((function(e){return Object.getOwnPropertyDescriptor(s,e).enumerable})))),r.forEach((function(t){Xt(e,t,s[t])}))}return e}function Zt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),s.push.apply(s,r)}return s}(Object(t)).forEach((function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})),e}const es=e=>["boolean"].includes(null==e?void 0:e.toLowerCase()),ts=e=>["int","float","decimal"].some((t=>null==e?void 0:e.toLowerCase().includes(t))),ss=e=>{const t=null==e?void 0:e.toLowerCase();return(null==t?void 0:t.startsWith("date"))||(null==t?void 0:t.startsWith("nullable(date"))},rs=e=>!(es(e)||ts(e)||ss(e)),as=e=>rs(e.type)&&[i.In,i.NotIn].includes(e.operator);function ns(e,t,a){var o,l,i,c,p;const m=x(e);if(!m)throw new Error("The query is not valid SQL.");if("select"!==m.type)throw new Error("The query is not a select statement.");if(!m.from||1!==m.from.length)throw new Error("The query has too many 'FROM' clauses.");if("table"!==m.from[0].type)throw new Error("The 'FROM' clause is not a table.");const u=m.from[0],b=function(e){if(!e)return{columns:[],aggregates:[]};const t=[],s=[];for(let a of e)switch(a.expr.type){case"ref":var r;t.push({name:a.expr.name,alias:null===(r=a.alias)||void 0===r?void 0:r.name});break;case"call":const[e,o]=bs(a);if(!e)break;(0,k.isString)(e)?t.push({name:e,type:"datetime",alias:o,hint:n.Time}):s.push(e)}return{columns:t,aggregates:s}}(m.columns||null),y={database:u.name.schema||"",table:u.name.name||"",queryType:t||r.Table,mode:s.List,columns:[],aggregates:[]};b.columns.length>0&&(y.columns=b.columns||[]),t===r.Logs?(ze(y.columns,null==a?void 0:a.getDefaultLogsColumns()),ze(y.columns,Se)):t===r.Traces&&ze(y.columns,null==a?void 0:a.getDefaultTraceColumns()),b.aggregates.length>0&&(y.mode=s.Aggregate,y.aggregates=b.aggregates);const d=ne(y,n.Time);var $;!t&&d&&(y.queryType=r.TimeSeries,(null===($=y.aggregates)||void 0===$?void 0:$.length)&&(y.mode=s.Trend));m.where&&(y.filters=function(e,t){const s=[];let r=0,a=!1;return(0,_.astVisitor)((e=>({expr:n=>{switch(n===null||n===void 0?void 0:n.type){case"binary":a=us(n,s,r,a);e.super().expr(n);break;case"ref":({i:r,notFlag:a}=os(n,s,r,a));break;case"string":r=ps(s,r,n);break;case"integer":r=ms(s,r,n);break;case"unary":a=cs(n,a,r,s);e.super().expr(n);break;case"call":r=is(n,t,s,r);break;case"list":r=ls(s,r,n);break;default:console.error(`${n===null||n===void 0?void 0:n.type} is not supported. This is likely a bug.`);break}}}))).expr(e),s}(m.where,(null==d?void 0:d.name)||""));const h=null===(o=m.orderBy)||void 0===o?void 0:o.map((e=>"ref"!==e.by.type?{}:{name:e.by.name,dir:e.order})).filter((e=>e.name));h&&h.length>0&&(y.orderBy=h),y.limit="integer"===(null===(i=m.limit)||void 0===i||null===(l=i.limit)||void 0===l?void 0:l.type)?null===(c=m.limit)||void 0===c?void 0:c.limit.value:void 0;const f=null===(p=m.groupBy)||void 0===p?void 0:p.map((e=>"ref"!==e.type?"":e.name)).filter((e=>""!==e));return f&&f.length>0&&(y.groupBy=f),y}function os(e,t,s,r){var a,n;return"$__fromtime"===(null===(a=e.name)||void 0===a?void 0:a.toLowerCase())&&t[s].operator===i.GreaterThanOrEqual?(r?(t[s].operator=i.OutsideGrafanaTimeRange,r=!1):t[s].operator=i.WithInGrafanaTimeRange,t[s].type="datetime",{i:++s,notFlag:r}):"$__totime"===(null===(n=e.name)||void 0===n?void 0:n.toLowerCase())?(t.splice(s,1),{i:s,notFlag:r}):t[s].key?(t[s]=Zt(Jt({},t[s]),{value:[e.name],type:"string"}),{i:++s,notFlag:r}):(t[s].key=e.name,t[s].operator===i.IsNotNull&&s++,{i:s,notFlag:r})}function ls(e,t,s){return e[t]=Zt(Jt({},e[t]),{value:s.expressions.map((e=>e.value)),type:"string"}),++t}function is(e,t,s,r){const a=`${e.function.name}(${e.args.map((e=>e.name)).join(",")})`;return a===`$__timefilter(${t})`?(s.splice(r,1),r):a.startsWith("$__timefilter(")?(s[r]=Zt(Jt({},s[r]),{key:e.args[0].name,operator:i.WithInGrafanaTimeRange,type:"datetime"}),++r):(s[r]=Zt(Jt({},s[r]),{value:a,type:"datetime"}),a||r++,r)}function cs(e,t,s,r){return"NOT"===e.op||(0===s&&r.unshift({}),r[s].operator=e.op,t)}function ps(e,t,s){return e[t].key?(e[t]=Zt(Jt({},e[t]),{value:s.value,type:"string"}),++t):(e[t]=Zt(Jt({},e[t]),{key:s.value}),t)}function ms(e,t,s){return e[t].key?(e[t]=Zt(Jt({},e[t]),{value:s.value,type:"int"}),++t):(e[t]=Zt(Jt({},e[t]),{key:s.value.toString()}),t)}function us(e,t,s,r){return"AND"===e.op||"OR"===e.op?t.unshift({condition:e.op}):Object.values(i).find((t=>e.op===t))&&(0===s?t.unshift({}):t[s]||t.push({condition:"AND"}),t[s].operator=e.op,r&&t[s].operator===i.Like&&(t[s].operator=i.NotLike,r=!1)),r}function bs(e){var t;if("call"!==e.expr.type)return[{},void 0];let s=e.expr.args.map((e=>"ref"!==e.type?"":e.name));return s.length>1?["",void 0]:Object.values(a).includes(e.expr.function.name.toLowerCase())?[{aggregateType:e.expr.function.name,column:s[0],alias:null===(r=e.alias)||void 0===r?void 0:r.name},null===(n=e.alias)||void 0===n?void 0:n.name]:[s[0],null===(t=e.alias)||void 0===t?void 0:t.name];var r,n}new Map([["equals",i.Equals],["contains",i.Like]]);function ys(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}function ds(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{},r=Object.keys(s);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(s).filter((function(e){return Object.getOwnPropertyDescriptor(s,e).enumerable})))),r.forEach((function(t){ys(e,t,s[t])}))}return e}function _s(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),s.push.apply(s,r)}return s}(Object(t)).forEach((function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})),e}const $s=[{label:At.types.EditorType.sql,value:c.SQL},{label:At.types.EditorType.builder,value:c.Builder}],hs=e=>{const{datasource:t,query:s,onChange:r}=e,{label:a,tooltip:n,switcher:o,cannotConvert:l}=At.components.EditorTypeSwitcher,i=s.editorType||c.Builder,[p,m]=(0,Ie.useState)(!1),[u,b]=(0,Ie.useState)(!1),[y,_]=(0,Ie.useState)(""),$=(e,a=!1)=>{if(s.editorType!==c.SQL||e!==c.Builder||a){let a;switch(s.editorType){case c.Builder:a=s.builderOptions;break;case c.SQL:a=ns(s.rawSql,s.queryType,t);break;default:a=d.builderOptions}e===c.SQL?r(_s(ds({},s),{editorType:c.SQL,queryType:a.queryType,rawSql:J(a),format:Ge(a.queryType),meta:{builderOptions:a}})):e===c.Builder&&r(_s(ds({},s),{editorType:c.Builder,queryType:a.queryType,rawSql:J(a),builderOptions:a}))}else try{ns(s.rawSql,s.queryType,t),m(!0)}catch(e){b(!0),_(e.message)}},h=()=>{$(c.Builder,!0),m(!1),b(!1)};return Ne().createElement("span",null,Ne().createElement(C.InlineFormLabel,{width:8,className:"query-keyword",tooltip:n},a),Ne().createElement(C.RadioButtonGroup,{options:$s,value:i,onChange:e=>$(e)}),Ne().createElement(C.ConfirmModal,{isOpen:p,title:o.title,body:o.body,confirmText:o.confirmText,dismissText:o.dismissText,icon:"exclamation-triangle",onConfirm:h,onDismiss:()=>m(!1)}),Ne().createElement(C.ConfirmModal,{title:l.title,body:`${y}\n${l.message}`,isOpen:u,icon:"exclamation-triangle",onConfirm:h,confirmText:o.confirmText,onDismiss:()=>b(!1)}))};const fs=e=>{const{allColumns:t,selectedColumns:s,onSelectedColumnsChange:r,disabled:a,showAllOption:n}=e,[o,l]=(0,Ie.useState)([]),[i,c]=(0,Ie.useState)(!1),p=t.map((e=>({label:e.label||e.name,value:e.name})));n&&p.push({label:"*",value:"*"});const m=(s||[]).map((e=>({label:e.alias||e.name,value:e.name}))),{label:u,tooltip:b}=At.components.ColumnsEditor,y=[...p,...o];(0,Ie.useEffect)((()=>{if(0===t.length)return;const e=function(e,t){const s=new Set(e);return t.filter((e=>s.has(e.name))).map((e=>({label:e.label||e.name,value:e.name})))}(s.map((e=>e.name)),t);l(e)}),[t,s]);return Ne().createElement("div",{className:"gf-form"},Ne().createElement(C.InlineFormLabel,{width:8,className:"query-keyword",tooltip:b},u),Ne().createElement("div",{"data-testid":Ye.QueryBuilder.ColumnsEditor.multiSelectWrapper,className:Rt.Common.selectWrapper},Ne().createElement(C.MultiSelect,{disabled:a,options:y,value:m,isOpen:i,onOpenMenu:()=>c(!0),onCloseMenu:()=>c(!1),onChange:e=>{c(!1);const a=new Set(e.map((e=>e.value))),n=new Set(o.map((e=>e.value))),l=new Map,i=new Map;t.forEach((e=>l.set(e.name,e))),s.forEach((e=>i.set(e.name,e)));const p=a.size>1,m=[];for(let e of a){if(p&&"*"===e)continue;const t=l.get(e),s=i.get(e);s?m.push(s):m.push({name:e,type:(null==t?void 0:t.type)||"String",custom:n.has(e),alias:(null==t?void 0:t.label)||e})}r(m)},allowCustomValue:!0,menuPlacement:"bottom"})))},ws=()=>!0,xs=e=>{const{allColumns:t,selectedColumn:s,onColumnChange:r,columnFilterFn:a,columnHint:n,label:o,tooltip:l,disabled:i,invalid:c,wide:p,inline:m,clearable:u}=e,b=null==s?void 0:s.name,y=t.filter(a||ws).map((e=>({label:e.label||e.name,value:e.name})));let d=!1;s&&!y.find((e=>e.value===s.name))&&(y.push({label:s.alias||s.name,value:s.name}),d=!0);const _="query-keyword "+(m?Rt.QueryEditor.inlineField:"");return Ne().createElement("div",{className:"gf-form"},Ne().createElement(C.InlineFormLabel,{width:p?12:8,className:_,tooltip:l},o),Ne().createElement(C.Select,{disabled:i,invalid:c||d,options:y,value:b,placeholder:b||void 0,onChange:e=>{if(!e||!e.value)return void r(void 0);const s=t.find((t=>t.name===e.value)),a={name:(null==s?void 0:s.name)||e.value,type:null==s?void 0:s.type,hint:n};s&&void 0!==s.label&&(a.alias=s.label),r(a)},width:p?25:20,menuPlacement:"bottom",isClearable:void 0===u||u,allowCustomValue:!0}))};function gs(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}function vs(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{},r=Object.keys(s);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(s).filter((function(e){return Object.getOwnPropertyDescriptor(s,e).enumerable})))),r.forEach((function(t){gs(e,t,s[t])}))}return e}function ks(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),s.push.apply(s,r)}return s}(Object(t)).forEach((function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})),e}const Es=[{label:"ASC",value:l.ASC},{label:"DESC",value:l.DESC}],Ts=e=>{const{columnOptions:t,index:s,orderByItem:r,updateOrderByItem:a,removeOrderByItem:n}=e;return Ne().createElement(Ne().Fragment,null,Ne().createElement(C.Select,{disabled:Boolean(r.hint),placeholder:r.hint?At.types.ColumnHint[r.hint]:void 0,value:r.hint?void 0:r.name,className:Rt.Common.inlineSelect,width:36,options:t,onChange:e=>a(s,ks(vs({},r),{name:e.value})),allowCustomValue:!0,menuPlacement:"bottom"}),Ne().createElement(C.Select,{value:r.dir,className:Rt.Common.inlineSelect,width:12,options:Es,onChange:e=>a(s,ks(vs({},r),{dir:e.value})),menuPlacement:"bottom"}),Ne().createElement(C.Button,{"data-testid":"query-builder-orderby-remove-button",className:Rt.Common.smallBtn,variant:"destructive",size:"sm",icon:"trash-alt",onClick:()=>n(s)}))},Os=e=>{const{orderByOptions:t,orderBy:s,onOrderByChange:r}=e,{label:a,tooltip:n,addLabel:o}=At.components.OrderByEditor,i=e=>{const t=s.slice();t.splice(e,1),r(t)},c=(e,t)=>{const a=s.slice();a[e]=t,r(a)};if(0===t.length)return null;const p=Ne().createElement(C.InlineFormLabel,{width:8,className:"query-keyword","data-testid":"query-builder-orderby-item-label",tooltip:n},a),m=Ne().createElement("div",{className:`width-8 ${Rt.Common.firstLabel}`});return Ne().createElement(Ne().Fragment,null,s.map(((e,s)=>{const r=`${s}-${e.name}-${e.hint||""}-${e.dir}`;return Ne().createElement("div",{className:"gf-form",key:r,"data-testid":"query-builder-orderby-item-wrapper"},0===s?p:m,Ne().createElement(Ts,{columnOptions:t,index:s,orderByItem:e,updateOrderByItem:c,removeOrderByItem:i}))})),Ne().createElement("div",{className:"gf-form"},0===s.length?p:m,Ne().createElement(C.Button,{"data-testid":"query-builder-orderby-add-button",icon:"plus-circle",variant:"secondary",size:"sm",onClick:()=>{var e;const a=s.slice();a.push({name:null===(e=t[0])||void 0===e?void 0:e.value,dir:l.ASC}),r(a)},className:Rt.Common.smallBtn},o)))},Cs=(e,t)=>{var s;let r=[];var a;(e=>{var t;return((null===(t=e.aggregates)||void 0===t?void 0:t.length)||0)>0})(e)?(null===(a=e.columns)||void 0===a||a.forEach((e=>{r.push({label:e.alias||e.name,value:e.name})})),e.aggregates.forEach((e=>{let t=`${e.aggregateType}(${e.column})`,s=t;e.alias&&(t+=` as ${e.alias}`,s=e.alias),r.push({label:t,value:s})})),e.groupBy&&e.groupBy.length>0&&e.groupBy.forEach((e=>r.push({label:e,value:e})))):t.forEach((e=>r.push({label:e.label||e.name,value:e.name})));const n=new Set(r.map((e=>e.value))),o=null===(s=e.orderBy)||void 0===s?void 0:s.filter((e=>!n.has(e.name)));return null==o||o.forEach((e=>r.push({label:e.name,value:e.name}))),r},As=e=>{const[t,s]=(0,Ie.useState)(e.limit||0),{label:r,tooltip:a}=At.components.LimitEditor;return Ne().createElement("div",{className:"gf-form"},Ne().createElement(C.InlineFormLabel,{width:8,className:"query-keyword",tooltip:a},r),Ne().createElement(C.Input,{"data-testid":Ye.QueryBuilder.LimitEditor.input,width:10,value:t,type:"number",min:0,onChange:e=>s(e.currentTarget.valueAsNumber),onBlur:()=>e.onLimitChange(t)}))};function Ss(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}function Is(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{},r=Object.keys(s);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(s).filter((function(e){return Object.getOwnPropertyDescriptor(s,e).enumerable})))),r.forEach((function(t){Ss(e,t,s[t])}))}return e}function Ns(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),s.push.apply(s,r)}return s}(Object(t)).forEach((function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})),e}const Ls=[{value:!0,label:"True"},{value:!1,label:"False"}],Rs=[{value:"AND",label:"AND"},{value:"OR",label:"OR"}],Ds=[{value:i.WithInGrafanaTimeRange,label:"Within dashboard time range"},{value:i.OutsideGrafanaTimeRange,label:"Outside dashboard time range"},{value:i.IsAnything,label:"IS ANYTHING"},{value:i.Equals,label:"="},{value:i.NotEquals,label:"!="},{value:i.LessThan,label:"<"},{value:i.LessThanOrEqual,label:"<="},{value:i.GreaterThan,label:">"},{value:i.GreaterThanOrEqual,label:">="},{value:i.Like,label:"LIKE"},{value:i.NotLike,label:"NOT LIKE"},{value:i.IsEmpty,label:"IS EMPTY"},{value:i.IsNotEmpty,label:"IS NOT EMPTY"},{value:i.In,label:"IN"},{value:i.NotIn,label:"NOT IN"},{value:i.IsNull,label:"IS NULL"},{value:i.IsNotNull,label:"IS NOT NULL"}],qs=[{value:"today()",label:"TODAY"},{value:"yesterday()",label:"YESTERDAY"},{value:"now()",label:"NOW"},{value:"GRAFANA_START_TIME",label:"DASHBOARD START TIME"},{value:"GRAFANA_END_TIME",label:"DASHBOARD END TIME"}],js={filterType:"custom",condition:"AND",key:"",type:"",operator:i.IsAnything},Ps=e=>{const[t,s]=(0,Ie.useState)(e.value||0);return Ne().createElement("div",{"data-testid":"query-builder-filters-number-value-container"},Ne().createElement(C.Input,{"data-testid":"query-builder-filters-number-value-input",type:"number",value:t,onChange:e=>s(e.currentTarget.valueAsNumber||0),onBlur:()=>e.onChange(t)}))},Bs=e=>Ne().createElement("div",{"data-testid":"query-builder-filters-single-string-value-container"},Ne().createElement(C.Input,{"data-testid":"query-builder-filters-single-string-value-input",type:"text",defaultValue:e.value,width:70,onBlur:t=>e.onChange(t.currentTarget.value)})),Ms=e=>{const[t,s]=(0,Ie.useState)(e.value||[]);return Ne().createElement("div",{"data-testid":"query-builder-filters-multi-string-value-container"},Ne().createElement(C.Input,{type:"text",value:t.join(","),placeholder:"comma separated values",onChange:e=>s((e.currentTarget.value||"").split(",")),onBlur:()=>e.onChange(t)}))},Fs=e=>{const{filter:t,onFilterChange:s,allColumns:r}=e,a=()=>{const e=r.find((e=>e.name===t.key));return(null==e?void 0:e.picklistValues)||[]};if((e=>[i.IsNull,i.IsNotNull].includes(e.operator))(t))return Ne().createElement(Ne().Fragment,null);if([i.IsAnything,i.IsEmpty,i.IsNotEmpty].includes(t.operator))return Ne().createElement(Ne().Fragment,null);if((e=>es(e.type))(t)){const e=e=>{s(Ns(Is({},t),{value:e}))};return Ne().createElement("div",{"data-testid":"query-builder-filters-boolean-value-container"},Ne().createElement(C.RadioButtonGroup,{options:Ls,value:t.value,onChange:t=>e(t)}))}if((e=>ts(e.type))(t))return Ne().createElement(Ps,{value:t.value,onChange:e=>s(Ns(Is({},t),{value:e}))});if((e=>ss(e.type))(t)){if((e=>ss(e.type)&&[i.WithInGrafanaTimeRange,i.OutsideGrafanaTimeRange].includes(e.operator))(t))return null;const e=e=>{s(Ns(Is({},t),{value:e}))},r=[...qs];return t.value&&!qs.find((e=>e.value===t.value))&&r.push({label:t.value,value:t.value}),Ne().createElement("div",{"data-testid":"query-builder-filters-date-value-container"},Ne().createElement(C.Select,{value:t.value||"TODAY",onChange:t=>e(t.value),options:r,width:40,allowCustomValue:!0}))}if((e=>rs(e.type)&&![i.In,i.NotIn].includes(e.operator))(t)){const e=e=>{s(Ns(Is({},t),{value:e}))};return"picklist"!==t.type||t.operator!==i.Equals&&t.operator!==i.NotEquals?Ne().createElement(Bs,{value:t.value,onChange:e,key:t.value}):Ne().createElement("div",{"data-testid":"query-builder-filters-single-picklist-value-container"},Ne().createElement(C.Select,{value:t.value,onChange:t=>e(t.value),options:a()}))}if(as(t)){const e=e=>{s(Ns(Is({},t),{value:e}))};return"picklist"===t.type?Ne().createElement("div",{"data-testid":"query-builder-filters-multi-picklist-value-container"},Ne().createElement(C.MultiSelect,{value:t.value,options:a(),onChange:t=>e(t.map((e=>e.value)))})):Ne().createElement(Ms,{value:t.value,onChange:e})}return Ne().createElement(Ne().Fragment,null)},Us=e=>{const{index:t,filter:s,allColumns:r,onFilterChange:a,removeFilter:n}=e,[o,l]=(0,Ie.useState)(!1),c=s.type.startsWith("Map"),p=((e,t,s,r)=>{const[a,n]=(0,Ie.useState)([]);(0,Ie.useEffect)((()=>{if(!(e&&t&&s&&r))return;let a=!1;return e.fetchUniqueMapKeys(t,s,r).then((e=>{a||n(e)})).catch((e=>{console.error("Failed to fetch map keys for column:",t,s,r,e)})),()=>{a=!0}}),[e,t,s,r]);const o=(0,Ie.useRef)(""),l=(0,Ie.useRef)("");return s!==o.current||r!==l.current?(o.current=s,l.current=r,n([]),[]):a})(e.datasource,c?s.key:"",e.database,e.table),m=p.map((e=>({label:e,value:e})));s.mapKey&&!p.includes(s.mapKey)&&m.push({label:s.mapKey,value:s.mapKey});return Ne().createElement(C.HorizontalGroup,{wrap:!0,align:"flex-start",justify:"flex-start"},0!==t&&Ne().createElement(C.RadioButtonGroup,{options:Rs,value:s.condition,onChange:e=>(e=>{const r=Is({},s);r.condition=e,a(t,r)})(e)}),Ne().createElement(C.Select,{disabled:Boolean(s.hint),placeholder:s.hint?At.types.ColumnHint[s.hint]:void 0,value:s.key,width:40,className:Rt.Common.inlineSelect,options:(()=>{const e=(s.restrictToFields||r).map((e=>{let t=e.label||e.name;return e.type.startsWith("Map")&&(t+="[]"),{label:t,value:e.name}}));return(null==s?void 0:s.key)&&!e.find((e=>e.value===s.key))&&e.push({label:s.label||s.key,value:s.key}),e})(),isOpen:o,onOpenMenu:()=>l(!0),onCloseMenu:()=>l(!1),onChange:e=>(e=>{l(!1);const n=r.find((t=>t.name===e)),o={key:(null==n?void 0:n.name)||e,type:(null==n?void 0:n.type)||"String",label:null==n?void 0:n.label};let c;c=s.restrictToFields?{filterType:"custom",key:o.key||s.key,type:"datetime",condition:s.condition||"AND",operator:i.WithInGrafanaTimeRange,restrictToFields:s.restrictToFields,label:o.label}:es(o.type)?{filterType:"custom",key:o.key,type:"boolean",condition:s.condition||"AND",operator:i.Equals,value:!1,label:o.label}:ss(o.type)?{filterType:"custom",key:o.key,type:o.type,condition:s.condition||"AND",operator:i.Equals,value:"TODAY",label:o.label}:{filterType:"custom",key:o.key,type:o.type,condition:s.condition||"AND",operator:i.IsNotNull,label:o.label},a(t,c)})(e.value),allowCustomValue:!0,menuPlacement:"bottom"}),c&&Ne().createElement(C.Select,{value:s.mapKey,placeholder:At.components.FilterEditor.mapKeyPlaceholder,width:40,className:Rt.Common.inlineSelect,options:m,onChange:e=>(e=>{const r=Is({},s);r.mapKey=e,a(t,r)})(e.value),allowCustomValue:!0,menuPlacement:"bottom"}),Ne().createElement(C.Select,{value:s.operator,width:40,className:Rt.Common.inlineSelect,options:((e="string")=>es(e)?Ds.filter((e=>[i.Equals,i.NotEquals].includes(e.value))):ts(e)?Ds.filter((e=>[i.IsAnything,i.IsNull,i.IsNotNull,i.Equals,i.NotEquals,i.LessThan,i.LessThanOrEqual,i.GreaterThan,i.GreaterThanOrEqual].includes(e.value))):ss(e)?Ds.filter((e=>[i.IsAnything,i.IsNull,i.IsNotNull,i.Equals,i.NotEquals,i.LessThan,i.LessThanOrEqual,i.GreaterThan,i.GreaterThanOrEqual,i.WithInGrafanaTimeRange,i.OutsideGrafanaTimeRange].includes(e.value))):Ds.filter((e=>[i.IsAnything,i.Like,i.NotLike,i.In,i.NotIn,i.IsNull,i.IsNotNull,i.Equals,i.NotEquals,i.IsEmpty,i.IsNotEmpty,i.LessThan,i.LessThanOrEqual,i.GreaterThan,i.GreaterThanOrEqual].includes(e.value))))(s.type),onChange:e=>(e=>{const r=Is({},s);r.operator=e,as(r)&&(Array.isArray(r.value)||(r.value=[r.value||""])),a(t,r)})(e.value),menuPlacement:"bottom"}),Ne().createElement(Fs,{filter:s,onFilterChange:e=>{a(t,e)},allColumns:r}),Ne().createElement(C.Button,{"data-testid":"query-builder-filters-remove-button",icon:"trash-alt",variant:"destructive",size:"sm",className:Rt.Common.smallBtn,onClick:()=>n(t)}))},Hs=e=>{const{filters:t=[],onFiltersChange:s,allColumns:r=[],datasource:a,database:n,table:o}=e,{label:l,tooltip:i,addLabel:c}=At.components.FilterEditor,p=()=>{s([...t,Is({},js)])},m=e=>{const r=[...t];r.splice(e,1),s(r)},u=(e,r)=>{const a=[...t];a[e]=r,s(a)};return Ne().createElement(Ne().Fragment,null,0===t.length&&Ne().createElement("div",{className:"gf-form"},Ne().createElement(C.InlineFormLabel,{width:8,className:"query-keyword",tooltip:i},l),Ne().createElement(C.Button,{"data-testid":"query-builder-filters-add-button",icon:"plus-circle",variant:"secondary",size:"sm",className:Rt.Common.smallBtn,onClick:p},c)),t.map(((e,t)=>Ne().createElement("div",{className:"gf-form",key:t},0===t?Ne().createElement(C.InlineFormLabel,{width:8,className:"query-keyword",tooltip:i},l):Ne().createElement("div",{className:`width-8 ${Rt.Common.firstLabel}`}),Ne().createElement(Us,{allColumns:r,filter:e,onFilterChange:u,removeFilter:m,index:t,datasource:a,database:n,table:o})))),0!==t.length&&Ne().createElement("div",{className:"gf-form"},Ne().createElement("div",{className:`width-8 ${Rt.Common.firstLabel}`}),Ne().createElement(C.Button,{"data-testid":"query-builder-filters-inline-add-button",icon:"plus-circle",variant:"secondary",size:"sm",className:Rt.Common.smallBtn,onClick:p},c)))},Gs=e=>(e.type||"").toLowerCase().includes("date"),Vs=e=>(e.type||"").toLowerCase().includes("string")||(e.type||"").toLowerCase().includes("enum");function zs(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}function Ws(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),s.push.apply(s,r)}return s}(Object(t)).forEach((function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})),e}function Ks(e,t){return s=>r=>{const a=Ws(function(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{},r=Object.keys(s);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(s).filter((function(e){return Object.getOwnPropertyDescriptor(s,e).enumerable})))),r.forEach((function(t){zs(e,t,s[t])}))}return e}({},t),{[s]:r});e(a)}}const Qs=(e,t,s)=>{const[r,a]=(0,Ie.useState)([]);(0,Ie.useEffect)((()=>{if(!e||!t||!s)return;let r=!1;return e.fetchColumns(t,s).then((e=>{r||a(e)})).catch((e=>{console.error(e)})),()=>{r=!0}}),[e,t,s]);const n=(0,Ie.useRef)(""),o=t+s;return o!==n.current?(n.current=o,a([]),[]):r};function Ys(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}function Xs(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{},r=Object.keys(s);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(s).filter((function(e){return Object.getOwnPropertyDescriptor(s,e).enumerable})))),r.forEach((function(t){Ys(e,t,s[t])}))}return e}function Js(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),s.push.apply(s,r)}return s}(Object(t)).forEach((function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})),e}var Zs;!function(e){e.SetOptions="set_options",e.SetAllOptions="set_all_options",e.SetQueryType="set_query_type",e.SetDatabase="set_database",e.SetTable="set_table",e.SetOtelEnabled="set_otel_enabled",e.SetOtelVersion="set_otel_version",e.SetColumnByHint="set_column_by_hint",e.SetBuilderMinimized="set_builder_minimized"}(Zs||(Zs={}));const er=(e,t)=>({type:e,payload:t}),tr=(e,t)=>({type:e,payload:t}),sr=e=>er("set_options",e),rr=e=>er("set_all_options",e),ar=e=>er("set_otel_enabled",{meta:{otelEnabled:e}}),nr=e=>er("set_otel_version",{meta:{otelVersion:e}}),or=e=>tr("set_column_by_hint",{column:e}),lr=(e,t)=>{const s=ir.get(t.type);if(!s)throw Error("missing function for BuilderOptionsActionType: "+t.type);return s(e,t)},ir=new Map([["set_options",(e,t)=>{const s=t.payload;return pr(e,s)}],["set_all_options",(e,t)=>{const s=t.payload;return cr(s)}],["set_query_type",(e,t)=>{const s=t.payload.queryType;return e.queryType!==s?cr({database:e.database,table:e.table,queryType:s}):e}],["set_database",(e,t)=>cr({database:t.payload.database,table:"",queryType:e.queryType})],["set_table",(e,t)=>cr({database:e.database,table:t.payload.table,queryType:e.queryType})],["set_otel_enabled",(e,t)=>{var s;return pr(e,{meta:{otelEnabled:Boolean(null===(s=t.payload.meta)||void 0===s?void 0:s.otelEnabled)}})}],["set_otel_version",(e,t)=>{var s;return pr(e,{meta:{otelVersion:null===(s=t.payload.meta)||void 0===s?void 0:s.otelVersion}})}],["set_column_by_hint",(e,t)=>{const s=t.payload.column,r=(e.columns||[]).filter((e=>e.hint!==s.hint));return r.push(s),pr(e,{columns:r})}],["set_builder_minimized",(e,t)=>{const s=t.payload.minimized;return pr(e,{meta:{minimized:s}})}]]),cr=e=>{const t=d.builderOptions;return Js(Xs({},t,e),{meta:Xs({},t.meta,null==e?void 0:e.meta)})},pr=(e,t)=>Js(Xs({},e,t),{meta:Xs({},e.meta,t.meta)}),mr=e=>(0,Ie.useRef)(!Ue(e)).current,ur=e=>{var t;const{datasource:s,builderOptions:r,builderOptionsDispatch:a}=e,o=At.components.LogsQueryBuilder,c=Qs(s,r.database,r.table),p=mr(r),m=(0,Ie.useMemo)((()=>{var e,t,s,a;return{otelEnabled:(null===(e=r.meta)||void 0===e?void 0:e.otelEnabled)||!1,otelVersion:(null===(t=r.meta)||void 0===t?void 0:t.otelVersion)||"",timeColumn:ne(r,n.Time),logLevelColumn:ne(r,n.LogLevel),messageColumn:ne(r,n.LogMessage),labelsColumn:ne(r,n.LogLabels),selectedColumns:(null===(s=r.columns)||void 0===s?void 0:s.filter((e=>e.hint!==n.Time&&e.hint!==n.LogLevel&&e.hint!==n.LogMessage&&e.hint!==n.LogLabels)))||[],filters:r.filters||[],orderBy:r.orderBy||[],limit:r.limit||0,logMessageLike:(null===(a=r.meta)||void 0===a?void 0:a.logMessageLike)||""}}),[r]),[u,b]=(0,Ie.useState)(0===s.getDefaultLogsColumns().size&&0===(null===(t=r.columns)||void 0===t?void 0:t.length)),y=Ks((e=>{const t=e.selectedColumns.slice();e.timeColumn&&t.push(e.timeColumn),e.logLevelColumn&&t.push(e.logLevelColumn),e.messageColumn&&t.push(e.messageColumn),e.labelsColumn&&t.push(e.labelsColumn),a(sr({columns:t,filters:e.filters,orderBy:e.orderBy,limit:e.limit,meta:{logMessageLike:e.logMessageLike}}))}),m);((e,t,s,r)=>{const a=(0,Ie.useRef)(!1);(0,Ie.useEffect)((()=>{if(!t||a.current)return;const n=e.getDefaultLogsDatabase()||e.getDefaultDatabase(),o=e.getDefaultLogsTable()||e.getDefaultTable(),l=e.getLogsOtelVersion(),i=e.getDefaultLogsColumns(),c=[],p=new Set;for(let[e,t]of i)c.push({name:t,hint:e}),p.add(t);if(e.shouldSelectLogContextColumns()){const t=e.getLogContextColumnNames();for(let e of t)p.has(e)||(c.push({name:e}),p.add(e))}r(sr({database:n,table:o||s.table,columns:c,meta:{otelEnabled:Boolean(l),otelVersion:l}})),a.current=!0}),[s.columns,s.orderBy,s.table,r,e,t])})(s,p,r,a),((e,t,s,r)=>{const a=(0,Ie.useRef)(t);t||(a.current=!1),(0,Ie.useEffect)((()=>{if(!t||a.current)return;const n=K.getVersion(s),o=null==n?void 0:n.logColumnMap;if(!o)return;const l=[],i=new Set;if(o.forEach(((e,t)=>{l.push({name:e,hint:t}),i.add(e)})),e.shouldSelectLogContextColumns()){const t=e.getLogContextColumnNames();for(let e of t)i.has(e)||(l.push({name:e}),i.add(e))}r(sr({columns:l})),a.current=!0}),[e,t,s,r])})(s,m.otelEnabled,m.otelVersion,a),((e,t,s,r,a,o)=>{const l=(0,Ie.useMemo)((()=>Boolean(e.getDefaultLogsTable())&&e.getDefaultLogsColumns().has(n.Time)),[e]),i=(0,Ie.useRef)(Boolean(r)||l),c=(0,Ie.useRef)(s||"");s!==c.current&&(i.current=!1),(Boolean(r)||a)&&(c.current=s,i.current=!0),(0,Ie.useEffect)((()=>{if(i.current||0===t.length||!s)return;const e=t.filter(Gs)[0];if(!e)return;const r={name:e.name,type:e.type,hint:n.Time};o(or(r)),c.current=s,i.current=!0}),[e,t,s,o])})(s,c,r.table,m.timeColumn,m.otelEnabled,a),((e,t,s)=>{const r=(0,Ie.useRef)(!t),a=(0,Ie.useRef)(e||"");e!==a.current&&(r.current=!1),(0,Ie.useEffect)((()=>{if(!e||r.current)return;const t=[{type:"datetime",operator:i.WithInGrafanaTimeRange,filterType:"custom",key:"",hint:n.Time,condition:"AND"},{type:"string",operator:i.IsAnything,filterType:"custom",key:"",hint:n.LogLevel,condition:"AND"}],o=[{name:"",hint:n.Time,dir:l.DESC,default:!0}];a.current=e,r.current=!0,s(sr({filters:t,orderBy:o}))}),[e,s])})(r.table,p,a);const d=u&&Ne().createElement(C.Alert,{title:"",severity:"warning",buttonContent:"Close",onRemove:()=>b(!1)},Ne().createElement(C.VerticalGroup,null,Ne().createElement("div",null,"To speed up your query building, enter your default logs configuration in your ",Ne().createElement("a",{style:{textDecoration:"underline"},href:`/connections/datasources/edit/${encodeURIComponent(s.uid)}#logs-config`},"ClickHouse Data Source settings"))));return Ne().createElement("div",null,d,Ne().createElement(Nt,{enabled:m.otelEnabled,onEnabledChange:e=>a(ar(e)),selectedVersion:m.otelVersion,onVersionChange:e=>a(nr(e))}),Ne().createElement(fs,{disabled:m.otelEnabled,allColumns:c,selectedColumns:m.selectedColumns,onSelectedColumnsChange:y("selectedColumns")}),Ne().createElement("div",{className:"gf-form"},Ne().createElement(xs,{disabled:m.otelEnabled,allColumns:c,selectedColumn:m.timeColumn,invalid:!m.timeColumn,onColumnChange:y("timeColumn"),columnFilterFn:Gs,columnHint:n.Time,label:o.logTimeColumn.label,tooltip:o.logTimeColumn.tooltip}),Ne().createElement(xs,{disabled:m.otelEnabled,allColumns:c,selectedColumn:m.logLevelColumn,invalid:!m.logLevelColumn,onColumnChange:y("logLevelColumn"),columnFilterFn:Vs,columnHint:n.LogLevel,label:o.logLevelColumn.label,tooltip:o.logLevelColumn.tooltip,inline:!0})),Ne().createElement("div",{className:"gf-form"},Ne().createElement(xs,{disabled:m.otelEnabled,allColumns:c,selectedColumn:m.messageColumn,invalid:!m.messageColumn,onColumnChange:y("messageColumn"),columnFilterFn:Vs,columnHint:n.LogMessage,label:o.logMessageColumn.label,tooltip:o.logMessageColumn.tooltip}),Ne().createElement(xs,{disabled:m.otelEnabled,allColumns:c,selectedColumn:m.labelsColumn,invalid:!m.labelsColumn,onColumnChange:y("labelsColumn"),columnHint:n.LogLabels,label:o.logLabelsColumn.label,tooltip:o.logLabelsColumn.tooltip,inline:!0})),Ne().createElement(Os,{orderByOptions:Cs(r,c),orderBy:m.orderBy,onOrderByChange:y("orderBy")}),Ne().createElement(As,{limit:m.limit,onLimitChange:y("limit")}),Ne().createElement(Hs,{filters:m.filters,onFiltersChange:y("filters"),allColumns:c,datasource:s,database:r.database,table:r.table}),Ne().createElement(br,{logMessageLike:m.logMessageLike,onChange:y("logMessageLike")}))},br=e=>{const[t,s]=(0,Ie.useState)(""),{logMessageLike:r,onChange:a}=e,{label:n,tooltip:o,clearButton:l}=At.components.LogsQueryBuilder.logMessageFilter;return(0,Ie.useEffect)((()=>{s(r)}),[r]),Ne().createElement("div",{className:"gf-form"},Ne().createElement(C.InlineFormLabel,{width:8,className:"query-keyword",tooltip:o},n),Ne().createElement(C.Input,{width:200,value:t,type:"string",onChange:e=>s(e.currentTarget.value),onBlur:()=>a(t)}),r&&Ne().createElement(C.Button,{"data-testid":Qe.QueryBuilder.LogsQueryBuilder.LogMessageLikeInput.input,variant:"secondary",size:"md",onClick:()=>a(""),className:Rt.Common.smallBtn,tooltip:At.components.expandBuilderButton.tooltip},l))},yr=e=>{const{labelA:t,labelB:s,value:r,onChange:a,label:n,tooltip:o}=e,l=[{label:t,value:!1},{label:s,value:!0}];return Ne().createElement("div",{className:"gf-form"},Ne().createElement(C.InlineFormLabel,{width:8,className:"query-keyword",tooltip:o},n),Ne().createElement(C.RadioButtonGroup,{options:l,value:r,onChange:e=>a(e)}))};function dr(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}function _r(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{},r=Object.keys(s);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(s).filter((function(e){return Object.getOwnPropertyDescriptor(s,e).enumerable})))),r.forEach((function(t){dr(e,t,s[t])}))}return e}function $r(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),s.push.apply(s,r)}return s}(Object(t)).forEach((function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})),e}const hr=[{label:"Count",value:a.Count},{label:"Sum",value:a.Sum},{label:"Min",value:a.Min},{label:"Max",value:a.Max},{label:"Average",value:a.Average},{label:"Any",value:a.Any}],fr=e=>{const{index:t,aggregate:s,updateAggregate:r,removeAggregate:a}=e,[n,o]=(0,Ie.useState)(!1),[l,i]=(0,Ie.useState)(s.alias||""),{aliasLabel:c}=At.components.AggregatesEditor,p=hr.slice();p.find((e=>e.value===s.aggregateType))||p.push({label:s.aggregateType,value:s.aggregateType});const m=e.columnOptions.slice();return m.find((e=>e.value===s.column))||m.push({label:s.column,value:s.column}),Ne().createElement(C.HorizontalGroup,{wrap:!0,align:"flex-start",justify:"flex-start"},Ne().createElement(C.Select,{width:20,className:Rt.Common.inlineSelect,options:p,value:s.aggregateType,onChange:e=>r(t,$r(_r({},s),{aggregateType:e.value})),menuPlacement:"bottom",allowCustomValue:!0}),Ne().createElement(C.Select,{width:40,className:Rt.Common.inlineSelect,options:m,isOpen:n,onOpenMenu:()=>o(!0),onCloseMenu:()=>o(!1),onChange:e=>r(t,$r(_r({},s),{column:e.value})),value:s.column,menuPlacement:"bottom",allowCustomValue:!0}),Ne().createElement(C.InlineFormLabel,{width:2,className:"query-keyword"},c),Ne().createElement(C.Input,{width:20,value:l,onChange:e=>i(e.currentTarget.value),onBlur:e=>r(t,$r(_r({},s),{alias:e.currentTarget.value})),placeholder:"alias"}),Ne().createElement(C.Button,{"data-testid":Ye.QueryBuilder.AggregateEditor.itemRemoveButton,className:Rt.Common.smallBtn,variant:"destructive",size:"sm",icon:"trash-alt",onClick:()=>a(t)}))},wr=e=>{const{allColumns:t,aggregates:s,onAggregatesChange:r}=e,{label:n,tooltip:o,addLabel:l}=At.components.AggregatesEditor,i=t.map((e=>({label:e.label||e.name,value:e.name})));i.push({label:"*",value:"*"});const c=e=>{const t=s.slice();t.splice(e,1),r(t)},p=(e,t)=>{const a=s.slice();a[e]=t,r(a)},m=Ne().createElement(C.InlineFormLabel,{width:8,className:"query-keyword","data-testid":Ye.QueryBuilder.AggregateEditor.sectionLabel,tooltip:o},n),u=Ne().createElement("div",{className:`width-8 ${Rt.Common.firstLabel}`});return Ne().createElement(Ne().Fragment,null,s.map(((e,t)=>{const s=`${t}-${e.column}-${e.aggregateType}-${e.alias}`;return Ne().createElement("div",{className:"gf-form",key:s,"data-testid":Ye.QueryBuilder.AggregateEditor.itemWrapper},0===t?m:u,Ne().createElement(fr,{columnOptions:i,index:t,aggregate:e,updateAggregate:p,removeAggregate:c}))})),Ne().createElement("div",{className:"gf-form"},0===s.length?m:u,Ne().createElement(C.Button,{"data-testid":Ye.QueryBuilder.AggregateEditor.addButton,icon:"plus-circle",variant:"secondary",size:"sm",onClick:()=>{const e=s.slice();e.push({column:"",aggregateType:a.Count}),r(e)},className:Rt.Common.smallBtn},l)))},xr=e=>{const{allColumns:t,groupBy:s,onGroupByChange:r}=e,[a,n]=(0,Ie.useState)(!1),{label:o,tooltip:l}=At.components.GroupByEditor,i=t.map((e=>({label:e.name,value:e.name})));return Ne().createElement("div",{className:"gf-form"},Ne().createElement(C.InlineFormLabel,{width:8,className:"query-keyword",tooltip:l},o),Ne().createElement("div",{"data-testid":Ye.QueryBuilder.GroupByEditor.multiSelectWrapper,className:Rt.Common.selectWrapper},Ne().createElement(C.MultiSelect,{options:i,isOpen:a,onOpenMenu:()=>n(!0),onCloseMenu:()=>n(!1),value:s,onChange:e=>{n(!1),r(e.map((e=>e.value)))},allowCustomValue:!0,menuPlacement:"bottom"})))},gr=e=>{const{datasource:t,builderOptions:r,builderOptionsDispatch:a}=e,o=mr(r),c=Qs(t,r.database,r.table),p=At.components.TimeSeriesQueryBuilder,m=(0,Ie.useMemo)((()=>({isAggregateMode:r.mode===s.Trend,timeColumn:ne(r,n.Time),selectedColumns:(r.columns||[]).filter((e=>e.hint!==n.Time)),aggregates:r.aggregates||[],groupBy:r.groupBy||[],orderBy:r.orderBy||[],limit:r.limit||0,filters:r.filters||[]})),[r]),u=Ks((e=>{let t=e.selectedColumns.slice();e.isAggregateMode&&(t=[]),e.timeColumn&&t.push(e.timeColumn),a(sr({mode:e.isAggregateMode?s.Trend:s.Aggregate,columns:t,aggregates:e.isAggregateMode?e.aggregates:[],groupBy:e.isAggregateMode?e.groupBy:[],filters:e.filters,orderBy:e.orderBy,limit:e.limit}))}),m);return((e,t,s,r)=>{const a=(0,Ie.useRef)(Boolean(s)),o=(0,Ie.useRef)(t||"");t!==o.current&&(a.current=!1),(0,Ie.useEffect)((()=>{if(a.current||0===e.length||!t)return;const s=e.filter(Gs)[0];if(!s)return;const l={name:s.name,type:s.type,hint:n.Time};r(or(l)),o.current=t,a.current=!0}),[e,t,r])})(c,r.table,m.timeColumn,a),((e,t,s)=>{const r=(0,Ie.useRef)(!t),a=(0,Ie.useRef)(e||"");e!==a.current&&(r.current=!1),(0,Ie.useEffect)((()=>{if(!e||r.current)return;const t=[{type:"datetime",operator:i.WithInGrafanaTimeRange,filterType:"custom",key:"",hint:n.Time,condition:"AND"}],o=[{name:"",hint:n.Time,dir:l.ASC,default:!0}];a.current=e,r.current=!0,s(sr({filters:t,orderBy:o}))}),[e,s])})(r.table,o,a),Ne().createElement("div",null,Ne().createElement(yr,{labelA:p.simpleQueryModeLabel,labelB:p.aggregateQueryModeLabel,value:m.isAggregateMode,onChange:u("isAggregateMode"),label:p.builderModeLabel,tooltip:p.builderModeTooltip}),Ne().createElement(xs,{allColumns:c,selectedColumn:m.timeColumn,invalid:!m.timeColumn,onColumnChange:u("timeColumn"),columnFilterFn:Gs,columnHint:n.Time,label:p.timeColumn.label,tooltip:p.timeColumn.tooltip,clearable:!1}),m.isAggregateMode?Ne().createElement(Ne().Fragment,null,Ne().createElement(wr,{allColumns:c,aggregates:m.aggregates,onAggregatesChange:u("aggregates")}),Ne().createElement(xr,{groupBy:m.groupBy,onGroupByChange:u("groupBy"),allColumns:c})):Ne().createElement(fs,{allColumns:c,selectedColumns:m.selectedColumns,onSelectedColumnsChange:u("selectedColumns")}),Ne().createElement(Os,{orderByOptions:Cs(r,c),orderBy:m.orderBy,onOrderByChange:u("orderBy")}),Ne().createElement(As,{limit:m.limit,onLimitChange:u("limit")}),Ne().createElement(Hs,{filters:m.filters,onFiltersChange:u("filters"),allColumns:c,datasource:t,database:r.database,table:r.table}))},vr=e=>{const{datasource:t,builderOptions:r,builderOptionsDispatch:a}=e,n=Qs(t,r.database,r.table),o=At.components.TableQueryBuilder,l=(0,Ie.useMemo)((()=>({isAggregateMode:r.mode===s.Aggregate,selectedColumns:r.columns||[],aggregates:r.aggregates||[],groupBy:r.groupBy||[],orderBy:r.orderBy||[],limit:r.limit||0,filters:r.filters||[]})),[r]),i=Ks((e=>{a(sr({mode:e.isAggregateMode?s.Aggregate:s.List,columns:e.selectedColumns,aggregates:e.aggregates,groupBy:e.groupBy,filters:e.filters,orderBy:e.orderBy,limit:e.limit}))}),l);return Ne().createElement("div",null,Ne().createElement(yr,{labelA:o.simpleQueryModeLabel,labelB:o.aggregateQueryModeLabel,value:l.isAggregateMode,onChange:i("isAggregateMode"),label:o.builderModeLabel,tooltip:o.builderModeTooltip}),Ne().createElement(fs,{allColumns:n,selectedColumns:l.selectedColumns,onSelectedColumnsChange:i("selectedColumns"),showAllOption:!0}),l.isAggregateMode&&Ne().createElement(Ne().Fragment,null,Ne().createElement(wr,{allColumns:n,aggregates:l.aggregates,onAggregatesChange:i("aggregates")}),Ne().createElement(xr,{groupBy:l.groupBy,onGroupByChange:i("groupBy"),allColumns:n})),Ne().createElement(Os,{orderByOptions:Cs(r,n),orderBy:l.orderBy,onOrderByChange:i("orderBy")}),Ne().createElement(As,{limit:l.limit,onLimitChange:i("limit")}),Ne().createElement(Hs,{filters:l.filters,onFiltersChange:i("filters"),allColumns:n,datasource:t,database:r.database,table:r.table}))},kr=e=>{const{sql:t}=e,{label:s,tooltip:r}=At.components.SqlPreview;return Ne().createElement("div",{className:"gf-form"},Ne().createElement(C.InlineFormLabel,{width:8,className:"query-keyword",tooltip:r},s),Ne().createElement("pre",null,t))},Er=e=>{const{datasource:t,onDatabaseChange:s,database:r}=e,a=(e=>{const[t,s]=(0,Ie.useState)([]);return(0,Ie.useEffect)((()=>{e&&e.fetchDatabases().then((e=>s(e))).catch((e=>{console.error("Failed to fetch databases",e)}))}),[e]),t})(t),{label:n,tooltip:o,empty:l}=At.components.DatabaseSelect,i=a.map((e=>({label:e,value:e})));return i.push({label:l,value:""}),r&&!a.includes(r)&&i.push({label:r,value:r}),(0,Ie.useEffect)((()=>{r||s(t.getDefaultDatabase())}),[t,r,s]),Ne().createElement(Ne().Fragment,null,Ne().createElement(C.InlineFormLabel,{width:8,className:"query-keyword",tooltip:o},n),Ne().createElement(C.Select,{className:`width-15 ${Rt.Common.inlineSelect}`,options:i,value:r,onChange:e=>s(e.value),menuPlacement:"bottom",allowCustomValue:!0}))},Tr=e=>{const{datasource:t,onTableChange:s,database:r,table:a}=e,n=((e,t)=>{const[s,r]=(0,Ie.useState)([]);(0,Ie.useEffect)((()=>{if(!e||!t)return;let s=!1;return e.fetchTables(t).then((e=>{s||r(e)})).catch((e=>{console.error("Failed to fetch tables for database:",t,e)})),()=>{s=!0}}),[e,t]);const a=(0,Ie.useRef)("");return t!==a.current?(a.current=t,r([]),[]):s})(t,r),{label:o,tooltip:l,empty:i}=At.components.TableSelect,c=n.map((e=>({label:e,value:e})));return c.push({label:i,value:""}),a&&!n.includes(a)&&c.push({label:a,value:a}),(0,Ie.useEffect)((()=>{r&&!a&&n.length>0&&s(t.getDefaultTable()||n[0])}),[r,a,n,t,s]),Ne().createElement(Ne().Fragment,null,Ne().createElement(C.InlineFormLabel,{width:8,className:"query-keyword",tooltip:l},o),Ne().createElement(C.Select,{className:`width-15 ${Rt.Common.inlineSelect}`,options:c,value:a,onChange:e=>s(e.value),menuPlacement:"bottom",allowCustomValue:!0}))},Or=e=>{const{datasource:t,database:s,onDatabaseChange:r,table:a,onTableChange:n}=e;return Ne().createElement("div",{className:"gf-form"},Ne().createElement(Er,{datasource:t,database:s,onDatabaseChange:r}),Ne().createElement(Tr,{datasource:t,database:s,table:a,onTableChange:n}))},Cr=[{label:At.types.QueryType.table,value:r.Table},{label:At.types.QueryType.logs,value:r.Logs},{label:At.types.QueryType.timeseries,value:r.TimeSeries},{label:At.types.QueryType.traces,value:r.Traces}],Ar=e=>{const{queryType:t,onChange:s,sqlEditor:r}=e,{label:a,tooltip:n,sqlTooltip:o}=At.components.QueryTypeSwitcher;return Ne().createElement("span",null,Ne().createElement(C.InlineFormLabel,{width:8,className:"query-keyword",tooltip:r?o:n},a),Ne().createElement(C.RadioButtonGroup,{options:Cr,value:t,onChange:s}))},Sr=e=>{const[t,s]=(0,Ie.useState)(""),{traceId:r,onChange:a,disabled:n}=e,{label:o,tooltip:l}=At.components.TraceQueryBuilder.columns.traceIdFilter;return(0,Ie.useEffect)((()=>{s(r)}),[r]),Ne().createElement("div",{className:"gf-form"},Ne().createElement(C.InlineFormLabel,{width:8,className:"query-keyword",tooltip:l},o),Ne().createElement(C.Input,{"data-testid":Ye.QueryBuilder.TraceIdInput.input,width:40,value:t,disabled:n,type:"string",onChange:e=>s(e.currentTarget.value),onBlur:()=>a(t)}))},Ir=e=>{var t,s;const{datasource:r,builderOptions:a,builderOptionsDispatch:c}=e,p=Qs(r,a.database,a.table),m=mr(a),[u,b]=(0,Ie.useState)(0===r.getDefaultTraceColumns().size&&0===(null===(t=a.columns)||void 0===t?void 0:t.length)),[y,d]=(0,Ie.useState)(u),[_,$]=(0,Ie.useState)(!((null===(s=a.meta)||void 0===s?void 0:s.isTraceIdMode)&&a.meta.traceId)),h=At.components.TraceQueryBuilder,f=(0,Ie.useMemo)((()=>{var e,t,s,r,l;return{isTraceIdMode:(null===(e=a.meta)||void 0===e?void 0:e.isTraceIdMode)||!1,otelEnabled:(null===(t=a.meta)||void 0===t?void 0:t.otelEnabled)||!1,otelVersion:(null===(s=a.meta)||void 0===s?void 0:s.otelVersion)||"",traceIdColumn:ne(a,n.TraceId),spanIdColumn:ne(a,n.TraceSpanId),parentSpanIdColumn:ne(a,n.TraceParentSpanId),serviceNameColumn:ne(a,n.TraceServiceName),operationNameColumn:ne(a,n.TraceOperationName),startTimeColumn:ne(a,n.Time),durationTimeColumn:ne(a,n.TraceDurationTime),durationUnit:(null===(r=a.meta)||void 0===r?void 0:r.traceDurationUnit)||o.Nanoseconds,tagsColumn:ne(a,n.TraceTags),serviceTagsColumn:ne(a,n.TraceServiceTags),traceId:(null===(l=a.meta)||void 0===l?void 0:l.traceId)||"",orderBy:a.orderBy||[],limit:a.limit||0,filters:a.filters||[]}}),[a]),w=Ks((e=>{const t=[e.traceIdColumn,e.spanIdColumn,e.parentSpanIdColumn,e.serviceNameColumn,e.operationNameColumn,e.startTimeColumn,e.durationTimeColumn,e.tagsColumn,e.serviceTagsColumn].filter((e=>void 0!==e));c(sr({columns:t,orderBy:e.orderBy,limit:e.limit,filters:e.filters,meta:{isTraceIdMode:e.isTraceIdMode,traceDurationUnit:e.durationUnit,traceId:e.traceId}}))}),f);((e,t,s,r)=>{const a=(0,Ie.useRef)(!1);(0,Ie.useEffect)((()=>{if(!t||a.current)return;const n=e.getDefaultTraceDatabase()||e.getDefaultDatabase(),o=e.getDefaultTraceTable()||e.getDefaultTable(),l=e.getDefaultTraceDurationUnit(),i=e.getTraceOtelVersion(),c=e.getDefaultTraceColumns(),p=[];for(let[e,t]of c)p.push({name:t,hint:e});r(sr({database:n,table:o||s.table,columns:p,meta:{otelEnabled:Boolean(i),otelVersion:i,traceDurationUnit:l}})),a.current=!0}),[s.columns,s.orderBy,s.table,r,e,t])})(r,m,a,c),((e,t,s)=>{const r=(0,Ie.useRef)(e);e||(r.current=!1),(0,Ie.useEffect)((()=>{if(!e||r.current)return;const a=K.getVersion(t),n=null==a?void 0:a.traceColumnMap;if(!n)return;const o=[];n.forEach(((e,t)=>{o.push({name:e,hint:t})})),s(sr({columns:o,meta:{traceDurationUnit:a.traceDurationUnit}})),r.current=!0}),[e,t,s])})(f.otelEnabled,f.otelVersion,c),((e,t,s,r)=>{const a=(0,Ie.useRef)(!s),o=(0,Ie.useRef)(e||"");e!==o.current&&(a.current=!1),(0,Ie.useEffect)((()=>{if(t||!e||a.current)return;const s=[{type:"datetime",operator:i.WithInGrafanaTimeRange,filterType:"custom",key:"",hint:n.Time,condition:"AND"},{type:"string",operator:i.IsEmpty,filterType:"custom",key:"",hint:n.TraceParentSpanId,condition:"AND",value:""},{type:"UInt64",operator:i.GreaterThan,filterType:"custom",key:"",hint:n.TraceDurationTime,condition:"AND",value:0},{type:"string",operator:i.IsAnything,filterType:"custom",key:"",hint:n.TraceServiceName,condition:"AND",value:""}],c=[{name:"",hint:n.Time,dir:l.DESC,default:!0},{name:"",hint:n.TraceDurationTime,dir:l.DESC,default:!0}];o.current=e,a.current=!0,r(sr({filters:s,orderBy:c}))}),[e,t,r])})(a.table,f.isTraceIdMode,m,c);const x=u&&Ne().createElement(C.Alert,{title:"",severity:"warning",buttonContent:"Close",onRemove:()=>b(!1)},Ne().createElement(C.VerticalGroup,null,Ne().createElement("div",null,"To speed up your query building, enter your default trace configuration in your ",Ne().createElement("a",{style:{textDecoration:"underline"},href:`/connections/datasources/edit/${encodeURIComponent(r.uid)}#traces-config`},"ClickHouse Data Source settings"))));return Ne().createElement("div",null,Ne().createElement(yr,{labelA:h.traceSearchModeLabel,labelB:h.traceIdModeLabel,value:f.isTraceIdMode,onChange:w("isTraceIdMode"),label:h.traceModeLabel,tooltip:h.traceModeTooltip}),Ne().createElement(C.Collapse,{label:h.columnsSection,collapsible:!0,isOpen:y,onToggle:d},x,Ne().createElement(Nt,{enabled:f.otelEnabled,onEnabledChange:e=>c(ar(e)),selectedVersion:f.otelVersion,onVersionChange:e=>c(nr(e)),wide:!0}),Ne().createElement("div",{className:"gf-form"},Ne().createElement(xs,{disabled:f.otelEnabled,allColumns:p,selectedColumn:f.traceIdColumn,invalid:!f.traceIdColumn,onColumnChange:w("traceIdColumn"),columnHint:n.TraceId,label:h.columns.traceId.label,tooltip:h.columns.traceId.tooltip,wide:!0}),Ne().createElement(xs,{disabled:f.otelEnabled,allColumns:p,selectedColumn:f.spanIdColumn,invalid:!f.spanIdColumn,onColumnChange:w("spanIdColumn"),columnHint:n.TraceSpanId,label:h.columns.spanId.label,tooltip:h.columns.spanId.tooltip,wide:!0,inline:!0})),Ne().createElement("div",{className:"gf-form"},Ne().createElement(xs,{disabled:f.otelEnabled,allColumns:p,selectedColumn:f.parentSpanIdColumn,invalid:!f.parentSpanIdColumn,onColumnChange:w("parentSpanIdColumn"),columnHint:n.TraceParentSpanId,label:h.columns.parentSpanId.label,tooltip:h.columns.parentSpanId.tooltip,wide:!0}),Ne().createElement(xs,{disabled:f.otelEnabled,allColumns:p,selectedColumn:f.serviceNameColumn,invalid:!f.serviceNameColumn,onColumnChange:w("serviceNameColumn"),columnHint:n.TraceServiceName,label:h.columns.serviceName.label,tooltip:h.columns.serviceName.tooltip,wide:!0,inline:!0})),Ne().createElement("div",{className:"gf-form"},Ne().createElement(xs,{disabled:f.otelEnabled,allColumns:p,selectedColumn:f.operationNameColumn,invalid:!f.operationNameColumn,onColumnChange:w("operationNameColumn"),columnHint:n.TraceOperationName,label:h.columns.operationName.label,tooltip:h.columns.operationName.tooltip,wide:!0}),Ne().createElement(xs,{disabled:f.otelEnabled,allColumns:p,selectedColumn:f.startTimeColumn,invalid:!f.startTimeColumn,onColumnChange:w("startTimeColumn"),columnHint:n.Time,label:h.columns.startTime.label,tooltip:h.columns.startTime.tooltip,wide:!0,inline:!0})),Ne().createElement("div",{className:"gf-form"},Ne().createElement(xs,{disabled:f.otelEnabled,allColumns:p,selectedColumn:f.durationTimeColumn,invalid:!f.durationTimeColumn,onColumnChange:w("durationTimeColumn"),columnHint:n.TraceDurationTime,label:h.columns.durationTime.label,tooltip:h.columns.durationTime.tooltip,wide:!0}),Ne().createElement(Pt,{disabled:f.otelEnabled,unit:f.durationUnit,onChange:w("durationUnit"),inline:!0})),Ne().createElement("div",{className:"gf-form"},Ne().createElement(xs,{disabled:f.otelEnabled,allColumns:p,selectedColumn:f.tagsColumn,invalid:!f.tagsColumn,onColumnChange:w("tagsColumn"),columnHint:n.TraceTags,label:h.columns.tags.label,tooltip:h.columns.tags.tooltip,wide:!0}),Ne().createElement(xs,{disabled:f.otelEnabled,allColumns:p,selectedColumn:f.serviceTagsColumn,invalid:!f.serviceTagsColumn,onColumnChange:w("serviceTagsColumn"),columnHint:n.TraceServiceTags,label:h.columns.serviceTags.label,tooltip:h.columns.serviceTags.tooltip,wide:!0,inline:!0}))),Ne().createElement(C.Collapse,{label:h.filtersSection,collapsible:!0,isOpen:_,onToggle:$},Ne().createElement(Os,{orderByOptions:Cs(a,p),orderBy:f.orderBy,onOrderByChange:w("orderBy")}),Ne().createElement(As,{limit:f.limit,onLimitChange:w("limit")}),Ne().createElement(Hs,{allColumns:p,filters:f.filters,onFiltersChange:w("filters"),datasource:r,database:a.database,table:a.table})),f.isTraceIdMode&&Ne().createElement(Sr,{traceId:f.traceId,onChange:w("traceId")}))},Nr=e=>{var t;const{datasource:s,builderOptions:a,builderOptionsDispatch:n,generatedSql:o}=e;return(null===(t=a.meta)||void 0===t?void 0:t.minimized)?Ne().createElement(Lr,{builderOptions:a,builderOptionsDispatch:n,datasource:s}):Ne().createElement("div",{"data-testid":"query-editor-section-builder"},Ne().createElement("div",{className:"gf-form "+Rt.QueryEditor.queryType},Ne().createElement(Or,{datasource:s,database:a.database,onDatabaseChange:e=>n((e=>er("set_database",{database:e}))(e)),table:a.table,onTableChange:e=>n((e=>er("set_table",{table:e}))(e))})),Ne().createElement("div",{className:"gf-form "+Rt.QueryEditor.queryType},Ne().createElement(Ar,{queryType:a.queryType,onChange:e=>n((e=>er("set_query_type",{queryType:e}))(e))})),a.queryType===r.Table&&Ne().createElement(vr,{datasource:s,builderOptions:a,builderOptionsDispatch:n}),a.queryType===r.Logs&&Ne().createElement(ur,{datasource:s,builderOptions:a,builderOptionsDispatch:n}),a.queryType===r.TimeSeries&&Ne().createElement(gr,{datasource:s,builderOptions:a,builderOptionsDispatch:n}),a.queryType===r.Traces&&Ne().createElement(Ir,{datasource:s,builderOptions:a,builderOptionsDispatch:n}),Ne().createElement(kr,{sql:o}))},Lr=e=>{var t,s,a;const{builderOptions:o,builderOptionsDispatch:l,datasource:i}=e,c=(0,Ie.useMemo)((()=>o.queryType===r.Logs?i.getDefaultLogsColumns():o.queryType===r.Traces?i.getDefaultTraceColumns():void 0),[o.queryType,i]),p=0===(null==c?void 0:c.size)&&0===(null===(t=o.columns)||void 0===t?void 0:t.length),m=o.queryType===r.Logs?"logs":o.queryType===r.Traces?"trace":o.queryType;let u;if(o.queryType===r.Traces&&(null===(s=o.meta)||void 0===s?void 0:s.isTraceIdMode)&&o.meta.traceId)u=o.meta.traceId;else if(o.queryType===r.Logs&&(null===(a=o.filters)||void 0===a?void 0:a.find((e=>e.hint===n.TraceId&&"value"in e)))){var b;u=(null===(b=o.filters)||void 0===b?void 0:b.find((e=>e.hint===n.TraceId&&"value"in e))).value}return Ne().createElement("div",{"data-testid":"query-editor-minimized-viewer"},p&&Ne().createElement(C.Alert,{title:"",severity:"warning"},Ne().createElement(C.VerticalGroup,null,Ne().createElement("div",null,`To enable data linking, enter your default ${m} configuration in your `,Ne().createElement("a",{style:{textDecoration:"underline"},href:`/connections/datasources/edit/${encodeURIComponent(i.uid)}#${o.queryType}-config`},"ClickHouse Data Source settings")))),!u&&Ne().createElement(C.Alert,{title:"",severity:"warning"},Ne().createElement(C.VerticalGroup,null,Ne().createElement("div",null,"Trace ID is empty"))),u&&Ne().createElement(Sr,{traceId:u,onChange:()=>{},disabled:!0}),Ne().createElement(C.Button,{"data-testid":Qe.QueryBuilder.expandBuilderButton,icon:"plus",variant:"secondary",size:"md",onClick:()=>l(tr("set_builder_minimized",{minimized:!1})),className:Rt.Common.smallBtn,tooltip:At.components.expandBuilderButton.tooltip},At.components.expandBuilderButton.label))};function Rr(e,t,s,r,a,n,o){try{var l=e[n](o),i=l.value}catch(e){return void s(e)}l.done?t(i):Promise.resolve(i).then(r,a)}function Dr(e,t,s){t.updateOptions({fixedOverflowWidgets:!0,scrollBeyondLastLine:!1});return void 0!==monaco.languages.getLanguages().find((t=>t.id===e))||(monaco.languages.register({id:e}),monaco.languages.registerCompletionItemProvider("sql",{triggerCharacters:[" ","$",".",","],provideCompletionItems:function(){var e,t=(e=function*(e,t){const r=e.getWordUntilPosition(t),a=e.getValueInRange({startLineNumber:1,startColumn:1,endLineNumber:t.lineNumber,endColumn:t.column}),n={startLineNumber:t.lineNumber,endLineNumber:t.lineNumber,startColumn:r.startColumn,endColumn:r.endColumn};return s(a,n)},function(){var t=this,s=arguments;return new Promise((function(r,a){var n=e.apply(t,s);function o(e){Rr(n,r,a,o,l,"next",e)}function l(e){Rr(n,r,a,o,l,"throw",e)}o(void 0)}))});return function(e,s){return t.apply(this,arguments)}}()})),monaco.editor}var qr;function jr(e,t,s,r,a,n,o){try{var l=e[n](o),i=l.value}catch(e){return void s(e)}l.done?t(i):Promise.resolve(i).then(r,a)}function Pr(e){return function(){var t=this,s=arguments;return new Promise((function(r,a){var n=e.apply(t,s);function o(e){jr(n,r,a,o,l,"next",e)}function l(e){jr(n,r,a,o,l,"throw",e)}o(void 0)}))}}function Br(){return Br=Pr((function*(e,t,s){if(e.endsWith("$"))return function(e){const t=(0,b.getTemplateSrv)();if(!t)return[];return t.getVariables().map((s=>{const r=`\${${s.name}}`,a=t.replace(r);return{label:r,detail:`(Template Variable) ${a}`,kind:qr.VARIABLE,documentation:`(Template Variable) ${a}`,insertText:`{${s.name}}`,range:e}}))}(s);const r=["select","from","where"];let a=e.replace(/[\n\r]/g," ");for(const e of r)a=a.replace(e,e.toUpperCase());if(a.endsWith("SELECT ")||a.endsWith("FROM ")||a.endsWith(", "))return void 0!==t.defaultDatabase?Fr(t,s):function(e,t){return Mr.apply(this,arguments)}(t,s);if(a.endsWith("WHERE ")){const e=a.split("FROM ");return e[e.length-1].split(" WHERE")[0].split(",").map((e=>e.trim())).map((e=>({label:e,kind:qr.TABLE,documentation:"Table",insertText:e,range:s})))}if(e.endsWith(".")){const r=e.split(" "),a=r[r.length-1].split(".");if(void 0!==t.defaultDatabase){const e=a[0];return Hr(t,s,"",e)}if(2===a.length){const e=a[0];return Fr(t,s,e)}const n=a[0],o=a[1];return Hr(t,s,n,o)}return[]})),Br.apply(this,arguments)}function Mr(){return(Mr=Pr((function*(e,t){return(yield e.databases()).map((e=>({label:e,kind:qr.DATABASE,documentation:"Database",insertText:e,range:t})))}))).apply(this,arguments)}function Fr(e,t,s){return Ur.apply(this,arguments)}function Ur(){return(Ur=Pr((function*(e,t,s){return(yield e.tables(s)).map((e=>({label:e,kind:qr.TABLE,documentation:"Table",insertText:e,range:t})))}))).apply(this,arguments)}function Hr(e,t,s,r){return Gr.apply(this,arguments)}function Gr(){return(Gr=Pr((function*(e,t,s,r){return(yield e.fields(s,r)).map((e=>({label:e,kind:qr.FIELD,documentation:"Field",insertText:e,range:t})))}))).apply(this,arguments)}!function(e){e[e.FIELD=3]="FIELD",e[e.DATABASE=8]="DATABASE",e[e.TABLE=5]="TABLE",e[e.VARIABLE=4]="VARIABLE"}(qr||(qr={}));var Vr=p(6775);const zr=["INTERVAL"];function Wr(e,t,s,r,a,n,o){try{var l=e[n](o),i=l.value}catch(e){return void s(e)}l.done?t(i):Promise.resolve(i).then(r,a)}function Kr(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}function Qr(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{},r=Object.keys(s);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(s).filter((function(e){return Object.getOwnPropertyDescriptor(s,e).enumerable})))),r.forEach((function(t){Kr(e,t,s[t])}))}return e}function Yr(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),s.push.apply(s,r)}return s}(Object(t)).forEach((function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})),e}const Xr=e=>{const{query:t,onChange:s,datasource:a}=e,n=t,o=n.queryType||r.Table,l=e=>{s(Qr(Yr(Qr({},n),{pluginVersion:De,editorType:c.SQL,format:Ge(e.queryType||o)}),e))},i={databases:()=>a.fetchDatabases(),tables:e=>a.fetchTables(e),fields:(e,t)=>a.fetchFields(e,t),defaultDatabase:a.getDefaultDatabase()},p=function(){var e,t=(e=function*(e,t){const s=yield function(e,t,s){return Br.apply(this,arguments)}(e,i,t);return Promise.resolve({suggestions:s})},function(){var t=this,s=arguments;return new Promise((function(r,a){var n=e.apply(t,s);function o(e){Wr(n,r,a,o,l,"next",e)}function l(e){Wr(n,r,a,o,l,"throw",e)}o(void 0)}))});return function(e,s){return t.apply(this,arguments)}}(),m=(e,t,s)=>{const r=function(e){try{return Vr.parse(e),{valid:!0}}catch(t){const s=t,r=s.message.split("\n"),a=s.hash.loc,n=e.split("\n"),o=n[a.first_line-1],l=o.substring(a.first_column,a.last_column);if(zr.includes(l.toUpperCase()))return{valid:!0};if(o.trim()===l){const e=n[a.first_line];if(null==e?void 0:e.trim().startsWith("$"))return{valid:!0}}return o.substring(a.last_column+1).trim().startsWith("$")?{valid:!0}:{valid:!1,error:{startLine:a.first_line,endLine:a.last_line,startCol:a.first_column+1,endCol:a.last_column+1,message:t.message,expected:r[3]}}}}(e);if(r.valid)s.setModelMarkers(t,"clickhouse",[]);else{const e=r.error;s.setModelMarkers(t,"clickhouse",[{startLineNumber:e.startLine,startColumn:e.startCol,endLineNumber:e.endLine,endColumn:e.endCol,message:e.expected,severity:8}])}},u=e=>{const t=Dr("chSql",e,p);!function(e){const t=e.getDomNode(),s=()=>{if(t){const s=Math.max(100,Math.min(1e3,e.getContentHeight())),r=parseInt(t.style.width,10);t.style.width=`${r}px`,t.style.height=`${s}px`,e.layout({width:r,height:s})}};e.onDidContentSizeChange(s),s()}(e),e.onKeyUp((s=>{if(a.settings.jsonData.validateSql){const s=e.getValue();m(s,e.getModel(),t)}}))};return Ne().createElement(Ne().Fragment,null,Ne().createElement("div",{className:"gf-form "+Rt.QueryEditor.queryType},Ne().createElement(Ar,{queryType:o,onChange:e=>l({queryType:e}),sqlEditor:!0})),Ne().createElement("div",{className:Rt.Common.wrapper},Ne().createElement(C.CodeEditor,{"aria-label":"SQL Editor",language:"sql",value:t.rawSql,onSave:e=>l({rawSql:e}),showMiniMap:!1,showLineNumbers:!0,onBlur:e=>l({rawSql:e}),onEditorDidMount:e=>u(e)})))};function Jr(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}function Zr(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{},r=Object.keys(s);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(s).filter((function(e){return Object.getOwnPropertyDescriptor(s,e).enumerable})))),r.forEach((function(t){Jr(e,t,s[t])}))}return e}function ea(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),s.push.apply(s,r)}return s}(Object(t)).forEach((function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})),e}const ta=e=>{if("builder"===e.queryType){var t;const s=ea(Zr({},e),{pluginVersion:De,editorType:c.Builder,builderOptions:sa(e.builderOptions||{}),rawSql:e.rawSql||"",refId:e.refId||"",format:e.format});return(null==e||null===(t=e.meta)||void 0===t?void 0:t.timezone)&&(s.meta={timezone:e.meta.timezone}),delete s.queryType,delete s.selectedFormat,s}const s=ea(Zr({},e),{pluginVersion:De,editorType:c.SQL,rawSql:e.rawSql||"",refId:e.refId||"",format:e.format,queryType:Ve(e.format),meta:{}});if(e.expand&&(s.expand=e.expand),e.meta){const t=e.meta;t.timezone&&(s.meta.timezone=t.timezone),t.builderOptions&&(s.meta.builderOptions=sa(t.builderOptions))}return delete s.builderOptions,delete s.selectedFormat,s},sa=e=>{const t={database:e.database||"",table:e.table||"",queryType:aa(e),columns:[]};if(e.mode&&(t.mode=e.mode),e.fields||Array.isArray(e.fields)){const s=e.fields;t.columns=s.map((e=>({name:e})))}const s=e.timeField,r=e.timeFieldType;if(s){const e={name:s,type:r,hint:n.Time};t.columns.push(e)}const a=e.logLevelField;if(a){const e={name:a,hint:n.LogLevel};t.columns.push(e)}if(e.metrics||Array.isArray(e.metrics)){const s=e.metrics;t.aggregates=s.map((e=>({aggregateType:e.aggregation,column:e.field,alias:e.alias})))}if(e.filters||Array.isArray(e.filters)){const r=e.filters;t.filters=r.map((e=>{const t=Zr({},e);return e.key===s?t.hint=n.Time:e.key===a&&(t.hint=n.LogLevel),t}))}return(e.groupBy||Array.isArray(e.groupBy))&&(t.groupBy=e.groupBy),(e.orderBy||Array.isArray(e.orderBy))&&(t.orderBy=e.orderBy),void 0!==e.limit&&e.limit>=0&&(t.limit=e.limit),t},ra=e=>{const t=!e.pluginVersion||!Pe(e.pluginVersion,"4.0.0"),s="sql"===e.queryType||"builder"===e.queryType;return t||s},aa=e=>e.timeField?r.TimeSeries:e.logLevelField?r.Logs:r.Table;function na(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}function oa(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{},r=Object.keys(s);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(s).filter((function(e){return Object.getOwnPropertyDescriptor(s,e).enumerable})))),r.forEach((function(t){na(e,t,s[t])}))}return e}function la(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var s=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);t&&(r=r.filter((function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable}))),s.push.apply(s,r)}return s}(Object(t)).forEach((function(s){Object.defineProperty(e,s,Object.getOwnPropertyDescriptor(t,s))})),e}const ia=e=>{const{query:t,onChange:s,app:r}=e,[a,n]=(e=>{const[t,s]=(0,Ie.useReducer)(lr,e,cr);return[t,s]})(t.builderOptions),o=t.key||"",l=(0,Ie.useRef)(o);o!==l.current&&t.editorType===c.Builder&&(n(rr(t.builderOptions||{})),l.current=o);const i=(0,Ie.useRef)();t.editorType!==i.current&&t.editorType===c.Builder&&(n(rr(t.builderOptions||{})),i.current=t.editorType);const p=(0,Ie.useRef)(!0);return Ue(a)&&(p.current=!1),(0,Ie.useEffect)((()=>{p.current||t.editorType===c.SQL||s(la(oa({},t),{pluginVersion:De,editorType:c.Builder,rawSql:J(a),builderOptions:a,format:He(a)}))}),[a]),t.editorType===c.SQL?Ne().createElement("div",{"data-testid":"query-editor-section-sql"},Ne().createElement(Xr,e)):Ne().createElement(Nr,{datasource:e.datasource,builderOptions:a,builderOptionsDispatch:n,generatedSql:t.rawSql,app:r})},ca=JSON.parse('{"id":"grafana-clickhouse-datasource"}');function pa(e,t,s){return t in e?Object.defineProperty(e,t,{value:s,enumerable:!0,configurable:!0,writable:!0}):e[t]=s,e}const ma=new u.DataSourcePlugin(ot).setConfigEditor((e=>{const{options:t,onOptionsChange:s}=e,{jsonData:r,secureJsonFields:a}=t,n=At.components.Config.ConfigEditor,l=t.secureJsonData||{},i=a&&a.tlsCACert,c=a&&a.tlsClientCert,p=a&&a.tlsClientKey,m=[{label:"Native",value:pt.Native},{label:"HTTP",value:pt.Http}];((e,t)=>{const s=(0,Ie.useRef)(!1);(0,Ie.useEffect)((()=>{if(s.current)return;const r=Gt({},e.jsonData);r.version=De;const a=r.server;a&&!r.host&&(r.host=a),delete r.server;const n=r.timeout;n&&!r.dialTimeout&&(r.dialTimeout=n),delete r.timeout,r.protocol||(r.protocol=pt.Native),r.logs&&void 0!==r.logs.defaultTable||(r.logs=Vt(Gt({},r.logs),{defaultTable:U,selectContextColumns:!0,contextColumns:[]})),r.traces&&void 0!==r.traces.defaultTable||(r.traces=Vt(Gt({},r.traces),{defaultTable:H})),t(Vt(Gt({},e),{jsonData:r})),s.current=!0}),[e,t])})(t,s);const y=(e,r)=>{s(Yt(Qt({},t),{jsonData:Yt(Qt({},t.jsonData),{[e]:r})}))},d=(e,r)=>{s(Yt(Qt({},t),{jsonData:Yt(Qt({},t.jsonData),{[e]:r})}))},_=(e,r)=>{s(Yt(Qt({},t),{secureJsonData:Yt(Qt({},l),{[e]:r})}))},$=e=>{s(Yt(Qt({},t),{secureJsonFields:Yt(Qt({},a),{[e]:!1}),secureJsonData:Yt(Qt({},l),{[e]:""})}))},h=e=>{s(Yt(Qt({},t),{jsonData:Yt(Qt({},t.jsonData),{customSettings:e.filter((e=>!!e.setting&&!!e.value))})}))},f=(e,r)=>{s(Yt(Qt({},t),{jsonData:Yt(Qt({},t.jsonData),{logs:Yt(Qt({},t.jsonData.logs),{[e]:r})})}))},w=(e,r)=>{var a;s(Yt(Qt({},t),{jsonData:Yt(Qt({},t.jsonData),{traces:Yt(Qt({},t.jsonData.traces),{durationUnit:(null===(a=t.jsonData.traces)||void 0===a?void 0:a.durationUnit)||o.Nanoseconds,[e]:r})})}))},[x,g]=(0,Ie.useState)(r.customSettings||[]),v=Boolean(window.location.hash||t.jsonData.defaultDatabase||t.jsonData.defaultTable||t.jsonData.dialTimeout||t.jsonData.queryTimeout||t.jsonData.validateSql||t.jsonData.enableSecureSocksProxy||t.jsonData.customSettings||t.jsonData.logs||t.jsonData.traces),k=r.secure?r.protocol===pt.Native?n.serverPort.secureNativePort:n.serverPort.secureHttpPort:r.protocol===pt.Native?n.serverPort.insecureNativePort:n.serverPort.insecureHttpPort,E=`${n.serverPort.tooltip} (default for ${r.secure?"secure":""} ${r.protocol}: ${k})`,T=!t.uid&&Ne().createElement(C.Alert,{title:"",severity:"warning",buttonContent:"Close"},Ne().createElement(C.VerticalGroup,null,Ne().createElement("div",null,"This datasource is missing the",Ne().createElement("code",null,"uid"),"field in its configuration. If your datasource is ",Ne().createElement("a",{style:{textDecoration:"underline"},href:"https://grafana.com/docs/grafana/latest/administration/provisioning/#data-sources",target:"_blank",rel:"noreferrer"},"provisioned via YAML"),", please verify the UID is set. This is required to enable data linking between logs and traces.")));return Ne().createElement(Ne().Fragment,null,T,Ne().createElement(Ot,{dataSourceName:"Clickhouse",docsLink:"https://grafana.com/grafana/plugins/grafana-clickhouse-datasource/",hasRequiredFields:!0}),Ne().createElement(Ct,null),Ne().createElement(ft,{title:"Server"},Ne().createElement(C.Field,{required:!0,label:n.serverAddress.label,description:n.serverAddress.tooltip,invalid:!r.host,error:n.serverAddress.error},Ne().createElement(C.Input,{name:"host",width:80,value:r.host||"",onChange:(0,u.onUpdateDatasourceJsonDataOption)(e,"host"),label:n.serverAddress.label,"aria-label":n.serverAddress.label,placeholder:n.serverAddress.placeholder})),Ne().createElement(C.Field,{required:!0,label:n.serverPort.label,description:E,invalid:!r.port,error:n.serverPort.error},Ne().createElement(C.Input,{name:"port",width:40,type:"number",value:r.port||"",onChange:e=>{return r=e.currentTarget.value,void s(Yt(Qt({},t),{jsonData:Yt(Qt({},t.jsonData),{port:+r})}));var r},label:n.serverPort.label,"aria-label":n.serverPort.label,placeholder:k})),Ne().createElement(C.Field,{label:n.protocol.label,description:n.protocol.tooltip},Ne().createElement(C.RadioButtonGroup,{options:m,disabledOptions:[],value:r.protocol||pt.Native,onChange:e=>{return r=e,void s(Yt(Qt({},t),{jsonData:Yt(Qt({},t.jsonData),{protocol:r})}));var r}})),Ne().createElement(C.Field,{label:n.secure.label,description:n.secure.tooltip},Ne().createElement(C.Switch,{id:"secure",className:"gf-form",value:r.secure||!1,onChange:e=>d("secure",e.currentTarget.checked)})),r.protocol===pt.Http&&Ne().createElement(C.Field,{label:n.path.label,description:n.path.tooltip},Ne().createElement(C.Input,{value:r.path||"",name:"path",width:80,onChange:(0,u.onUpdateDatasourceJsonDataOption)(e,"path"),label:n.path.label,"aria-label":n.path.label,placeholder:n.path.placeholder}))),r.protocol===pt.Http&&Ne().createElement(Mt,{headers:t.jsonData.httpHeaders,forwardGrafanaHeaders:t.jsonData.forwardGrafanaHeaders,secureFields:t.secureJsonFields,onHttpHeadersChange:e=>((e,t,s)=>{const r=[],a={},n={};for(let t of e)if(t.name){if(t.secure){const e=`secureHttpHeaders.${t.name}`;a[e]=!0,t.value&&(n[e]=t.value,t.value="")}r.push(t)}const o=Gt({},t.secureJsonFields);for(let e in o)!a[e]&&e.startsWith("secureHttpHeaders.")&&(a[e]=!1,n[e]="");s(Vt(Gt({},t),{jsonData:Vt(Gt({},t.jsonData),{httpHeaders:r}),secureJsonFields:Gt({},t.secureJsonFields,a),secureJsonData:Gt({},t.secureJsonData,n)}))})(e,t,s),onForwardGrafanaHeadersChange:e=>d("forwardGrafanaHeaders",e)}),Ne().createElement(Ct,null),Ne().createElement(ft,{title:"TLS / SSL Settings"},Ne().createElement(C.Field,{label:n.tlsSkipVerify.label,description:n.tlsSkipVerify.tooltip},Ne().createElement(C.Switch,{className:"gf-form",value:r.tlsSkipVerify||!1,onChange:e=>y("tlsSkipVerify",e.currentTarget.checked)})),Ne().createElement(C.Field,{label:n.tlsClientAuth.label,description:n.tlsClientAuth.tooltip},Ne().createElement(C.Switch,{className:"gf-form",value:r.tlsAuth||!1,onChange:e=>y("tlsAuth",e.currentTarget.checked)})),Ne().createElement(C.Field,{label:n.tlsAuthWithCACert.label,description:n.tlsAuthWithCACert.tooltip},Ne().createElement(C.Switch,{className:"gf-form",value:r.tlsAuthWithCACert||!1,onChange:e=>y("tlsAuthWithCACert",e.currentTarget.checked)})),r.tlsAuthWithCACert&&Ne().createElement(ct,{hasCert:!!i,onChange:e=>_("tlsCACert",e.currentTarget.value),placeholder:n.tlsCACert.placeholder,label:n.tlsCACert.label,onClick:()=>$("tlsCACert")}),r.tlsAuth&&Ne().createElement(Ne().Fragment,null,Ne().createElement(ct,{hasCert:!!c,onChange:e=>_("tlsClientCert",e.currentTarget.value),placeholder:n.tlsClientCert.placeholder,label:n.tlsClientCert.label,onClick:()=>$("tlsClientCert")}),Ne().createElement(ct,{hasCert:!!p,placeholder:n.tlsClientKey.placeholder,label:n.tlsClientKey.label,onChange:e=>_("tlsClientKey",e.currentTarget.value),onClick:()=>$("tlsClientKey")}))),Ne().createElement(Ct,null),Ne().createElement(ft,{title:"Credentials"},Ne().createElement(C.Field,{label:n.username.label,description:n.username.tooltip},Ne().createElement(C.Input,{name:"user",width:40,value:r.username||"",onChange:(0,u.onUpdateDatasourceJsonDataOption)(e,"username"),label:n.username.label,"aria-label":n.username.label,placeholder:n.username.placeholder})),Ne().createElement(C.Field,{label:n.password.label,description:n.password.tooltip},Ne().createElement(C.SecretInput,{name:"pwd",width:40,label:n.password.label,"aria-label":n.password.label,placeholder:n.password.placeholder,value:l.password||"",isConfigured:a&&a.password,onReset:()=>{s(Yt(Qt({},t),{secureJsonFields:Yt(Qt({},t.secureJsonFields),{password:!1}),secureJsonData:Yt(Qt({},t.secureJsonData),{password:""})}))},onChange:(0,u.onUpdateDatasourceSecureJsonDataOption)(e,"password")}))),Ne().createElement(Ct,null),Ne().createElement(ft,{title:"Additional settings",description:"Additional settings are optional settings that can be configured for more control over your data source. This includes the default database, dial and query timeouts, SQL validation, and custom ClickHouse settings.",isCollapsible:!0,isInitiallyOpen:v},Ne().createElement(Ct,null),Ne().createElement(St,{defaultDatabase:r.defaultDatabase,defaultTable:r.defaultTable,onDefaultDatabaseChange:(0,u.onUpdateDatasourceJsonDataOption)(e,"defaultDatabase"),onDefaultTableChange:(0,u.onUpdateDatasourceJsonDataOption)(e,"defaultTable")}),Ne().createElement(Ct,null),Ne().createElement(It,{dialTimeout:r.dialTimeout,queryTimeout:r.queryTimeout,validateSql:r.validateSql,onDialTimeoutChange:(0,u.onUpdateDatasourceJsonDataOption)(e,"dialTimeout"),onQueryTimeoutChange:(0,u.onUpdateDatasourceJsonDataOption)(e,"queryTimeout"),onValidateSqlChange:e=>d("validateSql",e.currentTarget.checked)}),Ne().createElement(Ct,null),Ne().createElement(qt,{logsConfig:r.logs,onDefaultDatabaseChange:e=>f("defaultDatabase",e),onDefaultTableChange:e=>f("defaultTable",e),onOtelEnabledChange:e=>f("otelEnabled",e),onOtelVersionChange:e=>f("otelVersion",e),onTimeColumnChange:e=>f("timeColumn",e),onLevelColumnChange:e=>f("levelColumn",e),onMessageColumnChange:e=>f("messageColumn",e),onSelectContextColumnsChange:e=>f("selectContextColumns",e),onContextColumnsChange:e=>f("contextColumns",e)}),Ne().createElement(Ct,null),Ne().createElement(Bt,{tracesConfig:r.traces,onDefaultDatabaseChange:e=>w("defaultDatabase",e),onDefaultTableChange:e=>w("defaultTable",e),onOtelEnabledChange:e=>w("otelEnabled",e),onOtelVersionChange:e=>w("otelVersion",e),onTraceIdColumnChange:e=>w("traceIdColumn",e),onSpanIdColumnChange:e=>w("spanIdColumn",e),onOperationNameColumnChange:e=>w("operationNameColumn",e),onParentSpanIdColumnChange:e=>w("parentSpanIdColumn",e),onServiceNameColumnChange:e=>w("serviceNameColumn",e),onDurationColumnChange:e=>w("durationColumn",e),onDurationUnitChange:e=>w("durationUnit",e),onStartTimeColumnChange:e=>w("startTimeColumn",e),onTagsColumnChange:e=>w("tagsColumn",e),onServiceTagsColumnChange:e=>w("serviceTagsColumn",e)}),Ne().createElement(Ct,null),Ne().createElement(zt,{aliasTables:r.aliasTables,onAliasTablesChange:e=>{s(Yt(Qt({},t),{jsonData:Yt(Qt({},t.jsonData),{aliasTables:e})}))}}),Ne().createElement(Ct,null),b.config.secureSocksDSProxyEnabled&&(0,mt.gte)(b.config.buildInfo.version,"10.0.0")&&Ne().createElement(C.Field,{label:n.secureSocksProxy.label,description:n.secureSocksProxy.tooltip},Ne().createElement(C.Switch,{className:"gf-form",value:r.enableSecureSocksProxy||!1,onChange:e=>d("enableSecureSocksProxy",e.currentTarget.checked)})),Ne().createElement(vt,{title:"Custom Settings"},x.map((({setting:e,value:t},s)=>Ne().createElement(C.HorizontalGroup,{key:s},Ne().createElement(C.Field,{label:"Setting","aria-label":"Setting"},Ne().createElement(C.Input,{value:e,placeholder:"Setting",onChange:e=>{let r=x.concat();r[s]={setting:e.target.value,value:t},g(r)},onBlur:()=>{h(x)}})),Ne().createElement(C.Field,{label:"Value","aria-label":"Value"},Ne().createElement(C.Input,{value:t,placeholder:"Value",onChange:t=>{let r=x.concat();r[s]={setting:e,value:t.target.value},g(r)},onBlur:()=>{h(x)}}))))),Ne().createElement(C.Button,{variant:"secondary",icon:"plus",type:"button",onClick:()=>{g([...x,{setting:"",value:""}])}},"Add custom setting"))))})).setQueryEditor((e=>{const{datasource:t,query:s,onRunQuery:r}=e,a=(e=>void 0===e.rawSql?e:ra(e)?ta(e):e)(s);return Ne().createElement(Ne().Fragment,null,Ne().createElement("div",{className:"gf-form "+Rt.QueryEditor.queryType},Ne().createElement(hs,la(oa({},e),{query:a,datasource:t})),Ne().createElement(C.Button,{onClick:()=>r()},"Run Query")),Ne().createElement(ia,la(oa({},e),{query:a})))}));(0,b.getAppEvents)().subscribe(u.DashboardLoadedEvent,(({payload:{dashboardId:e,orgId:t,grafanaVersion:a,queries:n}})=>{var o;const l=null===(o=n[ca.id])||void 0===o?void 0:o.filter((e=>!e.hide));var i;(null==l?void 0:l.length)&&(i=function(e){for(var t=1;t<arguments.length;t++){var s=null!=arguments[t]?arguments[t]:{},r=Object.keys(s);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(s).filter((function(e){return Object.getOwnPropertyDescriptor(s,e).enumerable})))),r.forEach((function(t){pa(e,t,s[t])}))}return e}({clickhouse_plugin_version:Le,grafana_version:a,dashboard_id:e,org_id:t},(e=>{const t={sql_queries:0,sql_query_type_table:0,sql_query_type_logs:0,sql_query_type_timeseries:0,sql_query_type_traces:0,builder_queries:0,builder_query_type_table:0,builder_query_type_table_simple:0,builder_query_type_table_aggregate:0,builder_query_type_logs:0,builder_query_type_timeseries:0,builder_query_type_timeseries_simple:0,builder_query_type_timeseries_aggregate:0,builder_query_type_traces:0,builder_query_type_traces_search:0,builder_query_type_traces_id:0,builder_minimized_queries:0,builder_otel_queries:0};return e.forEach((e=>{if(e.editorType===c.SQL)t.sql_queries++,e.queryType===r.Table?t.sql_query_type_table++:e.queryType===r.Logs?t.sql_query_type_logs++:e.queryType===r.TimeSeries?t.sql_query_type_timeseries++:e.queryType===r.Traces&&t.sql_query_type_traces++;else if(e.editorType===c.Builder){var a,n;if(t.builder_queries++,!e.builderOptions)return;if(e.builderOptions.queryType===r.Table)t.builder_query_type_table++,e.builderOptions.mode===s.Aggregate?t.builder_query_type_table_aggregate++:t.builder_query_type_table_simple++;else if(e.builderOptions.queryType===r.Logs)t.builder_query_type_logs++;else if(e.builderOptions.queryType===r.TimeSeries)t.builder_query_type_timeseries++,e.builderOptions.mode===s.Trend?t.builder_query_type_timeseries_aggregate++:t.builder_query_type_timeseries_simple++;else if(e.builderOptions.queryType===r.Traces){var o;t.builder_query_type_traces++,(null===(o=e.builderOptions.meta)||void 0===o?void 0:o.isTraceIdMode)?t.builder_query_type_traces_id++:t.builder_query_type_traces_search++}(null===(a=e.builderOptions.meta)||void 0===a?void 0:a.minimized)&&t.builder_minimized_queries++,(null===(n=e.builderOptions.meta)||void 0===n?void 0:n.otelEnabled)&&t.builder_otel_queries++}})),t})(l)),(0,b.reportInteraction)("grafana_ds_clickhouse_dashboard_loaded",i))}))})(),m})()));
//# sourceMappingURL=module.js.map