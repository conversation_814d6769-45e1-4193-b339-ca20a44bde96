{"$schema": "https://raw.githubusercontent.com/grafana/grafana/main/docs/sources/developers/plugins/plugin.schema.json", "alerting": true, "annotations": true, "backend": true, "category": "sql", "dependencies": {"grafanaDependency": ">=9.5.0", "plugins": []}, "executable": "gpx_clickhouse", "id": "grafana-clickhouse-datasource", "includes": [{"name": "Query Analysis", "path": "dashboards/query-analysis.json", "type": "dashboard"}, {"name": "Data Analysis", "path": "dashboards/data-analysis.json", "type": "dashboard"}, {"name": "Cluster Analysis", "path": "dashboards/cluster-analysis.json", "type": "dashboard"}, {"name": "ClickHouse OTel Dashboard", "path": "dashboards/opentelemetry-clickhouse.json", "type": "dashboard"}, {"name": "ClickHouse System Dashboards", "path": "dashboards/system-dashboards.json", "type": "dashboard"}], "info": {"author": {"name": "Grafana Labs"}, "build": {"time": 1732723429280, "repo": "https://github.com/grafana/clickhouse-datasource", "branch": "main", "hash": "1d56c57c04de91749cff198f86292e04c4e1138d", "build": 2565}, "description": "ClickHouse datasource plugin for Grafana", "keywords": ["Simple"], "links": [{"name": "Website", "url": "https://github.com/grafana/clickhouse-datasource"}, {"name": "License", "url": "https://github.com/grafana/clickhouse-datasource/blob/master/LICENSE"}], "logos": {"large": "img/logo.svg", "small": "img/logo.svg"}, "screenshots": [], "updated": "2024-11-27", "version": "4.5.1"}, "logs": true, "metrics": true, "name": "ClickHouse", "tracing": true, "type": "datasource"}