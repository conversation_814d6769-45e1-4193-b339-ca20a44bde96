{"$schema": "https://raw.githubusercontent.com/grafana/grafana/main/docs/sources/developers/plugins/plugin.schema.json", "type": "datasource", "name": "ClickHouse", "id": "grafana-clickhouse-datasource", "metrics": true, "backend": true, "logs": true, "tracing": true, "alerting": true, "annotations": true, "executable": "gpx_clickhouse", "includes": [{"type": "dashboard", "name": "Query Analysis", "path": "dashboards/query-analysis.json"}, {"type": "dashboard", "name": "Data Analysis", "path": "dashboards/data-analysis.json"}, {"type": "dashboard", "name": "Cluster Analysis", "path": "dashboards/cluster-analysis.json"}, {"type": "dashboard", "name": "ClickHouse OTel Dashboard", "path": "dashboards/opentelemetry-clickhouse.json"}, {"type": "dashboard", "name": "ClickHouse System Dashboards", "path": "dashboards/system-dashboards.json"}], "category": "sql", "info": {"description": "ClickHouse datasource plugin for Grafana", "author": {"name": "Grafana Labs"}, "keywords": ["Simple"], "logos": {"small": "img/logo.svg", "large": "img/logo.svg"}, "links": [{"name": "Website", "url": "https://github.com/grafana/clickhouse-datasource"}, {"name": "License", "url": "https://github.com/grafana/clickhouse-datasource/blob/master/LICENSE"}], "screenshots": [], "version": "4.10.2", "updated": "2025-08-14"}, "dependencies": {"grafanaDependency": ">=9.5.0", "plugins": []}}