{"__inputs": [], "__elements": [], "__requires": [{"type": "panel", "id": "barchart", "name": "Bar chart", "version": ""}, {"type": "panel", "id": "bargauge", "name": "Bar gauge", "version": ""}, {"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "9.0.1"}, {"type": "datasource", "id": "grafana-clickhouse-datasource", "name": "ClickHouse", "version": "2.0.0"}, {"type": "panel", "id": "stat", "name": "Stat", "version": ""}, {"type": "panel", "id": "table", "name": "Table", "version": ""}, {"type": "panel", "id": "timeseries", "name": "Time series", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "iteration": 1661857991116, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "semi-dark-blue", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Version"}, "properties": [{"id": "unit", "value": "string"}]}]}, "gridPos": {"h": 6, "w": 3, "x": 0, "y": 0}, "id": 4, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "/.*/", "values": false}, "textMode": "auto"}, "pluginVersion": "9.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT version()", "refId": "A"}], "transformations": [{"id": "convertFieldType", "options": {"conversions": [{"destinationType": "string", "targetField": "Version"}], "fields": {}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"version()": "Version"}}}], "type": "stat"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "semi-dark-blue", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "uptime"}, "properties": [{"id": "unit", "value": "s"}]}]}, "gridPos": {"h": 6, "w": 3, "x": 3, "y": 0}, "id": 2, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT uptime() as uptime", "refId": "A"}], "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"uptime": "Server uptime"}}}], "type": "stat"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "light-blue", "value": null}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 3, "x": 6, "y": 0}, "id": 22, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "value_and_name"}, "pluginVersion": "9.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT  sum(total_rows) as \"Total rows\" FROM system.tables WHERE database IN (${database}) AND name IN (${table})", "refId": "A"}], "transformations": [], "type": "stat"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "light-blue", "value": null}]}}, "overrides": []}, "gridPos": {"h": 6, "w": 3, "x": 9, "y": 0}, "id": 23, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "value_and_name"}, "pluginVersion": "9.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT count() as \"Total columns\" FROM system.columns WHERE database IN (${database}) AND table IN (${table})", "refId": "A"}], "transformations": [], "type": "stat"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Used"}, "properties": [{"id": "unit", "value": "percentunit"}, {"id": "custom.displayMode", "value": "lcd-gauge"}, {"id": "max", "value": 1}, {"id": "min", "value": 0}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "semi-dark-green", "value": 0.6}, {"color": "#EAB839", "value": 0.7}, {"color": "semi-dark-orange", "value": 0.75}, {"color": "semi-dark-red", "value": 0.8}]}}, {"id": "custom.width", "value": 357}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Name"}, "properties": [{"id": "custom.width", "value": 74}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Free space"}, "properties": [{"id": "custom.width", "value": 96}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Path"}, "properties": [{"id": "custom.width", "value": 277}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total space"}, "properties": [{"id": "custom.width", "value": 103}]}]}, "gridPos": {"h": 6, "w": 12, "x": 12, "y": 0}, "id": 9, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.0.1", "targets": [{"builderOptions": {"fields": [], "limit": 100, "mode": "list"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT\n    name as Name,\n    path as Path,\n    formatReadableSize(free_space) as Free,\n    formatReadableSize(total_space) as Total,\n    1 - free_space/total_space as Used\nFROM system.disks", "refId": "A"}], "title": "Disk usage", "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"Free": "Free space", "Name": "", "Path": "", "Total": "Total space", "Used": ""}}}], "type": "table"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 8, "x": 0, "y": 6}, "id": 25, "options": {"displayMode": "lcd", "minVizHeight": 10, "minVizWidth": 0, "orientation": "horizontal", "reduceOptions": {"calcs": [], "fields": "", "values": true}, "showUnfilled": true}, "pluginVersion": "9.0.1", "targets": [{"builderOptions": {"fields": [], "limit": 100, "mode": "list"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT concatAssumeInjective(table.database, '.', name) as name,\n       table_stats.total_rows as total_rows\nFROM system.tables table\n         LEFT JOIN ( SELECT table,\n       database,\n       sum(rows)                  as total_rows\nFROM system.parts\nWHERE table IN (${table}) AND active AND database IN (${database}) \nGROUP BY table, database\n ) AS table_stats ON table.name = table_stats.table AND table.database = table_stats.database\nORDER BY total_rows DESC\nLIMIT 10", "refId": "A"}], "title": "Top tables by rows", "type": "bargauge"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 7, "x": 8, "y": 6}, "id": 26, "options": {"displayMode": "lcd", "minVizHeight": 10, "minVizWidth": 0, "orientation": "horizontal", "reduceOptions": {"calcs": [], "fields": "", "values": true}, "showUnfilled": true}, "pluginVersion": "9.0.1", "targets": [{"builderOptions": {"fields": [], "limit": 100, "mode": "list"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT concatAssumeInjective(table.database, '.', name) as name,\n       col_stats.col_count as total_columns\nFROM system.tables table\n         LEFT JOIN (SELECT database, table, count() as col_count FROM system.columns  GROUP BY table, database) as col_stats\n                   ON table.name = col_stats.table AND col_stats.database = table.database\nWHERE database IN (${database}) AND name != '' AND table IN (${table}) AND name != '' ORDER BY total_columns DESC LIMIT 10;\n", "refId": "A"}], "title": "Top tables by columns", "type": "bargauge"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "fillOpacity": 61, "gradientMode": "hue", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 4, "x": 15, "y": 6}, "id": 12, "options": {"barRadius": 0, "barWidth": 1, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "orientation": "auto", "showValue": "auto", "stacking": "none", "text": {}, "tooltip": {"mode": "single", "sort": "none"}, "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "targets": [{"builderOptions": {"fields": [], "limit": 100, "mode": "list"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT engine, count() \"Number of databases\" FROM system.databases WHERE name IN (${database}) GROUP BY engine ", "refId": "A"}], "title": "Database engines", "type": "barchart"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisGridShow": false, "axisLabel": "", "axisPlacement": "auto", "axisSoftMin": 0, "fillOpacity": 61, "gradientMode": "hue", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1, "scaleDistribution": {"type": "linear"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 9, "w": 5, "x": 19, "y": 6}, "id": 11, "options": {"barRadius": 0, "barWidth": 0.97, "groupWidth": 0.7, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "orientation": "auto", "showValue": "auto", "stacking": "none", "tooltip": {"mode": "single", "sort": "none"}, "xTickLabelRotation": 0, "xTickLabelSpacing": 0}, "targets": [{"builderOptions": {"fields": [], "limit": 100, "mode": "list"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT engine, count() \"Number of tables\" FROM system.tables WHERE database IN (${database}) AND notLike(engine,'System%')  AND name IN (${table}) GROUP BY engine ORDER BY count() DESC", "refId": "A"}], "title": "Table engines", "type": "barchart"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Engine"}, "properties": [{"id": "custom.width", "value": 200}, {"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Number of tables"}, "properties": [{"id": "custom.width", "value": 128}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total rows"}, "properties": [{"id": "custom.width", "value": 113}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Column count"}, "properties": [{"id": "custom.width", "value": 126}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Part count"}, "properties": [{"id": "custom.width", "value": 98}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Database"}, "properties": [{"id": "custom.filterable", "value": true}, {"id": "custom.width", "value": 205}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Partition count"}, "properties": [{"id": "custom.width", "value": 143}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Size on disk"}, "properties": [{"id": "custom.width", "value": 203}]}]}, "gridPos": {"h": 10, "w": 15, "x": 0, "y": 15}, "id": 6, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.0.1", "targets": [{"builderOptions": {"fields": [], "limit": 100, "mode": "list"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT name,\n       engine,\n       tables,\n       partitions,\n       parts,\n       formatReadableSize(bytes_on_disk)            \"disk_size\",\n       col_count,\n       total_rows,\n       formatReadableSize(data_uncompressed_bytes) as \"uncompressed_size\"\nFROM system.databases db\n         LEFT JOIN ( SELECT database,\n                            uniq(table)                   \"tables\",\n                            uniq(table, partition)        \"partitions\",\n                            count()                    AS parts,\n                            sum(bytes_on_disk)            \"bytes_on_disk\",\n                            sum(data_uncompressed_bytes) as \"data_uncompressed_bytes\",\n                            sum(rows) as total_rows,\n                            max(col_count) as \"col_count\"\n                     FROM system.parts AS parts\n                               JOIN (SELECT database, count() as col_count\n                                         FROM system.columns\n                                         WHERE database IN (${database}) AND table IN (${table})\n                                         GROUP BY database) as col_stats\n                                        ON parts.database = col_stats.database\n                     WHERE database IN (${database}) AND active AND table IN (${table})\n                     GROUP BY database) AS db_stats ON db.name = db_stats.database\nWHERE database IN (${database}) AND lower(name) != 'information_schema'\nORDER BY bytes_on_disk DESC\nLIMIT 10;", "refId": "A"}], "title": "Database summary", "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {"col_count": 4, "disk_size": 7, "engine": 1, "name": 0, "partitions": 5, "parts": 6, "tables": 2, "total_rows": 3, "uncompressed_size": 8}, "renameByName": {"col_count": "Column count", "disk_size": "Size on disk", "engine": "Engine", "name": "Database", "partitions": "Partition count", "parts": "Part count", "tables": "Number of tables", "total_rows": "Total rows", "uncompressed_size": "Uncompressed size"}}}], "type": "table"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Source"}, "properties": [{"id": "custom.width", "value": 85}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Type"}, "properties": [{"id": "custom.width", "value": 64}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Status"}, "properties": [{"id": "custom.width", "value": 71}]}]}, "gridPos": {"h": 10, "w": 9, "x": 15, "y": 15}, "id": 14, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.0.1", "targets": [{"builderOptions": {"fields": [], "limit": 100, "mode": "list"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT source, type, status, count() \"count\" FROM system.dictionaries GROUP BY source, type, status ORDER BY status DESC, source", "refId": "A"}], "title": "Dictionaries", "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"count": "Usages", "source": "Source", "status": "Status", "type": "Type"}}}], "type": "table"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"custom": {"align": "auto", "displayMode": "auto", "filterable": false, "inspect": false}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Engine"}, "properties": [{"id": "custom.width", "value": 86}, {"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total rows"}, "properties": [{"id": "custom.width", "value": 116}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Column count"}, "properties": [{"id": "custom.width", "value": 156}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Partition count"}, "properties": [{"id": "custom.width", "value": 138}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Part count"}, "properties": [{"id": "custom.width", "value": 113}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Size on disk"}, "properties": [{"id": "custom.width", "value": 145}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Row Count"}, "properties": [{"id": "custom.width", "value": 109}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Database"}, "properties": [{"id": "custom.width", "value": 286}, {"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Table"}, "properties": [{"id": "custom.width", "value": 236}, {"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Uncompressed size"}, "properties": [{"id": "custom.width", "value": 171}]}]}, "gridPos": {"h": 10, "w": 15, "x": 0, "y": 25}, "id": 7, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": true, "displayName": "Column count"}]}, "pluginVersion": "9.0.1", "targets": [{"builderOptions": {"fields": [], "limit": 100, "mode": "list"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT name,\n       table.database,\n       engine,\n       partitions,\n       parts,\n       formatReadableSize(bytes_on_disk)            \"disk_size\",\n       col_count,\n       table_stats.total_rows,\n       formatReadableSize(data_uncompressed_bytes) as \"uncompressed_size\"\nFROM system.tables table\n         LEFT JOIN ( SELECT table,\n       database,\n       uniq(table, partition)        \"partitions\",\n       count()                    AS parts,\n       sum(bytes_on_disk)            \"bytes_on_disk\",\n       sum(data_uncompressed_bytes) as \"data_uncompressed_bytes\",\n       sum(rows)                  as total_rows,\n                            max(col_count) as col_count\nFROM system.parts as parts\n         LEFT JOIN (SELECT database, table, count() as col_count FROM system.columns GROUP BY table, database) as col_stats\n                   ON parts.table = col_stats.table AND col_stats.database = parts.database\nWHERE active\nGROUP BY table, database\n ) AS table_stats ON table.name = table_stats.table AND table.database = table_stats.database\nWHERE database IN (${database}) AND lower(name) != 'information_schema' AND table IN (${table})\nORDER BY bytes_on_disk DESC\nLIMIT 1000", "refId": "A"}], "title": "Table summary", "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {"col_count": 8, "database": 0, "disk_size": 6, "engine": 2, "name": 1, "partitions": 4, "parts": 5, "table_stats.total_rows": 3, "uncompressed_size": 7}, "renameByName": {"col_count": "Column count", "col_stats.col_count": "Column count", "database": "Database", "disk_size": "Size on disk", "engine": "Engine", "name": "Table", "partitions": "Partition count", "parts": "Part count", "table.database": "Database", "table_stats.total_rows": "Row Count", "tables": "Number of tables", "total_rows": "Total rows", "uncompressed_size": "Uncompressed size"}}}], "type": "table"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Database"}, "properties": [{"id": "custom.width", "value": 94}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Table"}, "properties": [{"id": "custom.width", "value": 116}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Partition id"}, "properties": [{"id": "custom.width", "value": 103}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Disk"}, "properties": [{"id": "custom.width", "value": 104}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Reason"}, "properties": [{"id": "custom.width", "value": 125}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Min block number"}, "properties": [{"id": "custom.width", "value": 141}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Max block number"}, "properties": [{"id": "custom.width", "value": 139}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Level"}, "properties": [{"id": "custom.width", "value": 89}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Name"}, "properties": [{"id": "custom.width", "value": 168}]}]}, "gridPos": {"h": 10, "w": 9, "x": 15, "y": 25}, "id": 28, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.0.1", "targets": [{"builderOptions": {"fields": [], "limit": 100, "mode": "list"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT database, table, partition_id, name, disk, level FROM system.detached_parts WHERE database IN (${database}) AND table IN (${table})", "refId": "A"}], "title": "Detached partitions", "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"database": "Database", "disk": "Disk", "level": "Level", "max_block_number": "Max block number", "min_block_number": "Min block number", "name": "Name", "partition_id": "Partition id", "reason": "Reason", "table": "Table"}}}], "type": "table"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "Rows in part", "axisPlacement": "auto", "axisWidth": 3, "barAlignment": 0, "drawStyle": "line", "fillOpacity": 10, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineStyle": {"fill": "solid"}, "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "always", "spanNulls": true, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": []}, "gridPos": {"h": 11, "w": 15, "x": 0, "y": 35}, "id": 18, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "right"}, "tooltip": {"mode": "multi", "sort": "none"}}, "targets": [{"builderOptions": {"fields": [], "limit": 100, "mode": "list"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT modification_time as timestamp, concatAssumeInjective(database, '.', table) as table, rows FROM system.parts parts WHERE parts.database IN ($database) AND parts.table IN (${table})  AND $__timeFilter(modification_time) ORDER BY modification_time ASC", "refId": "A"}], "title": "Parts over time with row count", "transformations": [{"id": "organize", "options": {"excludeByName": {"bytes_on_disk": true, "name": true, "rows": false}, "indexByName": {"rows": 2, "table": 1, "timestamp": 0}, "renameByName": {"rows": "rows in part"}}}, {"id": "prepareTimeSeries", "options": {"format": "many"}}], "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green"}]}}, "overrides": []}, "gridPos": {"h": 11, "w": 9, "x": 15, "y": 35}, "id": 16, "options": {"displayMode": "gradient", "minVizHeight": 10, "minVizWidth": 0, "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "showUnfilled": true}, "pluginVersion": "9.0.1", "targets": [{"builderOptions": {"fields": [], "limit": 100, "mode": "list"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT concatAssumeInjective(database, '.', table) as dbTable, count() \"partitions\", sum(part_count) \"parts\", max(part_count) \"max_parts_per_partition\"\nFROM ( SELECT database, table, count() \"part_count\"\n       FROM system.parts\n       WHERE database IN (${database}) AND active AND table IN (${table})\n       GROUP BY database, table, partition ) partitions\nGROUP BY database, table\nORDER BY max_parts_per_partition DESC\nLIMIT 10", "refId": "A"}], "title": "Max parts per partition", "transformations": [{"id": "organize", "options": {"excludeByName": {"partitions": true, "parts": true}, "indexByName": {}, "renameByName": {}}}], "type": "bargauge"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Active"}, "properties": [{"id": "custom.displayMode", "value": "color-background-solid"}, {"id": "mappings", "value": [{"options": {"false": {"color": "light-red", "index": 1}, "true": {"color": "light-green", "index": 0}}, "type": "value"}]}, {"id": "custom.width", "value": 77}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "level"}, "properties": [{"id": "custom.width", "value": 69}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "path"}, "properties": [{"id": "custom.width", "value": 286}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Database"}, "properties": [{"id": "custom.width", "value": 88}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Table"}, "properties": [{"id": "custom.width", "value": 111}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Partition Name"}, "properties": [{"id": "custom.width", "value": 226}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "disk_name"}, "properties": [{"id": "custom.width", "value": 109}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "marks"}, "properties": [{"id": "custom.width", "value": 65}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "rows"}, "properties": [{"id": "custom.width", "value": 87}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "bytes_on_disk"}, "properties": [{"id": "custom.width", "value": 112}]}]}, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 46}, "id": 20, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.0.1", "targets": [{"builderOptions": {"fields": [], "limit": 100, "mode": "list"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT database, table, partition_id, modification_time, name, part_type, active, level, disk_name, path, marks, rows, bytes_on_disk, refcount, min_block_number, max_block_number FROM system.parts WHERE database IN (${database}) AND table IN (${table})  AND modification_time > now() - INTERVAL 3 MINUTE ORDER BY modification_time DESC", "refId": "A"}], "title": "Recent part analysis", "transformations": [{"id": "organize", "options": {"excludeByName": {"is_frozen": true, "marks": false, "max_date": true, "min_date": true, "partition_id": true, "refcount": true}, "indexByName": {}, "renameByName": {"active": "Active", "database": "Database", "engine": "Engine", "name": "Partition Name", "part_type": "Partition Type", "partition_id": "Partition Id", "table": "Table"}}}, {"id": "convertFieldType", "options": {"conversions": [{"destinationType": "boolean", "targetField": "Active"}], "fields": {}}}], "type": "table"}], "schemaVersion": 36, "style": "dark", "tags": [], "templating": {"list": [{"current": {}, "hide": 0, "includeAll": false, "label": "ClickHouse instance", "multi": false, "name": "datasource", "options": [], "query": "grafana-clickhouse-datasource", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"current": {}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "definition": "SELECT name FROM system.databases;\n", "hide": 0, "includeAll": true, "label": "Database", "multi": true, "name": "database", "options": [], "query": "SELECT name FROM system.databases;\n", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "definition": "SELECT name FROM system.tables WHERE database IN (${database})", "hide": 0, "includeAll": true, "label": "Table", "multi": true, "name": "table", "options": [], "query": "SELECT name FROM system.tables WHERE database IN (${database})", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "ClickHouse - Data Analysis", "uid": "-B3tt7a7z", "version": 2, "weekStart": ""}