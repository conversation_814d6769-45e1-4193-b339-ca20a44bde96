{"__inputs": [{"name": "the_datasource", "label": "The Datasource", "description": "", "type": "datasource", "pluginId": "grafana-clickhouse-datasource", "pluginName": "ClickHouse"}], "__elements": {}, "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "11.2.0-pre"}, {"type": "datasource", "id": "grafana-clickhouse-datasource", "name": "ClickHouse", "version": "4.3.2"}, {"type": "panel", "id": "timeseries", "name": "Time series", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": {"type": "grafana", "uid": "-- <PERSON><PERSON> --"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "type": "dashboard"}]}, "description": "Similar to the monitoring dashboard that is built in to ClickHouse.", "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "links": [], "panels": [{"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 0}, "id": null, "panels": [], "title": "Overview", "type": "row"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}, "id": 1, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, avg(ProfileEvent_Query)\nFROM merge('system', '^metric_log')\nWHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s", "refId": "A"}], "title": "Queries/second", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}, "id": 2, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, avg(ProfileEvent_OSCPUVirtualTimeMicroseconds) / 1000000\nFROM merge('system', '^metric_log')\nWHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s", "refId": "A"}], "title": "CPU Usage (cores)", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, avg(CurrentMetric_Query)\nFROM merge('system', '^metric_log')\nWHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s", "refId": "A"}], "title": "Queries Running", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}, "id": 4, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, avg(CurrentMetric_Merge)\nFROM merge('system', '^metric_log')\nWHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s", "refId": "A"}], "title": "<PERSON><PERSON>", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}, "id": 5, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, avg(ProfileEvent_SelectedBytes)\nFROM merge('system', '^metric_log')\nWHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s", "refId": "A"}], "title": "Selected Bytes/second", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, avg(ProfileEvent_OSIOWaitMicroseconds) / 1000000\nFROM merge('system', '^metric_log')\nWHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s", "refId": "A"}], "title": "IO Wait", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 24}, "id": 7, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, avg(ProfileEvent_OSCPUWaitMicroseconds) / 1000000\nFROM merge('system', '^metric_log')\nWHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s", "refId": "A"}], "title": "CPU Wait", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 24}, "id": 8, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, avg(value)\nFROM merge('system', '^asynchronous_metric_log')\nWHERE $__dateFilter(event_date) AND $__timeFilter(event_time) AND metric = 'OSUserTimeNormalized'\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s", "refId": "A"}], "title": "OS CPU Usage (Userspace)", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 32}, "id": 9, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, avg(value)\nFROM merge('system', '^asynchronous_metric_log')\nWHERE $__dateFilter(event_date) AND $__timeFilter(event_time) AND metric = 'OSSystemTimeNormalized'\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s", "refId": "A"}], "title": "OS CPU Usage (Kernel)", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 32}, "id": 10, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, avg(ProfileEvent_OSReadBytes)\nFROM merge('system', '^metric_log')\nWHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s", "refId": "A"}], "title": "Read From Disk", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 40}, "id": 11, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, avg(ProfileEvent_OSReadChars)\nFROM merge('system', '^metric_log')\nWHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s", "refId": "A"}], "title": "Read From Filesystem", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 40}, "id": 12, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, avg(CurrentMetric_MemoryTracking)\nFROM merge('system', '^metric_log')\nWHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s", "refId": "A"}], "title": "Memory (tracked)", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 48}, "id": 13, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, avg(value)\nFROM merge('system', '^asynchronous_metric_log')\nWHERE $__dateFilter(event_date) AND $__timeFilter(event_time) AND metric = 'LoadAverage15'\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s", "refId": "A"}], "title": "Load Average (15 minutes)", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 48}, "id": 14, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, avg(ProfileEvent_SelectedRows)\nFROM merge('system', '^metric_log')\nWHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s", "refId": "A"}], "title": "Selected Rows/second", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 56}, "id": 15, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, avg(ProfileEvent_InsertedRows)\nFROM merge('system', '^metric_log')\nWHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s", "refId": "A"}], "title": "Inserted Rows/second", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 56}, "id": 16, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, avg(value)\nFROM merge('system', '^asynchronous_metric_log')\nWHERE $__dateFilter(event_date) AND $__timeFilter(event_time) AND metric = 'TotalPartsOfMergeTreeTables'\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s", "refId": "A"}], "title": "Total MergeTree Parts", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 64}, "id": 17, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, max(value)\nFROM merge('system', '^asynchronous_metric_log')\nWHERE $__dateFilter(event_date) AND $__timeFilter(event_time) AND metric = 'MaxPartCountForPartition'\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s", "refId": "A"}], "title": "Max Parts For Partition", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 64}, "id": 18, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t,\n    sum(CurrentMetric_TCPConnection) AS TCP_Connections,\n    sum(CurrentMetric_MySQLConnection) AS MySQL_Connections,\n    sum(CurrentMetric_HTTPConnection) AS HTTP_Connections\nFROM merge('system', '^metric_log')\nWHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s", "refId": "A"}], "title": "Concurrent network connections", "type": "timeseries"}, {"collapsed": false, "gridPos": {"h": 1, "w": 24, "x": 0, "y": 72}, "id": null, "panels": [], "title": "Cloud overview", "type": "row"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 72}, "id": 19, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT \n  toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t,\n  avg(metric)\nFROM (\n  SELECT event_time, sum(ProfileEvent_Query) AS metric \n  FROM clusterAllReplicas(default, merge('system', '^metric_log'))\n  WHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\n  GROUP BY event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "Queries/second", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 72}, "id": 20, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, avg(metric) / 1000000\nFROM (\n  SELECT event_time, sum(ProfileEvent_OSCPUVirtualTimeMicroseconds) AS metric \n  FROM clusterAllReplicas(default, merge('system', '^metric_log'))\n  WHERE $__dateFilter(event_date) AND $__timeFilter(event_time) GROUP BY event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "CPU Usage (cores)", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 80}, "id": 21, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT \n  toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t,\n  avg(metric)\nFROM (\n  SELECT event_time, sum(CurrentMetric_Query) AS metric \n  FROM clusterAllReplicas(default, merge('system', '^metric_log'))\n  WHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\n  GROUP BY event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "Queries Running", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 80}, "id": 22, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT \n  toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t,\n  avg(metric)\nFROM (\n  SELECT event_time, sum(CurrentMetric_Merge) AS metric \n  FROM clusterAllReplicas(default, merge('system', '^metric_log'))\n  WHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\n  GROUP BY event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "<PERSON><PERSON>", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 88}, "id": 23, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT \n  toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t,\n  avg(metric)\nFROM (\n  SELECT event_time, sum(ProfileEvent_SelectedBytes) AS metric \n  FROM clusterAllReplicas(default, merge('system', '^metric_log'))\n  WHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\n  GROUP BY event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "Selected Bytes/second", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 88}, "id": 24, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT \n  toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t,\n  avg(metric)\nFROM (\n  SELECT event_time, sum(ProfileEvent_OSIOWaitMicroseconds) / 1000000 AS metric \n  FROM clusterAllReplicas(default, merge('system', '^metric_log'))\n  WHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\n  GROUP BY event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "IO Wait (local fs)", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 96}, "id": 25, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT \n  toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t,\n  avg(metric)\nFROM (\n  SELECT event_time, sum(ProfileEvent_ReadBufferFromS3Microseconds) / 1000000 AS metric \n  FROM clusterAllReplicas(default, merge('system', '^metric_log'))\n  WHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\n  GROUP BY event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "S3 read wait", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 96}, "id": 26, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT \n  toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t,\n  avg(metric)\nFROM (\n  SELECT event_time, sum(ProfileEvent_ReadBufferFromS3RequestsErrors) AS metric \n  FROM clusterAllReplicas(default, merge('system', '^metric_log'))\n  WHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\n  GROUP BY event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "S3 read errors/sec", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 104}, "id": 27, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT \n  toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t,\n  avg(metric)\nFROM (\n  SELECT event_time, sum(ProfileEvent_OSCPUWaitMicroseconds) / 1000000 AS metric \n  FROM clusterAllReplicas(default, merge('system', '^metric_log'))\n  WHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\n  GROUP BY event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "CPU Wait", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 104}, "id": 28, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, avg(value)\nFROM clusterAllReplicas(default, merge('system', '^asynchronous_metric_log'))\nWHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\nAND metric = 'OSUserTimeNormalized'\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "OS CPU Usage (Userspace, normalized)", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 112}, "id": 29, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, avg(value)\nFROM clusterAllReplicas(default, merge('system', '^asynchronous_metric_log'))\nWHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\nAND metric = 'OSSystemTimeNormalized'\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "OS CPU Usage (Kernel, normalized)", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 112}, "id": 30, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT \n  toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t,\n  avg(metric)\nFROM (\n  SELECT event_time, sum(ProfileEvent_OSReadBytes) AS metric \n  FROM clusterAllReplicas(default, merge('system', '^metric_log'))\n  WHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\n  GROUP BY event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "Read From Disk (bytes/sec)", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 120}, "id": 31, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT \n  toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t,\n  avg(metric)\nFROM (\n  SELECT event_time, sum(ProfileEvent_OSReadChars) AS metric \n  FROM clusterAllReplicas(default, merge('system', '^metric_log'))\n  WHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\n  GROUP BY event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "Read From Filesystem (bytes/sec)", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 120}, "id": 32, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT \n  toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t,\n  avg(metric)\nFROM (\n  SELECT event_time, sum(CurrentMetric_MemoryTracking) AS metric \n  FROM clusterAllReplicas(default, merge('system', '^metric_log'))\n  WHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\n  GROUP BY event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "Memory (tracked, bytes)", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 128}, "id": 33, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, avg(value)\nFROM (\n  SELECT event_time, sum(value) AS value\n  FROM clusterAllReplicas(default, merge('system', '^asynchronous_metric_log'))\n  WHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\n    AND metric = 'LoadAverage15'\n  GROUP BY event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "Load Average (15 minutes)", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 128}, "id": 34, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT \n  toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t,\n  avg(metric)\nFROM (\n  SELECT event_time, sum(ProfileEvent_SelectedRows) AS metric \n  FROM clusterAllReplicas(default, merge('system', '^metric_log'))\n  WHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\n  GROUP BY event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "Selected Rows/sec", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 136}, "id": 35, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT \n  toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t,\n  avg(metric)\nFROM (\n  SELECT event_time, sum(ProfileEvent_InsertedRows) AS metric \n  FROM clusterAllReplicas(default, merge('system', '^metric_log'))\n  WHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\n  GROUP BY event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "Inserted Rows/sec", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 136}, "id": 36, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, max(value)\nFROM clusterAllReplicas(default, merge('system', '^asynchronous_metric_log'))\nWHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\nAND metric = 'TotalPartsOfMergeTreeTables'\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "Total MergeTree Parts", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 144}, "id": 37, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, max(value)\nFROM clusterAllReplicas(default, merge('system', '^asynchronous_metric_log'))\nWHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\nAND metric = 'MaxPartCountForPartition'\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "Max Parts For Partition", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 144}, "id": 38, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT \n  toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t,\n  avg(metric)\nFROM (\n  SELECT event_time, sum(ProfileEvent_ReadBufferFromS3Bytes) AS metric \n  FROM clusterAllReplicas(default, merge('system', '^metric_log'))\n  WHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\n  GROUP BY event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "Read From S3 (bytes/sec)", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 152}, "id": 39, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT \n  toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t,\n  avg(metric)\nFROM (\n  SELECT event_time, sum(CurrentMetric_FilesystemCacheSize) AS metric \n  FROM clusterAllReplicas(default, merge('system', '^metric_log'))\n  WHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\n  GROUP BY event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "Filesystem Cache Size", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 152}, "id": 40, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT \n  toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t,\n  avg(metric)\nFROM (\n  SELECT event_time, sum(ProfileEvent_DiskS3PutObject + ProfileEvent_DiskS3UploadPart + ProfileEvent_DiskS3CreateMultipartUpload + ProfileEvent_DiskS3CompleteMultipartUpload) AS metric \n  FROM clusterAllReplicas(default, merge('system', '^metric_log'))\n  WHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\n  GROUP BY event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "Disk S3 write req/sec", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 160}, "id": 41, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT \n  toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t,\n  avg(metric)\nFROM (\n  SELECT event_time, sum(ProfileEvent_DiskS3GetObject + ProfileEvent_DiskS3HeadObject + ProfileEvent_DiskS3ListObjects) AS metric \n  FROM clusterAllReplicas(default, merge('system', '^metric_log'))\n  WHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\n  GROUP BY event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "Disk S3 read req/sec", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 160}, "id": 42, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT \n  toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t,\n  avg(metric)\nFROM (\n  SELECT event_time, sum(ProfileEvent_CachedReadBufferReadFromCacheBytes) / (sum(ProfileEvent_CachedReadBufferReadFromCacheBytes) + sum(ProfileEvent_CachedReadBufferReadFromSourceBytes)) AS metric \n  FROM clusterAllReplicas(default, merge('system', '^metric_log'))\n  WHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\n  GROUP BY event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "FS cache hit rate", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 168}, "id": 43, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT \n  toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t,\n  avg(metric)\nFROM (\n  SELECT event_time, greatest(0, (sum(ProfileEvent_OSReadChars) - sum(ProfileEvent_OSReadBytes)) / (sum(ProfileEvent_OSReadChars) + sum(ProfileEvent_ReadBufferFromS3Bytes))) AS metric \n  FROM clusterAllReplicas(default, merge('system', '^metric_log'))\n  WHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\n  GROUP BY event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "Page cache hit rate", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 168}, "id": 44, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, avg(value)\nFROM (\n  SELECT event_time, sum(value) AS value\n  FROM clusterAllReplicas(default, merge('system', '^asynchronous_metric_log'))\n  WHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\n    AND metric LIKE 'NetworkReceiveBytes%'\n  GROUP BY event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "Network receive bytes/sec", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 176}, "id": 45, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, avg(value)\nFROM (\n  SELECT event_time, sum(value) AS value\n  FROM clusterAllReplicas(default, merge('system', '^asynchronous_metric_log'))\n  WHERE $__dateFilter(event_date) AND $__timeFilter(event_time)\n    AND metric LIKE 'NetworkSendBytes%'\n  GROUP BY event_time)\nGROUP BY t\nORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "Network send bytes/sec", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisBorderShow": false, "axisCenteredZero": false, "axisColorMode": "text", "axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "insertNulls": false, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 176}, "id": 46, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom", "showLegend": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${the_datasource}"}, "editorType": "sql", "format": 0, "meta": {"builderOptions": {"columns": [], "database": "", "limit": 1000, "mode": "list", "queryType": "table", "table": ""}}, "pluginVersion": "4.3.2", "queryType": "timeseries", "rawSql": "SELECT toStartOfInterval(event_time, INTERVAL $__interval_s SECOND) AS t, max(TCP_Connections), max(MySQL_Connections), max(HTTP_Connections) FROM (SELECT event_time, sum(CurrentMetric_TCPConnection) AS TCP_Connections, sum(CurrentMetric_MySQLConnection) AS MySQL_Connections, sum(CurrentMetric_HTTPConnection) AS HTTP_Connections FROM clusterAllReplicas(default, merge('system', '^metric_log')) WHERE $__dateFilter(event_date) AND $__timeFilter(event_time) GROUP BY event_time) GROUP BY t ORDER BY t WITH FILL STEP $__interval_s SETTINGS skip_unavailable_shards = 1", "refId": "A"}], "title": "Concurrent network connections", "type": "timeseries"}], "schemaVersion": 39, "tags": [], "templating": {"list": []}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "browser", "title": "Advanced ClickHouse Monitoring Dashboard", "uid": null, "version": 1, "weekStart": ""}