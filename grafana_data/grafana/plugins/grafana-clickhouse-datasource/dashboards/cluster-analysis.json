{"__inputs": [], "__elements": [], "__requires": [{"type": "panel", "id": "bargauge", "name": "Bar gauge", "version": ""}, {"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "9.0.1"}, {"type": "datasource", "id": "grafana-clickhouse-datasource", "name": "ClickHouse", "version": "2.0.0"}, {"type": "panel", "id": "stat", "name": "Stat", "version": ""}, {"type": "panel", "id": "table", "name": "Table", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "iteration": 1661857966580, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "semi-dark-blue", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Version"}, "properties": [{"id": "unit", "value": "string"}]}]}, "gridPos": {"h": 5, "w": 4, "x": 0, "y": 0}, "id": 2, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "/.*/", "values": false}, "textMode": "auto"}, "pluginVersion": "9.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT version()", "refId": "A"}], "transformations": [{"id": "convertFieldType", "options": {"conversions": [{"destinationType": "string", "targetField": "Version"}], "fields": {}}}, {"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"version()": "Version"}}}], "type": "stat"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "semi-dark-blue", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "uptime"}, "properties": [{"id": "unit", "value": "s"}]}]}, "gridPos": {"h": 5, "w": 4, "x": 4, "y": 0}, "id": 4, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT uptime() as uptime", "refId": "A"}], "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"uptime": "Server uptime"}}}], "type": "stat"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "semi-dark-blue", "value": null}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 8, "y": 0}, "id": 6, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT count() as \"Number of databases\" FROM system.databases WHERE name IN (${database:singlequote})", "refId": "A"}], "type": "stat"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "semi-dark-blue", "value": null}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 12, "y": 0}, "id": 7, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT count() as \"Number of tables\" FROM system.tables WHERE database IN (${database:singlequote})", "refId": "A"}], "type": "stat"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "semi-dark-blue", "value": null}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 16, "y": 0}, "id": 8, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT sum(total_rows) as \"Number of rows\" FROM system.tables WHERE database IN (${database:singlequote});", "refId": "A"}], "type": "stat"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "semi-dark-blue", "value": null}]}}, "overrides": []}, "gridPos": {"h": 5, "w": 4, "x": 20, "y": 0}, "id": 9, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "center", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT count() as \"Number of columns\" FROM system.columns WHERE database IN (${database:singlequote});", "refId": "A"}], "type": "stat"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Is local"}, "properties": [{"id": "mappings", "value": [{"options": {"0": {"color": "light-red", "index": 0, "text": "remote"}, "1": {"color": "light-green", "index": 1, "text": "local"}}, "type": "value"}]}, {"id": "custom.displayMode", "value": "color-background-solid"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Errors count"}, "properties": [{"id": "color", "value": {"mode": "continuous-GrYlRd"}}, {"id": "custom.displayMode", "value": "color-background"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Slowdowns count"}, "properties": [{"id": "color", "value": {"mode": "continuous-GrYlRd"}}, {"id": "custom.displayMode", "value": "color-background"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Cluster"}, "properties": [{"id": "custom.filterable", "value": true}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Host name"}, "properties": [{"id": "custom.filterable", "value": true}]}]}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 5}, "id": 20, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true}, "pluginVersion": "9.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT cluster, shard_num, replica_num, host_name, host_address, port, is_local, errors_count, slowdowns_count FROM system.clusters;\n", "refId": "A"}], "title": "Cluster Overview", "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"cluster": "Cluster", "errors_count": "Errors count", "host_address": "Host address", "host_name": "Host name", "is_local": "Is local", "port": "Port", "replica_num": "Replicated number", "shard_num": "Shard number", "slowdowns_count": "Slowdowns count"}}}], "type": "table"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "progress"}, "properties": [{"id": "unit", "value": "percent"}, {"id": "color", "value": {"mode": "continuous-RdYlGr"}}]}]}, "gridPos": {"h": 7, "w": 5, "x": 0, "y": 15}, "id": 13, "options": {"displayMode": "lcd", "minVizHeight": 10, "minVizWidth": 0, "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "showUnfilled": true}, "pluginVersion": "9.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT concatAssumeInjective(database, '.', table) as db_table, round(100 * progress, 1) \"progress\" FROM system.merges WHERE database IN (${database:singlequote}) ORDER BY progress DESC LIMIT 5;\n", "refId": "A"}], "title": "Merge progress per table", "type": "bargauge"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Database"}, "properties": [{"id": "custom.width", "value": 78}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Table"}, "properties": [{"id": "custom.width", "value": 200}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Elapsed"}, "properties": [{"id": "custom.width", "value": 75}, {"id": "unit", "value": "s"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Progress"}, "properties": [{"id": "custom.width", "value": 82}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Mutation"}, "properties": [{"id": "custom.width", "value": 100}, {"id": "unit", "value": "bool"}, {"id": "color", "value": {"mode": "thresholds"}}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 0}, {"color": "semi-dark-green", "value": 1}]}}, {"id": "custom.displayMode", "value": "color-background-solid"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Partition id"}, "properties": [{"id": "custom.width", "value": 92}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Target path"}, "properties": [{"id": "custom.width", "value": 396}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Num parts"}, "properties": [{"id": "custom.width", "value": 90}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "total_size_compressed"}, "properties": [{"id": "custom.width", "value": 185}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Columns written"}, "properties": [{"id": "custom.width", "value": 198}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Total compressed size"}, "properties": [{"id": "custom.width", "value": 166}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Compressed size"}, "properties": [{"id": "custom.width", "value": 138}]}]}, "gridPos": {"h": 7, "w": 19, "x": 5, "y": 15}, "id": 11, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT concatAssumeInjective(database, '.', table) as db_table, round(elapsed, 1) \"elapsed\", round(100 * progress, 1) \"progress\", is_mutation, partition_id, result_part_path, source_part_paths, num_parts, formatReadableSize(total_size_bytes_compressed) \"total_size_compressed\", formatReadableSize(bytes_read_uncompressed) \"read_uncompressed\", formatReadableSize(bytes_written_uncompressed) \"written_uncompressed\", columns_written, formatReadableSize(memory_usage) \"memory_usage\", thread_id FROM system.merges WHERE database IN (${database:singlequote});", "refId": "<PERSON><PERSON>"}], "title": "Current merges", "transformations": [{"id": "organize", "options": {"excludeByName": {"partition_id": true, "read_uncompressed": true, "source_part_paths": true, "thread_id": true, "written_uncompressed": true}, "indexByName": {}, "renameByName": {"columns_written": "Columns written", "database": "Database", "db_table": "Table", "elapsed": "Elapsed", "is_mutation": "Mutation", "memory_usage": "Memory usage", "num_parts": "Num parts", "partition_id": "Partition id", "progress": "Progress", "result_part_path": "Target path", "table": "Table", "total_size_compressed": "Compressed size"}}}], "type": "table"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "percentage", "steps": [{"color": "green", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "parts_remaining"}, "properties": [{"id": "min", "value": 0}]}]}, "gridPos": {"h": 7, "w": 5, "x": 0, "y": 22}, "id": 14, "options": {"displayMode": "lcd", "minVizHeight": 10, "minVizWidth": 0, "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "showUnfilled": true}, "pluginVersion": "9.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT concatAssumeInjective(database, '.', table, ' - ', mutation_id) as db_table, length(parts_to_do_names) as parts_remaining FROM system.mutations WHERE parts_remaining > 0 AND database IN (${database:singlequote}) ORDER BY parts_remaining DESC;", "refId": "A"}], "title": "Mutations parts remaining", "type": "bargauge"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "description": "", "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "center", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Is completed"}, "properties": [{"id": "custom.width", "value": 100}, {"id": "unit", "value": "bool"}, {"id": "color", "value": {"mode": "thresholds"}}, {"id": "thresholds", "value": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "semi-dark-orange", "value": 0}, {"color": "semi-dark-green", "value": 1}]}}, {"id": "custom.displayMode", "value": "color-background-solid"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Database"}, "properties": [{"id": "custom.width", "value": 86}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Table"}, "properties": [{"id": "custom.width", "value": 194}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Fail time"}, "properties": [{"id": "custom.width", "value": 201}, {"id": "custom.align", "value": "center"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Result"}, "properties": [{"id": "custom.width", "value": 133}, {"id": "mappings", "value": [{"options": {"failure": {"color": "light-red", "index": 1, "text": "failure"}, "success": {"color": "semi-dark-green", "index": 0, "text": "success"}}, "type": "value"}]}, {"id": "custom.displayMode", "value": "color-background"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Reason"}, "properties": [{"id": "custom.width", "value": 163}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Command"}, "properties": [{"id": "custom.width", "value": 300}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Mutation id"}, "properties": [{"id": "custom.width", "value": 157}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Create time"}, "properties": [{"id": "custom.width", "value": 194}]}]}, "gridPos": {"h": 7, "w": 19, "x": 5, "y": 22}, "id": 15, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT concatAssumeInjective(database, '.', table) as db_table, mutation_id, command, create_time, parts_to_do_names, is_done, latest_failed_part, if(latest_fail_time = '1970-01-01 00:00:00', 'success', 'failure') as success, if(latest_fail_time = '1970-01-01 00:00:00', '-', CAST(latest_fail_time, 'String')) as fail_time, latest_fail_reason FROM system.mutations WHERE database IN (${database:singlequote}) ORDER BY is_done ASC, create_time DESC LIMIT 10", "refId": "<PERSON><PERSON>"}], "title": "Current mutations", "transformations": [{"id": "organize", "options": {"excludeByName": {"latest_failed_part": true, "partition_id": true, "parts_to_do_names": true, "read_uncompressed": true, "source_part_paths": true, "thread_id": true, "written_uncompressed": true}, "indexByName": {"command": 5, "create_time": 2, "db_table": 0, "fail_time": 9, "is_done": 3, "latest_fail_reason": 6, "latest_failed_part": 8, "mutation_id": 1, "parts_to_do_names": 7, "success": 4}, "renameByName": {"columns_written": "Columns written", "command": "Command", "create_time": "Create time", "database": "Database", "db_table": "Table", "elapsed": "Elapsed", "fail_time": "Fail time", "is_done": "Is completed", "is_mutation": "Mutation", "latest_fail_reason": "Reason", "latest_fail_time": "Fail time", "memory_usage": "Memory usage", "mutation_id": "Mutation id", "num_parts": "Num parts", "partition_id": "Partition id", "parts_to_do_names": "Pending parts", "progress": "Progress", "result_part_path": "Target path", "success": "Result", "table": "Table", "total_size_compressed": "Compressed size"}}}, {"id": "convertFieldType", "options": {"conversions": [], "fields": {}}}], "type": "table"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 8, "w": 5, "x": 0, "y": 29}, "id": 18, "options": {"displayMode": "lcd", "minVizHeight": 10, "minVizWidth": 0, "orientation": "horizontal", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "showUnfilled": true}, "pluginVersion": "9.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT concatAssumeInjective(database, '.', table) as db_table, queue_size FROM system.replicas WHERE database IN (${database:singlequote}) ORDER BY absolute_delay DESC LIMIT 10", "refId": "A"}], "title": "Replicated tables by delay", "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"absolute_delay": "Delay", "db_table": "Table", "inserts_in_queue": "Inserts in queue", "is_leader": "Leader", "is_readonly": "<PERSON><PERSON><PERSON>", "merges_in_queue": "Merges in queue", "queue_size": "Queue size"}}}], "type": "bargauge"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto", "inspect": false}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Table"}, "properties": [{"id": "custom.width", "value": 200}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Leader"}, "properties": [{"id": "custom.width", "value": 122}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "<PERSON><PERSON><PERSON>"}, "properties": [{"id": "custom.width", "value": 138}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Delay"}, "properties": [{"id": "custom.width", "value": 108}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Queue size"}, "properties": [{"id": "custom.width", "value": 113}]}]}, "gridPos": {"h": 8, "w": 19, "x": 5, "y": 29}, "id": 17, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": []}, "pluginVersion": "9.0.1", "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT concatAssumeInjective(database, '.', table) as db_table, is_leader, is_readonly, absolute_delay, queue_size, inserts_in_queue, merges_in_queue FROM system.replicas WHERE database IN (${database:singlequote}) ORDER BY absolute_delay DESC LIMIT 10", "refId": "A"}], "title": "Replicated tables by delay", "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {}, "renameByName": {"absolute_delay": "Delay", "db_table": "Table", "inserts_in_queue": "Inserts in queue", "is_leader": "Leader", "is_readonly": "<PERSON><PERSON><PERSON>", "merges_in_queue": "Merges in queue", "queue_size": "Queue size"}}}], "type": "table"}], "schemaVersion": 36, "style": "dark", "tags": [], "templating": {"list": [{"current": {}, "hide": 0, "includeAll": false, "label": "ClickHouse instance", "multi": false, "name": "datasource", "options": [], "query": "grafana-clickhouse-datasource", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"current": {}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "definition": "SELECT name FROM system.databases;", "hide": 0, "includeAll": true, "label": "Database", "multi": false, "name": "database", "options": [], "query": "SELECT name FROM system.databases;", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "y-Ka8y37k"}, "filters": [], "hide": 0, "name": "filters", "skipUrlSync": false, "type": "adhoc"}]}, "time": {"from": "now-6h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "ClickHouse - Cluster Analysis", "uid": "_hAsuzBnz", "version": 6, "weekStart": ""}