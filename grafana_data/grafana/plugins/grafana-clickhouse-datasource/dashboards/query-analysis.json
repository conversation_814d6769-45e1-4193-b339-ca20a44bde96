{"__inputs": [], "__elements": [], "__requires": [{"type": "grafana", "id": "grafana", "name": "<PERSON><PERSON>", "version": "9.0.1"}, {"type": "datasource", "id": "grafana-clickhouse-datasource", "name": "ClickHouse", "version": "2.0.0"}, {"type": "panel", "id": "histogram", "name": "Histogram", "version": ""}, {"type": "panel", "id": "piechart", "name": "Pie chart", "version": ""}, {"type": "panel", "id": "stat", "name": "Stat", "version": ""}, {"type": "panel", "id": "table", "name": "Table", "version": ""}, {"type": "panel", "id": "timeseries", "name": "Time series", "version": ""}], "annotations": {"list": [{"builtIn": 1, "datasource": {"type": "datasource", "uid": "grafana"}, "enable": true, "hide": true, "iconColor": "rgba(0, 211, 255, 1)", "name": "Annotations & Alerts", "target": {"limit": 100, "matchAny": false, "tags": [], "type": "dashboard"}, "type": "dashboard"}]}, "editable": true, "fiscalYearStartMonth": 0, "graphTooltip": 0, "id": null, "iteration": 1661858001390, "links": [], "liveNow": false, "panels": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "semi-dark-blue", "value": null}]}}, "overrides": []}, "gridPos": {"h": 4, "w": 5, "x": 0, "y": 0}, "id": 10, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.0.1", "targets": [{"builderOptions": {"fields": [], "limit": 100, "mode": "list"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT count() as \"Total queries\" FROM system.query_log WHERE type in ($type) AND initial_user IN ($user) AND query_kind IN ($query_kind) AND $__timeFilter(event_time) ", "refId": "A"}], "type": "stat"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "semi-dark-blue", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Avg query memory"}, "properties": [{"id": "unit", "value": "decbytes"}]}]}, "gridPos": {"h": 4, "w": 5, "x": 5, "y": 0}, "id": 17, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": false}, "textMode": "auto"}, "pluginVersion": "9.0.1", "targets": [{"builderOptions": {"fields": [], "limit": 100, "mode": "list"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT avg(memory_usage) as \"Avg query memory\", $__timeInterval(query_start_time) as time FROM system.query_log WHERE type in ($type) AND initial_user IN ($user) AND query_kind IN ($query_kind) AND $__timeFilter(event_time) GROUP BY time ORDER BY time", "refId": "A"}], "type": "stat"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"fillOpacity": 80, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineWidth": 1}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "light-blue", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Query time"}, "properties": [{"id": "unit", "value": "ms"}]}]}, "gridPos": {"h": 8, "w": 8, "x": 10, "y": 0}, "id": 12, "options": {"bucketOffset": 0, "legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}}, "pluginVersion": "8.3.4", "targets": [{"builderOptions": {"fields": [], "limit": 100, "mode": "list"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT query_duration_ms as \"Query time\" FROM system.query_log WHERE type in ($type) AND initial_user IN ($user) AND query_kind IN ($query_kind) AND $__timeFilter(event_time) LIMIT 1000;", "refId": "A"}], "title": "Query time distribution", "type": "histogram"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "viz": false}}, "mappings": []}, "overrides": []}, "gridPos": {"h": 8, "w": 6, "x": 18, "y": 0}, "id": 8, "options": {"displayLabels": [], "legend": {"displayMode": "list", "placement": "bottom"}, "pieType": "pie", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "", "values": true}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"builderOptions": {"fields": [], "limit": 100, "mode": "list"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT initial_user, count() as c FROM system.query_log WHERE type in ($type) AND initial_user IN ($user) AND query_kind IN ($query_kind) GROUP BY initial_user LIMIT 100;\n", "refId": "A"}], "title": "Top users", "type": "piechart"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "semi-dark-blue", "value": null}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Avg query time"}, "properties": [{"id": "unit", "value": "dtdurationms"}]}]}, "gridPos": {"h": 4, "w": 10, "x": 0, "y": 4}, "id": 16, "options": {"colorMode": "background", "graphMode": "area", "justifyMode": "auto", "orientation": "auto", "reduceOptions": {"calcs": ["lastNotNull"], "fields": "/^Avg query time$/", "values": false}, "textMode": "auto"}, "pluginVersion": "9.0.1", "targets": [{"builderOptions": {"fields": [], "limit": 100, "mode": "list"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT avg(query_duration_ms) as \"Avg query time\", $__timeInterval(query_start_time) as time FROM system.query_log WHERE type in ($type) AND initial_user IN ($user) AND query_kind IN ($query_kind) AND $__timeFilter(event_time) GROUP BY time ORDER BY time", "refId": "A"}], "type": "stat"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 15, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 1, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 11, "w": 24, "x": 0, "y": 8}, "id": 3, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"builderOptions": {"fields": [], "limit": 100, "mode": "list"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 2, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT $__timeInterval(query_start_time) as time,\n       any(normalizeQuery(query)) AS normalized_query,\n       count() as c\nFROM system.query_log\nWHERE type != 'QueryStart'\n  AND $__timeFilter(event_time)\n  AND initial_user IN ($user)\n   AND query_kind IN ($query_kind)\n  AND normalized_query_hash IN (SELECT normalized_query_hash\n                                FROM system.query_log\n                                WHERE type in ($type) AND $__timeFilter(event_time) AND query_kind IN ($query_kind)\n                                GROUP BY normalized_query_hash\n                                ORDER BY count() DESC\n                                LIMIT 5)\nGROUP BY normalized_query_hash, time\nORDER BY time", "refId": "A"}], "title": "Top query types over time", "transformations": [{"id": "prepareTimeSeries", "options": {"format": "many"}}], "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 0, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 19}, "id": 6, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"builderOptions": {"fields": [], "limit": 100, "mode": "list"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 2, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT $__timeInterval(query_start_time) as time,\n       any(normalizeQuery(query)) AS normalized_query,\n       avg(query_duration_ms) as avg_query_duration\nFROM system.query_log\nWHERE type != 'QueryStart'\n  AND $__timeFilter(event_time)\n  AND type in ($type) AND initial_user IN ($user) AND query_kind IN ($query_kind)\n  AND normalized_query_hash IN (SELECT normalized_query_hash\n                                FROM system.query_log\n                                WHERE type in ($type) AND initial_user IN ($user) AND query_kind IN ($query_kind) AND $__timeFilter(event_time)\n                                GROUP BY normalized_query_hash\n                                ORDER BY avg(query_duration_ms) DESC\n                                LIMIT 10)\nGROUP BY normalized_query_hash, time\nORDER BY time", "refId": "A"}], "title": "Query performance by type over time", "transformations": [{"id": "prepareTimeSeries", "options": {"format": "many"}}], "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "bars", "fillOpacity": 0, "gradientMode": "opacity", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "linear", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "normal"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}, "overrides": []}, "gridPos": {"h": 10, "w": 24, "x": 0, "y": 29}, "id": 13, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single", "sort": "none"}}, "targets": [{"builderOptions": {"fields": [], "limit": 100, "mode": "list"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 2, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT $__timeInterval(query_start_time) as time, initial_user as user, count() as \"number of queries by\"\nFROM system.query_log\nWHERE query_kind IN ($query_kind) AND type IN ($type) AND $__timeFilter(event_time) AND initial_user IN (\n  SELECT initial_user\nFROM system.query_log\nWHERE query_kind IN ($query_kind) AND type IN ($type) AND $__timeFilter(event_time)\nGROUP BY initial_user\nORDER BY count() as c DESC\nLIMIT 10\n)\nGROUP BY initial_user, time\nORDER BY time;", "refId": "A"}], "title": "Query requests by user", "transformations": [{"id": "prepareTimeSeries", "options": {"format": "many"}}], "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "continuous-GrYlRd"}, "custom": {"axisLabel": "", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 20, "gradientMode": "scheme", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 3, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "Max Memory Usage"}, "properties": [{"id": "unit", "value": "decbytes"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 39}, "id": 15, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "pluginVersion": "8.3.4", "targets": [{"builderOptions": {"fields": [], "limit": 100, "mode": "list"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 2, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT $__timeInterval(query_start_time) as time, \n      max(memory_usage) as \"Max Memory Usage\"\nFROM system.query_log\nWHERE $__timeFilter(event_time)\nGROUP BY time\nORDER BY time DESC", "refId": "A"}], "title": "Memory usage over time", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"axisLabel": "Read rows", "axisPlacement": "auto", "barAlignment": 0, "drawStyle": "line", "fillOpacity": 26, "gradientMode": "none", "hideFrom": {"legend": false, "tooltip": false, "viz": false}, "lineInterpolation": "smooth", "lineWidth": 1, "pointSize": 5, "scaleDistribution": {"type": "linear"}, "showPoints": "auto", "spanNulls": false, "stacking": {"group": "A", "mode": "none"}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "read_rows"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-blue", "mode": "fixed"}}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "written_rows"}, "properties": [{"id": "color", "value": {"fixedColor": "dark-red", "mode": "fixed"}}, {"id": "custom.axisPlacement", "value": "right"}, {"id": "custom.axisLabel", "value": "Written rows"}]}]}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 39}, "id": 19, "options": {"legend": {"calcs": [], "displayMode": "list", "placement": "bottom"}, "tooltip": {"mode": "single"}}, "targets": [{"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT toStartOfInterval(toDateTime(event_time), INTERVAL 60 second),  sum(read_rows) read_rows, sum(written_rows) written_rows FROM system.query_log WHERE $__timeFilter(event_time) GROUP BY event_time ORDER BY event_time ASC", "refId": "A"}], "title": "Read vs Write Rows", "type": "timeseries"}, {"datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green"}, {"color": "red", "value": 80}]}}, "overrides": [{"matcher": {"id": "by<PERSON><PERSON>", "options": "written"}, "properties": [{"id": "custom.width", "value": 207}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "type"}, "properties": [{"id": "custom.width", "value": 116}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "memory usage"}, "properties": [{"id": "custom.width", "value": 119}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "result"}, "properties": [{"id": "custom.width", "value": 201}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "read"}, "properties": [{"id": "custom.width", "value": 123}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "query_kind"}, "properties": [{"id": "custom.width", "value": 90}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "query_duration_ms"}, "properties": [{"id": "custom.width", "value": 145}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "initial_user"}, "properties": [{"id": "custom.width", "value": 104}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "client_hostname"}, "properties": [{"id": "custom.width", "value": 246}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "databases"}, "properties": [{"id": "custom.width", "value": 114}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "user"}, "properties": [{"id": "custom.width", "value": 119}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "client"}, "properties": [{"id": "custom.width", "value": 270}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Client host"}, "properties": [{"id": "custom.width", "value": 110}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Query id"}, "properties": [{"id": "custom.width", "value": 104}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Start time"}, "properties": [{"id": "custom.width", "value": 162}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Duration"}, "properties": [{"id": "custom.width", "value": 92}, {"id": "unit", "value": "dtdurationms"}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Normalized query"}, "properties": [{"id": "custom.width", "value": 885}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Rows weitten"}, "properties": [{"id": "custom.width", "value": 150}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Rows read"}, "properties": [{"id": "custom.width", "value": 177}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Type"}, "properties": [{"id": "custom.width", "value": 84}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Result"}, "properties": [{"id": "custom.width", "value": 296}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "User"}, "properties": [{"id": "custom.width", "value": 84}]}, {"matcher": {"id": "by<PERSON><PERSON>", "options": "Rows written"}, "properties": [{"id": "custom.width", "value": 197}]}]}, "gridPos": {"h": 12, "w": 24, "x": 0, "y": 47}, "id": 4, "options": {"footer": {"fields": "", "reducer": ["sum"], "show": false}, "showHeader": true, "sortBy": [{"desc": false, "displayName": "Memory usage"}]}, "pluginVersion": "8.3.4", "targets": [{"builderOptions": {"fields": [], "limit": 100, "mode": "list"}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "format": 1, "meta": {"builderOptions": {"fields": [], "limit": 100, "mode": "list"}}, "queryType": "sql", "rawSql": "SELECT query_start_time, type, query_duration_ms, initial_user, substring(query_id,1, 8) as query_id, query_kind, normalizeQuery(query) AS normalized_query, concat( toString(read_rows), ' rows / ', formatReadableSize(read_bytes) ) AS read, concat( toString(written_rows), ' rows / ', formatReadableSize(written_bytes) ) AS written, concat( toString(result_rows), ' rows / ', formatReadableSize(result_bytes) ) AS result, formatReadableSize(memory_usage) AS \"memory usage\" FROM system.query_log WHERE type in ($type) AND initial_user IN ($user) AND query_kind IN ($query_kind) AND $__timeFilter(event_time) ORDER BY query_duration_ms DESC LIMIT  1000", "refId": "A"}], "title": "Query overview", "transformations": [{"id": "organize", "options": {"excludeByName": {}, "indexByName": {"initial_user": 5, "memory usage": 2, "normalized_query": 7, "query_duration_ms": 4, "query_id": 0, "query_kind": 6, "query_start_time": 3, "read": 8, "result": 10, "type": 1, "written": 9}, "renameByName": {"c": "", "client": "Client", "client_hostname": "Client host", "databases": "Databases", "exception": "Exception", "initial_user": "User", "memory usage": "Memory usage", "normalized_query": "Normalized query", "query_duration_ms": "Duration", "query_id": "Query id", "query_kind": "Type", "query_start_time": "Start time", "read": "Rows read", "result": "Result", "stack_trace": "Stack trace", "tables": "Tables", "type": "Status", "user": "User", "written": "Rows written"}}}], "type": "table"}], "refresh": false, "schemaVersion": 36, "style": "dark", "tags": [], "templating": {"list": [{"current": {}, "hide": 0, "includeAll": false, "label": "ClickHouse instance", "multi": false, "name": "datasource", "options": [], "query": "grafana-clickhouse-datasource", "queryValue": "", "refresh": 1, "regex": "", "skipUrlSync": false, "type": "datasource"}, {"current": {}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "definition": "SELECT DISTINCT(query_kind) as query_kind FROM system.query_log WHERE query_kind != ''", "description": "", "hide": 0, "includeAll": true, "label": "Query kind", "multi": true, "name": "query_kind", "options": [], "query": "SELECT DISTINCT(query_kind) as query_kind FROM system.query_log WHERE query_kind != ''", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "definition": "SELECT type FROM system.query_log GROUP BY type", "hide": 0, "includeAll": true, "label": "Query status", "multi": true, "name": "type", "options": [], "query": "SELECT type FROM system.query_log GROUP BY type", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}, {"current": {}, "datasource": {"type": "grafana-clickhouse-datasource", "uid": "${datasource}"}, "definition": "SELECT DISTINCT(initial_user) FROM system.query_log WHERE initial_user != '' LIMIT 100", "hide": 0, "includeAll": true, "label": "User", "multi": true, "name": "user", "options": [], "query": "SELECT DISTINCT(initial_user) FROM system.query_log WHERE initial_user != '' LIMIT 100", "refresh": 1, "regex": "", "skipUrlSync": false, "sort": 0, "type": "query"}]}, "time": {"from": "now-24h", "to": "now"}, "timepicker": {}, "timezone": "", "title": "ClickHouse - Query Analysis", "uid": "w5Q2Otank", "version": 1, "weekStart": ""}