{"$schema": "https://raw.githubusercontent.com/grafana/grafana/main/docs/sources/developers/plugins/plugin.schema.json", "type": "app", "name": "<PERSON><PERSON> Drilldown", "id": "grafana-lokiexplore-app", "autoEnabled": true, "info": {"keywords": ["app", "loki", "explore", "logs", "drilldown", "drill", "down", "drill-down"], "description": "Visualize log volumes to easily detect anomalies or significant changes over time, without needing to compose LogQL queries.", "author": {"name": "<PERSON><PERSON>"}, "logos": {"small": "img/logo.svg", "large": "img/logo.svg"}, "screenshots": [{"name": "patterns", "path": "img/patterns.png"}, {"name": "fields", "path": "img/fields.png"}, {"name": "table", "path": "img/table.png"}], "version": "1.0.26", "updated": "2025-08-25", "links": [{"name": "<PERSON><PERSON><PERSON>", "url": "https://github.com/grafana/explore-logs"}, {"name": "Report bug", "url": "https://github.com/grafana/explore-logs/issues/new"}]}, "includes": [{"type": "page", "name": "<PERSON><PERSON> Drilldown", "path": "/a/grafana-lokiexplore-app/explore", "action": "datasources:explore", "addToNav": true, "defaultNav": true}], "roles": [], "dependencies": {"grafanaDependency": ">=11.6.0", "plugins": [], "extensions": {"exposedComponents": ["grafana-adaptivelogs-app/temporary-exemptions/v1", "grafana-lokiexplore-app/embedded-logs-exploration/v1"]}}, "preload": true, "extensions": {"addedLinks": [{"targets": ["grafana/dashboard/panel/menu", "grafana/explore/toolbar/action", "grafana-metricsdrilldown-app/open-in-logs-drilldown/v1", "grafana-assistant-app/navigateToDrilldown/v1"], "title": "Open in Grafana Logs Drilldown", "description": "Open current query in the Grafana Logs Drilldown view"}], "exposedComponents": [{"id": "grafana-lokiexplore-app/open-in-explore-logs-button/v1", "title": "Open in Logs Drilldown button", "description": "A button that opens a logs view in the Logs Drilldown app."}, {"id": "grafana-lokiexplore-app/embedded-logs-exploration/v1", "title": "Embedded Logs Exploration", "description": "A component that renders a logs exploration view that can be embedded in other parts of Grafana."}], "extensionPoints": [{"id": "grafana-lokiexplore-app/investigation/v1"}]}, "buildMode": "production"}