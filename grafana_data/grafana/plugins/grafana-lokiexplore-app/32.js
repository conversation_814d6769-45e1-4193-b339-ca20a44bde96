"use strict";(self.webpackChunkgrafana_lokiexplore_app=self.webpackChunkgrafana_lokiexplore_app||[]).push([[32],{5218:(e,t,n)=>{n.d(t,{F:()=>o});var r=n(6089),a=n(2007),i=n(5959),l=n.n(i),s=n(1220);const o=e=>{const{isExcluded:t,isIncluded:n,onInclude:r,onExclude:i,onClear:o,titles:d,buttonFill:u}=e,p=(0,a.useStyles2)(c,n,t);return l().createElement("div",{className:p.container},l().createElement(a.But<PERSON>,{variant:n?"primary":"secondary",fill:u,size:"sm","aria-selected":n,className:p.includeButton,onClick:n?o:r,"data-testid":s.b.exploreServiceDetails.buttonFilterInclude,title:null==d?void 0:d.include},"Include"),l().createElement(a.<PERSON><PERSON>,{variant:t?"primary":"secondary",fill:u,size:"sm","aria-selected":t,className:p.excludeButton,onClick:t?o:i,title:null==d?void 0:d.exclude,"data-testid":s.b.exploreServiceDetails.buttonFilterExclude},"Exclude"))},c=(e,t,n)=>({container:(0,r.css)({display:"flex",justifyContent:"center"}),includeButton:(0,r.css)({borderRadius:0,borderRight:t?void 0:"none"}),excludeButton:(0,r.css)({borderRadius:`0 ${e.shape.radius.default} ${e.shape.radius.default} 0`,borderLeft:n?void 0:"none"})})},4482:(e,t,n)=>{n.d(t,{R:()=>c});var r=n(5959),a=n.n(r),i=n(6089),l=n(1575),s=n(2007);const o=e=>({graphicContainer:(0,i.css)({display:"flex",justifyContent:"center",margin:"0 auto"}),graphic:(0,i.css)({width:"200px",height:"120px",padding:e.spacing(1)}),text:(0,i.css)({display:"flex",justifyContent:"center",alignItems:"center"}),wrap:(0,i.css)({margin:"0 auto"})}),c=({children:e})=>{const t=(0,s.useStyles2)(o),n=(0,s.useTheme2)();return a().createElement("div",{className:t.wrap},a().createElement("div",{className:t.graphicContainer},a().createElement(l.A,{className:t.graphic,src:n.isDark?"/public/plugins/grafana-lokiexplore-app/img/grot_err.svg":"/public/plugins/grafana-lokiexplore-app/img/grot_err_light.svg"})),a().createElement("div",{className:t.text},a().createElement(s.Text,{textAlignment:"center",color:"primary",element:"span"},e||"An error occurred")))}},7841:(e,t,n)=>{n.d(t,{PR:()=>se,nB:()=>de,yQ:()=>le});var r=n(5959),a=n.n(r),i=n(7781),l=n(1119),s=n(3143),o=n(227),c=n(9507),d=n(2007),u=n(6089),p=n(1220);const g=({onRemove:e,pattern:t,size:n="lg"})=>{const i=(0,d.useStyles2)(h),[l,s]=(0,r.useState)(!1);return a().createElement("div",{className:i.pattern,onClick:()=>s(!l),onMouseLeave:()=>s(!1)},a().createElement(d.Tag,{title:t,key:t,name:l?t:v(t,n),className:i.tag}),a().createElement(d.Button,{"aria-label":"Remove pattern","data-testid":p.b.exploreServiceDetails.buttonRemovePattern,variant:"secondary",size:"sm",className:i.removeButton,onClick:e},a().createElement(d.Icon,{name:"times"})))},m={sm:50,lg:Math.round(window.innerWidth/8)};function v(e,t){const n=e.length;if(n<m[t])return e;const r=Math.round(.4*m[t]);return`${e.substring(0,r)} … ${e.substring(n-r)}`}const h=e=>({pattern:(0,u.css)({display:"flex",fontFamily:"monospace",gap:e.spacing(.25),cursor:"pointer",overflow:"hidden"}),tag:(0,u.css)({borderTopRightRadius:0,borderBottomRightRadius:0,backgroundColor:e.colors.secondary.main,border:`solid 1px ${e.colors.secondary.border}`,color:e.colors.secondary.text,boxSizing:"border-box",padding:e.spacing(.25,.75),overflow:"hidden",textOverflow:"ellipsis"}),removeButton:(0,u.css)({paddingLeft:2.5,paddingRight:2.5})});var f=n(2718);const b=({patterns:e,onRemove:t})=>{const n=(0,d.useStyles2)(y);if(!e||0===e.length)return null;const r=e.filter((e=>"include"===e.type)),i=e.filter((e=>"include"!==e.type)),l=n=>{t(e.filter((e=>e!==n))),(0,f.EE)(f.NO.service_details,f.ir.service_details.pattern_removed,{includePatternsLength:r.length-("include"===(null==n?void 0:n.type)?1:0),excludePatternsLength:i.length-("include"!==(null==n?void 0:n.type)?1:0),type:n.type})};return a().createElement("div",null,r.length>0&&a().createElement("div",{className:n.patternsContainer},a().createElement(d.Text,{variant:"bodySmall",weight:"bold","data-testid":p.b.patterns.buttonIncludedPattern},"Included pattern",e.length>1?"s":""),a().createElement("div",{className:n.patterns},r.map((e=>a().createElement(g,{key:e.pattern,pattern:e.pattern,size:"lg",onRemove:()=>l(e)}))))),i.length>0&&a().createElement("div",{className:n.patternsContainer},a().createElement(d.Text,{variant:"bodySmall",weight:"bold","data-testid":p.b.patterns.buttonExcludedPattern},"Excluded pattern",i.length>1?"s":"",":"),a().createElement("div",{className:n.patterns},i.map((e=>a().createElement(g,{key:e.pattern,pattern:e.pattern,size:i.length>1?"sm":"lg",onRemove:()=>l(e)}))))))};function y(e){return{patternsContainer:(0,u.css)({paddingBottom:e.spacing(1),overflow:"hidden"}),patterns:(0,u.css)({display:"flex",gap:e.spacing(1),alignItems:"center",flexWrap:"wrap"})}}const S=()=>{const e=(0,d.useStyles2)(w);return a().createElement("div",{className:e.wrapper},a().createElement("a",{href:"https://forms.gle/1sYWCTPvD72T1dPH9",className:e.feedback,title:"Share your thoughts about Logs in Grafana.",target:"_blank",rel:"noreferrer noopener"},a().createElement(d.Icon,{name:"comment-alt-message"})," Give feedback"))},w=e=>({wrapper:(0,u.css)({display:"flex",marginLeft:"auto",gap:e.spacing(1),position:"relative",top:e.spacing(-1)}),feedback:(0,u.css)({alignSelf:"center",color:e.colors.text.secondary,fontSize:e.typography.bodySmall.fontSize,"&:hover":{color:e.colors.text.link}})});function O(e){const t=(0,d.useStyles2)(E);return a().createElement(a().Fragment,null,a().createElement(d.Alert,{className:t.alert,severity:"info",title:"Welcome to Explore Logs!",onRemove:e.onRemove},a().createElement("div",null,"Check out our"," ",a().createElement("a",{className:"external-link",target:"_blank",href:"https://grafana.com/docs/grafana-cloud/visualizations/simplified-exploration/logs/",rel:"noreferrer"},"Get started doc"),", or see"," ",a().createElement("a",{className:"external-link",target:"_blank",href:"https://github.com/grafana/explore-logs/releases",rel:"noreferrer"},"recent changes"),".",a().createElement("br",null),"Help us shape the future of the app."," ",a().createElement("a",{className:"external-link",target:"_blank",href:"https://forms.gle/1sYWCTPvD72T1dPH9",rel:"noreferrer"},"Send us feedback")," ","or engage with us on"," ",a().createElement("a",{className:"external-link",target:"_blank",href:"https://github.com/grafana/explore-logs/?tab=readme-ov-file#explore-logs",rel:"noreferrer"},"GitHub"),".")))}function E(e){return{alert:(0,u.css)({flex:"none"})}}var x,C,P,j=n(8831);class F extends l.Bs{}function k(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}P=function({model:e}){var t,n;const r=l.jh.getVariables(e).useState();let i=r.variables;return(null===(t=e.state.include)||void 0===t?void 0:t.length)&&(i=r.variables.filter((t=>{var n,r;return null===(n=e.state.include)||void 0===n?void 0:n.includes(null!==(r=t.state.name)&&void 0!==r?r:"")}))),(null===(n=e.state.exclude)||void 0===n?void 0:n.length)&&(i=r.variables.filter((t=>{var n,r;return!(null===(n=e.state.exclude)||void 0===n?void 0:n.includes(null!==(r=t.state.name)&&void 0!==r?r:""))}))),a().createElement(a().Fragment,null,i.map((t=>a().createElement(l.Lp,{key:t.state.key,variable:t,layout:e.state.layout}))))},(C="Component")in(x=F)?Object.defineProperty(x,C,{value:P,enumerable:!0,configurable:!0,writable:!0}):x[C]=P;const T=`${j.s_}.interceptBannerStorageKey`;class _ extends l.Bs{dismiss(){this.setState({interceptDismissed:!0}),localStorage.setItem(T,"true")}constructor(e){var t,n;super((t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){k(e,t,n[t])}))}return e}({},e),n=null!=(n={interceptDismissed:!!localStorage.getItem(T)})?n:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})),t))}}function L(e){return{firstRowWrapper:(0,u.css)({"& > div > div":{gap:"16px",label:"first-row-wrapper",[e.breakpoints.down("lg")]:{flexDirection:"column"},"& > div:first-child":{flex:"1 0 auto",display:"inline-block"}}}),bodyContainer:(0,u.css)({flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column"}),container:(0,u.css)({flexGrow:1,display:"flex",gap:e.spacing(1),minHeight:"100%",flexDirection:"column",padding:e.spacing(2),maxWidth:"100vw"}),body:(0,u.css)({flexGrow:1,display:"flex",flexDirection:"column",gap:e.spacing(1)}),controlsFirstRowContainer:(0,u.css)({display:"flex",gap:e.spacing(2),justifyContent:"space-between",alignItems:"flex-start",marginBottom:e.spacing(2)}),controlsSecondRowContainer:(0,u.css)({display:"flex",gap:e.spacing(2),justifyContent:"space-between",alignItems:"flex-start"}),controlsContainer:(0,u.css)({label:"controlsContainer"}),filters:(0,u.css)({label:"filters"}),filtersWrap:(0,u.css)({label:"filtersWrap",display:"flex",gap:e.spacing(2),width:"calc(100% - 450)",flexWrap:"wrap",alignItems:"flex-end",'& + div[data-testid="data-testid Dashboard template variables submenu Label Filters"]:empty':{visibility:"hidden"},"& > div &:first-child":{"& > div":{"& > div":{flexWrap:"wrap","& > div":{maxWidth:"380px","& > div":{flex:"1 0 auto","&:nth-child(3)":{flex:"0 1 auto"}}}}}},'[data-testid="AdHocFilter-service_name"]':{'div[class*="input-wrapper"]:first-child':{display:"none"},'div[class*="input-wrapper"]:nth-child(2)':{marginLeft:0}},'div >[title="Add filter"]':{border:0,display:"none",width:0,padding:0,margin:0}}),controlsWrapper:(0,u.css)({label:"controlsWrapper",display:"flex",flexDirection:"column",marginTop:e.spacing(.375)}),controls:(0,u.css)({display:"flex",gap:e.spacing(1)}),feedback:(0,u.css)({textAlign:"end"}),rotateIcon:(0,u.css)({svg:{transform:"rotate(180deg)"}})}}k(_,"Component",(({model:e})=>{if(!e.parent)return null;const{controls:t,contentScene:n,patterns:r}=e.parent.useState(),{interceptDismissed:i}=e.useState();if(!n)return null;const s=(0,d.useStyles2)(L);return a().createElement("div",{className:s.bodyContainer},a().createElement("div",{className:s.container},!i&&a().createElement(O,{onRemove:()=>{e.dismiss()}}),t&&a().createElement("div",{className:s.controlsContainer},a().createElement("div",{className:s.controlsFirstRowContainer},a().createElement("div",{className:s.filtersWrap},a().createElement("div",{className:(0,u.cx)(s.filters,s.firstRowWrapper)},t.map((e=>e instanceof l.G1?a().createElement(e.Component,{key:e.state.key,model:e}):null)))),a().createElement("div",{className:s.controlsWrapper},a().createElement(S,null),a().createElement("div",{className:s.controls},t.map((e=>e instanceof F||e instanceof l.G1?null:a().createElement(e.Component,{key:e.state.key,model:e})))))),a().createElement("div",{className:s.controlsSecondRowContainer},a().createElement("div",{className:s.filtersWrap},a().createElement("div",{className:s.filters},t.map((e=>e instanceof F?a().createElement(e.Component,{key:e.state.key,model:e}):null)))))),a().createElement(b,{patterns:r,onRemove:t=>{var n;return null===(n=e.parent)||void 0===n?void 0:n.setState({patterns:t})}}),a().createElement("div",{className:s.body},n&&a().createElement(n.Component,{model:n}))))}));var D=n(892),N=n(6939),$=n(8531),I=n(7918),A=n(5435),B=n(5431),V=n(4750),M=n(4002),R=n(4106),W=n(1255),z=n(9829),H=n(2871),G=n(3241),q=n(4793);function Q(e,t,n,r,a,i,l){try{var s=e[i](l),o=s.value}catch(e){return void n(e)}s.done?t(o):Promise.resolve(o).then(r,a)}function U(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function l(e){Q(i,r,a,l,s,"next",e)}function s(e){Q(i,r,a,l,s,"throw",e)}l(void 0)}))}}const J=(Y=U((function*(e,t,n,r,a){const i=yield(0,$.getDataSourceSrv)().get((0,z.U4)(n));if(!(i instanceof $.DataSourceWithBackend))throw H.v.error(new Error("getTagValuesProvider: Invalid datasource!")),new Error("Invalid datasource!");const l=i.languageProvider;let o=[];if(l&&l.fetchDetectedLabelValues){const n={expr:t,limit:1e3,timeRange:r,throwError:!0},i={showErrorAlert:!1};try{let t=yield l.fetchDetectedLabelValues(e.key,n,i);if(t&&(0,G.isArray)(t)){if(a===s.mB){const n=(0,V.bu)(e,a);return{replace:!0,values:t.map((e=>({text:e,value:JSON.stringify({value:e,parser:n.parser})})))}}o=t.map((e=>({text:e})))}else o=[]}catch(e){H.v.error(e),H.v.warn("getDetectedFieldValuesTagValuesProvider: loki missing detected_field/.../values endpoint. Upgrade to Loki 3.3.0 or higher."),o=[]}}else H.v.warn("getDetectedFieldValuesTagValuesProvider: fetchDetectedLabelValues is not defined in Loki datasource. Upgrade to Grafana 11.4 or higher."),o=[];return{replace:!0,values:o}})),function(e,t,n,r,a){return Y.apply(this,arguments)});var Y;function K(e,t){return X.apply(this,arguments)}function X(){return(X=U((function*(e,t){const n=yield(0,$.getDataSourceSrv)().get((0,z.U4)(e));if(!(n instanceof $.DataSourceWithBackend))throw H.v.error(new Error("getTagValuesProvider: Invalid datasource!")),new Error("Invalid datasource!");const r=n;if(r&&r.getTagValues){const n=(0,I.W3)(e).filter((e=>!("="===t.operator&&e.key===t.key))),i={key:t.key,filters:n};let l=yield r.getTagValues(i);if((0,G.isArray)(l)){var a;l=l.filter((n=>!e.state.filters.filter((e=>e.key===t.key)).some((e=>e.operator===q.w.Equal&&e.value===n.text))));const n=(0,o.eT)(null===(a=(0,V.S9)(e).getValue())||void 0===a?void 0:a.toString(),t.key),r=new Set(n);n.length&&l.sort(((e,t)=>(r.has(t.text)?1:-1)-(r.has(e.text)?1:-1)))}return{replace:!0,values:l}}return H.v.error(new Error("getTagValuesProvider: missing or invalid datasource!")),{replace:!0,values:[]}}))).apply(this,arguments)}var Z=n(7097),ee=n(6001);function te(e,t,n,r,a,i,l){try{var s=e[i](l),o=s.value}catch(e){return void n(e)}s.done?t(o):Promise.resolve(o).then(r,a)}function ne(e){return re.apply(this,arguments)}function re(){var e;return e=function*(e){const t=yield(0,$.getDataSourceSrv)().get((0,z.U4)(e));if(!(t instanceof $.DataSourceWithBackend))throw H.v.error(new Error("getTagKeysProvider: Invalid datasource!")),new Error("Invalid datasource!");const n=t;if(n&&n.getTagKeys){const t={filters:(0,I.W3)(e)},r=yield n.getTagKeys(t),a=(Array.isArray(r)?r:[]).filter((e=>!ee.rm.includes(e.text)));return{replace:!0,values:a}}return H.v.error(new Error("getTagKeysProvider: missing or invalid datasource!")),{replace:!0,values:[]}},re=function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function l(e){te(i,r,a,l,s,"next",e)}function s(e){te(i,r,a,l,s,"throw",e)}l(void 0)}))},re.apply(this,arguments)}var ae=n(6059);function ie(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const le="showLogsButtonScene";class se extends l.Bs{onActivate(){const e={};var t,n;this.setVariableTagValuesProviders(),l.jh.findByKeyAndType(this,le,ae.H).setState({hidden:!1}),this.state.contentScene||(e.contentScene=(n=null===(t=this.state.routeMatch)||void 0===t?void 0:t.params.breakdownLabel,(0,D.FT)()===D.G3.explore?new N.yj({}):new c.Mn({drillDownLabel:n}))),this.setTagProviders(),this.setVariableOperators(),this.setState(e),this.updatePatterns(this.state,(0,V.Ku)(this)),this.resetVariablesIfNotInUrl((0,V.ir)(this),(0,V.n5)(s.mB)),this.resetVariablesIfNotInUrl((0,V.iw)(this),(0,V.n5)(s._Y)),this._subs.add(this.subscribeToState((e=>{this.updatePatterns(e,(0,V.Ku)(this))})));const r=l.jh.getTimeRange(this);this._subs.add(r.subscribeToState(this.limitMaxInterval(r)))}setVariableOperators(){const e=(0,V.ir)(this);e._getOperators=function(){return e.state.filters.some((e=>de.includes(e.operator)))?[...oe,...ue]:oe}}setTagProviders(){const e=(0,V.cR)(this);e._getOperators=function(){const t=e.state._wip;return t&&e.state.filters.some((e=>e.key===t.key&&e.operator===q.w.Equal))?ce:oe},e.setState({getTagKeysProvider:ne,getTagValuesProvider:K})}limitMaxInterval(e){return(t,n)=>{const{jsonData:r}=W.plugin.meta;if(null==r?void 0:r.interval)try{var a;const s=i.rangeUtil.intervalToSeconds(null!==(a=null==r?void 0:r.interval)&&void 0!==a?a:"");if(!s)return;const o=t.value.to.diff(t.value.from,"seconds");if(o>s){if(o<=n.value.to.diff(n.value.from,"seconds"))e.setState({value:n.value,from:n.from,to:n.to});else{const t=new l.JZ(R.sp);e.setState({value:t.state.value,from:t.state.from,to:t.state.to})}(0,$.getAppEvents)().publish({type:i.AppEvents.alertWarning.name,payload:["Time range interval exceeds maximum interval configured by the administrator."]}),(0,f.EE)("all","interval_too_long",{attempted_duration_seconds:o,configured_max_interval:s})}}catch(e){console.error(e)}}}setVariableTagValuesProviders(){const e=(0,V.ir)(this),t=(0,V.iw)(this),n=(0,V.oY)(this);e.setState({getTagValuesProvider:this.getFieldsTagValuesProvider(s.mB)}),t.setState({getTagValuesProvider:this.getFieldsTagValuesProvider(s._Y)}),n.setState({getTagValuesProvider:this.getFieldsTagValuesProvider(s._P)})}getFieldsTagValuesProvider(e){return(t,n)=>{const r=t.state.filters.filter((e=>e.key!==n.key)).map((t=>{const n=e===s.mB?(0,V.bu)(t,e):{value:t.value};return`${t.key}${t.operator}\`${(0,Z.ZD)(n.value)}\``})),a=r.length?"| "+r.join(" |"):"",i=this.getFieldsTagValuesExpression(e).replace(s.Gd,a),o=l.jh.interpolate(this,i);return J(n,o,this,l.jh.getTimeRange(this).state.value,e)}}getFieldsTagValuesExpression(e){switch(e){case s.mB:return s.Do;case s._P:return s.mz;case s._Y:return s.Sy;default:const t=new Error(`Unknown variable type: ${e}`);throw H.v.error(t,{variableType:e}),t}}resetVariablesIfNotInUrl(e,t){const n=$.locationService.getLocation();null===new URLSearchParams(n.search).get(t)&&e.setState({filters:[]})}updatePatterns(e,t){var n;const r=(0,I.M3)(null!==(n=e.patterns)&&void 0!==n?n:[]);t.changeValueTo(r)}getUrlState(){return{patterns:JSON.stringify(this.state.patterns)}}updateFromUrl(e){const t={};e.patterns&&"string"==typeof e.patterns&&(t.patterns=JSON.parse(e.patterns)),this.setState(t)}constructor(e){var t;const{variablesScene:n,unsub:r}=function(e,t){const n=new l.H9({name:s.MB,datasource:s.eL,layout:"combobox",label:"Labels",filters:null!=t?t:[],expressionBuilder:I.VW,hide:A.zL.dontHide,key:"adhoc_service_filter"});n._getOperators=function(){return oe};const r=new l.H9({name:s.mB,label:"Fields",applyMode:"manual",layout:"vertical",getTagKeysProvider:()=>Promise.resolve({replace:!0,values:[]}),getTagValuesProvider:()=>Promise.resolve({replace:!0,values:[]}),expressionBuilder:I.ZX,hide:A.zL.hideLabel});r._getOperators=()=>oe;const a=new l.H9({name:s._P,label:"Metadata",applyMode:"manual",layout:"vertical",getTagKeysProvider:()=>Promise.resolve({replace:!0,values:[]}),getTagValuesProvider:()=>Promise.resolve({replace:!0,values:[]}),expressionBuilder:I.E3,hide:A.zL.hideLabel});a._getOperators=()=>oe;const i=new l.H9({name:s._Y,label:"Filters",applyMode:"manual",layout:"vertical",getTagKeysProvider:()=>Promise.resolve({replace:!0,values:[]}),getTagValuesProvider:()=>Promise.resolve({replace:!0,values:[]}),expressionBuilder:I.E3,hide:A.zL.hideLabel});i._getOperators=()=>oe;const c=new l.mI({name:s.EY,label:"Data source",value:e,pluginId:"loki"}),d=c.subscribeToState((e=>{const t=`${e.value}`;e.value&&(0,o.ke)(t)}));return{variablesScene:new l.Pj({variables:[c,n,r,i,a,new l.yP({name:s.uw,value:"",hide:A.zL.hideVariable}),new l.yP({name:s.WM,value:"",hide:A.zL.hideVariable}),new B.m({name:s.QE,value:s.YN,skipUrlSync:!0,hide:A.zL.hideVariable,options:[{value:s.YN,label:s.YN}]})]}),unsub:d}}(null!==(t=(0,o.QB)())&&void 0!==t?t:"grafanacloud-logs",e.initialFilters),a=[new l.G1({direction:"row",children:[new l.vA({body:new F({layout:"vertical",include:[s.MB,s.EY]})}),new ae.H({key:le,disabled:!0})]}),new F({layout:"vertical",exclude:[s.MB,s.EY]}),new l.KE({}),new l.WM({})];var i,c,d,u,p;"explore"===(0,D.FT)()&&$.config.featureToggles.exploreLogsAggregatedMetrics&&a.push(new M.s({isOpen:!1})),super((u=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){ie(e,t,n[t])}))}return e}({$timeRange:null!==(i=e.$timeRange)&&void 0!==i?i:new l.JZ({}),$variables:null!==(c=e.$variables)&&void 0!==c?c:n,controls:null!==(d=e.controls)&&void 0!==d?d:a,patterns:[]},e),p=null!=(p={body:new _({})})?p:{},Object.getOwnPropertyDescriptors?Object.defineProperties(u,Object.getOwnPropertyDescriptors(p)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(p)).forEach((function(e){Object.defineProperty(u,e,Object.getOwnPropertyDescriptor(p,e))})),u)),ie(this,"_urlSync",new l.So(this,{keys:["patterns"]})),this._subs.add(r),this.addActivationHandler(this.onActivate.bind(this))}}ie(se,"Component",(({model:e})=>{const{body:t}=e.useState();return t?a().createElement(t.Component,{model:t}):a().createElement(d.LoadingPlaceholder,{text:"Loading..."})}));const oe=[q.w.Equal,q.w.NotEqual].map((e=>({label:e,value:e}))),ce=[q.w.Equal].map((e=>({label:e,value:e}))),de=[q.w.gt,q.w.gte,q.w.lt,q.w.lte],ue=de.map((e=>({label:e,value:e})))},6059:(e,t,n)=>{n.d(t,{H:()=>g});var r=n(1119),a=n(2007),i=n(5959),l=n.n(i),s=n(6089),o=n(8835),c=n(4750),d=n(4793),u=n(1220);function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class g extends r.Bs{onActivate(){const e=(0,c.cR)(this),t=e.state.filters.some((e=>e.operator===d.w.Equal));this.setState({disabled:!t}),e.subscribeToState((e=>{const t=e.filters.some((e=>e.operator===d.w.Equal));this.setState({disabled:!t})}))}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){p(e,t,n[t])}))}return e}({},e)),p(this,"onClick",(()=>{const e=(0,c.cR)(this).state.filters.find((e=>e.operator===d.w.Equal));e&&(0,o.jY)(e.key,e.value)})),this.addActivationHandler(this.onActivate.bind(this))}}function m(e){return{button:(0,s.css)({[e.breakpoints.down("lg")]:{alignSelf:"flex-end"},[e.breakpoints.down("md")]:{marginTop:e.spacing(1),alignSelf:"flex-start"},alignSelf:"flex-start",marginTop:"22px"})}}p(g,"Component",(({model:e})=>{const{disabled:t,hidden:n}=e.useState(),r=(0,a.useStyles2)(m);return!0===n?null:l().createElement(a.Button,{"data-testid":u.b.index.header.showLogsButton,disabled:t,fill:"outline",className:r.button,onClick:e.onClick},"Show logs")}))},4002:(e,t,n)=>{n.d(t,{s:()=>v});var r=n(1119),a=n(2007),i=n(5959),l=n.n(i),s=n(6089),o=n(8531),c=n(2718),d=n(6939),u=n(2533),p=n(1220);function g(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const m=`${u.id}.serviceSelection.aggregatedMetrics`;class v extends r.Bs{constructor(e){const t=localStorage.getItem(m),n=o.config.featureToggles.exploreLogsAggregatedMetrics&&"false"!==t;var r;super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){g(e,t,n[t])}))}return e}({isOpen:!1,options:{aggregatedMetrics:{active:null!=n&&n,userOverride:null!==(r="true"===t)&&void 0!==r&&r,disabled:!1}}},e)),g(this,"toggleAggregatedMetricsOverride",(()=>{const e=!this.state.options.aggregatedMetrics.active;(0,c.EE)(c.NO.service_selection,c.ir.service_selection.aggregated_metrics_toggled,{enabled:e}),localStorage.setItem(m,e.toString()),this.setState({options:{aggregatedMetrics:{active:e,disabled:this.state.options.aggregatedMetrics.disabled,userOverride:e}}})})),g(this,"onToggleOpen",(e=>{this.setState({isOpen:e})}))}}function h(e){return{popover:(0,s.css)({display:"flex",padding:e.spacing(2),flexDirection:"column",background:e.colors.background.primary,boxShadow:e.shadows.z3,borderRadius:e.shape.radius.default,border:`1px solid ${e.colors.border.weak}`,zIndex:1,marginRight:e.spacing(2)}),heading:(0,s.css)({fontWeight:e.typography.fontWeightMedium,paddingBottom:e.spacing(2)}),options:(0,s.css)({display:"grid",gridTemplateColumns:"1fr 50px",rowGap:e.spacing(1),columnGap:e.spacing(2),alignItems:"center"})}}g(v,"Component",(({model:e})=>{const{isOpen:t,options:n}=e.useState(),r=(0,a.useStyles2)(h);return n.aggregatedMetrics?l().createElement(a.Dropdown,{overlay:()=>l().createElement("div",{className:r.popover,onClick:e=>e.stopPropagation()},l().createElement("div",{className:r.heading},"Query options"),l().createElement("div",{className:r.options},l().createElement("div",{title:"Aggregated metrics will return service queries results much more quickly, but with lower resolution"},"Aggregated metrics"),l().createElement("span",{title:n.aggregatedMetrics.disabled?`Aggregated metrics can only be enabled for queries starting after ${d.XU.toLocaleString()}`:""},l().createElement(a.Switch,{label:"Toggle aggregated metrics","data-testid":p.b.index.aggregatedMetricsToggle,value:n.aggregatedMetrics.active,disabled:n.aggregatedMetrics.disabled,onChange:e.toggleAggregatedMetricsOverride})))),placement:"bottom",onVisibleChange:e.onToggleOpen},l().createElement(a.ToolbarButton,{icon:"cog",variant:"canvas",isOpen:t,"data-testid":p.b.index.aggregatedMetricsMenu})):l().createElement(l().Fragment,null)}))},4106:(e,t,n)=>{n.d(t,{Oo:()=>m,c:()=>g,sp:()=>u});var r=n(1119),a=n(892),i=n(7781),l=n(7841),s=n(8835),o=n(2871),c=n(8315),d=n(8831);const u={from:"now-15m",to:"now"};function p(e){return new r.P1({body:new l.PR({$timeRange:new r.JZ(u),routeMatch:e})})}function g(){return new r.jD({title:"Logs",url:(0,d._F)(a.G3.explore),layout:i.PageLayoutType.Custom,preserveUrlKeys:a.Zt,routePath:(0,d._F)(a.G3.explore),getScene:e=>p(e),drilldowns:[{routePath:a.HU.logs,getPage:(e,t)=>v(e,t,a.G3.logs),defaultRoute:!0},{routePath:a.HU.labels,getPage:(e,t)=>v(e,t,a.G3.labels)},{routePath:a.HU.patterns,getPage:(e,t)=>v(e,t,a.G3.patterns)},{routePath:a.HU.fields,getPage:(e,t)=>v(e,t,a.G3.fields)},{routePath:a.KL.label,getPage:(e,t)=>h(e,t,a._J.label)},{routePath:a.KL.field,getPage:(e,t)=>h(e,t,a._J.field)},{routePath:"*",getPage:()=>m()}]})}function m(){return new r.jD({title:"",url:d.Gy,getScene:()=>new r.P1({body:new r.G1({direction:"column",children:[]})}),hideFromBreadcrumbs:!0,routePath:"*",$behaviors:[()=>{(0,s.Ns)()}]})}function v(e,t,n){const{labelName:l,labelValue:s}=(0,a.XJ)(e);return new r.jD({title:(0,c.Zr)(n),layout:i.PageLayoutType.Custom,url:a.bw[n](s,l),preserveUrlKeys:a.tm,getParentPage:()=>t,getScene:e=>p(e)})}function h(e,t,n){const{labelName:l,labelValue:s,breakdownLabel:d}=(0,a.XJ)(e);if(!d){const e=new Error("Breakdown value missing!");throw o.v.error(e,{labelName:l,labelValue:s,breakdownLabel:null!=d?d:""}),e}return new r.jD({title:(0,c.Zr)(d),layout:i.PageLayoutType.Custom,url:a.mC[n](s,l,d),preserveUrlKeys:a.tm,getParentPage:()=>t,getScene:e=>p(e)})}},1194:(e,t,n)=>{n.d(t,{g:()=>g});var r=n(8531),a=n(1119),i=n(2007),l=n(5959),s=n.n(l),o=n(7608),c=n(9829);function d(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){d(e,t,n[t])}))}return e}function p(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class g extends a.Bs{constructor(e){super(p(u({},e),{disabledLinks:[],queries:[]})),d(this,"onActivate",(()=>{(0,c.hJ)(this).then((e=>{this.setState({ds:e})})),this._subs.add(this.subscribeToState((()=>{this.getQueries(),this.getContext()})))})),d(this,"getQueries",(()=>{const e=a.jh.getData(this),t=a.jh.findObject(e,(e=>e instanceof a.dt));if(t){const e=this.state.frame?m(this.state.frame):null,n=t.state.queries.map((n=>p(u({},n),{expr:a.jh.interpolate(t,n.expr),legendFormat:(null==e?void 0:e.name)?`{{ ${e.name} }}`:a.jh.interpolate(t,n.legendFormat)})));JSON.stringify(n)!==JSON.stringify(this.state.queries)&&this.setState({queries:n})}})),d(this,"getContext",(()=>{const{queries:e,ds:t,labelName:n,fieldName:r}=this.state,i=a.jh.getTimeRange(this);if(!i||!e||!(null==t?void 0:t.uid))return;const l={origin:"Explore Logs",type:"timeseries",queries:e,timeRange:u({},i.state.value),datasource:{uid:t.uid},url:window.location.href,id:`${JSON.stringify(e)}${n}${r}`,title:`${n}${r?` > ${r}`:""}`,logoPath:"public/plugins/grafana-lokiexplore-app/img/3d96a93cfcb32df74eef.svg",drillDownLabel:r};JSON.stringify(l)!==JSON.stringify(this.state.context)&&this.setState({context:l})})),this.addActivationHandler(this.onActivate)}}d(g,"Component",(({model:e})=>{const{context:t,disabledLinks:n}=e.useState(),{links:a}=(0,r.usePluginLinks)({extensionPointId:o.R6.MetricExploration,context:t});return s().createElement(s().Fragment,null,a.filter((e=>"grafana-explorations-app"===e.pluginId&&e.onClick)).map((t=>{var r;return s().createElement(i.IconButton,{tooltip:t.description,disabled:"disabled"===t.category||n.includes(t.id),"aria-label":"extension-link-to-open-exploration",key:t.id,name:null!==(r=t.icon)&&void 0!==r?r:"panel-add",onClick:r=>{t.onClick&&t.onClick(r),e.setState({disabledLinks:[...n,t.id]})}})})))}));const m=e=>{var t,n;const r=null!==(n=null===(t=e.fields[1])||void 0===t?void 0:t.labels)&&void 0!==n?n:{};if(1!==Object.keys(r).length)return;const a=Object.keys(r)[0];return{name:a,value:r[a]}}},558:(e,t,n)=>{n.d(t,{Of:()=>f,PT:()=>C,Qt:()=>x,VT:()=>O,XI:()=>y,hi:()=>S,oR:()=>F,ts:()=>P,vn:()=>E});var r=n(5959),a=n.n(r),i=n(7781),l=n(1119),s=n(5435),o=n(2718),c=n(3143),d=n(5218),u=n(9507),p=n(7097),g=n(4750),m=n(4793),v=n(9055);function h(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class f extends i.BusEventBase{constructor(e,t,n){super(),h(this,"operator",void 0),h(this,"key",void 0),h(this,"value",void 0),this.operator=e,this.key=t,this.value=n}}h(f,"type","add-filter");class b extends i.BusEventBase{constructor(e,t,n){super(),h(this,"key",void 0),h(this,"value",void 0),h(this,"operator",void 0),this.key=e,this.value=t,this.operator=n}}function y(e,t,n){const r="="===e.operator?"include":"exclude";x(e.key,e.value,r,t,n)}function S(e,t,n,r,a){n||(n=j(e,t));const i=(0,g.bY)(P(e,n),t);let l=i.state.filters.filter((t=>{const n=(0,g.z2)(i,t);return r&&a?!(t.key===e&&n.value===r&&t.operator===a):r?!(t.key===e&&n.value===r):a?!(t.key===e&&t.operator===a):!(t.key===e)}));t.publishEvent(new b(e,r,a),!0),i.setState({filters:l})}h(b,"type","add-filter");const w=e=>e===m.w.gt||e===m.w.gte?"greater":e===m.w.lt||e===m.w.lte?"lesser":void 0;function O(e,t,n,r){r||(r=j(e,t));const a=(0,g.bY)(P(e,r),t),i=n?w(n):void 0;let l=a.state.filters.filter((t=>!(t.key===e&&(w(t.operator)===i||t.operator===m.w.NotEqual))));a.setState({filters:l})}function E(e,t,n,r,a){const i=w(n);a||(a=j(e,r));const l=(0,g.bY)(P(e,a),r);let s;a===c.mB&&(s=JSON.stringify({value:t,parser:(0,p.Ri)(e,r)}));let o=l.state.filters.filter((t=>!(t.key===e&&(w(t.operator)===i||t.operator===m.w.NotEqual))));o=[...o,{key:e,operator:n,value:s||t,valueLabels:[t]}],r.publishEvent(new f(n,e,t),!0),l.setState({filters:o})}function x(e,t,n,r,a){a||(a=j(e,r)),a===c.MB&&(0,v._J)(e,t,r);const i=(0,g.bY)(P(e,a),r);let l;a===c.mB&&(l=JSON.stringify({value:t,parser:(0,p.Ri)(e,r)}));let s=i.state.filters.filter((r=>{const a=(0,g.z2)(i,r);return"include"===n?!(r.key===e&&r.operator!==m.w.Equal):!(r.key===e&&a.value===t)}));const o=s.length!==i.state.filters.length;("include"===n||"exclude"===n||!o&&"toggle"===n)&&(s=[...s,{key:e,operator:"exclude"===n?m.w.NotEqual:m.w.Equal,value:l||t,valueLabels:[t]}]),r.publishEvent(new f(n,e,t),!0),i.setState({filters:s})}function C(e,t,n,r){(0,g.bY)(P(e,j(e,r)),r).setState({filters:[{key:e,operator:"exclude"===n?m.w.NotEqual:m.w.Equal,value:t}],hide:s.zL.hideLabel})}function P(e,t){return e===c.e4?c._Y:t}function j(e,t){var n,r;return(null===(r=(0,u.TG)(t))||void 0===r||null===(n=r.fields)||void 0===n?void 0:n.find((t=>t.name===e)))?c.MB:c.mB}class F extends l.Bs{constructor(...e){super(...e),h(this,"onClick",(e=>{const t=k(this.state.frame);if(!t)return;x(t.name,t.value,e,this,this.state.variableName);const n=(0,g.bY)(P(t.name,this.state.variableName),this);(0,o.EE)(o.NO.service_details,o.ir.service_details.add_to_filters_in_breakdown_clicked,{filterType:this.state.variableName,key:t.name,action:e,filtersLength:(null==n?void 0:n.state.filters.length)||0})})),h(this,"isSelected",(()=>{const e=k(this.state.frame);if(!e)return{isIncluded:!1,isExcluded:!1};const t=(0,g.bY)(P(e.name,this.state.variableName),this),n=t.state.filters.find((n=>{const r=(0,g.z2)(t,n);return n.key===e.name&&r.value===e.value}));return n?{isIncluded:n.operator===m.w.Equal,isExcluded:n.operator===m.w.NotEqual}:{isIncluded:!1,isExcluded:!1}}))}}h(F,"Component",(({model:e})=>{const{isIncluded:t,isExcluded:n}=e.isSelected();return a().createElement(d.F,{buttonFill:"outline",isIncluded:t,isExcluded:n,onInclude:()=>e.onClick("include"),onClear:()=>e.onClick("clear"),onExclude:()=>e.onClick("exclude")})}));const k=e=>{var t,n;const r=null!==(n=null===(t=e.fields[1])||void 0===t?void 0:t.labels)&&void 0!==n?n:{};if(1!==Object.keys(r).length)return;const a=Object.keys(r)[0];return{name:a,value:r[a]}}},4462:(e,t,n)=>{n.d(t,{f:()=>u,u:()=>d});var r=n(6089),a=n(5959),i=n.n(a),l=n(2007),s=n(1220),o=n(7918);function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function d({options:e,value:t,onChange:n,label:r}){const s=(0,l.useStyles2)(p),[o,c]=(0,a.useState)(!1),d=e.map((e=>({label:e.label,value:e.value})));return i().createElement(l.InlineField,{label:r},i().createElement(l.Select,{options:d,value:t,onOpenMenu:()=>c(!0),onCloseMenu:()=>c(!1),onChange:e=>n(e.value),className:s.select,prefix:o?void 0:i().createElement(l.Icon,{name:"search"})}))}function u({options:e,value:t,onChange:n,label:r,selectOption:d,isLoading:u,initialFilter:g}){var m;const v=(0,l.useStyles2)(p),[h,f]=(0,a.useState)(!1),[b,y]=(0,a.useState)(g),S=e.map((e=>({label:e.label,value:e.value}))),w=b&&t&&(null===(m=b.value)||void 0===m?void 0:m.includes(t))?[b,...S]:S,O=null==w?void 0:w.find((e=>e.value===t));return i().createElement(l.InlineField,{className:v.selectWrapper,grow:!0,label:r},i().createElement(l.Select,{isLoading:u,"data-testid":s.b.exploreServiceSearch.search,placeholder:"Search values",options:w,isClearable:!0,value:t,onOpenMenu:()=>f(!0),onCloseMenu:()=>f(!1),allowCustomValue:!0,prefix:h||(null==O?void 0:O.__isNew__)?void 0:i().createElement(l.Icon,{name:"search"}),onChange:(e,t)=>{return(null==e?void 0:e.__isNew__)||(null==e?void 0:e.icon)?(y((r=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){c(e,t,n[t])}))}return e}({},e),a=null!=(a={icon:"filter"})?a:{},Object.getOwnPropertyDescriptors?Object.defineProperties(r,Object.getOwnPropertyDescriptors(a)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(a)).forEach((function(e){Object.defineProperty(r,e,Object.getOwnPropertyDescriptor(a,e))})),r)),n(e.value)):"clear"===t.action?n(""):void("select-option"===t.action&&e.value&&!e.__isNew__&&d(e.value));var r,a},onInputChange:(e,t)=>{const r=t;return"input-change"===r.action?n(e):"menu-close"===r.action&&r.prevInputValue?(y({value:(0,o.vC)(r.prevInputValue),label:r.prevInputValue,icon:"filter",__isNew__:!0}),n(r.prevInputValue)):void 0}}))}function p(e){return{input:(0,r.css)({marginBottom:0}),select:(0,r.css)({maxWidth:e.spacing(64),minWidth:e.spacing(20)}),selectWrapper:(0,r.css)({maxWidth:e.spacing(62.5),minWidth:e.spacing(20),marginRight:e.spacing.x1,marginBottom:0})}}},9507:(e,t,n)=>{n.d(t,{Mn:()=>Kr,rD:()=>Qr,UO:()=>Ur,nU:()=>Jr,dB:()=>Yr,TG:()=>qr,tn:()=>Gr});var r=n(5959),a=n.n(r),i=n(7781),l=n(1119),s=n(2007),o=n(5183),c=n(7918),d=n(3143),u=n(6949),p=n(8835),g=n(833),m=n(9829),v=n(892),h=n(8531),f=n(1220),b=n(2718),y=n(227);const S=({exploration:e})=>a().createElement(s.ToolbarButton,{"data-testid":f.b.exploreServiceDetails.openExplore,variant:"canvas",icon:"compass",onClick:()=>{(0,b.EE)(b.NO.service_details,b.ir.service_details.open_in_explore_clicked);const t=(0,m.U4)(e),n=(0,m.u9)(e).replace(/\s+/g," ").trimEnd(),r=l.jh.getTimeRange(e).state.value,a=(0,y.N$)(e),s=(0,y.k5)(),o=function(){const e=new URLSearchParams(window.location.search).get("urlColumns");if(e)try{const t=JSON.parse(e);let n={};for(const e in t)n[e]=t[e];return n}catch(e){console.error(e)}}(),c=JSON.stringify({"loki-explore":{range:(0,i.toURLRange)(r.raw),queries:[{refId:"logs",expr:n,datasource:t}],panelsState:{logs:{displayedFields:a,visualisationType:s,columns:o}},datasource:t}});var d;const u=null!==(d=h.config.appSubUrl)&&void 0!==d?d:"",p=i.urlUtil.renderUrl(`${u}/explore`,{panes:c,schemaVersion:1});window.open(p,"_blank")}},"Open in Explore");var w=n(6089),O=n(3241);function E(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const x=e=>{var{value:t,onChange:n,placeholder:r,onClear:i,suffix:l}=e,o=function(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}(e,["value","onChange","placeholder","onClear","suffix"]);return a().createElement(s.Input,function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){E(e,t,n[t])}))}return e}({value:t,onChange:n,suffix:a().createElement(a().Fragment,null,t?a().createElement(s.Icon,{onClick:i,title:"Clear search",name:"times",className:C.clearIcon}):void 0,l&&l),prefix:a().createElement(s.Icon,{name:"search"}),placeholder:r},o))},C={clearIcon:(0,w.css)({cursor:"pointer"})},P=e=>{const t=(0,s.useTheme2)(),n=e.caseSensitive?t.colors.text.maxContrast:t.colors.text.disabled,r=j(t,n);return a().createElement("span",{className:r.container,title:`Case ${e.caseSensitive?"insensitive":"sensitive"} search`},a().createElement("svg",{onClick:()=>e.onCaseSensitiveToggle(e.caseSensitive?"insensitive":"sensitive"),fill:n,width:"16",height:"16",viewBox:"0 0 16 16",xmlns:"http://www.w3.org/2000/svg"},a().createElement("text",{fontSize:"13",width:"16",height:"16",x:"50%",y:"50%",dominantBaseline:"central",textAnchor:"middle"},"Aa")))},j=(e,t)=>({container:(0,w.css)({display:"flex",justifyContent:"center",marginLeft:e.spacing.x0_5,cursor:"pointer"})});var F=n(4750);function k(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class T extends l.Bs{updateFilter(e,t=!0){this.setState({lineFilter:e}),t?this.updateVariableDebounced(e):this.updateVariable(e)}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){k(e,t,n[t])}))}return e}({lineFilter:(null==e?void 0:e.lineFilter)||"",caseSensitive:!1},e)),k(this,"onActivate",(()=>{const e=(0,F.Rr)(this).getValue(),t=e.toString();if(!e)return;const n=t.includes("|="),r=n?t.match(/\|=.`(.+?)`/):t.match(/`\(\?i\)(.+)`/);r&&2===r.length&&this.setState({lineFilter:r[1].replace(/\\(.)/g,"$1"),caseSensitive:n})})),k(this,"handleChange",(e=>{this.updateFilter(e.target.value)})),k(this,"handleEnter",(e=>{"Enter"===e.key&&this.updateVariable(this.state.lineFilter)})),k(this,"onCaseSensitiveToggle",(e=>{this.setState({caseSensitive:"sensitive"===e}),this.updateFilter(this.state.lineFilter)})),k(this,"updateVariableDebounced",(0,O.debounce)((e=>{this.updateVariable(e)}),1e3)),k(this,"updateVariable",(e=>{const t=(0,F.Rr)(this);""===e?t.changeValueTo(""):this.state.caseSensitive?t.changeValueTo(`|= \`${(0,O.escapeRegExp)(e)}\``):t.changeValueTo(`|~ \`(?i)${(0,O.escapeRegExp)(e)}\``),(0,b.EE)(b.NO.service_details,b.ir.service_details.search_string_in_logs_changed,{searchQueryLength:e.length,containsLevel:e.toLowerCase().includes("level")})})),this.addActivationHandler(this.onActivate)}}k(T,"Component",(function({model:e}){const{lineFilter:t,caseSensitive:n}=e.useState();return a().createElement(s.Field,{className:_.field},a().createElement(x,{"data-testid":f.b.exploreServiceDetails.searchLogs,value:t,className:_.input,onChange:e.handleChange,suffix:a().createElement(P,{caseSensitive:n,onCaseSensitiveToggle:e.onCaseSensitiveToggle}),placeholder:"Search in log lines",onClear:()=>{e.updateFilter("",!1)},onKeyUp:e.handleEnter}))}));const _={input:(0,w.css)({width:"100%"}),field:(0,w.css)({label:"field",width:"100%",marginBottom:0})};function L(e,t,n){const r=e.getFieldByName(t);if(void 0!==r)return r.type===n?r:void 0}const D="timestamp",N="body";function $(e){const t={};return Object.entries(e).forEach((([e,n])=>{t[e]="string"==typeof n?n:JSON.stringify(n)})),t}function I(e){var t;return null!==(t=null==e?void 0:e.timeField.name)&&void 0!==t?t:D}function A(e){var t;return null!==(t=null==e?void 0:e.bodyField.name)&&void 0!==t?t:N}var B=n(2871);function V(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function M(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){V(e,t,n[t])}))}return e}const R=`${n(8831).s_}.tableColumnWidths`;var W;!function(e){e.text="text",e.labels="labels",e.auto="auto"}(W||(W={}));const z=(0,r.createContext)({columnWidthMap:{},setColumnWidthMap:()=>{},columns:{},filteredColumns:{},setColumns:()=>{},setFilteredColumns:()=>{},setVisible:()=>!1,visible:!1,bodyState:"auto",setBodyState:()=>{},clearSelectedLine:()=>{}}),H=({children:e,initialColumns:t,logsFrame:n,setUrlColumns:i,clearSelectedLine:l})=>{const[s,o]=(0,r.useState)(G(t)),[c,d]=(0,r.useState)("auto"),[u,p]=(0,r.useState)(void 0),[g,m]=(0,r.useState)(!1),v=function(){let e={};const t=localStorage.getItem(R);if(t)try{e=JSON.parse(t)}catch(e){B.v.error(e,{msg:"error parsing table column widths from local storage"})}return e}(),[h,f]=(0,r.useState)(v),b=(0,r.useCallback)((e=>{if(e){const t=G(e);o(t),i((e=>{let t=[];return Object.keys(e).forEach((n=>{e[n].active&&void 0!==e[n].index&&t.push(n)})),t.sort(((t,n)=>{const r=e[t],a=e[n];return r.index-a.index})),t})(t))}}),[i]),y=(0,r.useCallback)((e=>{m(e)}),[]);return(0,r.useEffect)((()=>{t&&b(t)}),[t,b]),(0,r.useEffect)((()=>{const e=function(e,t){if(!t)return void B.v.warn("missing dataframe, cannot set url state");const n=Object.keys(e).filter((t=>{var n;return null===(n=e[t])||void 0===n?void 0:n.active})).sort(((t,n)=>{const r=e[t],a=e[n];return void 0!==r.index&&void 0!==a.index?r.index-a.index:0})),r=t.timeField,a=t.bodyField;if(r&&a||n.length){const e=[];return(null==r?void 0:r.name)&&e.push(r.name),(null==a?void 0:a.name)&&e.push(a.name),n.length?n:e}return[]}(s,n);(null==e?void 0:e.length)&&(0===Object.keys(s).filter((e=>s[e].active)).length&&function(e,t,n){const r=M({},e);r[I(n)]={index:0,active:!0,type:"TIME_FIELD",percentOfLinesWithLabel:100,cardinality:1/0},r[A(n)]={index:1,active:!0,type:"BODY_FIELD",percentOfLinesWithLabel:100,cardinality:1/0},t(r)}(s,b,n),p(void 0))}),[s,n,p,b]),a().createElement(z.Provider,{value:{setColumnWidthMap:e=>{localStorage.setItem(R,JSON.stringify(e)),f(e)},columnWidthMap:h,bodyState:c,setBodyState:d,setFilteredColumns:p,filteredColumns:u,columns:s,setColumns:b,visible:g,setVisible:y,clearSelectedLine:()=>{l()}}},e)},G=e=>{if("labelTypes"in e){const t=M({},e),{labelTypes:n}=t;return function(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}(t,["labelTypes"])}return e},q=()=>(0,r.useContext)(z);var Q=n(3367),U=n(1269),J=n(3321);const Y=(0,r.createContext)({cellIndex:{index:null,numberOfMenuItems:3},setActiveCellIndex:e=>!1}),K=({children:e})=>{const[t,n]=(0,r.useState)({index:null}),i=(0,r.useCallback)((e=>{n(e)}),[]);return a().createElement(Y.Provider,{value:{cellIndex:t,setActiveCellIndex:i}},e)},X=()=>(0,r.useContext)(Y),Z=(0,r.createContext)({isHeaderMenuActive:!1,setHeaderMenuActive:e=>!1}),ee=({children:e})=>{const[t,n]=(0,r.useState)(!1),i=(0,r.useCallback)((e=>{n(e)}),[]);return a().createElement(Z.Provider,{value:{isHeaderMenuActive:t,setHeaderMenuActive:i}},e)},te=()=>(0,r.useContext)(Z);var ne=n(7928);const re=new ne.A({intraMode:1,intraIns:1,intraSub:1,intraTrn:1,intraDel:1});function ae(e,t,n){const[r,a,i]=re.search(e,t,0,1e5);let l=[],s=new Set;if(r&&i){const t=(e,t)=>{t&&s.add(e)};for(let n=0;n<i.length;n++){let r=i[n];ne.A.highlight(e[a.idx[r]],a.ranges[r],t),l.push(e[a.idx[r]])}n([l,[...s]])}else t||n([])}const ie=(0,O.debounce)(ae,300);function le({searchValue:e,setSearchValue:t}){const{columns:n,setFilteredColumns:r}=q(),i=e=>{const t=e[0];let a={},i=0;var l;t.forEach((e=>{e in n&&(a[e]=n[e],i++)})),r(a),l=i,(0,h.reportInteraction)("grafana_logs_app_table_text_search_result_count",{resultCount:l})},l=function(e){return{searchWrap:(0,w.css)({padding:`${e.spacing(.4)} 0 ${e.spacing(.4)} ${e.spacing(.4)}`})}}((0,s.useTheme2)());return a().createElement(s.Field,{className:l.searchWrap},a().createElement(s.Input,{value:e,type:"text",placeholder:"Search fields by name",onChange:e=>{var a;const l=null===(a=e.currentTarget)||void 0===a?void 0:a.value;var s;t(l),l?(s=l,ie(Object.keys(n),s,i)):r(void 0)}}))}var se=n(5206),oe=n(5786);function ce(){const e=function(e){return{empty:(0,w.css)({marginBottom:e.spacing(2),marginLeft:e.spacing(1.75),fontSize:e.typography.fontSize})}}((0,s.useTheme2)());return a().createElement("div",{className:e.empty},"No fields")}function de(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ue(e){var t=function(e,t){if("object"!==pe(e)||null===e)return e;var n=e[Symbol.toPrimitive];if(void 0!==n){var r=n.call(e,"string");if("object"!==pe(r))return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return String(e)}(e);return"symbol"===pe(t)?t:String(t)}function pe(e){return e&&"undefined"!=typeof Symbol&&e.constructor===Symbol?"symbol":typeof e}function ge(e){const t=function(e){return{dragIcon:(0,w.css)({cursor:"drag",marginLeft:e.spacing(1),opacity:.4}),labelCount:(0,w.css)({marginLeft:e.spacing(.5),marginRight:e.spacing(.5),appearance:"none",background:"none",border:"none",fontSize:e.typography.pxToRem(11),opacity:.6,display:"flex",flexDirection:"column",alignItems:"self-end"}),contentWrap:(0,w.css)({display:"flex",alignItems:"center",justifyContent:"space-between",width:"100%"}),customWidthWrap:(0,w.css)({fontSize:e.typography.bodySmall.fontSize,cursor:"pointer"}),checkboxLabel:(0,w.css)({"> span":{overflow:"hidden",textOverflow:"ellipsis",whiteSpace:"nowrap",display:"block",maxWidth:"100%"}})}}((0,s.useTheme2)());var n,r,i,l,o,c,d;return e.labels[e.label]?a().createElement(a().Fragment,null,a().createElement("div",{className:t.contentWrap},a().createElement(s.Checkbox,{className:t.checkboxLabel,label:e.label,onChange:e.onChange,checked:null!==(d=null===(n=e.labels[e.label])||void 0===n?void 0:n.active)&&void 0!==d&&d}),e.showCount&&a().createElement("div",{className:t.labelCount},a().createElement("div",null,null===(r=e.labels[e.label])||void 0===r?void 0:r.percentOfLinesWithLabel,"%"),a().createElement("div",null,null===(i=e.labels[e.label])||void 0===i?void 0:i.cardinality," ",1===(null===(l=e.labels[e.label])||void 0===l?void 0:l.cardinality)?"value":"values")),e.columnWidthMap&&e.setColumnWidthMap&&void 0!==(null===(o=e.columnWidthMap)||void 0===o?void 0:o[e.label])&&a().createElement("div",{onClick:()=>{var t;const n=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){de(e,t,n[t])}))}return e}({},e.columnWidthMap),r=e.label,{[r]:a}=n,i=function(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}(n,[r].map(ue));null===(t=e.setColumnWidthMap)||void 0===t||t.call(e,i)},title:"Clear column width override",className:t.customWidthWrap},"Width: ",null===(c=e.columnWidthMap)||void 0===c?void 0:c[e.label],a().createElement(s.Icon,{name:"x"}))),e.draggable&&a().createElement(s.Icon,{"aria-label":"Drag and drop icon",title:"Drag and drop to reorder",name:"draggabledots",size:"lg",className:t.dragIcon})):null}function me(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function ve(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){me(e,t,n[t])}))}return e}function he(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const fe=e=>{const{columnWidthMap:t,setColumnWidthMap:n}=q(),{reorderColumn:r,labels:i,valueFilter:l,toggleColumn:s}=e,o=(0,oe.$j)(),{columns:c}=q(),d=function(e){return{wrap:(0,w.css)({marginTop:e.spacing(1),marginBottom:e.spacing(1),display:"flex",background:e.colors.background.primary}),dragging:(0,w.css)({background:e.colors.background.secondary}),columnWrapper:(0,w.css)({marginBottom:e.spacing(1.5),paddingLeft:e.spacing(.5)})}}(o),u=Object.keys(i).filter((e=>l(e))),p=e=>{const t=i[e];if(t)return`${e} appears in ${null==t?void 0:t.percentOfLinesWithLabel}% of log lines`};return u.length?a().createElement(se.JY,{onDragEnd:e=>{e.destination&&r(c,e.source.index,e.destination.index)}},a().createElement(se.gL,{droppableId:"order-fields",direction:"vertical"},(e=>a().createElement("div",he(ve({className:d.columnWrapper},e.droppableProps),{ref:e.innerRef}),u.sort(function(e){return(t,n)=>{const r=e[t],a=e[n];return null!=r.index&&null!=a.index?r.index-a.index:0}}(i)).map(((e,r)=>a().createElement(se.sx,{draggableId:e,key:e,index:r},((r,l)=>a().createElement("div",he(ve({className:(0,w.cx)(d.wrap,l.isDragging?d.dragging:void 0),ref:r.innerRef},r.draggableProps,r.dragHandleProps),{title:p(e)}),a().createElement(ge,{setColumnWidthMap:n,columnWidthMap:t,label:e,onChange:()=>s(e),labels:i,draggable:!0})))))),e.placeholder)))):a().createElement(ce,null)},be=new Intl.Collator(void 0,{sensitivity:"base"}),ye=e=>{const{labels:t,valueFilter:n,toggleColumn:r}=e,i=function(e){return{wrap:(0,w.css)({marginTop:e.spacing(.25),marginBottom:e.spacing(.25),display:"flex",background:e.colors.background.primary,borderBottom:`1px solid ${e.colors.background.canvas}`}),dragging:(0,w.css)({background:e.colors.background.secondary}),columnWrapper:(0,w.css)({marginBottom:e.spacing(1.5),paddingLeft:e.spacing(.5)})}}((0,s.useTheme2)()),l=Object.keys(t).filter((e=>n(e)));return l.length?a().createElement("div",{className:i.columnWrapper},l.sort(function(e){return(t,n)=>{const r=e[t],a=e[n];return null!=r&&null!=a?Number("TIME_FIELD"===a.type)-Number("TIME_FIELD"===r.type)||Number("BODY_FIELD"===a.type)-Number("BODY_FIELD"===r.type)||be.compare(t,n):0}}(t)).map((e=>{var n;return a().createElement("div",{key:e,className:i.wrap,title:`${e} appears in ${null===(n=t[e])||void 0===n?void 0:n.percentOfLinesWithLabel}% of log lines`},a().createElement(ge,{showCount:!0,label:e,onChange:()=>r(e),labels:t}))}))):a().createElement(ce,null)},Se=e=>{const t=function(e){return{sidebarWrap:(0,w.css)({overflowY:"scroll",height:"calc(100% - 50px)","&::-webkit-scrollbar":{display:"none"},scrollbarWidth:"none"}),columnHeaderButton:(0,w.css)({appearance:"none",background:"none",border:"none",fontSize:e.typography.pxToRem(11)}),columnHeader:(0,w.css)({display:"flex",justifyContent:"space-between",fontSize:e.typography.h6.fontSize,background:e.colors.background.secondary,position:"sticky",top:0,left:0,paddingTop:e.spacing(.75),paddingRight:e.spacing(.75),paddingBottom:e.spacing(.75),paddingLeft:e.spacing(1.5),zIndex:3,marginBottom:e.spacing(2)})}}((0,s.useTheme2)());var n,r;return a().createElement("div",{className:t.sidebarWrap},a().createElement(a().Fragment,null,a().createElement("div",{className:t.columnHeader},"Selected fields",a().createElement("button",{onClick:e.clear,className:t.columnHeaderButton},"Reset")),a().createElement(fe,{reorderColumn:e.reorderColumn,toggleColumn:e.toggleColumn,labels:null!==(n=e.filteredColumnsWithMeta)&&void 0!==n?n:e.columnsWithMeta,valueFilter:t=>{var n,r;return null!==(r=null===(n=e.columnsWithMeta[t])||void 0===n?void 0:n.active)&&void 0!==r&&r},id:"selected-fields"}),a().createElement("div",{className:t.columnHeader},"Fields"),a().createElement(ye,{toggleColumn:e.toggleColumn,labels:null!==(r=e.filteredColumnsWithMeta)&&void 0!==r?r:e.columnsWithMeta,valueFilter:t=>{var n;return!(null===(n=e.columnsWithMeta[t])||void 0===n?void 0:n.active)}})))};function we(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Oe(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){we(e,t,n[t])}))}return e}function Ee(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function xe(e){return(t,n,r)=>{if(n===r)return;const a=Oe({},t),i=Object.keys(a).filter((e=>a[e].active)).map((e=>{var t;return{fieldName:e,index:null!==(t=a[e].index)&&void 0!==t?t:0}})).sort(((e,t)=>e.index-t.index)),[l]=i.splice(n,1);i.splice(r,0,l),i.filter((e=>void 0!==e)).forEach(((e,t)=>{a[e.fieldName].index=t})),e(a)}}function Ce(){const{columns:e,setColumns:t,setVisible:n,filteredColumns:i,setFilteredColumns:l}=q(),[o,c]=(0,r.useState)(""),d=xe(t);return a().createElement(s.ClickOutsideWrapper,{onClick:()=>{n(!1),l(e),c("")},useCapture:!0},a().createElement(le,{searchValue:o,setSearchValue:c}),a().createElement(Se,{toggleColumn:n=>{if(!e||!(n in e))return void function(e,t){let n;try{n={columns:JSON.stringify(t),columnName:e}}catch(t){n={msg:"Table: ColumnSelectionDrawerWrap failed to encode context",columnName:e}}B.v.warn("failed to get column",n)}(n,e);const r=Object.keys(e).filter((t=>e[t].active)).length,a=!e[n].active||void 0;let s;if(s=Ee(Oe({},e),a?{[n]:Ee(Oe({},e[n]),{active:a,index:r})}:{[n]:Ee(Oe({},e[n]),{active:!1,index:void 0})}),function(t){if(e){var n,r;const a=!(null===(n=e[t])||void 0===n?void 0:n.active),i=null===(r=Object.keys(e).filter((t=>{var n;return null===(n=e[t])||void 0===n?void 0:n.active})))||void 0===r?void 0:r.length,l={columnAction:a?"add":"remove",columnCount:a?i+1:i-1};(0,h.reportInteraction)("grafana_logs_app_table_column_filter_clicked",l)}}(n),t(s),i){var o;const e=!(null===(o=i[n])||void 0===o?void 0:o.active);let t;t=Ee(Oe({},i),e?{[n]:Ee(Oe({},i[n]),{active:e,index:r})}:{[n]:Ee(Oe({},i[n]),{active:!1,index:void 0})}),l(t),c("")}},filteredColumnsWithMeta:i,columnsWithMeta:e,clear:()=>{const n=Oe({},e);let r=0;Object.keys(n).forEach((e=>{const t="BODY_FIELD"===n[e].type||"TIME_FIELD"===n[e].type;n[e].active=t,n[e].index=t?r++:void 0})),t(n),l(n),c("")},reorderColumn:d}))}const Pe=e=>a().createElement(je,{onMouseOut:e.onMouseOut,onMouseIn:e.onMouseIn,onClick:e.onClick,field:e.field,rowIndex:e.rowIndex},e.children),je=e=>{var t;const n=(0,s.useTheme2)(),r=X(),i=((e,t,n)=>({active:(0,w.css)({height:"calc(100% + 36px)",zIndex:e.zIndex.tooltip,background:"transparent"}),wrap:(0,w.css)({position:"absolute",overflowX:"hidden",whiteSpace:"nowrap",width:"100%",height:"100%",left:0,top:0,margin:"auto",background:"transparent"})}))(n,0,null===(t=r.cellIndex)||void 0===t||t.numberOfMenuItems);return a().createElement("div",{onMouseLeave:e.onMouseOut,onMouseEnter:e.onMouseIn,onClick:e.onClick,className:r.cellIndex.index===e.rowIndex&&r.cellIndex.fieldName===e.field.name?(0,w.cx)(i.wrap,i.active):i.wrap},e.children)},Fe={logsFrame:null,addFilter:e=>{},timeRange:void 0,selectedLine:void 0},ke=(0,r.createContext)(Fe),Te=({children:e,logsFrame:t,addFilter:n,selectedLine:r,timeRange:i})=>a().createElement(ke.Provider,{value:{logsFrame:t,addFilter:n,selectedLine:r,timeRange:i}},e),_e=()=>(0,r.useContext)(ke);var Le=n(4793);const De=e=>{const t=((e,t)=>({menu:(0,w.css)({position:"relative",paddingRight:"5px",display:"flex",minWidth:"60px",justifyContent:"flex-start"}),menuItemsWrap:(0,w.css)({boxShadow:e.shadows.z3,display:"flex",background:e.colors.background.secondary,padding:"5px 0",marginLeft:"column"===t?"5px":void 0}),menuItem:(0,w.css)({overflow:"auto",textOverflow:"ellipsis",cursor:"pointer",paddingLeft:"5px",paddingRight:"5px",display:"flex",alignItems:"center"})}))((0,s.useTheme2)(),e.pillType),{addFilter:n}=_e();return a().createElement("span",{className:t.menu},a().createElement("span",{className:t.menuItemsWrap},"derived"!==e.fieldType&&a().createElement(a().Fragment,null,a().createElement("div",{className:t.menuItem,onClick:()=>{n({key:e.label,value:e.value,operator:Le.w.Equal})}},a().createElement(s.Icon,{title:"Add to search",size:"md",name:"plus-circle"})),a().createElement("div",{className:t.menuItem,onClick:()=>{n({key:e.label,value:e.value,operator:Le.w.NotEqual})}},a().createElement(s.Icon,{title:"Exclude from search",size:"md",name:"minus-circle"}))),e.showColumn&&a().createElement("div",{title:"Add column",onClick:e.showColumn,className:t.menuItem},a().createElement("svg",{width:"18",height:"16",viewBox:"0 0 18 16",fill:"none",xmlns:"http://www.w3.org/2000/svg"},a().createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M1.38725 1.33301H13.3872C13.5641 1.33301 13.7336 1.40325 13.8587 1.52827C13.9837 1.65329 14.0539 1.82286 14.0539 1.99967V2.33333C14.0539 2.70152 13.7554 3 13.3872 3H13.0542C12.87 3 12.7206 2.85062 12.7206 2.66634H8.05391V13.333H12.7206C12.7206 13.1491 12.8697 13 13.0536 13H13.3872C13.7554 13 14.0539 13.2985 14.0539 13.6667V13.9997C14.0539 14.1765 13.9837 14.3461 13.8587 14.4711C13.7336 14.5961 13.5641 14.6663 13.3872 14.6663H1.38725C1.21044 14.6663 1.04087 14.5961 0.915843 14.4711C0.790819 14.3461 0.720581 14.1765 0.720581 13.9997V1.99967C0.720581 1.82286 0.790819 1.65329 0.915843 1.52827C1.04087 1.40325 1.21044 1.33301 1.38725 1.33301ZM2.05391 13.333H6.72058V2.66634H2.05391V13.333Z",fill:"#CCCCDC",fillOpacity:"1"}),a().createElement("path",{d:"M13.8538 7.19999H16.2538C16.466 7.19999 16.6695 7.28429 16.8195 7.4343C16.9696 7.58432 17.0538 7.78783 17.0538 7.99999C17.0538 8.21214 16.9696 8.41566 16.8195 8.56567C16.6695 8.71569 16.466 8.79999 16.2538 8.79999H13.8538V11.2C13.8538 11.4121 13.7696 11.6156 13.6195 11.7657C13.4695 11.9157 13.266 12 13.0538 12C12.8416 12 12.6382 11.9157 12.4881 11.7657C12.3381 11.6156 12.2538 11.4121 12.2538 11.2V8.79999H9.85384C9.64165 8.79999 9.43819 8.71569 9.28815 8.56567C9.13811 8.41566 9.05383 8.21214 9.05383 7.99999C9.05383 7.78783 9.13811 7.58432 9.28815 7.4343C9.43819 7.28429 9.64165 7.19999 9.85384 7.19999H12.2538V4.8C12.2538 4.58784 12.3381 4.38433 12.4881 4.23431C12.6382 4.0843 12.8416 4 13.0538 4C13.266 4 13.4695 4.0843 13.6195 4.23431C13.7696 4.38433 13.8538 4.58784 13.8538 4.8V7.19999Z",fill:"#CCCCDC",fillOpacity:"1"}))),e.links&&e.links.map((e=>{var n;return a().createElement("div",{className:t.menuItem,onClick:()=>{window.open(e.href,"_blank")},key:e.href},a().createElement(s.Icon,{title:null!==(n=e.title)&&void 0!==n?n:"Link",key:e.href,size:"md",name:"link"}))}))))},Ne="detected_level",$e=e=>{const{label:t,value:n}=e,r=(0,s.useTheme2)(),{cellIndex:l}=X();let o;if(t===Ne){const e=it().options;"string"==typeof n&&n in e&&(o=e[n].color)}const c=l.index===e.rowIndex&&e.field.name===l.fieldName,d=((e,t)=>({activePillWrap:(0,w.css)({}),pillWrap:(0,w.css)({width:"100%"}),pill:(0,w.css)({border:`1px solid ${e.colors.border.weak}`,"&:hover":{border:`1px solid ${e.colors.border.strong}`},marginRight:"5px",marginTop:"4px",marginLeft:"5px",padding:"2px 5px",position:"relative",display:"inline-flex",flexDirection:"row-reverse",backgroundColor:"transparent",paddingLeft:t?`${e.spacing(.75)}`:"2px","&:before":{content:'""',position:"absolute",left:0,top:0,height:"100%",width:`${e.spacing(.25)}`,backgroundColor:t}}),menu:(0,w.css)({width:"100%"}),menuItem:(0,w.css)({overflow:"auto",textOverflow:"ellipsis"}),menuItemText:(0,w.css)({width:"65px",display:"inline-block"})}))(r,o);return a().createElement("div",{className:(0,w.cx)(d.pillWrap,c?d.activePillWrap:void 0)},!!n&&a().createElement(a().Fragment,null,a().createElement("span",{className:d.pill},a().createElement(a().Fragment,null,n)),c&&"string"==typeof n&&e.field.type!==i.FieldType.time&&a().createElement(De,{label:e.label,value:n,pillType:"column"})))};var Ie=n(8315);function Ae(e){var t;const n=((e,t)=>({clipboardButton:(0,w.css)({padding:0,height:"100%",lineHeight:"1",width:"20px"}),inspectButton:(0,w.css)({display:"inline-flex",verticalAlign:"middle",margin:0,overflow:"hidden",borderRadius:"5px"}),iconWrapper:(0,w.css)({height:"35px",position:"sticky",left:0,display:"flex",background:e.colors.background.secondary,padding:`0 ${e.spacing(.5)}`,zIndex:1,boxShadow:e.shadows.z2}),inspect:(0,w.css)({padding:"5px 3px","&:hover":{color:e.colors.text.link,cursor:"pointer"}})}))((0,s.useTheme2)()),{logsFrame:i,timeRange:l}=_e(),o=null==i||null===(t=i.idField)||void 0===t?void 0:t.values[e.rowIndex],c=null==i?void 0:i.bodyField.values[e.rowIndex],[d,u]=(0,r.useState)(!1),p=(0,r.useCallback)((()=>l?(0,Ie.gW)("selectedLine",{id:o,row:e.rowIndex},l):""),[o,e.rowIndex,l]);return a().createElement(a().Fragment,null,a().createElement("div",{className:n.iconWrapper},a().createElement("div",{className:n.inspect},a().createElement(s.IconButton,{"data-testid":f.b.table.inspectLine,className:n.inspectButton,tooltip:"View log line",variant:"secondary","aria-label":"View log line",tooltipPlacement:"top",size:"md",name:"eye",onClick:()=>u(!0),tabIndex:0})),a().createElement("div",{className:n.inspect},a().createElement(s.ClipboardButton,{className:n.clipboardButton,icon:"share-alt",variant:"secondary",fill:"text",size:"md",tooltip:"Copy link to log line",tooltipPlacement:"top",tabIndex:0,getText:p}))),a().createElement(a().Fragment,null,d&&a().createElement(s.Modal,{onDismiss:()=>u(!1),isOpen:!0,title:"Inspect value"},a().createElement("pre",null,c),a().createElement(s.Modal.ButtonRow,null,a().createElement(s.ClipboardButton,{icon:"copy",getText:()=>e.value},"Copy to Clipboard")))))}const Be=e=>{var t;let n=e.value;const r=e.field,l=r.display(n),o=((e,t)=>({flexWrap:(0,w.css)({display:"flex",alignItems:"flex-start",flexDirection:t===i.FieldType.number?"row-reverse":"row",textAlign:t===i.FieldType.number?"right":"left"}),content:(0,w.css)({position:"relative",overflow:"hidden",display:"flex",height:"100%"}),linkWrapper:(0,w.css)({color:e.colors.text.link,marginTop:"7px",marginLeft:"7px","&:hover":{textDecoration:"underline"}})}))((0,s.useTheme2)(),e.field.type),{setVisible:c}=q(),{cellIndex:d,setActiveCellIndex:u}=X(),p={index:e.rowIndex},g=Boolean(null===(t=(0,s.getCellLinks)(e.field,p))||void 0===t?void 0:t.length);return null===n?a().createElement(a().Fragment,null):(n=a().isValidElement(e.value)?e.value:"object"==typeof n?JSON.stringify(e.value):(0,i.formattedValueToString)(l),a().createElement(Pe,{onClick:()=>e.rowIndex===d.index&&e.field.name===d.fieldName?u({index:null}):u({index:e.rowIndex,fieldName:e.field.name,numberOfMenuItems:3}),field:e.field,rowIndex:e.rowIndex},a().createElement("div",{className:o.content},0===e.fieldIndex&&a().createElement(Ae,{value:n,rowIndex:e.rowIndex}),a().createElement("div",{className:o.flexWrap}),!g&&((t,n)=>a().createElement($e,{field:e.field,rowIndex:e.rowIndex,showColumns:()=>c(!0),label:n,value:t}))(n,r.name),g&&r.getLinks&&a().createElement(s.DataLinksContextMenu,{links:()=>{var e;return null!==(e=(0,s.getCellLinks)(r,p))&&void 0!==e?e:[]}},(e=>e.openMenu?a().createElement("div",{className:o.linkWrapper,onClick:e.openMenu},a().createElement(a().Fragment,null,n)):a().createElement("div",{className:o.linkWrapper},a().createElement(a().Fragment,null,n)))))))};function Ve(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Me(e){const t=(0,s.useTheme2)();let n;if(e.label===Ne){const t=it().options;e.value in t&&(n=t[e.value].color)}const r=((e,t)=>({pill:(0,w.css)({flex:"0 1 auto",marginLeft:e.spacing(.5),marginRight:e.spacing(.5),padding:`${e.spacing(.25)} ${e.spacing(.25)}`,position:"relative",display:"inline-flex",flexDirection:"column",marginTop:e.spacing(.5)}),activePill:(0,w.css)({}),valueWrap:(0,w.css)({border:`1px solid ${e.colors.background.secondary}`,boxShadow:`-2px 2px 5px 0px ${e.colors.background.secondary}`,backgroundColor:"transparent",cursor:"pointer",position:"relative",paddingRight:`${e.spacing(.5)}`,paddingLeft:t?`${e.spacing(.75)}`:`${e.spacing(.5)}`,"&:before":{content:'""',position:"absolute",left:0,top:0,height:"100%",width:`${e.spacing(.25)}`,backgroundColor:t},"&:hover":{border:`1px solid ${e.colors.border.strong}`}})}))(t,n);return a().createElement("span",{className:(0,w.cx)(r.pill,e.menuActive?r.activePill:void 0),onClick:e.onClick},a().createElement("span",{className:r.valueWrap},e.label,"=",e.value),e.menuActive&&a().createElement(De,{pillType:"logPill",fieldType:e.fieldType,links:e.links,label:e.label,value:e.value,showColumn:e.onClickAdd}))}const Re=e=>{const{label:t}=e,{cellIndex:n,setActiveCellIndex:l}=X(),{columns:o,setColumns:c}=q(),d=e.value,u=(0,h.getTemplateSrv)(),p=(0,r.useMemo)((()=>u.replace.bind(u)),[u]),g=e.field;if(!g||(null==g?void 0:g.type)===i.FieldType.other)return null;const m={index:e.rowIndex};e.originalField&&e.isDerivedField&&e.originalFrame&&(e.originalField.getLinks=(0,i.getLinksSupplier)(e.originalFrame,e.originalField,{},p));const v=e.originalField&&(0,s.getCellLinks)(e.originalField,m);return a().createElement(Me,{onClick:()=>e.rowIndex===n.index&&g.name===n.fieldName&&t===n.subFieldName?l({index:null}):l({index:e.rowIndex,fieldName:g.name,subFieldName:t,numberOfMenuItems:e.isDerivedField?2:3}),menuActive:n.index===e.rowIndex&&n.fieldName===g.name&&n.subFieldName===t,fieldType:e.isDerivedField?"derived":void 0,label:t,value:d,onClickAdd:()=>(e=>{const t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Ve(e,t,n[t])}))}return e}({},o),n=Object.keys(o).filter((e=>o[e].active)).length;t[e].active?(t[e].active=!1,t[e].index=void 0):(t[e].active=!0,t[e].index=n),c(t)})(t),links:v})},We=e=>{var t,n;null==e||null===(n=e.current)||void 0===n||n.scrollTo({left:null===(t=e.current)||void 0===t?void 0:t.scrollLeft})};function ze({scrollerRef:e}){const t=(e=>({scroller:w.css`
    position: absolute;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 20px;
    top: 32px;
    margin-top: -24px;
    // For some reason clicking on this button causes text to be selected in the following row
    user-select: none;
  `,scrollLeft:w.css`
    cursor: pointer;
    background: ${e.colors.background.primary};

    &:hover {
      background: ${e.colors.background.secondary};
    }
  `,scrollRight:w.css`
    cursor: pointer;
    background: ${e.colors.background.primary};

    &:hover {
      background: ${e.colors.background.secondary};
    }
  `}))((0,s.useTheme2)());return a().createElement("div",{className:t.scroller},a().createElement("span",{onPointerDown:()=>{var t,n;null==(t=e)||null===(n=t.current)||void 0===n||n.scrollTo({top:0,left:0,behavior:"smooth"})},onPointerUp:()=>We(e),className:t.scrollLeft},a().createElement(s.Icon,{name:"arrow-left"})),a().createElement("span",{onPointerDown:()=>{var t,n;null==(t=e)||null===(n=t.current)||void 0===n||n.scrollTo({top:0,left:t.current.scrollWidth,behavior:"smooth"})},onPointerUp:()=>We(e),className:t.scrollRight},a().createElement(s.Icon,{name:"arrow-right"})))}function He(e){const t=(0,s.useTheme2)(),n=Ge(t);return a().createElement("div",{className:n.rawLogLine},a().createElement(a().Fragment,null,e.value))}const Ge=(e,t)=>({rawLogLine:(0,w.css)({fontFamily:e.typography.fontFamilyMonospace,height:"35px",lineHeight:"35px",paddingRight:e.spacing(1.5),paddingLeft:e.spacing(1),fontSize:e.typography.bodySmall.fontSize})}),qe=e=>{let t=e.value;const n=e.field,l=n.display(t),o=(0,s.useTheme2)(),c=Qe(o),{columns:d,setVisible:u,bodyState:p}=q(),{logsFrame:g}=_e(),[m,v]=(0,r.useState)(!1),h=(0,r.useRef)(null);t=a().isValidElement(e.value)?e.value:"object"==typeof t?JSON.stringify(e.value):(0,i.formattedValueToString)(l);const f=(t=>Object.keys(d).filter((e=>e!==A(g))).sort(((e,t)=>e===Ne?-1:t===Ne?1:"LINK_FIELD"===d[e].type?-1:"LINK_FIELD"===d[t].type?1:d[e].cardinality>d[t].cardinality?-1:1)).filter((e=>!d[e].active&&d[e].cardinality>1)).map((r=>{var l;const s=t[r],o=null==g||null===(l=g.raw)||void 0===l?void 0:l.fields.find((e=>e.name===r)),c=null==n?void 0:n.values[e.rowIndex],p=!s&&!!c;if(s)return a().createElement(Re,{originalFrame:void 0,field:n,columns:d,rowIndex:e.rowIndex,frame:e.frame,showColumns:()=>u(!0),key:r,label:r,isDerivedField:!1,value:s});if(p&&(null==o?void 0:o.name)){const t=null==o?void 0:o.values[e.rowIndex];if((null==o?void 0:o.type)===i.FieldType.string&&t)return a().createElement(Re,{originalFrame:null==g?void 0:g.raw,originalField:o,field:n,value:t,columns:d,rowIndex:e.rowIndex,frame:e.frame,showColumns:()=>u(!0),key:o.name,label:o.name,isDerivedField:!0})}return null})).filter((e=>e)))(e.labels),b=p===W.auto,y=f.length>0;return a().createElement(Pe,{onMouseIn:()=>{v(!0)},onMouseOut:()=>{v(!1)},rowIndex:e.rowIndex,field:e.field},a().createElement(Q.ScrollSyncPane,{innerRef:h,group:"horizontal"},a().createElement("div",{className:c.content},0===e.fieldIndex&&a().createElement(Ae,{rowIndex:e.rowIndex,value:t}),b&&y&&a().createElement(a().Fragment,null,f),p===W.labels&&y&&a().createElement(a().Fragment,null,f),p===W.labels&&!y&&a().createElement(He,{value:t}),b&&!y&&a().createElement(He,{value:t}),p===W.text&&a().createElement(He,{value:t}),m&&a().createElement(ze,{scrollerRef:h}))))},Qe=e=>({content:w.css`
    white-space: nowrap;
    overflow-x: auto;
    -ms-overflow-style: none; /* IE and Edge */
    scrollbar-width: none; /* Firefox */
    padding-right: 30px;
    display: flex;
    align-items: flex-start;
    height: 100%;
    &::-webkit-scrollbar {
      display: none; /* Chrome, Safari and Opera */
    }

    &:after {
      pointer-events: none;
      content: '';
      width: 100%;
      height: 100%;
      position: absolute;
      left: 0;
      top: 0;
      // Fade out text in last 10px to background color to add affordance to horiziontal scroll
      background: linear-gradient(to right, transparent calc(100% - 10px), ${e.colors.background.primary});
    }
  `}),Ue=e=>{const{setHeaderMenuActive:t,isHeaderMenuActive:n}=te(),{logsFrame:i}=_e(),l=(0,r.useRef)(null),o=((e,t,n)=>({tableHeaderMenu:(0,w.css)({label:"tableHeaderMenu",width:"100%",minWidth:"250px",height:"100%",maxHeight:"400px",backgroundColor:e.colors.background.primary,border:`1px solid ${e.colors.border.weak}`,padding:e.spacing(2),margin:e.spacing(1,0),boxShadow:e.shadows.z3,borderRadius:e.shape.radius.default}),button:(0,w.css)({appearance:"none",right:"5px",background:"none",border:"none",padding:0}),wrapper:(0,w.css)({display:"flex",marginLeft:t?"56px":"6px",width:n?"calc(100% + 6px)":"100%",borderRight:`1px solid ${e.colors.border.weak}`,marginRight:"-6px"}),defaultContentWrapper:(0,w.css)({borderLeft:t?`1px solid ${e.colors.border.weak}`:"none",marginLeft:t?"-6px":0,paddingLeft:t?"12px":0,display:"flex"})}))((0,s.useTheme2)(),0===e.fieldIndex,e.field.name===A(i));return a().createElement("span",{className:o.wrapper},a().createElement("span",{className:o.defaultContentWrapper},e.defaultContent),a().createElement("button",{className:o.button,ref:l,onClick:e=>{t(!n)}},a().createElement(s.Icon,{title:"Show menu",name:"ellipsis-v"})),l.current&&a().createElement(s.Popover,{show:n,content:a().createElement(s.ClickOutsideWrapper,{onClick:()=>t(!1),useCapture:!0},a().createElement("div",{className:o.tableHeaderMenu},e.children)),referenceElement:l.current}))};function Je(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Ye(e){const{setHeaderMenuActive:t}=te(),{columns:n,setColumns:i,bodyState:l,setBodyState:o}=q(),{logsFrame:c}=_e(),d=Ke(),u=(0,r.useCallback)((e=>{const t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Je(e,t,n[t])}))}return e}({},n);Object.keys(t).filter((n=>{const r=t[n].index,a=t[e.name].index;return t[n].active&&a&&r&&r>a})).map((e=>t[e])).forEach((e=>{void 0!==e.index&&e.index--})),t[e.name].active=!1,t[e.name].index=void 0,i(t)}),[n,i]),p=e.headerProps.field.name===A(c);return a().createElement(Ue,e.headerProps,a().createElement("div",{className:d.linkWrap},a().createElement("a",{className:d.link,onClick:()=>{e.openColumnManagementDrawer(),t(!1)}},a().createElement(s.Icon,{className:d.icon,name:"columns",size:"md"}),"Manage columns")),a().createElement("div",{className:d.linkWrap},a().createElement("a",{className:d.link,onClick:()=>u(e.headerProps.field)},a().createElement("svg",{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 17 16",width:"17",height:"16",className:"css-q2u0ig-Icon"},a().createElement("path",{fillRule:"evenodd",clipRule:"evenodd",d:"M1.73446 1.33301H12.2345C12.3892 1.33301 12.5375 1.40325 12.6469 1.52827C12.7563 1.65329 12.8178 1.82286 12.8178 1.99967V4.74967C12.8178 5.07184 12.5566 5.33301 12.2345 5.33301C11.9123 5.33301 11.6511 5.07184 11.6511 4.74967V2.66634H7.56779V13.333H11.6511V10.9163C11.6511 10.5942 11.9123 10.333 12.2345 10.333C12.5566 10.333 12.8178 10.5942 12.8178 10.9163V13.9997C12.8178 14.1765 12.7563 14.3461 12.6469 14.4711C12.5375 14.5961 12.3892 14.6663 12.2345 14.6663H1.73446C1.57975 14.6663 1.43137 14.5961 1.32198 14.4711C1.21258 14.3461 1.15112 14.1765 1.15112 13.9997V1.99967C1.15112 1.82286 1.21258 1.65329 1.32198 1.52827C1.43137 1.40325 1.57975 1.33301 1.73446 1.33301ZM2.31779 13.333H6.40112V2.66634H2.31779V13.333Z",fill:"#CCCCDC",fillOpacity:"1"}),a().createElement("path",{d:"M15.9893 10.6315C15.9498 10.7263 15.8919 10.8123 15.819 10.8846C15.7467 10.9575 15.6607 11.0154 15.5659 11.0549C15.4712 11.0943 15.3695 11.1147 15.2668 11.1147C15.1641 11.1147 15.0625 11.0943 14.9677 11.0549C14.8729 11.0154 14.7869 10.9575 14.7146 10.8846L12.9335 9.09573L11.1524 10.8846C11.0801 10.9575 10.9941 11.0154 10.8993 11.0549C10.8045 11.0943 10.7028 11.1147 10.6002 11.1147C10.4975 11.1147 10.3958 11.0943 10.301 11.0549C10.2063 11.0154 10.1202 10.9575 10.0479 10.8846C9.97504 10.8123 9.91717 10.7263 9.87769 10.6315C9.8382 10.5367 9.81787 10.4351 9.81787 10.3324C9.81787 10.2297 9.8382 10.1281 9.87769 10.0333C9.91717 9.9385 9.97504 9.85248 10.0479 9.78017L11.8368 7.99906L10.0479 6.21795C9.90148 6.07149 9.8192 5.87285 9.8192 5.66573C9.8192 5.4586 9.90148 5.25996 10.0479 5.1135C10.1944 4.96705 10.393 4.88477 10.6002 4.88477C10.8073 4.88477 11.0059 4.96705 11.1524 5.1135L12.9335 6.90239L14.7146 5.1135C14.8611 4.96705 15.0597 4.88477 15.2668 4.88477C15.4739 4.88477 15.6726 4.96705 15.819 5.1135C15.9655 5.25996 16.0478 5.4586 16.0478 5.66573C16.0478 5.87285 15.9655 6.07149 15.819 6.21795L14.0302 7.99906L15.819 9.78017C15.8919 9.85248 15.9498 9.9385 15.9893 10.0333C16.0288 10.1281 16.0491 10.2297 16.0491 10.3324C16.0491 10.4351 16.0288 10.5367 15.9893 10.6315Z",fill:"#CCCCDC",fillOpacity:"1"})),"Remove column")),e.slideLeft&&a().createElement("div",{className:d.linkWrap},a().createElement("a",{className:d.link,onClick:()=>{var t;return null===(t=e.slideLeft)||void 0===t?void 0:t.call(e,n)}},a().createElement(s.Icon,{className:(0,w.cx)(d.icon,d.reverse),name:"arrow-from-right",size:"md"}),"Move left")),e.slideRight&&a().createElement("div",{className:d.linkWrap},a().createElement("a",{className:d.link,onClick:()=>{var t;return null===(t=e.slideRight)||void 0===t?void 0:t.call(e,n)}},a().createElement(s.Icon,{className:d.icon,name:"arrow-from-right",size:"md"}),"Move right")),p&&a().createElement("div",{className:d.linkWrap},a().createElement("a",{className:d.link,onClick:()=>{l===W.text?o(W.labels):o(W.text)}},l===W.text?a().createElement(s.Icon,{className:d.icon,name:"brackets-curly",size:"md"}):a().createElement(s.Icon,{className:d.icon,name:"text-fields",size:"md"}),l===W.text?"Show labels":"Show log text")),e.autoColumnWidths&&a().createElement("div",{className:d.linkWrap},a().createElement("a",{className:d.link,onClick:()=>{var t;return null===(t=e.autoColumnWidths)||void 0===t?void 0:t.call(e)}},a().createElement(s.Icon,{className:d.icon,name:"arrows-h",size:"md"}),"Reset column widths")))}const Ke=()=>({reverse:(0,w.css)({transform:"scaleX(-1)"}),link:(0,w.css)({paddingTop:"5px",paddingBottom:"5px"}),icon:(0,w.css)({marginRight:"10px"}),linkWrap:(0,w.css)({})});function Xe(e,t,n,r,a,i,l){try{var s=e[i](l),o=s.value}catch(e){return void n(e)}s.done?t(o):Promise.resolve(o).then(r,a)}function Ze(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function et(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Ze(e,t,n[t])}))}return e}function tt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function nt(e){return a().createElement(s.Table,{onColumnResize:e.onResize,initialSortBy:[{displayName:I(e.logsFrame),desc:!0}],initialRowIndex:e.selectedLine,cellHeight:J.qM.Sm,data:e.data,height:e.height,width:e.width,footerOptions:{show:!0,reducer:["count"],countRows:!0}})}const rt=e=>{const{height:t,timeZone:n,logsFrame:l,width:o,labels:c}=e,d=(0,s.useTheme2)(),u={section:(0,w.css)({position:"relative"}),tableWrap:(0,w.css)({".cellActions":{display:"none !important"}})},[p,g]=(0,r.useState)(void 0),{columns:m,visible:v,setVisible:b,setFilteredColumns:y,setColumns:S,clearSelectedLine:E,columnWidthMap:x,setColumnWidthMap:C}=q(),{selectedLine:P}=_e(),[j]=(0,r.useState)(P),F=xe(S),k=(0,h.getTemplateSrv)(),T=(0,r.useMemo)((()=>k.replace.bind(k)),[k]),_=(0,r.useCallback)((e=>{if(!e.length)return e;const[t]=(0,i.applyFieldOverrides)({data:[e],timeZone:n,theme:d,replaceVariables:T,fieldConfig:{defaults:{custom:{}},overrides:[]}});for(const[n,d]of t.fields.entries()){var r,s;d.type=d.type===i.FieldType.string?null!==(r=at(d))&&void 0!==r?r:i.FieldType.string:d.type,d.config=tt(et({},d.config),{custom:et({inspect:!0,filterable:!0,headerComponent:t=>a().createElement(ee,null,a().createElement(Ye,{headerProps:tt(et({},t),{fieldIndex:n}),openColumnManagementDrawer:()=>b(!0),slideLeft:0!==n?e=>F(e,n,n-1):void 0,slideRight:n!==e.fields.length-1?e=>F(e,n,n+1):void 0,autoColumnWidths:Object.keys(x).length>0?()=>{C({})}:void 0})),width:null!==(s=x[d.name])&&void 0!==s?s:st(d,n,m,o,t.fields.length,l),cellOptions:lt(d,n,c,l)},d.config.custom),filterable:!0})}return t}),[n,d,c,o,T,b,x]);(0,r.useEffect)((()=>{const e=(t=function*(){const e=(t=l.raw).fields.filter((e=>{var n,r,a;const l="json.RawMessage"===(null===(n=e.typeInfo)||void 0===n?void 0:n.frame)&&"labels"===e.name&&(null==t||null===(r=t.meta)||void 0===r?void 0:r.type)!==i.DataFrameType.LogLines,s="labels"===e.name&&e.type===i.FieldType.other&&(null==t||null===(a=t.meta)||void 0===a?void 0:a.type)===i.DataFrameType.LogLines;return l||s})).flatMap((e=>[{id:"extractFields",options:{format:"json",keepTime:!1,replace:!1,source:e.name}}]));var t;const n=function(e){let t={};for(const n in e)t[n]=!0;return Object.keys(e).length>0?{id:"organize",options:{indexByName:e,includeByName:t}}:null}(function(e){let t={};return Object.keys(e).filter((t=>e[t].active)).forEach((n=>{const r=e[n].index;void 0!==r&&(t[n]=r)})),t}(m));if(n)e.push(n);else{const t={time:l.timeField,body:l.bodyField,extraFields:l.extraFields};t&&void 0!==t.body&&void 0!==t.time&&e.push(function(e){return{id:"organize",options:{indexByName:{[e.time.name]:0,[e.body.name]:1},includeByName:{[e.body.name]:!0,[e.time.name]:!0}}}}(t))}if(e.length>0){const t=yield(0,U.lastValueFrom)((0,i.transformDataFrame)(e,[l.raw])),n=_(t[0]);g(n)}else g(_(l.raw))},n=function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function l(e){Xe(i,r,a,l,s,"next",e)}function s(e){Xe(i,r,a,l,s,"throw",e)}l(void 0)}))},function(){return n.apply(this,arguments)});var t,n;e()}),[l.raw,l.bodyField,l.timeField,l.extraFields,_,m]),(0,r.useEffect)((()=>{j&&P&&E()}),[j,E,P]);const L=l.raw.fields.find((e=>e.name===function(e){var t,n;return null!==(n=null==e||null===(t=e.idField)||void 0===t?void 0:t.name)&&void 0!==n?n:"id"}(l))),D=null==L?void 0:L.values.findIndex((e=>e===(null==j?void 0:j.id))),N=D&&-1!==D?D:void 0;return p?a().createElement("div",{"data-testid":f.b.table.wrapper,className:u.section},v&&a().createElement(s.Drawer,{size:"sm",onClose:()=>{b(!1),y(m)}},a().createElement(Ce,null)),a().createElement("div",{className:u.tableWrap},a().createElement(K,null,a().createElement(Q.ScrollSync,{horizontal:!0,vertical:!1,proportional:!1},a().createElement(nt,{logsFrame:l,selectedLine:N,data:p,height:t,width:o,onResize:(0,O.debounce)(((e,t)=>{const n=Object.keys(m).filter((e=>m[e].active)).find((t=>t===e));if(n&&t>0){const e=et({},x);e[n]=t,C(e)}}),100)}))))):a().createElement(a().Fragment,null)};function at(e){if(e.name){const t=e.name.toLowerCase();if("date"===t||"time"===t)return i.FieldType.time}for(let t=0;t<e.values.length;t++){const n=e.values[t];if(null!=n)return pt(n)}}const it=()=>({options:{critical:{color:"#705da0",index:0},crit:{color:"#705da0",index:1},error:{color:"#e24d42",index:2},err:{color:"#e24d42",index:3},eror:{color:"#e24d42",index:4},warning:{color:"#FF9900",index:5},warn:{color:"#FF9900",index:6},info:{color:"#7eb26d",index:7},debug:{color:"#1f78c1",index:8},trace:{color:"#6ed0e0",index:9}},type:i.MappingType.ValueToText});function lt(e,t,n,r){return e.name===A(r)?{cellComponent:e=>a().createElement(qe,tt(et({},e),{fieldIndex:t,labels:n[e.rowIndex]})),type:s.TableCellDisplayMode.Custom}:{cellComponent:e=>a().createElement(Be,tt(et({},e),{fieldIndex:t})),type:s.TableCellDisplayMode.Custom}}function st(e,t,n,r,a,l){var s,o;const c=a<=2?r:Math.min(r/2),d=0===t?50:0;if(e.type===i.FieldType.time)return 200+d;const u=n[e.name];if(void 0===u)return;var p;const g=Math.max(null!==(p=u.maxLength)&&void 0!==p?p:0,e.name.length);return u.maxLength?Math.min(Math.max(6.5*g+95+d,90+d),c):e.name!==A(l)?Math.min(Math.max(6.5*(null!==(m=null===(o=e.values)||void 0===o||null===(s=o[0])||void 0===s?void 0:s.length)&&void 0!==m?m:80)+95+d,90+d),c):void 0;var m}var ot=n(5540);const ct=/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}(?:\.\d{3,})?(?:Z|[-+]\d{2}:?\d{2})$/,dt=e=>{const{logsFrame:t}=_e(),[n,l]=(0,r.useState)({width:0,height:0});(0,ot.w)({ref:e.panelWrap,onResize:()=>{const t=e.panelWrap.current;t&&(n.width===t.clientWidth&&n.height===t.clientHeight||l({width:t.clientWidth,height:t.clientHeight}))}});const s={section:(0,w.css)({position:"relative"})},o=(0,i.getTimeZone)(),c=(0,r.useCallback)((t=>{const n=e.urlColumns;return(null==n?void 0:n.length)&&Object.values(n).forEach(((e,n)=>{t[e]&&(t[e].active=!0,t[e].index=n)})),t}),[e.urlColumns]);if(!t||!t.raw.length)return null;var d;const u=null!==(d=t.getLogFrameLabelsAsLabels())&&void 0!==d?d:[],p=t?t.raw.length:0;let g=function(e,t){let n={};const r=new Map,a=function(e){const t=new Map;return e.forEach((e=>{Object.keys(e).forEach((n=>{if(t.has(n)){const r=t.get(n),a=null==r?void 0:r.valueSet,i=null==r?void 0:r.maxLength;a&&!(null==a?void 0:a.has(e[n]))&&(null==a||a.add(e[n]),i&&e[n].length>i&&t.set(n,{maxLength:e[n].length,valueSet:a}))}else t.set(n,{maxLength:e[n].length,valueSet:new Set([e[n]])})}))})),t}(t),i=e?e.length:0;return(null==t?void 0:t.length)&&i&&(t.forEach((e=>{Object.keys(e).forEach((e=>{var t;const n=a.get(e);var i;const l=null!==(i=null==n||null===(t=n.valueSet)||void 0===t?void 0:t.size)&&void 0!==i?i:0;if(r.has(e)){const t=r.get(e);t&&((null==t?void 0:t.active)?r.set(e,{percentOfLinesWithLabel:t.percentOfLinesWithLabel+1,active:!0,index:t.index,cardinality:l,maxLength:null==n?void 0:n.maxLength}):r.set(e,{percentOfLinesWithLabel:t.percentOfLinesWithLabel+1,active:!1,index:void 0,cardinality:l,maxLength:null==n?void 0:n.maxLength}))}else r.set(e,{percentOfLinesWithLabel:1,active:!1,index:void 0,cardinality:l,maxLength:null==n?void 0:n.maxLength})}))})),n=Object.fromEntries(r),Object.keys(n).forEach((e=>{n[e].percentOfLinesWithLabel=ut(n[e].percentOfLinesWithLabel,i)}))),n}(t.raw,u);const m={time:t.timeField,body:t.bodyField,extraFields:t.extraFields};return m&&(function(e,t,n){e.forEach((e=>{var r,a;if(!e)return;const i=null===(r=t[e.name])||void 0===r?void 0:r.active,l=null===(a=t[e.name])||void 0===a?void 0:a.index;t[e.name]=i&&void 0!==l?{percentOfLinesWithLabel:ut(e.values.filter((e=>null!=e)).length,n),active:!0,index:l,cardinality:n}:{percentOfLinesWithLabel:ut(e.values.filter((e=>null!=e)).length,n),active:!1,index:void 0,cardinality:n}}))}([m.time,m.body,...m.extraFields],g,p),g=c(g),function(e,t,n){var r,a,i,l,s,o,c,d,u,p;0===e.length&&((null===(i=t.body)||void 0===i?void 0:i.name)&&(n[null===(s=t.body)||void 0===s?void 0:s.name].active=!0,n[null===(o=t.body)||void 0===o?void 0:o.name].index=1),(null===(l=t.time)||void 0===l?void 0:l.name)&&(n[null===(c=t.time)||void 0===c?void 0:c.name].active=!0,n[null===(d=t.time)||void 0===d?void 0:d.name].index=0));(null===(r=t.time)||void 0===r?void 0:r.name)&&(null===(a=t.body)||void 0===a?void 0:a.name)&&(n[null===(u=t.body)||void 0===u?void 0:u.name].type="BODY_FIELD",n[null===(p=t.time)||void 0===p?void 0:p.name].type="TIME_FIELD");t.extraFields.length&&t.extraFields.forEach((e=>{var t;(null===(t=e.config.links)||void 0===t?void 0:t.length)&&(n[e.name].type="LINK_FIELD")}))}(Object.keys(g).filter((e=>g[e].active)),m,g)),a().createElement("section",{className:s.section},a().createElement(H,{logsFrame:t,initialColumns:g,setUrlColumns:e.setUrlColumns,clearSelectedLine:e.clearSelectedLine},a().createElement(rt,{logsFrame:t,timeZone:o,height:n.height-50,width:n.width-25,labels:u})))},ut=(e,t)=>Math.ceil(100*e/t);function pt(e){let t=(0,i.guessFieldTypeFromValue)(e);return"string"===t&&ct.test(e)&&(t=i.FieldType.time),t}const gt=({dataFrame:e,setUrlColumns:t,urlColumns:n,addFilter:r,selectedLine:l,timeRange:s,panelWrap:o,clearSelectedLine:c})=>{if(!e)return null;const d=(null===(p=(u=e).meta)||void 0===p?void 0:p.type)===i.DataFrameType.LogLines?function(e){const t=new i.FieldCache(e),n=L(t,D,i.FieldType.time),r=L(t,N,i.FieldType.string);if(void 0===n||void 0===r)return null;var a;const l=null!==(a=L(t,"severity",i.FieldType.string))&&void 0!==a?a:null;var s;const o=null!==(s=L(t,"id",i.FieldType.string))&&void 0!==s?s:null;var c;const d=null!==(c=L(t,"labels",i.FieldType.other))&&void 0!==c?c:null,u=null===d?null:d.values,p=t.fields.filter(((e,t)=>t!==n.index&&t!==r.index&&t!==(null==l?void 0:l.index)&&t!==(null==o?void 0:o.index)&&t!==(null==d?void 0:d.index)));return{raw:e,timeField:n,bodyField:r,severityField:l,idField:o,getLogFrameLabels:()=>u,timeNanosecondField:null,getLogFrameLabelsAsLabels:()=>null!==u?u.map($):null,getLabelFieldName:()=>null!==d?d.name:null,extraFields:p}}(u):function(e){const t=new i.FieldCache(e),n=t.getFirstFieldOfType(i.FieldType.time),r=t.getFirstFieldOfType(i.FieldType.string);if(void 0===n||void 0===r)return null;var a;const l=null!==(a=t.getFieldByName("tsNs"))&&void 0!==a?a:null;var s;const o=null!==(s=t.getFieldByName("level"))&&void 0!==s?s:null;var c;const d=null!==(c=t.getFieldByName("id"))&&void 0!==c?c:null,[u,p]=function(e,t,n){const r=e.getFieldByName("labels");if(void 0!==r&&r.type===i.FieldType.other){const e=r.values.map($);return[r,()=>e]}return[null,()=>function(e,t){const n=e.labels;if(void 0!==n){const e=new Array(t);return e.fill(n),e}return null}(t,n.length)]}(t,r,e),g=t.fields.filter(((e,t)=>t!==n.index&&t!==r.index&&t!==(null==l?void 0:l.index)&&t!==(null==o?void 0:o.index)&&t!==(null==d?void 0:d.index)&&t!==(null==u?void 0:u.index)));return{timeField:n,bodyField:r,timeNanosecondField:l,severityField:o,idField:d,getLogFrameLabels:p,getLogFrameLabelsAsLabels:p,getLabelFieldName:()=>{var e;return null!==(e=null==u?void 0:u.name)&&void 0!==e?e:null},extraFields:g,raw:e}}(u);var u,p;return d?a().createElement(Te,{addFilter:r,selectedLine:l,timeRange:s,logsFrame:d},a().createElement(dt,{setUrlColumns:t,urlColumns:n,panelWrap:o,clearSelectedLine:c})):null};function mt(e){return a().createElement("div",{className:vt.visualisationType},a().createElement(s.RadioButtonGroup,{className:vt.visualisationTypeRadio,options:[{label:"Logs",value:"logs",description:"Show results in logs visualisation"},{label:"Table",value:"table",description:"Show results in table visualisation"}],size:"sm",value:e.vizType,onChange:e.onChange}))}const vt={visualisationType:(0,w.css)({display:"flex",flex:"1",justifyContent:"space-between",marginTop:"8px"}),visualisationTypeRadio:(0,w.css)({margin:"0 0 0 8px"})};var ht,ft,bt,yt=n(558),St=n(7097);class wt extends l.Bs{}bt=({model:e})=>{const t=Ot(),n=l.jh.getAncestor(e,Tt),{data:i}=l.jh.getData(e).useState(),{selectedLine:o,urlColumns:c,visualizationType:d}=n.useState(),u=l.jh.getTimeRange(e),{value:p}=u.useState(),m=Gr(i),v=(0,r.useRef)(null);return a().createElement("div",{className:t.panelWrapper,ref:v},a().createElement(s.PanelChrome,{loadingState:null==i?void 0:i.state,title:"Logs",actions:a().createElement(mt,{vizType:d,onChange:n.setVisualizationType})},m&&a().createElement(gt,{panelWrap:v,addFilter:t=>{const r=(0,St.OE)(m,t.key,e);(0,yt.XI)(t,n,r)},timeRange:p,selectedLine:o,urlColumns:null!=c?c:[],setUrlColumns:e=>{(0,g.B)(e,n.state.urlColumns)||n.setState({urlColumns:e})},dataFrame:m,clearSelectedLine:()=>{n.state.selectedLine&&n.clearSelectedLine()}})))},(ft="Component")in(ht=wt)?Object.defineProperty(ht,ft,{value:bt,enumerable:!0,configurable:!0,writable:!0}):ht[ft]=bt;const Ot=()=>({panelWrapper:(0,w.css)({height:"100%"})});function Et(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class xt extends l.Bs{constructor(e){var t,n;super((t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Et(e,t,n[t])}))}return e}({},e),n=null!=(n={wrapLines:Boolean((0,y.YM)("wrapLines"))})?n:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})),t)),Et(this,"handleWrapLinesChange",(e=>{const t=e.target.checked;this.setState({wrapLines:t}),(0,y.YK)("wrapLines",t),this.getParentScene().setLogsVizOption({wrapLogMessage:t})})),Et(this,"getParentScene",(()=>l.jh.getAncestor(this,Tt))),Et(this,"clearDisplayedFields",(()=>{this.getParentScene().clearDisplayedFields(),(0,b.EE)(b.NO.service_details,b.ir.service_details.logs_clear_displayed_fields)}))}}Et(xt,"Component",(function({model:e}){const{wrapLines:t}=e.useState(),{displayedFields:n}=e.getParentScene().useState();return a().createElement(a().Fragment,null,a().createElement(s.InlineField,{label:"Wrap lines",transparent:!0,htmlFor:"wrap-lines-switch"},a().createElement(s.InlineSwitch,{value:t,onChange:e.handleWrapLinesChange,className:Ct.horizontalInlineSwitch,transparent:!0,id:"wrap-lines-switch"})),n.length>0&&a().createElement(s.Tooltip,{content:`Clear displayed fields: ${n.join(", ")}`},a().createElement(s.Button,{variant:"secondary",fill:"outline",onClick:e.clearDisplayedFields},"Show original log line")))}));const Ct={input:(0,w.css)({width:"100%"}),field:(0,w.css)({label:"field",marginBottom:0}),horizontalInlineSwitch:(0,w.css)({padding:"0 4px 0 0"})},Pt=({onClick:e})=>{const[t,n]=(0,r.useState)(!1);(0,r.useEffect)((()=>{let e;return t&&(e=setTimeout((()=>{n(!1)}),2e3)),()=>{clearTimeout(e)}}),[t]);const i=(0,r.useCallback)(((t,r)=>{e(t,r),n(!0)}),[e]);return a().createElement(s.IconButton,{"aria-label":t?"Copied":"Copy link to log line",tooltip:t?"Copied":"Copy link to log line",tooltipPlacement:"top",variant:t?"primary":"secondary",size:"md",name:t?"check":"share-alt",onClick:i})};function jt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Ft extends l.Bs{onActivate(){this.state.body||this.setState({body:this.getLogsPanel()})}setLogsVizOption(e={}){this.state.body&&this.state.body.onOptionsChange(e)}getParentScene(){return l.jh.getAncestor(this,Tt)}getLogsPanel(){const e=this.getParentScene(),t=e.state.visualizationType;return l.d0.logs().setTitle("Logs").setOption("showTime",!0).setOption("onClickFilterLabel",this.handleLabelFilterClick).setOption("onClickFilterOutLabel",this.handleLabelFilterOutClick).setOption("isFilterLabelActive",this.handleIsFilterLabelActive).setOption("onClickFilterString",this.handleFilterStringClick).setOption("onClickShowField",this.onClickShowField).setOption("onClickHideField",this.onClickHideField).setOption("displayedFields",e.state.displayedFields).setOption("wrapLogMessage",Boolean((0,y.YM)("wrapLines"))).setOption("showLogContextToggle",!0).setOption("showLogContextToggle",!0).setOption("logRowMenuIconsAfter",[a().createElement(Pt,{onClick:this.handleShareLogLineClick,key:0})]).setHeaderActions(a().createElement(mt,{vizType:t,onChange:e.setVisualizationType})).build()}handleLabelFilter(e,t,n,r){const a=(0,St.OE)(n,e,this);(0,yt.Qt)(e,t,r,this,a),(0,b.EE)(b.NO.service_details,b.ir.service_details.logs_detail_filter_applied,{filterType:a,key:e,action:r})}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){jt(e,t,n[t])}))}return e}({},e)),jt(this,"onClickShowField",(e=>{const t=this.getParentScene();if(-1===t.state.displayedFields.indexOf(e)&&this.state.body){const n=[...t.state.displayedFields,e];this.setLogsVizOption({displayedFields:n}),t.setState({displayedFields:n}),(0,y.ZF)(this,t.state.displayedFields),(0,b.EE)(b.NO.service_details,b.ir.service_details.logs_toggle_displayed_field)}})),jt(this,"onClickHideField",(e=>{const t=this.getParentScene();if(t.state.displayedFields.indexOf(e)>=0&&this.state.body){const n=t.state.displayedFields.filter((t=>e!==t));this.setLogsVizOption({displayedFields:n}),t.setState({displayedFields:n}),(0,y.ZF)(this,t.state.displayedFields),(0,b.EE)(b.NO.service_details,b.ir.service_details.logs_toggle_displayed_field)}})),jt(this,"clearDisplayedFields",(()=>{this.state.body&&(this.setLogsVizOption({displayedFields:[]}),(0,y.ZF)(this,[]))})),jt(this,"handleShareLogLineClick",((e,t)=>{if((null==t?void 0:t.rowId)&&this.state.body){const n=this.getParentScene(),r=l.jh.getTimeRange(this.state.body),a=e.currentTarget instanceof HTMLButtonElement?e.currentTarget:void 0;(0,Ie.Dk)((0,Ie.gW)("panelState",{logs:{id:t.uid,displayedFields:n.state.displayedFields}},r.state.value),a)}})),jt(this,"handleLabelFilterClick",((e,t,n)=>{this.handleLabelFilter(e,t,n,"toggle")})),jt(this,"handleLabelFilterOutClick",((e,t,n)=>{this.handleLabelFilter(e,t,n,"exclude")})),jt(this,"handleIsFilterLabelActive",((e,t)=>{const n=(0,F.bY)(d.MB,this),r=(0,F.bY)(d.mB,this),a=(0,F.bY)(d._Y,this),i=(0,F.bY)(d._P,this),l=n=>n&&n.state.filters.findIndex((n=>"="===n.operator&&n.key===e&&n.value===t))>=0;return l(n)||(n=>{if(n){const r=n.state.filters.find((t=>"="===t.operator&&t.key===e));if(r)return(0,F.bu)(r,e).value===t}return!1})(r)||l(a)||l(i)})),jt(this,"handleFilterStringClick",(e=>{const t=l.jh.getAncestor(this,Tt).getLineFilterScene();t&&(t.updateFilter(e,!1),(0,b.EE)(b.NO.service_details,b.ir.service_details.logs_popover_line_filter,{selectionLength:e.length}))})),this.addActivationHandler(this.onActivate.bind(this))}}function kt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}jt(Ft,"Component",(({model:e})=>{const{body:t}=e.useState();return t?a().createElement(t.Component,{model:t}):a().createElement(s.LoadingPlaceholder,{text:"Loading..."})}));class Tt extends l.Bs{getUrlState(){var e;const t=null!==(e=this.state.urlColumns)&&void 0!==e?e:[],n=this.state.selectedLine,r=this.state.visualizationType;var a,i;const l=null!==(i=null!==(a=this.state.displayedFields)&&void 0!==a?a:(0,y.N$)(this))&&void 0!==i?i:[];return{urlColumns:JSON.stringify(t),selectedLine:JSON.stringify(n),visualizationType:JSON.stringify(r),displayedFields:JSON.stringify(l)}}updateFromUrl(e){const t={};try{if("string"==typeof e.urlColumns){const n=JSON.parse(e.urlColumns);n!==this.state.urlColumns&&(t.urlColumns=n)}if("string"==typeof e.selectedLine){const n=JSON.parse(e.selectedLine);n!==this.state.selectedLine&&(t.selectedLine=n)}if("string"==typeof e.visualizationType){const n=JSON.parse(e.visualizationType);n!==this.state.visualizationType&&(t.visualizationType=n)}if("string"==typeof e.displayedFields){const n=JSON.parse(e.displayedFields);n&&n.length&&(t.displayedFields=n)}}catch(e){B.v.error(e,{msg:"LogsListScene: updateFromUrl unexpected error"})}Object.keys(t).length&&this.setState(t)}clearSelectedLine(){this.setState({selectedLine:void 0})}onActivate(){const e=new URLSearchParams(h.locationService.getLocation().search);this.setStateFromUrl(e),this.state.panel||this.updateLogsPanel(),this._subs.add(this.subscribeToState(((e,t)=>{e.visualizationType!==t.visualizationType&&this.updateLogsPanel()})))}getLineFilterScene(){return this.lineFilterScene}setStateFromUrl(e){const t=e.get("selectedLine"),n=e.get("urlColumns"),r=e.get("visualizationType");var a;const i=null!==(a=e.get("displayedFields"))&&void 0!==a?a:JSON.stringify((0,y.N$)(this));this.updateFromUrl({selectedLine:t,urlColumns:n,vizType:r,displayedFields:i})}getVizPanel(){return this.lineFilterScene=new T,this.logsPanelScene=new Ft({}),new l.G1({direction:"column",children:"logs"===this.state.visualizationType?[new l.G1({children:[new l.vA({body:this.lineFilterScene,xSizing:"fill"}),new xt]}),new l.vA({height:"calc(100vh - 220px)",body:this.logsPanelScene})]:[new l.vA({body:this.lineFilterScene,xSizing:"fill"}),new l.vA({height:"calc(100vh - 220px)",body:new wt({})})]})}constructor(e){var t,n;super((t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){kt(e,t,n[t])}))}return e}({},e),n=null!=(n={visualizationType:(0,y.k5)(),displayedFields:[]})?n:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})),t)),kt(this,"_urlSync",new l.So(this,{keys:["urlColumns","selectedLine","visualizationType","displayedFields"]})),kt(this,"lineFilterScene",void 0),kt(this,"logsPanelScene",void 0),kt(this,"clearDisplayedFields",(()=>{this.setState({displayedFields:[]}),this.logsPanelScene&&this.logsPanelScene.clearDisplayedFields()})),kt(this,"setLogsVizOption",((e={})=>{this.logsPanelScene&&this.logsPanelScene.setLogsVizOption(e)})),kt(this,"updateLogsPanel",(()=>{this.setState({panel:this.getVizPanel()})})),kt(this,"setVisualizationType",(e=>{this.setState({visualizationType:e}),(0,b.EE)(b.NO.service_details,b.ir.service_details.logs_visualization_toggle,{visualisationType:e}),(0,y.o5)(e)})),this.addActivationHandler(this.onActivate.bind(this))}}kt(Tt,"Component",(({model:e})=>{const{panel:t}=e.useState();if(t)return a().createElement("div",{className:_t.panelWrapper},a().createElement(t.Component,{model:t}))}));const _t={panelWrapper:(0,w.css)({".show-on-hover":{display:"none"},'section > div[class$="panel-content"]':(0,w.css)({contain:"none",overflow:"auto"})})};var Lt=n(5722),Dt=n(1383);function Nt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class $t extends i.BusEventBase{constructor(e,t,n){super(),Nt(this,"target",void 0),Nt(this,"sortBy",void 0),Nt(this,"direction",void 0),this.target=e,this.sortBy=t,this.direction=n}}Nt($t,"type","sort-criteria-changed");class It extends l.Bs{constructor(e){const{sortBy:t,direction:n}=(0,y.vs)(e.target,Lt.DEFAULT_SORT_BY,"desc");super({target:e.target,sortBy:t,direction:n}),Nt(this,"sortingOptions",[{label:"",options:[{value:"changepoint",label:"Most relevant",description:"Smart ordering of graphs based on the most significant spikes in the data"},{value:"outliers",label:"Outlying values",description:"Order by the amount of outlying values in the data"},{value:i.ReducerID.stdDev,label:"Widest spread",description:"Sort graphs by deviation from the average value"},{value:"alphabetical",label:"Name",description:"Alphabetical order"},{value:i.ReducerID.sum,label:"Count",description:"Sort graphs by total number of logs"},{value:i.ReducerID.max,label:"Highest spike",description:"Sort graphs by the highest values (max)"},{value:i.ReducerID.min,label:"Lowest dip",description:"Sort graphs by the smallest values (min)"}]},{label:"Percentiles",options:[...i.fieldReducers.selectOptions([],Bt).options]}]),Nt(this,"onCriteriaChange",(e=>{e.value&&(this.setState({sortBy:e.value}),(0,y.fq)(this.state.target,e.value,this.state.direction),this.publishEvent(new $t(this.state.target,e.value,this.state.direction),!0))})),Nt(this,"onDirectionChange",(e=>{e.value&&(this.setState({direction:e.value}),(0,y.fq)(this.state.target,this.state.sortBy,e.value),this.publishEvent(new $t(this.state.target,this.state.sortBy,e.value),!0))}))}}Nt(It,"Component",(({model:e})=>{const{sortBy:t,direction:n}=e.useState(),r=e.sortingOptions.find((e=>e.options.find((e=>e.value===t)))),i=null==r?void 0:r.options.find((e=>e.value===t));return a().createElement(a().Fragment,null,a().createElement(s.InlineField,null,a().createElement(s.Select,{"data-testid":f.b.breakdowns.common.sortByDirection,onChange:e.onDirectionChange,"aria-label":"Sort direction",placeholder:"",value:n,options:[{label:"Asc",value:"asc"},{label:"Desc",value:"desc"}]})),a().createElement(s.InlineField,{label:"Sort by",htmlFor:"sort-by-criteria",tooltip:"Calculate a derived quantity from the values in your time series and sort by this criteria. Defaults to standard deviation."},a().createElement(s.Select,{"data-testid":f.b.breakdowns.common.sortByFunction,value:i,width:20,isSearchable:!0,options:e.sortingOptions,placeholder:"Choose criteria",onChange:e.onCriteriaChange,inputId:"sort-by-criteria"})))}));const At=["p10","p25","p75","p90","p99"];function Bt(e){return e.id>="p1"&&e.id<="p99"&&At.includes(e.id)}function Vt(e){var t;return null!==(t=(0,Dt.H7)(e))&&void 0!==t?t:"No labels"}var Mt,Rt,Wt=n(5431),zt=n(4482),Ht=n(7841);function Gt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class qt extends l.Bs{Selector({model:e}){const{active:t,options:n}=e.useState();return a().createElement(s.Field,null,a().createElement(s.RadioButtonGroup,{options:n,value:t,onChange:e.onLayoutChange}))}constructor(...e){super(...e),Gt(this,"onLayoutChange",(e=>{(0,b.EE)(b.NO.service_details,b.ir.service_details.layout_type_changed,{layout:e,view:(0,v.FT)()}),this.setState({active:e})}))}}function Qt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}Gt(qt,"Component",(({model:e})=>{const{layouts:t,options:n,active:r}=e.useState(),i=n.findIndex((e=>e.value===r));if(-1===i)return null;const l=t[i];return a().createElement(l.Component,{model:l})})),function(e){e.ns="ns",e.us="µs",e.ms="ms",e.s="s",e.m="m",e.h="h"}(Mt||(Mt={})),function(e){e.B="B",e.KB="KB",e.MB="MB",e.GB="GB",e.TB="TB"}(Rt||(Rt={}));class Ut extends l.Bs{onActivate(){const e=(0,F.bY)((0,yt.ts)(this.state.labelName,this.state.variableType),this).state.filters.filter((e=>e.key===this.state.labelName)),t=e.find((e=>e.operator===Le.w.gte||e.operator===Le.w.gt)),n=e.find((e=>e.operator===Le.w.lte||e.operator===Le.w.lt));let r={};if("duration"===this.state.fieldType||"bytes"===this.state.fieldType){if(t){const e=Jt((0,F.bu)(t).value,this.state.fieldType);e&&(r.gt=e.value,r.gtu=e.unit,r.gte=t.operator===Le.w.gte)}if(n){const e=Jt((0,F.bu)(n).value,this.state.fieldType);e&&(r.lt=e.value,r.ltu=e.unit,r.lte=n.operator===Le.w.lte)}}else{if(t){const e=(0,F.bu)(t).value;r.gt=Number(e),r.gtu="",r.gte=t.operator===Le.w.gte}if(n){const e=(0,F.bu)(n).value;r.lt=Number(e),r.ltu="",r.lte=n.operator===Le.w.lte}}0!==Object.keys(r).length&&(r.hasExistingFilter=!0),this.setState(r)}onSubmit(){this.state.gt?(0,yt.vn)(this.state.labelName,this.state.gt.toString()+this.state.gtu,this.state.gte?Le.w.gte:Le.w.gt,this,this.state.variableType):(0,yt.VT)(this.state.labelName,this,this.state.gte?Le.w.gte:Le.w.gt,this.state.variableType),this.state.lt?(0,yt.vn)(this.state.labelName,this.state.lt.toString()+this.state.ltu,this.state.lte?Le.w.lte:Le.w.lt,this,this.state.variableType):(0,yt.VT)(this.state.labelName,this,this.state.lte?Le.w.lte:Le.w.lt,this.state.variableType),l.jh.getAncestor(this,an).togglePopover()}constructor(e){let t;const n=e.fieldType;if("bytes"===n)t={ltu:"B",gtu:"B"};else if("duration"===n)t={ltu:"s",gtu:"s"};else{if("float"!==n)throw new Error(`field type incorrectly defined: ${n}`);t={ltu:"",gtu:""}}super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Qt(e,t,n[t])}))}return e}({},e,t)),Qt(this,"onInputKeydown",(e=>{const t=void 0===this.state.gt&&void 0===this.state.lt;"Enter"!==e.key||t||this.onSubmit()})),this.addActivationHandler(this.onActivate.bind(this))}}function Jt(e,t){if("duration"===t){const t=Object.values(Mt).find((t=>{const n=t.length;return e.slice(-1*n)===t}));if(t){const n=Number(e.replace(t,""));if(!isNaN(n))return{unit:t,value:n}}}if("bytes"===t){const t=Object.values(Rt).sort(((e,t)=>t.length-e.length)).find((t=>{const n=t.length;return e.slice(-1*n)===t}));if(t){const n=Number(e.replace(t,""));if(!isNaN(n))return{unit:t,value:n}}}}function Yt(e){if("duration"===e)return Object.keys(Mt).map((e=>({text:e,value:Mt[e],label:e})));if("bytes"===e)return Object.keys(Rt).map((e=>({text:e,value:Rt[e],label:e})));const t=new Error(`invalid field type: ${e}`);throw B.v.error(t),t}Qt(Ut,"Component",(({model:e})=>{const t=(0,s.useStyles2)(Kt),{labelName:n,gt:r,lt:i,gte:o,lte:c,gtu:d,ltu:u,fieldType:p,hasExistingFilter:g}=e.useState(),m="float"!==p&&p!==n?`(${p})`:void 0,v=l.jh.getAncestor(e,an),h=void 0===r&&void 0===i;return a().createElement(s.ClickOutsideWrapper,{useCapture:!0,onClick:()=>v.togglePopover()},a().createElement(s.Stack,{direction:"column",gap:0,role:"tooltip"},a().createElement("div",{className:t.card.body},a().createElement("div",{className:t.card.title},n," ",m),a().createElement("div",{className:t.card.fieldWrap},a().createElement(s.FieldSet,{className:t.card.fieldset},a().createElement(s.Field,{"data-testid":f.b.breakdowns.common.filterNumericPopover.inputGreaterThanInclusive,horizontal:!0,className:(0,w.cx)(t.card.field,t.card.inclusiveField)},a().createElement(s.Select,{className:t.card.inclusiveInput,menuShouldPortal:!1,value:void 0!==o?o.toString():"false",options:[{label:"Greater than",value:"false"},{label:"Greater than or equal",value:"true"}],onChange:t=>e.setState({gte:"true"===t.value})})),a().createElement(s.Field,{"data-testid":f.b.breakdowns.common.filterNumericPopover.inputGreaterThan,horizontal:!0,className:t.card.field},a().createElement(s.Input,{onKeyDownCapture:e.onInputKeydown,autoFocus:!0,onChange:t=>{e.setState({gt:""!==t.currentTarget.value?Number(t.currentTarget.value):void 0})},className:t.card.numberInput,value:r,type:"number"})),"float"!==p&&a().createElement(s.Label,null,a().createElement(s.Field,{"data-testid":f.b.breakdowns.common.filterNumericPopover.inputGreaterThanUnit,horizontal:!0,className:t.card.field,label:a().createElement("span",{className:t.card.unitFieldLabel},"Unit")},a().createElement(s.Select,{onChange:t=>{e.setState({gtu:t.value})},menuShouldPortal:!1,options:Yt(p),className:t.card.selectInput,value:d})))),a().createElement(s.FieldSet,{className:t.card.fieldset},a().createElement(s.Field,{"data-testid":f.b.breakdowns.common.filterNumericPopover.inputLessThanInclusive,horizontal:!0,className:(0,w.cx)(t.card.field,t.card.inclusiveField)},a().createElement(s.Select,{className:t.card.inclusiveInput,menuShouldPortal:!1,value:void 0!==c?c.toString():"false",options:[{label:"Less than",value:"false"},{label:"Less than or equal",value:"true"}],onChange:t=>e.setState({lte:"true"===t.value})})),a().createElement(s.Field,{"data-testid":f.b.breakdowns.common.filterNumericPopover.inputLessThan,horizontal:!0,className:t.card.field},a().createElement(s.Input,{onKeyDownCapture:e.onInputKeydown,onChange:t=>e.setState({lt:""!==t.currentTarget.value?Number(t.currentTarget.value):void 0}),className:t.card.numberInput,value:i,type:"number"})),"float"!==p&&a().createElement(s.Label,null,a().createElement(s.Field,{"data-testid":f.b.breakdowns.common.filterNumericPopover.inputLessThanUnit,horizontal:!0,className:t.card.field,label:a().createElement("span",{className:t.card.unitFieldLabel},"Unit")},a().createElement(s.Select,{onChange:t=>{e.setState({ltu:t.value})},menuShouldPortal:!1,options:Yt(p),className:t.card.selectInput,value:u}))))),a().createElement("div",{className:t.card.buttons},g&&a().createElement(s.Button,{"data-testid":f.b.breakdowns.common.filterNumericPopover.removeButton,disabled:!g,onClick:()=>{e.setState({gt:void 0,lt:void 0}),e.onSubmit()},size:"sm",variant:"destructive",fill:"outline"},"Remove"),a().createElement(s.Button,{"data-testid":f.b.breakdowns.common.filterNumericPopover.submitButton,disabled:h,onClick:()=>e.onSubmit(),size:"sm",variant:"primary",fill:"outline",type:"submit"},"Add"),a().createElement(s.Button,{"data-testid":f.b.breakdowns.common.filterNumericPopover.cancelButton,onClick:()=>v.togglePopover(),size:"sm",variant:"secondary",fill:"outline"},"Cancel")))))}));const Kt=e=>({card:{buttons:(0,w.css)({display:"flex",flexWrap:"wrap",justifyContent:"flex-end",gap:e.spacing(1.5),marginTop:e.spacing(1)}),inclusiveInput:(0,w.css)({minWidth:"185px"}),selectInput:(0,w.css)({minWidth:"65px"}),numberInput:(0,w.css)({width:"75px"}),fieldWrap:(0,w.css)({display:"flex",flexDirection:"column",paddingTop:e.spacing(2),paddingBottom:0}),field:(0,w.css)({display:"flex",alignItems:"center",marginBottom:e.spacing(1)}),inclusiveField:(0,w.css)({marginRight:e.spacing(1)}),unitFieldLabel:(0,w.css)({marginLeft:e.spacing(2),marginRight:e.spacing(1.5)}),numberFieldLabel:(0,w.css)({width:"100px"}),switchFieldLabel:(0,w.css)({marginLeft:e.spacing(2),marginRight:e.spacing(1)}),fieldset:(0,w.css)({display:"flex",alignItems:"center",justifyContent:"space-between",width:"100%",marginBottom:0}),title:(0,w.css)({}),body:(0,w.css)({padding:e.spacing(2)}),p:(0,w.css)({maxWidth:300})}});function Xt(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Zt(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Xt(e,t,n[t])}))}return e}function en(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const tn="Include",nn="Exclude",rn="Add to filter";class an extends l.Bs{onChange(e){const t=this.getVariable(),n=t.state.name,r=this.getExistingFilter(t),a=(0,F.z2)(t,r);(null==r?void 0:r.operator)===Le.w.NotEqual&&a.value===d.ZO&&e.value===tn?this.clearFilter(n):e.value===tn?this.onClickExcludeEmpty(n):e.value===nn?this.onClickIncludeEmpty(n):e.value===rn&&this.onClickNumericFilter(n),this.setState({selectedValue:e})}getExistingFilter(e){let{labelName:t}=(0,v.W6)();if(this.state.labelName!==t)return null==e?void 0:e.state.filters.find((e=>e.key===this.state.labelName))}onActivate(){var e,t;const n=l.jh.getAncestor(this,Kr);(null===(t=n.state.$data)||void 0===t||null===(e=t.state.data)||void 0===e?void 0:e.state)===i.LoadingState.Done&&this.calculateSparsity(),this._subs.add(l.jh.getData(this).subscribeToState((e=>{var t,r,a,l;(null===(t=e.data)||void 0===t?void 0:t.state)===i.LoadingState.Done&&((null===(a=n.state.$data)||void 0===a||null===(r=a.state.data)||void 0===r?void 0:r.state)===i.LoadingState.Done&&this.calculateSparsity(),this._subs.add(null===(l=n.state.$data)||void 0===l?void 0:l.subscribeToState((e=>{var t;(null===(t=e.data)||void 0===t?void 0:t.state)===i.LoadingState.Done&&this.calculateSparsity()}))))})))}togglePopover(){this.setState({showPopover:!this.state.showPopover})}calculateSparsity(){var e;const t=Gr(null===(e=l.jh.getAncestor(this,Kr).state.$data)||void 0===e?void 0:e.state.data),n=null==t?void 0:t.fields.find((e=>"labels"===e.name)),r=l.jh.getData(this),a=l.jh.findObject(r,(e=>e instanceof l.dt));if(a){const e=a.state.queries[0];(null==e?void 0:e.expr.includes("avg_over_time"))&&this.setState({hasNumericFilters:!0})}if(!n||!t)return void this.setState({hasSparseFilters:!1});const i=this.getVariable(),s=n.values.reduce(((e,t)=>((null==t?void 0:t[this.state.labelName])&&e++,e)),0),o=l.jh.getAncestor(this,l.Eb);if(void 0!==s&&t.length>0){const e=(s/t.length*100).toLocaleString(),n=`${this.state.labelName} exists on ${e}% of ${t.length} sampled log lines`;o.setState({description:n})}else o.setState({description:void 0});const c=this.getExistingFilter(i),u=c&&i.state.name===d.mB?(0,F.bu)(c):void 0;s<t.length||(null==u?void 0:u.value)===d.ZO?this.setState({hasSparseFilters:!0}):this.setState({hasSparseFilters:!1})}getVariable(){return this.state.fieldType===v._J.field?(0,F.ir)(this):this.state.labelName===d.e4?(0,F.iw)(this):(0,F.cR)(this)}constructor(e){super(en(Zt({},e),{showPopover:!1})),Xt(this,"onClickNumericFilter",(e=>{const t=Qr(this),n=(0,St.ph)(this.state.labelName,t);if(!n||"string"===n||"boolean"===n||"int"===n){const e=new Error(`Incorrect field type: ${n}`);throw B.v.error(e),e}this.setState({popover:new Ut({labelName:this.state.labelName,variableType:e,fieldType:n})}),this.togglePopover()})),Xt(this,"onClickViewValues",(()=>{const e=l.jh.getAncestor(this,Kr);(0,p.fg)(this.state.fieldType,this.state.labelName,e)})),Xt(this,"onClickExcludeEmpty",(e=>{(0,yt.Qt)(this.state.labelName,d.ZO,"exclude",this,e)})),Xt(this,"onClickIncludeEmpty",(e=>{(0,yt.Qt)(this.state.labelName,d.ZO,"include",this,e)})),Xt(this,"clearFilter",(e=>{(0,yt.Qt)(this.state.labelName,d.ZO,"clear",this,e)})),Xt(this,"clearFilters",(e=>{(0,yt.hi)(this.state.labelName,this,e)})),this.addActivationHandler(this.onActivate.bind(this))}}function ln(e){const t=(0,s.useStyles2)(sn);return a().createElement("span",{className:t.description},e.selected&&a().createElement("span",{className:t.selected}),e.text)}Xt(an,"Component",(({model:e})=>{const{hideValueDrilldown:t,labelName:n,hasSparseFilters:i,hasNumericFilters:l,selectedValue:o,popover:c,showPopover:u,fieldType:p}=e.useState(),g=e.getVariable(),m=g.useState().name,h=e.getExistingFilter(g),b=(0,F.z2)(g,h),y=(0,s.useStyles2)(on),S=(0,r.useRef)(null),w=p===v._J.label&&0===g.state.filters.filter((e=>e.key!==n&&e.operator===Le.w.Equal)).length,E=(null==h?void 0:h.operator)===Le.w.NotEqual&&b.value===d.ZO,x=!!h;var C;const P=null!==(C=null==o?void 0:o.value)&&void 0!==C?C:E?tn:l?rn:tn,j=!!(null==h?void 0:h.operator)&&[Le.w.gte,Le.w.gt,Le.w.lte,Le.w.lt].includes(h.operator),k=P===rn||j,T=P===tn&&!k,_={value:tn,component:()=>a().createElement(ln,{selected:T,text:`Include all log lines with ${n}`})},L={value:nn,component:()=>a().createElement(ln,{selected:!1,text:`Exclude all log lines with ${n}`})},D={value:rn,component:()=>a().createElement(ln,{selected:k,text:`Add an expression, i.e. ${n} > 30`})},N=[];l&&N.push(D),i&&(j||N.push(_),N.push(L));const $=E?_:l?D:_;var I;return a().createElement(a().Fragment,null,x&&a().createElement(s.IconButton,{disabled:w,name:"filter",tooltip:`Clear ${n} filters`,onClick:()=>e.clearFilters(m)}),(l||i)&&a().createElement(a().Fragment,null,a().createElement(s.ButtonGroup,{"data-testid":f.b.breakdowns.common.filterButtonGroup},a().createElement(s.Button,{"data-testid":f.b.breakdowns.common.filterButton,ref:S,onClick:()=>e.onChange(null!=o?o:$),size:"sm",fill:"outline",variant:"secondary"},null!==(I=null==o?void 0:o.value)&&void 0!==I?I:$.value),a().createElement(s.ButtonSelect,{"data-testid":f.b.breakdowns.common.filterSelect,className:y.buttonSelect,variant:"default",options:N,onChange:t=>{e.onChange(t)}}))),!0!==t&&a().createElement(s.Button,{title:`View breakdown of values for ${n}`,variant:"primary",fill:"outline",size:"sm",onClick:e.onClickViewValues,"aria-label":`Select ${n}`},"Select"),c&&a().createElement(s.PopoverController,{content:a().createElement(c.Component,{model:c})},((e,t,n)=>{const r={onBlur:t,onFocus:e};return a().createElement(a().Fragment,null,S.current&&a().createElement(a().Fragment,null,a().createElement(s.Popover,Zt(en(Zt({},n,O.rest),{show:u,wrapperClassName:y.popover,referenceElement:S.current,renderArrow:!0}),r))))})))}));const sn=e=>({selected:(0,w.css)({label:"selectable-value-selected","&:before":{content:'""',position:"absolute",left:0,top:"4px",height:"calc(100% - 8px)",width:"2px",backgroundColor:e.colors.warning.main}}),description:(0,w.css)({textAlign:"left",fontSize:e.typography.pxToRem(12)})}),on=e=>({popover:(0,w.css)({borderRadius:e.shape.radius.default,boxShadow:e.shadows.z3,background:e.colors.background.primary,border:`1px solid ${e.colors.border.weak}`}),description:(0,w.css)({textAlign:"left",fontSize:e.typography.pxToRem(12)}),buttonSelect:(0,w.css)({border:`1px solid ${e.colors.border.strong}`,borderLeft:"none",borderTopLeftRadius:0,borderBottomLeftRadius:0,padding:1,height:"24px"})});class cn extends l.Bs{onActivate(){const e=l.jh.getAncestor(this,l.Eb);this._subs.add(e.subscribeToState(((e,t)=>{var n;const r=l.jh.getData(this);var a;(null===(n=r.state.data)||void 0===n?void 0:n.state)===i.LoadingState.Done&&this.setState({currentSeriesCount:null===(a=r.state.data)||void 0===a?void 0:a.series.length})})))}constructor(e){super(e),this.addActivationHandler(this.onActivate.bind(this))}}function dn(e){var t;const n=e.state.body,r=null===(t=e.state.body)||void 0===t?void 0:t.state.$data;r instanceof l.Es&&(null==n||n.setState({titleItems:[new cn({showAllSeries:!1,toggleShowAllSeries:e=>{r.setState({transformations:[]}),e.setState({showAllSeries:!0}),r.reprocessTransformations()}})]}))}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(cn,"Component",(({model:e})=>{const{toggleShowAllSeries:t,showAllSeries:n,currentSeriesCount:r}=e.useState(),o=l.jh.getData(e),{data:c}=o.useState(),d=(0,s.useStyles2)(un);if(!(o instanceof l.Es)||n||(null==c?void 0:c.state)!==i.LoadingState.Done||!r||c.series.length<20)return null;const u=o._prevDataFromSource,p=null==u?void 0:u.series.length;return a().createElement("div",{key:"disclaimer",className:d.timeSeriesDisclaimer},a().createElement("span",{className:d.warningMessage},a().createElement(a().Fragment,null,a().createElement(s.Icon,{title:"Showing only 20 series",name:"exclamation-triangle","aria-hidden":"true"}))),a().createElement(s.Tooltip,{content:"Rendering too many series in a single panel may impact performance and make data harder to read. Consider adding more filters."},a().createElement(s.Button,{variant:"secondary",size:"sm",onClick:()=>t(e)},a().createElement(a().Fragment,null,"Show all ",p))))}));const un=e=>({timeSeriesDisclaimer:(0,w.css)({label:"time-series-disclaimer",display:"flex",alignItems:"center",gap:e.spacing(1)}),warningMessage:(0,w.css)({display:"flex",alignItems:"center",gap:e.spacing(.5),color:e.colors.warning.main,fontSize:e.typography.bodySmall.fontSize})});var pn=n(1194);function gn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class mn extends l.Bs{updateChildren(e,t=void 0){var n;const r=Ur(e),a=Jr(e),i=Yr(e),s=this.calculateCardinalityMap(e);null===(n=this.state.body)||void 0===n||n.state.layouts.forEach((e=>{if(e instanceof l.gF){const n=new Set(null==a?void 0:a.values),o=e.state.children;for(let s=0;s<o.length;s++){const c=e.state.children[s];if(c instanceof l.xK){const e=c.state.body;if(e instanceof l.Eb){if(t){const n=null==a?void 0:a.values.indexOf(e.state.title);if((n&&-1!==n?null==i?void 0:i.values[n]:void 0)!==t){const t=(0,St.ph)(e.state.title,r),n=this.getDataTransformerForPanel(e.state.title,r,t);e.setState({$data:n})}}n.has(e.state.title)?n.delete(e.state.title):(o.splice(s,1),s--)}else B.v.warn("panel is not VizPanel!")}else B.v.warn("gridItem is not SceneCSSGridItem")}const c=Array.from(n).map((e=>({label:e,value:e})));o.push(...this.buildChildren(c)),o.sort(this.sortChildren(s)),o.map((e=>{dn(e),this.subscribeToPanel(e)})),e.setState({children:o})}else B.v.warn("Layout is not SceneCSSGridLayout")}))}sortChildren(e){return(t,n)=>{const r=t.state.body,a=n.state.body;var i;const l=null!==(i=e.get(r.state.title))&&void 0!==i?i:0;var s;return(null!==(s=e.get(a.state.title))&&void 0!==s?s:0)-l}}calculateCardinalityMap(e){const t=Ur(e),n=new Map;if(null==t?void 0:t.length)for(let e=0;e<(null==t?void 0:t.length);e++){const r=t.fields[0].values[e],a=t.fields[1].values[e];n.set(r,a)}return n}onActivate(){var e;this.setState({body:this.build()});const t=l.jh.getAncestor(this,Kr);void 0===t.state.fieldsCount&&this.updateFieldCount(),this._subs.add(null===(e=t.state.$detectedFieldsData)||void 0===e?void 0:e.subscribeToState(this.onDetectedFieldsChange)),this._subs.add(this.subscribeToFieldsVar())}subscribeToFieldsVar(){return(0,F.ir)(this).subscribeToState(((e,t)=>{const n=l.jh.getAncestor(this,Kr),r=e.filters.map((e=>(0,F.bu)(e).parser)),a=t.filters.map((e=>(0,F.bu)(e).parser)),i=(0,St.Qg)(r);if(i!==(0,St.Qg)(a)){var s;const e=null===(s=n.state.$detectedFieldsData)||void 0===s?void 0:s.state;e&&this.updateChildren(e,i)}}))}build(){var e;const t=(0,F.Hj)(this).state.options.map((e=>({label:e.label,value:String(e.value)})));l.jh.getAncestor(this,xn).state.search.reset();const n=this.buildChildren(t),r=l.jh.getAncestor(this,Kr),a=this.calculateCardinalityMap(null===(e=r.state.$detectedFieldsData)||void 0===e?void 0:e.state);n.sort(this.sortChildren(a));const i=n.map((e=>e.clone()));return[...n,...i].map((e=>{dn(e),this.subscribeToPanel(e)})),new qt({options:[{value:"grid",label:"Grid"},{value:"rows",label:"Rows"}],active:"grid",layouts:[new l.gF({templateColumns:En,autoRows:"200px",children:n,isLazy:!0}),new l.gF({templateColumns:"1fr",autoRows:"200px",children:i,isLazy:!0})]})}subscribeToPanel(e){const t=e.state.body;var n;t&&this._subs.add(null==t||null===(n=t.state.$data)||void 0===n?void 0:n.getResultsStream().subscribe((t=>{t.data.errors&&t.data.errors.length>0&&(e.setState({isHidden:!0}),this.updateFieldCount())})))}buildChildren(e){const t=[],n=Qr(this);for(const r of e){const{value:e}=r;if(e===d.To||!e)continue;const a=(0,St.ph)(e,n),i=this.getDataTransformerForPanel(e,n,a);let c=l.d0.timeseries().setTitle(e).setData(i);const u=[];(0,St.JI)(a)?u.push(new an({labelName:String(e),hideValueDrilldown:!0,fieldType:v._J.field})):(c=c.setCustomFieldConfig("stacking",{mode:s.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("drawStyle",s.DrawStyle.Bars).setOverrides(o.jC),u.push(new an({labelName:String(e),fieldType:v._J.field}))),c.setHeaderActions([...u,new pn.g({labelName:e})]);const p=c.build(),g=new l.xK({body:p});t.push(g)}return t}getDataTransformerForPanel(e,t,n){const r=(0,F.ir)(this),a=(0,St.Jl)(e,r,t),i=(0,c.l)(a,{legendFormat:(0,St.JI)(n)?e:`{{${e}}}`,refId:e}),s=(0,o.rS)([i]);return new l.Es({$data:s,transformations:[()=>vn(20)]})}updateFieldCount(){var e,t,n,r,a;const i=null!==(a=null===(e=this.state.body)||void 0===e?void 0:e.state.layouts.find((e=>e.isActive)))&&void 0!==a?a:null===(t=this.state.body)||void 0===t?void 0:t.state.layouts[0],s=null==i?void 0:i.state.children,o=null==s?void 0:s.filter((e=>!e.state.isHidden));var c;null===(n=(r=l.jh.getAncestor(this,xn).state).changeFieldCount)||void 0===n||n.call(r,null!==(c=null==o?void 0:o.length)&&void 0!==c?c:0)}static Selector({model:e}){const{body:t}=e.useState();return a().createElement(a().Fragment,null,t&&a().createElement(t.Selector,{model:t}))}constructor(e){super(e),gn(this,"onDetectedFieldsChange",(e=>{var t;(null===(t=e.data)||void 0===t?void 0:t.state)===i.LoadingState.Done&&this.updateChildren(e)})),this.addActivationHandler(this.onActivate.bind(this))}}function vn(e){return t=>t.pipe((0,U.map)((t=>t.slice(0,e))))}gn(mn,"Component",(({model:e})=>{const{body:t}=e.useState();return t?a().createElement(a().Fragment,null,t&&a().createElement(t.Component,{model:t})):a().createElement(s.LoadingPlaceholder,{text:"Loading..."})}));var hn=n(4462);class fn extends l.Bs{static Selector({model:e}){const{body:t}=e.useState();return t instanceof qt?a().createElement(a().Fragment,null,t&&a().createElement(t.Selector,{model:t})):a().createElement(a().Fragment,null)}onActivate(){var e;const t=(0,F.Hj)(this),n=String(t.state.value),r=(0,F.ir)(this),a=Qr(this),i=(0,St.Jl)(n,r,a),l=(0,c.l)(i,{legendFormat:`{{${n}}}`,refId:n});this.setState({body:this.build(l),$data:(0,o.rS)([l])}),this._subs.add(this.subscribeToEvent(yt.Of,(e=>{this.setState({lastFilterEvent:e})}))),this._subs.add(null===(e=this.state.$data)||void 0===e?void 0:e.subscribeToState((e=>{this.onValuesDataQueryChange(e,l)})))}onValuesDataQueryChange(e,t){var n,r;if((null===(n=e.data)||void 0===n?void 0:n.state)===i.LoadingState.Done){var a;const n=this.state.lastFilterEvent;(null===(a=e.data)||void 0===a?void 0:a.state)===i.LoadingState.Done&&n&&("exclude"===n.operator&&e.data.series.length<1&&this.navigateToFields(),"include"===n.operator&&e.data.series.length<=1&&this.navigateToFields()),this.state.body instanceof l.dM&&this.setState({body:this.build(t)})}(null===(r=e.data)||void 0===r?void 0:r.state)===i.LoadingState.Error&&this.setErrorState(e.data.errors)}setErrorState(e){this.setState({body:new l.dM({reactNode:a().createElement(s.Alert,{title:"Something went wrong with your request",severity:"error"},null==e?void 0:e.map(((e,t)=>a().createElement("div",{key:t},e.status&&a().createElement(a().Fragment,null,a().createElement("strong",null,"Status"),": ",e.status," ",a().createElement("br",null)),e.message&&a().createElement(a().Fragment,null,a().createElement("strong",null,"Message"),": ",e.message," ",a().createElement("br",null)),e.traceId&&a().createElement(a().Fragment,null,a().createElement("strong",null,"TraceId"),": ",e.traceId)))))})})}navigateToFields(){this.setState({lastFilterEvent:void 0}),(0,p.Vt)(v.G3.fields,l.jh.getAncestor(this,Kr))}build(e){const t=(0,F.Hj)(this),n=String(t.state.value),{sortBy:r,direction:i}=(0,y.vs)("fields",Lt.DEFAULT_SORT_BY,"desc"),o=l.jh.getAncestor(this,xn),c=()=>{var e;return null!==(e=o.state.search.state.filter)&&void 0!==e?e:""},u=(0,St.Ri)(n,this);return new qt({options:[{value:"single",label:"Single"},{value:"grid",label:"Grid"},{value:"rows",label:"Rows"}],active:"grid",layouts:[new l.G1({direction:"column",children:[new l.vA({minHeight:300,body:l.d0.timeseries().setTitle(n).build()})]}),new Bn({body:new l.gF({templateColumns:En,autoRows:"200px",children:[new l.vA({body:new l.dM({reactNode:a().createElement(s.LoadingPlaceholder,{text:"Loading..."})})})],isLazy:!0}),getLayoutChild:(0,St.Zp)(Vt,(null==e?void 0:e.expr.includes("count_over_time"))?s.DrawStyle.Bars:s.DrawStyle.Line,"structuredMetadata"===u?d._P:d.mB,l.jh.getAncestor(this,xn).state.sort,n),sortBy:r,direction:i,getFilter:c}),new Bn({body:new l.gF({templateColumns:"1fr",autoRows:"200px",children:[new l.vA({body:new l.dM({reactNode:a().createElement(s.LoadingPlaceholder,{text:"Loading..."})})})],isLazy:!0}),getLayoutChild:(0,St.Zp)(Vt,(null==e?void 0:e.expr.includes("count_over_time"))?s.DrawStyle.Bars:s.DrawStyle.Line,"structuredMetadata"===u?d._P:d.mB,l.jh.getAncestor(this,xn).state.sort,n),sortBy:r,direction:i,getFilter:c})]})}constructor(e){super(e),this.addActivationHandler(this.onActivate.bind(this))}}function bn({blockingMessage:e,isLoading:t,children:n}){const r=(0,s.useStyles2)(yn);return t&&!e&&(e="Loading..."),t?a().createElement(s.LoadingPlaceholder,{className:r.statusMessage,text:e}):e?a().createElement("div",{className:r.statusMessage},e):a().createElement(a().Fragment,null,n)}function yn(e){return{statusMessage:(0,w.css)({fontStyle:"italic",marginTop:e.spacing(7),textAlign:"center"})}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(fn,"Component",(({model:e})=>{const{body:t}=e.useState();return t?a().createElement(a().Fragment,null,t&&a().createElement(t.Component,{model:t})):a().createElement(s.LoadingPlaceholder,{text:"Loading..."})}));var Sn=n(6001);class wn extends l.Bs{static Component({model:e}){const{type:t}=e.useState();return a().createElement(zt.R,null,a().createElement(s.Alert,{title:"",severity:"warning"},"We did not find any ",t," for the given timerange. Please"," ",a().createElement("a",{className:Cn.link,href:"https://forms.gle/1sYWCTPvD72T1dPH9",target:"_blank",rel:"noopener noreferrer"},"let us know")," ","if you think this is a mistake."))}}function On(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const En="repeat(auto-fit, minmax(400px, 1fr))";class xn extends l.Bs{onActivate(){var e,t,n;const r=(0,F.Hj)(this),a=l.jh.getAncestor(this,Kr);this.setState({loading:(null===(t=a.state.$detectedLabelsData)||void 0===t||null===(e=t.state.data)||void 0===e?void 0:e.state)!==i.LoadingState.Done}),this._subs.add(this.subscribeToEvent(Nn,(()=>{this.state.search.clearValueFilter()}))),this._subs.add(this.subscribeToEvent($t,this.handleSortByChange)),this._subs.add(r.subscribeToState(this.variableChanged)),this._subs.add((0,F.cR)(this).subscribeToState(((e,t)=>{const n=(0,F.Hj)(this);let{labelName:r}=(0,v.W6)();const a=e.filters.find((e=>e.key===r)),i=t.filters.find((e=>e.key===r));n.state.value===d.To&&a!==i&&this.setState({loading:!0,body:void 0})}))),this._subs.add(null===(n=a.state.$detectedFieldsData)||void 0===n?void 0:n.subscribeToState(((e,t)=>{var n,r,a;(null===(n=e.data)||void 0===n?void 0:n.state)===i.LoadingState.Done&&(null===(r=e.data.series)||void 0===r?void 0:r[0])&&this.updateOptions(null===(a=e.data.series)||void 0===a?void 0:a[0])})));const s=Qr(this);s&&this.updateOptions(s),(0,v.NX)(this)}updateOptions(e){if(!e||!e.length){const e=l.jh.getAncestor(this,Ht.PR),r=this.getVariablesThatCanBeCleared(e);let a;var t,n;return r.length>1?(null===(t=(n=this.state).changeFieldCount)||void 0===t||t.call(n,0),a=this.buildClearFiltersLayout((()=>this.clearVariables(r)))):a=new wn({type:"fields"}),void this.setState({loading:!1,body:a})}const r=l.jh.getAncestor(this,Kr);var a;(0,F.Hj)(this).setState({options:(0,Sn.rd)(e.fields[0].values.map((e=>String(e)))),loading:!1,value:null!==(a=r.state.drillDownLabel)&&void 0!==a?a:d.To}),this.setState({loading:!1})}updateBody(e){const t=(0,F.Hj)(this);if(!t.state.options||!t.state.options.length)return;const n={};if(t.state.options&&t.state.options.length<=1){const e=l.jh.getAncestor(this,Ht.PR),t=this.getVariablesThatCanBeCleared(e);var r,a;t.length>1?(null===(r=(a=this.state).changeFieldCount)||void 0===r||r.call(a,0),n.body=this.buildClearFiltersLayout((()=>this.clearVariables(t)))):n.body=new wn({type:"fields"})}else e.value===d.To&&this.state.body instanceof fn?n.body=new mn({}):e.value!==d.To&&this.state.body instanceof mn?n.body=new fn({}):(void 0===this.state.body||this.state.body instanceof wn||this.state.body instanceof l.dM)&&(n.body=e.value===d.To?new mn({}):new fn({}));this.setState(n)}getVariablesThatCanBeCleared(e){const t=l.jh.getVariables(e);let n=[];for(const e of t.state.variables)e instanceof l.H9&&e.state.filters.length&&n.push(e),e instanceof Wt.m&&e.state.value&&"logsFormat"!==e.state.name&&n.push(e);return n}buildClearFiltersLayout(e){return new l.dM({reactNode:a().createElement(zt.R,null,a().createElement(s.Alert,{title:"",severity:"info"},"No labels match these filters."," ",a().createElement(s.Button,{className:Cn.button,onClick:()=>e()},"Clear filters")," "))})}constructor(e){var t,n,r,a;super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){On(e,t,n[t])}))}return e}({$variables:null!==(r=e.$variables)&&void 0!==r?r:new l.Pj({variables:[new Wt.m({name:d.LI,defaultToAll:!1,includeAll:!0,value:null!==(t=e.value)&&void 0!==t?t:d.To,options:null!==(n=e.options)&&void 0!==n?n:[]})]}),loading:!0,sort:new It({target:"fields"}),search:new In("fields"),value:null!==(a=e.value)&&void 0!==a?a:d.To},e)),On(this,"_variableDependency",new l.Sh(this,{variableNames:[d.MB]})),On(this,"variableChanged",((e,t)=>{(e.value!==t.value||!(0,g.B)(e.options,t.options)||void 0===this.state.body||this.state.body instanceof wn||this.state.body instanceof l.dM)&&this.updateBody(e)})),On(this,"handleSortByChange",(e=>{var t;"fields"===e.target&&(this.state.body instanceof fn&&this.state.body.state.body instanceof qt&&(null===(t=this.state.body.state.body)||void 0===t||t.state.layouts.forEach((t=>{t instanceof Bn&&t.sort(e.sortBy,e.direction)}))),(0,b.EE)(b.NO.service_details,b.ir.service_details.value_breakdown_sort_change,{target:"fields",criteria:e.sortBy,direction:e.direction}))})),On(this,"clearVariables",(e=>{l.jh.getAncestor(this,Ht.PR).setState({patterns:[]}),e.forEach((e=>{if(e instanceof l.H9&&"adhoc_service_filter"===e.state.key){let{labelName:t}=(0,v.W6)();t===d.ky&&(t=d.OX),e.setState({filters:e.state.filters.filter((e=>e.key===t))})}else e instanceof l.H9?e.setState({filters:[]}):e instanceof Wt.m&&e.setState({value:"",text:""})}))})),On(this,"onFieldSelectorChange",(e=>{if(!e)return;const t=(0,F.Hj)(this),{sortBy:n,direction:r}=(0,y.vs)("fields",Lt.DEFAULT_SORT_BY,"desc");(0,b.EE)(b.NO.service_details,b.ir.service_details.select_field_in_breakdown_clicked,{field:e,previousField:t.getValueText(),view:"fields",sortBy:n,sortByDirection:r});const a=l.jh.getAncestor(this,Kr);(0,p.fg)(v._J.field,e,a)})),this.addActivationHandler(this.onActivate.bind(this))}}On(xn,"Component",(({model:e})=>{const{body:t,loading:n,blockingMessage:r,search:i,sort:l}=e.useState(),o=(0,F.Hj)(e),{options:c,value:u}=o.useState(),p=(0,s.useStyles2)(Pn);return a().createElement("div",{className:p.container},a().createElement(bn,{isLoading:n,blockingMessage:r},a().createElement("div",{className:p.controls},t instanceof mn&&a().createElement(mn.Selector,{model:t}),t instanceof fn&&a().createElement(fn.Selector,{model:t}),!n&&u!==d.To&&a().createElement(a().Fragment,null,a().createElement(l.Component,{model:l}),a().createElement(i.Component,{model:i})),!n&&c.length>1&&a().createElement(hn.u,{label:"Field",options:c,value:String(u),onChange:e.onFieldSelectorChange})),a().createElement("div",{className:p.content},t&&a().createElement(t.Component,{model:t}))))}));const Cn={link:(0,w.css)({textDecoration:"underline"}),button:(0,w.css)({marginLeft:"1.5rem"})};function Pn(e){return{container:(0,w.css)({flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column"}),content:(0,w.css)({flexGrow:1,display:"flex",paddingTop:e.spacing(0)}),controls:(0,w.css)({flexGrow:0,display:"flex",alignItems:"top",justifyContent:"space-between",flexDirection:"row-reverse",gap:e.spacing(2)})}}const jn="repeat(auto-fit, minmax(400px, 1fr))";function Fn(e,t,n){let r="",a="";const i=(0,F.ir)(e),l=(0,St.k$)(i);return n&&n!==d.e4?r=` ,${n} != ""`:n&&n===d.e4&&(a=` | ${n} != ""`),(0,c.l)(`sum(count_over_time(${(0,F.DX)({labelExpressionToAdd:r,structuredMetadataToAdd:a,parser:l})} [$__auto])) by (${t})`,{legendFormat:`{{${t}}}`,refId:"LABEL_BREAKDOWN_VALUES"})}function kn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Tn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){kn(e,t,n[t])}))}return e}function _n(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}class Ln extends l.Bs{onActivate(){var e;this.setState({$data:(0,o.rS)([Fn(this,d.zp,String((0,F.P4)(this).state.value))]),body:this.build()});const t=(0,F.P4)(this);this._subs.add(t.subscribeToState((e=>{e.value===d.To&&this.setState({$data:void 0,body:void 0})}))),this.subscribeToEvent(yt.Of,(e=>{this.setState({lastFilterEvent:e})})),this._subs.add(null===(e=this.state.$data)||void 0===e?void 0:e.subscribeToState(((e,t)=>{this.onValuesDataQueryChange(e,t)})))}onValuesDataQueryChange(e,t){var n,r,a,l,s;if((null==e||null===(n=e.data)||void 0===n?void 0:n.errors)&&(null===(r=e.data)||void 0===r?void 0:r.state)!==i.LoadingState.Done){var o;const t=this.state.errors;null==e||null===(o=e.data)||void 0===o||o.errors.forEach((e=>{const n=`${e.status}_${e.traceId}_${e.message}`;void 0===t[n]&&(t[n]=_n(Tn({},e),{displayed:!1}))})),this.setState({errors:t}),this.showErrorToast(this.state.errors)}if((null===(a=e.data)||void 0===a?void 0:a.state)===i.LoadingState.Done||(null===(l=e.data)||void 0===l?void 0:l.state)===i.LoadingState.Streaming){const t=this.state.lastFilterEvent;t&&("exclude"===t.operator&&e.data.series.length<1&&this.navigateToLabels(),"include"===t.operator&&e.data.series.length<=1&&this.navigateToLabels())}if((null===(s=e.data)||void 0===s?void 0:s.state)===i.LoadingState.Error&&this.activeLayoutContainsNoPanels()){const t=this.getActiveLayout();if(t instanceof Bn){const n=this.getErrorStateAlert(e.data.errors);t.state.body.setState({children:[n]})}}}getActiveLayout(){const e=this.state.body,t=null==e?void 0:e.state.layouts.find((e=>e.isActive));if(t instanceof Bn||t instanceof l.G1)return t}activeLayoutContainsNoPanels(){const e=this.getActiveLayout();if(e instanceof Bn){const t=e.state.body.state.children[0];if(t instanceof l.vA||t instanceof l.dM)return!0}return!1}getErrorStateAlert(e){return new l.dM({reactNode:a().createElement(s.Alert,{title:"Something went wrong with your request",severity:"error"},null==e?void 0:e.map(((e,t)=>this.renderError(t,e))))})}navigateToLabels(){this.setState({lastFilterEvent:void 0}),(0,p.Vt)(v.G3.labels,l.jh.getAncestor(this,Kr))}build(){const e=(0,F.P4)(this).state,t=l.jh.getAncestor(this,Hn),n=String(null==e?void 0:e.value);let r=l.d0.timeseries();r=r.setCustomFieldConfig("stacking",{mode:s.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("drawStyle",s.DrawStyle.Bars).setOverrides(o.jC).setTitle(n);const i=r.build(),{sortBy:c,direction:u}=(0,y.vs)("labels",Lt.DEFAULT_SORT_BY,"desc"),p=()=>{var e;return null!==(e=t.state.search.state.filter)&&void 0!==e?e:""};return new qt({options:[{value:"single",label:"Single"},{value:"grid",label:"Grid"},{value:"rows",label:"Rows"}],active:"grid",layouts:[new l.G1({direction:"column",children:[new l.vA({minHeight:300,body:i})]}),new Bn({body:new l.gF({isLazy:!0,templateColumns:jn,autoRows:"200px",children:[new l.vA({body:new l.dM({reactNode:a().createElement(s.LoadingPlaceholder,{text:"Loading..."})})})]}),getLayoutChild:(0,St.Zp)(Vt,s.DrawStyle.Bars,d.MB,l.jh.getAncestor(this,Hn).state.sort,n),sortBy:c,direction:u,getFilter:p}),new Bn({body:new l.gF({templateColumns:"1fr",autoRows:"200px",children:[new l.vA({body:new l.dM({reactNode:a().createElement(s.LoadingPlaceholder,{text:"Loading..."})})})]}),getLayoutChild:(0,St.Zp)(Vt,s.DrawStyle.Bars,d.MB,l.jh.getAncestor(this,Hn).state.sort,n),sortBy:c,direction:u,getFilter:p})]})}showErrorToast(e){const t=(0,h.getAppEvents)();let n=[];for(const t in e){const r=e[t];r.displayed||(n.push(r),r.displayed=!0)}n.length&&(this.activeLayoutContainsNoPanels()||t.publish({type:i.AppEvents.alertError.name,payload:null==n?void 0:n.map(((e,t)=>this.renderError(t,e)))}),this.setState({errors:e}))}renderError(e,t){return a().createElement("div",{key:e},t.status&&a().createElement(a().Fragment,null,a().createElement("strong",null,"Status"),": ",t.status," ",a().createElement("br",null)),t.message&&a().createElement(a().Fragment,null,a().createElement("strong",null,"Message"),": ",t.message," ",a().createElement("br",null)),t.traceId&&a().createElement(a().Fragment,null,a().createElement("strong",null,"TraceId"),": ",t.traceId))}static Selector({model:e}){const{body:t}=e.useState();return a().createElement(a().Fragment,null,t&&t instanceof qt&&a().createElement(t.Selector,{model:t}))}constructor(e){super(_n(Tn({},e),{errors:{}})),this.addActivationHandler(this.onActivate.bind(this))}}function Dn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}kn(Ln,"Component",(({model:e})=>{const{body:t}=e.useState();return t?a().createElement(a().Fragment,null,t&&a().createElement(t.Component,{model:t})):a().createElement(s.LoadingPlaceholder,{text:"Loading..."})}));class Nn extends i.BusEventBase{}Dn(Nn,"type","breakdown-search-reset");const $n={};class In extends l.Bs{filterValues(e){if(this.parent instanceof Hn||this.parent instanceof xn){$n[this.cacheKey]=e;const n=this.parent.state.body;var t;n instanceof Ln||n instanceof fn?null===(t=n.state.body)||void 0===t||t.forEachChild((t=>{t instanceof Bn&&t.state.body.isActive&&t.filterByString(e)})):B.v.warn("invalid parent for search",{typeofBody:typeof n,filter:e})}}constructor(e){var t;super({filter:null!==(t=$n[e])&&void 0!==t?t:""}),Dn(this,"cacheKey",void 0),Dn(this,"onValueFilterChange",(e=>{this.setState({filter:e.target.value}),this.filterValues(e.target.value)})),Dn(this,"clearValueFilter",(()=>{this.setState({filter:""}),this.filterValues("")})),Dn(this,"reset",(()=>{this.setState({filter:""}),$n[this.cacheKey]=""})),this.cacheKey=e}}function An(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}Dn(In,"Component",(({model:e})=>{const{filter:t}=e.useState();return a().createElement(x,{value:t,onChange:e.onValueFilterChange,onClear:e.clearValueFilter,placeholder:"Search for value"})}));class Bn extends l.Bs{performRepeat(e){const t=[],n=(0,Lt.sortSeries)(e.series,this.sortBy,this.direction);for(let e=0;e<n.length;e++){const r=this.state.getLayoutChild(n[e],e);t.push(r)}this.sortedSeries=n,this.unfilteredChildren=t,this.getFilter()?(this.state.body.setState({children:[]}),this.filterByString(this.getFilter())):this.state.body.setState({children:t})}constructor(e){var{sortBy:t,direction:n,getFilter:r}=e;super(function(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}(e,["sortBy","direction","getFilter"])),An(this,"unfilteredChildren",[]),An(this,"sortBy",void 0),An(this,"direction",void 0),An(this,"sortedSeries",[]),An(this,"getFilter",void 0),An(this,"sort",((e,t)=>{const n=l.jh.getData(this);this.sortBy=e,this.direction=t,n.state.data&&this.performRepeat(n.state.data)})),An(this,"iterateFrames",(e=>{if(l.jh.getData(this).state.data)for(let t=0;t<this.sortedSeries.length;t++)e(this.sortedSeries,t)})),An(this,"filterByString",(e=>{let t=[];this.iterateFrames(((e,n)=>{const r=Vt(e[n]);t.push(r)})),ae(t,e,(e=>{e&&e[0]?this.filterFrames((t=>{const n=Vt(t);return e[0].includes(n)})):this.filterFrames((()=>!0))}))})),An(this,"filterFrames",(e=>{const t=[];var n,r;this.iterateFrames(((n,r)=>{e(n[r])&&t.push(this.unfilteredChildren[r])})),0===t.length?this.state.body.setState({children:[(n=this.getFilter(),r=this.clearFilter,new l.G1({direction:"row",children:[new l.vA({body:new l.dM({reactNode:a().createElement("div",{className:Vn.alertContainer},a().createElement(s.Alert,{title:"",severity:"info",className:Vn.noResultsAlert},"No values found matching “",n,"”",a().createElement(s.Button,{className:Vn.clearButton,onClick:r},"Clear filter")))})})]}))]}):this.state.body.setState({children:t})})),An(this,"clearFilter",(()=>{this.publishEvent(new Nn,!0)})),this.sortBy=t,this.direction=n,this.getFilter=r,this.addActivationHandler((()=>{const e=l.jh.getData(this);this._subs.add(e.subscribeToState(((e,t)=>{var n,r,a,l;((null===(n=e.data)||void 0===n?void 0:n.state)===i.LoadingState.Done||(null===(r=e.data)||void 0===r?void 0:r.state)===i.LoadingState.Streaming&&e.data.series.length>(null!==(l=null===(a=t.data)||void 0===a?void 0:a.series.length)&&void 0!==l?l:0))&&this.performRepeat(e.data)}))),e.state.data&&this.performRepeat(e.state.data)}))}}An(Bn,"Component",(({model:e})=>{const{body:t}=e.useState();return a().createElement(t.Component,{model:t})}));const Vn={alertContainer:(0,w.css)({flexGrow:1,display:"flex",justifyContent:"center",alignItems:"center"}),noResultsAlert:(0,w.css)({minWidth:"30vw",flexGrow:0}),clearButton:(0,w.css)({marginLeft:"1.5rem"})};function Mn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class Rn extends l.Bs{onActivate(){var e;const t=(0,F.ir)(this),n=l.jh.getAncestor(this,Kr).state.$detectedLabelsData;this.state.body?(null==n||null===(e=n.state.data)||void 0===e?void 0:e.state)===i.LoadingState.Done&&this.update(null==n?void 0:n.state.data.series[0]):this.setState({body:this.build()}),this._subs.add(null==n?void 0:n.subscribeToState(((e,t)=>{var n;(null===(n=e.data)||void 0===n?void 0:n.state)===i.LoadingState.Done&&this.update(e.data.series[0])}))),this._subs.add(t.subscribeToState((()=>{this.updateQueriesOnFieldsVariableChange()})))}getPanelByIndex(e,t){const n=e.state.children[t].state.body;return{panel:n,title:n.state.title}}update(e){var t;const n=(0,F.P4)(this).state.options.filter((e=>e.value!==d.To)).map((e=>e.label));null===(t=this.state.body)||void 0===t||t.state.layouts.forEach((t=>{let r=[];const a=t,i=new Set(n),l=a.state.children;for(let e=0;e<l.length;e++){const{title:t}=this.getPanelByIndex(a,e);i.has(t)?i.delete(t):(l.splice(e,1),e--),r.push(t)}const s=Array.from(i).map((e=>({label:e,value:e})));l.push(...this.buildChildren(s));const o=this.calculateCardinalityMap(e);l.sort(this.sortChildren(o)),l.map((e=>{dn(e)})),a.setState({children:l})}))}calculateCardinalityMap(e){const t=new Map;if(null==e?void 0:e.length)for(let n=0;n<(null==e?void 0:e.fields.length);n++){const r=e.fields[n].name,a=e.fields[n].values[0];t.set(r,a)}return t}build(){var e;const t=(0,F.P4)(this);l.jh.getAncestor(this,Hn).state.search.reset();const n=this.buildChildren(t.state.options),r=l.jh.getAncestor(this,Kr).state.$detectedLabelsData;if((null==r||null===(e=r.state.data)||void 0===e?void 0:e.state)===i.LoadingState.Done){const e=this.calculateCardinalityMap(null==r?void 0:r.state.data.series[0]);n.sort(this.sortChildren(e))}const a=n.map((e=>e.clone()));return[...n,...a].map((e=>{dn(e)})),new qt({options:[{value:"grid",label:"Grid"},{value:"rows",label:"Rows"}],active:"grid",layouts:[new l.gF({isLazy:!0,templateColumns:jn,autoRows:"200px",children:n}),new l.gF({isLazy:!0,templateColumns:"1fr",autoRows:"200px",children:a})]})}buildChildren(e){const t=[];for(const n of e){const{value:e}=n,r=String(e);if(e===d.To||!e)continue;const a=Fn(this,String(n.value),String(n.value)),i=this.getDataTransformer(a);t.push(new l.xK({body:l.d0.timeseries().setTitle(r).setData(i).setHeaderActions([new an({labelName:r,fieldType:v._J.label}),new pn.g({labelName:r})]).setCustomFieldConfig("stacking",{mode:s.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("drawStyle",s.DrawStyle.Bars).setOverrides(o.jC).build()}))}return t}sortChildren(e){return(t,n)=>{const r=t.state.body,a=n.state.body;if(r.state.title===d.e4)return-1;if(a.state.title===d.e4)return 1;var i;const l=null!==(i=e.get(r.state.title))&&void 0!==i?i:0;var s;return(null!==(s=e.get(a.state.title))&&void 0!==s?s:0)-l}}getDataTransformer(e){const t=(0,o.rS)([e]);return new l.Es({$data:t,transformations:[()=>vn(20)]})}static Selector({model:e}){const{body:t}=e.useState();return a().createElement(a().Fragment,null,t&&a().createElement(t.Selector,{model:t}))}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Mn(e,t,n[t])}))}return e}({},e)),Mn(this,"updateQueriesOnFieldsVariableChange",(()=>{var e;null===(e=this.state.body)||void 0===e||e.state.layouts.forEach((e=>{const t=e;for(let e=0;e<t.state.children.length;e++){const{panel:a,title:i}=this.getPanelByIndex(t,e),s=a.state.$data,o=Fn(this,i,i);var n,r;if(s instanceof l.dt&&o.expr===(null==s||null===(r=s.state.queries)||void 0===r||null===(n=r[0])||void 0===n?void 0:n.expr))break;a.setState({$data:this.getDataTransformer(o)})}}))})),this.addActivationHandler(this.onActivate.bind(this))}}function Wn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function zn(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Wn(e,t,n[t])}))}return e}Mn(Rn,"Component",(({model:e})=>{const{body:t}=e.useState();return t?a().createElement(a().Fragment,null,t&&a().createElement(t.Component,{model:t})):a().createElement(s.LoadingPlaceholder,{text:"Loading..."})}));class Hn extends l.Bs{onActivate(){var e,t,n,r,a;const s=l.jh.getAncestor(this,Kr),o=(0,F.P4)(this);this.setState({loading:(null===(t=s.state.$detectedLabelsData)||void 0===t||null===(e=t.state.data)||void 0===e?void 0:e.state)!==i.LoadingState.Done,error:(null===(r=s.state.$detectedLabelsData)||void 0===r||null===(n=r.state.data)||void 0===n?void 0:n.state)===i.LoadingState.Error}),this._subs.add(this.subscribeToEvent(Nn,(()=>{this.state.search.clearValueFilter()}))),this._subs.add(this.subscribeToEvent($t,this.handleSortByChange)),this._subs.add(null===(a=s.state.$detectedLabelsData)||void 0===a?void 0:a.subscribeToState(this.onDetectedLabelsDataChange)),this._subs.add((0,F.cR)(this).subscribeToState(((e,t)=>{this.onLabelsVariableChange(e,t)}))),this._subs.add(o.subscribeToState(((e,t)=>{this.onGroupByVariableChange(e,t)})));const c=qr(this);c&&this.updateOptions(c),(0,v.NX)(this)}onGroupByVariableChange(e,t){(e.value!==t.value||!(0,g.B)(e.options,t.options)||void 0===this.state.body||this.state.body instanceof wn)&&this.updateBody()}onLabelsVariableChange(e,t){let{labelName:n}=(0,v.W6)();n===d.ky&&(n=d.OX);const r=(0,F.P4)(this),a=e.filters.find((e=>e.key===n)),i=t.filters.find((e=>e.key===n));r.state.value===d.To&&a!==i&&this.setState({loading:!0,body:void 0,error:void 0})}updateOptions(e){if(!e||!e.length)return void this.setState({loading:!1,body:new wn({type:"labels"})});const t=(0,F.P4)(this),n=(0,Sn.dD)(e.fields.map((e=>e.name)));var r;t.setState({loading:!1,options:n,value:null!==(r=this.state.value)&&void 0!==r?r:d.To})}updateBody(){const e=(0,F.P4)(this);if(!e.state.options||!e.state.options.length)return;const t={loading:!1,blockingMessage:void 0,error:!1};e.hasAllValue()&&this.state.body instanceof Ln?t.body=new Rn({}):!e.hasAllValue()&&this.state.body instanceof Rn?t.body=new Ln({}):void 0===this.state.body?e.state.options.length>0?t.body=e.hasAllValue()?new Rn({}):new Ln({}):t.body=new wn({type:"labels"}):this.state.body instanceof wn&&e.state.options.length>0&&(t.body=e.hasAllValue()?new Rn({}):new Ln({})),this.setState(zn({},t))}constructor(e){var t,n,r,a,s;super((a=zn({},e),s=null!=(s={$variables:null!==(r=e.$variables)&&void 0!==r?r:new l.Pj({variables:[new Wt.m({name:d.Jg,defaultToAll:!1,includeAll:!0,value:null!==(t=e.value)&&void 0!==t?t:d.To,options:null!==(n=e.options)&&void 0!==n?n:[]})]}),loading:!0,sort:new It({target:"labels"}),search:new In("labels"),value:e.value})?s:{},Object.getOwnPropertyDescriptors?Object.defineProperties(a,Object.getOwnPropertyDescriptors(s)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(s)).forEach((function(e){Object.defineProperty(a,e,Object.getOwnPropertyDescriptor(s,e))})),a)),Wn(this,"_variableDependency",new l.Sh(this,{variableNames:[d.MB]})),Wn(this,"onDetectedLabelsDataChange",((e,t)=>{var n,r,a,l,s,o,c,d,u;(null===(n=e.data)||void 0===n?void 0:n.state)===i.LoadingState.Done&&(null===(r=e.data.series)||void 0===r?void 0:r[0])&&!(0,g.B)(null===(l=e.data.series)||void 0===l||null===(a=l[0])||void 0===a?void 0:a.fields,null===(c=t.data)||void 0===c||null===(o=c.series)||void 0===o||null===(s=o[0])||void 0===s?void 0:s.fields)?this.updateOptions(null===(u=e.data.series)||void 0===u?void 0:u[0]):(null===(d=e.data)||void 0===d?void 0:d.state)===i.LoadingState.Done&&(0,F.P4)(this).setState({loading:!1})})),Wn(this,"handleSortByChange",(e=>{var t,n;"labels"===e.target&&(this.state.body instanceof Ln&&(null===(n=this.state.body)||void 0===n||null===(t=n.state.body)||void 0===t||t.state.layouts.forEach((t=>{t instanceof Bn&&t.sort(e.sortBy,e.direction)}))),(0,b.EE)(b.NO.service_details,b.ir.service_details.value_breakdown_sort_change,{target:"labels",criteria:e.sortBy,direction:e.direction}))})),Wn(this,"onChange",(e=>{if(!e)return;const t=(0,F.P4)(this);t.changeValueTo(e);const{sortBy:n,direction:r}=(0,y.vs)("labels",Lt.DEFAULT_SORT_BY,"desc");(0,b.EE)(b.NO.service_details,b.ir.service_details.select_field_in_breakdown_clicked,{label:e,previousLabel:t.getValueText(),view:"labels",sortBy:n,sortByDirection:r});const a=l.jh.getAncestor(this,Kr);(0,p.fg)(v._J.label,e,a)})),this.addActivationHandler(this.onActivate.bind(this))}}function Gn(e){return{container:(0,w.css)({flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column"}),content:(0,w.css)({flexGrow:1,display:"flex",paddingTop:e.spacing(0)}),controls:(0,w.css)({flexGrow:0,display:"flex",alignItems:"top",justifyContent:"space-between",flexDirection:"row-reverse",gap:e.spacing(2)})}}function qn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Qn(e){var t,n;const{indexScene:r,pattern:a,type:i}=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){qn(e,t,n[t])}))}return e}({},e),s=l.jh.getAncestor(r,Ht.PR);if(!s)return void B.v.warn("logs exploration scene not found");const{patterns:o=[]}=s.state,c=o.filter((e=>e.pattern!==a));var d;const u=null!==(d=null===(t=c.filter((e=>"include"===e.type)))||void 0===t?void 0:t.length)&&void 0!==d?d:0;var p;const g=null!==(p=null===(n=c.filter((e=>"exclude"===e.type)))||void 0===n?void 0:n.length)&&void 0!==p?p:0;(0,b.EE)(b.NO.service_details,b.ir.service_details.pattern_selected,{type:i,includePatternsLength:u+("include"===i?1:0),excludePatternsLength:g+("exclude"===i?1:0)}),"undo"===i?s.setState({patterns:c}):s.setState({patterns:[...c,{pattern:a,type:i}]})}Wn(Hn,"Component",(({model:e})=>{const{body:t,loading:n,blockingMessage:r,error:i,search:l,sort:o}=e.useState(),c=(0,F.P4)(e),{options:u,value:p}=c.useState(),g=(0,s.useStyles2)(Gn);return a().createElement("div",{className:g.container},a().createElement(bn,{isLoading:n,blockingMessage:r},a().createElement("div",{className:g.controls},t instanceof Ln&&a().createElement(Ln.Selector,{model:t}),t instanceof Rn&&a().createElement(Rn.Selector,{model:t}),!n&&p!==d.To&&a().createElement(a().Fragment,null,a().createElement(o.Component,{model:o}),a().createElement(l.Component,{model:l})),!n&&u.length>0&&a().createElement(hn.u,{label:"Label",options:u,value:String(p),onChange:e.onChange})),i&&a().createElement(s.Alert,{title:"",severity:"warning"},"The labels are not available at this moment. Try using a different time range or check again later."),a().createElement("div",{className:g.content},t&&a().createElement(t.Component,{model:t}))))}));var Un=n(5218);const Jn=e=>({logsStatsRow:(0,w.css)({margin:`${e.spacing(1.15)}px 0`}),logsStatsRowActive:(0,w.css)({color:e.colors.primary.text,position:"relative"}),logsStatsRowLabel:(0,w.css)({display:"flex",marginBottom:"1px"}),logsStatsRowValue:(0,w.css)({flex:1,textOverflow:"ellipsis",overflow:"hidden"}),logsStatsRowCount:(0,w.css)({textAlign:"right",marginLeft:e.spacing(.75)}),logsStatsRowPercent:(0,w.css)({textAlign:"right",marginLeft:e.spacing(.75),width:e.spacing(4.5)}),logsStatsRowBar:(0,w.css)({height:e.spacing(.5),overflow:"hidden",background:e.colors.text.disabled}),logsStatsRowInnerBar:(0,w.css)({height:e.spacing(.5),overflow:"hidden",background:e.colors.primary.main})}),Yn=({active:e,count:t,proportion:n,value:r})=>{const i=(0,s.useStyles2)(Jn),l=`${Math.round(100*n)}%`,o={width:l};return a().createElement("div",{className:e?`${i.logsStatsRow} ${i.logsStatsRowActive}`:i.logsStatsRow},a().createElement("div",{className:i.logsStatsRowLabel},a().createElement("div",{className:i.logsStatsRowValue,title:r},r),a().createElement("div",{className:i.logsStatsRowCount},t),a().createElement("div",{className:i.logsStatsRowPercent},l)),a().createElement("div",{className:i.logsStatsRowBar},a().createElement("div",{className:i.logsStatsRowInnerBar,style:o})))};function Kn(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const Xn=e=>({logsStats:(0,w.css)({background:"inherit",color:e.colors.text.primary,wordBreak:"break-all",width:"fit-content",maxHeight:"40vh",overflowY:"auto",marginTop:e.spacing(1)}),logsStatsHeader:(0,w.css)({borderBottom:`1px solid ${e.colors.border.medium}`,display:"flex"}),logsStatsTitle:(0,w.css)({fontWeight:e.typography.fontWeightMedium,paddingRight:e.spacing(2),display:"inline-block",whiteSpace:"nowrap",textOverflow:"ellipsis",flexGrow:1}),logsStatsClose:(0,w.css)({cursor:"pointer"}),logsStatsBody:(0,w.css)({padding:"5px 0px"})}),Zn=e=>{const t=(0,s.useStyles2)(Xn),{stats:n,value:r}=e,i=n.slice(0,10);let l=i.find((e=>e.value===r)),o=n.slice(10);!l&&(l=o.find((e=>e.value===r)),o=o.filter((e=>e.value!==r)));const c=o.reduce(((e,t)=>e+t.count),0),d=i.reduce(((e,t)=>e+t.count),0)+c;let u=[...i];return c>0&&u.push({value:"Other",count:c,proportion:c/d}),u.sort(((e,t)=>t.count-e.count)),a().createElement("div",{className:t.logsStats},a().createElement("div",{className:t.logsStatsHeader},a().createElement("div",{className:t.logsStatsTitle},"From a sample of ",d," rows found")),a().createElement("div",{className:t.logsStatsBody},u.map((e=>{return a().createElement(Yn,(t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Kn(e,t,n[t])}))}return e}({key:e.value},e),n=null!=(n={active:e.value===r})?n:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})),t));var t,n}))))};function er(e,t,n,r,a,i,l){try{var s=e[i](l),o=s.value}catch(e){return void n(e)}s.done?t(o):Promise.resolve(o).then(r,a)}const tr=({exploration:e,pattern:t})=>{const n=function(e){const t=[];let n=e.indexOf("<_>");for(;-1!==n;)t.push(n),n=e.indexOf("<_>",n+1);return t}(t),[o,d]=(0,r.useState)(void 0),[u,p]=(0,r.useState)(!1),g=(0,s.useStyles2)(nr),v=(0,r.useRef)(null),h=(0,r.useRef)(null),f=(y=function*(){(0,b.EE)(b.NO.service_details,b.ir.service_details.pattern_field_clicked);const r=function(e,t,n){let r=1;const a=e.replace(/<_>/g,(()=>`<field_${r++}>`));return`{${n.state.filterExpression}} |> \`${e}\` | pattern \`${a}\` | keep ${t.map(((e,t)=>`field_${t+1}`)).join(" ,")} | line_format ""`}(t,n,(0,F.cR)(e)),a=yield(0,m.hJ)(e),s=l.jh.getTimeRange(e).state.value;o&&r===v.current&&s===h.current||(v.current=r,h.current=s,null==a||a.query({requestId:"1",interval:"",intervalMs:0,scopedVars:{},range:s,targets:[(0,c.l)(r,{maxLines:1e3})],timezone:"",app:"",startTime:0}).forEach((e=>{var t,r;e.state!==i.LoadingState.Done||(null===(t=e.errors)||void 0===t?void 0:t.length)?(e.state===i.LoadingState.Error||(null===(r=e.errors)||void 0===r?void 0:r.length))&&(d(void 0),p(!0)):(d(function(e,t){const n=new Map;e.data[0].fields[0].values.toArray().forEach((e=>{Object.keys(e).forEach((t=>{var r,a;n.has(t)||n.set(t,new Map),null===(a=n.get(t))||void 0===a||a.set(e[t],((null===(r=n.get(t))||void 0===r?void 0:r.get(e[t]))||0)+1)}))}));const r=[];for(let e=0;e<=t;e++){var a;const t=[];null===(a=n.get(`field_${e+1}`))||void 0===a||a.forEach(((e,n)=>{t.push({value:n,count:e,proportion:e/1e3})})),t.sort(((e,t)=>t.count-e.count)),r.push(t)}return r}(e,n.length)),p(!1))})))},S=function(){var e=this,t=arguments;return new Promise((function(n,r){var a=y.apply(e,t);function i(e){er(a,n,r,i,l,"next",e)}function l(e){er(a,n,r,i,l,"throw",e)}i(void 0)}))},function(){return S.apply(this,arguments)});var y,S;const w=(0,r.useMemo)((()=>t.split("<_>")),[t]);return a().createElement("div",null,w.map(((e,t)=>a().createElement("span",{key:t},e,t!==n.length&&a().createElement(s.Toggletip,{onOpen:f,content:a().createElement(a().Fragment,null,o&&o[t].length>0&&a().createElement(Zn,{stats:o[t],value:""}),o&&0===o[t].length&&a().createElement("div",null,"No available stats for this field in the current timestamp."),!o&&u&&a().createElement("div",null,"Could not load stats for this pattern."),!o&&!u&&a().createElement("div",{style:{padding:"10px"}},a().createElement(s.Spinner,{size:"xl"})))},a().createElement("span",{className:g.pattern},"<_>"))))))};function nr(e){return{pattern:(0,w.css)({cursor:"pointer",backgroundColor:e.colors.emphasize(e.colors.background.primary,.1),margin:"0 2px","&:hover":{backgroundColor:e.colors.emphasize(e.colors.background.primary,.2)}})}}function rr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class ar extends l.Bs{onActivate(){if(this.state.body)return;const e=(0,c.l)(d.SA);this.replacePatternsInQuery(e);const t=(0,o.rS)([e]);t.getResultsStream().subscribe((e=>{this.onQueryWithFiltersResult(e)})),this.setState({body:new l.G1({direction:"column",children:[new l.vA({body:void 0,width:"100%",height:0}),new l.vA({height:300,width:"100%",body:l.d0.logs().setHoverHeader(!0).setOption("showLogContextToggle",!0).setOption("showTime",!0).setData(t).build()})]})})}replacePatternsInQuery(e){const t={pattern:this.state.pattern,type:"include"},n=(0,c.M3)([t]);e.expr=e.expr.replace(d.sC,n)}removePatternFromFilterExclusion(){const e=l.jh.getAncestor(this,sr);var t;const n=null!==(t=e.state.patternsNotMatchingFilters)&&void 0!==t?t:[],r=n.findIndex((e=>e===this.state.pattern));-1!==r&&(n.splice(r,1),e.setState({patternsNotMatchingFilters:n}))}setWarningMessage(e){const t=this.getNoticeFlexItem(),n=this.getVizFlexItem();return t instanceof l.vA&&t.setState({isHidden:!1,height:"auto",body:new l.dM({reactNode:e})}),n}getNoticeFlexItem(){const e=this.getFlexItemChildren();return null==e?void 0:e[0]}getVizFlexItem(){const e=this.getFlexItemChildren();return null==e?void 0:e[1]}getFlexItemChildren(){var e;return null===(e=this.state.body)||void 0===e?void 0:e.state.children}excludeThisPatternFromFiltering(){const e=l.jh.getAncestor(this,sr);var t;const n=null!==(t=e.state.patternsNotMatchingFilters)&&void 0!==t?t:[];e.setState({patternsNotMatchingFilters:[...n,this.state.pattern]})}static Component({model:e}){const{body:t}=e.useState();return t?a().createElement(t.Component,{model:t}):null}constructor(e){super(e),rr(this,"clearFilters",(()=>{const e=(0,F.ir)(this),t=(0,F.Rr)(this),n=(0,F.iw)(this);if(e.setState({filters:[]}),n.setState({filters:[]}),t.state.value){t.changeValueTo("");const e=this.getNoticeFlexItem();null==e||e.setState({isHidden:!0}),this.removePatternFromFilterExclusion()}})),rr(this,"onQueryError",(e=>{if(e.data.state===i.LoadingState.Done&&(0===e.data.series.length||e.data.series.every((e=>0===e.length)))||e.data.state===i.LoadingState.Error){let t;try{t={pattern:this.state.pattern,traceIds:JSON.stringify(e.data.traceIds),request:JSON.stringify(e.data.request)}}catch(e){t={pattern:this.state.pattern,msg:"Failed to encode context"}}B.v.error(new Error("Pattern sample query returns no results"),t),this.setWarningMessage(a().createElement(s.Alert,{severity:"error",title:""},"This pattern returns no logs."));const n=this.getVizFlexItem();n instanceof l.vA&&n.setState({isHidden:!0})}})),rr(this,"onQueryWithFiltersResult",(e=>{const t=(0,c.l)(d.pT);this.replacePatternsInQuery(t);const n=(0,o.rS)([t]);if(n.getResultsStream().subscribe(this.onQueryError),e.data.state===i.LoadingState.Done&&(0===e.data.series.length||e.data.series.every((e=>0===e.length)))){const e=this.getNoticeFlexItem(),t=this.getVizFlexItem();if(e instanceof l.vA&&e.setState({isHidden:!1,height:"auto",body:new l.dM({reactNode:a().createElement(s.Alert,{severity:"warning",title:""},"The logs returned by this pattern do not match the current query filters.",a().createElement(s.Button,{className:Cn.button,onClick:()=>this.clearFilters()},"Clear filters"))})}),t instanceof l.vA){const e=t.state.body;e instanceof l.Eb&&(null==e||e.setState({$data:n}))}this.excludeThisPatternFromFiltering()}e.data.state===i.LoadingState.Error&&this.onQueryError(e)})),this.addActivationHandler(this.onActivate.bind(this))}}function ir({tableViz:e,row:t}){const{expandedRows:n}=e.useState(),i=null==n?void 0:n.find((e=>e.state.key===t.pattern));return(0,r.useEffect)((()=>{if(!i){const a=(r=t.pattern,new ar({pattern:r,key:r}));var n;e.setState({expandedRows:[...null!==(n=e.state.expandedRows)&&void 0!==n?n:[],a]})}var r}),[t,e,i]),i?a().createElement(i.Component,{model:i}):null}const lr=[""," K"," Mil"," Bil"," Tri"," Quadr"," Quint"," Sext"," Sept"];class sr extends l.Bs{buildColumns(e,t,n,r){const o=ur(n),c=l.jh.getTimeRange(this).state.value,d=[{id:"volume-samples",header:"",cell:e=>{const t={timeRange:c,series:[e.cell.row.original.dataFrame],state:i.LoadingState.Done},n=new l.Zv({data:t}),r=l.d0.timeseries().setData(n).setHoverHeader(!0).setOption("tooltip",{mode:s.TooltipDisplayMode.None}).setCustomFieldConfig("hideFrom",{legend:!0,tooltip:!0}).setCustomFieldConfig("axisPlacement",s.AxisPlacement.Hidden).setDisplayMode("transparent").build();return a().createElement("div",{className:o.tableTimeSeriesWrap},a().createElement("div",{className:o.tableTimeSeries},a().createElement(r.Component,{model:r})))}},{id:"count",header:"Count",sortType:"number",cell:e=>{const t=(0,i.scaledUnits)(1e3,lr)(e.cell.row.original.sum);var n,r;return a().createElement("div",{className:o.countTextWrap},a().createElement("div",null,null!==(n=t.prefix)&&void 0!==n?n:"",t.text,null!==(r=t.suffix)&&void 0!==r?r:""))}},{id:"percent",header:"%",sortType:"number",cell:t=>a().createElement("div",{className:o.countTextWrap},a().createElement("div",null,(100*t.cell.row.original.sum/e).toFixed(0),"%"))},{id:"pattern",header:"Pattern",cell:e=>a().createElement("div",{className:(0,w.cx)(cr(),o.tablePatternTextDefault)},a().createElement(tr,{exploration:(0,m.Ti)(this),pattern:e.cell.row.original.pattern}))},{id:"include",header:void 0,disableGrow:!0,cell:e=>{if(null==r?void 0:r.includes(e.cell.row.original.pattern))return;const n=null==t?void 0:t.find((t=>t.pattern===e.cell.row.original.pattern)),i="include"===(null==n?void 0:n.type),l="exclude"===(null==n?void 0:n.type);return a().createElement(Un.F,{isExcluded:l,isIncluded:i,onInclude:()=>e.cell.row.original.includeLink(),onExclude:()=>e.cell.row.original.excludeLink(),onClear:()=>e.cell.row.original.undoLink(),buttonFill:"outline"})}}];return d}buildTableData(e,t){const n=l.jh.getAncestor(this,Ht.PR);return e.filter((e=>!t.size||t.has(e.pattern))).map((e=>({dataFrame:e.dataFrame,pattern:e.pattern,sum:e.sum,includeLink:()=>Qn({pattern:e.pattern,type:"include",indexScene:n}),excludeLink:()=>Qn({pattern:e.pattern,type:"exclude",indexScene:n}),undoLink:()=>Qn({pattern:e.pattern,type:"undo",indexScene:n})})))}constructor(e){super(e)}}!function(e,t,n){t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n}(sr,"Component",(function({model:e}){const t=l.jh.getAncestor(e,Ht.PR),{patterns:n}=t.useState(),r=(0,s.useTheme2)(),i=dr(r),o=l.jh.getAncestor(e,hr),{legendSyncPatterns:c}=o.useState(),{patternFrames:d,patternsNotMatchingFilters:u}=e.useState(),p=null!=d?d:[],g=p.reduce(((e,t)=>e+t.sum),0),m=e.buildTableData(p,c),v=e.buildColumns(g,n,r,u);return a().createElement("div",{"data-testid":f.b.patterns.tableWrapper,className:i.tableWrap},a().createElement(s.InteractiveTable,{columns:v,data:m,getRowId:e=>e.pattern,renderExpandedRow:t=>a().createElement(ir,{tableViz:e,row:t})}))}));const or=h.config.theme2,cr=()=>(0,w.css)({minWidth:"200px",fontFamily:or.typography.fontFamilyMonospace,overflow:"hidden",overflowWrap:"break-word"}),dr=e=>({link:(0,w.css)({textDecoration:"underline"}),tableWrap:(0,w.css)({"> div":{height:"calc(100vh - 450px)",minHeight:"470px"},th:{top:0,position:"sticky",backgroundColor:e.colors.background.canvas,zIndex:e.zIndex.navbarFixed}})}),ur=e=>({tablePatternTextDefault:(0,w.css)({fontFamily:e.typography.fontFamilyMonospace,minWidth:"200px",maxWidth:"100%",overflow:"hidden",overflowWrap:"break-word",fontSize:e.typography.bodySmall.fontSize,wordBreak:"break-word"}),countTextWrap:(0,w.css)({textAlign:"right",fontSize:e.typography.bodySmall.fontSize}),tableTimeSeriesWrap:(0,w.css)({width:"230px",pointerEvents:"none"}),tableTimeSeries:(0,w.css)({height:"30px",overflow:"hidden"})});function pr(e,t,n,r,a,i,l){try{var s=e[i](l),o=s.value}catch(e){return void n(e)}s.done?t(o):Promise.resolve(o).then(r,a)}function gr(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function l(e){pr(i,r,a,l,s,"next",e)}function s(e){pr(i,r,a,l,s,"throw",e)}l(void 0)}))}}function mr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const vr=h.config.theme2.visualization.palette;class hr extends l.Bs{onActivate(){this.updateBody(),this._subs.add(l.jh.getAncestor(this,Kr).subscribeToState(((e,t)=>{var n,r,a,i,s,o;const c=null==e||null===(a=e.$patternsData)||void 0===a||null===(r=a.state)||void 0===r||null===(n=r.data)||void 0===n?void 0:n.series,d=null==t||null===(o=t.$patternsData)||void 0===o||null===(s=o.state)||void 0===s||null===(i=s.data)||void 0===i?void 0:i.series;if(!(0,g.B)(c,d)){const e=l.jh.getAncestor(this,Cr);this.updatePatterns(e.state.patternFrames),e.setState({filteredPatterns:void 0})}}))),this._subs.add(l.jh.getAncestor(this,Cr).subscribeToState(((e,t)=>{const n=l.jh.getAncestor(this,Cr);e.filteredPatterns&&!(0,g.B)(e.filteredPatterns,t.filteredPatterns)?this.updatePatterns(n.state.filteredPatterns):n.state.patternFilter||this.updatePatterns(n.state.patternFrames)})))}updatePatterns(e=[]){var t=this;return gr((function*(){var n;null===(n=t.state.body)||void 0===n||n.forEachChild((n=>{n instanceof l.Eb&&n.setState({$data:t.getTimeseriesDataNode(e)}),n instanceof sr&&n.setState({patternFrames:e})}))}))()}updateBody(){var e=this;return gr((function*(){var t,n;const r=l.jh.getAncestor(e,Cr).state.patternFrames;(null===(n=l.jh.getAncestor(e,Kr).state.$patternsData)||void 0===n||null===(t=n.state.data)||void 0===t?void 0:t.series)&&r?e.setState({body:e.getSingleViewLayout(),legendSyncPatterns:new Set,loading:!1}):B.v.warn("Failed to update PatternsFrameScene body")}))()}extendTimeSeriesLegendBus(e,t){const n=t.onToggleSeriesVisibility;t.onToggleSeriesVisibility=(t,r)=>{var a;null==n||n(t,r);const i=null===(a=e.state.fieldConfig.overrides)||void 0===a?void 0:a[0],l=null==i?void 0:i.matcher.options.names,s=new Set;l&&l.forEach(s.add,s),this.setState({legendSyncPatterns:s})}}getSingleViewLayout(){const e=l.jh.getAncestor(this,Cr).state.patternFrames;if(!e)return void B.v.warn("Failed to set getSingleViewLayout");const t=this.getTimeSeries(e);return new l.gF({templateColumns:"100%",autoRows:"200px",isLazy:!0,children:[t,new sr({patternFrames:e})]})}getTimeSeries(e){const t=l.jh.getAncestor(this,Ht.PR),n=l.d0.timeseries().setData(this.getTimeseriesDataNode(e)).setOption("legend",{asTable:!0,showLegend:!0,displayMode:s.LegendDisplayMode.Table,placement:"right",width:200}).setHoverHeader(!0).setUnit("short").setLinks([{url:"#",targetBlank:!1,onClick:e=>{Qn({pattern:e.origin.labels.name,type:"include",indexScene:t})},title:"Include"},{url:"#",targetBlank:!1,onClick:e=>{Qn({pattern:e.origin.labels.name,type:"exclude",indexScene:t})},title:"Exclude"}]).build();return n.setState({extendPanelContext:(e,t)=>this.extendTimeSeriesLegendBus(e,t)}),n}getTimeseriesDataNode(e){const t=l.jh.getTimeRange(this).state.value;return new l.Zv({data:{series:e.map(((e,t)=>{const n=e.dataFrame;return n.fields[1].config.color=function(e){return{mode:"fixed",fixedColor:vr[e]}}(t),n.fields[1].name="",n})),state:i.LoadingState.Done,timeRange:t}})}constructor(e){var t,n;super((t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){mr(e,t,n[t])}))}return e}({loading:!0},e),n=null!=(n={legendSyncPatterns:new Set})?n:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})),t)),this.addActivationHandler(this.onActivate.bind(this))}}mr(hr,"Component",(({model:e})=>{var t;const{body:n,loading:r}=e.useState(),i=l.jh.getAncestor(e,Kr),{$patternsData:s}=i.useState(),o=null==s||null===(t=s.state.data)||void 0===t?void 0:t.series;return a().createElement("div",{className:fr.container},!r&&o&&o.length>0&&a().createElement(a().Fragment,null,n&&a().createElement(n.Component,{model:n})))}));const fr={container:(0,w.css)({width:"100%",".show-on-hover":{display:"none"}})};function br(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class yr extends l.Bs{onActivate(){const e=l.jh.getAncestor(this,Cr);this._subs.add(e.subscribeToState(((e,t)=>{if(e.patternFilter!==t.patternFilter){const e=l.jh.getAncestor(this,Cr);e.state.patternFrames&&ie(e.state.patternFrames.map((e=>e.pattern)),e.state.patternFilter,this.onSearchResult)}}))),this._subs.add(e.subscribeToState(((e,t)=>{e.patternFilter&&!e.filteredPatterns&&e.patternFrames&&!(0,g.B)(e.filteredPatterns,t.filteredPatterns)&&ae(e.patternFrames.map((e=>e.pattern)),e.patternFilter,this.onSearchResult)})))}setFilteredPatterns(e,t){const n=l.jh.getAncestor(this,Cr),r=null!=t?t:n.state.patternFrames;if(r){const t=r.filter((t=>!(!n.state.patternFilter||!(null==r?void 0:r.length))&&e.find((e=>e===t.pattern))));n.setState({filteredPatterns:t})}}setEmptySearch(){l.jh.getAncestor(this,Cr).setState({filteredPatterns:void 0})}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){br(e,t,n[t])}))}return e}({},e)),br(this,"clearSearch",(()=>{l.jh.getAncestor(this,Cr).setState({patternFilter:""})})),br(this,"handleSearchChange",(e=>{l.jh.getAncestor(this,Cr).setState({patternFilter:e.target.value})})),br(this,"onSearchResult",(e=>{const t=l.jh.getAncestor(this,Cr);t.state.patternFilter?this.setFilteredPatterns(e[0]):t.state.filteredPatterns&&!t.state.patternFilter&&this.setEmptySearch()})),this.addActivationHandler(this.onActivate.bind(this))}}br(yr,"Component",(function({model:e}){const t=l.jh.getAncestor(e,Cr),{patternFilter:n}=t.useState();return a().createElement(s.Field,{className:Sr.field},a().createElement(x,{onChange:e.handleSearchChange,onClear:e.clearSearch,value:n,placeholder:"Search patterns"}))}));const Sr={field:(0,w.css)({label:"field",marginBottom:0}),icon:(0,w.css)({cursor:"pointer"})};function wr(){return a().createElement(zt.R,null,a().createElement("div",null,a().createElement("p",null,a().createElement("strong",null,"Sorry, we could not detect any patterns.")),a().createElement("p",null,"Check back later or reach out to the team in the"," ",a().createElement(s.TextLink,{href:"https://slack.grafana.com/",external:!0},"Grafana Labs community Slack channel")),a().createElement("p",null,"Patterns let you detect similar log lines to include or exclude from your search.")))}function Or(){return a().createElement(zt.R,null,a().createElement("div",null,a().createElement("p",null,a().createElement("strong",null,"Patterns are only available for the most recent ",xr," hours of data.")),a().createElement("p",null,"See the"," ",a().createElement(s.TextLink,{href:"https://grafana.com/docs/grafana/latest/explore/simplified-exploration/logs/patterns/",external:!0},"patterns docs")," ","for more info.")))}function Er(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}const xr=3;class Cr extends l.Bs{onActivate(){var e,t,n;const r=l.jh.getAncestor(this,Kr);this.setBody();const a=null===(t=r.state.$patternsData)||void 0===t||null===(e=t.state.data)||void 0===e?void 0:e.series;a&&this.updatePatternFrames(a),this._subs.add(null===(n=r.state.$patternsData)||void 0===n?void 0:n.subscribeToState(this.onDataChange))}setBody(){this.setState({body:new l.G1({direction:"column",children:[new l.vA({ySizing:"content",body:new yr}),new l.vA({body:new hr})]})})}updatePatternFrames(e){if(!e)return;const t=this.dataFrameToPatternFrame(e);this.setState({patternFrames:t})}dataFrameToPatternFrame(e){const t=l.jh.getAncestor(this,Kr),n=l.jh.getAncestor(t,Ht.PR).state.patterns;return e.map((e=>{var t,r;const a=null==n?void 0:n.find((t=>t.pattern===e.name)),i=null===(r=e.meta)||void 0===r||null===(t=r.custom)||void 0===t?void 0:t.sum;var l;return{dataFrame:e,pattern:null!==(l=e.name)&&void 0!==l?l:"",sum:i,status:null==a?void 0:a.type}}))}constructor(e){var t;super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Er(e,t,n[t])}))}return e}({$variables:null!==(t=e.$variables)&&void 0!==t?t:new l.Pj({variables:[new l.yP({name:d.Jg,defaultToAll:!0,includeAll:!0})]}),loading:!0,patternFilter:""},e)),Er(this,"onDataChange",((e,t)=>{var n,r,a,l;const s=null===(n=e.data)||void 0===n?void 0:n.series,o=null===(r=t.data)||void 0===r?void 0:r.series;(null===(a=e.data)||void 0===a?void 0:a.state)===i.LoadingState.Done?(this.setState({loading:!1}),(0,g.B)(s,o)||this.updatePatternFrames(s)):(null===(l=e.data)||void 0===l?void 0:l.state)===i.LoadingState.Loading&&this.setState({loading:!0})})),this.addActivationHandler(this.onActivate.bind(this))}}function Pr(e){return{container:(0,w.css)({flexGrow:1,display:"flex",minHeight:"100%",flexDirection:"column"}),content:(0,w.css)({flexGrow:1,display:"flex",paddingTop:e.spacing(0)}),controls:(0,w.css)({flexGrow:0,display:"flex",alignItems:"top",gap:e.spacing(2)}),controlsRight:(0,w.css)({flexGrow:0,display:"flex",justifyContent:"flex-end"}),controlsLeft:(0,w.css)({display:"flex",justifyContent:"flex-left",justifyItems:"left",width:"100%",flexDirection:"column"}),patternMissingText:(0,w.css)({padding:e.spacing(2)})}}function jr(e,t,n=!0){const r=(0,F.ir)(e);let a="";n&&t===d.e4&&(a=`| ${d.e4} != ""`);const i=r.state.filters,l=(0,St.k$)(r);if(i.length){if("mixed"===l)return`sum(count_over_time({${d.S1}} ${a} ${d.S6} ${d.A2} ${d.sC} ${d.YN} ${d.Oc} [$__auto])) by (${t})`;if("json"===l)return`sum(count_over_time({${d.S1}} ${a} ${d.S6} ${d.A2} ${d.sC} ${d.VL} ${d.Oc} [$__auto])) by (${t})`;if("logfmt"===l)return`sum(count_over_time({${d.S1}} ${a} ${d.S6} ${d.A2} ${d.sC} ${d.mF} ${d.Oc} [$__auto])) by (${t})`}return`sum(count_over_time({${d.S1}} ${a} ${d.S6} ${d.A2} ${d.sC} ${d.Oc} [$__auto])) by (${t})`}function Fr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}Er(Cr,"Component",(({model:e})=>{const{body:t,loading:n,blockingMessage:r,patternFrames:o}=e.useState(),{value:c}=l.jh.getTimeRange(e).useState(),d=(0,s.useStyles2)(Pr),u=(0,i.dateTime)().diff(c.to,"hours")>=xr;return a().createElement("div",{className:d.container},a().createElement(bn,{isLoading:n,blockingMessage:r},!n&&!o&&a().createElement("div",{className:d.patternMissingText},a().createElement(s.Text,{textAlignment:"center",color:"primary"},a().createElement("p",null,"There are no pattern matches."),a().createElement("p",null,"Pattern matching has not been configured."),a().createElement("p",null,"Patterns let you detect similar log lines and add or exclude them from your search."),a().createElement("p",null,"To see them in action, add the following to your configuration"),a().createElement("p",null,a().createElement("code",null,"--pattern-ingester.enabled=true")))),!n&&0===(null==o?void 0:o.length)&&u&&a().createElement(Or,null),!n&&0===(null==o?void 0:o.length)&&!u&&a().createElement(wr,null),!n&&o&&o.length>0&&a().createElement("div",{className:d.content},t&&a().createElement(t.Component,{model:t}))))}));class kr extends l.Bs{onActivate(){this.state.panel||this.setState({panel:this.getVizPanel()});const e=(0,F.cR)(this),t=(0,F.ir)(this);e.subscribeToState(((e,t)=>{(0,g.B)(e.filters,t.filters)||this.setState({panel:this.getVizPanel()})})),t.subscribeToState(((e,t)=>{(0,g.B)(e.filters,t.filters)||this.setState({panel:this.getVizPanel()})}))}getVizPanel(){var e;const t=l.d0.timeseries().setTitle("Log volume").setOption("legend",{showLegend:!0,calcs:["sum"],displayMode:s.LegendDisplayMode.List}).setUnit("short").setData((0,o.rS)([(0,c.l)(jr(this,d.e4,!1),{legendFormat:`{{${d.e4}}}`})]));(0,o.ZC)(t);const n=t.build();return n.setState({extendPanelContext:(e,t)=>this.extendTimeSeriesLegendBus(t)}),this._subs.add(null===(e=n.state.$data)||void 0===e?void 0:e.subscribeToState((e=>{var t;(null===(t=e.data)||void 0===t?void 0:t.state)===i.LoadingState.Done&&(0,o.Cw)(n,e.data.series,this)}))),n}constructor(e){super(e),Fr(this,"extendTimeSeriesLegendBus",(e=>{const t=(0,F.iw)(this);this._subs.add(null==t?void 0:t.subscribeToState((()=>{var e,t,n,r;const a=this.state.panel;(null==a||null===(t=a.state.$data)||void 0===t||null===(e=t.state.data)||void 0===e?void 0:e.series)&&(0,o.Cw)(a,null==a||null===(r=a.state.$data)||void 0===r||null===(n=r.state.data)||void 0===n?void 0:n.series,this)}))),e.onToggleSeriesVisibility=(e,t)=>{if(t===s.SeriesVisibilityChangeMode.AppendToSelection)return;const n=(0,Dt.PE)(e,this);(0,b.EE)(b.NO.service_details,b.ir.service_details.level_in_logs_volume_clicked,{level:e,action:n})}})),this.addActivationHandler(this.onActivate.bind(this))}}var Tr;Fr(kr,"Component",(({model:e})=>{const{panel:t}=e.useState();if(t)return a().createElement(t.Component,{model:t})})),function(e){e.logs="Logs",e.labels="Labels",e.fields="Fields",e.patterns="Patterns"}(Tr||(Tr={}));const _r=[{displayName:"Logs",value:v.G3.logs,getScene:()=>new l.G1({direction:"column",children:[new l.vA({minHeight:200,body:new kr({})}),new l.vA({minHeight:"470px",height:"calc(100vh - 500px)",body:new Tt({})})]}),testId:f.b.exploreServiceDetails.tabLogs},{displayName:"Labels",value:v.G3.labels,getScene:()=>new l.G1({children:[new l.vA({body:new Hn({})})]}),testId:f.b.exploreServiceDetails.tabLabels},{displayName:"Fields",value:v.G3.fields,getScene:e=>{return t=e,new l.G1({children:[new l.vA({body:new xn({changeFieldCount:t})})]});var t},testId:f.b.exploreServiceDetails.tabFields},{displayName:"Patterns",value:v.G3.patterns,getScene:()=>new l.G1({children:[new l.vA({body:new Cr({})})]}),testId:f.b.exploreServiceDetails.tabPatterns}],Lr=[{displayName:"Label",value:v._J.label,getScene:e=>function(e){return new l.G1({children:[new l.vA({body:new Hn({value:e})})]})}(e),testId:f.b.exploreServiceDetails.tabLabels},{displayName:"Field",value:v._J.field,getScene:e=>function(e){return new l.G1({children:[new l.vA({body:new xn({value:e})})]})}(e),testId:f.b.exploreServiceDetails.tabFields}];function Dr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Nr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Dr(e,t,n[t])}))}return e}class $r extends l.Bs{}Dr($r,"Component",(({model:e})=>{const t=(0,s.useStyles2)(Ar),n=(0,m.Ti)(e);let r=(0,v.FT)(),i=!1;if(!Object.values(v.G3).includes(r)){const e=(0,v.er)();i=!0,e===v._J.field&&(r=v.G3.fields),e===v._J.label&&(r=v.G3.labels)}const o=l.jh.getAncestor(e,Kr),c=o.useState(),{loading:d,$data:u}=c,g=function(e,t){if(null==e)return{};var n,r,a=function(e,t){if(null==e)return{};var n,r,a={},i=Object.keys(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||(a[n]=e[n]);return a}(e,t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(e);for(r=0;r<i.length;r++)n=i[r],t.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(e,n)&&(a[n]=e[n])}return a}(c,["loading","$data"]),f=g.loadingStates;return a().createElement(s.Box,{paddingY:0},a().createElement("div",{className:t.actions},a().createElement(s.Stack,{gap:1},h.config.featureToggles.appSidecar&&a().createElement(Br,{serviceScene:o}),a().createElement(S,{exploration:n}))),a().createElement(s.TabsBar,null,_r.map(((t,n)=>{return a().createElement(s.Tab,{"data-testid":t.testId,key:n,label:t.displayName,active:r===t.value,counter:f[t.displayName]?void 0:Ir(t,(o=Nr({},g),c={$data:u},c=null!=c?c:{},Object.getOwnPropertyDescriptors?Object.defineProperties(o,Object.getOwnPropertyDescriptors(c)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(c)).forEach((function(e){Object.defineProperty(o,e,Object.getOwnPropertyDescriptor(c,e))})),o)),icon:f[t.displayName]?"spinner":void 0,onChangeTab:()=>{if(t.value&&t.value!==r||i){(0,b.EE)(b.NO.service_details,b.ir.service_details.action_view_changed,{newActionView:t.value,previousActionView:r});const n=l.jh.getAncestor(e,Kr);(0,p.Vt)(t.value,n)}}});var o,c}))))}));const Ir=(e,t)=>{switch(e.value){case"fields":return t.fieldsCount;case"patterns":return t.patternsCount;case"labels":return t.labelsCount;default:return}};function Ar(e){return{actions:(0,w.css)({[e.breakpoints.up(e.breakpoints.values.md)]:{position:"absolute",right:0,zIndex:2}})}}function Br(e){const[t,n]=(0,r.useState)((0,F.cR)(e.serviceScene).state.filters);(0,r.useEffect)((()=>{const t=(0,F.cR)(e.serviceScene).subscribeToState((e=>{n(e.filters)}));return()=>{t.unsubscribe()}}),[e.serviceScene]);const[i,l]=(0,r.useState)(!1),o=(0,h.usePluginLinks)({extensionPointId:"grafana-lokiexplore-app/toolbar-open-related/v1",limitPerPlugin:3,context:{filters:t}});if(o.isLoading||0===o.links.length)return null;if(1===o.links.length){const e=o.links[0];return a().createElement("div",null,a().createElement(s.ToolbarButton,{variant:"canvas",key:e.id,onClick:t=>{var n;return null===(n=e.onClick)||void 0===n?void 0:n.call(e,t)},icon:e.icon},"Related ",e.title))}const c=a().createElement(s.Menu,null,o.links.map((e=>a().createElement(s.Menu.Item,{ariaLabel:e.title,icon:(null==e?void 0:e.icon)||"plug",key:e.id,label:e.title,onClick:t=>{var n;null===(n=e.onClick)||void 0===n||n.call(e,t)}}))));return a().createElement(s.Dropdown,{onVisibleChange:l,placement:"bottom-start",overlay:c},a().createElement(s.ToolbarButton,{"aria-label":"Open related",variant:"canvas",isOpen:i},"Open related"))}var Vr=n(7608),Mr=n(6059);function Rr(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Wr(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Rr(e,t,n[t])}))}return e}function zr(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const Hr="logsPanelQuery";function Gr(e){return null==e?void 0:e.series.find((e=>e.refId===Hr))}function qr(e){var t,n,r;return null===(r=l.jh.getAncestor(e,Kr).state.$detectedLabelsData)||void 0===r||null===(n=r.state.data)||void 0===n||null===(t=n.series)||void 0===t?void 0:t[0]}function Qr(e){var t;const n=l.jh.getAncestor(e,Kr);return Ur(null===(t=n.state.$detectedFieldsData)||void 0===t?void 0:t.state)}const Ur=e=>{var t,n;return null==e||null===(n=e.data)||void 0===n||null===(t=n.series)||void 0===t?void 0:t[0]},Jr=e=>{var t,n,r,a;return null===(a=e.data)||void 0===a||null===(r=a.series)||void 0===r||null===(n=r[0])||void 0===n||null===(t=n.fields)||void 0===t?void 0:t[0]},Yr=e=>{var t,n,r,a;return null===(a=e.data)||void 0===a||null===(r=a.series)||void 0===r||null===(n=r[0])||void 0===n||null===(t=n.fields)||void 0===t?void 0:t[2]};class Kr extends l.Bs{setSubscribeToLabelsVariable(){const e=(0,F.cR)(this);0!==e.state.filters.length?this._subs.add(e.subscribeToState(((e,t)=>{0===e.filters.length&&this.redirectToStart();let{labelName:n,labelValue:r,breakdownLabel:a}=(0,v.W6)();n===d.ky&&(n=d.OX);const i=l.jh.getAncestor(this,Ht.PR),s=i.state.routeMatch;if(e.filters.some((e=>e.key===n&&"="===e.operator&&e.value===r))){if(!(0,g.B)(e.filters,t.filters)){var o,c,u;null===(o=this.state.$patternsData)||void 0===o||o.runQueries(),null===(c=this.state.$detectedLabelsData)||void 0===c||c.runQueries(),null===(u=this.state.$detectedFieldsData)||void 0===u||u.runQueries()}}else{const t=e.filters.find((e=>"="===e.operator&&e.value!==d.ZO));var m,h,f;t?(i.setState({routeMatch:zr(Wr({},s),{params:zr(Wr({},null==s?void 0:s.params),{labelName:t.key===d.OX?d.ky:t.key,labelValue:(0,Vr.uu)(t.value)}),url:null!==(m=null==s?void 0:s.url)&&void 0!==m?m:"",path:null!==(h=null==s?void 0:s.path)&&void 0!==h?h:"",isExact:null===(f=null==s?void 0:s.isExact)||void 0===f||f})}),this.resetTabCount(),a?(0,p.fg)((0,v.er)(),a,this):(0,p.Vt)((0,v.FT)(),this)):this.redirectToStart()}}))):this.redirectToStart()}redirectToStart(){this.setState({$data:void 0,body:void 0,$patternsData:void 0,$detectedLabelsData:void 0,$detectedFieldsData:void 0,patternsCount:void 0,labelsCount:void 0,fieldsCount:void 0}),(0,u.JO)().setServiceSceneState(this.state),this._subs.unsubscribe(),this.clearAdHocVariables(),(0,p.Ns)()}getMetadata(){const e=(0,u.JO)().getServiceSceneState();e&&this.setState(Wr({},e))}onActivate(){l.jh.findByKeyAndType(this,Ht.yQ,Mr.H).setState({hidden:!0}),this.getMetadata(),this.resetBodyAndData(),this.setBreakdownView(),this.runQueries(),this._subs.add(this.subscribeToPatternsQuery()),this._subs.add(this.subscribeToDetectedLabelsQuery()),this._subs.add(this.subscribeToDetectedFieldsQuery((0,v.FT)()!==v.G3.fields)),this._subs.add(this.subscribeToLogsQuery()),this.setSubscribeToLabelsVariable(),this._subs.add(this.subscribeToFieldsVariable()),this._subs.add(this.subscribeToMetadataVariable()),this._subs.add(this.subscribeToLevelsVariable()),this._subs.add(this.subscribeToDataSourceVariable()),this._subs.add(this.subscribeToPatternsVariable()),this._subs.add(this.subscribeToTimeRange())}subscribeToPatternsVariable(){return(0,F.Ku)(this).subscribeToState(((e,t)=>{var n;e.value!==t.value&&(null===(n=this.state.$detectedFieldsData)||void 0===n||n.runQueries())}))}subscribeToDataSourceVariable(){return(0,F.S9)(this).subscribeToState((()=>{this.redirectToStart()}))}resetTabCount(){this.setState({fieldsCount:void 0,labelsCount:void 0,patternsCount:void 0}),(0,u.JO)().setServiceSceneState(this.state)}subscribeToFieldsVariable(){return(0,F.ir)(this).subscribeToState(((e,t)=>{var n;(0,g.B)(e.filters,t.filters)||null===(n=this.state.$detectedFieldsData)||void 0===n||n.runQueries()}))}subscribeToMetadataVariable(){return(0,F.oY)(this).subscribeToState(((e,t)=>{var n;(0,g.B)(e.filters,t.filters)||null===(n=this.state.$detectedFieldsData)||void 0===n||n.runQueries()}))}subscribeToLevelsVariable(){return(0,F.iw)(this).subscribeToState(((e,t)=>{var n;(0,g.B)(e.filters,t.filters)||null===(n=this.state.$detectedFieldsData)||void 0===n||n.runQueries()}))}runQueries(){const e=(0,v.FT)(),t=(0,v.er)();var n,r,a;e!==v.G3.patterns&&void 0!==this.state.patternsCount||null===(n=this.state.$patternsData)||void 0===n||n.runQueries(),e!==v.G3.labels&&t!==v._J.label&&void 0!==this.state.labelsCount||null===(r=this.state.$detectedLabelsData)||void 0===r||r.runQueries(),e!==v.G3.fields&&t!==v._J.field&&void 0!==this.state.fieldsCount||null===(a=this.state.$detectedFieldsData)||void 0===a||a.runQueries()}subscribeToPatternsQuery(){var e;return null===(e=this.state.$patternsData)||void 0===e?void 0:e.subscribeToState((e=>{var t;if(this.updateLoadingState(e,Tr.patterns),(null===(t=e.data)||void 0===t?void 0:t.state)===i.LoadingState.Done){const t=e.data.series;void 0!==(null==t?void 0:t.length)&&(this.setState({patternsCount:t.length}),(0,u.JO)().setPatternsCount(t.length))}}))}subscribeToDetectedLabelsQuery(){var e;return null===(e=this.state.$detectedLabelsData)||void 0===e?void 0:e.subscribeToState((e=>{var t;if(this.updateLoadingState(e,Tr.labels),(null===(t=e.data)||void 0===t?void 0:t.state)===i.LoadingState.Done){const t=e.data,n=t.series[0].fields;if(void 0!==t.series.length&&void 0!==n.length){const e=t.series[0].fields.filter((e=>d.e4!==e.name));this.setState({labelsCount:e.length+1}),(0,u.JO)().setLabelsCount(n.length)}}}))}updateLoadingState(e,t){var n;const r=this.state.loadingStates;r[t]=(null===(n=e.data)||void 0===n?void 0:n.state)===i.LoadingState.Loading;const a=Object.values(r).some((e=>e));this.setState({loading:a,loadingStates:r})}subscribeToLogsQuery(){var e;return null===(e=this.state.$data)||void 0===e?void 0:e.subscribeToState((e=>{this.updateLoadingState(e,Tr.logs)}))}subscribeToDetectedFieldsQuery(e){var t;return null===(t=this.state.$detectedFieldsData)||void 0===t?void 0:t.subscribeToState((t=>{var n;if(this.updateLoadingState(t,Tr.fields),e&&(null===(n=t.data)||void 0===n?void 0:n.state)===i.LoadingState.Done){const e=t.data.series[0];void 0!==e&&e.length!==this.state.fieldsCount&&(this.setState({fieldsCount:e.length}),(0,u.JO)().setFieldsCount(e.length))}}))}subscribeToTimeRange(){return l.jh.getTimeRange(this).subscribeToState((()=>{var e,t,n;null===(e=this.state.$patternsData)||void 0===e||e.runQueries(),null===(t=this.state.$detectedLabelsData)||void 0===t||t.runQueries(),null===(n=this.state.$detectedFieldsData)||void 0===n||n.runQueries()}))}resetBodyAndData(){let e={};this.state.$data||(e.$data=na()),this.state.$patternsData||(e.$patternsData=Zr()),this.state.$detectedLabelsData||(e.$detectedLabelsData=ea()),this.state.$detectedFieldsData||(e.$detectedFieldsData=ta()),this.state.body||(e.body=Xr()),Object.keys(e).length&&this.setState(e)}setBreakdownView(){const{body:e}=this.state,t=(0,v.FT)(),n=_r.find((e=>e.value===t));if(!e){const e=new Error("body is not defined in setBreakdownView!");throw B.v.error(e,{msg:"ServiceScene setBreakdownView error"}),e}if(n)e.setState({children:[...e.state.children.slice(0,1),n.getScene((e=>{"fields"===n.value&&this.setState({fieldsCount:e})}))]});else{const t=(0,v.er)(),n=Lr.find((e=>e.value===t));n&&this.state.drillDownLabel?e.setState({children:[...e.state.children.slice(0,1),n.getScene(this.state.drillDownLabel)]}):B.v.error(new Error("not setting breakdown view"))}}constructor(e){var t;super(Wr({loadingStates:{[Tr.patterns]:!1,[Tr.labels]:!1,[Tr.fields]:!1,[Tr.logs]:!1},loading:!0,body:null!==(t=e.body)&&void 0!==t?t:Xr(),$data:na(),$patternsData:Zr(),$detectedLabelsData:ea(),$detectedFieldsData:ta()},e)),Rr(this,"_variableDependency",new l.Sh(this,{variableNames:[d.EY,d.MB,d.mB,d.uw,d._Y]})),Rr(this,"clearAdHocVariables",(()=>{[(0,F.cR)(this),(0,F.ir)(this),(0,F.iw)(this)].forEach((e=>{e.setState({filters:[]})}))})),this.addActivationHandler(this.onActivate.bind(this))}}function Xr(){return new l.G1({direction:"column",children:[new l.vA({ySizing:"content",body:new $r({})})]})}function Zr(){return(0,o.FH)([(0,c.BM)(`{${d.S1}}`,"patterns",{refId:"patterns"})])}function ea(){return(0,o.FH)([(0,c.BM)(`{${d.S1}}`,"detected_labels",{refId:"detectedLabels"})])}function ta(){return(0,o.FH)([(0,c.BM)(d.SA,"detected_fields",{refId:"detectedFields"})])}function na(){return(0,o.rS)([(0,c.l)(d.SA,{refId:Hr})])}Rr(Kr,"Component",(({model:e})=>{const{body:t}=e.useState();return t?a().createElement(t.Component,{model:t}):a().createElement(s.LoadingPlaceholder,{text:"Loading..."})}))},866:(e,t,n)=>{n.d(t,{p:()=>g});var r,a,i,l=n(1119),s=n(5959),o=n.n(s),c=n(2007),d=n(6089),u=n(227),p=n(9055);class g extends l.Bs{setHover(e){this.setState({hover:e})}onClick(e){e?(0,p.wy)(this.state.labelName,this.state.labelValue,this):(0,p._J)(this.state.labelName,this.state.labelValue,this)}}i=({model:e})=>{const{ds:t,labelValue:n,labelName:r,hover:a}=e.useState(),i=(0,u.eT)(t,r).includes(n),l=(0,c.useStyles2)((e=>({wrapper:(0,d.css)({display:"flex",flexDirection:"column",justifyContent:"center",alignSelf:"center"})}))),s=i?`Remove  ${n} from favorites`:`Add ${n} to favorites`;return o().createElement("span",{className:l.wrapper},o().createElement(c.ToolbarButton,{onMouseOver:()=>{e.setHover(!0)},onMouseOut:()=>{e.setHover(!1)},icon:o().createElement(c.Icon,{name:i?"favorite":"star",size:"lg",type:i?"mono":"default"}),color:i?"rgb(235, 123, 24)":"#ccc",onClick:()=>e.onClick(i),name:"star","aria-label":s,tooltip:s}))},(a="Component")in(r=g)?Object.defineProperty(r,a,{value:i,enumerable:!0,configurable:!0,writable:!0}):r[a]=i},6939:(e,t,n)=>{n.d(t,{XU:()=>re,yj:()=>le});var r=n(6089),a=n(3241),i=n(5959),l=n.n(i),s=n(7781),o=n(1119),c=n(2007),d=n(227),u=n(3143),p=n(2718),g=n(8835),m=n(4750),v=n(4793),h=n(1220),f=n(9055);function b(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function y(e,t,n){const r=(0,m.cR)(n);(0,p.EE)(p.NO.service_selection,p.ir.service_selection.service_selected,{value:t,label:e});const a=[...r.state.filters.filter((n=>!(n.key===e&&n.value===t))),{key:e,operator:v.w.Equal,value:t}];r.setState({filters:a}),(0,f._J)(e,t,n),e===u.OX&&(e=u.ky),(0,g.jY)(e,t)}class S extends o.Bs{constructor(...e){super(...e),b(this,"onClick",(()=>{this.state.labelValue&&y(this.state.labelName,this.state.labelValue,this)}))}}function w(e){return{button:(0,r.css)({alignSelf:"center"})}}b(S,"Component",(({model:e})=>{const t=(0,c.useStyles2)(w);return l().createElement(c.Button,{"data-testid":h.b.index.showLogsButton,tooltip:`View logs for ${e.state.labelValue}`,className:t.button,variant:"secondary",size:"sm",onClick:e.onClick},"Show logs")}));var O=n(7918),E=n(5183),x=n(4482);const C=()=>l().createElement(x.R,null,l().createElement("p",null,"Log volume has not been configured."),l().createElement("p",null,l().createElement(c.TextLink,{href:"https://grafana.com/docs/loki/latest/reference/api/#query-log-volume",external:!0},"Instructions to enable volume in the Loki config:")),l().createElement(c.Text,{textAlignment:"left"},l().createElement("pre",null,l().createElement("code",null,"limits_config:",l().createElement("br",null),"  volume_enabled: true")))),P=()=>l().createElement(x.R,null,l().createElement("p",null,"No service matched your search."));var j=n(1383),F=n(4462),k=n(5431),T=n(833),_=n(8531),L=n(5435),D=n(4002),N=n(7841),$=n(8315);function I(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class A extends o.Bs{}I(A,"Component",(({model:e})=>{const t=o.jh.getAncestor(e,le),n=o.jh.getAncestor(e,R),{tabOptions:r,showPopover:a}=n.useState(),i=(0,c.useStyles2)(B),s=r.map((e=>{return t=function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){I(e,t,n[t])}))}return e}({},e),n=null!=(n={icon:e.saved?"save":void 0,label:`${e.label}`})?n:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})),t;var t,n}));return l().createElement(c.Stack,{direction:"column",gap:0,role:"tooltip"},l().createElement("div",{className:i.card.body},l().createElement(c.Select,{menuShouldPortal:!1,width:50,onBlur:()=>{n.toggleShowPopover()},autoFocus:!0,isOpen:a,placeholder:"Search labels",options:s,isSearchable:!0,openMenuOnFocus:!0,onChange:e=>{e.value&&(n.toggleShowPopover(),t.setSelectedTab(e.value))}})))}));const B=e=>({card:{body:(0,r.css)({padding:e.spacing(1)}),p:(0,r.css)({maxWidth:300})}});function V(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function M(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){V(e,t,n[t])}))}return e}class R extends o.Bs{getLabelsFromQueryRunnerState(e=(()=>{var e;return null===(e=this.state.$labelsData)||void 0===e?void 0:e.state})()){var t;return null===(t=e.data)||void 0===t?void 0:t.series[0].fields.map((e=>({label:e.name,cardinality:e.values[0]})))}populatePrimaryLabelsVariableOptions(e){const t=o.jh.getAncestor(this,le).getSelectedTab(),n=(0,d.sj)((0,m.S9)(this).getValue().toString()),r=e.map((e=>{const r=n.indexOf(e.label);return{label:e.label===u.OX?u.ky:e.label,value:e.label,active:t===e.label,saved:-1!==r,savedIndex:r}})).sort(((e,t)=>e.value===u.OX||t.value===u.OX?e.value===u.OX?-1:1:e.label<t.label?-1:e.label>t.label?1:0));this.setState({tabOptions:r})}runDetectedLabels(){this.state.$labelsData.runQueries()}runDetectedLabelsSubs(){this._subs.add(o.jh.getTimeRange(this).subscribeToState((()=>{this.runDetectedLabels()}))),this._subs.add((0,m.S9)(this).subscribeToState((()=>{this.runDetectedLabels()})))}onActivate(){this.runDetectedLabels(),this.setState({popover:new A({})}),this.runDetectedLabelsSubs(),this._subs.add((0,m.S9)(this).subscribeToState((()=>{this.state.$labelsData.runQueries()}))),this._subs.add((0,m.El)(this).subscribeToState((()=>{var e;const t=this.getLabelsFromQueryRunnerState(null===(e=this.state.$labelsData)||void 0===e?void 0:e.state);t&&this.populatePrimaryLabelsVariableOptions(t)}))),this._subs.add(this.state.$labelsData.subscribeToState((e=>{var t;if((null===(t=e.data)||void 0===t?void 0:t.state)===s.LoadingState.Done){const t=this.getLabelsFromQueryRunnerState(e),n=o.jh.getAncestor(this,le);t&&this.populatePrimaryLabelsVariableOptions(t);const r=n.getSelectedTab();(null==t?void 0:t.some((e=>e.label===r)))||n.selectDefaultLabelTab()}})))}constructor(e){super(M({showPopover:!1,$labelsData:(0,E.HF)({queries:[(0,O.BM)("","detected_labels")],runQueriesMode:"manual"}),tabOptions:[{label:u.ky,value:u.OX,saved:!0}]},e)),V(this,"removeSavedTab",(e=>{(0,d.Gg)((0,m.S9)(this).getValue().toString(),e);const t=this.getLabelsFromQueryRunnerState();t&&this.populatePrimaryLabelsVariableOptions(t);const n=o.jh.getAncestor(this,le);n.getSelectedTab()===e&&n.selectDefaultLabelTab()})),V(this,"toggleShowPopover",(()=>{this.setState({showPopover:!this.state.showPopover})})),this.addActivationHandler(this.onActivate.bind(this))}}V(R,"Component",(({model:e})=>{const{tabOptions:t,showPopover:n,popover:d,$labelsData:p}=e.useState(),{data:g}=p.useState(),v=o.jh.getAncestor(e,le);(0,m.El)(e).useState();const h=(0,c.useStyles2)(W),f=(0,i.useRef)(null);return l().createElement(c.TabsBar,null,t.filter((e=>e.saved||e.active||e.value===u.OX)).sort(((e,t)=>{return e.value===u.OX||t.value===u.OX?e.value===u.OX?-1:1:(null!==(n=e.savedIndex)&&void 0!==n?n:0)-(null!==(r=t.savedIndex)&&void 0!==r?r:0);var n,r})).map((t=>{const n=l().createElement(c.Tab,{key:t.value,onChangeTab:()=>{v.setSelectedTab(t.value)},label:(0,$.EJ)(t.label,15,!0),active:t.active,suffix:t.value!==u.OX?n=>l().createElement(l().Fragment,null,l().createElement(c.Tooltip,{content:"Remove tab"},l().createElement(c.Icon,{onKeyDownCapture:n=>{"Enter"===n.key&&e.removeSavedTab(t.value)},onClick:n=>{n.stopPropagation(),e.removeSavedTab(t.value)},name:"times",className:(0,r.cx)(n.className)}))):void 0});return t.label.length>15?l().createElement(c.Tooltip,{key:t.value,content:t.label},n):n})),(null==g?void 0:g.state)===s.LoadingState.Loading&&l().createElement(c.Tab,{label:"Loading tabs",icon:"spinner"}),(null==g?void 0:g.state)===s.LoadingState.Done&&l().createElement("span",{className:h.addTab},l().createElement(c.Tab,{onChangeTab:e.toggleShowPopover,label:"Add label",ref:f,icon:"plus-circle"})),d&&l().createElement(c.PopoverController,{content:l().createElement(d.Component,{model:d})},((e,t,r)=>{const i={onBlur:t,onFocus:e};return l().createElement(l().Fragment,null,f.current&&l().createElement(l().Fragment,null,l().createElement(c.Popover,M((s=M({},r,a.rest),o=null!=(o={show:n,wrapperClassName:h.popover,referenceElement:f.current,renderArrow:!0})?o:{},Object.getOwnPropertyDescriptors?Object.defineProperties(s,Object.getOwnPropertyDescriptors(o)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(o)).forEach((function(e){Object.defineProperty(s,e,Object.getOwnPropertyDescriptor(o,e))})),s),i))));var s,o})))}));const W=e=>({addTab:(0,r.css)({label:"add-label-tab",color:e.colors.primary.text,"& button":{color:e.colors.primary.text}}),popover:(0,r.css)({borderRadius:e.shape.radius.default,boxShadow:e.shadows.z3,background:e.colors.background.primary,border:`1px solid ${e.colors.border.weak}`})});var z=n(866);const H=e=>l().createElement(x.R,null,l().createElement("p",null,"No logs found in ",l().createElement("strong",null,e.labelName),".",l().createElement("br",null),"Please adjust time range or select another label."));var G=n(9829),q=n(558);function Q(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function U(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){Q(e,t,n[t])}))}return e}class J extends o.Bs{onActivate(){this.setState(U({},this.isSelected())),this._subs.add((0,m.cR)(this).subscribeToState((()=>{const e=this.isSelected();this.state.included!==e.included&&this.setState(U({},e))})))}getFilter(){return{name:this.state.name,value:this.state.value}}constructor(e){var t,n;super((t=U({},e),n=null!=(n={included:null})?n:{},Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(n)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(n)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(n,e))})),t)),Q(this,"isSelected",(()=>{const e=(0,m.cR)(this),t=e.state.filters.find((t=>{const n=(0,m.z2)(e,t);return t.key===this.state.name&&n.value===this.state.value}));return t?{included:t.operator===v.w.Equal}:{included:!1}})),Q(this,"onClick",(e=>{const t=this.getFilter();(0,q.Qt)(t.name,t.value,e,this,u.MB);const n=(0,m.cR)(this);(0,p.EE)(p.NO.service_selection,p.ir.service_selection.add_to_filters,{filterType:"index-filters",key:t.name,action:e,filtersLength:(null==n?void 0:n.state.filters.length)||0}),this.setState(U({},this.isSelected()))})),this.addActivationHandler(this.onActivate.bind(this))}}Q(J,"Component",(({model:e})=>{const{value:t,hidden:n,included:r}=e.useState();if(n)return l().createElement(l().Fragment,null);const a=(0,c.useStyles2)(Y);return l().createElement("span",{className:a.wrapper},l().createElement(c.Button,{tooltip:!0===r?`Remove ${t} from filters`:`Add ${t} to filters`,variant:"secondary",fill:"outline",icon:!0===r?"minus":"plus",size:"sm","aria-selected":!0===r,className:a.includeButton,onClick:()=>!0===r?e.onClick("clear"):e.onClick("include"),"data-testid":h.b.exploreServiceDetails.buttonFilterInclude}))}));const Y=()=>({container:(0,r.css)({display:"flex",justifyContent:"center"}),includeButton:(0,r.css)({borderRadius:0}),wrapper:(0,r.css)({display:"flex",flexDirection:"column",justifyContent:"center",alignSelf:"center"})});var K=n(6059);function X(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function Z(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){X(e,t,n[t])}))}return e}function ee(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const te=_.config.featureToggles.exploreLogsAggregatedMetrics,ne="__aggregated_metric__",re=(0,s.dateTime)("2024-08-30","YYYY-MM-DD"),ae="var-primary_label",ie="var-ds";class le extends o.Bs{getUrlState(){const{key:e}=se(),t=(0,m.El)(this).state.filters[0];return t.key&&t.key!==e&&(0,m.El)(this).setState({filters:[ee(Z({},t),{key:null!=e?e:t.key})]}),{}}updateFromUrl(e){}addDatasourceChangeToBrowserHistory(e){const t=_.locationService.getLocation(),n=new URLSearchParams(t.search),r=n.get(ie);if(r&&e!==r){const r=t.pathname+t.search;n.set(ie,e);const a=t.pathname+"?"+n.toString();r!==a&&(0,g.ad)(a)}}addLabelChangeToBrowserHistory(e,t=!1){const{key:n,search:r,location:a}=se();if(n){const i=null==n?void 0:n.split("|");if((null==i?void 0:i[0])!==e){i[0]=e,r.set(ae,i.join("|"));const n=a.pathname+a.search,l=a.pathname+"?"+r.toString();n!==l&&(t?_.locationService.replace(l):(0,g.ad)(l))}}}getSelectedTab(){var e;return null===(e=(0,m.El)(this).state.filters[0])||void 0===e?void 0:e.key}selectDefaultLabelTab(){this.addLabelChangeToBrowserHistory(u.OX,!0),this.setSelectedTab(u.OX)}setSelectedTab(e){(0,d.cO)((0,m.S9)(this).getValue().toString(),e),(0,m.h)(this),(0,m.BL)(e,this)}buildServiceLayout(e,t,n,r,a,i){var l;let d;n.to.diff(n.from,"hours")>=4&&n.to.diff(n.from,"hours")<=26&&(d="2h");const p=o.d0.timeseries().setTitle(t).setData((0,E.rS)([(0,O.l)(this.getMetricExpression(t,r,a),{legendFormat:`{{${u.e4}}}`,splitDuration:d,refId:`ts-${t}`})],{runQueriesMode:"manual"})).setCustomFieldConfig("stacking",{mode:c.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("drawStyle",c.DrawStyle.Bars).setUnit("short").setOverrides(E.jC).setOption("legend",{showLegend:!0,calcs:["sum"],placement:"right",displayMode:c.LegendDisplayMode.Table}).setHeaderActions([new z.p({ds:null===(l=i.getValue())||void 0===l?void 0:l.toString(),labelName:e,labelValue:t}),new J({name:e,value:t,hidden:this.isAggregatedMetricsActive()}),new S({labelValue:t,labelName:e})]).build();p.setState({extendPanelContext:(n,r)=>this.extendTimeSeriesLegendBus(e,t,r,p)});const g=new o.xK({$behaviors:[new o.Gg.K2({key:"serviceCrosshairSync",sync:s.DashboardCursorSync.Crosshair})],body:p});return g.addActivationHandler((()=>{var e;(null===(e=(0,G.oh)(g)[0].state.data)||void 0===e?void 0:e.state)!==s.LoadingState.Done&&this.runPanelQuery(g)})),g}isAggregatedMetricsActive(){const e=this.getQueryOptionsToolbar();return!(null==e?void 0:e.state.options.aggregatedMetrics.disabled)&&(null==e?void 0:e.state.options.aggregatedMetrics.active)}formatPrimaryLabelForUI(){const e=this.getSelectedTab();return e===u.OX?u.ky:e}setVolumeQueryRunner(){this.setState({$data:(0,E.HF)({queries:[(0,O.$k)(`{${u.kl}, ${u.ll}}`,"volume",this.getSelectedTab())],runQueriesMode:"manual"})}),this.subscribeToVolume()}doVariablesNeedSync(){const e=(0,m.cR)(this),t=(0,m.aW)(this),n=this.getSelectedTab(),r=e.state.filters.filter((e=>e.key!==n));return{filters:r,needsSync:!(0,T.B)(r,t.state.filters)}}syncVariables(){const e=(0,m.aW)(this),{filters:t,needsSync:n}=this.doVariablesNeedSync();n&&e.setState({filters:t})}onActivate(){var e;this.fixRequiredUrlParams(),this.syncVariables(),this.setVolumeQueryRunner(),this.subscribeToPrimaryLabelsVariable(),this.subscribeToLabelFilterChanges(),this.subscribeToActiveTabVariable((0,m.El)(this)),(null===(e=this.state.$data.state.data)||void 0===e?void 0:e.state)!==s.LoadingState.Done&&this.runVolumeOnActivate(),this.subscribeToTimeRange(),this.subscribeToDatasource(),this.subscribeToAggregatedMetricToggle(),this.subscribeToAggregatedMetricVariable()}runVolumeOnActivate(){var e,t;this.isTimeRangeTooEarlyForAggMetrics()?(this.onUnsupportedAggregatedMetricTimeRange(),(null===(e=this.state.$data.state.data)||void 0===e?void 0:e.state)!==s.LoadingState.Done&&this.runVolumeQuery()):(this.onSupportedAggregatedMetricTimeRange(),(null===(t=this.state.$data.state.data)||void 0===t?void 0:t.state)!==s.LoadingState.Done&&this.runVolumeQuery())}subscribeToAggregatedMetricToggle(){var e;this._subs.add(null===(e=this.getQueryOptionsToolbar())||void 0===e?void 0:e.subscribeToState(((e,t)=>{e.options.aggregatedMetrics.userOverride!==t.options.aggregatedMetrics.userOverride&&this.runVolumeQuery(!0)})))}subscribeToDatasource(){this._subs.add((0,m.S9)(this).subscribeToState((e=>{this.addDatasourceChangeToBrowserHistory(e.value.toString()),this.runVolumeQuery()})))}subscribeToActiveTabVariable(e){this._subs.add(e.subscribeToState(((e,t)=>{if(e.filterExpression!==t.filterExpression){const t=e.filters[0].key;this.addLabelChangeToBrowserHistory(t);const{needsSync:n}=this.doVariablesNeedSync();n?this.syncVariables():this.runVolumeQuery(!0)}})))}subscribeToAggregatedMetricVariable(){this._subs.add((0,m.vm)(this).subscribeToState(((e,t)=>{e.value!==t.value&&(this.setState({body:new o.gF({children:[]})}),this.updateBody(!0))})))}subscribeToPrimaryLabelsVariable(){const e=(0,m.cR)(this);this._subs.add(e.subscribeToState(((e,t)=>{(0,T.B)(e.filters,t.filters)||this.syncVariables()})))}subscribeToLabelFilterChanges(){const e=(0,m.aW)(this);this._subs.add(e.subscribeToState(((e,t)=>{(0,T.B)(e.filters,t.filters)||this.runVolumeQuery(!0)})))}subscribeToVolume(){this._subs.add(this.state.$data.subscribeToState(((e,t)=>{var n,r,a;(null===(n=e.data)||void 0===n?void 0:n.state)!==s.LoadingState.Done||(0,T.B)(null==t||null===(r=t.data)||void 0===r?void 0:r.series,null==e||null===(a=e.data)||void 0===a?void 0:a.series)||this.updateBody(!0)})))}subscribeToTimeRange(){this._subs.add(o.jh.getTimeRange(this).subscribeToState((()=>{this.isTimeRangeTooEarlyForAggMetrics()?this.onUnsupportedAggregatedMetricTimeRange():this.onSupportedAggregatedMetricTimeRange(),this.runVolumeQuery()})))}fixRequiredUrlParams(){const{key:e}=se();e||this.selectDefaultLabelTab()}isTimeRangeTooEarlyForAggMetrics(){return o.jh.getTimeRange(this).state.value.from.isBefore((0,s.dateTime)(re))}onUnsupportedAggregatedMetricTimeRange(){const e=this.getQueryOptionsToolbar();null==e||e.setState({options:{aggregatedMetrics:ee(Z({},null==e?void 0:e.state.options.aggregatedMetrics),{disabled:!0})}})}getQueryOptionsToolbar(){return o.jh.getAncestor(this,N.PR).state.controls.find((e=>e instanceof D.s))}onSupportedAggregatedMetricTimeRange(){const e=this.getQueryOptionsToolbar();null==e||e.setState({options:{aggregatedMetrics:ee(Z({},null==e?void 0:e.state.options.aggregatedMetrics),{disabled:!1})}})}runVolumeQuery(e=!1){e&&this.setVolumeQueryRunner(),this.updateAggregatedMetricVariable(),this.state.$data.runQueries()}updateAggregatedMetricVariable(){const e=(0,m.vm)(this),t=(0,m.cR)(this);this.isTimeRangeTooEarlyForAggMetrics()&&te||!this.isAggregatedMetricsActive()?(e.changeValueTo(u.OX),t.setState({hide:L.zL.dontHide}),e.changeValueTo(u.OX),o.jh.findByKeyAndType(this,N.yQ,K.H).setState({hidden:!1})):(e.changeValueTo(ne),t.setState({hide:L.zL.hideVariable,filters:[]}),o.jh.findByKeyAndType(this,N.yQ,K.H).setState({hidden:!0}))}updateTabs(){if(!this.state.tabs){const e=new R({});this.setState({tabs:e})}}getGridItems(){return this.state.body.state.children}getVizPanel(e){return e.state.body instanceof o.Eb?e.state.body:void 0}runPanelQuery(e){if(e.isActive){const n=(0,G.oh)(e);if(1===n.length){var t;const e=n[0],r=e.state.queries[0],a=null===(t=e.state.data)||void 0===t?void 0:t.timeRange,i=o.jh.getTimeRange(this),l=a?Math.abs(i.state.value.from.diff(null==a?void 0:a.from,"s")):1/0,s=a?Math.abs(i.state.value.to.diff(null==a?void 0:a.to,"s")):1/0,c=o.jh.interpolate(this,r.expr);(e.state.key!==c||l>0||s>0)&&(e.setState({key:c}),e.runQueries())}}}updateBody(e=!1){var t;const{labelsToQuery:n}=this.getLabels(null===(t=this.state.$data.state.data)||void 0===t?void 0:t.series),r=this.getSelectedTab();if(this.updateTabs(),n&&0!==n.length){const t=[],a=this.getGridItems(),i=o.jh.getTimeRange(this).state.value,l=(0,m.vm)(this),s=(0,m.El)(this),c=(0,m.S9)(this);for(const o of n.slice(0,20)){const n=a.filter((e=>{const t=this.getVizPanel(e);return(null==t?void 0:t.state.title)===o}));if(2===n.length)t.push(n[0],n[1]),n[0].isActive&&e&&this.runPanelQuery(n[0]),n[1].isActive&&e&&this.runPanelQuery(n[1]);else{const e=this.buildServiceLayout(r,o,i,l,s,c),n=this.buildServiceLogsLayout(r,o);t.push(e,n)}}this.state.body.setState({children:t,isLazy:!0,templateColumns:"repeat(auto-fit, minmax(500px, 1fr) minmax(300px, 70vw))",autoRows:"200px",md:{templateColumns:"1fr",rowGap:1,columnGap:1}})}else this.state.body.setState({children:[]})}updateServiceLogs(e,t){var n;if(!this.state.body)return void this.updateBody();const{labelsToQuery:r}=this.getLabels(null===(n=this.state.$data.state.data)||void 0===n?void 0:n.series),a=null==r?void 0:r.indexOf(t);if(void 0===a||a<0)return;let i=[...this.getGridItems()];i.splice(2*a+1,1,this.buildServiceLogsLayout(e,t)),this.state.body.setState({children:i})}getLogExpression(e,t,n){return`{${e}=\`${t}\` , ${u.ll} }${n}`}getMetricExpression(e,t,n){const r=n.state.filters[0];return t.state.value===ne?r.key===u.OX?`sum by (${u.e4}) (sum_over_time({${ne}=\`${e}\` } | logfmt | unwrap count [$__auto]))`:`sum by (${u.e4}) (sum_over_time({${ne}=~\`.+\` } | logfmt | ${r.key}=\`${e}\` | unwrap count [$__auto]))`:`sum by (${u.e4}) (count_over_time({ ${r.key}=\`${e}\`, ${u.ll} } [$__auto]))`}getLabels(e){var t,n,r;const a=null!==(r=null==e||null===(t=e[0])||void 0===t?void 0:t.fields[0].values)&&void 0!==r?r:[],i=null===(n=(0,m.S9)(this).getValue())||void 0===n?void 0:n.toString(),l=(0,m.eY)(this).getValue(),s=this.getSelectedTab(),o=function(e,t,n,r){if(!(null==e?void 0:e.length))return[];".+"===n&&(n="");const a=(0,d.eT)(t,r).filter((t=>t.toLowerCase().includes(n.toLowerCase())&&e.includes(t)));return Array.from(new Set([...a,...e]))}(a,i,String(l),s);return{labelsByVolume:a,labelsToQuery:o}}constructor(e){var t;super(Z({body:new o.gF({children:[]}),$variables:new o.Pj({variables:[new k.m({name:u.Du,label:"Service",hide:L.zL.hideVariable,skipUrlSync:!0,value:".+"}),new k.m({name:u.Wi,label:"",hide:L.zL.hideLabel,value:u.OX,skipUrlSync:!0,options:[{value:u.OX,label:u.OX},{value:ne,label:ne}]}),new o.H9({name:u.Gb,hide:L.zL.hideLabel,expressionBuilder:e=>function(e){if(e.length){const t=e[0];return`${t.key}${t.operator}\`${t.value}\``}return""}(e),filters:[{key:null!==(t=se().key)&&void 0!==t?t:u.OX,value:".+",operator:"=~"}]}),new o.H9({name:u.fi,datasource:u.eL,layout:"vertical",filters:[],expressionBuilder:O.VW,hide:L.zL.hideVariable,key:"adhoc_service_filter_replica",skipUrlSync:!0})]}),$data:(0,E.HF)({queries:[],runQueriesMode:"manual"}),serviceLevel:new Map,showPopover:!1,tabOptions:[{label:u.ky,value:u.OX}]},e)),X(this,"_urlSync",new o.So(this,{keys:[ae]})),X(this,"onSearchServicesChange",(0,a.debounce)((e=>{const t=(0,m.eY)(this);(e?(0,O.vC)(e):".+")!==t.state.value&&t.setState({value:e?(0,O.vC)(e):".+",label:null!=e?e:""});const n=(0,m.El)(this),r=n.state.filters[0];(0,O.vC)(t.state.value.toString())!==r.value&&n.setState({filters:[ee(Z({},r),{value:(0,O.vC)(t.state.value.toString())})]}),(0,p.EE)(p.NO.service_selection,p.ir.service_selection.search_services_changed,{searchQuery:e})}),500)),X(this,"getLevelFilterForService",(e=>{let t=this.state.serviceLevel.get(e)||[];return 0===t.length?"":` | ${t.map((e=>("logs"===e&&(e=""),`${u.e4}=\`${e}\``))).join(" or ")} `})),X(this,"buildServiceLogsLayout",((e,t)=>{const n=this.getLevelFilterForService(t),r=new o.xK({$behaviors:[new o.Gg.K2({sync:s.DashboardCursorSync.Off})],body:o.d0.logs().setHoverHeader(!0).setData((0,E.rS)([(0,O.l)(this.getLogExpression(e,t,n),{maxLines:100,refId:`logs-${t}`})],{runQueriesMode:"manual"})).setTitle(t).setOption("showTime",!0).setOption("enableLogDetails",!1).build()});return r.addActivationHandler((()=>{var e;(null===(e=(0,G.oh)(r)[0].state.data)||void 0===e?void 0:e.state)!==s.LoadingState.Done&&this.runPanelQuery(r)})),r})),X(this,"extendTimeSeriesLegendBus",((e,t,n,r)=>{const a=n.onToggleSeriesVisibility;n.onToggleSeriesVisibility=(n,i)=>{var l,s,o;null==a||a(n,i);const c=(0,j.de)(null!==(o=null===(s=r.state.$data)||void 0===s||null===(l=s.state.data)||void 0===l?void 0:l.series)&&void 0!==o?o:[]),d=(0,j.pC)(n,this.state.serviceLevel.get(t),i,c);this.state.serviceLevel.set(t,d),this.updateServiceLogs(e,t)}})),this.addActivationHandler(this.onActivate.bind(this))}}function se(){const e=_.locationService.getLocation(),t=new URLSearchParams(e.search),n=t.get(ae),r=null==n?void 0:n.split("|");return{key:null==r?void 0:r[0],search:t,location:e}}function oe(e){return{container:(0,r.css)({display:"flex",flexDirection:"column",flexGrow:1,position:"relative"}),headingWrapper:(0,r.css)({marginTop:e.spacing(1)}),loadingText:(0,r.css)({margin:0}),header:(0,r.css)({position:"absolute",right:0,top:"4px",zIndex:2}),bodyWrapper:(0,r.css)({flexGrow:1,display:"flex",flexDirection:"column"}),body:(0,r.css)({flexGrow:1,display:"flex",flexDirection:"column"}),icon:(0,r.css)({color:e.colors.text.disabled,marginLeft:e.spacing.x1}),searchFieldPlaceholderText:(0,r.css)({fontSize:e.typography.bodySmall.fontSize,color:e.colors.text.disabled,alignItems:"center",display:"flex"}),searchWrapper:(0,r.css)({display:"flex",alignItems:"center",flexWrap:"wrap"}),searchField:(0,r.css)({marginTop:e.spacing(1),position:"relative"})}}X(le,"Component",(({model:e})=>{var t;const n=(0,c.useStyles2)(oe),{body:r,$data:a,tabs:i}=e.useState(),{data:o}=a.useState(),d=e.getSelectedTab(),u=(0,m.eY)(e),{label:p,value:g}=u.useState(),v=g&&".+"!==g,{labelsByVolume:h,labelsToQuery:f}=e.getLabels(null==o?void 0:o.series),b=(null==o?void 0:o.state)===s.LoadingState.Loading||(null==o?void 0:o.state)===s.LoadingState.Streaming||void 0===o,S=(null===(t=a.state.data)||void 0===t?void 0:t.state)===s.LoadingState.Error;var w;const E=null!==(w=null==f?void 0:f.length)&&void 0!==w?w:0,x=r.state.children.length/2,j=e.formatPrimaryLabelForUI();let k=u.getValue().toString();".+"===k&&(k="");const T=(0,O.sT)(k);var _;return l().createElement("div",{className:n.container},l().createElement("div",{className:n.bodyWrapper},i&&l().createElement(i.Component,{model:i}),l().createElement(c.Field,{className:n.searchField},l().createElement("div",{className:n.searchWrapper},l().createElement(F.f,{initialFilter:{label:T,value:k,icon:"filter"},isLoading:b,value:k||p,onChange:t=>(t=>{e.onSearchServicesChange(t)})(t),selectOption:t=>{y(d,t,e)},label:j,options:null!==(_=null==f?void 0:f.map((e=>({value:e,label:e}))))&&void 0!==_?_:[]}),!b&&l().createElement("span",{className:n.searchFieldPlaceholderText},"Showing ",x," of ",E," ",l().createElement(c.IconButton,{className:n.icon,"aria-label":"Count info",name:"info-circle",tooltip:`${E} labels have values for the selected time range. Total label count may differ`})))),!b&&S&&l().createElement(C,null),!b&&!S&&v&&!(null==h?void 0:h.length)&&l().createElement(P,null),!b&&!S&&!v&&!(null==h?void 0:h.length)&&l().createElement(H,{labelName:d}),!(!b&&S)&&l().createElement("div",{className:n.body},l().createElement(r.Component,{model:r}))))}))},5431:(e,t,n)=>{n.d(t,{m:()=>l});var r=n(1269),a=n(1119);function i(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}class l extends a.n8{getValueOptions(e){return(0,r.of)(this.state.options)}constructor(e){super(function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){i(e,t,n[t])}))}return e}({type:"custom",value:"",text:"",options:[],name:""},e))}}i(l,"Component",(({model:e})=>(0,a.yC)(e)))},2718:(e,t,n)=>{n.d(t,{EE:()=>i,NO:()=>l,ir:()=>s});var r=n(8531),a=n(2533);const i=(e,t,n)=>{(0,r.reportInteraction)(((e,t)=>`${a.id.replace(/-/g,"_")}_${e}_${t}`)(e,t),n)},l={service_selection:"service_selection",service_details:"service_details",all:"all"},s={[l.service_selection]:{search_services_changed:"search_services_changed",service_selected:"service_selected",aggregated_metrics_toggled:"aggregated_metrics_toggled",add_to_filters:"add_to_filters"},[l.service_details]:{open_in_explore_clicked:"open_in_explore_clicked",action_view_changed:"action_view_changed",add_to_filters_in_breakdown_clicked:"add_to_filters_in_breakdown_clicked",select_field_in_breakdown_clicked:"select_field_in_breakdown_clicked",level_in_logs_volume_clicked:"level_in_logs_volume_clicked",layout_type_changed:"layout_type_changed",search_string_in_logs_changed:"search_string_in_logs_changed",pattern_removed:"pattern_removed",pattern_selected:"pattern_selected",pattern_field_clicked:"pattern_field_clicked",logs_visualization_toggle:"logs_visualization_toggle",logs_detail_filter_applied:"logs_detail_filter_applied",logs_popover_line_filter:"logs_popover_line_filter",logs_toggle_displayed_field:"logs_toggle_displayed_field",logs_clear_displayed_fields:"logs_clear_displayed_fields",value_breakdown_sort_change:"value_breakdown_sort_change",wasm_not_supported:"wasm_not_supported"},[l.all]:{interval_too_long:"interval_too_long"}}},833:(e,t,n)=>{n.d(t,{B:()=>i});var r=n(3241),a=n.n(r);const i=(e,t)=>{if(typeof e!=typeof t)return!1;const n=new Set(e),r=new Set(t);return n.size===r.size&&a().isEqual(n,r)}},2854:(e,t,n)=>{n.r(t),n.d(t,{DETECTED_FIELDS_CARDINALITY_NAME:()=>H,DETECTED_FIELDS_NAME_FIELD:()=>z,DETECTED_FIELDS_PARSER_NAME:()=>G,DETECTED_FIELDS_TYPE_NAME:()=>q,WRAPPED_LOKI_DS_UID:()=>W,WrappedLokiDatasource:()=>Q,default:()=>U});var r=n(7781),a=n(8531),i=n(1119),l=n(1269),s=n(9829),o=n(892),c=n(6001),d=n(3143),u=n(2533),p=n(5745),g=n(2344),m=n(8682);function v(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function h(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){v(e,t,n[t])}))}return e}function f(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function b(e){if(function(e){return void 0!==e.targets.find((e=>function(e){return e.trim().length>2&&!function(e,t){let n=!1;return g.K3.parse(e).iterate({enter:({type:e})=>{if(e.id===t)return n=!0,!1}}),n}(e,g.Yw)}(e.expr)))}(e))return!1;for(let n=0;n<e.targets.length;n++){var t;if(null===(t=e.targets[n].expr)||void 0===t?void 0:t.includes("avg_over_time"))return!1}return!0}const y="__stream_shard_number__",S=e=>e.replace("}",`, __stream_shard__=~"${y}"}`),w=(e,t)=>{if(void 0===t||0===t.length)return e.map((e=>f(h({},e),{expr:e.expr.replace(`, __stream_shard__=~"${y}"}`,"}")})));let n=t.join("|");return"-1"===n||1===t.length?(n="-1"===n?"":n,e.map((e=>f(h({},e),{expr:e.expr.replace(`, __stream_shard__=~"${y}"}`,`, __stream_shard__="${n}"}`)})))):e.map((e=>f(h({},e),{expr:e.expr.replace(new RegExp(`${y}`,"g"),n)})))};var O=n(2871);function E(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function x(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){E(e,t,n[t])}))}return e}function C(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function P(e,t,n){const a=(0,r.closestIdx)(t.values[n],e.values);return a<0?0:t.values[n]===e.values[a]&&null!=t.nanos&&null!=e.nanos?t.nanos[n]>e.nanos[a]?a+1:a:t.values[n]>e.values[a]?a+1:a}function j(e,t,n,r,a,i){const l=function(e,t,n,r){return e.nanos&&n.nanos?void 0!==e.values[t]&&e.values[t]===n.values[r]&&void 0!==e.nanos[t]&&e.nanos[t]===n.nanos[r]:void 0!==e.values[t]&&e.values[t]===n.values[r]}(e,n,r,i);return!!l&&(null==t||null==a||void 0!==t.values[n]&&t.values[n]===a.values[i])}function F(e,t,n){const r=t.filter((t=>t.name===e.name));return 1===r.length?r[0]:t[n]}const k="Summary: total bytes processed";function T(e,t){const n=e.find((e=>e.displayName===k)),r=t.find((e=>e.displayName===k));if(null!=r&&null!=n)return[{value:r.value+n.value,displayName:k,unit:n.unit}];const a=null!=r?r:n;return null!=a?[a]:[]}function _(e){return C(x({},e),{fields:e.fields.map((e=>C(x({},e),{values:e.values})))})}function L(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function D(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){L(e,t,n[t])}))}return e}function N(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}function $(e,t){const n=e.interpolateVariablesInQueries(t.targets,t.scopedVars).filter((e=>e.expr)).map((e=>N(D({},e),{expr:S(e.expr)})));return function(e,t,n){let a=!1,i={data:[],state:r.LoadingState.Streaming,key:(0,p.A)()},s=null,o=new Map,c=null;const d=(l,u,p,g)=>{let m=g,v=!1;null!=s&&(s.unsubscribe(),s=null);const h=()=>{i.state=a?r.LoadingState.Error:r.LoadingState.Done,l.next(i),l.complete()};if(a)return void h();const f=()=>{const e=Math.min(u+g,p.length);u<p.length&&e<=p.length?d(l,e,p,m):h()},b=e=>{try{if(e&&!function(e){var t,n,r;const a=e.errors?(null!==(n=e.errors[0].message)&&void 0!==n?n:"").toLowerCase():null!==(r=null===(t=e.error)||void 0===t?void 0:t.message)&&void 0!==r?r:"";if(a.includes("timeout"))return!0;if(a.includes("parse error"))throw new Error(a);return!1}(e))return!1}catch(e){return O.v.error(e),a=!0,!1}if(g>1)return A(`Possible time out, new group size ${g=Math.floor(Math.sqrt(g))}`),v=!0,d(l,u,p,g),!0;var t;const n=null!==(t=o.get(u))&&void 0!==t?t:0;return n>3?(a=!0,!1):(o.set(u,n+1),c=setTimeout((()=>{O.v.info(`Retrying ${u} (${n+1})`),d(l,u,p,g),c=null}),1500*Math.pow(2,n)),v=!0,!0)},y=function(e,t,n){return t===e.length?[-1]:e.slice(t,t+n)}(p,u,g);A(`Querying ${y.join(", ")}`);const S=N(D({},t),{targets:w(n,y)});t.requestId&&(S.requestId=`${t.requestId}_shard_${u}_${g}`),s=e.runQuery(S).subscribe({next:e=>{var t;((null!==(t=e.errors)&&void 0!==t?t:[]).length>0||null!=e.error)&&b(e)||(m=function(e,t,n){return Math.min(t,Math.max(Math.floor(.7*(n-e)),1))}(u+g,function(e,t){var n,r;if(!e.data.length)return t+1;const a=null===(r=e.data[0].meta)||void 0===r||null===(n=r.stats)||void 0===n?void 0:n.find((e=>"Summary: exec time"===e.displayName));if(a){const e=Math.round(a.value);return A(`${a.value}`),e<=1?Math.floor(1.5*t):e<6?Math.ceil(1.1*t):1===t?t:e<20?Math.ceil(.9*t):Math.floor(t/2)}return t}(e,g),p.length),m!==g&&A(`New group size ${m}`),i=function(e,t){if(!e)return C(x({},n=t),{data:n.data.map(_)});var n,a,i;t.data.forEach((t=>{const n=e.data.find((e=>function(e,t){var n,a,i,l,s,o;if(e.refId!==t.refId)return!1;if(null!=e.name&&null!=t.name&&e.name!==t.name)return!1;const c=null===(n=e.meta)||void 0===n?void 0:n.type;if(c!==(null===(a=t.meta)||void 0===a?void 0:a.type))return!1;if(c===r.DataFrameType.TimeSeriesMulti)return function(e,t){const n=e.fields.find((e=>e.type===r.FieldType.number)),a=t.fields.find((e=>e.type===r.FieldType.number));return void 0!==n&&void 0!==a&&(null==e.name&&(e.name=JSON.stringify(n.labels)),null==t.name&&(t.name=JSON.stringify(a.labels)),e.name===t.name)}(e,t);const d=null===(l=e.meta)||void 0===l||null===(i=l.custom)||void 0===i?void 0:i.frameType,u=null===(o=t.meta)||void 0===o||null===(s=o.custom)||void 0===s?void 0:s.frameType;return"LabeledTimeValues"===d&&"LabeledTimeValues"===u||d===u}(e,t)));n?function(e,t){var n,a;const i=e.fields.find((e=>e.type===r.FieldType.time)),l=e.fields.find((e=>e.type===r.FieldType.string&&"id"===e.name)),s=t.fields.find((e=>e.type===r.FieldType.time)),o=t.fields.find((e=>e.type===r.FieldType.string&&"id"===e.name));if(!i||!s)return void O.v.error(new Error("Time fields not found in the data frames"));var c;const d=null!==(c=null==s?void 0:s.values.slice(0))&&void 0!==c?c:[],u=Math.max(e.fields.length,t.fields.length);for(let n=0;n<d.length;n++){const a=P(i,s,n),c=j(i,l,a,s,o,n);for(let i=0;i<u;i++){if(!e.fields[i])continue;const l=F(e.fields[i],t.fields,i);if(l)if(c){if(e.fields[i].type===r.FieldType.time)continue;var p;e.fields[i].type===r.FieldType.number?e.fields[i].values[a]=(null!==(p=e.fields[i].values[a])&&void 0!==p?p:0)+l.values[n]:e.fields[i].type===r.FieldType.other?"object"==typeof l.values[n]?e.fields[i].values[a]=x({},e.fields[i].values[a],l.values[n]):null!=l.values[n]&&(e.fields[i].values[a]=l.values[n]):e.fields[i].values[a]=l.values[n]}else if(void 0!==l.values[n]){var g,m;e.fields[i].values.splice(a,0,l.values[n]),l.nanos&&(e.fields[i].nanos=null!==(m=e.fields[i].nanos)&&void 0!==m?m:new Array(e.fields[i].values.length-1).fill(0),null===(g=e.fields[i].nanos)||void 0===g||g.splice(a,0,l.nanos[n]))}}}var v,h;e.length=e.fields[0].values.length,e.meta=C(x({},e.meta),{stats:T(null!==(v=null===(n=e.meta)||void 0===n?void 0:n.stats)&&void 0!==v?v:[],null!==(h=null===(a=t.meta)||void 0===a?void 0:a.stats)&&void 0!==h?h:[])})}(n,t):e.data.push(_(t))}));const l=[...null!==(a=e.errors)&&void 0!==a?a:[],...null!==(i=t.errors)&&void 0!==i?i:[]];var s;l.length>0&&(e.errors=l);const o=null!==(s=e.error)&&void 0!==s?s:t.error;var c,d;null!=o&&(e.error=o);const u=[...null!==(c=e.traceIds)&&void 0!==c?c:[],...null!==(d=t.traceIds)&&void 0!==d?d:[]];return u.length>0&&(e.traceIds=u),e}(i,e))},complete:()=>{v||(i.data.length&&l.next(i),f())},error:e=>{O.v.error(e,{msg:"failed to shard"}),l.next(i),b()||f()}})},u=n=>{s=e.query(t).subscribe({next:e=>{i=e},complete:()=>{n.next(i)},error:e=>{O.v.error(e,{msg:"runNonSplitRequest subscription error"}),n.error(i)}})},v=new l.Observable((r=>{const i=(e=>{const t=(0,m.QH)(e,[g.MD]);return t.length>0?e.substring(t[0].from,t[0].to).replace(`, __stream_shard__=~"${y}"}`,"}"):""})(n[0].expr);return(0,m.T0)(i)?(e.languageProvider.fetchLabelValues("__stream_shard__",{timeRange:t.range,streamSelector:i||void 0}).then((e=>{const t=e.map((e=>parseInt(e,10)));t&&t.length?(t.sort(((e,t)=>t-e)),A(`Querying ${t.join(", ")} shards`),d(r,0,t,function(e){return Math.floor(Math.sqrt(e.length))}(t))):(O.v.warn("Shard splitting not supported. Issuing a regular query."),u(r))})).catch((e=>{O.v.error(e,{msg:"failed to fetch label values for __stream_shard__"}),u(r)})),()=>{a=!0,c&&clearTimeout(c),null!=s&&(s.unsubscribe(),s=null)}):(A(`Skipping invalid selector: ${i}`),void r.complete())}));return v}(e,t,n)}const I=Boolean(localStorage.getItem(`${u.id}.sharding_debug_enabled`));function A(e){I&&console.log(e)}var B=n(8831),V=n(7918);function M(e,t,n,r,a,i,l){try{var s=e[i](l),o=s.value}catch(e){return void n(e)}s.done?t(o):Promise.resolve(o).then(r,a)}function R(e){return function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function l(e){M(i,r,a,l,s,"next",e)}function s(e){M(i,r,a,l,s,"throw",e)}l(void 0)}))}}const W="wrapped-loki-ds-uid",z="name",H="cardinality",G="parser",q="type";class Q extends i.UU{query(e){return new l.Observable((t=>{var n;if(!(null===(n=e.scopedVars)||void 0===n?void 0:n.__sceneObject))throw new Error("Scene object not found in request");var r,i=this;(0,a.getDataSourceSrv)().get((0,s.U4)(e.scopedVars.__sceneObject.valueOf())).then((r=R((function*(n){var r;if(!(n instanceof a.DataSourceWithBackend))throw new Error("Invalid datasource!");e.targets=null===(r=e.targets)||void 0===r?void 0:r.map((e=>(e.datasource=n,e)));const l=new Set;if(e.targets.forEach((e=>{var t;l.add(null!==(t=e.resource)&&void 0!==t?t:"")})),1!==l.size)throw new Error("A request cannot contain queries to multiple endpoints");switch(e.targets[0].resource){case"volume":yield i.getVolume(e,n,t);break;case"patterns":yield i.getPatterns(e,n,t);break;case"detected_labels":yield i.getDetectedLabels(e,n,t);break;case"detected_fields":yield i.getDetectedFields(e,n,t);break;case"labels":yield i.getLabels(e,n,t);break;default:i.getData(e,n,t)}})),function(e){return r.apply(this,arguments)}))}))}getData(e,t,n){const r=a.config.featureToggles.exploreLogsShardSplitting;return(!1!==b(e)&&r?$(t,e):t.query(e)).subscribe(n),n}getPatterns(e,t,n){var a=this;return R((function*(){const i=e.targets.filter((e=>"patterns"===e.resource));if(1!==i.length)throw new Error("Patterns query can only have a single target!");const{interpolatedTarget:l,expression:s}=a.interpolate(t,i,e);n.next({data:[],state:r.LoadingState.Loading});try{var o;const a=t.getResource("patterns",{query:s,start:e.range.from.utc().toISOString(),end:e.range.to.utc().toISOString()},{requestId:null!==(o=e.requestId)&&void 0!==o?o:"patterns",headers:{"X-Query-Tags":`Source=${B.s_}`}}),i=yield a,d=null==i?void 0:i.data;let u=-1/0,p=0;var c;const g=null!==(c=null==d?void 0:d.map((e=>{const t=[],n=[];let a=0;return e.samples.forEach((([e,r])=>{t.push(1e3*e),n.push(r),r>u&&(u=r),r<p&&(p=r),r>u&&(u=r),r<p&&(p=r),a+=r})),(0,r.createDataFrame)({refId:l.refId,name:e.pattern,fields:[{name:"time",type:r.FieldType.time,values:t,config:{}},{name:e.pattern,type:r.FieldType.number,values:n,config:{}}],meta:{preferredVisualisationType:"graph",custom:{sum:a}}})})))&&void 0!==c?c:[];g.sort(((e,t)=>{var n,r,a,i;return(null===(r=t.meta)||void 0===r||null===(n=r.custom)||void 0===n?void 0:n.sum)-(null===(i=e.meta)||void 0===i||null===(a=i.custom)||void 0===a?void 0:a.sum)})),n.next({data:g,state:r.LoadingState.Done})}catch(e){n.next({data:[],state:r.LoadingState.Error})}return n}))()}interpolate(e,t,n){const r=e.interpolateVariablesInQueries(t,n.scopedVars);if(!r.length)throw new Error("Datasource failed to interpolate query!");const a=r[0];return{interpolatedTarget:a,expression:a.expr}}getDetectedLabels(e,t,n){var a=this;return R((function*(){const i=e.targets.filter((e=>"detected_labels"===e.resource));if(1!==i.length)throw new Error("Detected labels query can only have a single target!");let{interpolatedTarget:l,expression:s}=a.interpolate(t,i,e);s.trim()===`{${V.tk}}`&&(s=""),n.next({data:[],state:r.LoadingState.Loading});try{var d,u,p;const a=yield t.getResource("detected_labels",{query:s,start:e.range.from.utc().toISOString(),end:e.range.to.utc().toISOString()},{requestId:null!==(p=e.requestId)&&void 0!==p?p:"detected_labels",headers:{"X-Query-Tags":`Source=${B.s_}`}}),{labelName:i}=(0,o.W6)(),g=null===(u=a.detectedLabels)||void 0===u||null===(d=u.filter((e=>i!==e.label&&!c.rm.includes(e.label))))||void 0===d?void 0:d.sort(((e,t)=>(0,c.p_)(e,t))),m=null==g?void 0:g.map((e=>({name:e.label,values:[e.cardinality]}))),v=(0,r.createDataFrame)({refId:l.refId,fields:null!=m?m:[]});n.next({data:[v],state:r.LoadingState.Done})}catch(e){n.next({data:[],state:r.LoadingState.Error})}return n}))()}getDetectedFields(e,t,n){var a=this;return R((function*(){const i=e.targets.filter((e=>"detected_fields"===e.resource));if(1!==i.length)throw new Error("Detected fields query can only have a single target!");n.next({data:[],state:r.LoadingState.Loading});const{interpolatedTarget:l,expression:s}=a.interpolate(t,i,e);try{var o,d;const a=yield t.getResource("detected_fields",{query:s,start:e.range.from.utc().toISOString(),end:e.range.to.utc().toISOString()},{requestId:null!==(d=e.requestId)&&void 0!==d?d:"detected_fields",headers:{"X-Query-Tags":`Source=${B.s_}`}}),i={name:z,type:r.FieldType.string,values:[],config:{}},u={name:H,type:r.FieldType.number,values:[],config:{}},p={name:G,type:r.FieldType.string,values:[],config:{}},g={name:q,type:r.FieldType.string,values:[],config:{}};null===(o=a.fields)||void 0===o||o.forEach((e=>{var t;c.$R.includes(e.label)||(i.values.push(e.label),u.values.push(e.cardinality),p.values.push((null===(t=e.parsers)||void 0===t?void 0:t.length)?e.parsers.join(", "):"structuredMetadata"),g.values.push(e.type))}));const m=(0,r.createDataFrame)({refId:l.refId,fields:[i,u,p,g]});n.next({data:[m],state:r.LoadingState.Done})}catch(e){O.v.error(e,{msg:"Detected fields error"}),n.next({data:[],state:r.LoadingState.Error})}return n}))()}getVolume(e,t,n){return R((function*(){if(1!==e.targets.length)throw new Error("Volume query can only have a single target!");const a=e.targets[0],i=a.primaryLabel;if(!i)throw new Error("Primary label is required for volume queries!");const l=t.interpolateVariablesInQueries([a],e.scopedVars)[0].expr.replace(".*.*",".+");n.next({data:[],state:r.LoadingState.Loading});try{var s,o,c;const a=yield t.getResource("index/volume",{query:l,start:e.range.from.utc().toISOString(),end:e.range.to.utc().toISOString(),limit:5e3},{requestId:null!==(c=e.requestId)&&void 0!==c?c:"volume",headers:{"X-Query-Tags":`Source=${B.s_}`}});null==a||a.data.result.sort(((e,t)=>{const n=e.value[1],r=t.value[1];return Number(r)-Number(n)}));const u=(0,r.createDataFrame)({fields:[{name:d.OX,values:null==a||null===(s=a.data.result)||void 0===s?void 0:s.map((e=>e.metric[i]))},{name:"volume",values:null==a||null===(o=a.data.result)||void 0===o?void 0:o.map((e=>Number(e.value[1])))}]});n.next({data:[u]})}catch(e){O.v.error(e),n.next({data:[],state:r.LoadingState.Error})}return n.complete(),n}))()}getLabels(e,t,n){return R((function*(){if(1!==e.targets.length)throw new Error("Volume query can only have a single target!");try{var a;const i=yield t.getResource("labels",{start:e.range.from.utc().toISOString(),end:e.range.to.utc().toISOString()},{requestId:null!==(a=e.requestId)&&void 0!==a?a:"labels",headers:{"X-Query-Tags":`Source=${B.s_}`}}),l=(0,r.createDataFrame)({fields:[{name:"labels",values:null==i?void 0:i.data}]});n.next({data:[l],state:r.LoadingState.Done})}catch(e){n.next({data:[],state:r.LoadingState.Error})}return n.complete(),n}))()}testDatasource(){return Promise.resolve({status:"success",message:"Data source is working",title:"Success"})}constructor(e,t){super(e,t)}}const U=function(){i.Go.registerRuntimeDataSource({dataSource:new Q("wrapped-loki-ds",W)})}},9055:(e,t,n)=>{n.d(t,{_J:()=>d,wy:()=>u});var r=n(1119),a=n(7841),i=n(866),l=n(6939),s=n(4750),o=n(227);function c(e){const t=r.jh.getAncestor(e,a.PR);r.jh.findAllObjects(t,(e=>e instanceof i.p)).forEach((e=>e.forceRender())),r.jh.findDescendents(t,l.yj).forEach((e=>e.forceRender()))}function d(e,t,n){const r=(0,s.S9)(n).getValue();(0,o.OB)(r,e,t),c(n)}function u(e,t,n){const r=(0,s.S9)(n).getValue();(0,o.cC)(r,e,t),c(n)}},7097:(e,t,n)=>{n.d(t,{JI:()=>x,Jl:()=>P,OE:()=>O,Qg:()=>y,Ri:()=>S,ZD:()=>j,Zp:()=>w,k$:()=>E,ph:()=>C});var r=n(7781),a=n(2007),i=n(1119),l=n(9829),s=n(558),o=n(3143),c=n(5183),d=n(1269),u=n(9507),p=n(4750),g=n(7232),m=n(2871),v=n(1194);const h=e=>{if(e&&Object.values(r.ReducerID).includes(e))return e};function f(e){switch(e){case"json":return"json";case"logfmt":return"logfmt";case"":case"structuredMetadata":return"structuredMetadata";default:return"mixed"}}function b(e){switch(e){case"int":case"float":case"duration":case"boolean":case"bytes":return e;default:return"string"}}function y(e){var t;const n=new Set(null!==(t=null==e?void 0:e.map((e=>e.toString())))&&void 0!==t?t:[]);n.delete("structuredMetadata");const r=Array.from(n);return 1===r.length?f(r[0]):0===n.size?"structuredMetadata":"mixed"}function S(e,t){var n;const r=(0,u.rD)(t),a=null==r?void 0:r.fields[2],i=null==r?void 0:r.fields[0],l=null==i?void 0:i.values.indexOf(e);var s;const o=void 0!==l&&-1!==l?f(null!==(s=null==a||null===(n=a.values)||void 0===n?void 0:n[l])&&void 0!==s?s:""):void 0;return void 0===o?(m.v.warn("missing parser, using mixed format for",{fieldName:e}),"mixed"):o}function w(e,t,n,r,o){return(u,p)=>{const g=h(r.state.sortBy),m=i.d0.timeseries().setOption("legend",{showLegend:!1}).setCustomFieldConfig("fillOpacity",9).setTitle(e(u)).setData(new i.Es({transformations:[()=>function(e){return t=>t.pipe((0,d.map)((()=>[e])))}(u)]})).setColor({mode:"fixed",fixedColor:(0,l.Vy)(p)}).setOverrides(c.jC).setHeaderActions([new s.oR({frame:u,variableName:n}),new v.g({frame:u,fieldName:e(u),labelName:o})]);return t===a.DrawStyle.Bars&&m.setCustomFieldConfig("stacking",{mode:a.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setOverrides(c.jC).setCustomFieldConfig("drawStyle",a.DrawStyle.Bars),g&&(m.setOption("legend",{showLegend:!0,calcs:[g]}),m.setDisplayName(" ")),new i.xK({body:m.build()})}}function O(e,t,n){const r=e?function(e,t,n=0){var r;const a=null===(r=t.fields.find((e=>"labelTypes"===e.name)))||void 0===r?void 0:r.values[n];if(!a)return null;switch(a[e]){case"I":return g.H.Indexed;case"S":return g.H.StructuredMetadata;case"P":return g.H.Parsed;default:return null}}(t,e):g.H.Parsed;if(r)return function(e,t,n){switch(e){case g.H.Indexed:return o.MB;case g.H.Parsed:return o.mB;case g.H.StructuredMetadata:return t===o.e4?o._Y:o._P;default:{const n=new Error(`Invalid label type for ${t}`);throw m.v.error(n,{type:e,msg:`Invalid label type for ${t}`}),n}}}(r,t);const a=S(t,n);return"structuredMetadata"===a?o._P:(m.v.warn("unable to determine label variable, falling back to parsed field",{key:t,parserForThisField:null!=a?a:""}),o.mB)}function E(e){return y(e.state.filters.map((e=>(0,p.bu)(e).parser)))}function x(e){return"duration"===e||"bytes"===e||"float"===e}function C(e,t){var n;const r=null==t?void 0:t.fields[0],a=null==t?void 0:t.fields[3],i=null==r?void 0:r.values.indexOf(e);return void 0!==i&&-1!==i?b(null==a||null===(n=a.values)||void 0===n?void 0:n[i]):void 0}function P(e,t,n){var r,a;const i=null==n?void 0:n.fields[2],l=null==n?void 0:n.fields[0],s=null==n?void 0:n.fields[3],o=null==l?void 0:l.values.indexOf(e),c=void 0!==o&&-1!==o?f(null==i||null===(r=i.values)||void 0===r?void 0:r[o]):"mixed",d=void 0!==o&&-1!==o?b(null==s||null===(a=s.values)||void 0===a?void 0:a[o]):void 0,u=t.state.filters.map((e=>{var t;const n=null==l?void 0:l.values.indexOf(e.key),r=(0,p.bu)(e);if(r.parser)return r.parser;var a;const s=void 0!==n&&-1!==n?f(null!==(a=null==i||null===(t=i.values)||void 0===t?void 0:t[n])&&void 0!==a?a:"mixed"):void 0;return null!=s?s:"mixed"}));let g="",m="";return"structuredMetadata"===c?m=`| ${e}!=""`:g=`| ${e}!=""`,function(e,t){return t.fieldType&&["bytes","duration"].includes(t.fieldType)?`avg_over_time(${(0,p.DX)(t)} | unwrap `+t.fieldType+`(${e}) | __error__="" [$__auto]) by ()`:t.fieldType&&"float"===t.fieldType?`avg_over_time(${(0,p.DX)(t)} | unwrap `+e+' | __error__="" [$__auto]) by ()':`sum by (${e}) (count_over_time(${(0,p.DX)(t)} [$__auto]))`}(e,{structuredMetadataToAdd:m,fieldExpressionToAdd:g,parser:y([...u,c]),fieldType:d})}function j(e){return"string"==typeof e?e.replace(/'/g,"\\\\'"):e}},6001:(e,t,n)=>{n.d(t,{$R:()=>l,dD:()=>i,p_:()=>a,rd:()=>o,rm:()=>s});var r=n(3143);function a(e,t){return 1===e.cardinality?1:1===t.cardinality?-1:e.cardinality-t.cardinality}function i(e){const t=[...e];e.includes(r.e4)||t.unshift(r.e4);const n=t.map((e=>({label:e,value:String(e)})));return[{label:"All",value:r.To},...n]}const l=["level_extracted",r.e4,"level"],s=["__aggregated_metric__","__stream_shard__"];function o(e){const t=[...e].map((e=>({label:e,value:String(e)})));return[{label:"All",value:r.To},...t]}},1383:(e,t,n)=>{n.d(t,{Ex:()=>u,H7:()=>d,PE:()=>g,de:()=>c,pC:()=>o});var r=n(2007),a=n(3143),i=n(558),l=n(4750),s=n(4793);function o(e,t,n,a){if(n===r.SeriesVisibilityChangeMode.ToggleSelection){const n=null!=t?t:[];return 1===n.length&&n.includes(e)?[]:[e]}let i=(null==t?void 0:t.length)?t:a;return i.includes(e)?i.filter((t=>t!==e)):[...i,e]}function c(e){return e.map((e=>{var t;return null!==(t=d(e))&&void 0!==t?t:"logs"}))}function d(e){var t;const n=null===(t=e.fields[1])||void 0===t?void 0:t.labels;if(!n)return null;const r=Object.keys(n);return 0===r.length?null:n[r[0]]}function u(e,t){const n=(0,l.iw)(t),r=n.state.filters.filter((e=>e.operator===s.w.Equal)).map((e=>p(e.value))),a=n.state.filters.filter((e=>e.operator===s.w.NotEqual)).map((e=>p(e.value)));return e.filter((e=>!a.includes(e)&&(0===r.length||r.includes(e))))}function p(e){return'""'===e?"logs":e}function g(e,t){const n=(0,l.iw)(t),r=0===n.state.filters.length,o=n.state.filters.find((t=>t.value===e&&t.operator===s.w.Equal));let c;return"logs"===e&&(e='""'),r||!o?((0,i.PT)(a.e4,e,"include",t),c="add"):((0,i.Qt)(a.e4,e,"toggle",t),c="remove"),c}},2871:(e,t,n)=>{n.d(t,{v:()=>s});var r=n(8531);function a(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function i(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){a(e,t,n[t])}))}return e}const l={app:n(2533).id,version:"1.0.4"},s={info:(e,t)=>{const n=i({},l,t);console.log(e,n),o(e,n)},warn:(e,t)=>{const n=i({},l,t);console.warn(e,n),c(e,n)},error:(e,t)=>{const n=i({},l,t);console.error(e,n),d(e,n)}},o=(e,t)=>{try{(0,r.logInfo)(e,t)}catch(e){console.warn("Failed to log faro event!")}},c=(e,t)=>{try{(0,r.logWarning)(e,t)}catch(n){console.warn("Failed to log faro warning!",{msg:e,context:t})}},d=(e,t)=>{let n=t;try{!function(e,t){if("object"==typeof e&&null!==e&&("object"==typeof e&&Object.keys(e).forEach((n=>{const r=e[n];"string"!=typeof r&&"boolean"!=typeof r&&"number"!=typeof r||(t[n]=r.toString())})),u(e)))if("object"==typeof e.data&&null!==e.data)try{t.data=JSON.stringify(e.data)}catch(e){}else"string"!=typeof e.data&&"boolean"!=typeof e.data&&"number"!=typeof e.data||(t.data=e.data.toString())}(e,n),e instanceof Error?(0,r.logError)(e,n):"string"==typeof e?(0,r.logError)(new Error(e),n):e&&"object"==typeof e?n.msg?(0,r.logError)(new Error(n.msg),n):(0,r.logError)(new Error("error object"),n):(0,r.logError)(new Error("unknown error"),n)}catch(t){console.error("Failed to log faro error!",{err:e,context:n})}},u=e=>"data"in e},6949:(e,t,n)=>{let r;function a(){r||(r=new i)}n.d(t,{JO:()=>l,rX:()=>a});class i{getServiceSceneState(){return this.serviceSceneState}setPatternsCount(e){this.serviceSceneState||(this.serviceSceneState={}),this.serviceSceneState.patternsCount=e}setLabelsCount(e){this.serviceSceneState||(this.serviceSceneState={}),this.serviceSceneState.labelsCount=e}setFieldsCount(e){this.serviceSceneState||(this.serviceSceneState={}),this.serviceSceneState.fieldsCount=e}setServiceSceneState(e){this.serviceSceneState={patternsCount:e.patternsCount,labelsCount:e.labelsCount,fieldsCount:e.fieldsCount,loading:e.loading}}constructor(){var e,t;t=void 0,(e="serviceSceneState")in this?Object.defineProperty(this,e,{value:t,enumerable:!0,configurable:!0,writable:!0}):this[e]=t}}function l(){return r}},8835:(e,t,n)=>{n.d(t,{Ns:()=>S,Vt:()=>b,ad:()=>y,fg:()=>h,jY:()=>f});var r=n(7841),a=n(3143),i=n(6949),l=n(8531),s=n(892),o=n(1119),c=n(7781),d=n(7608),u=n(2871),p=n(8831);function g(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}let m;function v(e,t){return c.urlUtil.renderUrl(e,function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){g(e,t,n[t])}))}return e}({},Object.entries(c.urlUtil.getUrlSearchParams()).reduce(((e,[t,n])=>(s.tm.includes(t)&&(e[t]=n),e)),{}),e)}(t))}function h(e,t,n){const l=o.jh.getAncestor(n,r.PR);if(l){var c,g;const r=null===(c=l.state.routeMatch)||void 0===c?void 0:c.params.labelName,o=null===(g=l.state.routeMatch)||void 0===g?void 0:g.params.labelValue;if(r&&o){let l=function(e,t,n,r="service"){return e===a.To&&t===s._J.label?(0,p._F)(`${s.G3.explore}/${r}/${(0,d.uu)(n)}/${s.G3.labels}`):e===a.To&&t===s._J.field?(0,p._F)(`${s.G3.explore}/${r}/${(0,d.uu)(n)}/${s.G3.fields}`):(0,p._F)(`${s.G3.explore}/${r}/${(0,d.uu)(n)}/${t}/${(0,d.uu)(e)}`)}(t,e,o,r);const c=v(l);return n&&(0,i.JO)().setServiceSceneState(n.state),void y(c)}u.v.warn("missing url params",{urlLabelName:null!=r?r:"",urlLabelValue:null!=o?o:""})}}function f(e,t){y(v(s.bw.logs(t,e)))}function b(e,t,n){var a,l;const c=o.jh.getAncestor(t,r.PR),u=null===(a=c.state.routeMatch)||void 0===a?void 0:a.params.labelValue,g=null===(l=c.state.routeMatch)||void 0===l?void 0:l.params.labelName;if(u){const r=v((0,p._F)(`${s.G3.explore}/${g}/${(0,d.uu)(u)}/${e}`),n);t&&(0,i.JO)().setServiceSceneState(t.state),y(r)}}function y(e){m=e,l.locationService.push(e)}function S(){const e=l.locationService.getLocation(),t=(0,s.qe)(s.bw.explore()),n=e.pathname+e.search,r=l.locationService.getSearch();t===n||n.includes(t)||(r.get("var-filters")?y(t):(m&&l.locationService.replace(m),l.locationService.push(t)))}},5183:(e,t,n)=>{n.d(t,{rS:()=>y,FH:()=>b,HF:()=>S,jC:()=>g,ZC:()=>m,Cw:()=>h});var r=n(7781),a=n(1119),i=n(1269),l=n(2854);class s extends a.dt{runQueries(){const e=a.jh.getTimeRange(this);this.runWithTimeRange(e)}constructor(e){super(e)}}var o=n(2007),c=n(1383);function d(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function u(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){d(e,t,n[t])}))}return e}const p="logs";function g(e){e.matchFieldsWithName("info").overrideColor({mode:"fixed",fixedColor:"semi-dark-green"}),e.matchFieldsWithName("debug").overrideColor({mode:"fixed",fixedColor:"semi-dark-blue"}),e.matchFieldsWithName("error").overrideColor({mode:"fixed",fixedColor:"semi-dark-red"}),e.matchFieldsWithName("warn").overrideColor({mode:"fixed",fixedColor:"semi-dark-orange"}),e.matchFieldsWithName("logs").overrideColor({mode:"fixed",fixedColor:"darkgray"})}function m(e){return e.setCustomFieldConfig("stacking",{mode:o.StackingMode.Normal}).setCustomFieldConfig("fillOpacity",100).setCustomFieldConfig("lineWidth",0).setCustomFieldConfig("pointSize",0).setCustomFieldConfig("drawStyle",o.DrawStyle.Bars).setOverrides(g)}function v(e,t){t.match({id:r.FieldMatcherID.byNames,options:{mode:"exclude",names:e,prefix:"All except:",readOnly:!0}}).overrideCustomFieldConfig("hideFrom",{legend:!1,tooltip:!1,viz:!0});const n=t.build();n[n.length-1].__systemRef="hideSeriesFrom"}function h(e,t,n){const r=(0,c.Ex)((0,c.de)(t),n);if(null==r?void 0:r.length){const t=m(a.No.timeseries()).setOverrides(v.bind(null,r));t instanceof a.OS&&e.onFieldConfigChange(t.build(),!0)}}function f(){return e=>e.pipe((0,i.map)((e=>e.map((e=>(e.fields[1].config.displayNameFromDS||(e.fields[1].config.displayNameFromDS=p),e))).sort(((e,t)=>{const n=e.fields[1].config.displayNameFromDS,r=(null==n?void 0:n.includes("error"))?4:(null==n?void 0:n.includes("warn"))?3:(null==n?void 0:n.includes("info"))?2:1,a=t.fields[1].config.displayNameFromDS;return r-((null==a?void 0:a.includes("error"))?4:(null==a?void 0:a.includes("warn"))?3:(null==a?void 0:a.includes("info"))?2:1)})))))}function b(e){return new s({datasource:{uid:l.WRAPPED_LOKI_DS_UID},queries:e})}function y(e,t){return e.find((e=>{var t;return null===(t=e.legendFormat)||void 0===t?void 0:t.toLowerCase().includes("level")}))?new a.Es({$data:S(u({datasource:{uid:l.WRAPPED_LOKI_DS_UID},queries:e},t)),transformations:[f]}):S(u({queries:e},t))}function S(e){return new a.dt(u({datasource:{uid:l.WRAPPED_LOKI_DS_UID},queries:[]},e))}},8831:(e,t,n)=>{n.d(t,{Gy:()=>a,_F:()=>i,s_:()=>r});const r=n(2533).id,a=`/a/${r}`;function i(e){return`${a}/${e}`}},7918:(e,t,n)=>{n.d(t,{$k:()=>h,BM:()=>p,E3:()=>S,M3:()=>x,VW:()=>b,W3:()=>C,ZX:()=>y,l:()=>g,sT:()=>j,tk:()=>v,vC:()=>P});var r=n(7841),a=n(3143),i=n(3241),l=n(4750),s=n(8831),o=n(4793);function c(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}function d(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){c(e,t,n[t])}))}return e}function u(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var n=Object.keys(e);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(e);n.push.apply(n,r)}return n}(Object(t)).forEach((function(n){Object.defineProperty(e,n,Object.getOwnPropertyDescriptor(t,n))})),e}const p=(e,t,n)=>u(d(u(d({},m),{resource:t,refId:t}),n),{datasource:{uid:a.gR},expr:e}),g=(e,t)=>u(d({},m,t),{expr:e}),m={refId:"A",queryType:"range",editorMode:"code",supportingQueryType:s.s_},v='__placeholder__=""',h=(e,t,n,r)=>p(e,t,u(d({},r),{primaryLabel:n}));function f(e){const t=e.filter((e=>e.operator===o.w.Equal));return{negative:e.filter((e=>e.operator===o.w.NotEqual)),positiveGroups:(0,i.groupBy)(t,(e=>e.key))}}function b(e){let{positiveFilters:t,negative:n}=function(e){const{negative:t,positiveGroups:n}=f(e);let r=[];for(const e in n){const t=n[e].map((e=>e.value));r.push(1===t.length?w(n[e][0]):E(e,t))}return{positiveFilters:r,negative:t}}(e);const r=n.map((e=>w(e))).join(", ");return(0,i.trim)(`${t.join(", ")}, ${r}`," ,")||v}function y(e){const t=e.filter((e=>e.operator===o.w.Equal)),n=e.filter((e=>e.operator===o.w.NotEqual)),a=e.filter((e=>r.nB.includes(e.operator))),s=(0,i.groupBy)(t,(e=>e.key));let c="";for(const e in s)c+=" | "+s[e].map((e=>`${O(e)}`)).join(" or ");return`${c} ${n.map((e=>`| ${O(e)}`)).join(" ")} ${a.map((e=>`| ${function(e){const t=(0,l.bu)(e).value;return`${e.key}${e.operator}${t}`}(e)}`)).join(" ")}`.trim()}function S(e){const t=e.filter((e=>e.operator===o.w.Equal)),n=e.filter((e=>e.operator===o.w.NotEqual)),r=(0,i.groupBy)(t,(e=>e.key));let a="";for(const e in r)a+=" | "+r[e].map((e=>`${w(e)}`)).join(" or ");return`${a} ${n.map((e=>`| ${w(e)}`)).join(" ")}`.trim()}function w(e){return e.value===a.ZO?`${e.key}${e.operator}${e.value}`:`${e.key}${e.operator}\`${e.value}\``}function O(e){const t=(0,l.bu)(e).value;return t===a.ZO?`${e.key}${e.operator}${t}`:`${e.key}${e.operator}\`${t}\``}function E(e,t){return`${e}=~"${t.join("|")}"`}function x(e){const t=e.filter((e=>"exclude"===e.type)).map((e=>`!> \`${e.pattern}\``)).join(" ").trim(),n=e.filter((e=>"include"===e.type));let r="";return n.length>0&&(r=1===n.length?`|> \`${n[0].pattern}\``:`|>  ${n.map((e=>`\`${e.pattern}\``)).join(" or ")}`),`${t} ${r}`.trim()}function C(e){const{positiveGroups:t,negative:n}=f(e.state.filters),r=[];for(const e in t){const n=t[e].map((e=>e.value));1===n.length?r.push({key:e,value:t[e][0].value,operator:"="}):r.push({key:e,value:n.join("|"),operator:"=~"})}return n.forEach((e=>{r.push(e)})),r}function P(e){return".+"!==e&&".*"!==e.substring(0,2)?`.*${e}.*`:e}function j(e){return".*"===e.substring(0,2)&&".*"===e.slice(-2)?e.slice(2).slice(0,-2):e}},892:(e,t,n)=>{n.d(t,{FT:()=>y,G3:()=>r,HU:()=>v,KL:()=>h,NX:()=>x,W6:()=>S,XJ:()=>E,Zt:()=>f,_J:()=>a,bw:()=>g,er:()=>w,mC:()=>m,qe:()=>O,tm:()=>b});var r,a,i=n(7781),l=n(3143),s=n(8531),o=n(7608),c=n(4750),d=n(2871),u=n(8831);function p(e,t,n){return t in e?Object.defineProperty(e,t,{value:n,enumerable:!0,configurable:!0,writable:!0}):e[t]=n,e}!function(e){e.explore="explore",e.logs="logs",e.labels="labels",e.patterns="patterns",e.fields="fields"}(r||(r={})),function(e){e.field="field",e.label="label"}(a||(a={}));const g={explore:()=>(0,u._F)("explore"),logs:(e,t="service")=>(0,u._F)(`explore/${t}/${(0,o.uu)(e)}/logs`),fields:(e,t="service")=>(0,u._F)(`explore/${t}/${(0,o.uu)(e)}/fields`),patterns:(e,t="service")=>(0,u._F)(`explore/${t}/${(0,o.uu)(e)}/patterns`),labels:(e,t="service")=>(0,u._F)(`explore/${t}/${(0,o.uu)(e)}/labels`)},m={label:(e,t="service",n)=>(0,u._F)(`explore/${t}/${(0,o.uu)(e)}/label/${n}`),field:(e,t="service",n)=>(0,u._F)(`explore/${t}/${(0,o.uu)(e)}/field/${n}`)},v={explore:(0,u._F)("explore"),logs:(0,u._F)("explore/:labelName/:labelValue/logs"),fields:(0,u._F)("explore/:labelName/:labelValue/fields"),patterns:(0,u._F)("explore/:labelName/:labelValue/patterns"),labels:(0,u._F)("explore/:labelName/:labelValue/labels")},h={field:(0,u._F)("explore/:labelName/:labelValue/field/:breakdownLabel"),label:(0,u._F)("explore/:labelName/:labelValue/label/:breakdownLabel")},f=["from","to",`var-${l.EY}`,`var-${l.MB}`],b=["from","to","mode","urlColumns","visualizationType","selectedLine","displayedFields",l.uw,`var-${l.uw}`,`var-${l.EY}`,`var-${l.MB}`,`var-${l.mB}`,`var-${l._Y}`,`var-${l.LI}`,`var-${l.Jg}`,`var-${l.EY}`,`var-${l.WM}`,`var-${l._P}`];function y(){const e=s.locationService.getLocation();return e.pathname.slice(e.pathname.lastIndexOf("/")+1,e.pathname.length)}function S(){const e=s.locationService.getLocation(),t=e.pathname.slice(e.pathname.indexOf("/a/grafana-lokiexplore-app/explore")+34+1).split("/");let n=t[0];const r=t[1],a=t[3];return n===l.OX&&(n=l.ky),{labelName:n,labelValue:r,breakdownLabel:a}}function w(){const e=s.locationService.getLocation().pathname.split("/");return e[e.length-2]}function O(e,t){return i.urlUtil.renderUrl(e,function(e){return function(e){for(var t=1;t<arguments.length;t++){var n=null!=arguments[t]?arguments[t]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(e){return Object.getOwnPropertyDescriptor(n,e).enumerable})))),r.forEach((function(t){p(e,t,n[t])}))}return e}({},Object.entries(i.urlUtil.getUrlSearchParams()).reduce(((e,[t,n])=>(f.includes(t)&&(e[t]=n),e)),{}),e)}(t))}function E(e){return{labelName:e.params.labelName,labelValue:e.params.labelValue,breakdownLabel:e.params.breakdownLabel}}function x(e){const t=(0,c.cR)(e);let{labelName:n,labelValue:r}=S();if(n===l.ky&&(n=l.OX),!t.state.filters.find((e=>e.key===n))){const e=s.locationService.getLocation();d.v.info("invalid primary label name in url",{labelName:n,url:`${e.pathname}${e.search}`})}if(!t.state.filters.find((e=>(0,o.uu)(e.value)===r))){const e=s.locationService.getLocation();d.v.info("invalid primary label value in url",{labelValue:r,url:`${e.pathname}${e.search}`})}}},9829:(e,t,n)=>{n.d(t,{Ti:()=>o,U4:()=>c,Vy:()=>u,hJ:()=>p,oh:()=>m,u9:()=>d}),n(7781);var r=n(8531),a=n(1119),i=n(3143),l=(n(892),n(7841));function s(e,t,n,r,a,i,l){try{var s=e[i](l),o=s.value}catch(e){return void n(e)}s.done?t(o):Promise.resolve(o).then(r,a)}function o(e){return a.jh.getAncestor(e,l.PR)}function c(e){return a.jh.interpolate(e,i.gR)}function d(e){return a.jh.interpolate(e,i.SA).replace(/\s+/g," ")}function u(e){const t=r.config.theme2.visualization;return t.getColorByName(t.palette[e%8])}function p(e){return g.apply(this,arguments)}function g(){var e;return e=function*(e){return yield(0,r.getDataSourceSrv)().get(i.gR,{__sceneObject:{value:e}})},g=function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function l(e){s(i,r,a,l,o,"next",e)}function o(e){s(i,r,a,l,o,"throw",e)}l(void 0)}))},g.apply(this,arguments)}function m(e){return a.jh.findDescendents(e,a.dt)}},5722:(e,t,n)=>{n.r(t),n.d(t,{DEFAULT_SORT_BY:()=>d,calculateDataFrameChangepoints:()=>p,calculateOutlierValue:()=>h,sortSeries:()=>u,sortSeriesByName:()=>g,wasmSupported:()=>f});var r=n(1854),a=n(6944),i=n(7781),l=n(1383),s=n(3241),o=n(2718),c=n(2871);const d="changepoint",u=(0,s.memoize)(((e,t,n)=>{if("alphabetical"===t)return g(e,n);"outliers"===t&&m(e);const r=n=>{var r;try{if("changepoint"===t)return p(n);if("outliers"===t)return h(e,n)}catch(e){c.v.error(e,{msg:"failed to sort"}),t=i.ReducerID.stdDev}const a=i.fieldReducers.get(t);var l,s;return null!==(s=(null!==(l=null===(r=a.reduce)||void 0===r?void 0:r.call(a,n.fields[1],!0,!0))&&void 0!==l?l:(0,i.doStandardCalcs)(n.fields[1],!0,!0))[t])&&void 0!==s?s:0},a=e.map((e=>({value:r(e),dataFrame:e})));return a.sort(((e,t)=>void 0!==e.value&&void 0!==t.value?t.value-e.value:0)),"asc"===n&&a.reverse(),a.map((({dataFrame:e})=>e))}),((e,t,n)=>{const r=e.length>0?e[0].fields[0].values[0]:0,a=e.length>0?e[e.length-1].fields[0].values[e[e.length-1].fields[0].values.length-1]:0,i=e.length>0?(0,l.H7)(e[0]):"",s=e.length>0?(0,l.H7)(e[e.length-1]):"",o=e.map((e=>e.length+"_"+e.fields.map((e=>e.name+"_"+e.values[0]+"_"+e.values[e.values.length-1]))));return`${i}_${s}_${r}_${a}_${e.length}_${o}_${t}_${n}`})),p=e=>{if(!f())throw new Error("WASM not supported, fall back to stdDev");const t=e.fields.filter((e=>e.type===i.FieldType.number)),n=t[0].values.length;let a=Math.floor(n/100)||1;a>1&&(a=Math.ceil(a/2));const l=t[0].values.filter(((e,t)=>t%a==0)),s=new Float64Array(l);return r.ChangepointDetector.defaultArgpcp().detectChangepoints(s).indices.length},g=(e,t)=>{const n=[...e];return n.sort(((e,t)=>{const n=(0,l.H7)(e),r=(0,l.H7)(t);return n&&r&&null!==(a=null==n?void 0:n.localeCompare(r))&&void 0!==a?a:0;var a})),"desc"===t&&n.reverse(),n},m=e=>{if(!f())return;const t=(0,i.outerJoinDataFrames)({frames:e});if(!t)return;const n=t.fields.filter((e=>e.type===i.FieldType.number)).flatMap((e=>new Float64Array(e.values)));try{const e=a.OutlierDetector.dbscan({sensitivity:.4}).preprocess(n);v=e.detect()}catch(e){c.v.error(e,{msg:"initOutlierDetector: OutlierDetector error"})}};let v;const h=(e,t)=>{if(!f())throw new Error("WASM not supported, fall back to stdDev");if(!v)throw new Error("Initialize outlier detector first");const n=e.indexOf(t);return v.seriesResults[n].isOutlier?v.seriesResults[n].outlierIntervals.length:0},f=()=>{const e="object"==typeof WebAssembly;return e||(0,o.EE)(o.NO.service_details,o.ir.service_details.wasm_not_supported),e}},227:(e,t,n)=>{n.d(t,{Gg:()=>m,N$:()=>j,OB:()=>u,QB:()=>b,YK:()=>C,YM:()=>x,ZF:()=>F,cC:()=>p,cO:()=>g,eT:()=>d,fq:()=>O,k5:()=>T,ke:()=>y,o5:()=>_,sj:()=>v,vs:()=>w});var r=n(2533),a=n(4750),i=n(2871),l=n(3143);const s=`${r.id}.services.favorite`,o=`${r.id}.primarylabels.tabs.favorite`,c=`${r.id}.datasource`;function d(e,t){if(!e||"string"!=typeof e)return[];const n=h(e,t);let r=[];try{r=JSON.parse(localStorage.getItem(n)||"[]")}catch(e){i.v.error(e,{msg:"Error parsing favorite services from local storage"})}return Array.isArray(r)||(r=[]),r}function u(e,t,n){if(!e||"string"!=typeof e)return;const r=h(e,t);let a=[];try{a=JSON.parse(localStorage.getItem(r)||"[]")}catch(e){i.v.error(e,{msg:"Error parsing favorite services from local storage"})}Array.isArray(a)||(a=[]);const l=a.filter((e=>e!==n));l.unshift(n),localStorage.setItem(r,JSON.stringify(l))}function p(e,t,n){if(!e||!t||!n||"string"!=typeof e)return;const r=h(e,t);let a=[];try{a=JSON.parse(localStorage.getItem(r)||"[]")}catch(e){i.v.error(e,{msg:"Error parsing favorite services from local storage"})}Array.isArray(a)||(a=[]);const l=a.filter((e=>e!==n));localStorage.setItem(r,JSON.stringify(l))}function g(e,t){if(!e||!t)return;const n=f(e);let r=[];try{r=JSON.parse(localStorage.getItem(n)||"[]")}catch(e){i.v.error(e,{msg:"Error parsing saved tabs from local storage"})}if(Array.isArray(r)||(r=[]),-1===r.indexOf(t)){const e=r.filter((e=>e!==t));e.unshift(t),localStorage.setItem(n,JSON.stringify(e))}}function m(e,t){if(!e||!t)return;const n=f(e);let r=[];try{r=JSON.parse(localStorage.getItem(n)||"[]")}catch(e){i.v.error(e,{msg:"Error parsing favorite services from local storage"})}Array.isArray(r)||(r=[]);const a=r.filter((e=>e!==t));localStorage.setItem(n,JSON.stringify(a))}function v(e){if(!e||"string"!=typeof e)return[];const t=f(e);let n=[];try{n=JSON.parse(localStorage.getItem(t)||"[]")}catch(e){i.v.error(e,{msg:"Error parsing favorite services from local storage"})}return Array.isArray(n)||(n=[]),n}function h(e,t){return t=t===l.OX?"":`_${t}`,`${s}_${e}${t}`}function f(e){return`${o}_${e}`}function b(){var e;return null!==(e=localStorage.getItem(c))&&void 0!==e?e:void 0}function y(e){localStorage.setItem(c,e)}const S=`${r.id}.values.sort`;function w(e,t,n){var r;const a=(null!==(r=localStorage.getItem(`${S}.${e}.by`))&&void 0!==r?r:"").split(".");return a[0]&&a[1]?{sortBy:a[0],direction:a[1]}:{sortBy:t,direction:n}}function O(e,t,n){t&&n&&localStorage.setItem(`${S}.${e}.by`,`${t}.${n}`)}const E=`${r.id}.logs.option`;function x(e){return localStorage.getItem(`${E}.${e}`)}function C(e,t){let n=t.toString();"boolean"!=typeof t||t||(n=""),localStorage.setItem(`${E}.${e}`,n)}function P(e){return`${(0,a.nH)(e)}.${(0,a.p_)(e)}`}function j(e){const t=P(e),n=localStorage.getItem(`${r.id}.${t}.logs.fields`);return n?JSON.parse(n):[]}function F(e,t){const n=P(e);localStorage.setItem(`${r.id}.${n}.logs.fields`,JSON.stringify(t))}const k="grafana.explore.logs.visualisationType";function T(){var e;const t=null!==(e=localStorage.getItem(k))&&void 0!==e?e:"";switch(t){case"table":case"logs":return t;default:return"logs"}}function _(e){localStorage.setItem(k,e)}},1220:(e,t,n)=>{n.d(t,{b:()=>r});const r={appConfig:{container:"data-testid ac-container",apiKey:"data-testid ac-api-key",apiUrl:"data-testid ac-api-url",submit:"data-testid ac-submit-form"},exploreServiceSearch:{search:"data-testid search-services"},header:{refreshPicker:"data-testid RefreshPicker run button"},variables:{datasource:{label:"data-testid Dashboard template variables submenu Label Data source"},combobox:{},serviceName:{label:"data-testid Dashboard template variables submenu Label Labels"}},breakdowns:{labels:{},fields:{},common:{sortByFunction:"data-testid SortBy function",sortByDirection:"data-testid SortBy direction",filterButtonGroup:"data-testid filter-button-group",filterButton:"data-testid filter-button",filterSelect:"data-testid filter-select",filterNumericPopover:{removeButton:"data-testid filter-numeric-remove",submitButton:"data-testid filter-numeric-submit",cancelButton:"data-testid filter-numeric-cancel",inputGreaterThan:"data-testid filter-numeric-gt",inputGreaterThanUnit:"data-testid filter-numeric-gtu",inputGreaterThanInclusive:"data-testid filter-numeric-gte",inputLessThan:"data-testid filter-numeric-lt",inputLessThanUnit:"data-testid filter-numeric-ltu",inputLessThanInclusive:"data-testid filter-numeric-lte"}}},index:{showLogsButton:"data-testid Show logs",addNewLabelTab:"data-testid Tab Add label",searchLabelValueInput:"data-testid search-services-input",aggregatedMetricsMenu:"data-testid aggregated-metrics-menu",aggregatedMetricsToggle:"data-testid aggregated-metrics-toggle",header:{showLogsButton:"data-testid Show logs header"}},exploreServiceDetails:{searchLogs:"data-testid search-logs",openExplore:"data-testid open-explore",tabPatterns:"data-testid tab-patterns",tabLogs:"data-testid tab-logs",tabFields:"data-testid tab-fields",tabLabels:"data-testid tab-labels",buttonRemovePattern:"data-testid button-remove-pattern",buttonFilterInclude:"data-testid button-filter-include",buttonFilterExclude:"data-testid button-filter-exclude"},patterns:{tableWrapper:"data-testid table-wrapper",buttonIncludedPattern:"data-testid button-included-pattern",buttonExcludedPattern:"data-testid button-excluded-pattern"},logsPanelHeader:{header:"data-testid Panel header Logs",radio:"data-testid radio-button"},table:{wrapper:"data-testid table-wrapper",inspectLine:"data-testid inspect"}}},8315:(e,t,n)=>{n.d(t,{Dk:()=>l,EJ:()=>p,Zr:()=>u,gW:()=>d});var r=n(8531),a=n(2871);function i(e,t,n,r,a,i,l){try{var s=e[i](l),o=s.value}catch(e){return void n(e)}s.done?t(o):Promise.resolve(o).then(r,a)}const l=(s=function*(e,t){if(navigator.clipboard&&window.isSecureContext)return navigator.clipboard.writeText(e);if(document.execCommand&&t){const n=document.createElement("textarea"),r=t instanceof HTMLButtonElement?t:null==t?void 0:t.current;null==r||r.appendChild(n),n.value=e,n.focus(),n.select(),document.execCommand("copy"),n.remove()}},o=function(){var e=this,t=arguments;return new Promise((function(n,r){var a=s.apply(e,t);function l(e){i(a,n,r,l,o,"next",e)}function o(e){i(a,n,r,l,o,"throw",e)}l(void 0)}))},function(e,t){return o.apply(this,arguments)});var s,o,c;!function(e){e.From="from",e.To="to"}(c||(c={}));const d=(e,t,n)=>{const a=r.locationService.getLocation(),i=new URLSearchParams(a.search);i.set("from",n.from.toISOString()),i.set("to",n.to.toISOString()),i.set(e,JSON.stringify(t));const l=i.toString().replace(/\+/g,"%20");return window.location.origin+a.pathname+"?"+l};function u(e){return e.length?(null==e?void 0:e.charAt(0).toUpperCase())+e.slice(1):(a.v.warn("invalid string argument"),e)}function p(e,t,n){return e.substring(0,t)+(n&&e.length>t?"…":"")}},4750:(e,t,n)=>{n.d(t,{BL:()=>E,DX:()=>s,El:()=>O,Hj:()=>f,Ku:()=>o,P4:()=>v,Rr:()=>m,S9:()=>b,aW:()=>d,bY:()=>y,bu:()=>C,cR:()=>c,eY:()=>S,h:()=>w,ir:()=>p,iw:()=>g,n5:()=>x,nH:()=>F,oY:()=>u,p_:()=>j,vm:()=>h,z2:()=>P});var r=n(1119),a=n(5431),i=n(3143),l=n(2871);function s(e){const{labelExpressionToAdd:t="",structuredMetadataToAdd:n="",fieldExpressionToAdd:r="",parser:a}=e;switch(a){case"structuredMetadata":return`{${i.S1}${t}} ${n} ${i.S6} ${i.qZ} ${i.sC} ${i.A2} ${r} ${i.Oc}`;case"json":return`{${i.S1}${t}} ${n} ${i.S6} ${i.qZ} ${i.sC} ${i.A2} ${i.VL} ${r} ${i.Oc}`;case"logfmt":return`{${i.S1}${t}} ${n} ${i.S6} ${i.qZ} ${i.sC} ${i.A2} ${i.mF} ${r} ${i.Oc}`;default:return`{${i.S1}${t}} ${n} ${i.S6} ${i.qZ} ${i.sC} ${i.A2} ${i.YN} ${r} ${i.Oc}`}}function o(e){const t=r.jh.lookupVariable(i.uw,e);if(!(t instanceof r.yP))throw new Error("VAR_PATTERNS not found");return t}function c(e){return y(i.MB,e)}function d(e){return y(i.fi,e)}function u(e){return y(i._P,e)}function p(e){return y(i.mB,e)}function g(e){return y(i._Y,e)}function m(e){const t=r.jh.lookupVariable(i.WM,e);if(!(t instanceof r.yP))throw new Error("VAR_LINE_FILTER not found");return t}function v(e){const t=r.jh.lookupVariable(i.Jg,e);if(!(t instanceof a.m))throw new Error("VAR_LABEL_GROUP_BY not found");return t}function h(e){const t=r.jh.lookupVariable(i.Wi,e);if(!(t instanceof a.m))throw new Error("SERVICE_LABEL_VAR not found");return t}function f(e){const t=r.jh.lookupVariable(i.LI,e);if(!(t instanceof a.m))throw new Error("VAR_FIELD_GROUP_BY not found");return t}function b(e){const t=r.jh.lookupVariable(i.EY,e);if(!(t instanceof r.mI))throw new Error("VAR_DATASOURCE not found");return t}function y(e,t){const n=r.jh.lookupVariable(e,t);if(!(n instanceof r.H9))throw new Error(`Could not get AdHocFiltersVariable ${e}. Variable not found.`);return n}function S(e){const t=r.jh.lookupVariable(i.Du,e);if(!(t instanceof a.m))throw new Error("VAR_PRIMARY_LABEL_SEARCH not found");return t}function w(e){S(e).setState({value:".+",label:""})}function O(e){const t=r.jh.lookupVariable(i.Gb,e);if(!(t instanceof r.H9))throw new Error("VAR_PRIMARY_LABEL not found");return t}function E(e,t){O(t).setState({filters:[{value:".+",operator:"=~",key:e}]})}function x(e){return`var-${e}`}function C(e,t=i.mB){try{return JSON.parse(e.value)}catch(n){if(l.v.warn(`Failed to parse ${t}`,{value:e.value}),e.value)return{value:e.value,parser:"mixed"};throw n}}function P(e,t){return e.state.name===i.mB&&t?C(t):{value:null==t?void 0:t.value}}function j(e){return function(e){const t=e.filters.filter((e=>e.key===i.OX)).map((e=>e.value));if(!t)throw new Error("Service present in filters selected");return t[0]}(c(e).state)}function F(e){return b(e).getValue()}}}]);
//# sourceMappingURL=32.js.map