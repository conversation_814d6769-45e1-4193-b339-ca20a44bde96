"use strict";(self.webpackChunkgrafana_lokiexplore_app=self.webpackChunkgrafana_lokiexplore_app||[]).push([[826],{6826:(e,t,a)=>{a.r(t),a.d(t,{default:()=>D,updatePlugin:()=>O});var n=a(5959),r=a.n(n),i=a(6089),l=a(3241),o=a(1269),s=a(7781),c=a(8531),d=a(2007),u=a(5953),p=a(4351);function m(e,t,a,n,r,i,l){try{var o=e[i](l),s=o.value}catch(e){return void a(e)}o.done?t(s):Promise.resolve(s).then(n,r)}function g(e){return function(){var t=this,a=arguments;return new Promise(function(n,r){var i=e.apply(t,a);function l(e){m(i,n,r,l,o,"next",e)}function o(e){m(i,n,r,l,o,"throw",e)}l(void 0)})}}function v(e,t,a){return t in e?Object.defineProperty(e,t,{value:a,enumerable:!0,configurable:!0,writable:!0}):e[t]=a,e}function b(e){for(var t=1;t<arguments.length;t++){var a=null!=arguments[t]?arguments[t]:{},n=Object.keys(a);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(a).filter(function(e){return Object.getOwnPropertyDescriptor(a,e).enumerable}))),n.forEach(function(t){v(e,t,a[t])})}return e}function f(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var a=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),a.push.apply(a,n)}return a}(Object(t)).forEach(function(a){Object.defineProperty(e,a,Object.getOwnPropertyDescriptor(t,a))}),e}const h=e=>({colorWeak:i.css`
    color: ${e.colors.text.secondary};
  `,icon:(0,i.css)({marginLeft:e.spacing(1)}),label:(0,i.css)({alignItems:"center",display:"flex",marginBottom:e.spacing(.75)}),marginTop:i.css`
    margin-top: ${e.spacing(3)};
  `,marginTopXl:i.css`
    margin-top: ${e.spacing(6)};
  `,note:(0,i.css)({color:e.colors.text.secondary,marginBottom:e.spacing(1),marginTop:e.spacing(1)})}),y=(e,t)=>g(function*(){try{yield O(e,t),c.locationService.reload()}catch(e){u.v.error(e,{msg:"Error while updating the plugin"})}})(),w={appConfig:{container:"data-testid ac-container",datasource:"data-testid ac-datasource-input",interval:"data-testid ac-interval-input",pattern:"data-testid ac-patterns-disabled",submit:"data-testid ac-submit-form"}},O=(e,t)=>g(function*(){const a=(0,c.getBackendSrv)().fetch({data:t,method:"POST",url:`/api/plugins/${e}/settings`});return(yield(0,o.lastValueFrom)(a)).data})(),k=e=>{try{if(e){const t=s.rangeUtil.intervalToSeconds(e);return(0,l.isNumber)(t)&&t>=3600}return!0}catch(e){}return!1},D=({plugin:e})=>{const t=(0,d.useStyles2)(h),{enabled:a,jsonData:i,pinned:l}=e.meta;var o,s,u,m,g,v;const[O,D]=(0,n.useState)({dataSource:null!==(u=null!==(s=null!==(o=null==i?void 0:i.dataSource)&&void 0!==o?o:(0,p.x0)())&&void 0!==s?s:(0,p.QB)())&&void 0!==u?u:"",interval:null!==(m=null==i?void 0:i.interval)&&void 0!==m?m:"",isValid:k(null!==(g=null==i?void 0:i.interval)&&void 0!==g?g:""),patternsDisabled:null!==(v=null==i?void 0:i.patternsDisabled)&&void 0!==v&&v});return r().createElement("div",{"data-testid":w.appConfig.container},r().createElement(d.FieldSet,{label:"Settings"},r().createElement(d.Field,{description:r().createElement("span",null,"The default data source to be used for new Logs Drilldown users. Each user can override their default by setting another data source in Logs Drilldown."),label:"Default data source"},r().createElement(c.DataSourcePicker,{width:60,filter:e=>"loki"===e.type,current:O.dataSource,onChange:e=>{D(f(b({},O),{dataSource:e.uid}))}})),r().createElement(d.Field,{invalid:!k(O.interval),error:'Interval is invalid. Please enter an interval longer then "60m". For example: 3d, 1w, 1m',description:r().createElement("span",null,"The maximum interval that can be selected in the time picker within the Grafana Logs Drilldown app. If empty, users can select any time range interval in Grafana Logs Drilldown. ",r().createElement("br",null),"Example values: 7d, 24h, 2w"),label:"Maximum time picker interval",className:t.marginTop},r().createElement(d.Input,{width:60,id:"interval","data-testid":w.appConfig.interval,label:"Max interval",value:null==O?void 0:O.interval,placeholder:"7d",onChange:e=>{const t=e.target.value.trim();D(f(b({},O),{interval:t,isValid:k(t)}))}})),r().createElement(d.Field,{className:t.marginTop,description:r().createElement("span",null,"Disables Logs Drilldown's usage of the"," ",r().createElement("a",{className:"external-link",href:"https://grafana.com/docs/loki/latest/reference/loki-http-api/#patterns-detection",target:"_blank",rel:"noreferrer"},"Loki Patterns API")," ","endpoint, and removes the Patterns tab."),label:"Disable Loki patterns"},r().createElement(d.Checkbox,{id:"disable-patterns","data-testid":w.appConfig.interval,label:"Disable patterns",value:null==O?void 0:O.patternsDisabled,placeholder:"7d",onChange:e=>{const t=e.currentTarget.checked;D(f(b({},O),{patternsDisabled:t}))}})),r().createElement("div",{className:t.marginTop},r().createElement(d.Button,{type:"submit","data-testid":w.appConfig.submit,onClick:()=>y(e.meta.id,{enabled:a,jsonData:{dataSource:O.dataSource,interval:O.interval,patternsDisabled:O.patternsDisabled},pinned:l}),disabled:!k(O.interval)},"Save settings")),r().createElement("p",{className:t.note},"Active users must refresh the app to update configuration.")))}}}]);
//# sourceMappingURL=826.js.map?_cache=91e39090c5611938563c