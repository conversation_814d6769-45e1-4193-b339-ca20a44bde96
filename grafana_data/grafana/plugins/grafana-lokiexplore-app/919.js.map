{"version": 3, "file": "919.js?_cache=728718e594379dd03c81", "mappings": "wOAUA,MAAMA,EAAc,IAClB,IAAIC,EAAAA,GAAS,CACXC,MAAO,EAACC,EAAAA,EAAAA,MAAiBC,EAAAA,EAAAA,OAAoBC,EAAAA,EAAAA,OAC7CC,eAAgB,CACdC,2BAA2B,EAC3BC,iBAAiB,KA8BvB,QA1BA,WACE,MAAOC,EAAeC,GAAoBC,IAAAA,UAAe,IAEzDC,EAAAA,EAAAA,MAEA,MAAMC,GAAQC,EAAAA,EAAAA,IAAYd,IAE1Be,EAAAA,EAAAA,WAAU,KACHN,GACHC,GAAiB,IAElB,CAACG,EAAOJ,IAEX,MAAMO,EAAkBC,EAAAA,OAAOC,SAASC,KAAKC,YAE7C,OADkBJ,aAAAA,EAAAA,EAAkB,mCAAmCA,aAAAA,EAAAA,EAAkB,wBAKpFP,EAIE,kBAACI,EAAMQ,UAAS,CAACC,MAAOT,IAHtB,KAJA,kBAACU,EAAAA,SAAQA,CAACC,GAAG,IAAIC,SAAAA,GAQ5B,C", "sources": ["webpack://grafana-lokiexplore-app/./Components/LogExplorationPage.tsx"], "sourcesContent": ["import React, { useEffect } from 'react';\n\nimport { Navigate } from 'react-router-dom';\n\nimport { config } from '@grafana/runtime';\nimport { SceneApp, useSceneApp } from '@grafana/scenes';\n\nimport { initializeMetadataService } from '../services/metadata';\nimport { makeEmbeddedPage, makeIndexPage, makeRedirectPage } from './Pages';\n\nconst getSceneApp = () =>\n  new SceneApp({\n    pages: [makeIndexPage(), makeEmbeddedPage(), makeRedirectPage()],\n    urlSyncOptions: {\n      createBrowserHistorySteps: true,\n      updateUrlOnInit: true,\n    },\n  });\n\nfunction LogExplorationView() {\n  const [isInitialized, setIsInitialized] = React.useState(false);\n\n  initializeMetadataService();\n\n  const scene = useSceneApp(getSceneApp);\n\n  useEffect(() => {\n    if (!isInitialized) {\n      setIsInitialized(true);\n    }\n  }, [scene, isInitialized]);\n\n  const userPermissions = config.bootData.user.permissions;\n  const canUseApp = userPermissions?.['grafana-lokiexplore-app:read'] || userPermissions?.['datasources:explore'];\n  if (!canUseApp) {\n    return <Navigate to=\"/\" replace />;\n  }\n\n  if (!isInitialized) {\n    return null;\n  }\n\n  return <scene.Component model={scene} />;\n}\n\nexport default LogExplorationView;\n"], "names": ["getSceneApp", "SceneApp", "pages", "makeIndexPage", "makeEmbeddedPage", "makeRedirectPage", "urlSyncOptions", "createBrowserHistorySteps", "updateUrlOnInit", "isInitialized", "setIsInitialized", "React", "initializeMetadataService", "scene", "useSceneApp", "useEffect", "userPermissions", "config", "bootData", "user", "permissions", "Component", "model", "Navigate", "to", "replace"], "sourceRoot": ""}