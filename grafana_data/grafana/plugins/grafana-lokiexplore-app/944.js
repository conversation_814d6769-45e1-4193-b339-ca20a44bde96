"use strict";(self.webpackChunkgrafana_lokiexplore_app=self.webpackChunkgrafana_lokiexplore_app||[]).push([[944],{2011:(t,n,e)=>{t.exports=e.p+"c9b2776a9b35003e7123.wasm"},6944:(t,n,e)=>{let r;e.r(n),e.d(n,{LoadedOutlierDetector:()=>j,OutlierDetector:()=>x,custom_init:()=>k,default:()=>D,initLogging:()=>I,initSync:()=>E});const _="undefined"!=typeof TextDecoder?new TextDecoder("utf-8",{ignoreBOM:!0,fatal:!0}):{decode:()=>{throw Error("TextDecoder not available")}};"undefined"!=typeof TextDecoder&&_.decode();let o=null;function i(){return null!==o&&0!==o.byteLength||(o=new Uint8Array(r.memory.buffer)),o}function c(t,n){return t>>>=0,_.decode(i().subarray(t,t+n))}const a=new Array(128).fill(void 0);a.push(void 0,null,!0,!1);let s=a.length;function g(t){s===a.length&&a.push(a.length+1);const n=s;return s=a[n],a[n]=t,n}function b(t){return a[t]}function u(t){const n=b(t);return function(t){t<132||(a[t]=s,s=t)}(t),n}function f(t){return null==t}let w=null;function d(){return(null===w||!0===w.buffer.detached||void 0===w.buffer.detached&&w.buffer!==r.memory.buffer)&&(w=new DataView(r.memory.buffer)),w}let l=0;const y="undefined"!=typeof TextEncoder?new TextEncoder("utf-8"):{encode:()=>{throw Error("TextEncoder not available")}},p="function"==typeof y.encodeInto?function(t,n){return y.encodeInto(t,n)}:function(t,n){const e=y.encode(t);return n.set(e),{read:t.length,written:e.length}};function h(t,n,e){if(void 0===e){const e=y.encode(t),r=n(e.length,1)>>>0;return i().subarray(r,r+e.length).set(e),l=e.length,r}let r=t.length,_=n(r,1)>>>0;const o=i();let c=0;for(;c<r;c++){const n=t.charCodeAt(c);if(n>127)break;o[_+c]=n}if(c!==r){0!==c&&(t=t.slice(c)),_=e(_,r,r=c+3*t.length,1)>>>0;const n=i().subarray(_+c,_+r);c+=p(t,n).written,_=e(_,r,c,1)>>>0}return l=c,_}function m(t){const n=typeof t;if("number"==n||"boolean"==n||null==t)return`${t}`;if("string"==n)return`"${t}"`;if("symbol"==n){const n=t.description;return null==n?"Symbol":`Symbol(${n})`}if("function"==n){const n=t.name;return"string"==typeof n&&n.length>0?`Function(${n})`:"Function"}if(Array.isArray(t)){const n=t.length;let e="[";n>0&&(e+=m(t[0]));for(let r=1;r<n;r++)e+=", "+m(t[r]);return e+="]",e}const e=/\[object ([^\]]+)\]/.exec(toString.call(t));let r;if(!(e.length>1))return toString.call(t);if(r=e[1],"Object"==r)try{return"Object("+JSON.stringify(t)+")"}catch(t){return"Object"}return t instanceof Error?`${t.name}: ${t.message}\n${t.stack}`:r}function I(t){try{const e=r.__wbindgen_add_to_stack_pointer(-16);r.initLogging(e,f(t)?0:g(t));var n=d().getInt32(e+0,!0);if(d().getInt32(e+4,!0))throw u(n)}finally{r.__wbindgen_add_to_stack_pointer(16)}}function k(){r.custom_init()}function v(t,n){try{return t.apply(this,n)}catch(t){r.__wbindgen_exn_store(g(t))}}const A="undefined"==typeof FinalizationRegistry?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry((t=>r.__wbg_loadedoutlierdetector_free(t>>>0,1)));class j{static __wrap(t){t>>>=0;const n=Object.create(j.prototype);return n.__wbg_ptr=t,A.register(n,n.__wbg_ptr,n),n}__destroy_into_raw(){const t=this.__wbg_ptr;return this.__wbg_ptr=0,A.unregister(this),t}free(){const t=this.__destroy_into_raw();r.__wbg_loadedoutlierdetector_free(t,0)}detect(){try{const e=r.__wbindgen_add_to_stack_pointer(-16);r.loadedoutlierdetector_detect(e,this.__wbg_ptr);var t=d().getInt32(e+0,!0),n=d().getInt32(e+4,!0);if(d().getInt32(e+8,!0))throw u(n);return u(t)}finally{r.__wbindgen_add_to_stack_pointer(16)}}updateDetector(t){try{const e=r.__wbindgen_add_to_stack_pointer(-16);r.loadedoutlierdetector_updateDetector(e,this.__wbg_ptr,g(t));var n=d().getInt32(e+0,!0);if(d().getInt32(e+4,!0))throw u(n)}finally{r.__wbindgen_add_to_stack_pointer(16)}}}const O="undefined"==typeof FinalizationRegistry?{register:()=>{},unregister:()=>{}}:new FinalizationRegistry((t=>r.__wbg_outlierdetector_free(t>>>0,1)));class x{static __wrap(t){t>>>=0;const n=Object.create(x.prototype);return n.__wbg_ptr=t,O.register(n,n.__wbg_ptr,n),n}__destroy_into_raw(){const t=this.__wbg_ptr;return this.__wbg_ptr=0,O.unregister(this),t}free(){const t=this.__destroy_into_raw();r.__wbg_outlierdetector_free(t,0)}constructor(t,n){try{const o=r.__wbindgen_add_to_stack_pointer(-16);r.outlierdetector_new(o,g(t),g(n));var e=d().getInt32(o+0,!0),_=d().getInt32(o+4,!0);if(d().getInt32(o+8,!0))throw u(_);return this.__wbg_ptr=e>>>0,O.register(this,this.__wbg_ptr,this),this}finally{r.__wbindgen_add_to_stack_pointer(16)}}static dbscan(t){try{const _=r.__wbindgen_add_to_stack_pointer(-16);r.outlierdetector_dbscan(_,g(t));var n=d().getInt32(_+0,!0),e=d().getInt32(_+4,!0);if(d().getInt32(_+8,!0))throw u(e);return x.__wrap(n)}finally{r.__wbindgen_add_to_stack_pointer(16)}}static mad(t){try{const _=r.__wbindgen_add_to_stack_pointer(-16);r.outlierdetector_mad(_,g(t));var n=d().getInt32(_+0,!0),e=d().getInt32(_+4,!0);if(d().getInt32(_+8,!0))throw u(e);return x.__wrap(n)}finally{r.__wbindgen_add_to_stack_pointer(16)}}detect(t){try{const _=r.__wbindgen_add_to_stack_pointer(-16);r.outlierdetector_detect(_,this.__wbg_ptr,g(t));var n=d().getInt32(_+0,!0),e=d().getInt32(_+4,!0);if(d().getInt32(_+8,!0))throw u(e);return u(n)}finally{r.__wbindgen_add_to_stack_pointer(16)}}preprocess(t){try{const _=r.__wbindgen_add_to_stack_pointer(-16);r.outlierdetector_preprocess(_,this.__wbg_ptr,g(t));var n=d().getInt32(_+0,!0),e=d().getInt32(_+4,!0);if(d().getInt32(_+8,!0))throw u(e);return j.__wrap(n)}finally{r.__wbindgen_add_to_stack_pointer(16)}}}function S(){const t={wbg:{}};return t.wbg.__wbindgen_error_new=function(t,n){return g(new Error(c(t,n)))},t.wbg.__wbindgen_object_drop_ref=function(t){u(t)},t.wbg.__wbindgen_is_object=function(t){const n=b(t);return"object"==typeof n&&null!==n},t.wbg.__wbindgen_is_undefined=function(t){return void 0===b(t)},t.wbg.__wbindgen_in=function(t,n){return b(t)in b(n)},t.wbg.__wbindgen_number_get=function(t,n){const e=b(n),r="number"==typeof e?e:void 0;d().setFloat64(t+8,f(r)?0:r,!0),d().setInt32(t+0,!f(r),!0)},t.wbg.__wbindgen_object_clone_ref=function(t){return g(b(t))},t.wbg.__wbindgen_is_string=function(t){return"string"==typeof b(t)},t.wbg.__wbindgen_string_get=function(t,n){const e=b(n),_="string"==typeof e?e:void 0;var o=f(_)?0:h(_,r.__wbindgen_malloc,r.__wbindgen_realloc),i=l;d().setInt32(t+4,i,!0),d().setInt32(t+0,o,!0)},t.wbg.__wbindgen_bigint_from_u64=function(t){return g(BigInt.asUintN(64,t))},t.wbg.__wbindgen_boolean_get=function(t){const n=b(t);return"boolean"==typeof n?n?1:0:2},t.wbg.__wbg_new_abda76e883ba8a5f=function(){return g(new Error)},t.wbg.__wbg_stack_658279fe44541cf6=function(t,n){const e=h(b(n).stack,r.__wbindgen_malloc,r.__wbindgen_realloc),_=l;d().setInt32(t+4,_,!0),d().setInt32(t+0,e,!0)},t.wbg.__wbg_error_f851667af71bcfc6=function(t,n){let e,_;try{e=t,_=n,console.error(c(t,n))}finally{r.__wbindgen_free(e,_,1)}},t.wbg.__wbg_mark_f0616123624944ec=function(t,n){performance.mark(c(t,n))},t.wbg.__wbg_log_914e3639af348b4e=function(t,n){let e,_;try{e=t,_=n,console.log(c(t,n))}finally{r.__wbindgen_free(e,_,1)}},t.wbg.__wbg_log_12b4ba535cbd9499=function(t,n,e,_,o,i,a,s){let g,b;try{g=t,b=n,console.log(c(t,n),c(e,_),c(o,i),c(a,s))}finally{r.__wbindgen_free(g,b,1)}},t.wbg.__wbg_measure_a990198e921c09fd=function(){return v((function(t,n,e,_){let o,i,a,s;try{o=t,i=n,a=e,s=_,performance.measure(c(t,n),c(e,_))}finally{r.__wbindgen_free(o,i,1),r.__wbindgen_free(a,s,1)}}),arguments)},t.wbg.__wbindgen_jsval_loose_eq=function(t,n){return b(t)==b(n)},t.wbg.__wbg_String_b9412f8799faab3e=function(t,n){const e=h(String(b(n)),r.__wbindgen_malloc,r.__wbindgen_realloc),_=l;d().setInt32(t+4,_,!0),d().setInt32(t+0,e,!0)},t.wbg.__wbindgen_number_new=function(t){return g(t)},t.wbg.__wbindgen_string_new=function(t,n){return g(c(t,n))},t.wbg.__wbg_getwithrefkey_edc2c8960f0f1191=function(t,n){return g(b(t)[b(n)])},t.wbg.__wbg_set_f975102236d3c502=function(t,n,e){b(t)[u(n)]=u(e)},t.wbg.__wbg_call_1084a111329e68ce=function(){return v((function(t,n){return g(b(t).call(b(n)))}),arguments)},t.wbg.__wbg_get_3baa728f9d58d3f6=function(t,n){return g(b(t)[n>>>0])},t.wbg.__wbg_length_ae22078168b726f5=function(t){return b(t).length},t.wbg.__wbg_new_a220cf903aa02ca2=function(){return g(new Array)},t.wbg.__wbindgen_is_function=function(t){return"function"==typeof b(t)},t.wbg.__wbg_next_de3e9db4440638b2=function(t){return g(b(t).next)},t.wbg.__wbg_next_f9cb570345655b9a=function(){return v((function(t){return g(b(t).next())}),arguments)},t.wbg.__wbg_done_bfda7aa8f252b39f=function(t){return b(t).done},t.wbg.__wbg_value_6d39332ab4788d86=function(t){return g(b(t).value)},t.wbg.__wbg_iterator_888179a48810a9fe=function(){return g(Symbol.iterator)},t.wbg.__wbg_get_224d16597dbbfd96=function(){return v((function(t,n){return g(Reflect.get(b(t),b(n)))}),arguments)},t.wbg.__wbg_new_525245e2b9901204=function(){return g(new Object)},t.wbg.__wbg_set_673dda6c73d19609=function(t,n,e){b(t)[n>>>0]=u(e)},t.wbg.__wbg_isArray_8364a5371e9737d8=function(t){return Array.isArray(b(t))},t.wbg.__wbg_instanceof_ArrayBuffer_61dfc3198373c902=function(t){let n;try{n=b(t)instanceof ArrayBuffer}catch(t){n=!1}return n},t.wbg.__wbg_entries_7a0e06255456ebcd=function(t){return g(Object.entries(b(t)))},t.wbg.__wbg_buffer_b7b08af79b0b0974=function(t){return g(b(t).buffer)},t.wbg.__wbg_new_ea1883e1e5e86686=function(t){return g(new Uint8Array(b(t)))},t.wbg.__wbg_set_d1e79e2388520f18=function(t,n,e){b(t).set(b(n),e>>>0)},t.wbg.__wbg_length_8339fcf5d8ecd12e=function(t){return b(t).length},t.wbg.__wbg_instanceof_Uint8Array_247a91427532499e=function(t){let n;try{n=b(t)instanceof Uint8Array}catch(t){n=!1}return n},t.wbg.__wbindgen_debug_string=function(t,n){const e=h(m(b(n)),r.__wbindgen_malloc,r.__wbindgen_realloc),_=l;d().setInt32(t+4,_,!0),d().setInt32(t+0,e,!0)},t.wbg.__wbindgen_throw=function(t,n){throw new Error(c(t,n))},t.wbg.__wbindgen_memory=function(){return g(r.memory)},t}function R(t,n){return r=t.exports,W.__wbindgen_wasm_module=n,w=null,o=null,r.__wbindgen_start(),r}function E(t){if(void 0!==r)return r;void 0!==t&&Object.getPrototypeOf(t)===Object.prototype?({module:t}=t):console.warn("using deprecated parameters for `initSync()`; pass a single object instead");const n=S();return t instanceof WebAssembly.Module||(t=new WebAssembly.Module(t)),R(new WebAssembly.Instance(t,n),t)}async function W(t){if(void 0!==r)return r;void 0!==t&&Object.getPrototypeOf(t)===Object.prototype?({module_or_path:t}=t):console.warn("using deprecated parameters for the initialization function; pass a single object instead"),void 0===t&&(t=new URL(e(2011),e.b));const n=S();("string"==typeof t||"function"==typeof Request&&t instanceof Request||"function"==typeof URL&&t instanceof URL)&&(t=fetch(t));const{instance:_,module:o}=await async function(t,n){if("function"==typeof Response&&t instanceof Response){if("function"==typeof WebAssembly.instantiateStreaming)try{return await WebAssembly.instantiateStreaming(t,n)}catch(n){if("application/wasm"==t.headers.get("Content-Type"))throw n;console.warn("`WebAssembly.instantiateStreaming` failed because your server does not serve wasm with `application/wasm` MIME type. Falling back to `WebAssembly.instantiate` which is slower. Original error:\n",n)}const e=await t.arrayBuffer();return await WebAssembly.instantiate(e,n)}{const e=await WebAssembly.instantiate(t,n);return e instanceof WebAssembly.Instance?{instance:e,module:t}:e}}(await t,n);return R(_,o)}const D=W}}]);
//# sourceMappingURL=944.js.map