{"version": 3, "file": "7.js?_cache=617e09944eff4d2b85bc", "mappings": "2SAUO,MAAMA,EAAwBC,GAC5BA,EACJC,OACEC,IAAYA,EAAOC,WAAaC,EAAAA,GAAaC,OAASH,EAAOC,WAAaC,EAAAA,GAAaE,QAAUJ,EAAOK,OAE1GC,IAAKN,IACJ,IACE,OAAO,IAAIO,OAAOP,EAAOK,MAAsB,kBAAfL,EAAOQ,IAA0B,IAAM,KACzE,CAAE,MAAOC,GAEP,YADAC,EAAAA,EAAOC,KAAK,mCAAoC,CAAEP,MAAOJ,EAAOK,OAElE,IAEDN,OAAQa,GAAMA,GAGbC,EAAqB,CAACC,EAA+BC,IACrDD,EACK,kBAACE,OAAAA,CAAKF,UAAWA,GAAYC,GAE7B,kBAACE,OAAAA,KAAMF,GAwCLG,EAA8B,CACzCC,EACAd,EACAe,EACAN,KAEA,IAAIO,EAAgD,GAChDC,EAAuB,EACvBC,EAAgBJ,EAAkBG,GAEtC,IAAK,IAAIE,EAAa,EAAGA,EAAanB,EAAMoB,OAAQD,IAAc,CAEhE,KAAOA,GAAcD,EAAc,IAAMD,EAAuBF,EAAO,GACrEE,IACAC,EAAgBJ,EAAkBG,GAEhCE,GAAcD,EAAc,IAAMC,EAAaD,EAAc,GAE/DF,EAAWK,KAAK,CAAErB,MAAOA,EAAMmB,KAE/BH,EAAWK,KAAKrB,EAAMmB,GAE1B,CAEA,MAxDqC,EAACH,EAA+CP,KACrF,IAAIa,EAAmC,GAEnCZ,EAAY,GACZa,EAAe,GACnB,IAAK,IAAIC,EAAI,EAAGA,EAAIR,EAAWI,OAAQI,IAAK,CAC1C,MAAMC,EAAOT,EAAWQ,GAGJ,iBAATC,GACLf,IACFY,EAAOD,KAAKb,EAAmBC,EAAWC,IAC1CA,EAAY,IAEda,GAAgBE,IAEZF,IACFD,EAAOD,KAAKE,GACZA,EAAe,IAEjBb,GAAae,EAAKzB,MAEtB,CAQA,OANIuB,GACFD,EAAOD,KAAKE,GAEVb,GACFY,EAAOD,KAAKb,EAAmBC,EAAWC,IAErCY,GA0BAI,CAAwBV,EAAYP,IAIhCkB,EAAuB,CAClCC,EACA5B,KAEA,IAAI6B,EAAmC,GA4BvC,OA3BAD,EAAiBE,QAAS/B,IACxB,IAAIgC,EACAC,EAAkC,GACtC,GACE,IACED,EAAahC,aAAAA,EAAAA,EAAOkC,KAAKjC,GAErB+B,IAEEA,EAAW,GACbC,EAAaX,KAAKU,GAGlBA,EAAa,KAGnB,CAAE,MAAO3B,G,IACkDL,EAAzDM,EAAAA,EAAOC,KAAK,mCAAoC,CAAEP,MAAoB,QAAbA,EAAAA,aAAAA,EAAAA,EAAOmC,cAAPnC,IAAAA,EAAAA,EAAiB,KAC1EgC,EAAa,IACf,QACOA,GACT,GAAIC,EAAaZ,OAAQ,CACvB,MAAMe,EAAuCH,EAAa/B,IAAKmC,GAAO,CAACA,EAAGC,MAAOD,EAAGC,MAAQD,EAAG,GAAGhB,SAClGS,EAAQR,QAAQc,EAClB,IAGKN,GA8BF,MAAMS,EAAoBC,GAC3BA,EAAanB,OA5BnB,SAAsBoB,GAKpBA,EAAIC,KAAK,CAACC,EAAGC,IAAMD,EAAE,GAAKC,EAAE,IAG5B,IAAIC,EAAS,EAEb,IAAK,IAAIpB,EAAI,EAAGA,EAAIgB,EAAIpB,OAAQI,IAG1BgB,EAAII,GAAQ,IAAMJ,EAAIhB,GAAG,GAC3BgB,EAAII,GAAQ,GAAKC,KAAKC,IAAIN,EAAII,GAAQ,GAAIJ,EAAIhB,GAAG,KAIjDoB,IACAJ,EAAII,GAAUJ,EAAIhB,IAKtB,OAAOoB,EAAS,CAClB,CAIWG,CAAaR,GAEf,EAGIS,EAA0BC,IAErC,MAAMC,EAAS,CACbC,SAAU,UACVC,MAAO,UACPC,MAAOJ,EAAMC,OAAOG,MAAMC,KAC1BhD,KAAM,UACNiD,SAAUN,EAAMC,OAAOI,KAAKE,QAC5BC,YAAaR,EAAMC,OAAOI,KAAKE,QAC/BE,MAAO,UACPC,QAASV,EAAMC,OAAOS,QAAQL,MAGhC,MAAO,CACL,sBAAuB,CACrBM,MAAOV,EAAOC,UAEhB,mBAAoB,CAClBS,MAAOV,EAAOE,OAEhB,sBAAuB,CACrBQ,MAAOX,EAAMC,OAAOW,QAAQP,MAE9B,mBAAoB,CAClBM,MAAOV,EAAOG,OAEhB,kBAAmB,CACjBO,MAAOV,EAAO5C,MAEhB,sBAAuB,CACrBsD,MAAOV,EAAOO,YACdK,WAAYb,EAAMc,WAAWC,iBAC7BC,QAAS,IAEX,iBAAkB,CAChBL,MAAOV,EAAOO,YACdK,WAAYb,EAAMc,WAAWC,iBAC7BC,QAAS,IAEX,mBAAoB,CAClBL,MAAOV,EAAOK,SACdO,WAAYb,EAAMc,WAAWG,gBAE/B,oBAAqB,CACnBN,MAAOX,EAAMC,OAAO5C,KAAK6D,OAE3B,kBAAmB,CACjBP,MAAOX,EAAMC,OAAOW,QAAQP,MAE9B,mBAAoB,CAClBM,MAAOV,EAAOQ,OAEhB,kBAAmB,CACjBE,MAAOX,EAAMC,OAAOW,QAAQP,MAE9B,qBAAsB,CACpBM,MAAOV,EAAOS,WCrNb,SAASS,EACdC,EACArE,EACAS,GAEA,MAAMmB,EAAmBpC,EAAqB6E,GACxCC,EAAoB3C,EAAqBC,EAAkB5B,GAC3De,EAAOuB,EAAiBgC,GAC9B,IAAItD,EAAuC,GAK3C,OAHIsD,EAAkBlD,SACpBJ,EAAaH,EAA4ByD,EAAmBtE,EAAOe,EAAMN,IAEpEO,CACT,C,cCbO,SAASuD,GAAc,KAAEjB,EAAI,cAAEkB,IACpC,MAAMC,GAASC,EAAAA,EAAAA,YAAWC,EAAAA,IAC1B,OAAO,kBAACC,SAAAA,CAAOnE,UAAWgE,EAAOI,qBAAsBvB,EAAKlC,OAASkC,EAAOkB,EAAc,IAC5F,C,8DCUO,MAAMM,EAAqB,EAChC3E,MACA4E,UACA/E,QACAgF,aACAC,gBACAC,oBAEAC,EAAAA,EAAAA,MAEAhF,EAAMA,EAAIiF,QAAQC,EAAAA,GAA0B,MAE5CC,EAAAA,EAAAA,IAAwBL,EAAeF,GAEvC,MAAMQ,EAAgBC,EAAAA,GAAWC,YAAYR,EAAeS,EAAAA,IAC5DC,EAAAA,EAAAA,IAAaxF,EAAKH,EAAOgF,EAAYO,EAAeL,GAAc,GAAO,IAEzEU,EAAAA,EAAAA,IACEC,EAAAA,GAAkBC,gBAClBC,EAAAA,GAAoBD,gBAAgBE,6BACpC,CACEC,OAAQjB,EACRA,WAAY,OACZ7E,S,YCpCN,MAAM+F,GAAYC,EAAAA,EAAAA,MAAK,IAAM,wCAUtB,SAASC,GAA2B,OAAEC,EAAM,YAAEC,EAAW,QAAEvB,EAAO,KAAEwB,EAAI,cAAEtB,IAC/E,MAAMR,GAASC,EAAAA,EAAAA,YAAW8B,EAA2BH,GACrD,OAAOI,EAAAA,EAAAA,SACL,IACE,kBAACP,EAAAA,CACCzF,UAAWgE,EAAOiC,OAClBC,QAAS,GAAY,YAATJ,EAAqB,UAAY,oCAAoCxB,EAAQ,KACzF6B,QAAUxG,IACRA,EAAEyG,kBACF/B,EAAmB,CACjB9E,MAAO8G,EAAAA,GACP3G,IAAKmG,EACLpB,aAAc6B,EAAAA,GACd9B,gBACAF,UACAC,WAAYqB,EAAS,SAAoB,YAATE,EAAqB,UAAY,aAGrES,gBAAeX,EACfY,QAASZ,EAAS,UAAY,YAC9Ba,KAAe,YAATX,EAAqB,cAAgB,eAC3CY,aAAY,GAAGZ,aAGnB,CAACF,EAAQtB,EAASuB,EAAarB,EAAeR,EAAOiC,OAAQH,GAEjE,CAEO,MAAMC,EAA4B,CAACvD,EAAsBmE,KACvD,CACLV,QAAQW,EAAAA,EAAAA,KAAI,CACVzD,MAAOwD,OAAWE,EAAYrE,EAAMC,OAAOI,KAAKiE,UAChD,UAAW,CACT3D,MAAOX,EAAMC,OAAOI,KAAKkE,iBCzC3BtB,GAAYC,EAAAA,EAAAA,MAAK,IAAM,wCAYhBsB,GAAuBC,EAAAA,EAAAA,MAClC,EAAGC,iBAAgBC,UAAS7C,UAAS8C,QAAOtB,OAAMvG,QAAO8H,YACvD,MAAMlI,EAAoB,YAAT2G,EAAqBwB,EAAAA,GAASC,MAAQD,EAAAA,GAASE,SAC1Db,GAAWO,aAAAA,EAAAA,EAAgB/H,YAAaA,EACxC6E,GAASC,EAAAA,EAAAA,YAAW8B,EAA2BY,GAC/Cc,GAAWP,aAAAA,EAAAA,EAAgB/H,YAAaA,EAE9C,OAAO6G,EAAAA,EAAAA,SACL,IACE,kBAACP,EAASA,CACRzF,UAAWgE,EAAOiC,OAClBC,QAAS,GAAY,YAATJ,EAAqB,UAAY,kCAAkCsB,MAAU7H,KACzF4G,QAAUxG,IACRA,EAAEyG,kBACF/B,EAAmB,CACjBC,QAASA,EACT5E,IAAKyH,EACL5H,QACAgF,WAAYkD,EAAW,SAAW3B,EAClCtB,cAAe6C,EACf5C,aAAc6B,EAAAA,MAGlBC,gBAAeI,EACfH,QAASG,EAAW,UAAY,YAChCF,KAAe,YAATX,EAAqB,cAAgB,eAC3CY,aAAY,GAAGZ,aAGnB,CAACa,EAAUc,EAAU3B,EAAM9B,EAAOiC,OAAQ3B,EAAS6C,EAAS5H,EAAO6H,EAAOC,MAIhFL,EAAqBU,YAAc,wBAW5B,MAAMC,GAAqBV,EAAAA,EAAAA,MAChC,EAAGC,iBAAgBE,QAAOtB,OAAMvG,QAAOkF,eAAcmD,eACnD,MAAMzI,EAAoB,YAAT2G,EAAqBwB,EAAAA,GAASC,MAAQD,EAAAA,GAASE,SAC1Db,GAAWO,aAAAA,EAAAA,EAAgB/H,YAAaA,EACxC6E,GAASC,EAAAA,EAAAA,YAAW8B,EAA2BY,GAC/Cc,GAAWP,aAAAA,EAAAA,EAAgB/H,YAAaA,EAE9C,OAAO6G,EAAAA,EAAAA,SACL,IACE,kBAACP,EAASA,CACRzF,UAAWgE,EAAOiC,OAClBC,QAAS,GAAY,YAATJ,EAAqB,UAAY,kCAAkCsB,MAAU7H,KACzF4G,QAAUxG,IACRA,EAAEyG,kBFtBuB,GAAGgB,QAAO7H,QAAOgF,aAAYE,eAAcmD,gBAC9ElD,EAAAA,EAAAA,OACAQ,EAAAA,EAAAA,IAAakC,EAAO7H,EAAOgF,EAAYqD,EAAUnD,GAAc,IAC/DU,EAAAA,EAAAA,IACEC,EAAAA,GAAkBC,gBAClBC,EAAAA,GAAoBD,gBAAgBE,6BACpC,CACEC,OAAQjB,EACRA,aACA6C,MAAOA,KEeDS,CAAsB,CACpBT,QACA7H,QACAgF,WAAYkD,EAAW,SAAW3B,EAClCrB,eACAmD,cAGJrB,gBAAekB,EACfjB,QAASiB,EAAW,UAAY,YAChChB,KAAe,YAATX,EAAqB,cAAgB,eAC3CY,aAAY,GAAGZ,aAGnB,CAAC2B,EAAUL,EAAOQ,EAAU5D,EAAOiC,OAAQH,EAAMvG,EAAOkF,MC9EvD,SAASqD,GAAyB,MAAEV,EAAK,MAAE7H,EAAK,YAAEsG,EAAW,QAAEsB,EAAO,eAAED,EAAc,MAAEG,IAC7F,OACE,oCACE,kBAACL,EAAoBA,CACnBI,MAAOA,EACP7H,MAAOA,EACP+E,QAASuB,EACTsB,QAASA,EACTD,eAAgBA,EAChBpB,KAAM,UACNuB,MAAOA,IAET,kBAACL,EAAoBA,CACnBI,MAAOA,EACP7H,MAAOA,EACP+E,QAASuB,EACTsB,QAASA,EACTD,eAAgBA,EAChBpB,KAAM,UACNuB,MAAOA,IAIf,CD2DAM,EAAmBD,YAAc,qB,aEnF1B,SAASK,GAAoB,eAAEb,EAAc,MAAEE,EAAK,SAAEQ,EAAQ,MAAErI,EAAK,aAAEkF,IAG5E,OACE,oCAEE,kBAACkD,EAAkBA,CACjB7B,KAAM,UACNsB,MAAOA,EAAMY,WACbzI,MAAOA,EACPkF,aAAcA,EACdyC,eAAgBA,EAAee,KAVVhJ,IAAkCiJ,EAAAA,EAAAA,IAAoBjJ,EAAOE,WAWlFyI,SAAUA,IAGZ,kBAACD,EAAkBA,CACjB7B,KAAM,UACNsB,MAAOA,EAAMY,WACbzI,MAAOA,EACPkF,aAAcA,EACdyC,eAAgBA,EAAee,KAlBVhJ,IAAkCkJ,EAAAA,EAAAA,IAAoBlJ,EAAOE,WAmBlFyI,SAAUA,IAIlB,C,mlBCpBe,SAASQ,GAAuB,SAAER,IAC/C,MACM5I,GADgBqJ,EAAAA,EAAAA,IAAsBT,GACdU,MAAMtJ,QAC9BuJ,EAAc,CAACC,EAAAA,EAAuB,EAAGC,EAAAA,IAE/C,OACE,oCACE,kBAACvI,OAAAA,CAAKF,UAAW0I,EAAAA,GAAqBhJ,IAAK+I,EAAAA,IACzC,kBAACE,EAAAA,OAAMA,CACLrI,KAAM,KACN6F,QAAS,IAAMyC,EAAeL,EAAaX,GAC3CpB,QAAS,YACTqC,KAAM,UACNC,UAAW9J,EAAQ2B,OACnB8F,KAAMgC,EAAAA,IAELA,EAAAA,IAEFzJ,EAAQ2B,OAAS,GAAK,kBAACoI,EAAAA,KAAIA,CAAC/I,UAAWgJ,EAAAA,GAAqBvC,KAAM,iBAGpEzH,EAAQQ,IAAI,CAACP,EAAQ8B,KACpB,MAAM0G,EAAWxI,EAAOS,MAAQV,EAAQA,EAAQ2B,OAAS,GAAGjB,IAC5D,OACE,kBAACQ,OAAAA,CAAKF,UAAW0I,EAAAA,GAAqBhJ,IAAKT,EAAOS,KAE9C,kBAACiJ,EAAAA,OAAMA,CACLrI,KAAM,KACNwI,SAAUrB,EACVtB,QAAS,IAAM8C,EAAWhK,EAAOS,IAAKkI,GACtCpB,QAAS,YACTqC,KAAM,WAEL5J,EAAOS,KAGXqB,EAAI/B,EAAQ2B,OAAS,GAAK,kBAACoI,EAAAA,KAAIA,CAAC/I,UAAWgJ,EAAAA,GAAqBvC,KAAM,gBACtE1F,IAAM/B,EAAQ2B,OAAS,GAAK,kBAACoI,EAAAA,KAAIA,CAAC/I,UAAWkJ,EAAAA,GAAqBzC,KAAM,mBAMrF,CAEO,SAAS0C,EACd7E,EACA8E,GAEA,MAEMC,EAA2C,KAF3BhB,EAAAA,EAAAA,IAAsBe,GAGzBd,MAAMtJ,WACpBsF,EAEArF,OAAQS,GAAuB,iBAARA,KAAqB4J,EAAAA,EAAAA,IAAe5J,IAAQA,IAAQ+I,EAAAA,IAE3Ec,UAEA/J,IAAKgK,IAAa,CACjB9J,IAAK8J,EAAQxB,WAGb7I,SAAUsK,EAAAA,GAAmBC,MAC7BnK,MAAOoK,EAAAA,OAKb,MAAO,CAAE9D,YADW,IAAIwD,EAAgB7J,IAAKP,GAAWA,EAAOS,KAAK6J,aAAcjF,EAAQsF,OAAO,IAC3EP,kBACxB,CAEO,MAAMT,EAAiB,CAACtE,EAAkBsD,MAC/ClD,EAAAA,EAAAA,MACA,MAAM,YAAEmB,EAAW,gBAAEwD,GAAoBF,EAAe7E,EAASsD,GAEjE,GAAItD,EAAQ3D,OAAS,EAAG,EACtBkE,EAAAA,EAAAA,IAAwB+C,EAAU/B,IAEZwC,EAAAA,EAAAA,IAAsBT,GAE9BiC,SAAS,CAErB7K,QAASqK,EAAgB7J,IAAKP,GAAY,E,kUAAA,IACrCA,GAAAA,CACHS,IAAKT,EAAOS,IAAIiF,QAAQC,EAAAA,GAA0B,UAGtDkF,EAAgB,MAAOxF,EAAQ,GAAG0D,WACpC,MAEE+B,EAAAA,EAAAA,GAAwBnC,IACxBoC,EAAAA,EAAAA,IAAsBpC,GACtBkC,EAAgB,SAAUrB,EAAAA,KAOjBqB,EAAkB,CAAChE,EAAwBpG,MACtDyF,EAAAA,EAAAA,IACEC,EAAAA,GAAkBC,gBAClBC,EAAAA,GAAoBD,gBAAgB4E,iCACpC,CACEvK,MACAoG,KAAMA,KAUCmD,EAAa,CAACvJ,EAAakI,MACtClD,EAAAA,EAAAA,MAEA,MAAMwF,GAAqB7B,EAAAA,EAAAA,IAAsBT,GAC3CuC,GAAUC,EAAAA,EAAAA,IAAsBxC,GAChCyC,GAAYC,EAAAA,EAAAA,IAAkB1C,GAE9B2C,EAAoBL,EAAmB5B,MAAMtJ,QAC7CwL,EAAWD,EAAkBE,UAAWxL,GAAWA,EAAOS,MAAQA,GAClEgL,EAA0BH,EAAkBtL,OAAO,CAAC0L,EAAG/I,IAAUA,GAAS4I,GAC1EI,EAA2B,GAEjC,IAAK,IAAI7J,EAAI,EAAGA,EAAIwJ,EAAkB5J,OAAQI,IAC5C6J,EAAehK,KACb,GACEgK,EAAejK,OACX,GAAG4J,EACA/K,IAAKP,GAAWA,EAAOS,KACvBkK,MAAM,EAAG7I,GACT8J,KAAK,QACR,KACHN,EAAkBxJ,GAAGrB,OAI5B,MAAMoL,EAAyBF,EAAehB,MAAMY,EAAW,GACzDO,EAAkB,IAAIC,IAC5BX,EAAU/B,MAAMtJ,QAAQqC,QAAS4J,GAAgBF,EAAgBG,IAAID,EAAYvL,MAEjF,MAAMyL,EAAoBhB,EAAQ7B,MAAMtJ,QAAQC,OAC7CA,IAAY6L,EAAuBM,SAASnM,EAAOS,MAAQqL,EAAgBM,IAAIpM,EAAOS,MAGzFyK,EAAQN,SAAS,CACf7K,QAASmM,IAEXjB,EAAmBL,SAAS,CAC1B7K,QAAS0L,IAGXZ,EAAgB,SAAUpK,ICzKf4L,EAAmBhH,GACvBA,EAAQ,KAAOiH,EAAAA,GAOXC,EAAsBlH,GAC1BA,EAAQ,KAAOmH,EAAAA,IAAuCnH,EAAQ,KAAOoH,EAAAA,GCgBvE,SAASC,GAAc,QAC5BrH,EAAO,UACPsH,EAAS,UACTvB,EAAS,mBACTwB,EAAkB,YAClBjI,EAAW,qBACXkI,EAAoB,cACpBC,I,IAEcC,EAAd,MAAMzM,EAA0C,QAAlCyM,EA8DhB,SAAkB1H,EAAkBsH,GAClC,MAAMK,EAAO,IAAI3H,GACX4H,EAAY,GAElB,KAAOD,EAAKtL,QAAQ,CAClB,MAAMjB,EAAMuM,EAAKE,MAEbzM,IAAQ+I,EAAAA,SAA2B5B,IAARnH,GAC7BwM,EAAUtL,KAAKlB,EAEnB,CAEA,OAAO0M,EAAAA,EAAAA,IAAyBR,EAAWM,EAC7C,CA3EgBF,CAAS1H,EAASsH,EAAUS,eAA5BL,IAAAA,OAAAA,EAAAA,EAAqChE,WAC7CZ,EAAQ9C,EAAQ,GAChBgI,EA2ER,SAAuChI,GACrC,OAAIA,EAAQ,KAAOmH,EAAAA,GACbnH,EAAQ,KAAOiI,EAAAA,GACVC,EAAAA,GAEFC,EAAAA,GACEnI,EAAQ,KAAOoH,EAAAA,GACjBgB,EAAAA,GAEApG,EAAAA,EAEX,CAtF+BqG,CAA8BrI,GACrDN,GAASC,EAAAA,EAAAA,YAAWC,EAAAA,IAE1B,IAAI0I,EAA+D,GAMnE,GALIb,EAAczD,MAAMuE,eAAiBrB,EAAmBlH,KAC1DsI,EAAmBjJ,EAA+BC,EAAaU,EAAQ,GAAG0D,aAIxEwD,EAAmBlH,GAAU,CAC/B,MACM4C,GADmB4F,EAAAA,EAAAA,IAAwBR,EAAsBP,GAC/BzD,MAAMtJ,QAAQC,OACnDA,GAAWA,EAAOS,MAAQ0H,EAAMY,YAAc/I,EAAOM,QAAUA,GAGlE,OACE,kBAACW,OAAAA,CAAKF,UAAWgE,EAAO+I,kBACtB,kBAAChF,EAAmBA,CAClBH,SAAUmE,EACV3E,MAAOA,EACP7H,MAAOA,EACPkF,aAAc6H,EACdpF,eAAgBA,IAElB,kBAACpD,EAAaA,CAACjB,KAAM+J,EAAkB7I,eAAeiJ,EAAAA,EAAAA,GAAiB1I,EAAS,MAGtF,CAEA,MAAM,YAAEuB,GAAgBsD,EAAe7E,EAASyH,GAC1C5E,GAAU8F,EAAAA,EAAAA,IAAWpH,GACrBqH,EAAiBrB,EAAmBsB,IAAIhG,GACxCiG,EACJF,GACA7C,EAAU/B,MAAMtJ,QAAQiJ,KAAMnI,GAAMA,EAAEJ,OAAQwN,aAAAA,EAAAA,EAAgBxN,OAAO2N,EAAAA,EAAAA,IAAyBvN,GAAGP,QAAUA,GAG7G,OACE,kBAACW,OAAAA,CAAKF,UAAWgE,EAAO+I,kBACrBjB,GACC,kBAAChE,EAAwBA,CACvBgE,qBAAsBA,EACtB1E,MAAOA,EACP7H,MAAOA,EACPsG,YAAaA,EACbsB,QAASA,EACTD,eAAgBkG,EAChBE,SAAUV,EACV7I,eAAeiJ,EAAAA,EAAAA,GAAiB1I,EAAS,IACzC+C,MAAO0E,IAGX,kBAACjI,EAAaA,CAACjB,KAAM+J,EAAkB7I,eAAeiJ,EAAAA,EAAAA,GAAiB1I,EAAS,MAGtF,CC7FA,MAAMmB,GAAYC,EAAAA,EAAAA,MAAK,IAAM,wCAEvB6H,GAAmBtG,EAAAA,EAAAA,MAAK,EAAG3C,UAASsD,eACxC,MAAM5D,GAASC,EAAAA,EAAAA,YAAW8B,GAA2B,GACrD,OAAOC,EAAAA,EAAAA,SACL,IACE,kBAACP,EAASA,CACRzF,UAAWgE,EAAOiC,OAClBC,QAAS,OAAO5B,EAAQ,kBACxB6B,QAAUxG,IACRA,EAAEyG,kBACFwC,EAAetE,EAASsD,IAE1BnB,KAAM,MACNC,aAAY,kBAAkBpC,EAAQ,OAG1C,CAACA,EAASsD,EAAU5D,EAAOiC,WAI/BsH,EAAiB7F,YAAc,kBAC/B,WCLA,SAAS8F,IAAiC,QACxClJ,EAAO,cACPmJ,EAAa,mBACb5B,EAAkB,YAClBjI,EAAW,qBACXkI,EAAoB,cACpBC,IAEA,MAAM,YAAElG,GAAgBsD,EAAe7E,EAASyH,GAC1C5E,GAAU8F,EAAAA,EAAAA,IAAWpH,GACrB7B,GAASC,EAAAA,EAAAA,YAAWC,EAAAA,IAEpBgJ,EAAiBrB,EAAmBsB,IAAIhG,GACxCD,EACJgG,GACAO,EAAcxF,KACXnI,GAAMA,EAAEJ,OAAQwN,aAAAA,EAAAA,EAAgBxN,OAAO2N,EAAAA,EAAAA,IAAyBvN,GAAGP,QAAU8G,EAAAA,IAGlF,IAAIuG,EAA+D,GAKnE,OAJIb,EAAczD,MAAMuE,eACtBD,EAAmBjJ,EAA+BC,EAAaU,EAAQ,GAAG0D,aAI1E,kBAAC9H,OAAAA,CAAKF,UAAWgE,EAAO0J,2BACrB5B,GACC,oCACE,kBAACyB,GAAgBA,CAACjJ,QAASA,EAASsD,SAAUmE,IAC9C,kBAACpG,EAA0BA,CACzBG,KAAM,UACND,YAAasB,EACb7C,QAASuB,EACTD,SAAQsB,IAAiBiB,EAAAA,EAAAA,IAAoBjB,EAAe/H,UAC5DqF,cAAeuH,IAEjB,kBAACpG,EAA0BA,CACzBG,KAAM,UACND,YAAasB,EACb7C,QAASuB,EACTD,SAAQsB,IAAiBgB,EAAAA,EAAAA,IAAoBhB,EAAe/H,UAC5DqF,cAAeuH,KAIrB,kBAAC5H,SAAAA,CAAOnE,UAAWgE,EAAOI,qBACvBwI,EAAiBjM,OAASiM,GAAmBI,EAAAA,EAAAA,GAAiB1I,EAAS,IAAI,KAIpF,CAEO,MAAMqJ,IAA8B1G,EAAAA,EAAAA,MAAKuG,ICvCjC,SAASI,IAAc,UACpCvD,EAAS,qBACTyB,EAAoB,mBACpBD,EAAkB,QAClBvH,EAAO,UACPsH,EAAS,YACThI,EAAW,MACXyD,EAAK,SACLwG,IAEA,MAAMC,GAAQ7J,EAAAA,EAAAA,YAAWC,EAAAA,IACnB3E,EAAoD+E,EAAQ,GAAG0D,WAC/D+F,EAAcF,EAIpB,GAAIvJ,EAAQ,KAAOmH,EAAAA,GACjB,OAAO,kBAACtH,SAAAA,CAAOnE,UAAW8N,EAAM1J,qBAAsB4J,EAAAA,IAGxD,GAAI1J,EAAQ,KAAOoH,EAAAA,GACjB,OAAO,kBAACvH,SAAAA,CAAOnE,UAAW8N,EAAM1J,qBAAsB6J,EAAAA,IAGxD,GAAI3J,EAAQ,KAAO4J,EAAAA,GACjB,OAAO,kBAAC/J,SAAAA,CAAOnE,UAAW8N,EAAM1J,qBAAsB+J,EAAAA,IAGxD,GAAI7J,EAAQ,KAAO4J,EAAAA,GACjB,OAAO,kBAAC/J,SAAAA,CAAOnE,UAAW8N,EAAM1J,qBAAsB7E,EAAM,KAG9D,GAAI+E,EAAQ,KAAOmE,EAAAA,GACjB,OAAO,kBAACL,EAAsBA,CAACR,SAAUP,IAI3C,GAAI+G,GAAeL,EAAazJ,GAC9B,OACE,kBAACqH,EAAaA,CACZI,cAAe1E,EACf/C,QAASA,EACTsH,UAAWA,EACXvB,UAAWA,EACXwB,mBAAoBA,EACpBjI,YAAaA,EACbkI,qBAAsBA,IAM5B,GAAIuC,GAAiBN,EAAazJ,GAChC,OACE,kBAACqJ,GAA2BA,CAC1BrJ,QAASA,EACTV,YAAaA,EACbmI,cAAe1E,EACfoG,cAAepD,EAAU/B,MAAMtJ,QAC/B6M,mBAAoBA,EACpBC,qBAAsBA,IAM5B,GAAIwC,GAAgBhK,KAAYiK,EAAAA,EAAAA,UAASjK,EAAQ,IAAK,C,IAC/BsH,EAArB,MAAM4C,EAA2C,QAA5B5C,EAAAA,EAAUS,OAAO/H,EAAQ,WAAzBsH,IAAAA,OAAAA,EAAAA,EAA+BL,EAAAA,IACpD,OAAO,kBAACpH,SAAAA,CAAOnE,UAAWyO,EAAAA,IAA6BD,EACzD,CAGA,OAAIlD,EAAgBhH,GACX,KAGF,kBAACH,SAAAA,CAAOnE,UAAW8N,EAAM1J,qBAAsB7E,EAAM,IAC9D,CAOA,MAAM6O,GAAiB,CAACL,EAAuBzJ,IAE3B,WAAhByJ,GACgB,UAAhBA,GACAzJ,EAAQ,KAAOiH,EAAAA,MACdjC,EAAAA,EAAAA,IAAehF,EAAQ,GAAG0D,aAC3B1D,EAAQ,KAAOmE,EAAAA,MACd8F,EAAAA,EAAAA,UAASjK,EAAQ,IAShB+J,GAAmB,CAACN,EAAuBzJ,MAE5B,WAAhByJ,GAA4C,UAAhBA,IAC5BzE,EAAAA,EAAAA,IAAehF,EAAQ,GAAG0D,aAC3B1D,EAAQ,KAAOmE,EAAAA,KACd8F,EAAAA,EAAAA,UAASjK,EAAQ,KAIhBgK,GAAmBhK,GAChBA,EAAQ,KAAOmE,EAAAA,G,eC/GxB,MAAMiG,GAAalM,IACV,CACLyD,QAAQW,EAAAA,EAAAA,KAAI,CACV,UAAW,CACTzD,MAAOX,EAAMC,OAAOM,QAAQF,UAMpC,GAtCA,UAA4B,QAAE8L,IAC5B,MAAM3K,GAASC,EAAAA,EAAAA,YAAWyK,IAC1B,IAAIE,EACJ,IACEA,EAAiBC,KAAKC,MAAMH,EAC9B,CAAE,MAAOhP,GACPC,EAAAA,EAAOgD,MAAMjD,EAAG,CAAEoP,IAAK,2CACzB,CAEA,MAAMC,GAAyBC,EAAAA,GAAAA,IAAkCL,GACjE,OAAII,EAEA,kBAACE,EAAAA,WAAUA,CACTlP,UAAWgE,EAAOiC,OAClBkJ,KAAM,oBACN3I,QAAS,YACTlG,KAAM,KACNuI,KAAM,OACNuG,KAAMJ,EAAuBI,KAC7BC,OAAQ,UAEPL,EAAuBvI,MAKvB,IACT,ECnCa6I,GAA4C,CAEvD,qBAAsB,8BACtB,kBAAmB,0BACnB,oBAAqB,gCACrB,kBAAmB,sBACnB,iBAAkB,wBAClB,kBAAmB,sBAGnB,iBAAkB,mGAClB,mBAAoB,+DACpB,gBAAiB,0BACjB,iBAAkB,wDAClB,qBAAsB,sCCMT,SAASC,IAAc,QAAEjL,EAAO,YAAEV,EAAW,cAAE4L,EAAa,MAAEnI,IAC3E,GAAIiE,EAAgBhH,GAClB,OAAO,KAET,MAAM/E,EAAQiQ,aAAAA,EAAAA,EAAexH,WAG7B,IAAKzI,EACH,OAAO,KAIT,GAAI+E,EAAQ,KAAO4J,EAAAA,GACjB,OAAO,kBAACuB,GAAkBA,CAACd,QAASpP,IAItC,GAAI8H,EAAMiB,MAAMuE,aAAc,CAE5B,IAAKrB,EAAmBlH,GAAU,CAChC,IAAI/D,EAAaoD,EAA+BC,EAAarE,GAG7D,GAAIgB,EAAWI,OACb,OAAOJ,CAEX,CAGA,IAAImP,EAAwD,GAa5D,GAVAC,OAAO1D,KAAKqD,IAAmBM,KAAMlQ,KACrBH,EAAMF,MAAMiQ,GAAkB5P,MAE1CgQ,Ef/BD,SAAmCpQ,EAAiBC,EAAeS,GACxE,MAAM6D,EAAoB3C,EAAqB5B,EAAOC,GAChDe,EAAOuB,EAAiBgC,GAC9B,IAAItD,EAAuC,GAK3C,OAHIsD,EAAkBlD,SACpBJ,EAAaH,EAA4ByD,EAAmBtE,EAAOe,EAAMN,IAEpEO,CACT,CesB6BsP,CAA0B,CAACP,GAAkB5P,IAAOH,EAAOG,IACzE,IAMPgQ,EAAmB/O,OACrB,OAAO+O,CAEX,CAEA,OAAOnQ,CACT,C,gDCvDA,SAASuQ,IAAa,cACpBC,EAAa,SACbnI,EAAQ,iBACRoI,IAMA,MAAMhM,GAASC,EAAAA,EAAAA,YAAWyK,IACpBuB,EAAaN,OAAO1D,KAAKiE,GAAAA,IAAwBjI,KAAMjI,GAC3D+P,EAAc1Q,MAAM6Q,GAAAA,GAAuBlQ,KAEvCkH,EAAiB8I,EAAiBJ,KACrC3Q,GAAWA,EAAOM,QAAUwQ,GAAiB9Q,EAAOE,WAAamI,EAAAA,GAASC,OAG7E,OAAOvB,EAAAA,EAAAA,SACL,IACE,kBAACmK,EAAAA,QAAOA,CACNC,SAASC,EAAAA,GAAAA,GACP,4CACAnJ,EAAiB,UAAU6I,WAAyB,qBAAqBA,YAG3E,kBAAC9J,SAAAA,CACCE,QAAUxG,IACRA,EAAEyG,mBACFlB,EAAAA,EAAAA,IAAaqH,EAAAA,GAAsBwD,EAAe,SAAUnI,EAAU4E,EAAAA,KAExExM,UAAW,GAAGiQ,KAAcjM,EAAOsM,qBAElCP,EAAcQ,gBAIrB,CAACrJ,EAAgB6I,EAAeE,EAAYrI,EAAU5D,EAAOsM,mBAEjE,CAEO,MAAME,IAAmBvJ,EAAAA,EAAAA,MAAK6I,IAE/BpB,GAAalM,IACV,CACL8N,mBAAmB1J,EAAAA,EAAAA,KAAI,CACrB6J,OAAQC,GACRC,WAAY,OACZC,WAAYpO,EAAMc,WAAWuN,oBAC7BC,WAAY,OACZC,WAAY,OACZC,OAAQ,OACRC,SAAU,QAEVC,QAAS1O,EAAM2O,QAAQ,EAAG,GAAK,EAAG,IAClC,mBAAoB,CAClBJ,WAAYvO,EAAMC,OAAOsO,WAAWK,c,0BC5D5C,MAAMC,GAAwB,IACxBC,IAAyBjB,EAAAA,GAAAA,GAAE,0CAA2C,qBACtEkB,IAAyBlB,EAAAA,GAAAA,GAAE,+BAAgC,yBAC3DmB,IAAuBnB,EAAAA,GAAAA,GAAE,gDAAiD,uBAC1EoB,IAAepB,EAAAA,GAAAA,GAAE,wCAAyC,UAEjD,SAASqB,IAAsB,QAC5CvL,EAAO,gBACPC,GAAkB,EAAI,KACtBN,EAAO,SAMP,MAAM6L,EAAuB,SAAT7L,EAAkBwL,GAAyBC,IACxDK,EAAQC,GAAaC,IAAAA,UAAe,IACpCC,EAAYC,GAAiBF,IAAAA,SAAeL,IAC7CQ,GAAYC,EAAAA,EAAAA,QAAiC,MAC7ClO,GAASC,EAAAA,EAAAA,YAAWyK,IAgB1B,OAdAyD,EAAAA,EAAAA,WAAU,KACR,IAAIC,EAQJ,OANIR,IACFQ,EAAYC,WAAW,KACrBR,GAAU,IACTR,KAGE,KACLiB,OAAOC,aAAaH,KAErB,CAACR,IAGF,oCACGA,GACC,kBAACY,EAAAA,YAAWA,CAACC,UAAU,MAAMC,iBAAkBT,EAAUU,SACtDZ,GAGL,kBAACtM,GAAAA,QAASA,CACRzF,UAAWgE,EACX4O,eAAchB,EACd1L,QAAS0L,EAAS,GAAKD,EACvBlL,KAAMX,EACN+M,IAAKZ,EACL9L,QAAUxG,IACJyG,GAEFzG,EAAEyG,kBAEJ,IACED,IACA6L,EAAcP,GAChB,CAAE,MAAO9R,GACPqS,EAAcR,GAChB,CACAK,GAAU,IAEZiB,SAAU,IAIlB,CAEA,MAAMpE,GAAalM,IACVoE,EAAAA,EAAAA,KAAI,CACTzD,MAAOX,EAAMC,OAAOI,KAAKiE,YC5DtB,SAASiM,IAAyB,MAAE1L,EAAK,QAAE/C,IAChD,MAAM0O,EAAYjO,EAAAA,GAAWkO,aAAa5L,GAAOiB,MAAM/I,MACvD,OACE,oCACE,kBAACmS,GAAqBA,CAACvL,QAAS,IAAM+M,GAAY5O,EAASS,EAAAA,GAAWoO,QAAQ9L,MAC9E,kBAACqK,GAAqBA,CACpB5L,KAAM,YACNK,QAAS,IAgBjB,SAAsB7B,EAAkB0O,EAAsBI,GAC5D,MAAMC,EAAqCD,aAAAA,EAAAA,EAAUE,OAAOrL,KAAMnI,IAAMyT,EAAAA,EAAAA,IAAczT,EAAE2G,OAClF+M,EAAelP,EAAQ,GAC7B,KAAKiK,EAAAA,EAAAA,UAASiF,GAAe,CAC3B,MAAM5Q,EAAQ6Q,MAAM,sBAEpB,MADA7T,EAAAA,EAAOgD,MAAMA,EAAO,CAAEmM,IAAK,iCACrBnM,CACR,CACA,MAAM8Q,EAAQL,aAAAA,EAAAA,EAAShH,OAAOmH,GACxBG,GAAcC,EAAAA,GAAAA,IAAqB,eAAgB,CAAEC,GAAIH,EAAOI,IAAKN,GAAgBR,IAC3Fe,EAAAA,GAAAA,IAASJ,EACX,CA3BuBK,CAAa1P,EAAS0O,EAAW3L,EAAMiB,MAAM8K,YAIpE,CAEA,MAAMF,GAAc,CAAC5O,EAAkB2P,KACrC,MAAMT,EAAelP,EAAQ,GACvB4P,GAAYC,EAAAA,GAAAA,IAAkBF,EAAM3L,MAAM8L,MAC1CxI,EAAYsI,aAAAA,EAAAA,EAAWZ,OAAOrL,KAAMnI,IAAMwJ,EAAAA,EAAAA,IAAexJ,EAAE2G,OACjE,IAAI8H,EAAAA,EAAAA,UAASiF,IAAiB5H,EAAW,CACvC,MAAMyI,EAAOzI,EAAUS,OAAOmH,IAC9BO,EAAAA,GAAAA,IAASM,EAAKrM,WAChB,GCNa,SAASsM,IAAW,KAAEF,EAAI,WAAEG,EAAU,SAAEC,EAAQ,QAAElQ,EAAO,MAAE+C,EAAK,UAAEoN,IAC/E,MAAMzQ,GAASC,EAAAA,EAAAA,YAAWyK,IAC1B,GAAI0F,IAAQM,EAAAA,GAAAA,IAAQN,EAAM7I,EAAAA,KAA+C,iBAAd6I,EAAKO,KAC9D,OAAO,kBAAC5B,GAAwBA,CAACzO,QAASA,EAAS+C,MAAOA,IAI5D,GAAI/C,EAAQ,KAAOmE,EAAAA,GACjB,OACE,kBAACvI,OAAAA,CAAKF,UAAW4U,EAAAA,IACdJ,EAAS,IAAED,GAMlB,GAAIjQ,EAAQ,KAAOkE,EAAAA,EAAuB,CACxC,MAAMuH,EAAgB8E,GAAqBxN,EAAO/C,GAElD,GAAIyL,EACF,OACE,kBAACS,GAAgBA,CAAC5I,SAAUP,EAAO0I,cAAeA,EAAeC,iBAAkByE,EAAUnM,MAAMtJ,SAGzG,CAGA,OAAIsF,EAAQ,KAAO4J,EAAAA,GAEf,kBAAChO,OAAAA,CAAKF,UAAWgE,EAAO8Q,SACtB,kBAAC/L,EAAAA,KAAIA,CAACzI,KAAM,KAAMmG,KAAM,UAMvB,kBAACvG,OAAAA,CAAKF,UAAWgE,EAAO8Q,SAAUN,EAC3C,CAEA,MAAMK,GAAuB,CAACxN,EAAsB/C,K,IACH+C,EAA/C,MAAM0N,EAA6D,QAApB1N,EAAAA,EAAMiB,MAAM8K,gBAAZ/L,IAAAA,OAAAA,EAAAA,EAAsBiM,OAAOrL,KACzEnI,GAAMA,EAAEgG,OAASkP,EAAAA,UAAUC,QAASC,EAAAA,EAAAA,IAAcpV,EAAE2G,OAEjD7E,EAA8B,iBAAf0C,EAAQ,GAAkBA,EAAQ,QAAKuC,EACtDsO,OAAmBtO,IAAVjF,EAAsBmT,aAAAA,EAAAA,EAAa1I,OAAOzK,QAASiF,EAClE,OAAOsO,aAAAA,EAAAA,EAAS5I,EAAAA,KAGZmC,GAAalM,IAA0B,CAC3CsS,SAASlO,EAAAA,EAAAA,KAAI,CACXzD,MAAOX,EAAMC,OAAO2S,UAAU5S,EAAMC,OAAOI,KAAKiE,UAAW,KAC3DuO,QAAS,cACTC,WAAY,SACZ7E,OAAQ,W,gBCtFZ,WAAyB,IAAI,UAAU8E,SAAS,IAAI,CAAC,IAAI5V,EAAE4V,SAASC,cAAc,SAAS7V,EAAE8V,YAAYF,SAASG,eAAe,knJAAknJH,SAASI,KAAKF,YAAY9V,EAAE,CAAC,CAAC,MAAMiW,GAAGC,QAAQjT,MAAM,iCAAiCgT,EAAE,CAAE,CAA70J,GAOA,MAAyFE,GAAI,CAC3FC,MADQ,iBAERC,cAF8B,4BAG9BC,WAH+D,wBAKjE,SAASC,IACPC,WAAYxW,EAAI,SAChByW,SAAU/F,EACVlK,QAASkQ,IAET,OAAuB,WACrB,MACA,CACEC,KAAM,SACN,gBAAiBjG,EACjByC,SAAU,EACV3M,QAASkQ,EACTE,UAAYtU,KACC,UAAVA,EAAEvC,KAA6B,MAAVuC,EAAEvC,OAAiBuC,EAAEuU,iBAAkBH,MAE/DrW,UAAW,GAAG8V,GAAEC,SAAS1F,EAAIyF,GAAEE,cAAgB,MAAY,WAANrW,EAAiBmW,GAAEW,sBAAwBX,GAAEY,wBAClGC,SAAU,CACR,IACM,WAANhX,IAAkC,UAAE,MAAO,CAAEK,UAAW,GAAG8V,GAAEG,aAAcU,SAAU,QAI7F,CAOA,SAASC,GAAEjX,EAAG0Q,EAAGgG,EAAGpU,EAAI,EAAG2T,EAAI,KAC7B,IAAIiB,EACJ,GAAU,WAANlX,EAAgB,CAClB,IAAImX,EAAInH,OAAOoH,oBAAoB1G,GACnCgG,GAAKS,EAAE9U,MAAW,IAANqU,OAAW,EAASA,GAAIS,EAAIA,EAAElN,MAAM3H,EAAG2T,EAAI,GAAIiB,EAAI,CAC7DG,QAASF,EAAEtX,IAAKyX,IAAM,CAAGvX,IAAKuX,EAAG1X,MAAO8Q,EAAE4G,MAE9C,MAAO,GAAU,UAANtX,EACTkX,EAAI,CACFG,QAAS3G,EAAEzG,MAAM3H,EAAG2T,EAAI,GAAGpW,IAAI,CAACsX,EAAGG,KAAM,CAAGvX,IAAKuX,EAAIhV,EAAG1C,MAAOuX,UAE9D,CACH,IAAIA,EAAI,EACR,MAAMG,EAAI,GACV,IAAIlW,GAAI,EACR,MAAMmW,EAlBV,SAAWvX,GACT,MAAuB,mBAATA,EAAEwX,GAClB,CAgBcC,CAAE/G,GACZ,IAAK,MAAMgH,KAAKhH,EAAG,CACjB,GAAIyG,EAAIlB,EAAG,CACT7U,GAAI,EACJ,KACF,CACAkB,GAAK6U,IAAMI,GAAKI,MAAMC,QAAQF,GAAoB,iBAARA,EAAE,IAAiC,iBAARA,EAAE,GAAiBJ,EAAErW,KAAK,CAAElB,IAAK2X,EAAE,GAAI9X,MAAO8X,EAAE,KAAQJ,EAAErW,KAAK,CAClIlB,IAAK,UAAUoX,KACfvX,MAAO,CACL,QAAS8X,EAAE,GACX,UAAWA,EAAE,MAEZJ,EAAErW,KAAK,CAAElB,IAAKoX,EAAGvX,MAAO8X,KAAOP,GACtC,CACAD,EAAI,CACFW,SAAUzW,EACViW,QAASC,EAEb,CACA,OAAOJ,CACT,CACA,SAASY,GAAE9X,EAAG0Q,EAAGgG,GACf,MAAMpU,EAAI,GACV,KAAOoO,EAAI1Q,EAAI0W,EAAIA,GACjBA,GAAQA,EACV,IAAK,IAAIT,EAAIjW,EAAGiW,GAAKvF,EAAGuF,GAAKS,EAC3BpU,EAAErB,KAAK,CAAE8W,KAAM9B,EAAG+B,GAAIvV,KAAKwV,IAAIvH,EAAGuF,EAAIS,EAAI,KAC5C,OAAOpU,CACT,CACA,SAAS4V,GAAGlY,EAAG0Q,EAAGgG,EAAGpU,EAAG2T,EAAI,EAAGiB,EAAI,KACjC,MAAMC,EAAIF,GAAEkB,KACV,KACAnY,EACA0Q,EACAgG,GAEF,IAAKpU,EACH,OAAO6U,IAAIE,QACb,MAAMC,EAAIJ,EAAI,IAAO9V,EAAIqB,KAAKwV,IAAIf,EAAIjB,EA3DxC,SAAWjW,EAAG0Q,GACZ,MAAa,WAAN1Q,EAAiBgQ,OAAO1D,KAAKoE,GAAG1P,OAAe,UAANhB,EAAgB0Q,EAAE1P,OAAS,GAC7E,CAyD2CoX,CAAEpY,EAAG0Q,IAC9C,GAAU,aAAN1Q,GACF,GAAIoB,GAAKkB,GAAKA,EAAI,EAChB,OAAO6U,EAAElB,EAAGiB,GAAGG,aACZ,GAAIjW,GAAKkB,IAAMgV,EACpB,OAAOH,EAAElB,EAAGiB,GAAGG,QACjB,IAAIE,EACJ,GAAU,aAANvX,EAAkB,CACpB,MAAQ6X,QAASH,EAAGL,QAASgB,GAAMlB,EAAElB,EAAGA,EAAI3T,EAAI,GAChDiV,EAAIG,EAAI,IAAIW,KAAMP,GAAE7B,EAAI3T,EAAG2T,EAAI,EAAI3T,EAAI,EAAGA,IAAM+V,CAClD,MACEd,EAAID,EAAIQ,GAAE7B,EAAGiB,EAAG5U,GAAK,IAChB6U,EAAE,EAAG7U,EAAI,GAAG+U,WACZS,GAAExV,EAAI,EAAGlB,EAAI,EAAGkB,MAChB6U,EAAE/V,EAAI,EAAGA,EAAI,GAAGiW,SAEvB,OAAOE,CACT,CACA,MAAiCe,GAAI,CACnCC,UADS,sBAGX,SAASC,GAAGxY,GACV,MAAQ+X,KAAMrH,EAAGsH,GAAItB,EAAG+B,iBAAkBnW,EAAG4L,SAAU+H,EAAGyC,aAAcxB,EAAGvS,QAASwS,GAAMnX,EAC1F,IAAIsX,GAAI,EACR,GAAIJ,EAAG,CACL,MAAOmB,GAAKnB,EACZyB,GAAEzB,EAAEjN,OAAkB,EAAZkN,EAAEnW,QAAcmW,IAAMkB,EAAI3H,GAAK2H,GAAK3B,IAAMY,GAAI,EAC1D,CACA,MAAOlW,EAAGmW,IAAK,cAAED,GAAII,GAAI,iBAAE,KACzBH,GAAGnW,IACF,CAACA,IACJ,OAAOA,GAAoB,UAAE,MAAO,CAAEf,UAAW,GAAGiY,GAAEC,YAAavB,SAAU1U,EAAEtC,EAAG0Q,EAAGgG,MAAwB,WAAE,MAAO,CAAErW,UAAW,GAAGiY,GAAEC,YAAavB,SAAU,EAC7I,UACdT,GACA,CACErI,SAAU+H,EACVQ,UAAU,EACVjQ,QAASkR,EACTlB,WAAY,WAGhB,GAAG9F,SAASgG,MAEhB,CACA,MAA0hBnU,GAAI,CAC5hBqW,WADS,sBAETC,qBAFqC,kCAGrCC,mBAH6E,iCAI7EC,mBAJoH,iCAKpHC,2BAL2J,2CAM3JC,oBAN4M,gCAO5MC,qBAPkP,iCAQlPC,mBARyR,+BASzRC,gBAT8T,4BAU9TC,0BAVgW,wCAWhWC,wBAX8Y,sCAY9YC,SAZ0b,qBAa1bC,mBAbqd,+BAcrdC,iBAd0f,8BAendC,GAAjC,gCAELC,GAAI,EACL3C,SAAUhX,EACVyW,SAAU/F,EACVkJ,WAAYlD,EACZxI,SAAU5L,EACVqC,QAASsR,EACT5V,UAAW6W,EACXwB,aAAcvB,MAEd,IAAIO,EAAGW,EACP,MAAMf,EAAI,SAAS,MAAOlW,OAAU,IAAN+V,GAAgBwB,GAAExB,EAAGlB,GACnD,YAAY,KACVqB,EAAEtE,SAAWsE,EAAEtE,QAAQ6G,eAAe,CAAEC,SAAU,UACjD,IACH,MAAMvC,EAAInW,EAAI,CAAE8R,IAAKoE,EAAG,gBAAiB,QAAW,CAAC,EACrD,OAAOZ,GAAoB,UACzB,KACA,CACEC,KAAM,WACN,gBAAiBjG,EACjB,gBAAiBpO,EACjB,eAAgB2T,EAAE,GAClB,aAA4B,OAAbyB,EAAIzB,EAAE,SAAc,EAASyB,EAAErP,WAC9ChI,UAAW,GAAG6W,KAAK9V,EAAIsY,GAA0B,QAC9CnC,EACHP,SAAUhX,KAEM,UAClB,KACA,CACE2W,KAAM,WACN,gBAAiBrU,EACjB,eAAgB2T,EAAE,GAClB,aAA4B,OAAboC,EAAIpC,EAAE,SAAc,EAASoC,EAAEhQ,WAC9ChI,UAAW6W,KACRK,EACHP,SAAUhX,KAOhB,SAAS+Z,GAAE/Z,EAAG0Q,EAAGgG,GACf,MACExI,SAAU5L,EACVmS,KAAMwB,EACN+D,gBAAiB9C,EACjB+C,cAAe9C,EACfxS,QAAS2S,EACT4C,iBAAkB9Y,EAClB+Y,eAAgB5C,EAChBmB,aAAchB,GACZ1X,EAAGqY,EAAI,GACX,OAAOH,GACL5V,EACA2T,EACAsB,EACAL,EACAxG,EACAgG,GACAhV,QAASvB,IACT,GAtBJ,SAAYH,GACV,YAAgB,IAATA,EAAEgY,EACX,CAoBQoC,CAAGja,GACLkY,EAAEpX,MACgB,mBACduX,GACA,IACKxY,EACHD,IAAK,cAAcI,EAAE4X,QAAQ5X,EAAE6X,KAC/BD,KAAM5X,EAAE4X,KACRC,GAAI7X,EAAE6X,GACNS,iBAAkBsB,GAClBrB,aAAchB,SAIjB,CACH,MAAQ3X,IAAKsa,EAAGza,MAAO0a,GAAMna,EAAGoa,EAAIpD,EAAE1L,SAAS6O,GAC/CjC,EAAEpX,MACgB,mBACduZ,GACA,IACKxa,EACHka,iBAAkB9Y,EAClB4Y,gBAAiB9C,EACjBnX,IAAK,SAASsa,IACd1V,QAAS,CAAC0V,KAAM/C,GAChB1X,MAAOwB,EAAEkZ,GACTL,cAAe,IAAI9C,EAAGmD,GACtBG,WAAYF,EACZG,UAAU,IAIlB,IACErC,CACN,CACA,SAASsC,GAAE3a,GACT,MACEia,cAAevJ,EAAI,GACnBsJ,gBAAiBtD,EACjBkE,iBAAkBtY,EAClBmS,KAAMwB,EACN2D,WAAY1C,EACZ2D,cAAe1D,EACfuD,SAAUpD,EACVwD,eAAgB1Z,EAChBqZ,WAAYlD,EACZ5S,QAAS+S,EACTqD,cAAe1C,EACf2C,MAAO7a,EAAI,EACX+N,SAAUmM,EACVY,kBAAmBX,EACnBY,0BAA2BX,EAC3B7B,aAAcyC,GACZnb,EAAGob,EAAa,SAAT1D,EAAE,GAAe2D,EAAIja,EAAI8V,IAAMkE,GAAKha,EAAI8V,EAAGoE,EAAIpE,GAAKmE,GAAIrQ,EAAGuQ,IAAK,eAEzEhE,GAASgD,EAAE7C,EAAGzB,EAAG9V,IAChBqb,GAAI,iBAAE,KACPF,GAAKC,GAAGvQ,IACP,CAACsQ,EAAGtQ,IAAKyQ,EAAIzQ,GAAKsM,GAAW,IAANnX,EAAU4Z,GAAE,IAAK/Z,EAAGia,cAAevJ,EAAGsK,MAAO7a,EAAI,IAAO,KAM/Eub,EAAIvE,EACLkD,EACApE,GAR0G,UAC1G,OACA,CACE5V,UAAW,GAAGkC,GAAEwW,sBAAsB/N,EAAIzI,GAAEyW,2BAA6B,KACzEhC,SAAUsD,IAMZhY,EAAE2T,EAAGS,GACLgB,GACCiE,EAAI,CAACjE,EAAG2C,EAAGrP,EAAGsQ,GACjB,OAAOhE,GAAoB,UACzBqC,GACA,CACEjB,aAAcyC,EACdvB,WAAY0B,EACZ7E,SAAUzL,EACVkD,SAAUmM,EACV1V,QAAS+S,EACTrX,UAAW,GAAGkC,GAAEgX,YAAYvO,EAAIzI,GAAEqZ,iBAAmB,MAAM1E,EAAI3U,GAAEiX,mBAAqB,KACtFxC,UAA0B,UAAE,KAAM,CAAE3W,UAAW,GAAGkC,GAAEkX,mBAAoBzC,SAAUyE,OAElE,WAClB9B,GACA,CACEjB,aAAcyC,EACdvB,WAAY0B,EACZ7E,SAAUzL,EACVkD,SAAUmM,EACV1V,QAAS+S,EACTrX,UAAW,GAAGkC,GAAEqW,eAAe5N,EAAIzI,GAAEuW,mBAAqB,MAAMwC,EAAI/Y,GAAEsW,qBAAuB,KAC7F7B,SAAU,EACQ,WAAE,OAAQ,CAAE3W,UAAWkC,GAAE0W,oBAAqBjC,SAAU,CACtEqE,IAAqB,UACnB9E,GACA,CACErI,SAAUmM,EACV5D,SAAUzL,EACVxE,QAASgV,KAGG,UACd,OACA,CACE,gBAAiBnB,EACjB,eAAgB3C,EAAE,GAClBrX,UAAW,GAAGkC,GAAE6W,mBAAmBpO,EAAIzI,GAAE+W,wBAA0B,MAAMgC,EAAI/Y,GAAE8W,0BAA4B,KAC3G7S,QAASgV,EACTxE,SAAUqB,KAAKsD,MAGH,UAAE,OAAQ,CAAEtb,UAAWkC,GAAE2W,qBAAsB1S,QAASgV,EAAGxE,SAAU0E,OAEvFD,IAAqB,UAAE,KAAM,CAAEpb,UAAWkC,GAAE4W,mBAAoBnC,SAAUyE,MAIlF,CACA,SAASI,GAAG7b,GACV,MAAM0Q,EAAIV,OAAOoH,oBAAoBpX,GAAGgB,OACxC,MAAO,GAAG0P,KAAW,IAANA,EAAU,OAAS,OACpC,CACA,SAASoL,IAAKrH,KAAMzU,KAAM0Q,IACxB,OAAuB,UACrBiK,GACA,IACKjK,EACH+D,KAAMzU,EACNkO,SAAU,SACV+M,kBAAkC,UAAfvK,EAAExC,SAAuB,UAAY,KACxD0M,iBAAkBiB,GAClBjC,WAAY5J,OAAOoH,oBAAoBpX,GAAGgB,OAAS,GAGzD,CACA,SAAS+a,GAAG/b,GACV,MAAO,GAAGA,EAAEgB,UAAuB,IAAbhB,EAAEgB,OAAe,QAAU,QACnD,CACA,SAASgb,IAAKvH,KAAMzU,KAAM0Q,IACxB,OAAuB,UACrBiK,GACA,IACKjK,EACH+D,KAAMzU,EACNkO,SAAU,QACV+M,kBAAmB,KACnBL,iBAAkBmB,GAClBnC,WAAY5Z,EAAEgB,OAAS,GAG7B,CACA,SAASib,GAAGjc,EAAG0Q,GACb,IAAIgG,EAAI,EAAGpU,GAAI,EACf,GAAI4Z,OAAOC,cAAcnc,EAAEW,MACzB+V,EAAI1W,EAAEW,UAEN,IAAK,MAAMsV,KAAKjW,EAAG,CACjB,GAAI0Q,GAAKgG,EAAI,EAAIhG,EAAG,CAClBpO,GAAI,EACJ,KACF,CACAoU,GAAK,CACP,CACF,MAAO,GAAGpU,EAAI,IAAM,KAAKoU,KAAW,IAANA,EAAU,UAAY,SACtD,CACA,SAAS0F,GAAGpc,GACV,OAAuB,UACrB2a,GACA,IACK3a,EACHkO,SAAU,WACV+M,kBAAmB,KACnBL,iBAAkBqB,GAClBrC,YAAY,GAGlB,CACA,MAAsIyC,GAAI,CACxIC,UADS,qBAETC,eAFoC,0BAGpCC,eAHoE,2BAIpEC,kBAJqG,+BAMvG,SAASC,IACPxO,SAAUlO,EACV+a,cAAerK,EACf/L,QAAS+R,EACTiG,cAAera,EACf1C,MAAOqW,EACP2G,YAAa1F,EAAKI,GAAMA,EACxBoB,aAAcvB,IAEd,MAAMG,EAAI,SAAS,MAAOlW,OAAU,IAAN+V,GAAgBwB,GAAExB,EAAGT,GACnD,YAAY,KACVY,EAAEtE,SAAWsE,EAAEtE,QAAQ6G,eAAe,CAAEC,SAAU,UACjD,IACH,MAAMvC,EAAInW,EAAI,CAAE8R,IAAKoE,EAAG,gBAAiB,QAAW,CAAC,EACrD,OAAuB,WACrB,KACA,CACEjX,UAAW,GAAGgc,GAAEC,wBAAwBtc,gBAAgB0W,EAAE,MAAMtV,EAAIib,GAAEI,kBAAoB,QACvFlF,EACHP,SAAU,EACQ,UAAE,OAAQ,CAAE3W,UAAWgc,GAAEG,eAAgBxF,SAAUtG,EAAEgG,EAAG1W,GAAG,GAAI,MAC/D,UAAE,OAAQ,CAAEK,UAAWgc,GAAEE,eAAgBvF,SAAU1U,EACjE4U,EAAIA,EAAEjB,QAAK,EACXA,KACGS,OAKb,CACA,SAAS8D,IACPK,cAAe7a,EACf2E,QAAS+L,EACTqK,cAAerE,EACf9W,MAAO0C,EACPqa,cAAe1G,EACf4G,aAAc3F,EACd4F,UAAW3F,EACXuB,aAAcpB,KACXlW,IAEH,MAAMmW,EAAIL,EAAE5U,GAAK,SAtbnB,SAAWtC,GACT,MAAM0Q,EAAIV,OAAO+M,UAAU1U,SAAS2U,KAAKhd,GAAGiK,MAAM,GAAI,GACtD,MAAa,WAANyG,GAA+C,mBAAtB1Q,EAAEid,OAAOC,UAA0B,WAAmB,WAANxM,GAAkB1Q,EAAEmd,cAAgBnN,QAAUhQ,aAAagQ,OAAS,SAAWU,CACjK,CAmb8B0M,CAAE9a,GAAIoV,EAAIhH,EAAE,GAAI2H,EAAI,CAC9C1T,QAAS+L,EACTqK,cAAerE,EACfxI,SAAUqJ,EACV3X,MAAO0C,EACPqa,cAAe1G,EACfyC,aAAcpB,GACbnX,EAAI,IACFiB,KACAiX,EACHwC,cAAe7a,EACfyU,KAAMnS,EACNua,aAAc3F,EACd4F,UAAW3F,GAEb,OAAQI,GACN,IAAK,SACL,IAAK,QACL,IAAK,UACL,IAAK,UACH,OAAuB,UAAEuE,GAAI,IAAK3b,GAAKuX,GACzC,IAAK,QACH,OAAuB,UAAEsE,GAAI,IAAK7b,GAAKuX,GACzC,IAAK,WACL,IAAK,MACL,IAAK,MACH,OAAuB,UAAE0E,GAAI,IAAKjc,GAAKuX,GACzC,IAAK,SACH,OAAuB,mBACrBgF,GACA,IACKrE,EACHtY,IAAK2X,EACLkF,YAAcvC,GAAM,GAAGlD,IAAIkD,IAAIlD,MAGrC,IAAK,SAgDL,IAAK,SACH,OAAuB,UAAEuF,GAAG,IAAKrE,GAAKX,GA/CxC,IAAK,UACH,OAAuB,UACrBgF,GACA,IACKrE,EACHuE,YAAcvC,GAAMA,EAAI,OAAS,SAEnC3C,GAEJ,IAAK,OACH,OAAuB,UACrBgF,GACA,IACKrE,EACHuE,YAAcvC,GAAMA,EAAEgD,eAExB3F,GAEJ,IAAK,OACH,OAAuB,UACrBgF,GACA,IACKrE,EACHuE,YAAa,IAAM,QAErBlF,GAEJ,IAAK,YACH,OAAuB,UACrBgF,GACA,IACKrE,EACHuE,YAAa,IAAM,aAErBlF,GAEJ,IAAK,WACL,IAAK,SACH,OAAuB,UACrBgF,GACA,IACKrE,EACHuE,YAAcvC,GAAMA,EAAEhS,YAExBqP,GAIJ,QACE,OAAuB,UACrBgF,GACA,IACKrE,EACHuE,YAAa,IAAM,IAAIrF,MAEzBG,GAGR,CACA,MAAmJ4F,GAAI,CACrJC,KADS,iBAETC,kBAFgC,8BAGhCC,iBAHoE,6BAIpEC,2BAJuG,0CAKtGC,GAAK3d,GAAMA,EAAG4d,GAAK,CAAC5d,EAAG0Q,EAAGgG,IAAY,IAANA,EAASmH,GAAK,CAAC7d,EAAG0Q,EAAGgG,EAAGpU,EAAG2T,KAAsB,WAAE,OAAQ,CAAE5V,UAAWid,GAAEE,kBAAmBxG,SAAU,CACxIN,EACA,IACApU,KACIwb,GAAK,EAAE9d,GAAI0Q,EAAGgG,EAAGpU,KAAsB,WAC3C,OACA,CACEjC,UAAW,GAAGid,GAAEG,oBAAoBnb,EAAIgb,GAAEI,2BAA6B,KACvE1G,SAAU,CACRhX,EACA,OAGH+d,GAAK,KAAM,EACd,SAASC,IACPvJ,KAAMzU,EACN2E,QAAS+L,EAAI,CAAC,QACdqK,cAAerE,EAAIoH,GACnBnB,cAAera,EAAIqb,GACnBzC,0BAA2BjF,EAAI2H,GAC/BlD,SAAUxD,GAAI,EACd4D,eAAgB3D,GAAI,EACpB0D,cAAevD,EAAIuG,GACnB3D,iBAAkB9Y,EAAIuc,GACtBd,aAActF,EAAIwG,GAClB/D,gBAAiBtC,EAAI,GACrByC,eAAgB9B,GAAI,EACpBK,aAAcvY,EACd2c,UAAWzC,EAAI,MAEf,OAAuB,UACrB,KACA,CACE1D,KAAM,OACN,wBAAwB,EACxB,gBAAiB,OACjBtW,UAAWid,GAAEC,KACbvG,UAA0B,UACxBwD,GACA,CACE9B,aAAcvY,EACd2a,eAAgB3D,EAChBxS,QAASuS,EAAI,GAAKxG,EAClB9Q,MAAOwB,EAAEpB,GACT6c,aAActF,EACdwD,cAAerE,EACfiG,cAAera,EACf4Y,0BAA2BjF,EAC3ByE,SAAUxD,EACV2D,cAAevD,EACf4C,iBAAkB9Y,EAClB4Y,gBAAiBtC,EACjByC,eAAgB9B,EAChByE,UAAWzC,KAKrB,CACA,MAAM1B,GAAI,CAAC3Y,EAAG0Q,KACZ,IAAK1Q,EAAEgB,QAAUhB,EAAEgB,SAAW0P,EAAE1P,OAC9B,OAAO,EACT,IAAK,IAAI0V,EAAI,EAAGA,EAAI1W,EAAEgB,OAAQ0V,IAC5B,GAAI1W,EAAE0W,KAAOhG,EAAEgG,GACb,OAAO,EACX,OAAO,G,85BC9jBF,MAAM3F,GAAuB,OAErB,SAASkN,IAAkB,MAAEvW,I,IAsGrB4M,EAAAA,EACDA,EAtGpB,MAAM,WACJ4J,EAAU,cACVC,EAAa,qBACbhS,EAAoB,KACpBiS,EAAI,aACJlR,EAAY,UACZmR,EAAS,YACTC,EAAW,UACXC,EAAS,eACTC,EAAc,KACd/J,EAAI,SACJhB,GACE/L,EAAM+W,WAEJnK,EAAQlP,EAAAA,GAAWoO,QAAQ9L,GACjC4M,EAAMmK,WAEN,MAAMtZ,EAAgBC,EAAAA,GAAWC,YAAYqC,EAAOpC,EAAAA,IAC9C,kBAAEoZ,EAAiB,aAAEC,GAAiBxZ,EAAcsZ,WACpDpa,GAASC,EAAAA,EAAAA,YAAWyK,GAAWyP,GAE/B9T,GAAYC,EAAAA,EAAAA,IAAkBjD,GAC9BkX,GAAenU,EAAAA,EAAAA,IAAsB/C,GACrCoN,GAAY+J,EAAAA,EAAAA,IAAkBnX,GAG9B6M,GAAYC,EAAAA,GAAAA,IAAkBC,GAC9BxI,EAAYsI,aAAAA,EAAAA,EAAWZ,OAAOrL,KAAMwW,GAAUA,EAAM3Y,OAASkP,EAAAA,UAAU0J,SAAUpV,EAAAA,EAAAA,IAAemV,EAAMhY,OACtGoF,EAAqB,IAAI8S,IACzBC,GAAgBC,EAAAA,EAAAA,IAAuBxX,GAEvCgR,GAAyCrS,EAAAA,EAAAA,SAAQ,KACrD,QAAqBa,IAAjByX,EACF,OAEF,MAAMjL,EAAUD,aAAAA,EAAAA,EAAUE,OAAOrL,KAAMwW,IAAUlL,EAAAA,EAAAA,IAAckL,EAAMhY,OAC/DqY,EAAYzL,aAAAA,EAAAA,EAAShH,OAAO5B,UAAW2Q,GAAMA,KAAMkD,aAAAA,EAAAA,EAAczK,KACjEkL,OAA+BlY,IAAdiY,IAA0C,IAAfA,EAAmBA,OAAYjY,EACjF,YAA0BA,IAAnBkY,EAA+B,CAACA,EAAgBtW,EAAAA,SAAmB5B,GACzE,CAACyX,EAAclL,IAElBmL,EAAajW,MAAMtJ,QAAQqC,QAASpC,IAGlC,MAAM+f,EAA6B/f,EAAOM,MACvC0f,UAAU,EAAGhgB,EAAOM,MAAMoB,OAAS,GACnCue,MAAM,YACNrU,KAAK,KACRgB,EAAmBsL,IAAI6H,EAA4B/f,KAErD,MAAMkgB,GAAYjN,EAAAA,EAAAA,QAA8B,MAE1CkN,GAAwBC,EAAAA,EAAAA,aAAY,K,IACxCF,EAAiB,QAAjBA,EAAAA,EAAUxM,eAAVwM,IAAAA,GAAAA,EAAmBG,SAAS,EAAGH,EAAUxM,QAAQ4M,eAChD,IAEGC,GAAqBH,EAAAA,EAAAA,aAAY,K,IACrCF,EAAiB,QAAjBA,EAAAA,EAAUxM,eAAVwM,IAAAA,GAAAA,EAAmBG,SAAS,EAAG,IAC9B,IAEGG,GAAkCJ,EAAAA,EAAAA,aACrCK,IACCrY,EAAMwC,SAAS,CAAEoU,YAAayB,KAC9BC,EAAAA,GAAAA,IAA0BD,IAE5B,CAACrY,IAGGuY,GAAsBP,EAAAA,EAAAA,aACzBK,IACCrY,EAAMwC,SAAS,CAAEmU,UAAW0B,KAC5BG,EAAAA,GAAAA,IAAwBH,IAE1B,CAACrY,IAGGyY,GAAyBT,EAAAA,EAAAA,aAC5BK,IACCrY,EAAMwC,SAAS,CAAEgD,aAAc6S,KAC/BK,EAAAA,GAAAA,IAA2BL,IAE7B,CAACrY,IAGG2Y,GAAwBX,EAAAA,EAAAA,aAC3BY,IACC5Y,EAAMwC,SAAS,CAAEsU,eAAgB8B,KACjCC,EAAAA,GAAAA,IAAa,iBAAkBD,IAEjC,CAAC5Y,IAGG8Y,EAAqBvU,GAAaA,EAAUS,OAAO1L,OAAS,IAAuB,IAAlBmd,EACjEsC,GAAwBtU,IAA+C,IAAvBqU,EAEtD,OACE,kBAACE,MAAAA,CAAIrgB,UAAWgE,EAAOsc,iBAErB,kBAACC,EAAAA,YAAWA,CACVrP,QAAS,OACTsP,gBAAgB,EAChBC,cAA+B,QAAhBxM,EAAAA,EAAM3L,MAAM8L,YAAZH,IAAAA,GAAwB,QAAxBA,EAAAA,EAAkByM,cAAlBzM,IAAAA,OAAAA,EAAAA,EAA2B,GAAG0M,QAC7CC,aAA8B,QAAhB3M,EAAAA,EAAM3L,MAAM8L,YAAZH,IAAAA,OAAAA,EAAAA,EAAkB3L,MAChCuY,MAAO,OACP9C,KAAMA,EAAO,kBAACA,EAAK+C,UAAS,CAACzZ,MAAO0W,SAAWlX,EAC/Cka,QAAS,kBAACC,GAAAA,EAAsBA,CAACC,QAAS5C,EAAmB6C,SAAUpc,EAAcqc,wBAErF,kBAACd,MAAAA,CAAIrgB,UAAWgE,EAAOod,YACpBxV,aAAAA,EAAAA,EAAWS,UAAUT,aAAAA,EAAAA,EAAWS,OAAO1L,QAAS,GAC/C,kBAAC0gB,GAAAA,EAAeA,CACdrB,sBAAuBA,EACvB7B,eAAgBA,EAChBmD,cAAezU,EACfiT,uBAAwBA,EACxByB,aAActD,EACdwB,gCAAiCA,EACjC+B,WAAYxD,EACZ4B,oBAAqBA,EACrB1B,UAAWA,EACXuD,kBAAmBpa,EAAMqa,iBACzBtC,sBAAuBA,EACvBI,mBAAoBA,KAGvB5T,aAAAA,EAAAA,EAAWS,UAAUT,aAAAA,EAAAA,EAAWS,OAAO1L,QAAS,GAC/C,kBAAC0f,MAAAA,CAAIrgB,UAAWgE,EAAO2d,aAAc9O,IAAKsM,GACvCiB,GACC,kBAACwB,EAAAA,MAAKA,CAAC5hB,UAAWgE,EAAO6d,MAAOC,SAAU,UAAWjB,MAAO,uCAAuC,+DAIpGV,GACC,kBAACyB,EAAAA,MAAKA,CAAC5hB,UAAWgE,EAAO6d,MAAOC,SAAU,OAAQjB,MAAO,2BAA2B,4HAKtF,kBAACkB,GAAQA,CACP1J,aAAcA,EACdjE,KAAMxI,EAAUS,OAChBoO,gBAAgB,EAChBgC,UAAW,GACX5B,0BAA2B,CAAClQ,EAAGqX,EAAIrH,IAAUA,GAAS,EAEtDH,cAAe,CAAC3M,EAAUuG,EAAMI,EAAUD,EAAYjQ,IACpD,kBAACgQ,GAAUA,CACTC,WAAYA,EACZjQ,QAASA,EACTkQ,SAAUA,EACVJ,KAAMA,EACNvG,SAAUA,EACVxG,MAAOA,EACPoN,UAAWA,IAIf6H,cAAe,CAAC9M,EAAe7E,KAAMrG,IACnC,kBAACiL,GAAaA,CACZC,cAAeA,EACflL,QAASA,EACTV,YAAagb,EAActW,MAAMtJ,QACjCqI,MAAOA,IAIXqT,cAAe,CAACpW,EAASuJ,IACvB,kBAACD,GAAaA,CACZvG,MAAOA,EACPwG,SAAUA,EACVvJ,QAASA,EACT+F,UAAWA,EACXuB,UAAWA,EACXE,qBAAsBA,EACtBD,mBAAoBA,EACpBjI,YAAagb,EAActW,MAAMtJ,aAM1C6e,GAA2C,KAA7BjS,aAAAA,EAAAA,EAAWS,OAAO1L,SAAgB,kBAACshB,EAAAA,EAAsBnB,UAAS,CAACzZ,MAAOwW,MAKnG,CAEA,MAAMnP,GAAY,CAAClM,EAAsB2b,KACvC,MAAM+D,EAAU1f,EAAM2f,OAClBC,EAAAA,iBAAiBC,MAAMD,EAAAA,iBAAiBE,QAAQ9f,EAAMC,OAAOsO,WAAWwR,OAAQ,IAAM,IACtFH,EAAAA,iBAAiBC,MAAMD,EAAAA,iBAAiBI,OAAOhgB,EAAMC,OAAOsO,WAAWwR,OAAQ,IAAM,IAEnFE,EAAajgB,EAAM2f,OACrBC,EAAAA,iBAAiBI,OAAOJ,EAAAA,iBAAiBC,MAAM7f,EAAMC,OAAO5C,KAAK6iB,YAAa,GAAI,IAClFN,EAAAA,iBAAiBE,QAAQF,EAAAA,iBAAiBC,MAAM7f,EAAMC,OAAO5C,KAAK6iB,YAAa,GAAI,IAEvF,MAAO,CACLb,OAAOjb,EAAAA,EAAAA,KAAI,CACT+b,UAAWngB,EAAM2O,QAAQ,KACzByR,aAAc,IAEhBtC,iBAAiB1Z,EAAAA,EAAAA,KAAI,CAEnBic,QAAS,SACTC,MAAO,OACPrS,OAAQ,SAEV2Q,WAAWxa,EAAAA,EAAAA,KAAI,OACbyO,QAAS,OACT0N,cAAe,cACftS,OAAQ,OACRuS,cAAexgB,EAAM2O,QAAQ,GAC7B8R,aAAczgB,EAAM2O,QAAQ,IACzB5O,EAAuBC,IAAAA,CAC1BqgB,QAAS,aAEXK,WAAWtc,EAAAA,EAAAA,KAAI,CACbuc,gBAAiB,mBACjBhgB,MAAO,UAGTwe,aAAc/a,EAAAA,GAAG;qBACApE,EAAMc,WAAWuN;qBACjBrO,EAAMc,WAAWuN;;iCAELrO,EAAMC,OAAOI,KAAKE;uCACZP,EAAMC,OAAOI,KAAKiE;iCACxBtE,EAAMC,OAAOqE,UAAUsc;qCACnB5gB,EAAM2O,QAAQ,QAAQ3O,EAAM2O,QAAQ;wCACjC3O,EAAM2O,QAAQ;;;;;;yBAM7BmB,OAAO+Q,WAAW,oCAAoCC,QAAU,OAAS;QAC1FC,GAAwB/gB,EAAO2b;;;;;;;;;;uBAUhBzN;;;;;;;sBAODlO,EAAM2O,QAAQ;;;;;;;yBAOX3O,EAAM2O,QAAQ;;;;;;;;6BAQV3O,EAAM2O,QAAQ;sBACrB3O,EAAMC,OAAOsO,WAAWhO;0BACpBP,EAAM2O,QAAQ;yBACf3O,EAAM2O,QAAQ;;;wBAGf3O,EAAM2O,QAAQ;;;;;;;;;;8BAURsR;;4BAEFL,EAAAA,iBAAiBC,MAAM7f,EAAMC,OAAO5C,KAAK6iB,YAAa;;;;;;;;8BAQpDlgB,EAAMC,OAAOsO,WAAWwR;wBAC9B/f,EAAMghB,QAAQC;;;;;;;;;;;;kCAYJjhB,EAAMC,OAAOsO,WAAWwR;4BAC9B/f,EAAMghB,QAAQC;;;;;gCAKVvB;0BACN1f,EAAMghB,QAAQC;;;;;;;;;;;;sBAYlBjhB,EAAMC,OAAOsO,WAAWhO;;;;;8BAKhBP,EAAMC,OAAOsO,WAAWwR;wBAC9B/f,EAAMghB,QAAQC;;;QAOhCF,GAA0B,CAAC/gB,EAAsB2b,KACrD,IAAKA,EACH,OAAOvX,EAAAA,GAAG;;;8wDCtUd,MAAM8c,EAAiB,CACrBC,IAAK,CACHC,KAAM,CACJ9c,UAAW+c,EACX9gB,QAAS+gB,EACTC,MAAOC,GAETC,MAAO,CACLnd,UAAWod,EACXnhB,QAAS+gB,EACTC,MAAOI,IAGX,eAAgB,CACdP,KAAM,CACJ9c,UAAWsd,EACXrhB,QAASshB,EACTN,MAAOO,GAETL,MAAO,CACLnd,UAAWyd,EACXxhB,QAASshB,EACTN,MAAOS,IAGX,cAAe,CACbZ,KAAM,CACJ9c,UAAW2d,EACX1hB,QAAS2hB,EACTX,MAAOY,GAETV,MAAO,CACLnd,UAAW8d,EACX7hB,QAAS2hB,EACTX,MAAOc,IAGX,YAAa,CACXjB,KAAM,CACJ9c,UAAWge,EACXf,MAAOgB,EAEPhiB,QAAS,IAEXkhB,MAAO,CACLnd,UAAWke,EACXjB,MAAOkB,EAEPliB,QAAS,KAGbmiB,KAAM,CACJtB,KAAM,CACJ9c,UAAWqe,EACXpB,MAAOqB,EAEPriB,QAAS,IAEXkhB,MAAO,CACLnd,UAAWue,EACXtB,MAAOuB,EAEPviB,QAAS,MAKT0C,EAAYqM,EAAAA,WAA0D,CAACyT,EAAO1S,KAClF,MAAM,QAAErM,EAAU,YAAW,KAAEC,EAAI,UAAEzG,EAAS,QAAEkG,GAA0Bqf,EAAdC,EAAAA,EAAcD,EAAAA,CAAlE/e,UAAuBC,OAAMzG,YAAWkG,YAE1ClC,GAASC,EAAAA,EAAAA,YAAWyK,EAAWlI,EAASC,EAAMid,GAEpD,IAAI+B,EACAxT,EAIJ,OAHAwT,EAA+B,iBAAZvf,EAAuBA,OAAUW,GAG7Cb,EAAAA,EAAAA,SACL,IACE,gBAACmK,EAAAA,QAAOA,CAAC0C,IAAKA,EAAKzC,QAASlK,GAC1B,gBAACD,SAAAA,E,kUAAAA,CAAAA,CAAAA,EACKuf,GAAAA,CACJ3S,IAAKZ,EACLvL,aAAY+e,EACZzlB,WAAW0lB,EAAAA,EAAAA,IAAG1hB,EAAOiC,OAAQjG,GAC7B8F,KAAK,WAEL,gBAAC5F,OAAAA,CAAKF,UAAWgE,EAAO2hB,QAI9B,CAACF,EAAW5S,EAAK7S,EAAWwlB,EAAWxhB,EAAQkC,EAAS+L,MAG5DxM,EAAUiC,YAAc,YACxB,UAEMgH,EAAY,CAAClM,EAAsBgE,EAA4BC,EAAgBid,KACnF,IAAIkC,EAAYpjB,EAAMC,OAAOI,KAAKE,QAElB,YAAZyD,IACFof,EAAYpjB,EAAMC,OAAOM,QAAQF,MAGnC,MAAMgjB,EAAYrjB,EAAM2f,OAAS,OAAS,QAE1C,MAAO,CACLlc,QAAQW,EAAAA,EAAAA,KAAI,CACVkf,OAAQ,EACRC,SAAU,WACVC,OAAQ,KAAKxjB,EAAM2O,QAAQ8U,WAC3BC,UAAW,OACXlV,OAAQ,OACRqE,QAAS,cACTtE,WAAY,cACZoV,eAAgB,SAChB7Q,WAAY,SACZpE,QAAS,EACT/N,MAAOyiB,EAEP,0BAA2B,CACzBQ,OAAQ,cACRjjB,MAAOX,EAAMC,OAAO+C,OAAO6gB,aAC3B7iB,QAAS,KAGX,2BAA4B,CAC1B8iB,QAAS,yBACTC,cAAe,MACfL,UAAW,aAAa1jB,EAAMC,OAAOsO,WAAWwR,uBAAuB/f,EAAMC,OAAOM,QAAQyjB,OAC5FC,yBAA0B,iCAC1BC,mBAAoB,OACpBC,mBAAoB,uCAGtB,8BAA+B,CAC7BL,QAAS,OACTJ,UAAW,UAGf/W,MAAMvI,EAAAA,EAAAA,KAAI,CACRggB,cAAe,aAEjBjB,KAAK/e,EAAAA,EAAAA,KAAI,CACPigB,gBACc,YAAZrgB,EAAwB,OAAOkd,EAAOjd,GAAMof,GAAW9iB,WAAa,OAAO2gB,EAAOjd,GAAMof,GAAW/e,aACrGgc,MAAO,OACPrS,OAAQ,OAER,WAAY,CACVqS,MAAO,OACPrS,OAAQ,OACRqW,KAAM,EACNhB,QAAS,EACTC,SAAU,WACVviB,QAAS,EACTujB,aAAcvkB,EAAMwkB,MAAMC,OAAOC,QACjC9W,QAAS,KACT+W,UAAW,cACX,CAAC3kB,EAAM4kB,YAAYC,aAAa,gBAAiB,WAAY,CAC3DX,mBAAoB,OACpBD,yBAA0B,+BAC1BE,mBAAoB,YAIxB,UAAW,CACTE,gBAAiB,OAAOnD,EAAOjd,GAAMof,GAAW9B,SAChD,WAAY,CACVZ,gBACc,cAAZ3c,EAA0BhE,EAAMC,OAAO+C,OAAOue,MAAQ3B,EAAAA,iBAAiBC,MAAMuD,EAAW,KAC1FpiB,QAAS,O", "sources": ["webpack://grafana-lokiexplore-app/./services/highlight.tsx", "webpack://grafana-lokiexplore-app/./services/JSONHighlightLineFilterMatches.ts", "webpack://grafana-lokiexplore-app/./Components/ServiceScene/JSONPanel/JSONLabelText.tsx", "webpack://grafana-lokiexplore-app/./services/JSONFilter.ts", "webpack://grafana-lokiexplore-app/./Components/ServiceScene/JSONPanel/JSONNestedNodeFilterButton.tsx", "webpack://grafana-lokiexplore-app/./Components/ServiceScene/JSONPanel/JSONFilterButtons.tsx", "webpack://grafana-lokiexplore-app/./Components/ServiceScene/JSONPanel/JSONLeafNodeLabelButtons.tsx", "webpack://grafana-lokiexplore-app/./Components/ServiceScene/JSONPanel/JSONMetadataButtons.tsx", "webpack://grafana-lokiexplore-app/./Components/ServiceScene/JSONPanel/JSONRootNodeNavigation.tsx", "webpack://grafana-lokiexplore-app/./services/JSONVizNodes.ts", "webpack://grafana-lokiexplore-app/./Components/ServiceScene/JSONPanel/JSONLeafLabel.tsx", "webpack://grafana-lokiexplore-app/./Components/ServiceScene/JSONPanel/ReRootJSONButton.tsx", "webpack://grafana-lokiexplore-app/./Components/ServiceScene/JSONPanel/JSONParentNodeFilterButtons.tsx", "webpack://grafana-lokiexplore-app/./Components/ServiceScene/JSONPanel/LabelRenderer.tsx", "webpack://grafana-lokiexplore-app/./Components/ServiceScene/JSONPanel/JSONLinkNodeButton.tsx", "webpack://grafana-lokiexplore-app/./services/logsSyntaxMatches.ts", "webpack://grafana-lokiexplore-app/./Components/ServiceScene/JSONPanel/ValueRenderer.tsx", "webpack://grafana-lokiexplore-app/./Components/ServiceScene/JSONPanel/JSONLineItemType.tsx", "webpack://grafana-lokiexplore-app/./Components/Buttons/CopyToClipboardButton.tsx", "webpack://grafana-lokiexplore-app/./Components/ServiceScene/JSONPanel/JSONLogLineActionButtons.tsx", "webpack://grafana-lokiexplore-app/./Components/ServiceScene/JSONPanel/ItemString.tsx", "webpack://grafana-lokiexplore-app/../node_modules/@gtk-grafana/react-json-tree/dist/main.js", "webpack://grafana-lokiexplore-app/./Components/ServiceScene/JSONPanel/LogsJSONComponent.tsx", "webpack://grafana-lokiexplore-app/./Components/UI/ImgButton.tsx"], "sourcesContent": ["import React from 'react';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { AdHocFilterWithLabels } from '@grafana/scenes';\n\nimport { LineFilterOp } from './filterTypes';\nimport { logger } from './logger';\n\nexport type TextWithHighlightedValue = Array<React.JSX.Element | string>;\n\nexport const getLineFilterRegExps = (filters: AdHocFilterWithLabels[]): Array<RegExp | undefined> => {\n  return filters\n    .filter(\n      (search) => (search.operator === LineFilterOp.match || search.operator === LineFilterOp.regex) && search.value\n    )\n    .map((search) => {\n      try {\n        return new RegExp(search.value, search.key === 'caseSensitive' ? 'g' : 'gi');\n      } catch (e) {\n        logger.info('Error executing match expression', { regex: search.value });\n        return undefined;\n      }\n    })\n    .filter((f) => f);\n};\n\nconst getWrappingElement = (className: string | undefined, jsxValues: string) => {\n  if (className) {\n    return <span className={className}>{jsxValues}</span>;\n  } else {\n    return <mark>{jsxValues}</mark>;\n  }\n};\n/**\n *\n * @param valueArray - array of chars to be wrapped\n * @param className - if defined, will wrap matches with span containing this classname instead of <mark> element\n */\nexport const mergeStringsAndElements = (valueArray: Array<{ value: string } | string>, className?: string) => {\n  let result: TextWithHighlightedValue = [];\n\n  let jsxValues = '';\n  let stringValues = '';\n  for (let i = 0; i < valueArray.length; i++) {\n    const char = valueArray[i];\n\n    // Merge contiguous jsx elements\n    if (typeof char === 'string') {\n      if (jsxValues) {\n        result.push(getWrappingElement(className, jsxValues));\n        jsxValues = '';\n      }\n      stringValues += char;\n    } else {\n      if (stringValues) {\n        result.push(stringValues);\n        stringValues = '';\n      }\n      jsxValues += char.value;\n    }\n  }\n\n  if (stringValues) {\n    result.push(stringValues);\n  }\n  if (jsxValues) {\n    result.push(getWrappingElement(className, jsxValues));\n  }\n  return result;\n};\nexport const highlightValueStringMatches = (\n  matchingIntervals: Array<[number, number]>,\n  value: string,\n  size: number,\n  className?: string\n) => {\n  let valueArray: Array<{ value: string } | string> = [];\n  let lineFilterMatchIndex = 0;\n  let matchInterval = matchingIntervals[lineFilterMatchIndex];\n\n  for (let valueIndex = 0; valueIndex < value.length; valueIndex++) {\n    // Size is 1 based length, lineFilterMatchIndex is 0 based index\n    while (valueIndex >= matchInterval[1] && lineFilterMatchIndex < size - 1) {\n      lineFilterMatchIndex++;\n      matchInterval = matchingIntervals[lineFilterMatchIndex];\n    }\n    if (valueIndex >= matchInterval[0] && valueIndex < matchInterval[1]) {\n      // this char is part of highlight, return an object in the array so we don't lose the original order, and we can differentiate between highlighted text in the subsequent merge\n      valueArray.push({ value: value[valueIndex] });\n    } else {\n      valueArray.push(value[valueIndex]);\n    }\n  }\n\n  return mergeStringsAndElements(valueArray, className);\n};\n\n// @todo cache results by regex/value?\nexport const getMatchingIntervals = (\n  matchExpressions: Array<RegExp | undefined>,\n  value: string\n): Array<[number, number]> => {\n  let results: Array<[number, number]> = [];\n  matchExpressions.forEach((regex) => {\n    let valueMatch: RegExpExecArray | null | undefined;\n    let valueMatches: RegExpExecArray[] = [];\n    do {\n      try {\n        valueMatch = regex?.exec(value);\n        // Did we match something?\n        if (valueMatch) {\n          // If we have a valid result\n          if (valueMatch[0]) {\n            valueMatches.push(valueMatch);\n          } else {\n            // Otherwise break the loop\n            valueMatch = null;\n          }\n        }\n      } catch (e) {\n        logger.info('Error executing match expression', { regex: regex?.source ?? '' });\n        valueMatch = null;\n      }\n    } while (valueMatch);\n    if (valueMatches.length) {\n      const fromToArray: Array<[number, number]> = valueMatches.map((vm) => [vm.index, vm.index + vm[0].length]);\n      results.push(...fromToArray);\n    }\n  });\n\n  return results;\n};\n\nfunction mergeOverlap(arr: number[][]) {\n  // Merge overlapping intervals in-place. We return\n  // modified size of the array arr.\n\n  // Sort intervals based on start values\n  arr.sort((a, b) => a[0] - b[0]);\n\n  // Index of the last merged\n  let resIdx = 0;\n\n  for (let i = 1; i < arr.length; i++) {\n    // If current interval overlaps with the\n    // last merged interval\n    if (arr[resIdx][1] >= arr[i][0]) {\n      arr[resIdx][1] = Math.max(arr[resIdx][1], arr[i][1]);\n    }\n    // Move to the next interval\n    else {\n      resIdx++;\n      arr[resIdx] = arr[i];\n    }\n  }\n\n  // Returns size of the merged intervals\n  return resIdx + 1;\n}\n\nexport const mergeOverlapping = (matchIndices: number[][]) => {\n  if (matchIndices.length) {\n    return mergeOverlap(matchIndices);\n  }\n  return 0;\n};\n\nexport const getLogsHighlightStyles = (theme: GrafanaTheme2) => {\n  // @todo find way to sync/pull from core?\n  const colors = {\n    critical: '#B877D9',\n    debug: '#6E9FFF',\n    error: theme.colors.error.text,\n    info: '#6CCF8E',\n    metadata: theme.colors.text.primary,\n    parsedField: theme.colors.text.primary,\n    trace: '#6ed0e0',\n    warning: theme.colors.warning.text,\n  };\n\n  return {\n    '.log-token-critical': {\n      color: colors.critical,\n    },\n    '.log-token-debug': {\n      color: colors.debug,\n    },\n    '.log-token-duration': {\n      color: theme.colors.success.text,\n    },\n    '.log-token-error': {\n      color: colors.error,\n    },\n    '.log-token-info': {\n      color: colors.info,\n    },\n    '.log-token-json-key': {\n      color: colors.parsedField,\n      fontWeight: theme.typography.fontWeightMedium,\n      opacity: 0.9,\n    },\n    '.log-token-key': {\n      color: colors.parsedField,\n      fontWeight: theme.typography.fontWeightMedium,\n      opacity: 0.9,\n    },\n    '.log-token-label': {\n      color: colors.metadata,\n      fontWeight: theme.typography.fontWeightBold,\n    },\n    '.log-token-method': {\n      color: theme.colors.info.shade,\n    },\n    '.log-token-size': {\n      color: theme.colors.success.text,\n    },\n    '.log-token-trace': {\n      color: colors.trace,\n    },\n    '.log-token-uuid': {\n      color: theme.colors.success.text,\n    },\n    '.log-token-warning': {\n      color: colors.warning,\n    },\n  };\n};\n", "import { AdHocFilterWithLabels } from '@grafana/scenes';\n\nimport {\n  getLineFilterRegExps,\n  getMatchingIntervals,\n  highlightValueStringMatches,\n  mergeOverlapping,\n  TextWithHighlightedValue,\n} from 'services/highlight';\n\nexport function JSONHighlightLineFilterMatches(\n  lineFilters: AdHocFilterWithLabels[],\n  value: string,\n  className?: string\n) {\n  const matchExpressions = getLineFilterRegExps(lineFilters);\n  const lineFilterMatches = getMatchingIntervals(matchExpressions, value);\n  const size = mergeOverlapping(lineFilterMatches);\n  let valueArray: TextWithHighlightedValue = [];\n\n  if (lineFilterMatches.length) {\n    valueArray = highlightValueStringMatches(lineFilterMatches, value, size, className);\n  }\n  return valueArray;\n}\n\nexport function JSONHighlightRegexMatches(regex: RegExp[], value: string, className: string) {\n  const lineFilterMatches = getMatchingIntervals(regex, value);\n  const size = mergeOverlapping(lineFilterMatches);\n  let valueArray: TextWithHighlightedValue = [];\n\n  if (lineFilterMatches.length) {\n    valueArray = highlightValueStringMatches(lineFilterMatches, value, size, className);\n  }\n  return valueArray;\n}\n", "import React from 'react';\n\nimport { useStyles2 } from '@grafana/ui';\n\nimport { getJSONLabelWrapStyles } from 'services/JSONViz';\n\ninterface Props {\n  keyPathString: string | number;\n  text: Array<string | React.JSX.Element>;\n}\n\nexport function JSONLabelText({ text, keyPathString }: Props) {\n  const styles = useStyles2(getJSONLabelWrapStyles);\n  return <strong className={styles.JSONLabelWrapStyles}>{text.length ? text : keyPathString}:</strong>;\n}\n", "import { sceneGraph, SceneObject } from '@grafana/scenes';\n\nimport {\n  addToFilters,\n  FilterType,\n  InterpolatedFilterType,\n} from '../Components/ServiceScene/Breakdowns/AddToFiltersButton';\nimport { JSONLogsScene } from '../Components/ServiceScene/JSONLogsScene';\nimport { LogsListScene } from '../Components/ServiceScene/LogsListScene';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from './analytics';\nimport { addJsonParserFieldValue } from './filters';\nimport { LABEL_NAME_INVALID_CHARS } from './labels';\nimport { addCurrentUrlToHistory } from './navigate';\nimport { KeyPath } from '@gtk-grafana/react-json-tree';\n\ninterface JsonFilterProps {\n  filterType: FilterType;\n  key: string;\n  keyPath: KeyPath;\n  logsJsonScene: JSONLogsScene;\n  value: string;\n  variableType: InterpolatedFilterType;\n}\n\nexport const addJSONFieldFilter = ({\n  key,\n  keyPath,\n  value,\n  filterType,\n  logsJsonScene,\n  variableType,\n}: JsonFilterProps) => {\n  addCurrentUrlToHistory();\n  // https://grafana.com/docs/loki/latest/get-started/labels/#label-format\n  key = key.replace(LABEL_NAME_INVALID_CHARS, '_');\n\n  addJsonParserFieldValue(logsJsonScene, keyPath);\n\n  const logsListScene = sceneGraph.getAncestor(logsJsonScene, LogsListScene);\n  addToFilters(key, value, filterType, logsListScene, variableType, false, true);\n\n  reportAppInteraction(\n    USER_EVENTS_PAGES.service_details,\n    USER_EVENTS_ACTIONS.service_details.add_to_filters_in_json_panel,\n    {\n      action: filterType,\n      filterType: 'json',\n      key,\n    }\n  );\n};\n\ninterface NestedNodeFilterProps {\n  filterType: FilterType;\n  label: string;\n  sceneRef: SceneObject;\n  value: string;\n  variableType: InterpolatedFilterType;\n}\n\nexport const addJSONMetadataFilter = ({ label, value, filterType, variableType, sceneRef }: NestedNodeFilterProps) => {\n  addCurrentUrlToHistory();\n  addToFilters(label, value, filterType, sceneRef, variableType, false);\n  reportAppInteraction(\n    USER_EVENTS_PAGES.service_details,\n    USER_EVENTS_ACTIONS.service_details.add_to_filters_in_json_panel,\n    {\n      action: filterType,\n      filterType,\n      label: label,\n    }\n  );\n};\n", "import React, { lazy, useMemo } from 'react';\n\nimport { css } from '@emotion/css';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { useStyles2 } from '@grafana/ui';\n\nimport { JSONLogsScene } from '../JSONLogsScene';\nimport { KeyPath } from '@gtk-grafana/react-json-tree';\nimport { addJSONFieldFilter } from 'services/JSONFilter';\nimport { EMPTY_VARIABLE_VALUE, VAR_FIELDS } from 'services/variables';\nconst ImgButton = lazy(() => import('../../UI/ImgButton'));\n\ninterface Props {\n  active: boolean;\n  fullKeyPath: string;\n  keyPath: KeyPath;\n  logsJsonScene: JSONLogsScene;\n  type: 'exclude' | 'include';\n}\n\nexport function JSONNestedNodeFilterButton({ active, fullKeyPath, keyPath, type, logsJsonScene }: Props) {\n  const styles = useStyles2(getJSONFilterButtonStyles, active);\n  return useMemo(\n    () => (\n      <ImgButton\n        className={styles.button}\n        tooltip={`${type === 'include' ? 'Include' : 'Exclude'} log lines that contain ${keyPath[0]}`}\n        onClick={(e) => {\n          e.stopPropagation();\n          addJSONFieldFilter({\n            value: EMPTY_VARIABLE_VALUE,\n            key: fullKeyPath,\n            variableType: VAR_FIELDS,\n            logsJsonScene,\n            keyPath,\n            filterType: active ? 'toggle' : type === 'include' ? 'exclude' : 'include',\n          });\n        }}\n        aria-selected={active}\n        variant={active ? 'primary' : 'secondary'}\n        name={type === 'include' ? 'search-plus' : 'search-minus'}\n        aria-label={`${type} filter`}\n      />\n    ),\n    [active, keyPath, fullKeyPath, logsJsonScene, styles.button, type]\n  );\n}\n\nexport const getJSONFilterButtonStyles = (theme: GrafanaTheme2, isActive: boolean) => {\n  return {\n    button: css({\n      color: isActive ? undefined : theme.colors.text.secondary,\n      '&:hover': {\n        color: theme.colors.text.maxContrast,\n      },\n    }),\n  };\n};\n", "import React, { lazy, memo, useMemo } from 'react';\n\nimport { AdHocFilterWithLabels, SceneObject } from '@grafana/scenes';\nimport { useStyles2 } from '@grafana/ui';\n\nimport { InterpolatedFilterType } from '../Breakdowns/AddToFiltersButton';\nimport { JSONLogsScene } from '../JSONLogsScene';\nimport { getJSONFilterButtonStyles } from './JSONNestedNodeFilterButton';\nimport { KeyPath } from '@gtk-grafana/react-json-tree';\nimport { FilterOp } from 'services/filterTypes';\nimport { addJSONFieldFilter, addJSONMetadataFilter } from 'services/JSONFilter';\nimport { VAR_FIELDS } from 'services/variables';\n\nconst ImgButton = lazy(() => import('../../UI/ImgButton'));\n\ninterface JsonFilterProps {\n  existingFilter?: AdHocFilterWithLabels;\n  fullKey: string;\n  keyPath: KeyPath;\n  label: string | number;\n  model: JSONLogsScene;\n  type: 'exclude' | 'include';\n  value: string;\n}\n\nexport const JSONFieldValueButton = memo(\n  ({ existingFilter, fullKey, keyPath, label, type, value, model }: JsonFilterProps) => {\n    const operator = type === 'include' ? FilterOp.Equal : FilterOp.NotEqual;\n    const isActive = existingFilter?.operator === operator;\n    const styles = useStyles2(getJSONFilterButtonStyles, isActive);\n    const selected = existingFilter?.operator === operator;\n\n    return useMemo(\n      () => (\n        <ImgButton\n          className={styles.button}\n          tooltip={`${type === 'include' ? 'Include' : 'Exclude'} log lines containing ${label}=\"${value}\"`}\n          onClick={(e) => {\n            e.stopPropagation();\n            addJSONFieldFilter({\n              keyPath: keyPath,\n              key: fullKey,\n              value,\n              filterType: selected ? 'toggle' : type,\n              logsJsonScene: model,\n              variableType: VAR_FIELDS,\n            });\n          }}\n          aria-selected={isActive}\n          variant={isActive ? 'primary' : 'secondary'}\n          name={type === 'include' ? 'search-plus' : 'search-minus'}\n          aria-label={`${type} filter`}\n        />\n      ),\n      [isActive, selected, type, styles.button, keyPath, fullKey, value, label, model]\n    );\n  }\n);\nJSONFieldValueButton.displayName = 'JSONFilterValueButton';\n\ninterface MetadataFilterProps {\n  existingFilter?: AdHocFilterWithLabels;\n  label: string;\n  sceneRef: SceneObject;\n  type: 'exclude' | 'include';\n  value: string;\n  variableType: InterpolatedFilterType;\n}\n\nexport const JSONMetadataButton = memo(\n  ({ existingFilter, label, type, value, variableType, sceneRef }: MetadataFilterProps) => {\n    const operator = type === 'include' ? FilterOp.Equal : FilterOp.NotEqual;\n    const isActive = existingFilter?.operator === operator;\n    const styles = useStyles2(getJSONFilterButtonStyles, isActive);\n    const selected = existingFilter?.operator === operator;\n\n    return useMemo(\n      () => (\n        <ImgButton\n          className={styles.button}\n          tooltip={`${type === 'include' ? 'Include' : 'Exclude'} log lines containing ${label}=\"${value}\"`}\n          onClick={(e) => {\n            e.stopPropagation();\n\n            addJSONMetadataFilter({\n              label,\n              value,\n              filterType: selected ? 'toggle' : type,\n              variableType,\n              sceneRef,\n            });\n          }}\n          aria-selected={selected}\n          variant={selected ? 'primary' : 'secondary'}\n          name={type === 'include' ? 'search-plus' : 'search-minus'}\n          aria-label={`${type} filter`}\n        />\n      ),\n      [selected, label, sceneRef, styles.button, type, value, variableType]\n    );\n  }\n);\nJSONMetadataButton.displayName = 'JSONMetadataButton';\n", "import React, { ReactNode } from 'react';\n\nimport { AdHocFilterWithLabels } from '@grafana/scenes';\n\nimport { JSONLogsScene } from '../JSONLogsScene';\nimport { JSONFieldValueButton } from './JSONFilterButtons';\nimport { KeyPath } from '@gtk-grafana/react-json-tree';\n\ninterface Props {\n  elements: ReactNode[];\n  existingFilter?: AdHocFilterWithLabels;\n  fullKey: string;\n  fullKeyPath: KeyPath;\n  JSONFiltersSupported: boolean | undefined;\n  keyPathString: string | number;\n  label: string | number;\n  model: JSONLogsScene;\n  value: string;\n}\n\nexport function JSONLeafNodeLabelButtons({ label, value, fullKeyPath, fullKey, existingFilter, model }: Props) {\n  return (\n    <>\n      <JSONFieldValueButton\n        label={label}\n        value={value}\n        keyPath={fullKeyPath}\n        fullKey={fullKey}\n        existingFilter={existingFilter}\n        type={'include'}\n        model={model}\n      />\n      <JSONFieldValueButton\n        label={label}\n        value={value}\n        keyPath={fullKeyPath}\n        fullKey={fullKey}\n        existingFilter={existingFilter}\n        type={'exclude'}\n        model={model}\n      />\n    </>\n  );\n}\n", "import React from 'react';\n\nimport { AdHocFilterWithLabels, SceneObject } from '@grafana/scenes';\n\nimport { InterpolatedFilterType } from '../Breakdowns/AddToFiltersButton';\nimport { JSONMetadataButton } from './JSONFilterButtons';\nimport { isOperatorExclusive, isOperatorInclusive } from 'services/operatorHelpers';\n\ninterface Props {\n  existingFilter: AdHocFilterWithLabels[];\n  label: string | number;\n  sceneRef: SceneObject;\n  value: string;\n  variableType: InterpolatedFilterType;\n}\n\n/**\n * Labels and metadata nodes\n */\nexport function JSONMetadataButtons({ existingFilter, label, sceneRef, value, variableType }: Props) {\n  const isFilterInclusive = (filter: AdHocFilterWithLabels) => isOperatorInclusive(filter.operator);\n  const isFilterExclusive = (filter: AdHocFilterWithLabels) => isOperatorExclusive(filter.operator);\n  return (\n    <>\n      {/* Include */}\n      <JSONMetadataButton\n        type={'include'}\n        label={label.toString()}\n        value={value}\n        variableType={variableType}\n        existingFilter={existingFilter.find(isFilterInclusive)}\n        sceneRef={sceneRef}\n      />\n      {/* Exclude */}\n      <JSONMetadataButton\n        type={'exclude'}\n        label={label.toString()}\n        value={value}\n        variableType={variableType}\n        existingFilter={existingFilter.find(isFilterExclusive)}\n        sceneRef={sceneRef}\n      />\n    </>\n  );\n}\n", "import React from 'react';\n\nimport { AdHocFilterWith<PERSON>abels, SceneObject } from '@grafana/scenes';\nimport { Button, Icon } from '@grafana/ui';\n\nimport { JSONDataFrameLineName, JSONVizRootName } from '../JSONLogsScene';\nimport { KeyPath } from '@gtk-grafana/react-json-tree';\nimport { reportAppInteraction, USER_EVENTS_ACTIONS, USER_EVENTS_PAGES } from 'services/analytics';\nimport { clearJSONParserFields, isLogLineField } from 'services/fields';\nimport { addJsonParserFieldValue, EMPTY_AD_HOC_FILTER_VALUE, removeLineFormatFilters } from 'services/filters';\nimport { LineFormatFilterOp } from 'services/filterTypes';\nimport { breadCrumbDelimiter, drillUpWrapperStyle, itemStringDelimiter } from 'services/JSONViz';\nimport { LABEL_NAME_INVALID_CHARS } from 'services/labels';\nimport { addCurrentUrlToHistory } from 'services/navigate';\nimport { getFieldsVariable, getJSONFieldsVariable, getLineFormatVariable } from 'services/variableGetters';\n\ninterface Props {\n  sceneRef: SceneObject;\n}\n\n/**\n * Gets re-root button and key label for root node when line format filter is active.\n * aka breadcrumbs\n */\nexport default function JSONRootNodeNavigation({ sceneRef }: Props) {\n  const lineFormatVar = getLineFormatVariable(sceneRef);\n  const filters = lineFormatVar.state.filters;\n  const rootKeyPath = [JSONDataFrameLineName, 0, JSONVizRootName];\n\n  return (\n    <>\n      <span className={drillUpWrapperStyle} key={JSONVizRootName}>\n        <Button\n          size={'sm'}\n          onClick={() => setNewRootNode(rootKeyPath, sceneRef)}\n          variant={'secondary'}\n          fill={'outline'}\n          disabled={!filters.length}\n          name={JSONVizRootName}\n        >\n          {JSONVizRootName}\n        </Button>\n        {filters.length > 0 && <Icon className={breadCrumbDelimiter} name={'angle-right'} />}\n      </span>\n\n      {filters.map((filter, i) => {\n        const selected = filter.key === filters[filters.length - 1].key;\n        return (\n          <span className={drillUpWrapperStyle} key={filter.key}>\n            {\n              <Button\n                size={'sm'}\n                disabled={selected}\n                onClick={() => addDrillUp(filter.key, sceneRef)}\n                variant={'secondary'}\n                fill={'outline'}\n              >\n                {filter.key}\n              </Button>\n            }\n            {i < filters.length - 1 && <Icon className={breadCrumbDelimiter} name={'angle-right'} />}\n            {i === filters.length - 1 && <Icon className={itemStringDelimiter} name={'angle-right'} />}\n          </span>\n        );\n      })}\n    </>\n  );\n}\n\nexport function getFullKeyPath(\n  keyPath: ReadonlyArray<string | number>,\n  sceneObject: SceneObject\n): { fullKeyPath: KeyPath; fullPathFilters: AdHocFilterWithLabels[] } {\n  const lineFormatVar = getLineFormatVariable(sceneObject);\n\n  const fullPathFilters: AdHocFilterWithLabels[] = [\n    ...lineFormatVar.state.filters,\n    ...keyPath\n      // line format filters only store the parent node field names\n      .filter((key) => typeof key === 'string' && !isLogLineField(key) && key !== JSONVizRootName)\n      // keyPath order is from child to root, we want to order from root to child\n      .reverse()\n      // convert to ad-hoc filter\n      .map((nodeKey) => ({\n        key: nodeKey.toString(),\n        // The operator and value are not used when interpolating the variable, but empty values will cause the ad-hoc filter to get removed from the URL state, we work around this by adding an empty space for the value and operator\n        // we could store the depth of the node as a value, right now we assume that these filters always include every parent node of the current node, ordered by node depth ASC (root node first)\n        operator: LineFormatFilterOp.Empty,\n        value: EMPTY_AD_HOC_FILTER_VALUE,\n      })),\n  ];\n  // the last 3 in the key path are always array\n  const fullKeyPath = [...fullPathFilters.map((filter) => filter.key).reverse(), ...keyPath.slice(-3)];\n  return { fullKeyPath, fullPathFilters };\n}\n\nexport const setNewRootNode = (keyPath: KeyPath, sceneRef: SceneObject) => {\n  addCurrentUrlToHistory();\n  const { fullKeyPath, fullPathFilters } = getFullKeyPath(keyPath, sceneRef);\n  // If keyPath length is greater than 3 we're drilling down (root, line index, line)\n  if (keyPath.length > 3) {\n    addJsonParserFieldValue(sceneRef, fullKeyPath);\n\n    const lineFormatVar = getLineFormatVariable(sceneRef);\n\n    lineFormatVar.setState({\n      // Need to strip out any unsupported chars to match the field name we're creating in the json parser args\n      filters: fullPathFilters.map((filter) => ({\n        ...filter,\n        key: filter.key.replace(LABEL_NAME_INVALID_CHARS, '_'),\n      })),\n    });\n    lineFormatEvent('add', keyPath[0].toString());\n  } else {\n    // Otherwise we're drilling back up to the root\n    removeLineFormatFilters(sceneRef);\n    clearJSONParserFields(sceneRef);\n    lineFormatEvent('remove', JSONVizRootName);\n  }\n};\n\n/**\n * Fires rudderstack event when the viz adds/removes a new root (line format)\n */\nexport const lineFormatEvent = (type: 'add' | 'remove', key: string) => {\n  reportAppInteraction(\n    USER_EVENTS_PAGES.service_details,\n    USER_EVENTS_ACTIONS.service_details.change_line_format_in_json_panel,\n    {\n      key,\n      type: type,\n    }\n  );\n};\n\n/**\n * Drill back up to a parent node via the sticky \"breadcrumbs\"\n * @param key\n * @param sceneRef\n */\nexport const addDrillUp = (key: string, sceneRef: SceneObject) => {\n  addCurrentUrlToHistory();\n\n  const lineFormatVariable = getLineFormatVariable(sceneRef);\n  const JSONVar = getJSONFieldsVariable(sceneRef);\n  const fieldsVar = getFieldsVariable(sceneRef);\n\n  const lineFormatFilters = lineFormatVariable.state.filters;\n  const keyIndex = lineFormatFilters.findIndex((filter) => filter.key === key);\n  const lineFormatFiltersToKeep = lineFormatFilters.filter((_, index) => index <= keyIndex);\n  const JSONParserKeys: string[] = [];\n\n  for (let i = 0; i < lineFormatFilters.length; i++) {\n    JSONParserKeys.push(\n      `${\n        JSONParserKeys.length\n          ? `${lineFormatFilters\n              .map((filter) => filter.key)\n              .slice(0, i)\n              .join('_')}_`\n          : ''\n      }${lineFormatFilters[i].key}`\n    );\n  }\n\n  const JSONParserKeysToRemove = JSONParserKeys.slice(keyIndex + 1);\n  const fieldsFilterSet = new Set();\n  fieldsVar.state.filters.forEach((fieldFilter) => fieldsFilterSet.add(fieldFilter.key));\n\n  const JSONParserFilters = JSONVar.state.filters.filter(\n    (filter) => !JSONParserKeysToRemove.includes(filter.key) || fieldsFilterSet.has(filter.key)\n  );\n\n  JSONVar.setState({\n    filters: JSONParserFilters,\n  });\n  lineFormatVariable.setState({\n    filters: lineFormatFiltersToKeep,\n  });\n\n  lineFormatEvent('remove', key);\n};\n", "import {\n  JSONDataFrameLabelsName,\n  JSONDataFrameStructuredMetadataName,\n  JSONDataFrameTimeName,\n} from '../Components/ServiceScene/JSONLogsScene';\nimport { KeyPath } from '@gtk-grafana/react-json-tree';\n\n/**\n * Determines if the current node is the timestamp label\n * @param keyPath\n */\nexport const isTimeLabelNode = (keyPath: KeyPath) => {\n  return keyPath[0] === JSONDataFrameTimeName;\n};\n\n/**\n * Does the node at keyPath have a metadata or labels parent node?\n * @param keyPath\n */\nexport const hasFieldParentNode = (keyPath: KeyPath) => {\n  return keyPath[1] === JSONDataFrameStructuredMetadataName || keyPath[1] === JSONDataFrameLabelsName;\n};\n", "import React from 'react';\n\nimport { Field } from '@grafana/data';\nimport { AdHocFiltersVariable, AdHocFilterWithLabels } from '@grafana/scenes';\nimport { useStyles2 } from '@grafana/ui';\n\nimport { JSONHighlightLineFilterMatches } from '../../../services/JSONHighlightLineFilterMatches';\nimport { InterpolatedFilterType } from '../Breakdowns/AddToFiltersButton';\nimport {\n  getKeyPathString,\n  JSONDataFrameLabelsName,\n  JSONDataFrameStructuredMetadataName,\n  JSONLogsScene,\n  JSONVizRootName,\n} from '../JSONLogsScene';\nimport { JSONLabelText } from './JSONLabelText';\nimport { JSONLeafNodeLabelButtons } from './JSONLeafNodeLabelButtons';\nimport { JSONMetadataButtons } from './JSONMetadataButtons';\nimport { getFullKeyPath } from './JSONRootNodeNavigation';\nimport { KeyPath } from '@gtk-grafana/react-json-tree';\nimport { getJSONKey } from 'services/filters';\nimport { getJSONLabelWrapStyles, getJSONVizNestedProperty } from 'services/JSONViz';\nimport { hasFieldParentNode } from 'services/JSONVizNodes';\nimport { getAdHocFiltersVariable, getValueFromFieldsFilter } from 'services/variableGetters';\nimport { LEVEL_VARIABLE_VALUE, VAR_FIELDS, VAR_LABELS, VAR_LEVELS, VAR_METADATA } from 'services/variables';\n\ninterface Props {\n  fieldsVar: AdHocFiltersVariable;\n  JSONFiltersSupported: boolean | null;\n  JSONLogsScene: JSONLogsScene;\n  JSONParserPropsMap: Map<string, AdHocFilterWithLabels>;\n  keyPath: KeyPath;\n  lineField: Field<string | number>;\n  lineFilters: AdHocFilterWithLabels[];\n}\n\nexport function JSONLeafLabel({\n  keyPath,\n  lineField,\n  fieldsVar,\n  JSONParserPropsMap,\n  lineFilters,\n  JSONFiltersSupported,\n  JSONLogsScene,\n}: Props) {\n  const value = getValue(keyPath, lineField.values)?.toString();\n  const label = keyPath[0];\n  const existingVariableType = getFilterVariableTypeFromPath(keyPath);\n  const styles = useStyles2(getJSONLabelWrapStyles);\n\n  let highlightedValue: string | Array<string | React.JSX.Element> = [];\n  if (JSONLogsScene.state.hasHighlight && !hasFieldParentNode(keyPath)) {\n    highlightedValue = JSONHighlightLineFilterMatches(lineFilters, keyPath[0].toString());\n  }\n\n  // Field (labels, metadata) nodes\n  if (hasFieldParentNode(keyPath)) {\n    const existingVariable = getAdHocFiltersVariable(existingVariableType, JSONLogsScene);\n    const existingFilter = existingVariable.state.filters.filter(\n      (filter) => filter.key === label.toString() && filter.value === value\n    );\n\n    return (\n      <span className={styles.labelButtonsWrap}>\n        <JSONMetadataButtons\n          sceneRef={JSONLogsScene}\n          label={label}\n          value={value}\n          variableType={existingVariableType}\n          existingFilter={existingFilter}\n        />\n        <JSONLabelText text={highlightedValue} keyPathString={getKeyPathString(keyPath, '')} />\n      </span>\n    );\n  }\n\n  const { fullKeyPath } = getFullKeyPath(keyPath, JSONLogsScene);\n  const fullKey = getJSONKey(fullKeyPath);\n  const JSONParserProp = JSONParserPropsMap.get(fullKey);\n  const existingJSONFilter =\n    JSONParserProp &&\n    fieldsVar.state.filters.find((f) => f.key === JSONParserProp?.key && getValueFromFieldsFilter(f).value === value);\n\n  // Value nodes\n  return (\n    <span className={styles.labelButtonsWrap}>\n      {JSONFiltersSupported && (\n        <JSONLeafNodeLabelButtons\n          JSONFiltersSupported={JSONFiltersSupported}\n          label={label}\n          value={value}\n          fullKeyPath={fullKeyPath}\n          fullKey={fullKey}\n          existingFilter={existingJSONFilter}\n          elements={highlightedValue}\n          keyPathString={getKeyPathString(keyPath, '')}\n          model={JSONLogsScene}\n        />\n      )}\n      <JSONLabelText text={highlightedValue} keyPathString={getKeyPathString(keyPath, '')} />\n    </span>\n  );\n}\n\n/**\n * Gets value from log Field at keyPath\n */\nfunction getValue(keyPath: KeyPath, lineField: Array<string | number>): string | number {\n  const keys = [...keyPath];\n  const accessors = [];\n\n  while (keys.length) {\n    const key = keys.pop();\n\n    if (key !== JSONVizRootName && key !== undefined) {\n      accessors.push(key);\n    }\n  }\n\n  return getJSONVizNestedProperty(lineField, accessors);\n}\n\nfunction getFilterVariableTypeFromPath(keyPath: ReadonlyArray<string | number>): InterpolatedFilterType {\n  if (keyPath[1] === JSONDataFrameStructuredMetadataName) {\n    if (keyPath[0] === LEVEL_VARIABLE_VALUE) {\n      return VAR_LEVELS;\n    }\n    return VAR_METADATA;\n  } else if (keyPath[1] === JSONDataFrameLabelsName) {\n    return VAR_LABELS;\n  } else {\n    return VAR_FIELDS;\n  }\n}\n", "import React, { lazy, memo, useMemo } from 'react';\n\nimport { SceneObject } from '@grafana/scenes';\nimport { useStyles2 } from '@grafana/ui';\n\nimport { getJSONFilterButtonStyles } from './JSONNestedNodeFilterButton';\nimport { setNewRootNode } from './JSONRootNodeNavigation';\nimport { KeyPath } from '@gtk-grafana/react-json-tree';\n\nconst ImgButton = lazy(() => import('../../UI/ImgButton'));\n\nconst ReRootJSONButton = memo(({ keyPath, sceneRef }: { keyPath: KeyPath; sceneRef: SceneObject }) => {\n  const styles = useStyles2(getJSONFilterButtonStyles, false);\n  return useMemo(\n    () => (\n      <ImgButton\n        className={styles.button}\n        tooltip={`Set ${keyPath[0]} as root node`}\n        onClick={(e) => {\n          e.stopPropagation();\n          setNewRootNode(keyPath, sceneRef);\n        }}\n        name={'eye'}\n        aria-label={`drilldown into ${keyPath[0]}`}\n      />\n    ),\n    [keyPath, sceneRef, styles.button]\n  );\n});\n\nReRootJSONButton.displayName = 'DrilldownButton';\nexport default ReRootJSONButton;\n", "import React, { memo } from 'react';\n\nimport { AdHocFilterWithLabels } from '@grafana/scenes';\nimport { useStyles2 } from '@grafana/ui';\n\nimport { JSONHighlightLineFilterMatches } from '../../../services/JSONHighlightLineFilterMatches';\nimport { getJSONLabelWrapStyles } from '../../../services/JSONViz';\nimport { getKeyPathString, JSONLogsScene } from '../JSONLogsScene';\nimport { JSONNestedNodeFilterButton } from './JSONNestedNodeFilterButton';\nimport { getFullKeyPath } from './JSONRootNodeNavigation';\nimport ReRootJSONButton from './ReRootJSONButton';\nimport { KeyPath } from '@gtk-grafana/react-json-tree';\nimport { getJSONKey } from 'services/filters';\nimport { isOperatorExclusive, isOperatorInclusive } from 'services/operatorHelpers';\nimport { getValueFromFieldsFilter } from 'services/variableGetters';\nimport { EMPTY_VARIABLE_VALUE } from 'services/variables';\n\ninterface Props {\n  fieldsFilters: AdHocFilterWithLabels[];\n  JSONFiltersSupported: boolean | null;\n  JSONLogsScene: JSONLogsScene;\n  JSONParserPropsMap: Map<string, AdHocFilterWithLabels>;\n  keyPath: KeyPath;\n  lineFilters: AdHocFilterWithLabels[];\n}\n\nfunction NestedNodeFilterButtonsComponent({\n  keyPath,\n  fieldsFilters,\n  JSONParserPropsMap,\n  lineFilters,\n  JSONFiltersSupported,\n  JSONLogsScene,\n}: Props) {\n  const { fullKeyPath } = getFullKeyPath(keyPath, JSONLogsScene);\n  const fullKey = getJSONKey(fullKeyPath);\n  const styles = useStyles2(getJSONLabelWrapStyles);\n\n  const JSONParserProp = JSONParserPropsMap.get(fullKey);\n  const existingFilter =\n    JSONParserProp &&\n    fieldsFilters.find(\n      (f) => f.key === JSONParserProp?.key && getValueFromFieldsFilter(f).value === EMPTY_VARIABLE_VALUE\n    );\n\n  let highlightedValue: string | Array<string | React.JSX.Element> = [];\n  if (JSONLogsScene.state.hasHighlight) {\n    highlightedValue = JSONHighlightLineFilterMatches(lineFilters, keyPath[0].toString());\n  }\n\n  return (\n    <span className={styles.JSONNestedLabelWrapStyles}>\n      {JSONFiltersSupported && (\n        <>\n          <ReRootJSONButton keyPath={keyPath} sceneRef={JSONLogsScene} />\n          <JSONNestedNodeFilterButton\n            type={'include'}\n            fullKeyPath={fullKey}\n            keyPath={fullKeyPath}\n            active={existingFilter ? isOperatorExclusive(existingFilter.operator) : false}\n            logsJsonScene={JSONLogsScene}\n          />\n          <JSONNestedNodeFilterButton\n            type={'exclude'}\n            fullKeyPath={fullKey}\n            keyPath={fullKeyPath}\n            active={existingFilter ? isOperatorInclusive(existingFilter.operator) : false}\n            logsJsonScene={JSONLogsScene}\n          />\n        </>\n      )}\n      <strong className={styles.JSONLabelWrapStyles}>\n        {highlightedValue.length ? highlightedValue : getKeyPathString(keyPath, '')}:\n      </strong>\n    </span>\n  );\n}\n\nexport const JSONParentNodeFilterButtons = memo(NestedNodeFilterButtonsComponent);\n", "import React from 'react';\n\nimport { isNumber } from 'lodash';\n\nimport { Field } from '@grafana/data';\nimport { AdHocFiltersVariable, AdHocFilterWithLabels } from '@grafana/scenes';\nimport { useStyles2 } from '@grafana/ui';\n\nimport {\n  JSONDataFrameLabelsName,\n  JSONDataFrameLinksName,\n  JSONDataFrameStructuredMetadataName,\n  JSONDataFrameTimeName,\n  JSONLinksDisplayName,\n  JSONVizRootName,\n  JSONLabelsDisplayName,\n  JSONLogsScene,\n  NodeType,\n  JSONStructuredMetadataDisplayName,\n} from '../JSONLogsScene';\nimport { JSONLeafLabel } from './JSONLeafLabel';\nimport { JSONParentNodeFilterButtons } from './JSONParentNodeFilterButtons';\nimport JSONRootNodeNavigation from './JSONRootNodeNavigation';\nimport { KeyPath } from '@gtk-grafana/react-json-tree/dist/types';\nimport { isLogLineField } from 'services/fields';\nimport { getJSONLabelWrapStyles, JSONLabelWrapStylesPrimary } from 'services/JSONViz';\nimport { isTimeLabelNode } from 'services/JSONVizNodes';\n\ninterface LabelRendererProps {\n  fieldsVar: AdHocFiltersVariable;\n  JSONFiltersSupported: boolean | null;\n  JSONParserPropsMap: Map<string, AdHocFilterWithLabels>;\n  keyPath: KeyPath;\n  lineField: Field;\n  lineFilters: AdHocFilterWithLabels[];\n  model: JSONLogsScene;\n  nodeType: string;\n}\n\nexport default function LabelRenderer({\n  fieldsVar,\n  JSONFiltersSupported,\n  JSONParserPropsMap,\n  keyPath,\n  lineField,\n  lineFilters,\n  model,\n  nodeType,\n}: LabelRendererProps) {\n  const style = useStyles2(getJSONLabelWrapStyles);\n  const value: string | Array<string | React.JSX.Element> = keyPath[0].toString();\n  const nodeTypeLoc = nodeType as NodeType;\n\n  // Specific implementations for leaf nodes\n  // Metadata node\n  if (keyPath[0] === JSONDataFrameStructuredMetadataName) {\n    return <strong className={style.JSONLabelWrapStyles}>{JSONStructuredMetadataDisplayName}</strong>;\n  }\n  // Labels node\n  if (keyPath[0] === JSONDataFrameLabelsName) {\n    return <strong className={style.JSONLabelWrapStyles}>{JSONLabelsDisplayName}</strong>;\n  }\n  // Links parent\n  if (keyPath[0] === JSONDataFrameLinksName) {\n    return <strong className={style.JSONLabelWrapStyles}>{JSONLinksDisplayName}</strong>;\n  }\n  // Links node\n  if (keyPath[1] === JSONDataFrameLinksName) {\n    return <strong className={style.JSONLabelWrapStyles}>{value}:</strong>;\n  }\n  // Root node\n  if (keyPath[0] === JSONVizRootName) {\n    return <JSONRootNodeNavigation sceneRef={model} />;\n  }\n\n  // Value nodes\n  if (isJSONLeafNode(nodeTypeLoc, keyPath)) {\n    return (\n      <JSONLeafLabel\n        JSONLogsScene={model}\n        keyPath={keyPath}\n        lineField={lineField}\n        fieldsVar={fieldsVar}\n        JSONParserPropsMap={JSONParserPropsMap}\n        lineFilters={lineFilters}\n        JSONFiltersSupported={JSONFiltersSupported}\n      />\n    );\n  }\n\n  // Parent nodes\n  if (isJSONParentNode(nodeTypeLoc, keyPath)) {\n    return (\n      <JSONParentNodeFilterButtons\n        keyPath={keyPath}\n        lineFilters={lineFilters}\n        JSONLogsScene={model}\n        fieldsFilters={fieldsVar.state.filters}\n        JSONParserPropsMap={JSONParserPropsMap}\n        JSONFiltersSupported={JSONFiltersSupported}\n      />\n    );\n  }\n\n  // Show the timestamp as the label of the log line\n  if (isTimestampNode(keyPath) && isNumber(keyPath[0])) {\n    const time: string = lineField.values[keyPath[0]]?.[JSONDataFrameTimeName];\n    return <strong className={JSONLabelWrapStylesPrimary}>{time}</strong>;\n  }\n\n  // Don't render time node\n  if (isTimeLabelNode(keyPath)) {\n    return null;\n  }\n\n  return <strong className={style.JSONLabelWrapStyles}>{value}:</strong>;\n}\n\n/**\n * Is JSON node a leaf node\n * @param nodeTypeLoc\n * @param keyPath\n */\nconst isJSONLeafNode = (nodeTypeLoc: NodeType, keyPath: KeyPath) => {\n  return (\n    nodeTypeLoc !== 'Object' &&\n    nodeTypeLoc !== 'Array' &&\n    keyPath[0] !== JSONDataFrameTimeName &&\n    !isLogLineField(keyPath[0].toString()) &&\n    keyPath[0] !== JSONVizRootName &&\n    !isNumber(keyPath[0])\n  );\n};\n\n/**\n * Is JSON node a parent node\n * @param nodeTypeLoc\n * @param keyPath\n */\nconst isJSONParentNode = (nodeTypeLoc: NodeType, keyPath: KeyPath) => {\n  return (\n    (nodeTypeLoc === 'Object' || nodeTypeLoc === 'Array') &&\n    !isLogLineField(keyPath[0].toString()) &&\n    keyPath[0] !== JSONVizRootName &&\n    !isNumber(keyPath[0])\n  );\n};\n\nconst isTimestampNode = (keyPath: KeyPath) => {\n  return keyPath[1] === JSONVizRootName;\n};\n", "import React from 'react';\n\nimport { css } from '@emotion/css';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { LinkButton, useStyles2 } from '@grafana/ui';\n\nimport { logger } from 'services/logger';\nimport { narrowJsonDerivedFieldLinkPayload } from 'services/narrowing';\n\nfunction JSONLinkNodeButton({ payload }: { payload: string }) {\n  const styles = useStyles2(getStyles);\n  let decodedPayload;\n  try {\n    decodedPayload = JSON.parse(payload);\n  } catch (e) {\n    logger.error(e, { msg: 'Unable to parse JsonLinkButton payload!' });\n  }\n\n  const decodedPayloadNarrowed = narrowJsonDerivedFieldLinkPayload(decodedPayload);\n  if (decodedPayloadNarrowed) {\n    return (\n      <LinkButton\n        className={styles.button}\n        icon={'external-link-alt'}\n        variant={'secondary'}\n        size={'sm'}\n        fill={'text'}\n        href={decodedPayloadNarrowed.href}\n        target={'_blank'}\n      >\n        {decodedPayloadNarrowed.name}\n      </LinkButton>\n    );\n  }\n\n  return null;\n}\nconst getStyles = (theme: GrafanaTheme2) => {\n  return {\n    button: css({\n      '&:hover': {\n        color: theme.colors.primary.text,\n      },\n    }),\n  };\n};\n\nexport default JSONLinkNodeButton;\n", "// Synced with https://github.com/grafana/grafana/blob/ca730935733d86339177ed5d014b9343831df98b/public/app/features/logs/components/panel/grammar.ts\n/* eslint-disable sort/object-properties */\nexport const logsSyntaxMatches: Record<string, RegExp> = {\n  // Levels regex\n  'log-token-critical': /(\\b)(CRITICAL|CRIT)($|\\s)/gi,\n  'log-token-error': /(\\b)(ERROR|ERR)($|\\s)/gi,\n  'log-token-warning': /(\\b|\\B)(WARNING|WARN)($|\\s)/gi,\n  'log-token-debug': /(\\b)(DEBUG)($|\\s)/gi,\n  'log-token-info': /(\\b|\\B)(INFO)($|\\s)/gi,\n  'log-token-trace': /(\\b)(TRACE)($|\\s)/gi,\n\n  // Misc log markup regex\n  'log-token-uuid': /(\\b|\\B)[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}/g,\n  'log-token-method': /\\b(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS|TRACE|CONNECT)\\b/gi,\n  'log-token-key': /(\\b|\\B)[\\w_]+(?=\\s*=)/gi,\n  'log-token-size': /(?:\\b|\")\\d+\\.{0,1}\\d*\\s*[kKmMGgtTPp]*[bB]{1}(?:\"|\\b)/g,\n  'log-token-duration': /\\b\\d+(\\.\\d+)?(ns|µs|ms|s|m|h|d)\\b/g,\n};\n", "import React from 'react';\n\nimport { AdHocFilterWithLabels } from '@grafana/scenes';\n\nimport {\n  JSONHighlightLineFilterMatches,\n  JSONHighlightRegexMatches,\n} from '../../../services/JSONHighlightLineFilterMatches';\nimport { JSONDataFrameLinksName, JSONLogsScene } from '../JSONLogsScene';\nimport JSONLinkNodeButton from './JSONLinkNodeButton';\nimport { KeyPath } from '@gtk-grafana/react-json-tree/dist/types';\nimport { hasFieldParentNode, isTimeLabelNode } from 'services/JSONVizNodes';\nimport { logsSyntaxMatches } from 'services/logsSyntaxMatches';\n\ninterface ValueRendererProps {\n  keyPath: KeyPath;\n  lineFilters: AdHocFilterWithLabels[];\n  model: JSONLogsScene;\n  // @todo react-json-tree should probably return this type as string?\n  valueAsString: unknown;\n}\n\nexport default function ValueRenderer({ keyPath, lineFilters, valueAsString, model }: ValueRendererProps) {\n  if (isTimeLabelNode(keyPath)) {\n    return null;\n  }\n  const value = valueAsString?.toString();\n\n  // Don't bother rendering empty values\n  if (!value) {\n    return null;\n  }\n\n  // Link nodes\n  if (keyPath[1] === JSONDataFrameLinksName) {\n    return <JSONLinkNodeButton payload={value} />;\n  }\n\n  // If highlighting is enabled, split up the value string into an array of React objects wrapping text that matches syntax regex or matches line filter regex\n  if (model.state.hasHighlight) {\n    // Don't show line filter matches on field nodes\n    if (!hasFieldParentNode(keyPath)) {\n      let valueArray = JSONHighlightLineFilterMatches(lineFilters, value);\n\n      // If we have highlight matches we won't show syntax highlighting\n      if (valueArray.length) {\n        return valueArray;\n      }\n    }\n\n    // Check syntax highlighting results\n    let highlightedResults: Array<string | React.JSX.Element> = [];\n\n    // Only grab the first regex match from the logsSyntaxMatches object\n    Object.keys(logsSyntaxMatches).some((key) => {\n      const regex = value.match(logsSyntaxMatches[key]);\n      if (regex) {\n        highlightedResults = JSONHighlightRegexMatches([logsSyntaxMatches[key]], value, key);\n        return true;\n      }\n\n      return false;\n    });\n\n    if (highlightedResults.length) {\n      return highlightedResults;\n    }\n  }\n\n  return value;\n}\n", "import React, { memo, useMemo } from 'react';\n\nimport { css } from '@emotion/css';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { t } from '@grafana/i18n';\nimport { AdHocFilterWithLabels, SceneObject } from '@grafana/scenes';\nimport { Tooltip, useStyles2 } from '@grafana/ui';\n\nimport { addToFilters } from '../Breakdowns/AddToFiltersButton';\nimport { JSON_VIZ_LINE_HEIGHT } from './LogsJSONComponent';\nimport { FilterOp } from 'services/filterTypes';\nimport { logsLabelLevelsMatches } from 'services/panel';\nimport { LEVEL_VARIABLE_VALUE, VAR_LEVELS } from 'services/variables';\n\nfunction LineItemType({\n  detectedLevel,\n  sceneRef,\n  levelsVarFilters,\n}: {\n  detectedLevel: string;\n  levelsVarFilters: AdHocFilterWithLabels[];\n  sceneRef: SceneObject;\n}) {\n  const styles = useStyles2(getStyles);\n  const levelClass = Object.keys(logsLabelLevelsMatches).find((className) =>\n    detectedLevel.match(logsLabelLevelsMatches[className])\n  );\n  const existingFilter = levelsVarFilters.some(\n    (filter) => filter.value === detectedLevel && filter.operator === FilterOp.Equal\n  );\n\n  return useMemo(\n    () => (\n      <Tooltip\n        content={t(\n          'logs.json.line.detectedLevel.toggleButton',\n          existingFilter ? `Remove ${detectedLevel} filter` : `Include logs with ${detectedLevel} level`\n        )}\n      >\n        <button\n          onClick={(e) => {\n            e.stopPropagation();\n            addToFilters(LEVEL_VARIABLE_VALUE, detectedLevel, 'toggle', sceneRef, VAR_LEVELS);\n          }}\n          className={`${levelClass} ${styles.levelButtonStyles}`}\n        >\n          {detectedLevel.toUpperCase()}\n        </button>\n      </Tooltip>\n    ),\n    [existingFilter, detectedLevel, levelClass, sceneRef, styles.levelButtonStyles]\n  );\n}\n\nexport const JSONLineItemType = memo(LineItemType);\n\nconst getStyles = (theme: GrafanaTheme2) => {\n  return {\n    levelButtonStyles: css({\n      height: JSON_VIZ_LINE_HEIGHT,\n      marginLeft: '12px',\n      fontFamily: theme.typography.fontFamilyMonospace,\n      appearance: 'none',\n      background: 'none',\n      border: 'none',\n      fontSize: '0.9em',\n      // Keep button padding from pushing text further than other item string\n      padding: theme.spacing(0, 0.5, 0, 0.5),\n      '&:hover, &:focus': {\n        background: theme.colors.background.elevated,\n      },\n    }),\n  };\n};\n", "import React, { useEffect, useRef } from 'react';\n\nimport { css } from '@emotion/css';\n\nimport { GrafanaTheme2 } from '@grafana/data';\nimport { t } from '@grafana/i18n';\nimport { InlineToast, useStyles2 } from '@grafana/ui';\n\nimport ImgButton from '../UI/ImgButton';\n\nconst SHOW_SUCCESS_DURATION = 2 * 1000;\nconst COPY_TO_CLIPBOARD_TEXT = t('logs.log-line-details.copy-to-clipboard', 'Copy to clipboard');\nconst COPY_LINK_TO_LINE_TEXT = t('logs.log-line-menu.copy-link', 'Copy link to log line');\nconst COPY_LINK_ERROR_TEXT = t('logs.log-line-details.copy-to-clipboard-error', 'Error copying link!');\nconst COPY_SUCCESS = t('clipboard-button.inline-toast.success', 'Copied');\n\nexport default function CopyToClipboardButton({\n  onClick,\n  stopPropagation = true,\n  type = 'copy',\n}: {\n  onClick: () => void;\n  stopPropagation?: boolean;\n  type?: 'copy' | 'share-alt';\n}) {\n  const defaultText = type === 'copy' ? COPY_TO_CLIPBOARD_TEXT : COPY_LINK_TO_LINE_TEXT;\n  const [copied, setCopied] = React.useState(false);\n  const [copiedText, setCopiedText] = React.useState(COPY_SUCCESS);\n  const buttonRef = useRef<null | HTMLButtonElement>(null);\n  const styles = useStyles2(getStyles);\n\n  useEffect(() => {\n    let timeoutId: ReturnType<typeof setTimeout>;\n\n    if (copied) {\n      timeoutId = setTimeout(() => {\n        setCopied(false);\n      }, SHOW_SUCCESS_DURATION);\n    }\n\n    return () => {\n      window.clearTimeout(timeoutId);\n    };\n  }, [copied]);\n\n  return (\n    <>\n      {copied && (\n        <InlineToast placement=\"top\" referenceElement={buttonRef.current}>\n          {copiedText}\n        </InlineToast>\n      )}\n      <ImgButton\n        className={styles}\n        aria-pressed={copied}\n        tooltip={copied ? '' : defaultText}\n        name={type}\n        ref={buttonRef}\n        onClick={(e) => {\n          if (stopPropagation) {\n            // If the user clicked on the button, don't trigger the node to expand/collapse\n            e.stopPropagation();\n          }\n          try {\n            onClick();\n            setCopiedText(COPY_SUCCESS);\n          } catch (e) {\n            setCopiedText(COPY_LINK_ERROR_TEXT);\n          }\n          setCopied(true);\n        }}\n        tabIndex={0}\n      />\n    </>\n  );\n}\n\nconst getStyles = (theme: GrafanaTheme2) => {\n  return css({\n    color: theme.colors.text.secondary,\n  });\n};\n", "import React from 'react';\n\nimport { isNumber } from 'lodash';\n\nimport { DataFrame, Field, TimeRange } from '@grafana/data';\nimport { SceneDataProvider, sceneGraph } from '@grafana/scenes';\n\nimport { isLogLineField, isLogsIdField } from '../../../services/fields';\nimport { logger } from '../../../services/logger';\nimport { copyText, generateLogShortlink } from '../../../services/text';\nimport CopyToClipboardButton from '../../Buttons/CopyToClipboardButton';\nimport { JSONLogsScene } from '../JSONLogsScene';\nimport { getLogsPanelFrame } from '../ServiceScene';\nimport { KeyPath } from '@gtk-grafana/react-json-tree/dist/types';\n\ninterface Props {\n  keyPath: KeyPath;\n  model: JSONLogsScene;\n}\nexport function JSONLogLineActionButtons({ model, keyPath }: Props) {\n  const timeRange = sceneGraph.getTimeRange(model).state.value;\n  return (\n    <>\n      <CopyToClipboardButton onClick={() => copyLogLine(keyPath, sceneGraph.getData(model))} />\n      <CopyToClipboardButton\n        type={'share-alt'}\n        onClick={() => getLinkToLog(keyPath, timeRange, model.state.rawFrame)}\n      />\n    </>\n  );\n}\n\nconst copyLogLine = (keyPath: KeyPath, $data: SceneDataProvider) => {\n  const logLineIndex = keyPath[0];\n  const dataFrame = getLogsPanelFrame($data.state.data);\n  const lineField = dataFrame?.fields.find((f) => isLogLineField(f.name));\n  if (isNumber(logLineIndex) && lineField) {\n    const line = lineField.values[logLineIndex];\n    copyText(line.toString());\n  }\n};\n\nfunction getLinkToLog(keyPath: KeyPath, timeRange: TimeRange, rawFrame: DataFrame | undefined) {\n  const idField: Field<string> | undefined = rawFrame?.fields.find((f) => isLogsIdField(f.name));\n  const logLineIndex = keyPath[0];\n  if (!isNumber(logLineIndex)) {\n    const error = Error('Invalid line index');\n    logger.error(error, { msg: 'Error getting log line index' });\n    throw error;\n  }\n  const logId = idField?.values[logLineIndex];\n  const logLineLink = generateLogShortlink('selectedLine', { id: logId, row: logLineIndex }, timeRange);\n  copyText(logLineLink);\n}\n", "import React from 'react';\n\nimport { css } from '@emotion/css';\n\nimport { Field, FieldType, GrafanaTheme2, Labels } from '@grafana/data';\nimport { AdHocFiltersVariable } from '@grafana/scenes';\nimport { Icon, useStyles2 } from '@grafana/ui';\n\nimport { isLabelsField } from '../../../services/fields';\nimport { rootNodeItemString } from '../../../services/JSONViz';\nimport { hasProp } from '../../../services/narrowing';\nimport { LEVEL_VARIABLE_VALUE } from '../../../services/variables';\nimport {\n  JSONDataFrameLineName,\n  JSONDataFrameLinksName,\n  JSONDataFrameTimeName,\n  JSONLogsScene,\n  JSONVizRootName,\n} from '../JSONLogsScene';\nimport { JSONLineItemType } from './JSONLineItemType';\nimport { JSONLogLineActionButtons } from './JSONLogLineActionButtons';\nimport { KeyPath } from '@gtk-grafana/react-json-tree/dist/types';\n\ninterface ItemStringProps {\n  data: unknown;\n  itemString: string;\n  itemType: React.ReactNode;\n  keyPath: KeyPath;\n  levelsVar: AdHocFiltersVariable;\n  model: JSONLogsScene;\n  nodeType: string;\n}\n\nexport default function ItemString({ data, itemString, itemType, keyPath, model, levelsVar }: ItemStringProps) {\n  const styles = useStyles2(getStyles);\n  if (data && hasProp(data, JSONDataFrameTimeName) && typeof data.Time === 'string') {\n    return <JSONLogLineActionButtons keyPath={keyPath} model={model} />;\n  }\n\n  // The root node, which is visualized as the breadcrumb navigation\n  if (keyPath[0] === JSONVizRootName) {\n    return (\n      <span className={rootNodeItemString}>\n        {itemType} {itemString}\n      </span>\n    );\n  }\n\n  // log line nodes render the log level as the \"ItemString\"\n  if (keyPath[0] === JSONDataFrameLineName) {\n    const detectedLevel = getJsonDetectedLevel(model, keyPath);\n\n    if (detectedLevel) {\n      return (\n        <JSONLineItemType sceneRef={model} detectedLevel={detectedLevel} levelsVarFilters={levelsVar.state.filters} />\n      );\n    }\n  }\n\n  // Link nodes render the link icon\n  if (keyPath[0] === JSONDataFrameLinksName) {\n    return (\n      <span className={styles.wrapper}>\n        <Icon size={'sm'} name={'link'} />\n      </span>\n    );\n  }\n\n  // All other nodes return the itemType string from the library, e.g. [], {}\n  return <span className={styles.wrapper}>{itemType}</span>;\n}\n\nconst getJsonDetectedLevel = (model: JSONLogsScene, keyPath: KeyPath) => {\n  const labelsField: Field<Labels> | undefined = model.state.rawFrame?.fields.find(\n    (f) => f.type === FieldType.other && isLabelsField(f.name)\n  );\n  const index = typeof keyPath[1] === 'number' ? keyPath[1] : undefined;\n  const labels = index !== undefined ? labelsField?.values[index] : undefined;\n  return labels?.[LEVEL_VARIABLE_VALUE];\n};\n\nconst getStyles = (theme: GrafanaTheme2) => ({\n  wrapper: css({\n    color: theme.colors.emphasize(theme.colors.text.secondary, 0.33),\n    display: 'inline-flex',\n    alignItems: 'center',\n    height: '22px',\n  }),\n});\n", "(function(){\"use strict\";try{if(typeof document<\"u\"){var e=document.createElement(\"style\");e.appendChild(document.createTextNode(\"._arrowContainer_xqmcg_1{display:var(--json-tree-inline-flex);margin-right:var(--json-tree-arrow-container-margin-right)}._arrow_xqmcg_1{transition:var(--json-tree-arrow-transition);transform-origin:var(--json-tree-arrow-transform-origin);display:var(--json-tree-inline-flex);color:var(--json-tree-arrow-color);position:var(--json-tree-arrow-position);left:var(--json-tree-arrow-left-offset);margin-right:var(--json-tree-arrow-right-margin);top:0}._arrow--expanded_xqmcg_16{transform:var(--json-tree-arrow-transform)}._arrowInner_xqmcg_19{position:var(--json-tree-arrow-position);left:calc(var(--json-tree-arrow-width) / 3 * -1)}._itemRange_ed7dq_1{position:relative;display:var(--json-tree-inline)}._nestedNode_j6j98_1{display:var(--json-tree-inline-flex);position:relative;cursor:default}._nestedNode--expandable_j6j98_6{align-items:var(--json-tree-align-items);display:var(--json-tree-block);cursor:pointer}._nestedNode--expanded_j6j98_11{display:var(--json-tree-inline)}._nestedNode--itemType_j6j98_14{margin-right:var(--json-tree-nested-node-item-type-margin)}._nestedNode--itemType--expanded_j6j98_17{display:var(--json-tree-inline)}._nestedNodeLabelWrap_j6j98_21{display:var(--json-tree-block);align-items:var(--json-tree-align-items)}._nestedNodeItemString_j6j98_26{display:var(--json-tree-inline-flex)}._nestedNodeChildren_j6j98_30{display:var(--json-tree-block);flex-direction:var(--json-tree-flex-direction);margin-left:var(--json-tree-children-margin);padding:var(--json-tree-ul-padding)}._nestedNodeLabel_j6j98_21{display:var(--json-tree-inline-flex);align-items:var(--json-tree-align-items);margin-right:var(--json-tree-nested-node-label-margin);cursor:default}._nestedNodeLabel--expandable_j6j98_43{display:var(--json-tree-inline-flex);align-items:var(--json-tree-align-items);cursor:pointer}._nestedNodeLabel--expanded_j6j98_48{display:var(--json-tree-inline-flex)}._rootNode_j6j98_52{display:var(--json-tree-inline-flex);cursor:default}._rootNodeExpandable_j6j98_57{cursor:pointer}._rootNodeChildren_j6j98_61{display:var(--json-tree-block);flex-direction:var(--json-tree-flex-direction);padding:var(--json-tree-ul-root-children-padding)}._nodeListItemScrolled_18ya2_1{scroll-margin:var(--json-tree-scroll-margin)}._valueNode_1b1pr_1{display:var(--json-tree-block);align-items:var(--json-tree-value-node-align-items);cursor:default}._valueNodeValue_1b1pr_7{text-wrap:var(--json-tree-value-text-wrap);word-break:var(--json-tree-value-text-word-break);color:var(--json-tree-label-value-color)}._valueNodeLabel_1b1pr_13{margin-right:var(--json-tree-value-label-margin);display:var(--json-tree-inline-flex)}._valueNodeScrolled_1b1pr_18{scroll-margin:var(--json-tree-scroll-margin)}:root{--json-tree-spacing-sm: .25em;--json-tree-spacing-md: .5em;--json-tree-spacing-lg: 1em;--json-tree-spacing-xl: 1.5em;--json-tree-spacing-xxl: 2em;--json-tree-children-margin: var(--json-tree-spacing-xl);--json-tree-value-label-margin: var(--json-tree-spacing-sm);--json-tree-value-node-margin: var(--json-tree-spacing-xl);--json-tree-nested-node-label-margin: var(--json-tree-spacing-md);--json-tree-nested-node-item-type-margin: var(--json-tree-spacing-sm);--json-tree-default-label-wrap-margin-right: var(--json-tree-spacing-md);--json-tree-arrow-container-margin-right: 0;--json-tree-ul-root-padding: 0 0 0 var(--json-tree-spacing-xxl);--json-tree-ul-root-children-padding: 0;--json-tree-ul-padding: 0;--json-tree-value-text-wrap: wrap;--json-tree-value-text-word-break: break-word;--json-tree-inline: inline-grid;--json-tree-inline-flex: inline-flex;--json-tree-block: flex;--json-tree-align-items: center;--json-tree-value-node-align-items: flex-start;--json-tree-flex-direction: column;--json-tree-arrow-transition: var(--json-tree-transition-timing) linear transform;--json-tree-arrow-transform-origin: 45% 50%;--json-tree-arrow-transform: rotateZ(90deg);--json-tree-arrow-width: 1.5em;--json-tree-arrow-height: 1.5em;--json-tree-arrow-position: absolute;--json-tree-arrow-left-offset: calc(var(--json-tree-arrow-width) * -1);--json-tree-arrow-right-margin: 0;--json-tree-label-color: rgb(102, 217, 239);--json-tree-key-label-color: rgb(71, 131, 0);--json-tree-label-value-color: rgb(249, 38, 114);--json-tree-arrow-color: var(--json-tree-label-color);--json-tree-transition-timing: .15s;--json-tree-scroll-margin: 0}._tree_ze97d_48{padding:var(--json-tree-ul-root-padding)}._defaultItemString_ze97d_52{display:var(--json-tree-inline-flex);color:var(--json-tree-key-label-color)}._defaultLabelWrap_ze97d_57{display:var(--json-tree-inline-flex);margin-right:var(--json-tree-default-label-wrap-margin-right);color:var(--json-tree-label-color);cursor:default}._defaultLabelWrap--expandable_ze97d_63{cursor:pointer}\")),document.head.appendChild(e)}}catch(r){console.error(\"vite-plugin-css-injected-by-js\",r)}})();\nimport { jsxs as g, jsx as c } from \"react/jsx-runtime\";\nimport S, { useState as M, useCallback as J, createElement as k } from \"react\";\nfunction K(e) {\n  const t = Object.prototype.toString.call(e).slice(8, -1);\n  return t === \"Object\" && typeof e[Symbol.iterator] == \"function\" ? \"Iterable\" : t === \"Custom\" && e.constructor !== Object && e instanceof Object ? \"Object\" : t;\n}\nconst U = \"_arrow_xqmcg_1\", H = \"_arrow--expanded_xqmcg_16\", Q = \"_arrowInner_xqmcg_19\", x = {\n  arrow: U,\n  arrowExpanded: H,\n  arrowInner: Q\n};\nfunction P({\n  arrowStyle: e = \"single\",\n  expanded: t,\n  onClick: n\n}) {\n  return /* @__PURE__ */ g(\n    \"div\",\n    {\n      role: \"button\",\n      \"aria-expanded\": t,\n      tabIndex: 0,\n      onClick: n,\n      onKeyDown: (a) => {\n        (a.key === \"Enter\" || a.key === \" \") && (a.preventDefault(), n());\n      },\n      className: `${x.arrow} ${t ? x.arrowExpanded : \"\"} ${e === \"single\" ? x.arrowArrowStyleSingle : x.arrowArrowStyleDouble}`,\n      children: [\n        \"▶\",\n        e === \"double\" && /* @__PURE__ */ c(\"div\", { className: `${x.arrowInner}`, children: \"▶\" })\n      ]\n    }\n  );\n}\nfunction X(e, t) {\n  return e === \"Object\" ? Object.keys(t).length : e === \"Array\" ? t.length : 1 / 0;\n}\nfunction Y(e) {\n  return typeof e.set == \"function\";\n}\nfunction Z(e, t, n, a = 0, r = 1 / 0) {\n  let s;\n  if (e === \"Object\") {\n    let d = Object.getOwnPropertyNames(t);\n    n && d.sort(n === !0 ? void 0 : n), d = d.slice(a, r + 1), s = {\n      entries: d.map((l) => ({ key: l, value: t[l] }))\n    };\n  } else if (e === \"Array\")\n    s = {\n      entries: t.slice(a, r + 1).map((d, l) => ({ key: l + a, value: d }))\n    };\n  else {\n    let d = 0;\n    const l = [];\n    let i = !0;\n    const N = Y(t);\n    for (const o of t) {\n      if (d > r) {\n        i = !1;\n        break;\n      }\n      a <= d && (N && Array.isArray(o) ? typeof o[0] == \"string\" || typeof o[0] == \"number\" ? l.push({ key: o[0], value: o[1] }) : l.push({\n        key: `[entry ${d}]`,\n        value: {\n          \"[key]\": o[0],\n          \"[value]\": o[1]\n        }\n      }) : l.push({ key: d, value: o })), d++;\n    }\n    s = {\n      hasMore: !i,\n      entries: l\n    };\n  }\n  return s;\n}\nfunction L(e, t, n) {\n  const a = [];\n  for (; t - e > n * n; )\n    n = n * n;\n  for (let r = e; r <= t; r += n)\n    a.push({ from: r, to: Math.min(t, r + n - 1) });\n  return a;\n}\nfunction ee(e, t, n, a, r = 0, s = 1 / 0) {\n  const d = Z.bind(\n    null,\n    e,\n    t,\n    n\n  );\n  if (!a)\n    return d().entries;\n  const l = s < 1 / 0, i = Math.min(s - r, X(e, t));\n  if (e !== \"Iterable\") {\n    if (i <= a || a < 7)\n      return d(r, s).entries;\n  } else if (i <= a && !l)\n    return d(r, s).entries;\n  let N;\n  if (e === \"Iterable\") {\n    const { hasMore: o, entries: u } = d(r, r + a - 1);\n    N = o ? [...u, ...L(r + a, r + 2 * a - 1, a)] : u;\n  } else\n    N = l ? L(r, s, a) : [\n      ...d(0, a - 5).entries,\n      ...L(a - 4, i - 5, a),\n      ...d(i - 4, i - 1).entries\n    ];\n  return N;\n}\nconst te = \"_itemRange_ed7dq_1\", R = {\n  itemRange: te\n};\nfunction ne(e) {\n  const { from: t, to: n, renderChildNodes: a, nodeType: r, scrollToPath: s, keyPath: d } = e;\n  let l = !1;\n  if (s) {\n    const [u] = s;\n    C(s.slice(d.length * -1), d) && u > t && u <= n && (l = !0);\n  }\n  const [i, N] = M(l), o = J(() => {\n    N(!i);\n  }, [i]);\n  return i ? /* @__PURE__ */ c(\"div\", { className: `${R.itemRange}`, children: a(e, t, n) }) : /* @__PURE__ */ g(\"div\", { className: `${R.itemRange}`, children: [\n    /* @__PURE__ */ c(\n      P,\n      {\n        nodeType: r,\n        expanded: !1,\n        onClick: o,\n        arrowStyle: \"double\"\n      }\n    ),\n    `${t} ... ${n}`\n  ] });\n}\nconst ae = \"_nestedNode_j6j98_1\", re = \"_nestedNode--expandable_j6j98_6\", oe = \"_nestedNode--expanded_j6j98_11\", de = \"_nestedNode--itemType_j6j98_14\", se = \"_nestedNode--itemType--expanded_j6j98_17\", le = \"_nestedNodeLabelWrap_j6j98_21\", ce = \"_nestedNodeItemString_j6j98_26\", ie = \"_nestedNodeChildren_j6j98_30\", ue = \"_nestedNodeLabel_j6j98_21\", Ne = \"_nestedNodeLabel--expandable_j6j98_43\", pe = \"_nestedNodeLabel--expanded_j6j98_48\", fe = \"_rootNode_j6j98_52\", be = \"_rootNodeExpandable_j6j98_57\", _e = \"_rootNodeChildren_j6j98_61\", b = {\n  nestedNode: ae,\n  nestedNodeExpandable: re,\n  nestedNodeExpanded: oe,\n  nestedNodeItemType: de,\n  nestedNodeItemTypeExpanded: se,\n  nestedNodeLabelWrap: le,\n  nestedNodeItemString: ce,\n  nestedNodeChildren: ie,\n  nestedNodeLabel: ue,\n  nestedNodeLabelExpandable: Ne,\n  nestedNodeLabelExpanded: pe,\n  rootNode: fe,\n  rootNodeExpandable: be,\n  rootNodeChildren: _e\n}, he = \"_nodeListItemScrolled_18ya2_1\", ye = {\n  nodeListItemScrolled: he\n}, W = ({\n  children: e,\n  expanded: t,\n  expandable: n,\n  nodeType: a,\n  keyPath: r,\n  className: s,\n  scrollToPath: d\n}) => {\n  var o, u;\n  const l = S.useRef(null), i = d !== void 0 && C(d, r);\n  S.useEffect(() => {\n    l.current && l.current.scrollIntoView({ behavior: \"auto\" });\n  }, []);\n  const N = i ? { ref: l, \"data-scrolled\": \"true\" } : {};\n  return n ? /* @__PURE__ */ c(\n    \"li\",\n    {\n      role: \"treeitem\",\n      \"aria-expanded\": t,\n      \"data-nodetype\": a,\n      \"data-keypath\": r[0],\n      \"aria-label\": (o = r[0]) == null ? void 0 : o.toString(),\n      className: `${s} ${i ? ye.nodeListItemScrolled : \"\"}`,\n      ...N,\n      children: e\n    }\n  ) : /* @__PURE__ */ c(\n    \"li\",\n    {\n      role: \"treeitem\",\n      \"data-nodetype\": a,\n      \"data-keypath\": r[0],\n      \"aria-label\": (u = r[0]) == null ? void 0 : u.toString(),\n      className: s,\n      ...N,\n      children: e\n    }\n  );\n};\nfunction ge(e) {\n  return e.to !== void 0;\n}\nfunction V(e, t, n) {\n  const {\n    nodeType: a,\n    data: r,\n    collectionLimit: s,\n    circularCache: d,\n    keyPath: l,\n    postprocessValue: i,\n    sortObjectKeys: N,\n    scrollToPath: o\n  } = e, u = [];\n  return ee(\n    a,\n    r,\n    N,\n    s,\n    t,\n    n\n  ).forEach((f) => {\n    if (ge(f))\n      u.push(\n        /* @__PURE__ */ k(\n          ne,\n          {\n            ...e,\n            key: `ItemRange--${f.from}-${f.to}`,\n            from: f.from,\n            to: f.to,\n            renderChildNodes: V,\n            scrollToPath: o\n          }\n        )\n      );\n    else {\n      const { key: p, value: m } = f, E = d.includes(m);\n      u.push(\n        /* @__PURE__ */ k(\n          z,\n          {\n            ...e,\n            postprocessValue: i,\n            collectionLimit: s,\n            key: `Node--${p}`,\n            keyPath: [p, ...l],\n            value: i(m),\n            circularCache: [...d, m],\n            isCircular: E,\n            hideRoot: !1\n          }\n        )\n      );\n    }\n  }), u;\n}\nfunction w(e) {\n  const {\n    circularCache: t = [],\n    collectionLimit: n,\n    createItemString: a,\n    data: r,\n    expandable: s,\n    getItemString: d,\n    hideRoot: l,\n    hideRootExpand: i,\n    isCircular: N,\n    keyPath: o,\n    labelRenderer: u,\n    level: f = 0,\n    nodeType: p,\n    nodeTypeIndicator: m,\n    shouldExpandNodeInitially: E,\n    scrollToPath: O\n  } = e, G = o[0] === \"root\", T = i ? s && !G && i : s, y = s && T, [_, q] = M(\n    // calculate individual node expansion if necessary\n    N ? !1 : E(o, r, f)\n  ), j = J(() => {\n    y && q(!_);\n  }, [y, _]), v = _ || l && f === 0 ? V({ ...e, circularCache: t, level: f + 1 }) : null, D = /* @__PURE__ */ c(\n    \"span\",\n    {\n      className: `${b.nestedNodeItemType} ${_ ? b.nestedNodeItemTypeExpanded : \"\"}`,\n      children: m\n    }\n  ), B = d(\n    p,\n    r,\n    D,\n    a(r, n),\n    o\n  ), F = [o, p, _, y];\n  return l ? /* @__PURE__ */ c(\n    W,\n    {\n      scrollToPath: O,\n      expandable: y,\n      expanded: _,\n      nodeType: p,\n      keyPath: o,\n      className: `${b.rootNode} ${_ ? b.rootNodeExpanded : \"\"} ${s ? b.rootNodeExpandable : \"\"}`,\n      children: /* @__PURE__ */ c(\"ul\", { className: `${b.rootNodeChildren}`, children: v })\n    }\n  ) : /* @__PURE__ */ g(\n    W,\n    {\n      scrollToPath: O,\n      expandable: y,\n      expanded: _,\n      nodeType: p,\n      keyPath: o,\n      className: `${b.nestedNode}  ${_ ? b.nestedNodeExpanded : \"\"} ${y ? b.nestedNodeExpandable : \"\"}`,\n      children: [\n        /* @__PURE__ */ g(\"span\", { className: b.nestedNodeLabelWrap, children: [\n          T && /* @__PURE__ */ c(\n            P,\n            {\n              nodeType: p,\n              expanded: _,\n              onClick: j\n            }\n          ),\n          /* @__PURE__ */ c(\n            \"span\",\n            {\n              \"data-nodetype\": p,\n              \"data-keypath\": o[0],\n              className: `${b.nestedNodeLabel} ${_ ? b.nestedNodeLabelExpanded : \"\"} ${y ? b.nestedNodeLabelExpandable : \"\"}`,\n              onClick: j,\n              children: u(...F)\n            }\n          ),\n          /* @__PURE__ */ c(\"span\", { className: b.nestedNodeItemString, onClick: j, children: B })\n        ] }),\n        v && /* @__PURE__ */ c(\"ul\", { className: b.nestedNodeChildren, children: v })\n      ]\n    }\n  );\n}\nfunction me(e) {\n  const t = Object.getOwnPropertyNames(e).length;\n  return `${t} ${t !== 1 ? \"keys\" : \"key\"}`;\n}\nfunction xe({ data: e, ...t }) {\n  return /* @__PURE__ */ c(\n    w,\n    {\n      ...t,\n      data: e,\n      nodeType: \"Object\",\n      nodeTypeIndicator: t.nodeType === \"Error\" ? \"Error()\" : \"{}\",\n      createItemString: me,\n      expandable: Object.getOwnPropertyNames(e).length > 0\n    }\n  );\n}\nfunction Ie(e) {\n  return `${e.length} ${e.length !== 1 ? \"items\" : \"item\"}`;\n}\nfunction Se({ data: e, ...t }) {\n  return /* @__PURE__ */ c(\n    w,\n    {\n      ...t,\n      data: e,\n      nodeType: \"Array\",\n      nodeTypeIndicator: \"[]\",\n      createItemString: Ie,\n      expandable: e.length > 0\n    }\n  );\n}\nfunction $e(e, t) {\n  let n = 0, a = !1;\n  if (Number.isSafeInteger(e.size))\n    n = e.size;\n  else\n    for (const r of e) {\n      if (t && n + 1 > t) {\n        a = !0;\n        break;\n      }\n      n += 1;\n    }\n  return `${a ? \">\" : \"\"}${n} ${n !== 1 ? \"entries\" : \"entry\"}`;\n}\nfunction Ee(e) {\n  return /* @__PURE__ */ c(\n    w,\n    {\n      ...e,\n      nodeType: \"Iterable\",\n      nodeTypeIndicator: \"()\",\n      createItemString: $e,\n      expandable: !0\n    }\n  );\n}\nconst je = \"_valueNode_1b1pr_1\", ve = \"_valueNodeValue_1b1pr_7\", Le = \"_valueNodeLabel_1b1pr_13\", ke = \"_valueNodeScrolled_1b1pr_18\", I = {\n  valueNode: je,\n  valueNodeValue: ve,\n  valueNodeLabel: Le,\n  valueNodeScrolled: ke\n};\nfunction h({\n  nodeType: e,\n  labelRenderer: t,\n  keyPath: n,\n  valueRenderer: a,\n  value: r,\n  valueGetter: s = (l) => l,\n  scrollToPath: d\n}) {\n  const l = S.useRef(null), i = d !== void 0 && C(d, n);\n  S.useEffect(() => {\n    l.current && l.current.scrollIntoView({ behavior: \"auto\" });\n  }, []);\n  const N = i ? { ref: l, \"data-scrolled\": \"true\" } : {};\n  return /* @__PURE__ */ g(\n    \"li\",\n    {\n      className: `${I.valueNode} valueNode--${e} valueNode--${n[0]} ${i ? I.valueNodeScrolled : \"\"}`,\n      ...N,\n      children: [\n        /* @__PURE__ */ c(\"span\", { className: I.valueNodeLabel, children: t(n, e, !1, !1) }),\n        /* @__PURE__ */ c(\"span\", { className: I.valueNodeValue, children: a(\n          s ? s(r) : void 0,\n          r,\n          ...n\n        ) })\n      ]\n    }\n  );\n}\nfunction z({\n  getItemString: e,\n  keyPath: t,\n  labelRenderer: n,\n  value: a,\n  valueRenderer: r,\n  isCustomNode: s,\n  valueWrap: d,\n  scrollToPath: l,\n  ...i\n}) {\n  const N = s(a) ? \"Custom\" : K(a), o = t[0], u = {\n    keyPath: t,\n    labelRenderer: n,\n    nodeType: N,\n    value: a,\n    valueRenderer: r,\n    scrollToPath: l\n  }, f = {\n    ...i,\n    ...u,\n    getItemString: e,\n    data: a,\n    isCustomNode: s,\n    valueWrap: d\n  };\n  switch (N) {\n    case \"Object\":\n    case \"Error\":\n    case \"WeakMap\":\n    case \"WeakSet\":\n      return /* @__PURE__ */ c(xe, { ...f }, o);\n    case \"Array\":\n      return /* @__PURE__ */ c(Se, { ...f }, o);\n    case \"Iterable\":\n    case \"Map\":\n    case \"Set\":\n      return /* @__PURE__ */ c(Ee, { ...f }, o);\n    case \"String\":\n      return /* @__PURE__ */ k(\n        h,\n        {\n          ...u,\n          key: o,\n          valueGetter: (p) => `${d}${p}${d}`\n        }\n      );\n    case \"Number\":\n      return /* @__PURE__ */ c(h, { ...u }, o);\n    case \"Boolean\":\n      return /* @__PURE__ */ c(\n        h,\n        {\n          ...u,\n          valueGetter: (p) => p ? \"true\" : \"false\"\n        },\n        o\n      );\n    case \"Date\":\n      return /* @__PURE__ */ c(\n        h,\n        {\n          ...u,\n          valueGetter: (p) => p.toISOString()\n        },\n        o\n      );\n    case \"Null\":\n      return /* @__PURE__ */ c(\n        h,\n        {\n          ...u,\n          valueGetter: () => \"null\"\n        },\n        o\n      );\n    case \"Undefined\":\n      return /* @__PURE__ */ c(\n        h,\n        {\n          ...u,\n          valueGetter: () => \"undefined\"\n        },\n        o\n      );\n    case \"Function\":\n    case \"Symbol\":\n      return /* @__PURE__ */ c(\n        h,\n        {\n          ...u,\n          valueGetter: (p) => p.toString()\n        },\n        o\n      );\n    case \"Custom\":\n      return /* @__PURE__ */ c(h, { ...u }, o);\n    default:\n      return /* @__PURE__ */ c(\n        h,\n        {\n          ...u,\n          valueGetter: () => `<${N}>`\n        },\n        o\n      );\n  }\n}\nconst we = \"_tree_ze97d_48\", Ce = \"_defaultItemString_ze97d_52\", Oe = \"_defaultLabelWrap_ze97d_57\", Te = \"_defaultLabelWrap--expandable_ze97d_63\", $ = {\n  tree: we,\n  defaultItemString: Ce,\n  defaultLabelWrap: Oe,\n  defaultLabelWrapExpandable: Te\n}, A = (e) => e, Re = (e, t, n) => n === 0, We = (e, t, n, a, r) => /* @__PURE__ */ g(\"span\", { className: $.defaultItemString, children: [\n  n,\n  \" \",\n  a\n] }), Ae = ([e], t, n, a) => /* @__PURE__ */ g(\n  \"span\",\n  {\n    className: `${$.defaultLabelWrap} ${a ? $.defaultLabelWrapExpandable : \"\"}`,\n    children: [\n      e,\n      \":\"\n    ]\n  }\n), Me = () => !1;\nfunction Ve({\n  data: e,\n  keyPath: t = [\"root\"],\n  labelRenderer: n = Ae,\n  valueRenderer: a = A,\n  shouldExpandNodeInitially: r = Re,\n  hideRoot: s = !1,\n  hideRootExpand: d = !1,\n  getItemString: l = We,\n  postprocessValue: i = A,\n  isCustomNode: N = Me,\n  collectionLimit: o = 50,\n  sortObjectKeys: u = !1,\n  scrollToPath: f,\n  valueWrap: p = '\"'\n}) {\n  return /* @__PURE__ */ c(\n    \"ul\",\n    {\n      role: \"tree\",\n      \"aria-multiselectable\": !0,\n      \"aria-readonly\": \"true\",\n      className: $.tree,\n      children: /* @__PURE__ */ c(\n        z,\n        {\n          scrollToPath: f,\n          hideRootExpand: d,\n          keyPath: s ? [] : t,\n          value: i(e),\n          isCustomNode: N,\n          labelRenderer: n,\n          valueRenderer: a,\n          shouldExpandNodeInitially: r,\n          hideRoot: s,\n          getItemString: l,\n          postprocessValue: i,\n          collectionLimit: o,\n          sortObjectKeys: u,\n          valueWrap: p\n        }\n      )\n    }\n  );\n}\nconst C = (e, t) => {\n  if (!e.length || e.length !== t.length)\n    return !1;\n  for (let n = 0; n < e.length; n++)\n    if (e[n] !== t[n])\n      return !1;\n  return !0;\n};\nexport {\n  Ve as JSONTree,\n  C as areKeyPathsEqual\n};\n", "import React, { useCallback, useMemo, useRef } from 'react';\n\nimport { css } from '@emotion/css';\n\nimport { colorManipulator, FieldType, GrafanaTheme2 } from '@grafana/data';\nimport { AdHocFilterWithLabels, SceneComponentProps, sceneGraph } from '@grafana/scenes';\nimport { Alert, PanelChrome, useStyles2 } from '@grafana/ui';\n\nimport { NoMatchingLabelsScene } from '../Breakdowns/NoMatchingLabelsScene';\nimport { JSONLogsScene, JSONVizRootName } from '../JSONLogsScene';\nimport LabelRenderer from '../JSONPanel/LabelRenderer';\nimport ValueRenderer from '../JSONPanel/ValueRenderer';\nimport { LogListControls } from '../LogListControls';\nimport { LogsListScene } from '../LogsListScene';\nimport { getLogsPanelFrame } from '../ServiceScene';\nimport ItemString from './ItemString';\nimport { JSONTree } from '@gtk-grafana/react-json-tree';\nimport { ScrollToPath } from '@gtk-grafana/react-json-tree/dist/types';\nimport { LogsPanelHeaderActions } from 'Components/Table/LogsHeaderActions';\nimport { isLogLineField, isLogsIdField } from 'services/fields';\nimport { getLogsHighlightStyles } from 'services/highlight';\nimport {\n  setJSONHighlightVisibility,\n  setJSONLabelsVisibility,\n  setJSONMetadataVisibility,\n  setLogOption,\n} from 'services/store';\nimport {\n  getFieldsVariable,\n  getJSONFieldsVariable,\n  getLevelsVariable,\n  getLineFiltersVariable,\n} from 'services/variableGetters';\n\nexport const JSON_VIZ_LINE_HEIGHT = '24px';\n\nexport default function LogsJSONComponent({ model }: SceneComponentProps<JSONLogsScene>) {\n  const {\n    emptyScene,\n    hasJSONFields,\n    JSONFiltersSupported,\n    menu,\n    hasHighlight,\n    hasLabels,\n    hasMetadata,\n    sortOrder,\n    wrapLogMessage,\n    data,\n    rawFrame,\n  } = model.useState();\n  // Rerender on data change\n  const $data = sceneGraph.getData(model);\n  $data.useState();\n\n  const logsListScene = sceneGraph.getAncestor(model, LogsListScene);\n  const { visualizationType, selectedLine } = logsListScene.useState();\n  const styles = useStyles2(getStyles, wrapLogMessage);\n\n  const fieldsVar = getFieldsVariable(model);\n  const JSONVariable = getJSONFieldsVariable(model);\n  const levelsVar = getLevelsVariable(model);\n\n  // If we have a line format variable, we are drilled down into a nested node\n  const dataFrame = getLogsPanelFrame(data);\n  const lineField = dataFrame?.fields.find((field) => field.type === FieldType.string && isLogLineField(field.name));\n  const JSONParserPropsMap = new Map<string, AdHocFilterWithLabels>();\n  const lineFilterVar = getLineFiltersVariable(model);\n\n  const scrollToPath: ScrollToPath | undefined = useMemo(() => {\n    if (selectedLine === undefined) {\n      return undefined;\n    }\n    const idField = rawFrame?.fields.find((field) => isLogsIdField(field.name));\n    const lineIndex = idField?.values.findIndex((v) => v === selectedLine?.id);\n    const cleanLineIndex = lineIndex !== undefined && lineIndex !== -1 ? lineIndex : undefined;\n    return cleanLineIndex !== undefined ? [cleanLineIndex, JSONVizRootName] : undefined;\n  }, [selectedLine, rawFrame]);\n\n  JSONVariable.state.filters.forEach((filter) => {\n    // @todo this should probably be set in the AdHocFilterWithLabels valueLabels array\n    // all json props are wrapped with [\\\" ... \"\\], strip those chars out so we have the actual key used in the json\n    const fullKeyFromJSONParserProps = filter.value\n      .substring(3, filter.value.length - 3)\n      .split('\\\\\"][\\\\\"')\n      .join('_');\n    JSONParserPropsMap.set(fullKeyFromJSONParserProps, filter);\n  });\n  const scrollRef = useRef<HTMLDivElement | null>(null);\n\n  const onScrollToBottomClick = useCallback(() => {\n    scrollRef.current?.scrollTo(0, scrollRef.current.scrollHeight);\n  }, []);\n\n  const onScrollToTopClick = useCallback(() => {\n    scrollRef.current?.scrollTo(0, 0);\n  }, []);\n\n  const onToggleStructuredMetadataClick = useCallback(\n    (visible: boolean) => {\n      model.setState({ hasMetadata: visible });\n      setJSONMetadataVisibility(visible);\n    },\n    [model]\n  );\n\n  const onToggleLabelsClick = useCallback(\n    (visible: boolean) => {\n      model.setState({ hasLabels: visible });\n      setJSONLabelsVisibility(visible);\n    },\n    [model]\n  );\n\n  const onToggleHighlightClick = useCallback(\n    (visible: boolean) => {\n      model.setState({ hasHighlight: visible });\n      setJSONHighlightVisibility(visible);\n    },\n    [model]\n  );\n\n  const onWrapLogMessageClick = useCallback(\n    (wrap: boolean) => {\n      model.setState({ wrapLogMessage: wrap });\n      setLogOption('wrapLogMessage', wrap);\n    },\n    [model]\n  );\n\n  const showNoJSONDetected = lineField && lineField.values.length > 0 && hasJSONFields === false;\n  const showLokiNotSupported = !JSONFiltersSupported && showNoJSONDetected !== true;\n\n  return (\n    <div className={styles.panelChromeWrap}>\n      {/* @ts-expect-error todo: fix this when https://github.com/grafana/grafana/issues/103486 is done*/}\n      <PanelChrome\n        padding={'none'}\n        showMenuAlways={true}\n        statusMessage={$data.state.data?.errors?.[0].message}\n        loadingState={$data.state.data?.state}\n        title={'JSON'}\n        menu={menu ? <menu.Component model={menu} /> : undefined}\n        actions={<LogsPanelHeaderActions vizType={visualizationType} onChange={logsListScene.setVisualizationType} />}\n      >\n        <div className={styles.container}>\n          {lineField?.values && lineField?.values.length > 0 && (\n            <LogListControls\n              onWrapLogMessageClick={onWrapLogMessageClick}\n              wrapLogMessage={wrapLogMessage}\n              showHighlight={hasHighlight}\n              onToggleHighlightClick={onToggleHighlightClick}\n              showMetadata={hasMetadata}\n              onToggleStructuredMetadataClick={onToggleStructuredMetadataClick}\n              showLabels={hasLabels}\n              onToggleLabelsClick={onToggleLabelsClick}\n              sortOrder={sortOrder}\n              onSortOrderChange={model.handleSortChange}\n              onScrollToBottomClick={onScrollToBottomClick}\n              onScrollToTopClick={onScrollToTopClick}\n            />\n          )}\n          {lineField?.values && lineField?.values.length > 0 && (\n            <div className={styles.JSONTreeWrap} ref={scrollRef}>\n              {showLokiNotSupported && (\n                <Alert className={styles.alert} severity={'warning'} title={'JSON filtering requires Loki 3.5.0.'}>\n                  This view will be read only until Loki is upgraded to 3.5.0\n                </Alert>\n              )}\n              {showNoJSONDetected && (\n                <Alert className={styles.alert} severity={'info'} title={'No JSON fields detected'}>\n                  This view is built for JSON log lines, but none were detected. Switch to the Logs or Table view for a\n                  better experience.\n                </Alert>\n              )}\n              <JSONTree\n                scrollToPath={scrollToPath}\n                data={lineField.values}\n                hideRootExpand={true}\n                valueWrap={''}\n                shouldExpandNodeInitially={(_, __, level) => level <= 2}\n                // Render item type string, e.g. (), {}\n                getItemString={(nodeType, data, itemType, itemString, keyPath) => (\n                  <ItemString\n                    itemString={itemString}\n                    keyPath={keyPath}\n                    itemType={itemType}\n                    data={data}\n                    nodeType={nodeType}\n                    model={model}\n                    levelsVar={levelsVar}\n                  />\n                )}\n                // Render node values\n                valueRenderer={(valueAsString, _, ...keyPath) => (\n                  <ValueRenderer\n                    valueAsString={valueAsString}\n                    keyPath={keyPath}\n                    lineFilters={lineFilterVar.state.filters}\n                    model={model}\n                  />\n                )}\n                // Render node labels\n                labelRenderer={(keyPath, nodeType) => (\n                  <LabelRenderer\n                    model={model}\n                    nodeType={nodeType}\n                    keyPath={keyPath}\n                    fieldsVar={fieldsVar}\n                    lineField={lineField}\n                    JSONFiltersSupported={JSONFiltersSupported}\n                    JSONParserPropsMap={JSONParserPropsMap}\n                    lineFilters={lineFilterVar.state.filters}\n                  />\n                )}\n              />\n            </div>\n          )}\n          {emptyScene && lineField?.values.length === 0 && <NoMatchingLabelsScene.Component model={emptyScene} />}\n        </div>\n      </PanelChrome>\n    </div>\n  );\n}\n\nconst getStyles = (theme: GrafanaTheme2, wrapLogMessage: boolean) => {\n  const hoverBg = theme.isDark\n    ? colorManipulator.alpha(colorManipulator.lighten(theme.colors.background.canvas, 0.1), 0.4)\n    : colorManipulator.alpha(colorManipulator.darken(theme.colors.background.canvas, 0.1), 0.4);\n\n  const selectedBg = theme.isDark\n    ? colorManipulator.darken(colorManipulator.alpha(theme.colors.info.transparent, 1), 0.2)\n    : colorManipulator.lighten(colorManipulator.alpha(theme.colors.info.transparent, 1), 0.2);\n\n  return {\n    alert: css({\n      marginTop: theme.spacing(3.5),\n      marginBottom: 0,\n    }),\n    panelChromeWrap: css({\n      // Required to keep content from pushing out of page wrapper when the grafana menu is docked\n      contain: 'strict',\n      width: '100%',\n      height: '100%',\n    }),\n    container: css({\n      display: 'flex',\n      flexDirection: 'row-reverse',\n      height: '100%',\n      paddingBottom: theme.spacing(1),\n      paddingRight: theme.spacing(1),\n      ...getLogsHighlightStyles(theme),\n      contain: 'content',\n    }),\n    highlight: css({\n      backgroundColor: 'rgb(255, 153, 0)',\n      color: 'black',\n    }),\n\n    JSONTreeWrap: css`\n      font-family: ${theme.typography.fontFamilyMonospace};\n      font-family: ${theme.typography.fontFamilyMonospace}; // override css variables\n      --json-tree-align-items: flex-start;\n      --json-tree-label-color: ${theme.colors.text.primary};\n      --json-tree-label-value-color: ${theme.colors.text.secondary};\n      --json-tree-arrow-color: ${theme.colors.secondary.contrastText};\n      --json-tree-ul-root-padding: ${theme.spacing(3)} 0 ${theme.spacing(2)} 0;\n      --json-tree-arrow-left-offset: -${theme.spacing(2)};\n      --json-tree-inline: inline-grid;\n      --json-tree-value-label-margin: 1em;\n      // Scroll offset for sticky header\n      --json-tree-scroll-margin: 26px;\n      // Scroll behavior @todo set \"auto\" instead of \"smooth\" for users with prefers-reduced-motion\n      scroll-behavior: ${window.matchMedia('(prefers-reduced-motion: reduce)').matches ? 'auto' : 'smooth'};\n      ${getWrapLogMessageStyles(theme, wrapLogMessage)}\n\n      overflow: auto;\n      height: 100%;\n      width: 100%;\n      // Prevents scrollbar from getting tucked behind the fixed header\n      clip-path: inset(0 0 0 0);\n\n      // Line height defines the height of each line\n      li {\n        line-height: ${JSON_VIZ_LINE_HEIGHT};\n      }\n\n      //first treeItem node\n      > ul > li {\n        // line wrap\n        width: 100%;\n        margin-top: ${theme.spacing(2.5)};\n      }\n\n      // Array and other labels additional without markup\n      // first nested node padding\n      > ul > li > ul {\n        // Hackery to keep elements from under the sticky header from being in the scrollable area\n        padding: 0 0 0 ${theme.spacing(2)};\n      }\n\n      // Root node styles\n      > ul > li > span {\n        position: fixed;\n        top: 0;\n        left: 0;\n        width: calc(100% - ${theme.spacing(4.75)});\n        background: ${theme.colors.background.primary};\n        padding-bottom: ${theme.spacing(0.5)};\n        margin-bottom: ${theme.spacing(0.5)};\n        box-shadow: 0 1px 7px rgba(1, 4, 9, 0.75);\n        z-index: 2;\n        padding-left: ${theme.spacing(1)};\n        align-items: center;\n        overflow-x: auto;\n        overflow-y: hidden;\n      }\n\n      // Line node scrolledTo styles\n      li[data-scrolled='true'] {\n        // sticky header cannot have transparency!\n        & > span {\n          background-color: ${selectedBg};\n        }\n        background-color: ${colorManipulator.alpha(theme.colors.info.transparent, 0.25)};\n      }\n\n      // Value node background hover colors\n      .valueNode--String,\n      .valueNode--Number {\n        &:hover {\n          // This is the value line the user is hovering over, we don't want transparency so it always stands out from the bg\n          background-color: ${theme.colors.background.canvas};\n          box-shadow: ${theme.shadows.z1};\n        }\n      }\n\n      // Nested node hover styles\n      ul > li > ul > li > ul {\n        li[data-nodetype='Object'],\n        li[data-nodetype='Array'] {\n          // This is the nested node line the user is hovering over, we don't want transparency so it always stands out from the bg\n          & > span {\n            width: 100%;\n            &:hover {\n              background-color: ${theme.colors.background.canvas};\n              box-shadow: ${theme.shadows.z1};\n            }\n          }\n          // And this is the node the user is hovering in, we want some transparency to help differentiate between nodes of differing depths\n          &:hover {\n            background-color: ${hoverBg};\n            box-shadow: ${theme.shadows.z1};\n          }\n        }\n      }\n\n      // sticky time header\n      > ul > li > ul > li > span,\n      > ul > li > ul > div > li > span {\n        position: sticky;\n        width: 100%;\n        top: 26px;\n        left: 0;\n        background: ${theme.colors.background.primary};\n        z-index: 1;\n        display: flex;\n        align-items: center;\n        &:hover {\n          background-color: ${theme.colors.background.canvas};\n          box-shadow: ${theme.shadows.z1};\n        }\n      }\n    `,\n  };\n};\n\nconst getWrapLogMessageStyles = (theme: GrafanaTheme2, wrapLogMessage: boolean) => {\n  if (!wrapLogMessage) {\n    return css`\n      // line wrap\n      --json-tree-value-text-wrap: nowrap;\n    `;\n  }\n\n  return undefined;\n};\n", "import * as React from 'react';\nimport { useMemo } from 'react';\n\nimport { css, cx } from '@emotion/css';\n\nimport { colorManipulator, GrafanaTheme2 } from '@grafana/data';\nimport { PopoverContent, Tooltip, useStyles2 } from '@grafana/ui';\n\n// Dark theme\nimport copyDark from 'img/icons/dark/copy.svg';\nimport copyHoverDark from 'img/icons/dark/copy--hover.svg';\nimport eyeDark from 'img/icons/dark/eye.svg';\nimport eyeHoverDark from 'img/icons/dark/eye--hover.svg';\nimport searchMinusDark from 'img/icons/dark/search-minus.svg';\nimport searchMinusHoverDark from 'img/icons/dark/search-minus--hover.svg';\nimport searchPlusDark from 'img/icons/dark/search-plus.svg';\nimport searchPlusHoverDark from 'img/icons/dark/search-plus--hover.svg';\nimport shareAltDark from 'img/icons/dark/share-alt.svg';\nimport shareAltHoverDark from 'img/icons/dark/share-alt--hover.svg';\nimport eyeActive from 'img/icons/eye--active.svg';\n// Light theme\nimport copyLight from 'img/icons/light/copy.svg';\nimport copyHoverLight from 'img/icons/light/copy--hover.svg';\nimport eyeLight from 'img/icons/light/eye.svg';\nimport eyeHoverLight from 'img/icons/light/eye--hover.svg';\nimport searchMinusLight from 'img/icons/light/search-minus.svg';\nimport searchMinusHoverLight from 'img/icons/light/search-minus--hover.svg';\nimport searchPlusLight from 'img/icons/light/search-plus.svg';\nimport searchPlusHoverLight from 'img/icons/light/search-plus--hover.svg';\nimport shareAltLight from 'img/icons/light/share-alt.svg';\nimport shareAltHoverLight from 'img/icons/light/share-alt--hover.svg';\nimport searchMinusActive from 'img/icons/search-minus--active.svg';\nimport searchPlusActive from 'img/icons/search-plus--active.svg';\n\ntype ThemeVariant = 'dark' | 'light';\ntype IconButtonVariant = 'primary' | 'secondary';\ntype IconName = 'copy' | 'eye' | 'search-minus' | 'search-plus' | 'share-alt';\n\ninterface BaseProps extends Omit<React.ButtonHTMLAttributes<HTMLButtonElement>, 'aria-label'> {\n  /** Name of the icon **/\n  name: IconName;\n  /** Variant to change the color of the Icon */\n  variant?: IconButtonVariant;\n}\n\nexport interface BasePropsWithTooltip extends BaseProps {\n  /** Tooltip content to display on hover and as the aria-label */\n  tooltip: PopoverContent;\n}\n\ntype Images = Record<IconName, Record<ThemeVariant, Record<IconButtonVariant | 'hover', string>>>;\n\nconst images: Images = {\n  eye: {\n    dark: {\n      secondary: eyeDark,\n      primary: eyeActive,\n      hover: eyeHoverDark,\n    },\n    light: {\n      secondary: eyeLight,\n      primary: eyeActive,\n      hover: eyeHoverLight,\n    },\n  },\n  'search-minus': {\n    dark: {\n      secondary: searchMinusDark,\n      primary: searchMinusActive,\n      hover: searchMinusHoverDark,\n    },\n    light: {\n      secondary: searchMinusLight,\n      primary: searchMinusActive,\n      hover: searchMinusHoverLight,\n    },\n  },\n  'search-plus': {\n    dark: {\n      secondary: searchPlusDark,\n      primary: searchPlusActive,\n      hover: searchPlusHoverDark,\n    },\n    light: {\n      secondary: searchPlusLight,\n      primary: searchPlusActive,\n      hover: searchPlusHoverLight,\n    },\n  },\n  'share-alt': {\n    dark: {\n      secondary: shareAltDark,\n      hover: shareAltHoverDark,\n      // Unused\n      primary: '',\n    },\n    light: {\n      secondary: shareAltLight,\n      hover: shareAltHoverLight,\n      // Unused\n      primary: '',\n    },\n  },\n  copy: {\n    dark: {\n      secondary: copyDark,\n      hover: copyHoverDark,\n      // Unused\n      primary: '',\n    },\n    light: {\n      secondary: copyLight,\n      hover: copyHoverLight,\n      // Unused\n      primary: '',\n    },\n  },\n};\n\nconst ImgButton = React.forwardRef<HTMLButtonElement, BasePropsWithTooltip>((props, ref) => {\n  const { variant = 'secondary', name, className, tooltip, ...restProps } = props;\n\n  const styles = useStyles2(getStyles, variant, name, images);\n\n  let ariaLabel: string | undefined;\n  let buttonRef: typeof ref | undefined;\n  ariaLabel = typeof tooltip === 'string' ? tooltip : undefined;\n\n  // When using tooltip, ref is forwarded to Tooltip component instead for https://github.com/grafana/grafana/issues/65632\n  return useMemo(\n    () => (\n      <Tooltip ref={ref} content={tooltip}>\n        <button\n          {...restProps}\n          ref={buttonRef}\n          aria-label={ariaLabel}\n          className={cx(styles.button, className)}\n          type=\"button\"\n        >\n          <span className={styles.img}></span>\n        </button>\n      </Tooltip>\n    ),\n    [ariaLabel, ref, className, restProps, styles, tooltip, buttonRef]\n  );\n});\nImgButton.displayName = 'ImgButton';\nexport default ImgButton;\n\nconst getStyles = (theme: GrafanaTheme2, variant: IconButtonVariant, name: IconName, images: Images) => {\n  let iconColor = theme.colors.text.primary;\n\n  if (variant === 'primary') {\n    iconColor = theme.colors.primary.text;\n  }\n\n  const themeType = theme.isDark ? 'dark' : 'light';\n\n  return {\n    button: css({\n      zIndex: 0,\n      position: 'relative',\n      margin: `0 ${theme.spacing.x0_5} 0 0`,\n      boxShadow: 'none',\n      border: 'none',\n      display: 'inline-flex',\n      background: 'transparent',\n      justifyContent: 'center',\n      alignItems: 'center',\n      padding: 0,\n      color: iconColor,\n\n      '&[disabled], &:disabled': {\n        cursor: 'not-allowed',\n        color: theme.colors.action.disabledText,\n        opacity: 0.65,\n      },\n\n      '&:focus, &:focus-visible': {\n        outline: '2px dotted transparent',\n        outlineOffset: '2px',\n        boxShadow: `0 0 0 2px ${theme.colors.background.canvas}, 0 0 0px 4px ${theme.colors.primary.main}`,\n        transitionTimingFunction: `cubic-bezier(0.19, 1, 0.22, 1)`,\n        transitionDuration: '0.2s',\n        transitionProperty: 'outline, outline-offset, box-shadow',\n      },\n\n      '&:focus:not(:focus-visible)': {\n        outline: 'none',\n        boxShadow: `none`,\n      },\n    }),\n    icon: css({\n      verticalAlign: 'baseline',\n    }),\n    img: css({\n      backgroundImage:\n        variant === 'primary' ? `url(${images[name][themeType].primary})` : `url(${images[name][themeType].secondary})`,\n      width: '16px',\n      height: '16px',\n\n      '&:before': {\n        width: '16px',\n        height: '16px',\n        left: 0,\n        zIndex: -1,\n        position: 'absolute',\n        opacity: 0,\n        borderRadius: theme.shape.radius.default,\n        content: '\"\"',\n        transform: 'scale(1.45)',\n        [theme.transitions.handleMotion('no-preference', 'reduce')]: {\n          transitionDuration: '0.2s',\n          transitionTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)',\n          transitionProperty: 'opacity',\n        },\n      },\n\n      '&:hover': {\n        backgroundImage: `url(${images[name][themeType].hover})`,\n        '&:before': {\n          backgroundColor:\n            variant === 'secondary' ? theme.colors.action.hover : colorManipulator.alpha(iconColor, 0.12),\n          opacity: 1,\n        },\n      },\n    }),\n  };\n};\n"], "names": ["getLineFilterRegExps", "filters", "filter", "search", "operator", "LineFilterOp", "match", "regex", "value", "map", "RegExp", "key", "e", "logger", "info", "f", "getWrappingElement", "className", "jsxValues", "span", "mark", "highlightValueStringMatches", "matchingIntervals", "size", "valueArray", "lineFilterMatchIndex", "matchInterval", "valueIndex", "length", "push", "result", "stringValues", "i", "char", "mergeStringsAndElements", "getMatchingIntervals", "matchExpressions", "results", "for<PERSON>ach", "valueMatch", "valueMatches", "exec", "source", "fromToArray", "vm", "index", "mergeOverlapping", "matchIndices", "arr", "sort", "a", "b", "resIdx", "Math", "max", "mergeOverlap", "getLogsHighlightStyles", "theme", "colors", "critical", "debug", "error", "text", "metadata", "primary", "parsedField", "trace", "warning", "color", "success", "fontWeight", "typography", "fontWeightMedium", "opacity", "fontWeightBold", "shade", "JSONHighlightLineFilterMatches", "lineFilters", "lineFilterMatches", "JSONLabelText", "keyPathString", "styles", "useStyles2", "getJSONLabelWrapStyles", "strong", "JSONLabelWrapStyles", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "keyP<PERSON>", "filterType", "logsJsonScene", "variableType", "addCurrentUrlToHistory", "replace", "LABEL_NAME_INVALID_CHARS", "addJsonParserFieldValue", "logsListScene", "sceneGraph", "getAncestor", "LogsListScene", "addToFilters", "reportAppInteraction", "USER_EVENTS_PAGES", "service_details", "USER_EVENTS_ACTIONS", "add_to_filters_in_json_panel", "action", "ImgButton", "lazy", "JSONNestedNodeFilterButton", "active", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "getJSONFilterButtonStyles", "useMemo", "button", "tooltip", "onClick", "stopPropagation", "EMPTY_VARIABLE_VALUE", "VAR_FIELDS", "aria-selected", "variant", "name", "aria-label", "isActive", "css", "undefined", "secondary", "max<PERSON><PERSON><PERSON><PERSON>", "JSONFieldValueButton", "memo", "existingFilter", "<PERSON><PERSON><PERSON>", "label", "model", "FilterOp", "Equal", "NotEqual", "selected", "displayName", "JSONMetadataButton", "sceneRef", "addJSONMetadataFilter", "JSONLeafNodeLabelButtons", "JSONMetadataButtons", "toString", "find", "isOperatorInclusive", "isOperatorExclusive", "JSONRootNodeNavigation", "getLineFormatVariable", "state", "root<PERSON>ey<PERSON>ath", "JSONDataFrameLineName", "JSONVizRootName", "drillUpWrapperStyle", "<PERSON><PERSON>", "setNewRootNode", "fill", "disabled", "Icon", "breadCrumbDelimiter", "addDrillUp", "itemStringDelimiter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sceneObject", "fullPathFilters", "isLogLineField", "reverse", "nodeKey", "LineFormatFilterOp", "Empty", "EMPTY_AD_HOC_FILTER_VALUE", "slice", "setState", "lineFormatEvent", "removeLineFormatFilters", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "change_line_format_in_json_panel", "lineFormatVariable", "JSONVar", "getJSONFieldsVariable", "fieldsVar", "getFieldsVariable", "lineFormatFilters", "keyIndex", "findIndex", "lineFormatFiltersToKeep", "_", "JSONParserKeys", "join", "JSONParserKeysToRemove", "fieldsFilterSet", "Set", "fieldFilter", "add", "JSONParserFilters", "includes", "has", "isTimeLabelNode", "JSONDataFrameTimeName", "hasFieldParentNode", "JSONDataFrameStructuredMetadataName", "JSONDataFrameLabelsName", "JSONLeafLabel", "lineField", "JSONParserPropsMap", "JSONFiltersSupported", "JSONLogsScene", "getValue", "keys", "accessors", "pop", "getJSONVizNestedProperty", "values", "existingVariableType", "LEVEL_VARIABLE_VALUE", "VAR_LEVELS", "VAR_METADATA", "VAR_LABELS", "getFilterVariableTypeFromPath", "highlightedValue", "hasHighlight", "getAdHocFiltersVariable", "labelButtonsWrap", "getKeyPathString", "getJSONKey", "JSONParserProp", "get", "existingJSONFilter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "elements", "ReRootJSONButton", "NestedNodeFilterButtonsComponent", "fieldsFilters", "JSONNestedLabelWrapStyles", "JSONParentNodeFilterButtons", "LabelRenderer", "nodeType", "style", "nodeTypeLoc", "JSONStructuredMetadataDisplayName", "JSONLabelsDisplayName", "JSONDataFrameLinksName", "JSONLinksDisplayName", "isJSONLeafNode", "isJSONParentNode", "isTimestampNode", "isNumber", "time", "JSONLabelWrapStylesPrimary", "getStyles", "payload", "decodedPayload", "JSON", "parse", "msg", "decodedPayloadNarrowed", "narrowJsonDerivedFieldLinkPayload", "LinkButton", "icon", "href", "target", "logsSyntaxMatches", "<PERSON><PERSON><PERSON><PERSON>", "valueAsString", "JSONLinkNodeButton", "highlightedResults", "Object", "some", "JSONHighlightRegexMatches", "LineItemType", "detectedLevel", "levelsVarFilters", "levelClass", "logsLabelLevelsMatches", "<PERSON><PERSON><PERSON>", "content", "t", "levelButtonStyles", "toUpperCase", "JSONLineItemType", "height", "JSON_VIZ_LINE_HEIGHT", "marginLeft", "fontFamily", "fontFamilyMonospace", "appearance", "background", "border", "fontSize", "padding", "spacing", "elevated", "SHOW_SUCCESS_DURATION", "COPY_TO_CLIPBOARD_TEXT", "COPY_LINK_TO_LINE_TEXT", "COPY_LINK_ERROR_TEXT", "COPY_SUCCESS", "CopyToClipboardButton", "defaultText", "copied", "setCopied", "React", "copiedText", "setCopiedText", "buttonRef", "useRef", "useEffect", "timeoutId", "setTimeout", "window", "clearTimeout", "InlineToast", "placement", "referenceElement", "current", "aria-pressed", "ref", "tabIndex", "JSONLogLineActionButtons", "timeRange", "getTimeRange", "copyLogLine", "getData", "rawFrame", "idField", "fields", "isLogsIdField", "logLineIndex", "Error", "logId", "logLineLink", "generateLogShortlink", "id", "row", "copyText", "getLinkToLog", "$data", "dataFrame", "getLogsPanelFrame", "data", "line", "ItemString", "itemString", "itemType", "levelsVar", "hasProp", "Time", "rootNodeItemString", "getJsonDetectedLevel", "wrapper", "labelsField", "FieldType", "other", "isLabelsField", "labels", "emphasize", "display", "alignItems", "document", "createElement", "append<PERSON><PERSON><PERSON>", "createTextNode", "head", "r", "console", "x", "arrow", "arrowExpanded", "arrowInner", "P", "arrowStyle", "expanded", "n", "role", "onKeyDown", "preventDefault", "arrowArrowStyleSingle", "arrowArrowStyleDouble", "children", "Z", "s", "d", "getOwnPropertyNames", "entries", "l", "N", "set", "Y", "o", "Array", "isArray", "hasMore", "L", "from", "to", "min", "ee", "bind", "X", "u", "R", "itemRange", "ne", "renderChildNodes", "scrollToPath", "C", "nestedNode", "nestedNodeExpandable", "nestedNodeExpanded", "nestedNodeItemType", "nestedNodeItemTypeExpanded", "nestedNodeLabelWrap", "nestedNodeItemString", "nested<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nested<PERSON><PERSON><PERSON><PERSON><PERSON>", "nestedNodeLabelExpandable", "nestedNodeLabelExpanded", "rootNode", "rootNodeExpandable", "rootNodeChildren", "ye", "W", "expandable", "scrollIntoView", "behavior", "V", "collectionLimit", "circularCache", "postprocessValue", "sortObjectKeys", "ge", "p", "m", "E", "z", "isCircular", "hideRoot", "w", "createItemString", "getItemString", "hideRootExpand", "labelRenderer", "level", "nodeTypeIndicator", "shouldExpandNodeInitially", "O", "G", "T", "y", "q", "j", "v", "B", "F", "rootNodeExpanded", "me", "xe", "Ie", "Se", "$e", "Number", "isSafeInteger", "Ee", "I", "valueNode", "valueNodeValue", "valueNodeLabel", "valueNodeScrolled", "h", "valueR<PERSON><PERSON>", "valueGetter", "isCustomNode", "valueWrap", "prototype", "call", "Symbol", "iterator", "constructor", "K", "toISOString", "$", "tree", "defaultItemString", "defaultLabelWrap", "defaultLabelWrapExpandable", "A", "Re", "We", "Ae", "Me", "Ve", "LogsJSONComponent", "emptyScene", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "menu", "<PERSON><PERSON><PERSON><PERSON>", "hasMetadata", "sortOrder", "wrapLogMessage", "useState", "visualizationType", "selectedLine", "JSONVariable", "getLevelsVariable", "field", "string", "Map", "lineFilterVar", "getLineFiltersVariable", "lineIndex", "cleanLineIndex", "fullKeyFromJSONParserProps", "substring", "split", "scrollRef", "onScrollToBottomClick", "useCallback", "scrollTo", "scrollHeight", "onScrollToTopClick", "onToggleStructuredMetadataClick", "visible", "setJSONMetadataVisibility", "onToggleLabelsClick", "setJSONLabelsVisibility", "onToggleHighlightClick", "setJSONHighlightVisibility", "onWrapLogMessageClick", "wrap", "setLogOption", "showNoJSONDetected", "showLokiNotSupported", "div", "panelChromeWrap", "PanelChrome", "showMenuAlways", "statusMessage", "errors", "message", "loadingState", "title", "Component", "actions", "LogsPanelHeaderActions", "vizType", "onChange", "setVisualizationType", "container", "LogListControls", "showHighlight", "showMetadata", "showLabels", "onSortOrderChange", "handleSortChange", "JSONTreeWrap", "<PERSON><PERSON>", "alert", "severity", "JSONTree", "__", "NoMatchingLabelsScene", "hoverBg", "isDark", "colorManipulator", "alpha", "lighten", "canvas", "darken", "selectedBg", "transparent", "marginTop", "marginBottom", "contain", "width", "flexDirection", "paddingBottom", "paddingRight", "highlight", "backgroundColor", "contrastText", "matchMedia", "matches", "getWrapLogMessageStyles", "shadows", "z1", "images", "eye", "dark", "eyeDark", "eyeActive", "hover", "eyeHoverDark", "light", "eyeLight", "eyeHoverLight", "searchMinusDark", "searchMinusActive", "searchMinusHoverDark", "searchMinusLight", "searchMinusHoverLight", "searchPlusDark", "searchPlusActive", "searchPlusHoverDark", "searchPlusLight", "searchPlusHoverLight", "shareAltDark", "shareAltHoverDark", "shareAltLight", "shareAltHoverLight", "copy", "copyDark", "copyHoverDark", "copyLight", "copyHoverLight", "props", "restProps", "aria<PERSON><PERSON><PERSON>", "cx", "img", "iconColor", "themeType", "zIndex", "position", "margin", "x0_5", "boxShadow", "justifyContent", "cursor", "disabledText", "outline", "outlineOffset", "main", "transitionTimingFunction", "transitionDuration", "transitionProperty", "verticalAlign", "backgroundImage", "left", "borderRadius", "shape", "radius", "default", "transform", "transitions", "handleMotion"], "sourceRoot": ""}