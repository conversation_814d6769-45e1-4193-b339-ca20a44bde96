"use strict";(self.webpackChunkgrafana_lokiexplore_app=self.webpackChunkgrafana_lokiexplore_app||[]).push([[7],{7905:(e,t,r)=>{r.r(t),r.d(t,{JSON_VIZ_LINE_HEIGHT:()=>mt,default:()=>ft});var n=r(5959),a=r.n(n),o=r(6089),l=r(7781),s=r(6865),i=r(2007),c=r(6779),d=r(1111),u=r(3241),p=r(6854),g=r(5953);const m=e=>e.filter(e=>(e.operator===p.cK.match||e.operator===p.cK.regex)&&e.value).map(e=>{try{return new RegExp(e.value,"caseSensitive"===e.key?"g":"gi")}catch(t){return void g.v.info("Error executing match expression",{regex:e.value})}}).filter(e=>e),f=(e,t)=>e?a().createElement("span",{className:e},t):a().createElement("mark",null,t),y=(e,t,r,n)=>{let a=[],o=0,l=e[o];for(let n=0;n<t.length;n++){for(;n>=l[1]&&o<r-1;)o++,l=e[o];n>=l[0]&&n<l[1]?a.push({value:t[n]}):a.push(t[n])}return((e,t)=>{let r=[],n="",a="";for(let o=0;o<e.length;o++){const l=e[o];"string"==typeof l?(n&&(r.push(f(t,n)),n=""),a+=l):(a&&(r.push(a),a=""),n+=l.value)}return a&&r.push(a),n&&r.push(f(t,n)),r})(a,n)},b=(e,t)=>{let r=[];return e.forEach(e=>{let n,a=[];do{try{n=null==e?void 0:e.exec(t),n&&(n[0]?a.push(n):n=null)}catch(t){var o;g.v.info("Error executing match expression",{regex:null!==(o=null==e?void 0:e.source)&&void 0!==o?o:""}),n=null}}while(n);if(a.length){const e=a.map(e=>[e.index,e.index+e[0].length]);r.push(...e)}}),r};const h=e=>e.length?function(e){e.sort((e,t)=>e[0]-t[0]);let t=0;for(let r=1;r<e.length;r++)e[t][1]>=e[r][0]?e[t][1]=Math.max(e[t][1],e[r][1]):(t++,e[t]=e[r]);return t+1}(e):0,v=e=>{const t={critical:"#B877D9",debug:"#6E9FFF",error:e.colors.error.text,info:"#6CCF8E",metadata:e.colors.text.primary,parsedField:e.colors.text.primary,trace:"#6ed0e0",warning:e.colors.warning.text};return{".log-token-critical":{color:t.critical},".log-token-debug":{color:t.debug},".log-token-duration":{color:e.colors.success.text},".log-token-error":{color:t.error},".log-token-info":{color:t.info},".log-token-json-key":{color:t.parsedField,fontWeight:e.typography.fontWeightMedium,opacity:.9},".log-token-key":{color:t.parsedField,fontWeight:e.typography.fontWeightMedium,opacity:.9},".log-token-label":{color:t.metadata,fontWeight:e.typography.fontWeightBold},".log-token-method":{color:e.colors.info.shade},".log-token-size":{color:e.colors.success.text},".log-token-trace":{color:t.trace},".log-token-uuid":{color:e.colors.success.text},".log-token-warning":{color:t.warning}}};function j(e,t,r){const n=m(e),a=b(n,t),o=h(a);let l=[];return a.length&&(l=y(a,t,o,r)),l}var x=r(4586);function k({text:e,keyPathString:t}){const r=(0,i.useStyles2)(x.nu);return a().createElement("strong",{className:r.JSONLabelWrapStyles},e.length?e:t,":")}var N=r(9405),_=r(5607),S=r(4509),w=r(8502),E=r(42),O=r(7478);const P=({key:e,keyPath:t,value:r,filterType:n,logsJsonScene:a,variableType:o})=>{(0,O.bN)(),e=e.replace(E.HO,"_"),(0,w.gR)(a,t);const l=s.jh.getAncestor(a,_.i);(0,N.Qt)(e,r,n,l,o,!1,!0),(0,S.EE)(S.NO.service_details,S.ir.service_details.add_to_filters_in_json_panel,{action:n,filterType:"json",key:e})};var T=r(20);const $=(0,n.lazy)(()=>Promise.resolve().then(r.bind(r,4398)));function F({active:e,fullKeyPath:t,keyPath:r,type:o,logsJsonScene:l}){const s=(0,i.useStyles2)(C,e);return(0,n.useMemo)(()=>a().createElement($,{className:s.button,tooltip:`${"include"===o?"Include":"Exclude"} log lines that contain ${r[0]}`,onClick:n=>{n.stopPropagation(),P({value:T.ZO,key:t,variableType:T.mB,logsJsonScene:l,keyPath:r,filterType:e?"toggle":"include"===o?"exclude":"include"})},"aria-selected":e,variant:e?"primary":"secondary",name:"include"===o?"search-plus":"search-minus","aria-label":`${o} filter`}),[e,r,t,l,s.button,o])}const C=(e,t)=>({button:(0,o.css)({color:t?void 0:e.colors.text.secondary,"&:hover":{color:e.colors.text.maxContrast}})}),I=(0,n.lazy)(()=>Promise.resolve().then(r.bind(r,4398))),L=(0,n.memo)(({existingFilter:e,fullKey:t,keyPath:r,label:o,type:l,value:s,model:c})=>{const d="include"===l?p.w7.Equal:p.w7.NotEqual,u=(null==e?void 0:e.operator)===d,g=(0,i.useStyles2)(C,u),m=(null==e?void 0:e.operator)===d;return(0,n.useMemo)(()=>a().createElement(I,{className:g.button,tooltip:`${"include"===l?"Include":"Exclude"} log lines containing ${o}="${s}"`,onClick:e=>{e.stopPropagation(),P({keyPath:r,key:t,value:s,filterType:m?"toggle":l,logsJsonScene:c,variableType:T.mB})},"aria-selected":u,variant:u?"primary":"secondary",name:"include"===l?"search-plus":"search-minus","aria-label":`${l} filter`}),[u,m,l,g.button,r,t,s,o,c])});L.displayName="JSONFilterValueButton";const R=(0,n.memo)(({existingFilter:e,label:t,type:r,value:o,variableType:l,sceneRef:s})=>{const c="include"===r?p.w7.Equal:p.w7.NotEqual,d=(null==e?void 0:e.operator)===c,u=(0,i.useStyles2)(C,d),g=(null==e?void 0:e.operator)===c;return(0,n.useMemo)(()=>a().createElement(I,{className:u.button,tooltip:`${"include"===r?"Include":"Exclude"} log lines containing ${t}="${o}"`,onClick:e=>{e.stopPropagation(),(({label:e,value:t,filterType:r,variableType:n,sceneRef:a})=>{(0,O.bN)(),(0,N.Qt)(e,t,r,a,n,!1),(0,S.EE)(S.NO.service_details,S.ir.service_details.add_to_filters_in_json_panel,{action:r,filterType:r,label:e})})({label:t,value:o,filterType:g?"toggle":r,variableType:l,sceneRef:s})},"aria-selected":g,variant:g?"primary":"secondary",name:"include"===r?"search-plus":"search-minus","aria-label":`${r} filter`}),[g,t,s,u.button,r,o,l])});function M({label:e,value:t,fullKeyPath:r,fullKey:n,existingFilter:o,model:l}){return a().createElement(a().Fragment,null,a().createElement(L,{label:e,value:t,keyPath:r,fullKey:n,existingFilter:o,type:"include",model:l}),a().createElement(L,{label:e,value:t,keyPath:r,fullKey:n,existingFilter:o,type:"exclude",model:l}))}R.displayName="JSONMetadataButton";var J=r(708);function W({existingFilter:e,label:t,sceneRef:r,value:n,variableType:o}){return a().createElement(a().Fragment,null,a().createElement(R,{type:"include",label:t.toString(),value:n,variableType:o,existingFilter:e.find(e=>(0,J.BG)(e.operator)),sceneRef:r}),a().createElement(R,{type:"exclude",label:t.toString(),value:n,variableType:o,existingFilter:e.find(e=>(0,J.Lw)(e.operator)),sceneRef:r}))}var A=r(376),D=r(5553);function z(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function B(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}function K({sceneRef:e}){const t=(0,D.U2)(e).state.filters,r=[d.Z,0,d.FN];return a().createElement(a().Fragment,null,a().createElement("span",{className:x.tZ,key:d.FN},a().createElement(i.Button,{size:"sm",onClick:()=>G(r,e),variant:"secondary",fill:"outline",disabled:!t.length,name:d.FN},d.FN),t.length>0&&a().createElement(i.Icon,{className:x.N0,name:"angle-right"})),t.map((r,n)=>{const o=r.key===t[t.length-1].key;return a().createElement("span",{className:x.tZ,key:r.key},a().createElement(i.Button,{size:"sm",disabled:o,onClick:()=>q(r.key,e),variant:"secondary",fill:"outline"},r.key),n<t.length-1&&a().createElement(i.Icon,{className:x.N0,name:"angle-right"}),n===t.length-1&&a().createElement(i.Icon,{className:x.K4,name:"angle-right"}))}))}function V(e,t){const r=[...(0,D.U2)(t).state.filters,...e.filter(e=>"string"==typeof e&&!(0,A.Z6)(e)&&e!==d.FN).reverse().map(e=>({key:e.toString(),operator:p.h8.Empty,value:w.uE}))];return{fullKeyPath:[...r.map(e=>e.key).reverse(),...e.slice(-3)],fullPathFilters:r}}const G=(e,t)=>{(0,O.bN)();const{fullKeyPath:r,fullPathFilters:n}=V(e,t);if(e.length>3){(0,w.gR)(t,r);(0,D.U2)(t).setState({filters:n.map(e=>B(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){z(e,t,r[t])})}return e}({},e),{key:e.key.replace(E.HO,"_")}))}),H("add",e[0].toString())}else(0,w.X)(t),(0,A.Ak)(t),H("remove",d.FN)},H=(e,t)=>{(0,S.EE)(S.NO.service_details,S.ir.service_details.change_line_format_in_json_panel,{key:t,type:e})},q=(e,t)=>{(0,O.bN)();const r=(0,D.U2)(t),n=(0,D.Gc)(t),a=(0,D.ir)(t),o=r.state.filters,l=o.findIndex(t=>t.key===e),s=o.filter((e,t)=>t<=l),i=[];for(let e=0;e<o.length;e++)i.push(`${i.length?`${o.map(e=>e.key).slice(0,e).join("_")}_`:""}${o[e].key}`);const c=i.slice(l+1),d=new Set;a.state.filters.forEach(e=>d.add(e.key));const u=n.state.filters.filter(e=>!c.includes(e.key)||d.has(e.key));n.setState({filters:u}),r.setState({filters:s}),H("remove",e)},Z=e=>e[0]===d.EK,U=e=>e[1]===d.jf||e[1]===d.iU;function Y({keyPath:e,lineField:t,fieldsVar:r,JSONParserPropsMap:n,lineFilters:o,JSONFiltersSupported:l,JSONLogsScene:s}){var c;const u=null===(c=function(e,t){const r=[...e],n=[];for(;r.length;){const e=r.pop();e!==d.FN&&void 0!==e&&n.push(e)}return(0,x.IR)(t,n)}(e,t.values))||void 0===c?void 0:c.toString(),p=e[0],g=function(e){return e[1]===d.jf?e[0]===T.e4?T._Y:T._P:e[1]===d.iU?T.MB:T.mB}(e),m=(0,i.useStyles2)(x.nu);let f=[];if(s.state.hasHighlight&&!U(e)&&(f=j(o,e[0].toString())),U(e)){const t=(0,D.bY)(g,s).state.filters.filter(e=>e.key===p.toString()&&e.value===u);return a().createElement("span",{className:m.labelButtonsWrap},a().createElement(W,{sceneRef:s,label:p,value:u,variableType:g,existingFilter:t}),a().createElement(k,{text:f,keyPathString:(0,d.y)(e,"")}))}const{fullKeyPath:y}=V(e,s),b=(0,w.Oc)(y),h=n.get(b),v=h&&r.state.filters.find(e=>e.key===(null==h?void 0:h.key)&&(0,D.bu)(e).value===u);return a().createElement("span",{className:m.labelButtonsWrap},l&&a().createElement(M,{JSONFiltersSupported:l,label:p,value:u,fullKeyPath:y,fullKey:b,existingFilter:v,elements:f,keyPathString:(0,d.y)(e,""),model:s}),a().createElement(k,{text:f,keyPathString:(0,d.y)(e,"")}))}const Q=(0,n.lazy)(()=>Promise.resolve().then(r.bind(r,4398))),X=(0,n.memo)(({keyPath:e,sceneRef:t})=>{const r=(0,i.useStyles2)(C,!1);return(0,n.useMemo)(()=>a().createElement(Q,{className:r.button,tooltip:`Set ${e[0]} as root node`,onClick:r=>{r.stopPropagation(),G(e,t)},name:"eye","aria-label":`drilldown into ${e[0]}`}),[e,t,r.button])});X.displayName="DrilldownButton";const ee=X;function te({keyPath:e,fieldsFilters:t,JSONParserPropsMap:r,lineFilters:n,JSONFiltersSupported:o,JSONLogsScene:l}){const{fullKeyPath:s}=V(e,l),c=(0,w.Oc)(s),u=(0,i.useStyles2)(x.nu),p=r.get(c),g=p&&t.find(e=>e.key===(null==p?void 0:p.key)&&(0,D.bu)(e).value===T.ZO);let m=[];return l.state.hasHighlight&&(m=j(n,e[0].toString())),a().createElement("span",{className:u.JSONNestedLabelWrapStyles},o&&a().createElement(a().Fragment,null,a().createElement(ee,{keyPath:e,sceneRef:l}),a().createElement(F,{type:"include",fullKeyPath:c,keyPath:s,active:!!g&&(0,J.Lw)(g.operator),logsJsonScene:l}),a().createElement(F,{type:"exclude",fullKeyPath:c,keyPath:s,active:!!g&&(0,J.BG)(g.operator),logsJsonScene:l})),a().createElement("strong",{className:u.JSONLabelWrapStyles},m.length?m:(0,d.y)(e,""),":"))}const re=(0,n.memo)(te);function ne({fieldsVar:e,JSONFiltersSupported:t,JSONParserPropsMap:r,keyPath:n,lineField:o,lineFilters:l,model:s,nodeType:c}){const p=(0,i.useStyles2)(x.nu),g=n[0].toString(),m=c;if(n[0]===d.jf)return a().createElement("strong",{className:p.JSONLabelWrapStyles},d.eJ);if(n[0]===d.iU)return a().createElement("strong",{className:p.JSONLabelWrapStyles},d.fN);if(n[0]===d.mF)return a().createElement("strong",{className:p.JSONLabelWrapStyles},d.vP);if(n[1]===d.mF)return a().createElement("strong",{className:p.JSONLabelWrapStyles},g,":");if(n[0]===d.FN)return a().createElement(K,{sceneRef:s});if(ae(m,n))return a().createElement(Y,{JSONLogsScene:s,keyPath:n,lineField:o,fieldsVar:e,JSONParserPropsMap:r,lineFilters:l,JSONFiltersSupported:t});if(oe(m,n))return a().createElement(re,{keyPath:n,lineFilters:l,JSONLogsScene:s,fieldsFilters:e.state.filters,JSONParserPropsMap:r,JSONFiltersSupported:t});if(le(n)&&(0,u.isNumber)(n[0])){var f;const e=null===(f=o.values[n[0]])||void 0===f?void 0:f[d.EK];return a().createElement("strong",{className:x.VM},e)}return Z(n)?null:a().createElement("strong",{className:p.JSONLabelWrapStyles},g,":")}const ae=(e,t)=>"Object"!==e&&"Array"!==e&&t[0]!==d.EK&&!(0,A.Z6)(t[0].toString())&&t[0]!==d.FN&&!(0,u.isNumber)(t[0]),oe=(e,t)=>!("Object"!==e&&"Array"!==e||(0,A.Z6)(t[0].toString())||t[0]===d.FN||(0,u.isNumber)(t[0])),le=e=>e[1]===d.FN;var se=r(8428);const ie=e=>({button:(0,o.css)({"&:hover":{color:e.colors.primary.text}})}),ce=function({payload:e}){const t=(0,i.useStyles2)(ie);let r;try{r=JSON.parse(e)}catch(e){g.v.error(e,{msg:"Unable to parse JsonLinkButton payload!"})}const n=(0,se.Hy)(r);return n?a().createElement(i.LinkButton,{className:t.button,icon:"external-link-alt",variant:"secondary",size:"sm",fill:"text",href:n.href,target:"_blank"},n.name):null},de={"log-token-critical":/(\b)(CRITICAL|CRIT)($|\s)/gi,"log-token-error":/(\b)(ERROR|ERR)($|\s)/gi,"log-token-warning":/(\b|\B)(WARNING|WARN)($|\s)/gi,"log-token-debug":/(\b)(DEBUG)($|\s)/gi,"log-token-info":/(\b|\B)(INFO)($|\s)/gi,"log-token-trace":/(\b)(TRACE)($|\s)/gi,"log-token-uuid":/(\b|\B)[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[1-5][0-9a-fA-F]{3}-[89abAB][0-9a-fA-F]{3}-[0-9a-fA-F]{12}/g,"log-token-method":/\b(GET|POST|PUT|DELETE|PATCH|HEAD|OPTIONS|TRACE|CONNECT)\b/gi,"log-token-key":/(\b|\B)[\w_]+(?=\s*=)/gi,"log-token-size":/(?:\b|")\d+\.{0,1}\d*\s*[kKmMGgtTPp]*[bB]{1}(?:"|\b)/g,"log-token-duration":/\b\d+(\.\d+)?(ns|µs|ms|s|m|h|d)\b/g};function ue({keyPath:e,lineFilters:t,valueAsString:r,model:n}){if(Z(e))return null;const o=null==r?void 0:r.toString();if(!o)return null;if(e[1]===d.mF)return a().createElement(ce,{payload:o});if(n.state.hasHighlight){if(!U(e)){let e=j(t,o);if(e.length)return e}let r=[];if(Object.keys(de).some(e=>!!o.match(de[e])&&(r=function(e,t,r){const n=b(e,t),a=h(n);let o=[];return n.length&&(o=y(n,t,a,r)),o}([de[e]],o,e),!0)),r.length)return r}return o}var pe=r(6189),ge=r(9721),me=r(9814),fe=r(4907);function ye({detectedLevel:e,sceneRef:t,levelsVarFilters:r}){const o=(0,i.useStyles2)(he),l=Object.keys(fe.wx).find(t=>e.match(fe.wx[t])),s=r.some(t=>t.value===e&&t.operator===p.w7.Equal);return(0,n.useMemo)(()=>a().createElement(i.Tooltip,{content:(0,me.t)("logs.json.line.detectedLevel.toggleButton",s?`Remove ${e} filter`:`Include logs with ${e} level`)},a().createElement("button",{onClick:r=>{r.stopPropagation(),(0,N.Qt)(T.e4,e,"toggle",t,T._Y)},className:`${l} ${o.levelButtonStyles}`},e.toUpperCase())),[s,e,l,t,o.levelButtonStyles])}const be=(0,n.memo)(ye),he=e=>({levelButtonStyles:(0,o.css)({height:mt,marginLeft:"12px",fontFamily:e.typography.fontFamilyMonospace,appearance:"none",background:"none",border:"none",fontSize:"0.9em",padding:e.spacing(0,.5,0,.5),"&:hover, &:focus":{background:e.colors.background.elevated}})});var ve=r(5002),je=r(4398);const xe=2e3,ke=(0,me.t)("logs.log-line-details.copy-to-clipboard","Copy to clipboard"),Ne=(0,me.t)("logs.log-line-menu.copy-link","Copy link to log line"),_e=(0,me.t)("logs.log-line-details.copy-to-clipboard-error","Error copying link!"),Se=(0,me.t)("clipboard-button.inline-toast.success","Copied");function we({onClick:e,stopPropagation:t=!0,type:r="copy"}){const o="copy"===r?ke:Ne,[l,s]=a().useState(!1),[c,d]=a().useState(Se),u=(0,n.useRef)(null),p=(0,i.useStyles2)(Ee);return(0,n.useEffect)(()=>{let e;return l&&(e=setTimeout(()=>{s(!1)},xe)),()=>{window.clearTimeout(e)}},[l]),a().createElement(a().Fragment,null,l&&a().createElement(i.InlineToast,{placement:"top",referenceElement:u.current},c),a().createElement(je.default,{className:p,"aria-pressed":l,tooltip:l?"":o,name:r,ref:u,onClick:r=>{t&&r.stopPropagation();try{e(),d(Se)}catch(r){d(_e)}s(!0)},tabIndex:0}))}const Ee=e=>(0,o.css)({color:e.colors.text.secondary});function Oe({model:e,keyPath:t}){const r=s.jh.getTimeRange(e).state.value;return a().createElement(a().Fragment,null,a().createElement(we,{onClick:()=>Pe(t,s.jh.getData(e))}),a().createElement(we,{type:"share-alt",onClick:()=>function(e,t,r){const n=null==r?void 0:r.fields.find(e=>(0,A.vF)(e.name)),a=e[0];if(!(0,u.isNumber)(a)){const e=Error("Invalid line index");throw g.v.error(e,{msg:"Error getting log line index"}),e}const o=null==n?void 0:n.values[a],l=(0,ve.gW)("selectedLine",{id:o,row:a},t);(0,ve.Dk)(l)}(t,r,e.state.rawFrame)}))}const Pe=(e,t)=>{const r=e[0],n=(0,ge.tn)(t.state.data),a=null==n?void 0:n.fields.find(e=>(0,A.Z6)(e.name));if((0,u.isNumber)(r)&&a){const e=a.values[r];(0,ve.Dk)(e.toString())}};function Te({data:e,itemString:t,itemType:r,keyPath:n,model:o,levelsVar:l}){const s=(0,i.useStyles2)(Fe);if(e&&(0,se.cK)(e,d.EK)&&"string"==typeof e.Time)return a().createElement(Oe,{keyPath:n,model:o});if(n[0]===d.FN)return a().createElement("span",{className:x.Rl},r," ",t);if(n[0]===d.Z){const e=$e(o,n);if(e)return a().createElement(be,{sceneRef:o,detectedLevel:e,levelsVarFilters:l.state.filters})}return n[0]===d.mF?a().createElement("span",{className:s.wrapper},a().createElement(i.Icon,{size:"sm",name:"link"})):a().createElement("span",{className:s.wrapper},r)}const $e=(e,t)=>{var r;const n=null===(r=e.state.rawFrame)||void 0===r?void 0:r.fields.find(e=>e.type===l.FieldType.other&&(0,A.gE)(e.name)),a="number"==typeof t[1]?t[1]:void 0,o=void 0!==a?null==n?void 0:n.values[a]:void 0;return null==o?void 0:o[T.e4]},Fe=e=>({wrapper:(0,o.css)({color:e.colors.emphasize(e.colors.text.secondary,.33),display:"inline-flex",alignItems:"center",height:"22px"})});var Ce=r(2540);!function(){try{if(typeof document<"u"){var e=document.createElement("style");e.appendChild(document.createTextNode("._arrowContainer_xqmcg_1{display:var(--json-tree-inline-flex);margin-right:var(--json-tree-arrow-container-margin-right)}._arrow_xqmcg_1{transition:var(--json-tree-arrow-transition);transform-origin:var(--json-tree-arrow-transform-origin);display:var(--json-tree-inline-flex);color:var(--json-tree-arrow-color);position:var(--json-tree-arrow-position);left:var(--json-tree-arrow-left-offset);margin-right:var(--json-tree-arrow-right-margin);top:0}._arrow--expanded_xqmcg_16{transform:var(--json-tree-arrow-transform)}._arrowInner_xqmcg_19{position:var(--json-tree-arrow-position);left:calc(var(--json-tree-arrow-width) / 3 * -1)}._itemRange_ed7dq_1{position:relative;display:var(--json-tree-inline)}._nestedNode_j6j98_1{display:var(--json-tree-inline-flex);position:relative;cursor:default}._nestedNode--expandable_j6j98_6{align-items:var(--json-tree-align-items);display:var(--json-tree-block);cursor:pointer}._nestedNode--expanded_j6j98_11{display:var(--json-tree-inline)}._nestedNode--itemType_j6j98_14{margin-right:var(--json-tree-nested-node-item-type-margin)}._nestedNode--itemType--expanded_j6j98_17{display:var(--json-tree-inline)}._nestedNodeLabelWrap_j6j98_21{display:var(--json-tree-block);align-items:var(--json-tree-align-items)}._nestedNodeItemString_j6j98_26{display:var(--json-tree-inline-flex)}._nestedNodeChildren_j6j98_30{display:var(--json-tree-block);flex-direction:var(--json-tree-flex-direction);margin-left:var(--json-tree-children-margin);padding:var(--json-tree-ul-padding)}._nestedNodeLabel_j6j98_21{display:var(--json-tree-inline-flex);align-items:var(--json-tree-align-items);margin-right:var(--json-tree-nested-node-label-margin);cursor:default}._nestedNodeLabel--expandable_j6j98_43{display:var(--json-tree-inline-flex);align-items:var(--json-tree-align-items);cursor:pointer}._nestedNodeLabel--expanded_j6j98_48{display:var(--json-tree-inline-flex)}._rootNode_j6j98_52{display:var(--json-tree-inline-flex);cursor:default}._rootNodeExpandable_j6j98_57{cursor:pointer}._rootNodeChildren_j6j98_61{display:var(--json-tree-block);flex-direction:var(--json-tree-flex-direction);padding:var(--json-tree-ul-root-children-padding)}._nodeListItemScrolled_18ya2_1{scroll-margin:var(--json-tree-scroll-margin)}._valueNode_1b1pr_1{display:var(--json-tree-block);align-items:var(--json-tree-value-node-align-items);cursor:default}._valueNodeValue_1b1pr_7{text-wrap:var(--json-tree-value-text-wrap);word-break:var(--json-tree-value-text-word-break);color:var(--json-tree-label-value-color)}._valueNodeLabel_1b1pr_13{margin-right:var(--json-tree-value-label-margin);display:var(--json-tree-inline-flex)}._valueNodeScrolled_1b1pr_18{scroll-margin:var(--json-tree-scroll-margin)}:root{--json-tree-spacing-sm: .25em;--json-tree-spacing-md: .5em;--json-tree-spacing-lg: 1em;--json-tree-spacing-xl: 1.5em;--json-tree-spacing-xxl: 2em;--json-tree-children-margin: var(--json-tree-spacing-xl);--json-tree-value-label-margin: var(--json-tree-spacing-sm);--json-tree-value-node-margin: var(--json-tree-spacing-xl);--json-tree-nested-node-label-margin: var(--json-tree-spacing-md);--json-tree-nested-node-item-type-margin: var(--json-tree-spacing-sm);--json-tree-default-label-wrap-margin-right: var(--json-tree-spacing-md);--json-tree-arrow-container-margin-right: 0;--json-tree-ul-root-padding: 0 0 0 var(--json-tree-spacing-xxl);--json-tree-ul-root-children-padding: 0;--json-tree-ul-padding: 0;--json-tree-value-text-wrap: wrap;--json-tree-value-text-word-break: break-word;--json-tree-inline: inline-grid;--json-tree-inline-flex: inline-flex;--json-tree-block: flex;--json-tree-align-items: center;--json-tree-value-node-align-items: flex-start;--json-tree-flex-direction: column;--json-tree-arrow-transition: var(--json-tree-transition-timing) linear transform;--json-tree-arrow-transform-origin: 45% 50%;--json-tree-arrow-transform: rotateZ(90deg);--json-tree-arrow-width: 1.5em;--json-tree-arrow-height: 1.5em;--json-tree-arrow-position: absolute;--json-tree-arrow-left-offset: calc(var(--json-tree-arrow-width) * -1);--json-tree-arrow-right-margin: 0;--json-tree-label-color: rgb(102, 217, 239);--json-tree-key-label-color: rgb(71, 131, 0);--json-tree-label-value-color: rgb(249, 38, 114);--json-tree-arrow-color: var(--json-tree-label-color);--json-tree-transition-timing: .15s;--json-tree-scroll-margin: 0}._tree_ze97d_48{padding:var(--json-tree-ul-root-padding)}._defaultItemString_ze97d_52{display:var(--json-tree-inline-flex);color:var(--json-tree-key-label-color)}._defaultLabelWrap_ze97d_57{display:var(--json-tree-inline-flex);margin-right:var(--json-tree-default-label-wrap-margin-right);color:var(--json-tree-label-color);cursor:default}._defaultLabelWrap--expandable_ze97d_63{cursor:pointer}")),document.head.appendChild(e)}}catch(e){console.error("vite-plugin-css-injected-by-js",e)}}();const Ie={arrow:"_arrow_xqmcg_1",arrowExpanded:"_arrow--expanded_xqmcg_16",arrowInner:"_arrowInner_xqmcg_19"};function Le({arrowStyle:e="single",expanded:t,onClick:r}){return(0,Ce.jsxs)("div",{role:"button","aria-expanded":t,tabIndex:0,onClick:r,onKeyDown:e=>{("Enter"===e.key||" "===e.key)&&(e.preventDefault(),r())},className:`${Ie.arrow} ${t?Ie.arrowExpanded:""} ${"single"===e?Ie.arrowArrowStyleSingle:Ie.arrowArrowStyleDouble}`,children:["▶","double"===e&&(0,Ce.jsx)("div",{className:`${Ie.arrowInner}`,children:"▶"})]})}function Re(e,t,r,n=0,a=1/0){let o;if("Object"===e){let e=Object.getOwnPropertyNames(t);r&&e.sort(!0===r?void 0:r),e=e.slice(n,a+1),o={entries:e.map(e=>({key:e,value:t[e]}))}}else if("Array"===e)o={entries:t.slice(n,a+1).map((e,t)=>({key:t+n,value:e}))};else{let e=0;const r=[];let l=!0;const s=function(e){return"function"==typeof e.set}(t);for(const o of t){if(e>a){l=!1;break}n<=e&&(s&&Array.isArray(o)?"string"==typeof o[0]||"number"==typeof o[0]?r.push({key:o[0],value:o[1]}):r.push({key:`[entry ${e}]`,value:{"[key]":o[0],"[value]":o[1]}}):r.push({key:e,value:o})),e++}o={hasMore:!l,entries:r}}return o}function Me(e,t,r){const n=[];for(;t-e>r*r;)r*=r;for(let a=e;a<=t;a+=r)n.push({from:a,to:Math.min(t,a+r-1)});return n}function Je(e,t,r,n,a=0,o=1/0){const l=Re.bind(null,e,t,r);if(!n)return l().entries;const s=o<1/0,i=Math.min(o-a,function(e,t){return"Object"===e?Object.keys(t).length:"Array"===e?t.length:1/0}(e,t));if("Iterable"!==e){if(i<=n||n<7)return l(a,o).entries}else if(i<=n&&!s)return l(a,o).entries;let c;if("Iterable"===e){const{hasMore:e,entries:t}=l(a,a+n-1);c=e?[...t,...Me(a+n,a+2*n-1,n)]:t}else c=s?Me(a,o,n):[...l(0,n-5).entries,...Me(n-4,i-5,n),...l(i-4,i-1).entries];return c}const We={itemRange:"_itemRange_ed7dq_1"};function Ae(e){const{from:t,to:r,renderChildNodes:a,nodeType:o,scrollToPath:l,keyPath:s}=e;let i=!1;if(l){const[e]=l;it(l.slice(-1*s.length),s)&&e>t&&e<=r&&(i=!0)}const[c,d]=(0,n.useState)(i),u=(0,n.useCallback)(()=>{d(!c)},[c]);return c?(0,Ce.jsx)("div",{className:`${We.itemRange}`,children:a(e,t,r)}):(0,Ce.jsxs)("div",{className:`${We.itemRange}`,children:[(0,Ce.jsx)(Le,{nodeType:o,expanded:!1,onClick:u,arrowStyle:"double"}),`${t} ... ${r}`]})}const De={nestedNode:"_nestedNode_j6j98_1",nestedNodeExpandable:"_nestedNode--expandable_j6j98_6",nestedNodeExpanded:"_nestedNode--expanded_j6j98_11",nestedNodeItemType:"_nestedNode--itemType_j6j98_14",nestedNodeItemTypeExpanded:"_nestedNode--itemType--expanded_j6j98_17",nestedNodeLabelWrap:"_nestedNodeLabelWrap_j6j98_21",nestedNodeItemString:"_nestedNodeItemString_j6j98_26",nestedNodeChildren:"_nestedNodeChildren_j6j98_30",nestedNodeLabel:"_nestedNodeLabel_j6j98_21",nestedNodeLabelExpandable:"_nestedNodeLabel--expandable_j6j98_43",nestedNodeLabelExpanded:"_nestedNodeLabel--expanded_j6j98_48",rootNode:"_rootNode_j6j98_52",rootNodeExpandable:"_rootNodeExpandable_j6j98_57",rootNodeChildren:"_rootNodeChildren_j6j98_61"},ze="_nodeListItemScrolled_18ya2_1",Be=({children:e,expanded:t,expandable:r,nodeType:a,keyPath:o,className:l,scrollToPath:s})=>{var i,c;const d=n.useRef(null),u=void 0!==s&&it(s,o);n.useEffect(()=>{d.current&&d.current.scrollIntoView({behavior:"auto"})},[]);const p=u?{ref:d,"data-scrolled":"true"}:{};return r?(0,Ce.jsx)("li",{role:"treeitem","aria-expanded":t,"data-nodetype":a,"data-keypath":o[0],"aria-label":null==(i=o[0])?void 0:i.toString(),className:`${l} ${u?ze:""}`,...p,children:e}):(0,Ce.jsx)("li",{role:"treeitem","data-nodetype":a,"data-keypath":o[0],"aria-label":null==(c=o[0])?void 0:c.toString(),className:l,...p,children:e})};function Ke(e,t,r){const{nodeType:a,data:o,collectionLimit:l,circularCache:s,keyPath:i,postprocessValue:c,sortObjectKeys:d,scrollToPath:u}=e,p=[];return Je(a,o,d,l,t,r).forEach(t=>{if(function(e){return void 0!==e.to}(t))p.push((0,n.createElement)(Ae,{...e,key:`ItemRange--${t.from}-${t.to}`,from:t.from,to:t.to,renderChildNodes:Ke,scrollToPath:u}));else{const{key:r,value:a}=t,o=s.includes(a);p.push((0,n.createElement)(et,{...e,postprocessValue:c,collectionLimit:l,key:`Node--${r}`,keyPath:[r,...i],value:c(a),circularCache:[...s,a],isCircular:o,hideRoot:!1}))}}),p}function Ve(e){const{circularCache:t=[],collectionLimit:r,createItemString:a,data:o,expandable:l,getItemString:s,hideRoot:i,hideRootExpand:c,isCircular:d,keyPath:u,labelRenderer:p,level:g=0,nodeType:m,nodeTypeIndicator:f,shouldExpandNodeInitially:y,scrollToPath:b}=e,h="root"===u[0],v=c?l&&!h&&c:l,j=l&&v,[x,k]=(0,n.useState)(!d&&y(u,o,g)),N=(0,n.useCallback)(()=>{j&&k(!x)},[j,x]),_=x||i&&0===g?Ke({...e,circularCache:t,level:g+1}):null,S=s(m,o,(0,Ce.jsx)("span",{className:`${De.nestedNodeItemType} ${x?De.nestedNodeItemTypeExpanded:""}`,children:f}),a(o,r),u),w=[u,m,x,j];return i?(0,Ce.jsx)(Be,{scrollToPath:b,expandable:j,expanded:x,nodeType:m,keyPath:u,className:`${De.rootNode} ${x?De.rootNodeExpanded:""} ${l?De.rootNodeExpandable:""}`,children:(0,Ce.jsx)("ul",{className:`${De.rootNodeChildren}`,children:_})}):(0,Ce.jsxs)(Be,{scrollToPath:b,expandable:j,expanded:x,nodeType:m,keyPath:u,className:`${De.nestedNode}  ${x?De.nestedNodeExpanded:""} ${j?De.nestedNodeExpandable:""}`,children:[(0,Ce.jsxs)("span",{className:De.nestedNodeLabelWrap,children:[v&&(0,Ce.jsx)(Le,{nodeType:m,expanded:x,onClick:N}),(0,Ce.jsx)("span",{"data-nodetype":m,"data-keypath":u[0],className:`${De.nestedNodeLabel} ${x?De.nestedNodeLabelExpanded:""} ${j?De.nestedNodeLabelExpandable:""}`,onClick:N,children:p(...w)}),(0,Ce.jsx)("span",{className:De.nestedNodeItemString,onClick:N,children:S})]}),_&&(0,Ce.jsx)("ul",{className:De.nestedNodeChildren,children:_})]})}function Ge(e){const t=Object.getOwnPropertyNames(e).length;return`${t} ${1!==t?"keys":"key"}`}function He({data:e,...t}){return(0,Ce.jsx)(Ve,{...t,data:e,nodeType:"Object",nodeTypeIndicator:"Error"===t.nodeType?"Error()":"{}",createItemString:Ge,expandable:Object.getOwnPropertyNames(e).length>0})}function qe(e){return`${e.length} ${1!==e.length?"items":"item"}`}function Ze({data:e,...t}){return(0,Ce.jsx)(Ve,{...t,data:e,nodeType:"Array",nodeTypeIndicator:"[]",createItemString:qe,expandable:e.length>0})}function Ue(e,t){let r=0,n=!1;if(Number.isSafeInteger(e.size))r=e.size;else for(const a of e){if(t&&r+1>t){n=!0;break}r+=1}return`${n?">":""}${r} ${1!==r?"entries":"entry"}`}function Ye(e){return(0,Ce.jsx)(Ve,{...e,nodeType:"Iterable",nodeTypeIndicator:"()",createItemString:Ue,expandable:!0})}const Qe={valueNode:"_valueNode_1b1pr_1",valueNodeValue:"_valueNodeValue_1b1pr_7",valueNodeLabel:"_valueNodeLabel_1b1pr_13",valueNodeScrolled:"_valueNodeScrolled_1b1pr_18"};function Xe({nodeType:e,labelRenderer:t,keyPath:r,valueRenderer:a,value:o,valueGetter:l=e=>e,scrollToPath:s}){const i=n.useRef(null),c=void 0!==s&&it(s,r);n.useEffect(()=>{i.current&&i.current.scrollIntoView({behavior:"auto"})},[]);const d=c?{ref:i,"data-scrolled":"true"}:{};return(0,Ce.jsxs)("li",{className:`${Qe.valueNode} valueNode--${e} valueNode--${r[0]} ${c?Qe.valueNodeScrolled:""}`,...d,children:[(0,Ce.jsx)("span",{className:Qe.valueNodeLabel,children:t(r,e,!1,!1)}),(0,Ce.jsx)("span",{className:Qe.valueNodeValue,children:a(l?l(o):void 0,o,...r)})]})}function et({getItemString:e,keyPath:t,labelRenderer:r,value:a,valueRenderer:o,isCustomNode:l,valueWrap:s,scrollToPath:i,...c}){const d=l(a)?"Custom":function(e){const t=Object.prototype.toString.call(e).slice(8,-1);return"Object"===t&&"function"==typeof e[Symbol.iterator]?"Iterable":"Custom"===t&&e.constructor!==Object&&e instanceof Object?"Object":t}(a),u=t[0],p={keyPath:t,labelRenderer:r,nodeType:d,value:a,valueRenderer:o,scrollToPath:i},g={...c,...p,getItemString:e,data:a,isCustomNode:l,valueWrap:s};switch(d){case"Object":case"Error":case"WeakMap":case"WeakSet":return(0,Ce.jsx)(He,{...g},u);case"Array":return(0,Ce.jsx)(Ze,{...g},u);case"Iterable":case"Map":case"Set":return(0,Ce.jsx)(Ye,{...g},u);case"String":return(0,n.createElement)(Xe,{...p,key:u,valueGetter:e=>`${s}${e}${s}`});case"Number":case"Custom":return(0,Ce.jsx)(Xe,{...p},u);case"Boolean":return(0,Ce.jsx)(Xe,{...p,valueGetter:e=>e?"true":"false"},u);case"Date":return(0,Ce.jsx)(Xe,{...p,valueGetter:e=>e.toISOString()},u);case"Null":return(0,Ce.jsx)(Xe,{...p,valueGetter:()=>"null"},u);case"Undefined":return(0,Ce.jsx)(Xe,{...p,valueGetter:()=>"undefined"},u);case"Function":case"Symbol":return(0,Ce.jsx)(Xe,{...p,valueGetter:e=>e.toString()},u);default:return(0,Ce.jsx)(Xe,{...p,valueGetter:()=>`<${d}>`},u)}}const tt={tree:"_tree_ze97d_48",defaultItemString:"_defaultItemString_ze97d_52",defaultLabelWrap:"_defaultLabelWrap_ze97d_57",defaultLabelWrapExpandable:"_defaultLabelWrap--expandable_ze97d_63"},rt=e=>e,nt=(e,t,r)=>0===r,at=(e,t,r,n,a)=>(0,Ce.jsxs)("span",{className:tt.defaultItemString,children:[r," ",n]}),ot=([e],t,r,n)=>(0,Ce.jsxs)("span",{className:`${tt.defaultLabelWrap} ${n?tt.defaultLabelWrapExpandable:""}`,children:[e,":"]}),lt=()=>!1;function st({data:e,keyPath:t=["root"],labelRenderer:r=ot,valueRenderer:n=rt,shouldExpandNodeInitially:a=nt,hideRoot:o=!1,hideRootExpand:l=!1,getItemString:s=at,postprocessValue:i=rt,isCustomNode:c=lt,collectionLimit:d=50,sortObjectKeys:u=!1,scrollToPath:p,valueWrap:g='"'}){return(0,Ce.jsx)("ul",{role:"tree","aria-multiselectable":!0,"aria-readonly":"true",className:tt.tree,children:(0,Ce.jsx)(et,{scrollToPath:p,hideRootExpand:l,keyPath:o?[]:t,value:i(e),isCustomNode:c,labelRenderer:r,valueRenderer:n,shouldExpandNodeInitially:a,hideRoot:o,getItemString:s,postprocessValue:i,collectionLimit:d,sortObjectKeys:u,valueWrap:g})})}const it=(e,t)=>{if(!e.length||e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0};var ct=r(9641),dt=r(4351);function ut(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function pt(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){ut(e,t,r[t])})}return e}function gt(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}const mt="24px";function ft({model:e}){var t,r,o;const{emptyScene:u,hasJSONFields:p,JSONFiltersSupported:g,menu:m,hasHighlight:f,hasLabels:y,hasMetadata:b,sortOrder:h,wrapLogMessage:v,data:j,rawFrame:x}=e.useState(),k=s.jh.getData(e);k.useState();const N=s.jh.getAncestor(e,_.i),{visualizationType:S,selectedLine:w}=N.useState(),E=(0,i.useStyles2)(yt,v),O=(0,D.ir)(e),P=(0,D.Gc)(e),T=(0,D.iw)(e),$=(0,ge.tn)(j),F=null==$?void 0:$.fields.find(e=>e.type===l.FieldType.string&&(0,A.Z6)(e.name)),C=new Map,I=(0,D.Gk)(e),L=(0,n.useMemo)(()=>{if(void 0===w)return;const e=null==x?void 0:x.fields.find(e=>(0,A.vF)(e.name)),t=null==e?void 0:e.values.findIndex(e=>e===(null==w?void 0:w.id)),r=void 0!==t&&-1!==t?t:void 0;return void 0!==r?[r,d.FN]:void 0},[w,x]);P.state.filters.forEach(e=>{const t=e.value.substring(3,e.value.length-3).split('\\"][\\"').join("_");C.set(t,e)});const R=(0,n.useRef)(null),M=(0,n.useCallback)(()=>{var e;null===(e=R.current)||void 0===e||e.scrollTo(0,R.current.scrollHeight)},[]),J=(0,n.useCallback)(()=>{var e;null===(e=R.current)||void 0===e||e.scrollTo(0,0)},[]),W=(0,n.useCallback)(t=>{e.setState({hasMetadata:t}),(0,dt._2)(t)},[e]),z=(0,n.useCallback)(t=>{e.setState({hasLabels:t}),(0,dt.ki)(t)},[e]),B=(0,n.useCallback)(t=>{e.setState({hasHighlight:t}),(0,dt.FD)(t)},[e]),K=(0,n.useCallback)(t=>{e.setState({wrapLogMessage:t}),(0,dt.YK)("wrapLogMessage",t)},[e]),V=F&&F.values.length>0&&!1===p,G=!g&&!0!==V;return a().createElement("div",{className:E.panelChromeWrap},a().createElement(i.PanelChrome,{padding:"none",showMenuAlways:!0,statusMessage:null===(r=k.state.data)||void 0===r||null===(t=r.errors)||void 0===t?void 0:t[0].message,loadingState:null===(o=k.state.data)||void 0===o?void 0:o.state,title:"JSON",menu:m?a().createElement(m.Component,{model:m}):void 0,actions:a().createElement(ct.C,{vizType:S,onChange:N.setVisualizationType})},a().createElement("div",{className:E.container},(null==F?void 0:F.values)&&(null==F?void 0:F.values.length)>0&&a().createElement(pe.V,{onWrapLogMessageClick:K,wrapLogMessage:v,showHighlight:f,onToggleHighlightClick:B,showMetadata:b,onToggleStructuredMetadataClick:W,showLabels:y,onToggleLabelsClick:z,sortOrder:h,onSortOrderChange:e.handleSortChange,onScrollToBottomClick:M,onScrollToTopClick:J}),(null==F?void 0:F.values)&&(null==F?void 0:F.values.length)>0&&a().createElement("div",{className:E.JSONTreeWrap,ref:R},G&&a().createElement(i.Alert,{className:E.alert,severity:"warning",title:"JSON filtering requires Loki 3.5.0."},"This view will be read only until Loki is upgraded to 3.5.0"),V&&a().createElement(i.Alert,{className:E.alert,severity:"info",title:"No JSON fields detected"},"This view is built for JSON log lines, but none were detected. Switch to the Logs or Table view for a better experience."),a().createElement(st,{scrollToPath:L,data:F.values,hideRootExpand:!0,valueWrap:"",shouldExpandNodeInitially:(e,t,r)=>r<=2,getItemString:(t,r,n,o,l)=>a().createElement(Te,{itemString:o,keyPath:l,itemType:n,data:r,nodeType:t,model:e,levelsVar:T}),valueRenderer:(t,r,...n)=>a().createElement(ue,{valueAsString:t,keyPath:n,lineFilters:I.state.filters,model:e}),labelRenderer:(t,r)=>a().createElement(ne,{model:e,nodeType:r,keyPath:t,fieldsVar:O,lineField:F,JSONFiltersSupported:g,JSONParserPropsMap:C,lineFilters:I.state.filters})})),u&&0===(null==F?void 0:F.values.length)&&a().createElement(c.W.Component,{model:u}))))}const yt=(e,t)=>{const r=e.isDark?l.colorManipulator.alpha(l.colorManipulator.lighten(e.colors.background.canvas,.1),.4):l.colorManipulator.alpha(l.colorManipulator.darken(e.colors.background.canvas,.1),.4),n=e.isDark?l.colorManipulator.darken(l.colorManipulator.alpha(e.colors.info.transparent,1),.2):l.colorManipulator.lighten(l.colorManipulator.alpha(e.colors.info.transparent,1),.2);return{alert:(0,o.css)({marginTop:e.spacing(3.5),marginBottom:0}),panelChromeWrap:(0,o.css)({contain:"strict",width:"100%",height:"100%"}),container:(0,o.css)(gt(pt({display:"flex",flexDirection:"row-reverse",height:"100%",paddingBottom:e.spacing(1),paddingRight:e.spacing(1)},v(e)),{contain:"content"})),highlight:(0,o.css)({backgroundColor:"rgb(255, 153, 0)",color:"black"}),JSONTreeWrap:o.css`
      font-family: ${e.typography.fontFamilyMonospace};
      font-family: ${e.typography.fontFamilyMonospace}; // override css variables
      --json-tree-align-items: flex-start;
      --json-tree-label-color: ${e.colors.text.primary};
      --json-tree-label-value-color: ${e.colors.text.secondary};
      --json-tree-arrow-color: ${e.colors.secondary.contrastText};
      --json-tree-ul-root-padding: ${e.spacing(3)} 0 ${e.spacing(2)} 0;
      --json-tree-arrow-left-offset: -${e.spacing(2)};
      --json-tree-inline: inline-grid;
      --json-tree-value-label-margin: 1em;
      // Scroll offset for sticky header
      --json-tree-scroll-margin: 26px;
      // Scroll behavior @todo set "auto" instead of "smooth" for users with prefers-reduced-motion
      scroll-behavior: ${window.matchMedia("(prefers-reduced-motion: reduce)").matches?"auto":"smooth"};
      ${bt(e,t)}

      overflow: auto;
      height: 100%;
      width: 100%;
      // Prevents scrollbar from getting tucked behind the fixed header
      clip-path: inset(0 0 0 0);

      // Line height defines the height of each line
      li {
        line-height: ${mt};
      }

      //first treeItem node
      > ul > li {
        // line wrap
        width: 100%;
        margin-top: ${e.spacing(2.5)};
      }

      // Array and other labels additional without markup
      // first nested node padding
      > ul > li > ul {
        // Hackery to keep elements from under the sticky header from being in the scrollable area
        padding: 0 0 0 ${e.spacing(2)};
      }

      // Root node styles
      > ul > li > span {
        position: fixed;
        top: 0;
        left: 0;
        width: calc(100% - ${e.spacing(4.75)});
        background: ${e.colors.background.primary};
        padding-bottom: ${e.spacing(.5)};
        margin-bottom: ${e.spacing(.5)};
        box-shadow: 0 1px 7px rgba(1, 4, 9, 0.75);
        z-index: 2;
        padding-left: ${e.spacing(1)};
        align-items: center;
        overflow-x: auto;
        overflow-y: hidden;
      }

      // Line node scrolledTo styles
      li[data-scrolled='true'] {
        // sticky header cannot have transparency!
        & > span {
          background-color: ${n};
        }
        background-color: ${l.colorManipulator.alpha(e.colors.info.transparent,.25)};
      }

      // Value node background hover colors
      .valueNode--String,
      .valueNode--Number {
        &:hover {
          // This is the value line the user is hovering over, we don't want transparency so it always stands out from the bg
          background-color: ${e.colors.background.canvas};
          box-shadow: ${e.shadows.z1};
        }
      }

      // Nested node hover styles
      ul > li > ul > li > ul {
        li[data-nodetype='Object'],
        li[data-nodetype='Array'] {
          // This is the nested node line the user is hovering over, we don't want transparency so it always stands out from the bg
          & > span {
            width: 100%;
            &:hover {
              background-color: ${e.colors.background.canvas};
              box-shadow: ${e.shadows.z1};
            }
          }
          // And this is the node the user is hovering in, we want some transparency to help differentiate between nodes of differing depths
          &:hover {
            background-color: ${r};
            box-shadow: ${e.shadows.z1};
          }
        }
      }

      // sticky time header
      > ul > li > ul > li > span,
      > ul > li > ul > div > li > span {
        position: sticky;
        width: 100%;
        top: 26px;
        left: 0;
        background: ${e.colors.background.primary};
        z-index: 1;
        display: flex;
        align-items: center;
        &:hover {
          background-color: ${e.colors.background.canvas};
          box-shadow: ${e.shadows.z1};
        }
      }
    `}},bt=(e,t)=>{if(!t)return o.css`
      // line wrap
      --json-tree-value-text-wrap: nowrap;
    `}},4398:(e,t,r)=>{r.r(t),r.d(t,{default:()=>L});var n=r(5959),a=r(6089),o=r(7781),l=r(2007);const s=r.p+"b6946652df0df52a6ebf.svg",i=r.p+"7c69e09a44ae38215563.svg",c=r.p+"3982c6482f693636d827.svg",d=r.p+"da86b141436a1efb9287.svg",u=r.p+"f4a80ec106902b21468c.svg",p=r.p+"45984fdddc778c4b7076.svg",g=r.p+"f0b5af7b8afc5505b70a.svg",m=r.p+"26a78975976a604ba774.svg",f=r.p+"fca8b87950e835f73012.svg",y=r.p+"3cf91cbcfc3c94965931.svg",b=r.p+"f6f180a5b21afe1799fd.svg",h=r.p+"2946a9608ee7f2b5f618.svg",v=r.p+"5ff755573093d28cbf2b.svg",j=r.p+"85b746ba2cec564b9a89.svg",x=r.p+"087755a1246f96c42579.svg",k=r.p+"8de041fb65ff0bb36133.svg",N=r.p+"6327ceb22fede245e36b.svg",_=r.p+"8a289c3c1e2e6710580c.svg",S=r.p+"3d7fda5219d0a128c3a7.svg",w=r.p+"0b7992cbbccaaaabfb4e.svg",E=r.p+"bd5bafea986ef624f050.svg",O=r.p+"a02d697b42ff5ef7323b.svg",P=r.p+"2168a10beed690100fcb.svg";function T(e,t,r){return t in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}function $(e,t){return t=null!=t?t:{},Object.getOwnPropertyDescriptors?Object.defineProperties(e,Object.getOwnPropertyDescriptors(t)):function(e,t){var r=Object.keys(e);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(e);t&&(n=n.filter(function(t){return Object.getOwnPropertyDescriptor(e,t).enumerable})),r.push.apply(r,n)}return r}(Object(t)).forEach(function(r){Object.defineProperty(e,r,Object.getOwnPropertyDescriptor(t,r))}),e}function F(e,t){if(null==e)return{};var r,n,a=function(e,t){if(null==e)return{};var r,n,a={},o=Object.keys(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||(a[r]=e[r]);return a}(e,t);if(Object.getOwnPropertySymbols){var o=Object.getOwnPropertySymbols(e);for(n=0;n<o.length;n++)r=o[n],t.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(e,r)&&(a[r]=e[r])}return a}const C={eye:{dark:{secondary:c,primary:b,hover:d},light:{secondary:j,primary:b,hover:x}},"search-minus":{dark:{secondary:u,primary:O,hover:p},light:{secondary:k,primary:O,hover:N}},"search-plus":{dark:{secondary:g,primary:P,hover:m},light:{secondary:_,primary:P,hover:S}},"share-alt":{dark:{secondary:f,hover:y,primary:""},light:{secondary:w,hover:E,primary:""}},copy:{dark:{secondary:s,hover:i,primary:""},light:{secondary:h,hover:v,primary:""}}},I=n.forwardRef((e,t)=>{const{variant:r="secondary",name:o,className:s,tooltip:i}=e,c=F(e,["variant","name","className","tooltip"]),d=(0,l.useStyles2)(R,r,o,C);let u,p;return u="string"==typeof i?i:void 0,(0,n.useMemo)(()=>n.createElement(l.Tooltip,{ref:t,content:i},n.createElement("button",$(function(e){for(var t=1;t<arguments.length;t++){var r=null!=arguments[t]?arguments[t]:{},n=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&(n=n.concat(Object.getOwnPropertySymbols(r).filter(function(e){return Object.getOwnPropertyDescriptor(r,e).enumerable}))),n.forEach(function(t){T(e,t,r[t])})}return e}({},c),{ref:p,"aria-label":u,className:(0,a.cx)(d.button,s),type:"button"}),n.createElement("span",{className:d.img}))),[u,t,s,c,d,i,p])});I.displayName="ImgButton";const L=I,R=(e,t,r,n)=>{let l=e.colors.text.primary;"primary"===t&&(l=e.colors.primary.text);const s=e.isDark?"dark":"light";return{button:(0,a.css)({zIndex:0,position:"relative",margin:`0 ${e.spacing.x0_5} 0 0`,boxShadow:"none",border:"none",display:"inline-flex",background:"transparent",justifyContent:"center",alignItems:"center",padding:0,color:l,"&[disabled], &:disabled":{cursor:"not-allowed",color:e.colors.action.disabledText,opacity:.65},"&:focus, &:focus-visible":{outline:"2px dotted transparent",outlineOffset:"2px",boxShadow:`0 0 0 2px ${e.colors.background.canvas}, 0 0 0px 4px ${e.colors.primary.main}`,transitionTimingFunction:"cubic-bezier(0.19, 1, 0.22, 1)",transitionDuration:"0.2s",transitionProperty:"outline, outline-offset, box-shadow"},"&:focus:not(:focus-visible)":{outline:"none",boxShadow:"none"}}),icon:(0,a.css)({verticalAlign:"baseline"}),img:(0,a.css)({backgroundImage:"primary"===t?`url(${n[r][s].primary})`:`url(${n[r][s].secondary})`,width:"16px",height:"16px","&:before":{width:"16px",height:"16px",left:0,zIndex:-1,position:"absolute",opacity:0,borderRadius:e.shape.radius.default,content:'""',transform:"scale(1.45)",[e.transitions.handleMotion("no-preference","reduce")]:{transitionDuration:"0.2s",transitionTimingFunction:"cubic-bezier(0.4, 0, 0.2, 1)",transitionProperty:"opacity"}},"&:hover":{backgroundImage:`url(${n[r][s].hover})`,"&:before":{backgroundColor:"secondary"===t?e.colors.action.hover:o.colorManipulator.alpha(l,.12),opacity:1}}})}}}}]);
//# sourceMappingURL=7.js.map?_cache=617e09944eff4d2b85bc