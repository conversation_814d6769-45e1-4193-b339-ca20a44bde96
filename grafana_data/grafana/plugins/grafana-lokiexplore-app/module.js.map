{"version": 3, "file": "module.js", "mappings": "uMACIA,EADAC,ECAAC,EACAC,E,6YCMJ,MAAMC,GAAMC,EAAAA,EAAAA,MAAK,eACf,MAAM,cAAEC,SAAwB,wEAExBC,QAASC,SAAwB,wEACjCD,QAASE,SAA0B,+BACnCF,QAASG,SAAsB,8BAQvC,OANAF,IAEIF,YACIK,QAAQC,IAAI,CAACH,IAAmBC,OAGjC,6BACT,KAEMG,GAAYR,EAAAA,EAAAA,MAAK,eACrB,aAAa,6BACf,KAEaS,GAAS,IAAIC,EAAAA,WAAgBC,YAAYZ,GAAKa,cAAc,CACvEC,MAAO,gBACPC,KAAM,MACNC,KAAMP,EACNQ,GAAI,kBAGN,IAAK,MAAMC,KAAcC,EAAAA,GACvBT,EAAOU,QAAQF,E,qHCzBjB,MAAMJ,EAAQ,uBACRO,EAAc,8CACdN,EAAO,UAEAO,EAAkB,CAC7BC,kBAAmB,iDAKRJ,EAKT,CACF,CACEK,QAASC,EAAAA,sBAAsBC,mBAC/BZ,QACAO,cACAN,OACAY,KAAMC,IACNC,UAAWC,GAEb,CACEN,QAASC,EAAAA,sBAAsBM,qBAC/BjB,QACAO,cACAN,OACAY,KAAMC,IACNC,UAAWC,IAIf,SAASA,EAAqDE,G,IAKzCC,EAkBsCA,EAtBzD,IAAKD,EACH,OAEF,MAAMC,EAAYD,EAAQR,QAAQU,MAAMC,I,IAAWA,E,MAA4B,UAAX,QAAjBA,EAAAA,EAAOC,kBAAPD,IAAAA,OAAAA,EAAAA,EAAmBE,KAAe,IACrF,IAAKJ,KAAkC,QAApBA,EAAAA,EAAUG,kBAAVH,IAAAA,OAAAA,EAAAA,EAAsBK,KACvC,OAGF,MAAMC,EAAON,EAAUM,KACjBC,GAAeC,EAAAA,EAAAA,IAAoBF,GAEnCG,EAAgBF,EAAaN,MAAMS,GAAaA,EAASC,WAAaC,EAAAA,EAASC,QAErF,IAAKJ,EACH,OAGF,MAAMK,EAAaC,EAAaN,EAAcO,OAC9C,IAAIC,EAAYR,EAAcS,MAAQC,EAAAA,GAAe,UAAYV,EAAcS,IAE/EX,EAAaa,MAAK,CAACC,EAAGC,IAAOD,EAAEH,MAAQD,GAAa,EAAI,IAExD,IAAIM,EAASC,EAAgBC,EAAcC,aAAkC,QAApB1B,EAAAA,EAAUG,kBAAVH,IAAAA,OAAAA,EAAAA,EAAsBK,KAC/EkB,EAASC,EAAgBC,EAAcE,cAAe5B,EAAQ6B,UAAUC,KAAKC,UAAUC,WAAYR,GACnGA,EAASC,EAAgBC,EAAcO,YAAajC,EAAQ6B,UAAUK,GAAGH,UAAUC,WAAYR,GAE/F,IAAK,MAAMW,KAAe3B,EAEpB2B,EAAY9B,OAAS+B,EAAAA,EAAUC,UAInCb,EAASc,EACPZ,EAAca,OACd,GAAGJ,EAAYhB,OAAOgB,EAAYvB,YAAYuB,EAAYlB,QAC1DO,IAGJ,MAAO,CACL7B,KAAMC,EAAa,YAAYsB,KAAaH,SAAmBS,GAEnE,CAEO,SAAS5B,EAAaD,EAAO,WAAY6C,GAC9C,MAAO,MAAMC,EAAAA,KAAgB9C,IAAO6C,EAAY,IAAIA,EAAUR,aAAe,IAC/E,CAEO,MAAMN,EAAgB,CAC3BC,aAAc,OAAOe,EAAAA,KACrBd,cAAe,OACfK,YAAa,KACbM,OAAQ,OAAOI,EAAAA,KACfC,OAAQ,OAAOC,EAAAA,MAIV,SAASpB,EAAgBN,EAAuBF,EAAe6B,G,IAC3BA,EAAzC,MAAMC,EAAe,IAAIC,gBAAsC,QAAtBF,EAAAA,aAAAA,EAAAA,EAAcd,kBAAdc,IAAAA,EAAAA,EAA4BG,SAASC,QAG9E,OAFAH,EAAaI,IAAIhC,EAAKF,GAEf8B,CACT,CAEO,SAAST,EACdnB,EACAF,EACA6B,G,IAEyCA,EAAzC,MAAMC,EAAe,IAAIC,gBAAsC,QAAtBF,EAAAA,aAAAA,EAAAA,EAAcd,kBAAdc,IAAAA,EAAAA,EAA4BG,SAASC,QAG9E,OAFAH,EAAaK,OAAOjC,EAAKF,GAElB8B,CACT,CAEO,SAAS/B,EAAaqC,GAC3B,OAAOA,EAAUC,QAAQ,MAAO,IAClC,C,iDCvHYlB,G,qDAAAA,IAAAA,EAAAA,CAAAA,G,iDCCAvB,G,qEAAAA,IAAAA,EAAAA,CAAAA,G,+MCIL,MAAM0C,EAaX,eAAOC,CAASC,GACd,OAAO,IAAIF,EAAaE,EAAK3B,KAAM2B,EAAKvB,GAAIuB,EAAMA,EAAKpD,KACzD,CAEAqD,QAAAA,CAASC,GACP,OAAOC,KAAK9B,MAAQ6B,EAAS7B,MAAQ8B,KAAK1B,IAAMyB,EAASzB,EAC3D,CAEA2B,aAAAA,CAAcC,GACZ,OAAOA,EAAMC,UAAUH,KAAK9B,KAAM8B,KAAK1B,GACzC,CAjBA8B,WAAAA,CAAYlC,EAAcI,EAAY+B,EAAwB5D,GAL9DyB,EAAAA,KAAAA,YAAAA,GACAI,EAAAA,KAAAA,UAAAA,GACA7B,EAAAA,KAAAA,YAAAA,GACA4D,EAAAA,KAAAA,kBAAAA,GAGEL,KAAK9B,KAAOA,EACZ8B,KAAK1B,GAAKA,EACV0B,KAAKvD,KAAOA,EACZuD,KAAKK,WAAaA,CACpB,EAeK,SAASC,EAAkBJ,EAAeK,GAC/C,MAAMC,EAAsB,GAS5B,OARmBC,EAAAA,GAAOC,MAAMR,GAC3BS,QAAQ,CACXC,MAAQf,UACYgB,IAAdN,GAA2BA,EAAUO,SAASjB,EAAKpD,KAAKpB,MAC1DmF,EAAMO,KAAKlB,EAAKA,KAClB,IAGGW,CACT,CAEA,SAASQ,EAA4BnB,EAAkBpD,GACrD,GAAIoD,EAAKpD,KAAKpB,KAAOoB,EACnB,MAAO,CAACkD,EAAaC,SAASC,IAGhC,MAAMoB,EAA4B,GAClC,IAAIC,EAAM,EACNC,EAAQtB,EAAKuB,WAAWF,GAC5B,KAAOC,GACLF,EAAUF,QAAQC,EAA4BG,EAAO1E,IACrDyE,EAAMC,EAAM7C,GACZ6C,EAAQtB,EAAKuB,WAAWF,GAE1B,OAAOD,CACT,CAEO,SAASpE,EAAoBqD,GAClC,MAAMmB,EAAmB,GACnBtE,EAAWuD,EAAkBJ,EAAO,CAACoB,EAAAA,KAC3C,GAAwB,IAApBvE,EAASwE,OACX,OAAOF,EAET,MAAMG,EAAmB7B,EAAaC,SAAS7C,EAAS,IAElD0E,EAAanB,EAAkBJ,EAAO,CAACwB,EAAAA,KAC7C,IAAK,MAAMC,KAAWF,EAAY,CAChC,MAAMG,EAAkBjC,EAAaC,SAAS+B,GACxCE,EAAqBb,EAA4BW,EAASG,EAAAA,IAC1DC,EAAgBf,EAA4BW,EAASK,EAAAA,IAErDC,EAAmB,MADP/B,EAAMC,UAAU0B,EAAmB,GAAGvD,GAAIyD,EAAc,GAAG7D,MAC9CjB,EAAAA,EAASC,MAAQD,EAAAA,EAASiF,SACnD3E,EAAMsE,EAAmB,GAAG5B,cAAcC,GAC1C7C,EAAQ0E,EAAcI,KAAKpC,GAAaG,EAAMC,UAAUJ,EAAS7B,KAAO,EAAG6B,EAASzB,GAAK,KAAI,GAE9Ff,GAAQF,GAIbgE,EAAON,KAAK,CACVxD,MACAP,SAAUiF,EACV5E,QACAZ,KAAM+E,EAAiB1B,SAAS8B,GAAmBpD,EAAAA,EAAUC,aAAUoC,GAE3E,CAEA,OAAOQ,CACT,CAqBO,MAAMe,EAAU,EAChB,SAASC,EAAanC,GAC3B,OAA2C,IArBtC,SAAyBA,EAAeoC,GAC7C,IAAIC,GAAkB,EAUtB,OATa9B,EAAAA,GAAOC,MAAMR,GACrBS,QAAQ,CACXC,MAAO,EAAGnE,WACR,GAAIA,EAAKpB,KAAOiH,EAEd,OADAC,GAAkB,GACX,CACT,IAGGA,CACT,CASSA,CAAgBrC,EAAOkC,EAChC,C,yXC9FO,MAAMrD,EAAa,UACbyD,EAAkB,aAClBC,EAAqB,kBACrBC,EAA0B,qBAC1BzD,EAAa,SACb0D,EAAkB,YAClBC,EAAsB,mBACtBC,EAAe,WACfC,EAAoB,cACpBC,EAAe,WACfC,EAAoB,cACpBC,EAAa,SACbC,EAAkB,YAClBC,EAAqB,UACrBC,EAAqB,UACrBC,EAA0B,aAC1BC,EAA2B,uBAE3BC,EAAoB,gBACpBC,EAAyB,mBACzB1E,EAAiB,KACjB2E,EAAsB,QACtBC,EAAoB,sDACpBC,EAAmB,6CACnBC,EAAmB,WAEnBC,EAAkB,aAClBC,EAAuB,gBACvBC,EAAkB,aAClBC,EAAuB,gBACvBC,EAA2B,IAAIzB,MAAoBQ,KAAqBF,KAAqBkB,KAAwBd,KAAmBY,KAAwBnB,IAEhKuB,EAA6B,IAAI1B,MAAoBQ,KAAqBF,KAAqBkB,KAAwBd,KAAmBY,KAAwBlB,IAClKuB,EAAgC,IAAI3B,MAAoBQ,KAAqBJ,KAAuBoB,KAAwBd,KAAmBY,KAAwBnB,IACvKyB,EAA8B,IAAI5B,MAAoBQ,KAAqBF,KAAqBkB,KAAwBpB,KAAuBkB,KAAwBnB,IACvK0B,EAAgC,IAAI7B,MAAoBM,KAAqBE,KAAqBc,IAClGQ,EAAiB,CAAE5H,IAAK+G,GACxBc,EAAqB,SACrBC,EAAuB,iBACvBhH,EAAe,eACfiH,EAAmB,UACnBC,EAAyB,yBAEzBC,EAAuB,I,WClEpCC,EAAOC,QAAUC,C,WCAjBF,EAAOC,QAAUE,C,WCAjBH,EAAOC,QAAUG,C,WCAjBJ,EAAOC,QAAUI,C,WCAjBL,EAAOC,QAAUK,C,WCAjBN,EAAOC,QAAUM,C,WCAjBP,EAAOC,QAAUO,C,UCAjBR,EAAOC,QAAUQ,C,WCAjBT,EAAOC,QAAUS,C,WCAjBV,EAAOC,QAAUU,C,WCAjBX,EAAOC,QAAUW,C,sFCGjB,MAAMC,EAAsB,KAC5B,IAAIC,EAAa,EACjB,MAAMC,EACF,WAAAvF,CAAYlC,EAAMI,GACd0B,KAAK9B,KAAOA,EACZ8B,KAAK1B,GAAKA,CACd,EAOJ,MAAMsH,EAIF,WAAAxF,CAAYyF,EAAS,CAAC,GAClB7F,KAAK3E,GAAKqK,IACV1F,KAAK8F,UAAYD,EAAOC,QACxB9F,KAAK+F,YAAcF,EAAOE,aAAe,MACrC,MAAM,IAAIC,MAAM,uDACnB,EACL,CAUA,GAAAC,CAAIC,GACA,GAAIlG,KAAK8F,QACL,MAAM,IAAIK,WAAW,0CAGzB,MAFoB,mBAATD,IACPA,EAAQE,EAASF,MAAMA,IACnBzJ,IACJ,IAAI4J,EAASH,EAAMzJ,GACnB,YAAkBoE,IAAXwF,EAAuB,KAAO,CAACrG,KAAMqG,EAAO,CAE3D,EAQJT,EAASU,SAAW,IAAIV,EAAS,CAAEG,YAAaQ,GAAOA,EAAIC,MAAM,OAMjEZ,EAASa,SAAW,IAAIb,EAAS,CAAEG,YAAaQ,GAAOA,EAAIC,MAAM,OAMjEZ,EAASc,MAAQ,IAAId,EAAS,CAAEG,YAAaQ,GAAOA,EAAIC,MAAM,OAY9DZ,EAASe,QAAU,IAAIf,EAAS,CAAEG,YAAa1I,IACvC,GAAIA,GAAkB,OAATA,GAA2B,OAATA,GAA2B,QAATA,EAC7C,MAAM,IAAI8I,WAAW,8BAAgC9I,GACzD,OAAOA,GAAS,MAAM,IAO9BuI,EAASgB,YAAc,IAAIhB,EAAS,CAAEE,SAAS,IAO/CF,EAASiB,UAAY,IAAIjB,EAAS,CAAEE,SAAS,IAM7CF,EAASkB,QAAU,IAAIlB,EAAS,CAAEE,SAAS,IAM3C,MAAMiB,EACF,WAAA3G,CAIA4G,EAUAC,EAIAxG,GACIT,KAAKgH,KAAOA,EACZhH,KAAKiH,QAAUA,EACfjH,KAAKS,OAASA,CAClB,CAIA,UAAOyG,CAAIF,GACP,OAAOA,GAAQA,EAAKG,OAASH,EAAKG,MAAMvB,EAASkB,QAAQzL,GAC7D,EAEJ,MAAM+L,EAAUC,OAAOC,OAAO,MAI9B,MAAMlB,EAIF,WAAAhG,CAOAmH,EAIAJ,EAKA9L,EAIAmM,EAAQ,GACJxH,KAAKuH,KAAOA,EACZvH,KAAKmH,MAAQA,EACbnH,KAAK3E,GAAKA,EACV2E,KAAKwH,MAAQA,CACjB,CAIA,aAAOC,CAAOC,GACV,IAAIP,EAAQO,EAAKP,OAASO,EAAKP,MAAM5F,OAAS8F,OAAOC,OAAO,MAAQF,EAChEI,GAASE,EAAKC,IAAM,EAAuB,IAAMD,EAAKE,QAAU,EAA2B,IAC1FF,EAAKG,MAAQ,EAAyB,IAAmB,MAAbH,EAAKH,KAAe,EAA6B,GAC9F9K,EAAO,IAAI2J,EAASsB,EAAKH,MAAQ,GAAIJ,EAAOO,EAAKrM,GAAImM,GACzD,GAAIE,EAAKP,MACL,IAAK,IAAIW,KAAOJ,EAAKP,MAGjB,GAFKY,MAAMC,QAAQF,KACfA,EAAMA,EAAIrL,IACVqL,EAAK,CACL,GAAIA,EAAI,GAAGhC,QACP,MAAM,IAAIK,WAAW,8CACzBgB,EAAMW,EAAI,GAAGzM,IAAMyM,EAAI,EAC3B,CAER,OAAOrL,CACX,CAKA,IAAAwL,CAAKA,GAAQ,OAAOjI,KAAKmH,MAAMc,EAAK5M,GAAK,CAIzC,SAAI6M,GAAU,OAAqB,EAAblI,KAAKwH,OAAgC,CAAG,CAI9D,aAAIW,GAAc,OAAqB,EAAbnI,KAAKwH,OAAoC,CAAG,CAItE,WAAIY,GAAY,OAAqB,EAAbpI,KAAKwH,OAAkC,CAAG,CAKlE,eAAIa,GAAgB,OAAqB,EAAbrI,KAAKwH,OAAsC,CAAG,CAK1E,EAAAc,CAAGf,GACC,GAAmB,iBAARA,EAAkB,CACzB,GAAIvH,KAAKuH,MAAQA,EACb,OAAO,EACX,IAAIb,EAAQ1G,KAAKiI,KAAKrC,EAASc,OAC/B,QAAOA,GAAQA,EAAM6B,QAAQhB,IAAS,CAC1C,CACA,OAAOvH,KAAK3E,IAAMkM,CACtB,CASA,YAAOrB,CAAM/D,GACT,IAAIqG,EAASnB,OAAOC,OAAO,MAC3B,IAAK,IAAIW,KAAQ9F,EACb,IAAK,IAAIoF,KAAQU,EAAKzB,MAAM,KACxBgC,EAAOjB,GAAQpF,EAAI8F,GAC3B,OAAQpI,IACJ,IAAK,IAAI4I,EAAS5I,EAAKoI,KAAKrC,EAASc,OAAQgC,GAAK,EAAGA,GAAKD,EAASA,EAAOlH,OAAS,GAAImH,IAAK,CACxF,IAAIC,EAAQH,EAAOE,EAAI,EAAI7I,EAAK0H,KAAOkB,EAAOC,IAC9C,GAAIC,EACA,OAAOA,CACf,EAER,EAKJvC,EAASwC,KAAO,IAAIxC,EAAS,GAAIiB,OAAOC,OAAO,MAAO,EAAG,GAUzD,MAAMuB,EAKF,WAAAzI,CAIA0I,GACI9I,KAAK8I,MAAQA,EACb,IAAK,IAAIJ,EAAI,EAAGA,EAAII,EAAMvH,OAAQmH,IAC9B,GAAII,EAAMJ,GAAGrN,IAAMqN,EACf,MAAM,IAAIvC,WAAW,8EACjC,CAMA,MAAA4C,IAAU5B,GACN,IAAI6B,EAAW,GACf,IAAK,IAAIvM,KAAQuD,KAAK8I,MAAO,CACzB,IAAIG,EAAW,KACf,IAAK,IAAIC,KAAU/B,EAAO,CACtB,IAAIlB,EAAMiD,EAAOzM,GACbwJ,IACKgD,IACDA,EAAW5B,OAAO8B,OAAO,CAAC,EAAG1M,EAAK0K,QACtC8B,EAAShD,EAAI,GAAG5K,IAAM4K,EAAI,GAElC,CACA+C,EAASjI,KAAKkI,EAAW,IAAI7C,EAAS3J,EAAK8K,KAAM0B,EAAUxM,EAAKpB,GAAIoB,EAAK+K,OAAS/K,EACtF,CACA,OAAO,IAAIoM,EAAQG,EACvB,EAEJ,MAAMI,EAAa,IAAIC,QAAWC,EAAkB,IAAID,QAKxD,IAAIE,GACJ,SAAWA,GAMPA,EAASA,EAAyB,eAAI,GAAK,iBAM3CA,EAASA,EAA2B,iBAAI,GAAK,mBAM7CA,EAASA,EAAuB,aAAI,GAAK,eAOzCA,EAASA,EAAyB,eAAI,GAAK,gBAC9C,CA1BD,CA0BGA,IAAaA,EAAW,CAAC,IAiB5B,MAAMC,EAIF,WAAApJ,CAIA3D,EAIAgN,EAKAxI,EAIAM,EAIA4F,GASI,GARAnH,KAAKvD,KAAOA,EACZuD,KAAKyJ,SAAWA,EAChBzJ,KAAKiB,UAAYA,EACjBjB,KAAKuB,OAASA,EAIdvB,KAAKmH,MAAQ,KACTA,GAASA,EAAM5F,OAAQ,CACvBvB,KAAKmH,MAAQE,OAAOC,OAAO,MAC3B,IAAK,IAAKW,EAAM5K,KAAU8J,EACtBnH,KAAKmH,MAAqB,iBAARc,EAAmBA,EAAOA,EAAK5M,IAAMgC,CAC/D,CACJ,CAIA,QAAAe,GACI,IAAI0I,EAAUC,EAAYG,IAAIlH,MAC9B,GAAI8G,IAAYA,EAAQG,QACpB,OAAOH,EAAQE,KAAK5I,WACxB,IAAIqL,EAAW,GACf,IAAK,IAAIC,KAAM1J,KAAKyJ,SAAU,CAC1B,IAAIlD,EAAMmD,EAAGtL,WACTmI,IACIkD,IACAA,GAAY,KAChBA,GAAYlD,EAEpB,CACA,OAAQvG,KAAKvD,KAAK8K,MACb,KAAKoC,KAAK3J,KAAKvD,KAAK8K,QAAUvH,KAAKvD,KAAK2L,QAAUwB,KAAKC,UAAU7J,KAAKvD,KAAK8K,MAAQvH,KAAKvD,KAAK8K,OACzFkC,EAASlI,OAAS,IAAMkI,EAAW,IAAM,IAFzBA,CAG7B,CAMA,MAAAK,CAAOC,EAAO,GACV,OAAO,IAAIC,EAAWhK,KAAKiK,QAASF,EACxC,CAMA,QAAAG,CAAShJ,EAAKiJ,EAAO,EAAGJ,EAAO,GAC3B,IAAIK,EAAQhB,EAAWlC,IAAIlH,OAASA,KAAKiK,QACrCH,EAAS,IAAIE,EAAWI,GAG5B,OAFAN,EAAOO,OAAOnJ,EAAKiJ,GACnBf,EAAW7J,IAAIS,KAAM8J,EAAOQ,OACrBR,CACX,CAKA,WAAIG,GACA,OAAO,IAAIM,EAASvK,KAAM,EAAG,EAAG,KACpC,CAYA,OAAAwK,CAAQtJ,EAAKiJ,EAAO,GAChB,IAAItK,EAAO4K,EAAYrB,EAAWlC,IAAIlH,OAASA,KAAKiK,QAAS/I,EAAKiJ,GAAM,GAExE,OADAf,EAAW7J,IAAIS,KAAMH,GACdA,CACX,CAQA,YAAA6K,CAAaxJ,EAAKiJ,EAAO,GACrB,IAAItK,EAAO4K,EAAYnB,EAAgBpC,IAAIlH,OAASA,KAAKiK,QAAS/I,EAAKiJ,GAAM,GAE7E,OADAb,EAAgB/J,IAAIS,KAAMH,GACnBA,CACX,CAQA,YAAA8K,CAAazJ,EAAKiJ,EAAO,GACrB,OAwcR,SAAuBnD,EAAM9F,EAAKiJ,GAC9B,IAAIS,EAAQ5D,EAAK0D,aAAaxJ,EAAKiJ,GAAOU,EAAS,KACnD,IAAK,IAAIC,EAAOF,aAAiBL,EAAWK,EAAQA,EAAMxO,QAAQ2O,OAAQD,EAAMA,EAAOA,EAAKC,OACxF,GAAID,EAAKE,MAAQ,EAAG,CAChB,IAAID,EAASD,EAAKC,QACjBF,IAAWA,EAAS,CAACD,KAAS7J,KAAKgK,EAAOP,QAAQtJ,EAAKiJ,IACxDW,EAAOC,CACX,KACK,CACD,IAAIE,EAAQlE,EAAYG,IAAI4D,EAAK9D,MAEjC,GAAIiE,GAASA,EAAMhE,SAAWgE,EAAMhE,QAAQ,GAAG/I,MAAQgD,GAAO+J,EAAMhE,QAAQgE,EAAMhE,QAAQ1F,OAAS,GAAGjD,IAAM4C,EAAK,CAC7G,IAAIgK,EAAO,IAAIX,EAASU,EAAMjE,KAAMiE,EAAMhE,QAAQ,GAAG/I,KAAO4M,EAAK5M,MAAO,EAAG4M,IAC1ED,IAAWA,EAAS,CAACD,KAAS7J,KAAK0J,EAAYS,EAAMhK,EAAKiJ,GAAM,GACrE,CACJ,CAEJ,OAAOU,EAASM,EAAUN,GAAUD,CACxC,CA1deQ,CAAcpL,KAAMkB,EAAKiJ,EACpC,CAQA,OAAAxJ,CAAQ+G,GACJ,IAAI,MAAE9G,EAAK,MAAEyK,EAAK,KAAEnN,EAAO,EAAC,GAAEI,EAAK0B,KAAKuB,QAAWmG,EAC/CqC,EAAOrC,EAAKqC,MAAQ,EAAGuB,GAAQvB,EAAOR,EAASgC,kBAAoB,EACvE,IAAK,IAAIC,EAAIxL,KAAK8J,OAAOC,EAAOR,EAASgC,oBAAqB,CAC1D,IAAIE,GAAU,EACd,GAAID,EAAEtN,MAAQI,GAAMkN,EAAElN,IAAMJ,KAAUoN,GAAQE,EAAE/O,KAAK4L,cAA4B,IAAbzH,EAAM4K,IAAe,CACrF,GAAIA,EAAEE,aACF,SACJD,GAAU,CACd,CACA,KACQA,GAAWJ,IAAUC,IAASE,EAAE/O,KAAK4L,cACrCgD,EAAMG,IACNA,EAAEG,eAHD,CAKL,IAAKH,EAAET,SACH,OACJU,GAAU,CACd,CACJ,CACJ,CAKA,IAAAxD,CAAKA,GACD,OAAQA,EAAKnC,QAAiC9F,KAAKmH,MAAQnH,KAAKmH,MAAMc,EAAK5M,SAAMwF,EAA1Db,KAAKvD,KAAKwL,KAAKA,EAC1C,CAMA,cAAI2D,GACA,IAAIvF,EAAS,GACb,GAAIrG,KAAKmH,MACL,IAAK,IAAI9L,KAAM2E,KAAKmH,MAChBd,EAAOtF,KAAK,EAAE1F,EAAI2E,KAAKmH,MAAM9L,KACrC,OAAOgL,CACX,CAMA,OAAAwF,CAAQhG,EAAS,CAAC,GACd,OAAO7F,KAAKyJ,SAASlI,QAAU,EAA+BvB,KAC1D8L,EAAa1F,EAASwC,KAAM5I,KAAKyJ,SAAUzJ,KAAKiB,UAAW,EAAGjB,KAAKyJ,SAASlI,OAAQ,EAAGvB,KAAKuB,QAAQ,CAACkI,EAAUxI,EAAWM,IAAW,IAAIiI,EAAKxJ,KAAKvD,KAAMgN,EAAUxI,EAAWM,EAAQvB,KAAK4L,aAAa/F,EAAOkG,UAAY,EAAEtC,EAAUxI,EAAWM,IAAW,IAAIiI,EAAKpD,EAASwC,KAAMa,EAAUxI,EAAWM,IAClT,CAKA,YAAOyK,CAAMC,GAAQ,OA4tBzB,SAAmBA,GACf,IAAIC,EACJ,IAAI,OAAEC,EAAM,QAAEC,EAAO,gBAAEC,EAAkB5G,EAAmB,OAAE6G,EAAS,GAAE,cAAEC,EAAgBH,EAAQtD,MAAMvH,QAAW0K,EAChHnC,EAAS/B,MAAMC,QAAQmE,GAAU,IAAIK,EAAiBL,EAAQA,EAAO5K,QAAU4K,EAC/ErD,EAAQsD,EAAQtD,MAChBlC,EAAc,EAAGC,EAAY,EACjC,SAAS4F,EAASC,EAAaC,EAAQlD,EAAUxI,EAAW2L,EAAUC,GAClE,IAAI,GAAExR,EAAE,MAAEyR,EAAK,IAAEC,EAAG,KAAEC,GAASlD,EAC3BmD,EAAmBpG,EACvB,KAAOmG,EAAO,GAAG,CAEb,GADAlD,EAAOoD,QACM,GAATF,EAAsC,CACtC,IAAInN,EAAOyM,EAAOjR,GAGlB,OAFAoO,EAAS1I,KAAKlB,QACdoB,EAAUF,KAAK+L,EAAQJ,EAE3B,CACK,IAAa,GAATM,EAEL,YADApG,EAAcvL,GAGb,IAAa,GAAT2R,EAEL,YADAnG,EAAYxL,GAIZ,MAAM,IAAI8K,WAAW,6BAA6B6G,IAE1D,CACA,IAAsBnN,EAAMsM,EAAxB1P,EAAOqM,EAAMzN,GACb8R,EAAWL,EAAQJ,EACvB,GAAIK,EAAMD,GAAST,IAAoBF,EA8G3C,SAAwBiB,EAASR,GAO7B,IAAIS,EAAOvD,EAAOuD,OACdL,EAAO,EAAGF,EAAQ,EAAGQ,EAAO,EAAGC,EAAWF,EAAKN,IAAMV,EACrDhG,EAAS,CAAE2G,KAAM,EAAGF,MAAO,EAAGQ,KAAM,GACxCxC,EAAM,IAAK,IAAI6B,EAASU,EAAKnM,IAAMkM,EAASC,EAAKnM,IAAMyL,GAAS,CAC5D,IAAIa,EAAWH,EAAKL,KAEpB,GAAIK,EAAKhS,IAAMuR,GAAYY,GAAY,EAAG,CAGtCnH,EAAO2G,KAAOA,EACd3G,EAAOyG,MAAQA,EACfzG,EAAOiH,KAAOA,EACdA,GAAQ,EACRN,GAAQ,EACRK,EAAKH,OACL,QACJ,CACA,IAAIC,EAAWE,EAAKnM,IAAMsM,EAC1B,GAAIA,EAAW,GAAKL,EAAWR,GAAUU,EAAKP,MAAQS,EAClD,MACJ,IAAIE,EAAeJ,EAAKhS,IAAMkR,EAAgB,EAAI,EAC9CmB,EAAYL,EAAKP,MAErB,IADAO,EAAKH,OACEG,EAAKnM,IAAMiM,GAAU,CACxB,GAAIE,EAAKL,KAAO,EAAG,CACf,IAAkB,GAAdK,EAAKL,KAGL,MAAMlC,EAFN2C,GAAgB,CAGxB,MACSJ,EAAKhS,IAAMkR,IAChBkB,GAAgB,GAEpBJ,EAAKH,MACT,CACAJ,EAAQY,EACRV,GAAQQ,EACRF,GAAQG,CACZ,CAMA,OALIb,EAAW,GAAKI,GAAQI,KACxB/G,EAAO2G,KAAOA,EACd3G,EAAOyG,MAAQA,EACfzG,EAAOiH,KAAOA,GAEXjH,EAAO2G,KAAO,EAAI3G,OAASxF,CACtC,CAlKoD8M,CAAe7D,EAAO5I,IAAMyL,EAAQC,IAAY,CAE5F,IAAIX,EAAO,IAAI2B,YAAYzB,EAAOa,KAAOb,EAAOmB,MAC5CO,EAAS/D,EAAO5I,IAAMiL,EAAOa,KAAMhC,EAAQiB,EAAK1K,OACpD,KAAOuI,EAAO5I,IAAM2M,GAChB7C,EAAQ8C,EAAa3B,EAAOW,MAAOb,EAAMjB,GAC7CnL,EAAO,IAAIkO,EAAW9B,EAAMc,EAAMZ,EAAOW,MAAOV,GAChDe,EAAWhB,EAAOW,MAAQJ,CAC9B,KACK,CACD,IAAImB,EAAS/D,EAAO5I,IAAM8L,EAC1BlD,EAAOoD,OACP,IAAIc,EAAgB,GAAIC,EAAiB,GACrCC,EAAgB7S,GAAMkR,EAAgBlR,GAAM,EAC5C8S,EAAY,EAAGC,EAAUrB,EAC7B,KAAOjD,EAAO5I,IAAM2M,GACZK,GAAiB,GAAKpE,EAAOzO,IAAM6S,GAAiBpE,EAAOkD,MAAQ,GAC/DlD,EAAOiD,KAAOqB,EAAU/B,IACxBgC,EAAeL,EAAeC,EAAgBnB,EAAOqB,EAAWrE,EAAOiD,IAAKqB,EAASF,EAAejB,GACpGkB,EAAYH,EAAczM,OAC1B6M,EAAUtE,EAAOiD,KAErBjD,EAAOoD,QAEFL,EAAQ,KACbyB,EAAaxB,EAAOe,EAAQG,EAAeC,GAG3CxB,EAASK,EAAOe,EAAQG,EAAeC,EAAgBC,EAAerB,EAAQ,GAOtF,GAJIqB,GAAiB,GAAKC,EAAY,GAAKA,EAAYH,EAAczM,QACjE8M,EAAeL,EAAeC,EAAgBnB,EAAOqB,EAAWrB,EAAOsB,EAASF,EAAejB,GACnGe,EAAcO,UACdN,EAAeM,UACXL,GAAiB,GAAKC,EAAY,EAAG,CACrC,IAAIK,EA0ChB,SAAsB/R,GAClB,MAAO,CAACgN,EAAUxI,EAAWM,KACzB,IAAgDkN,EAAMC,EAAlD7H,EAAY,EAAG8H,EAAQlF,EAASlI,OAAS,EAC7C,GAAIoN,GAAS,IAAMF,EAAOhF,EAASkF,cAAmBnF,EAAM,CACxD,IAAKmF,GAASF,EAAKhS,MAAQA,GAAQgS,EAAKlN,QAAUA,EAC9C,OAAOkN,GACPC,EAAgBD,EAAKxG,KAAKrC,EAASiB,cACnCA,EAAY5F,EAAU0N,GAASF,EAAKlN,OAASmN,EACrD,CACA,OAAO3C,EAAStP,EAAMgN,EAAUxI,EAAWM,EAAQsF,EAAU,CAErE,CArDuB+H,CAAanS,GACxBoD,EAAOiM,EAAarP,EAAMuR,EAAeC,EAAgB,EAAGD,EAAczM,OAAQ,EAAGwL,EAAMD,EAAO0B,EAAMA,EAC5G,MAEI3O,EAAOkM,EAAStP,EAAMuR,EAAeC,EAAgBlB,EAAMD,EAAOG,EAAmBF,EAE7F,CACAtD,EAAS1I,KAAKlB,GACdoB,EAAUF,KAAKoM,EACnB,CACA,SAASmB,EAAa5B,EAAaC,EAAQlD,EAAUxI,GACjD,IAAIT,EAAQ,GACRqO,EAAY,EAAGC,GAAU,EAC7B,KAAOhF,EAAO5I,IAAMyL,GAAQ,CACxB,IAAI,GAAEtR,EAAE,MAAEyR,EAAK,IAAEC,EAAG,KAAEC,GAASlD,EAC/B,GAAIkD,EAAO,EACPlD,EAAOoD,WAEN,IAAI4B,GAAU,GAAKhC,EAAQgC,EAC5B,MAGIA,EAAS,IACTA,EAAS/B,EAAMV,GACnB7L,EAAMO,KAAK1F,EAAIyR,EAAOC,GACtB8B,IACA/E,EAAOoD,MACX,CACJ,CACA,GAAI2B,EAAW,CACX,IAAI1C,EAAS,IAAIyB,YAAwB,EAAZiB,GACzB/B,EAAQtM,EAAMA,EAAMe,OAAS,GACjC,IAAK,IAAImH,EAAIlI,EAAMe,OAAS,EAAGwN,EAAI,EAAGrG,GAAK,EAAGA,GAAK,EAC/CyD,EAAO4C,KAAOvO,EAAMkI,GACpByD,EAAO4C,KAAOvO,EAAMkI,EAAI,GAAKoE,EAC7BX,EAAO4C,KAAOvO,EAAMkI,EAAI,GAAKoE,EAC7BX,EAAO4C,KAAOA,EAElBtF,EAAS1I,KAAK,IAAIgN,EAAW5B,EAAQ3L,EAAM,GAAKsM,EAAOV,IACvDnL,EAAUF,KAAK+L,EAAQJ,EAC3B,CACJ,CAaA,SAAS2B,EAAe5E,EAAUxI,EAAW+N,EAAMtG,EAAGxK,EAAMI,EAAI7B,EAAMoK,GAClE,IAAImH,EAAgB,GAAIC,EAAiB,GACzC,KAAOxE,EAASlI,OAASmH,GACrBsF,EAAcjN,KAAK0I,EAASwF,OAC5BhB,EAAelN,KAAKE,EAAUgO,MAAQD,EAAO9Q,GAEjDuL,EAAS1I,KAAKgL,EAASK,EAAQtD,MAAMrM,GAAOuR,EAAeC,EAAgB3P,EAAKJ,EAAM2I,EAAYvI,IAClG2C,EAAUF,KAAK7C,EAAO8Q,EAC1B,CACA,SAASjD,EAAStP,EAAMgN,EAAUxI,EAAWM,EAAQsF,EAAY,EAAGM,GAChE,GAAIP,EAAa,CACb,IAAIsI,EAAO,CAACtJ,EAASgB,YAAaA,GAClCO,EAAQA,EAAQ,CAAC+H,GAAMC,OAAOhI,GAAS,CAAC+H,EAC5C,CACA,GAAIrI,EAAY,GAAI,CAChB,IAAIqI,EAAO,CAACtJ,EAASiB,UAAWA,GAChCM,EAAQA,EAAQ,CAAC+H,GAAMC,OAAOhI,GAAS,CAAC+H,EAC5C,CACA,OAAO,IAAI1F,EAAK/M,EAAMgN,EAAUxI,EAAWM,EAAQ4F,EACvD,CAsDA,SAAS2G,EAAasB,EAAajD,EAAQnB,GACvC,IAAI,GAAE3P,EAAE,MAAEyR,EAAK,IAAEC,EAAG,KAAEC,GAASlD,EAE/B,GADAA,EAAOoD,OACHF,GAAQ,GAAK3R,EAAKkR,EAAe,CACjC,IAAI8C,EAAarE,EACjB,GAAIgC,EAAO,EAAG,CACV,IAAIa,EAAS/D,EAAO5I,KAAO8L,EAAO,GAClC,KAAOlD,EAAO5I,IAAM2M,GAChB7C,EAAQ8C,EAAasB,EAAajD,EAAQnB,EAClD,CACAmB,IAASnB,GAASqE,EAClBlD,IAASnB,GAAS+B,EAAMqC,EACxBjD,IAASnB,GAAS8B,EAAQsC,EAC1BjD,IAASnB,GAAS3P,CACtB,MACkB,GAAT2R,EACLpG,EAAcvL,GAEA,GAAT2R,IACLnG,EAAYxL,GAEhB,OAAO2P,CACX,CACA,IAAIvB,EAAW,GAAIxI,EAAY,GAC/B,KAAO6I,EAAO5I,IAAM,GAChBuL,EAASR,EAAKa,OAAS,EAAGb,EAAKmD,aAAe,EAAG3F,EAAUxI,GAAY,EAAG,GAC9E,IAAIM,EAAgC,QAAtB2K,EAAKD,EAAK1K,cAA2B,IAAP2K,EAAgBA,EAAMzC,EAASlI,OAASN,EAAU,GAAKwI,EAAS,GAAGlI,OAAS,EACxH,OAAO,IAAIiI,EAAKV,EAAMmD,EAAKqD,OAAQ7F,EAAS8E,UAAWtN,EAAUsN,UAAWhN,EAChF,CA17BgCgO,CAAUtD,EAAO,EAKjDzC,EAAKgG,MAAQ,IAAIhG,EAAKpD,EAASwC,KAAM,GAAI,GAAI,GAC7C,MAAM4D,EACF,WAAApM,CAAY+L,EAAQnB,GAChBhL,KAAKmM,OAASA,EACdnM,KAAKgL,MAAQA,CACjB,CACA,MAAI3P,GAAO,OAAO2E,KAAKmM,OAAOnM,KAAKgL,MAAQ,EAAI,CAC/C,SAAI8B,GAAU,OAAO9M,KAAKmM,OAAOnM,KAAKgL,MAAQ,EAAI,CAClD,OAAI+B,GAAQ,OAAO/M,KAAKmM,OAAOnM,KAAKgL,MAAQ,EAAI,CAChD,QAAIgC,GAAS,OAAOhN,KAAKmM,OAAOnM,KAAKgL,MAAQ,EAAI,CACjD,OAAI9J,GAAQ,OAAOlB,KAAKgL,KAAO,CAC/B,IAAAkC,GAASlN,KAAKgL,OAAS,CAAG,CAC1B,IAAAqC,GAAS,OAAO,IAAIb,EAAiBxM,KAAKmM,OAAQnM,KAAKgL,MAAQ,EAQnE,MAAM+C,EAIF,WAAA3N,CAIA+L,EAIA5K,EAIAhC,GACIS,KAAKmM,OAASA,EACdnM,KAAKuB,OAASA,EACdvB,KAAKT,IAAMA,CACf,CAIA,QAAI9C,GAAS,OAAO2J,EAASwC,IAAM,CAInC,QAAAxK,GACI,IAAIiI,EAAS,GACb,IAAK,IAAI2E,EAAQ,EAAGA,EAAQhL,KAAKmM,OAAO5K,QACpC8E,EAAOtF,KAAKf,KAAKyP,YAAYzE,IAC7BA,EAAQhL,KAAKmM,OAAOnB,EAAQ,GAEhC,OAAO3E,EAAOqJ,KAAK,IACvB,CAIA,WAAAD,CAAYzE,GACR,IAAI3P,EAAK2E,KAAKmM,OAAOnB,GAAQ2E,EAAW3P,KAAKmM,OAAOnB,EAAQ,GACxDvO,EAAOuD,KAAKT,IAAIuJ,MAAMzN,GAAKgL,EAAS5J,EAAK8K,KAI7C,GAHI,KAAKoC,KAAKtD,KAAY5J,EAAK2L,UAC3B/B,EAASuD,KAAKC,UAAUxD,IAExBsJ,IADJ3E,GAAS,GAEL,OAAO3E,EACX,IAAIoD,EAAW,GACf,KAAOuB,EAAQ2E,GACXlG,EAAS1I,KAAKf,KAAKyP,YAAYzE,IAC/BA,EAAQhL,KAAKmM,OAAOnB,EAAQ,GAEhC,OAAO3E,EAAS,IAAMoD,EAASiG,KAAK,KAAO,GAC/C,CAIA,SAAAE,CAAUP,EAAYM,EAAUE,EAAK3O,EAAKiJ,GACtC,IAAI,OAAEgC,GAAWnM,KAAM8P,GAAQ,EAC/B,IAAK,IAAIpH,EAAI2G,EAAY3G,GAAKiH,KACtBI,EAAU5F,EAAMjJ,EAAKiL,EAAOzD,EAAI,GAAIyD,EAAOzD,EAAI,MAC/CoH,EAAOpH,EACHmH,EAAM,IAHsBnH,EAAIyD,EAAOzD,EAAI,IAOvD,OAAOoH,CACX,CAIA,KAAAE,CAAMC,EAAQC,EAAMhS,GAChB,IAAIP,EAAIqC,KAAKmM,OACTgE,EAAO,IAAIvC,YAAYsC,EAAOD,GAASG,EAAM,EACjD,IAAK,IAAI1H,EAAIuH,EAAQlB,EAAI,EAAGrG,EAAIwH,GAAO,CACnCC,EAAKpB,KAAOpR,EAAE+K,KACdyH,EAAKpB,KAAOpR,EAAE+K,KAAOxK,EACrB,IAAII,EAAK6R,EAAKpB,KAAOpR,EAAE+K,KAAOxK,EAC9BiS,EAAKpB,KAAOpR,EAAE+K,KAAOuH,EACrBG,EAAMC,KAAKC,IAAIF,EAAK9R,EACxB,CACA,OAAO,IAAIyP,EAAWoC,EAAMC,EAAKpQ,KAAKT,IAC1C,EAEJ,SAASwQ,EAAU5F,EAAMjJ,EAAKhD,EAAMI,GAChC,OAAQ6L,GACJ,KAAM,EAAqB,OAAOjM,EAAOgD,EACzC,KAAM,EAAyB,OAAO5C,GAAM4C,GAAOhD,EAAOgD,EAC1D,KAAK,EAAqB,OAAOhD,EAAOgD,GAAO5C,EAAK4C,EACpD,KAAK,EAAwB,OAAOhD,GAAQgD,GAAO5C,EAAK4C,EACxD,KAAK,EAAoB,OAAO5C,EAAK4C,EACrC,KAAK,EAAuB,OAAO,EAE3C,CACA,SAASuJ,EAAY5K,EAAMqB,EAAKiJ,EAAMoG,GAGlC,IAFA,IAAIrE,EAEGrM,EAAK3B,MAAQ2B,EAAKvB,KACpB6L,EAAO,EAAItK,EAAK3B,MAAQgD,EAAMrB,EAAK3B,KAAOgD,KAC1CiJ,GAAQ,EAAItK,EAAKvB,IAAM4C,EAAMrB,EAAKvB,GAAK4C,IAAM,CAC9C,IAAI6J,GAAUwF,GAAY1Q,aAAgB0K,GAAY1K,EAAKmL,MAAQ,EAAI,KAAOnL,EAAKkL,OACnF,IAAKA,EACD,OAAOlL,EACXA,EAAOkL,CACX,CACA,IAAIhB,EAAOwG,EAAW,EAAIhH,EAASiH,eAEnC,GAAID,EACA,IAAK,IAAIzF,EAAOjL,EAAMkL,EAASD,EAAKC,OAAQA,EAAQD,EAAOC,EAAQA,EAASD,EAAKC,OACzED,aAAgBP,GAAYO,EAAKE,MAAQ,IAA+C,QAAxCkB,EAAKnB,EAAOnK,MAAMM,EAAKiJ,EAAMJ,UAA0B,IAAPmC,OAAgB,EAASA,EAAGhO,OAAS4M,EAAK5M,OAC1I2B,EAAOkL,GAEnB,OAAS,CACL,IAAIH,EAAQ/K,EAAKe,MAAMM,EAAKiJ,EAAMJ,GAClC,IAAKa,EACD,OAAO/K,EACXA,EAAO+K,CACX,CACJ,CACA,MAAM6F,EACF,MAAA3G,CAAOC,EAAO,GAAK,OAAO,IAAIC,EAAWhK,KAAM+J,EAAO,CACtD,QAAA2G,CAASjU,EAAMkU,EAAS,KAAMC,EAAQ,MAClC,IAAIC,EAAIC,EAAY9Q,KAAMvD,EAAMkU,EAAQC,GACxC,OAAOC,EAAEtP,OAASsP,EAAE,GAAK,IAC7B,CACA,WAAAC,CAAYrU,EAAMkU,EAAS,KAAMC,EAAQ,MACrC,OAAOE,EAAY9Q,KAAMvD,EAAMkU,EAAQC,EAC3C,CACA,OAAApG,CAAQtJ,EAAKiJ,EAAO,GAChB,OAAOM,EAAYzK,KAAMkB,EAAKiJ,GAAM,EACxC,CACA,YAAAO,CAAaxJ,EAAKiJ,EAAO,GACrB,OAAOM,EAAYzK,KAAMkB,EAAKiJ,GAAM,EACxC,CACA,YAAA4G,CAAa3U,GACT,OAAO4U,EAAiBhR,KAAM5D,EAClC,CACA,0BAAA6U,CAA2B/P,GACvB,IAAI4J,EAAO9K,KAAKkR,YAAYhQ,GAAMrB,EAAOG,KACzC,KAAO8K,GAAM,CACT,IAAI2D,EAAO3D,EAAKqG,UAChB,IAAK1C,GAAQA,EAAKnQ,IAAMwM,EAAKxM,GACzB,MACAmQ,EAAKhS,KAAK2L,SAAWqG,EAAKvQ,MAAQuQ,EAAKnQ,IACvCuB,EAAOiL,EACPA,EAAO2D,EAAK2C,aAGZtG,EAAO2D,CAEf,CACA,OAAO5O,CACX,CACA,QAAIA,GAAS,OAAOG,IAAM,CAC1B,QAAIkN,GAAS,OAAOlN,KAAK+K,MAAQ,EAErC,MAAMR,UAAiBkG,EACnB,WAAArQ,CAAYkK,EAAOpM,EAEnB8M,EAAOqG,GACHC,QACAtR,KAAKsK,MAAQA,EACbtK,KAAK9B,KAAOA,EACZ8B,KAAKgL,MAAQA,EACbhL,KAAKqR,QAAUA,CACnB,CACA,QAAI5U,GAAS,OAAOuD,KAAKsK,MAAM7N,IAAM,CACrC,QAAI8K,GAAS,OAAOvH,KAAKsK,MAAM7N,KAAK8K,IAAM,CAC1C,MAAIjJ,GAAO,OAAO0B,KAAK9B,KAAO8B,KAAKsK,MAAM/I,MAAQ,CACjD,SAAAgQ,CAAU7I,EAAGmH,EAAK3O,EAAKiJ,EAAMJ,EAAO,GAChC,IAAK,IAAIgB,EAAS/K,OAAQ,CACtB,IAAK,IAAI,SAAEyJ,EAAQ,UAAExI,GAAc8J,EAAOT,MAAOkH,EAAI3B,EAAM,EAAIpG,EAASlI,QAAU,EAAGmH,GAAK8I,EAAG9I,GAAKmH,EAAK,CACnG,IAAI3C,EAAOzD,EAASf,GAAIoE,EAAQ7L,EAAUyH,GAAKqC,EAAO7M,KACtD,GAAK6R,EAAU5F,EAAMjJ,EAAK4L,EAAOA,EAAQI,EAAK3L,QAE9C,GAAI2L,aAAgBa,EAAY,CAC5B,GAAIhE,EAAOR,EAASkI,eAChB,SACJ,IAAIzG,EAAQkC,EAAK0C,UAAU,EAAG1C,EAAKf,OAAO5K,OAAQsO,EAAK3O,EAAM4L,EAAO3C,GACpE,GAAIa,GAAS,EACT,OAAO,IAAI0G,EAAW,IAAIC,EAAc5G,EAAQmC,EAAMxE,EAAGoE,GAAQ,KAAM9B,EAC/E,MACK,GAAKjB,EAAOR,EAASgC,mBAAuB2B,EAAKzQ,KAAK4L,aAAeuJ,EAAS1E,GAAQ,CACvF,IAAIpG,EACJ,KAAMiD,EAAOR,EAASsI,gBAAkB/K,EAAUC,EAAYG,IAAIgG,MAAWpG,EAAQG,QACjF,OAAO,IAAIsD,EAASzD,EAAQE,KAAM8F,EAAOpE,EAAGqC,GAChD,IAAIH,EAAQ,IAAIL,EAAS2C,EAAMJ,EAAOpE,EAAGqC,GACzC,OAAQhB,EAAOR,EAASgC,mBAAsBX,EAAMnO,KAAK4L,YAAcuC,EACjEA,EAAM2G,UAAU1B,EAAM,EAAI3C,EAAKzD,SAASlI,OAAS,EAAI,EAAGsO,EAAK3O,EAAKiJ,EAC5E,CACJ,CACA,GAAKJ,EAAOR,EAASgC,mBAAsBR,EAAOtO,KAAK4L,YACnD,OAAO,KAMX,GAJIK,EADAqC,EAAOC,OAAS,EACZD,EAAOC,MAAQ6E,EAEfA,EAAM,GAAK,EAAI9E,EAAOsG,QAAQ/G,MAAMb,SAASlI,OACrDwJ,EAASA,EAAOsG,SACXtG,EACD,OAAO,IACf,CACJ,CACA,cAAIW,GAAe,OAAO1L,KAAKuR,UAAU,EAAG,EAAG,EAAG,EAAwB,CAC1E,aAAIJ,GAAc,OAAOnR,KAAKuR,UAAUvR,KAAKsK,MAAMb,SAASlI,OAAS,GAAI,EAAG,EAAG,EAAwB,CACvG,UAAAH,CAAWF,GAAO,OAAOlB,KAAKuR,UAAU,EAAG,EAAGrQ,EAAK,EAAqB,CACxE,WAAAgQ,CAAYhQ,GAAO,OAAOlB,KAAKuR,UAAUvR,KAAKsK,MAAMb,SAASlI,OAAS,GAAI,EAAGL,GAAM,EAAsB,CACzG,KAAAN,CAAMM,EAAKiJ,EAAMJ,EAAO,GACpB,IAAIjD,EACJ,KAAMiD,EAAOR,EAASiH,kBAAoB1J,EAAUC,EAAYG,IAAIlH,KAAKsK,SAAWxD,EAAQG,QAAS,CACjG,IAAI6K,EAAO5Q,EAAMlB,KAAK9B,KACtB,IAAK,IAAI,KAAEA,EAAI,GAAEI,KAAQwI,EAAQG,QAC7B,IAAKkD,EAAO,EAAIjM,GAAQ4T,EAAO5T,EAAO4T,KACjC3H,EAAO,EAAI7L,GAAMwT,EAAOxT,EAAKwT,GAC9B,OAAO,IAAIvH,EAASzD,EAAQE,KAAMF,EAAQG,QAAQ,GAAG/I,KAAO8B,KAAK9B,MAAO,EAAG8B,KAEvF,CACA,OAAOA,KAAKuR,UAAU,EAAG,EAAGrQ,EAAKiJ,EAAMJ,EAC3C,CACA,qBAAAgI,GACI,IAAIC,EAAMhS,KACV,KAAOgS,EAAIvV,KAAK4L,aAAe2J,EAAIX,SAC/BW,EAAMA,EAAIX,QACd,OAAOW,CACX,CACA,UAAIjH,GACA,OAAO/K,KAAKqR,QAAUrR,KAAKqR,QAAQU,wBAA0B,IACjE,CACA,eAAIpG,GACA,OAAO3L,KAAKqR,SAAWrR,KAAKgL,OAAS,EAAIhL,KAAKqR,QAAQE,UAAUvR,KAAKgL,MAAQ,EAAG,EAAG,EAAG,GAAyB,IACnH,CACA,eAAIoG,GACA,OAAOpR,KAAKqR,SAAWrR,KAAKgL,OAAS,EAAIhL,KAAKqR,QAAQE,UAAUvR,KAAKgL,MAAQ,GAAI,EAAG,EAAG,GAAyB,IACpH,CACA,QAAIhE,GAAS,OAAOhH,KAAKsK,KAAO,CAChC,MAAA2H,GAAW,OAAOjS,KAAKsK,KAAO,CAI9B,QAAAlM,GAAa,OAAO4B,KAAKsK,MAAMlM,UAAY,EAE/C,SAAS0S,EAAYjR,EAAMpD,EAAMkU,EAAQC,GACrC,IAAIsB,EAAMrS,EAAKiK,SAAUzD,EAAS,GAClC,IAAK6L,EAAIxG,aACL,OAAOrF,EACX,GAAc,MAAVsK,EACA,IAAK,IAAIhI,GAAQ,GAAQA,GAErB,GADAA,EAAQuJ,EAAIzV,KAAK6L,GAAGqI,IACfuB,EAAIvG,cACL,OAAOtF,EAEnB,OAAS,CACL,GAAa,MAATuK,GAAiBsB,EAAIzV,KAAK6L,GAAGsI,GAC7B,OAAOvK,EAGX,GAFI6L,EAAIzV,KAAK6L,GAAG7L,IACZ4J,EAAOtF,KAAKmR,EAAIrS,OACfqS,EAAIvG,cACL,OAAgB,MAATiF,EAAgBvK,EAAS,EACxC,CACJ,CACA,SAAS2K,EAAiBnR,EAAMzD,EAASsM,EAAItM,EAAQmF,OAAS,GAC1D,IAAK,IAAI4Q,EAAItS,EAAKkL,OAAQrC,GAAK,EAAGyJ,EAAIA,EAAEpH,OAAQ,CAC5C,IAAKoH,EACD,OAAO,EACX,IAAKA,EAAE1V,KAAK4L,YAAa,CACrB,GAAIjM,EAAQsM,IAAMtM,EAAQsM,IAAMyJ,EAAE5K,KAC9B,OAAO,EACXmB,GACJ,CACJ,CACA,OAAO,CACX,CACA,MAAMiJ,EACF,WAAAvR,CAAY2K,EAAQoB,EAAQnB,EAAO8B,GAC/B9M,KAAK+K,OAASA,EACd/K,KAAKmM,OAASA,EACdnM,KAAKgL,MAAQA,EACbhL,KAAK8M,MAAQA,CACjB,EAEJ,MAAM4E,UAAmBjB,EACrB,QAAIlJ,GAAS,OAAOvH,KAAKvD,KAAK8K,IAAM,CACpC,QAAIrJ,GAAS,OAAO8B,KAAK5D,QAAQ0Q,MAAQ9M,KAAK5D,QAAQ+P,OAAOA,OAAOnM,KAAKgL,MAAQ,EAAI,CACrF,MAAI1M,GAAO,OAAO0B,KAAK5D,QAAQ0Q,MAAQ9M,KAAK5D,QAAQ+P,OAAOA,OAAOnM,KAAKgL,MAAQ,EAAI,CACnF,WAAA5K,CAAYhE,EAASiV,EAASrG,GAC1BsG,QACAtR,KAAK5D,QAAUA,EACf4D,KAAKqR,QAAUA,EACfrR,KAAKgL,MAAQA,EACbhL,KAAKvD,KAAOL,EAAQ+P,OAAO5M,IAAIuJ,MAAM1M,EAAQ+P,OAAOA,OAAOnB,GAC/D,CACA,KAAA7J,CAAM0O,EAAK3O,EAAKiJ,GACZ,IAAI,OAAEgC,GAAWnM,KAAK5D,QAClB4O,EAAQmB,EAAOyD,UAAU5P,KAAKgL,MAAQ,EAAGmB,EAAOA,OAAOnM,KAAKgL,MAAQ,GAAI6E,EAAK3O,EAAMlB,KAAK5D,QAAQ0Q,MAAO3C,GAC3G,OAAOa,EAAQ,EAAI,KAAO,IAAI0G,EAAW1R,KAAK5D,QAAS4D,KAAMgL,EACjE,CACA,cAAIU,GAAe,OAAO1L,KAAKmB,MAAM,EAAG,EAAG,EAAwB,CACnE,aAAIgQ,GAAc,OAAOnR,KAAKmB,OAAO,EAAG,EAAG,EAAwB,CACnE,UAAAC,CAAWF,GAAO,OAAOlB,KAAKmB,MAAM,EAAGD,EAAK,EAAqB,CACjE,WAAAgQ,CAAYhQ,GAAO,OAAOlB,KAAKmB,OAAO,EAAGD,GAAM,EAAsB,CACrE,KAAAN,CAAMM,EAAKiJ,EAAMJ,EAAO,GACpB,GAAIA,EAAOR,EAASkI,eAChB,OAAO,KACX,IAAI,OAAEtF,GAAWnM,KAAK5D,QAClB4O,EAAQmB,EAAOyD,UAAU5P,KAAKgL,MAAQ,EAAGmB,EAAOA,OAAOnM,KAAKgL,MAAQ,GAAIb,EAAO,EAAI,GAAK,EAAGjJ,EAAMlB,KAAK5D,QAAQ0Q,MAAO3C,GACzH,OAAOa,EAAQ,EAAI,KAAO,IAAI0G,EAAW1R,KAAK5D,QAAS4D,KAAMgL,EACjE,CACA,UAAID,GACA,OAAO/K,KAAKqR,SAAWrR,KAAK5D,QAAQ2O,OAAOgH,uBAC/C,CACA,eAAAK,CAAgBvC,GACZ,OAAO7P,KAAKqR,QAAU,KAAOrR,KAAK5D,QAAQ2O,OAAOwG,UAAUvR,KAAK5D,QAAQ4O,MAAQ6E,EAAKA,EAAK,EAAG,EACjG,CACA,eAAIlE,GACA,IAAI,OAAEQ,GAAWnM,KAAK5D,QAClBwU,EAAQzE,EAAOA,OAAOnM,KAAKgL,MAAQ,GACvC,OAAI4F,GAAS5Q,KAAKqR,QAAUlF,EAAOA,OAAOnM,KAAKqR,QAAQrG,MAAQ,GAAKmB,EAAOA,OAAO5K,QACvE,IAAImQ,EAAW1R,KAAK5D,QAAS4D,KAAKqR,QAAST,GAC/C5Q,KAAKoS,gBAAgB,EAChC,CACA,eAAIhB,GACA,IAAI,OAAEjF,GAAWnM,KAAK5D,QAClBsQ,EAAc1M,KAAKqR,QAAUrR,KAAKqR,QAAQrG,MAAQ,EAAI,EAC1D,OAAIhL,KAAKgL,OAAS0B,EACP1M,KAAKoS,iBAAiB,GAC1B,IAAIV,EAAW1R,KAAK5D,QAAS4D,KAAKqR,QAASlF,EAAOyD,UAAUlD,EAAa1M,KAAKgL,OAAQ,EAAG,EAAG,GACvG,CACA,QAAIhE,GAAS,OAAO,IAAM,CAC1B,MAAAiL,GACI,IAAIxI,EAAW,GAAIxI,EAAY,IAC3B,OAAEkL,GAAWnM,KAAK5D,QAClB6T,EAASjQ,KAAKgL,MAAQ,EAAGkF,EAAO/D,EAAOA,OAAOnM,KAAKgL,MAAQ,GAC/D,GAAIkF,EAAOD,EAAQ,CACf,IAAI/R,EAAOiO,EAAOA,OAAOnM,KAAKgL,MAAQ,GACtCvB,EAAS1I,KAAKoL,EAAO6D,MAAMC,EAAQC,EAAMhS,IACzC+C,EAAUF,KAAK,EACnB,CACA,OAAO,IAAIyI,EAAKxJ,KAAKvD,KAAMgN,EAAUxI,EAAWjB,KAAK1B,GAAK0B,KAAK9B,KACnE,CAIA,QAAAE,GAAa,OAAO4B,KAAK5D,QAAQ+P,OAAOsD,YAAYzP,KAAKgL,MAAQ,EAErE,SAASG,EAAUkH,GACf,IAAKA,EAAM9Q,OACP,OAAO,KACX,IAAIuO,EAAO,EAAGwC,EAASD,EAAM,GAC7B,IAAK,IAAI3J,EAAI,EAAGA,EAAI2J,EAAM9Q,OAAQmH,IAAK,CACnC,IAAI7I,EAAOwS,EAAM3J,IACb7I,EAAK3B,KAAOoU,EAAOpU,MAAQ2B,EAAKvB,GAAKgU,EAAOhU,MAC5CgU,EAASzS,EACTiQ,EAAOpH,EAEf,CACA,IAAIwE,EAAOoF,aAAkB/H,GAAY+H,EAAOtH,MAAQ,EAAI,KAAOsH,EAAOvH,OACtEwH,EAAWF,EAAMrC,QAKrB,OAJI9C,EACAqF,EAASzC,GAAQ5C,EAEjBqF,EAASC,OAAO1C,EAAM,GACnB,IAAI2C,EAAcF,EAAUD,EACvC,CACA,MAAMG,EACF,WAAArS,CAAYiS,EAAOxS,GACfG,KAAKqS,MAAQA,EACbrS,KAAKH,KAAOA,CAChB,CACA,QAAIqN,GAAS,OAAO/B,EAAUnL,KAAKqS,MAAQ,EAyB/C,MAAMrI,EAIF,QAAIzC,GAAS,OAAOvH,KAAKvD,KAAK8K,IAAM,CAIpC,WAAAnH,CAAYP,EAIZkK,EAAO,GAYH,GAXA/J,KAAK+J,KAAOA,EAIZ/J,KAAKmM,OAAS,KACdnM,KAAK0S,MAAQ,GAIb1S,KAAKgL,MAAQ,EACbhL,KAAK2S,WAAa,KACd9S,aAAgB0K,EAChBvK,KAAK4S,UAAU/S,OAEd,CACDG,KAAKsK,MAAQzK,EAAKzD,QAAQ2O,OAC1B/K,KAAKmM,OAAStM,EAAKzD,QACnB,IAAK,IAAIyW,EAAIhT,EAAKwR,QAASwB,EAAGA,EAAIA,EAAExB,QAChCrR,KAAK0S,MAAMI,QAAQD,EAAE7H,OACzBhL,KAAK2S,WAAa9S,EAClBG,KAAK+S,SAASlT,EAAKmL,MACvB,CACJ,CACA,SAAA4H,CAAU/S,GACN,QAAKA,IAELG,KAAKsK,MAAQzK,EACbG,KAAKvD,KAAOoD,EAAKpD,KACjBuD,KAAK9B,KAAO2B,EAAK3B,KACjB8B,KAAK1B,GAAKuB,EAAKvB,IACR,EACX,CACA,QAAAyU,CAAS/H,EAAOvO,GACZuD,KAAKgL,MAAQA,EACb,IAAI,MAAE8B,EAAK,OAAEX,GAAWnM,KAAKmM,OAI7B,OAHAnM,KAAKvD,KAAOA,GAAQ0P,EAAO5M,IAAIuJ,MAAMqD,EAAOA,OAAOnB,IACnDhL,KAAK9B,KAAO4O,EAAQX,EAAOA,OAAOnB,EAAQ,GAC1ChL,KAAK1B,GAAKwO,EAAQX,EAAOA,OAAOnB,EAAQ,IACjC,CACX,CAIA,KAAAgI,CAAMnT,GACF,QAAKA,IAEDA,aAAgB0K,GAChBvK,KAAKmM,OAAS,KACPnM,KAAK4S,UAAU/S,KAE1BG,KAAKmM,OAAStM,EAAKzD,QACZ4D,KAAK+S,SAASlT,EAAKmL,MAAOnL,EAAKpD,OAC1C,CAIA,QAAA2B,GACI,OAAO4B,KAAKmM,OAASnM,KAAKmM,OAAOA,OAAOsD,YAAYzP,KAAKgL,OAAShL,KAAKsK,MAAMlM,UACjF,CAIA,UAAA6U,CAAWpD,EAAK3O,EAAKiJ,GACjB,IAAKnK,KAAKmM,OACN,OAAOnM,KAAKgT,MAAMhT,KAAKsK,MAAMiH,UAAU1B,EAAM,EAAI7P,KAAKsK,MAAMA,MAAMb,SAASlI,OAAS,EAAI,EAAGsO,EAAK3O,EAAKiJ,EAAMnK,KAAK+J,OACpH,IAAI,OAAEoC,GAAWnM,KAAKmM,OAClBnB,EAAQmB,EAAOyD,UAAU5P,KAAKgL,MAAQ,EAAGmB,EAAOA,OAAOnM,KAAKgL,MAAQ,GAAI6E,EAAK3O,EAAMlB,KAAKmM,OAAOW,MAAO3C,GAC1G,QAAIa,EAAQ,KAEZhL,KAAK0S,MAAM3R,KAAKf,KAAKgL,OACdhL,KAAK+S,SAAS/H,GACzB,CAKA,UAAAU,GAAe,OAAO1L,KAAKiT,WAAW,EAAG,EAAG,EAAwB,CAIpE,SAAA9B,GAAc,OAAOnR,KAAKiT,YAAY,EAAG,EAAG,EAAwB,CAIpE,UAAA7R,CAAWF,GAAO,OAAOlB,KAAKiT,WAAW,EAAG/R,EAAK,EAAqB,CAItE,WAAAgQ,CAAYhQ,GAAO,OAAOlB,KAAKiT,YAAY,EAAG/R,GAAM,EAAsB,CAQ1E,KAAAN,CAAMM,EAAKiJ,EAAMJ,EAAO/J,KAAK+J,MACzB,OAAK/J,KAAKmM,SAEHpC,EAAOR,EAASkI,iBAAyBzR,KAAKiT,WAAW,EAAG/R,EAAKiJ,GAD7DnK,KAAKgT,MAAMhT,KAAKsK,MAAM1J,MAAMM,EAAKiJ,EAAMJ,GAEtD,CAIA,MAAAgB,GACI,IAAK/K,KAAKmM,OACN,OAAOnM,KAAK4S,UAAW5S,KAAK+J,KAAOR,EAASgC,iBAAoBvL,KAAKsK,MAAM+G,QAAUrR,KAAKsK,MAAMS,QACpG,GAAI/K,KAAK0S,MAAMnR,OACX,OAAOvB,KAAK+S,SAAS/S,KAAK0S,MAAMzD,OACpC,IAAIlE,EAAU/K,KAAK+J,KAAOR,EAASgC,iBAAoBvL,KAAKmM,OAAOpB,OAAS/K,KAAKmM,OAAOpB,OAAOgH,wBAE/F,OADA/R,KAAKmM,OAAS,KACPnM,KAAK4S,UAAU7H,EAC1B,CAIA,OAAAmI,CAAQrD,GACJ,IAAK7P,KAAKmM,OACN,QAAQnM,KAAKsK,MAAM+G,SACbrR,KAAKgT,MAAMhT,KAAKsK,MAAMU,MAAQ,EAAI,KAC9BhL,KAAKsK,MAAM+G,QAAQE,UAAUvR,KAAKsK,MAAMU,MAAQ6E,EAAKA,EAAK,EAAG,EAAuB7P,KAAK+J,OACvG,IAAI,OAAEoC,GAAWnM,KAAKmM,OAAQgH,EAAInT,KAAK0S,MAAMnR,OAAS,EACtD,GAAIsO,EAAM,EAAG,CACT,IAAInD,EAAcyG,EAAI,EAAI,EAAInT,KAAK0S,MAAMS,GAAK,EAC9C,GAAInT,KAAKgL,OAAS0B,EACd,OAAO1M,KAAK+S,SAAS5G,EAAOyD,UAAUlD,EAAa1M,KAAKgL,OAAQ,EAAG,EAAG,GAC9E,KACK,CACD,IAAI4F,EAAQzE,EAAOA,OAAOnM,KAAKgL,MAAQ,GACvC,GAAI4F,GAASuC,EAAI,EAAIhH,EAAOA,OAAO5K,OAAS4K,EAAOA,OAAOnM,KAAK0S,MAAMS,GAAK,IACtE,OAAOnT,KAAK+S,SAASnC,EAC7B,CACA,OAAOuC,EAAI,GAAInT,KAAKgT,MAAMhT,KAAKmM,OAAOpB,OAAOwG,UAAUvR,KAAKmM,OAAOnB,MAAQ6E,EAAKA,EAAK,EAAG,EAAuB7P,KAAK+J,MACxH,CAIA,WAAA4B,GAAgB,OAAO3L,KAAKkT,QAAQ,EAAI,CAIxC,WAAA9B,GAAgB,OAAOpR,KAAKkT,SAAS,EAAI,CACzC,UAAAE,CAAWvD,GACP,IAAI7E,EAAOD,GAAQ,OAAEoB,GAAWnM,KAChC,GAAImM,EAAQ,CACR,GAAI0D,EAAM,GACN,GAAI7P,KAAKgL,MAAQmB,EAAOA,OAAOA,OAAO5K,OAClC,OAAO,OAGX,IAAK,IAAImH,EAAI,EAAGA,EAAI1I,KAAKgL,MAAOtC,IAC5B,GAAIyD,EAAOA,OAAOA,OAAOzD,EAAI,GAAK1I,KAAKgL,MACnC,OAAO,IAEhBA,QAAOD,UAAWoB,EACzB,OAEOnB,QAAOqG,QAAStG,GAAW/K,KAAKsK,OAEvC,KAAOS,IAAUC,QAAOqG,QAAStG,GAAWA,GACxC,GAAIC,GAAS,EACT,IAAK,IAAItC,EAAIsC,EAAQ6E,EAAK2B,EAAI3B,EAAM,GAAK,EAAI9E,EAAOT,MAAMb,SAASlI,OAAQmH,GAAK8I,EAAG9I,GAAKmH,EAAK,CACzF,IAAI1O,EAAQ4J,EAAOT,MAAMb,SAASf,GAClC,GAAK1I,KAAK+J,KAAOR,EAASgC,kBACtBpK,aAAiB4M,IAChB5M,EAAM1E,KAAK4L,aACZuJ,EAASzQ,GACT,OAAO,CACf,CAER,OAAO,CACX,CACA,IAAAkS,CAAKxD,EAAKjP,GACN,GAAIA,GAASZ,KAAKiT,WAAWpD,EAAK,EAAG,GACjC,OAAO,EACX,OAAS,CACL,GAAI7P,KAAKkT,QAAQrD,GACb,OAAO,EACX,GAAI7P,KAAKoT,WAAWvD,KAAS7P,KAAK+K,SAC9B,OAAO,CACf,CACJ,CAQA,IAAAmC,CAAKtM,GAAQ,GAAQ,OAAOZ,KAAKqT,KAAK,EAAGzS,EAAQ,CAOjD,IAAA0S,CAAK1S,GAAQ,GAAQ,OAAOZ,KAAKqT,MAAM,EAAGzS,EAAQ,CAMlD,MAAAyJ,CAAOnJ,EAAKiJ,EAAO,GAEf,MAAOnK,KAAK9B,MAAQ8B,KAAK1B,KACpB6L,EAAO,EAAInK,KAAK9B,MAAQgD,EAAMlB,KAAK9B,KAAOgD,KAC1CiJ,GAAQ,EAAInK,KAAK1B,IAAM4C,EAAMlB,KAAK1B,GAAK4C,KACnClB,KAAK+K,WAGd,KAAO/K,KAAKiT,WAAW,EAAG/R,EAAKiJ,KAC/B,OAAOnK,IACX,CAKA,QAAIH,GACA,IAAKG,KAAKmM,OACN,OAAOnM,KAAKsK,MAChB,IAAIiJ,EAAQvT,KAAK2S,WAAYtM,EAAS,KAAMwG,EAAQ,EACpD,GAAI0G,GAASA,EAAMnX,SAAW4D,KAAKmM,OAC/BrB,EAAM,IAAK,IAAIE,EAAQhL,KAAKgL,MAAOmI,EAAInT,KAAK0S,MAAMnR,OAAQ4R,GAAK,GAAI,CAC/D,IAAK,IAAI3H,EAAI+H,EAAO/H,EAAGA,EAAIA,EAAE6F,QACzB,GAAI7F,EAAER,OAASA,EAAO,CAClB,GAAIA,GAAShL,KAAKgL,MACd,OAAOQ,EACXnF,EAASmF,EACTqB,EAAQsG,EAAI,EACZ,MAAMrI,CACV,CACJE,EAAQhL,KAAK0S,QAAQS,EACzB,CAEJ,IAAK,IAAIzK,EAAImE,EAAOnE,EAAI1I,KAAK0S,MAAMnR,OAAQmH,IACvCrC,EAAS,IAAIqL,EAAW1R,KAAKmM,OAAQ9F,EAAQrG,KAAK0S,MAAMhK,IAC5D,OAAO1I,KAAK2S,WAAa,IAAIjB,EAAW1R,KAAKmM,OAAQ9F,EAAQrG,KAAKgL,MACtE,CAMA,QAAIhE,GACA,OAAOhH,KAAKmM,OAAS,KAAOnM,KAAKsK,MAAMA,KAC3C,CAOA,OAAA3J,CAAQC,EAAOyK,GACX,IAAK,IAAIwB,EAAQ,IAAK,CAClB,IAAI2G,GAAY,EAChB,GAAIxT,KAAKvD,KAAK4L,cAA+B,IAAhBzH,EAAMZ,MAAiB,CAChD,GAAIA,KAAK0L,aAAc,CACnBmB,IACA,QACJ,CACK7M,KAAKvD,KAAK4L,cACXmL,GAAY,EACpB,CACA,KACQA,GAAanI,GACbA,EAAMrL,MACVwT,EAAYxT,KAAKvD,KAAK4L,aAClBrI,KAAK2L,eAJJ,CAML,IAAKkB,EACD,OACJ7M,KAAK+K,SACL8B,IACA2G,GAAY,CAChB,CACJ,CACJ,CAMA,YAAAzC,CAAa3U,GACT,IAAK4D,KAAKmM,OACN,OAAO6E,EAAiBhR,KAAKH,KAAMzD,GACvC,IAAI,OAAE+P,GAAWnM,KAAKmM,QAAQ,MAAErD,GAAUqD,EAAO5M,IACjD,IAAK,IAAImJ,EAAItM,EAAQmF,OAAS,EAAG4R,EAAInT,KAAK0S,MAAMnR,OAAS,EAAGmH,GAAK,EAAGyK,IAAK,CACrE,GAAIA,EAAI,EACJ,OAAOnC,EAAiBhR,KAAKH,KAAMzD,EAASsM,GAChD,IAAIjM,EAAOqM,EAAMqD,EAAOA,OAAOnM,KAAK0S,MAAMS,KAC1C,IAAK1W,EAAK4L,YAAa,CACnB,GAAIjM,EAAQsM,IAAMtM,EAAQsM,IAAMjM,EAAK8K,KACjC,OAAO,EACXmB,GACJ,CACJ,CACA,OAAO,CACX,EAEJ,SAASkJ,EAAS5K,GACd,OAAOA,EAAKyC,SAASgK,MAAK/J,GAAMA,aAAcqE,IAAerE,EAAGjN,KAAK4L,aAAeuJ,EAASlI,IACjG,CAgOA,MAAMgK,EAAgB,IAAIrK,QAC1B,SAASmE,EAASmG,EAAa9T,GAC3B,IAAK8T,EAAYtL,aAAexI,aAAgBkO,GAAclO,EAAKpD,MAAQkX,EACvE,OAAO,EACX,IAAI3G,EAAO0G,EAAcxM,IAAIrH,GAC7B,GAAY,MAARmN,EAAc,CACdA,EAAO,EACP,IAAK,IAAI7L,KAAStB,EAAK4J,SAAU,CAC7B,GAAItI,EAAM1E,MAAQkX,KAAiBxS,aAAiBqI,GAAO,CACvDwD,EAAO,EACP,KACJ,CACAA,GAAQQ,EAASmG,EAAaxS,EAClC,CACAuS,EAAcnU,IAAIM,EAAMmN,EAC5B,CACA,OAAOA,CACX,CACA,SAASlB,EAET6H,EAEAlK,EAAUxI,EAEV/C,EAAMI,EAENwO,EAEAvL,EAEAqS,EAEAC,GACI,IAAIC,EAAQ,EACZ,IAAK,IAAIpL,EAAIxK,EAAMwK,EAAIpK,EAAIoK,IACvBoL,GAAStG,EAASmG,EAAalK,EAASf,IAC5C,IAAIqL,EAAW1D,KAAK2D,KAAc,IAARF,EAAe,GACrC9F,EAAgB,GAAIC,EAAiB,GA2BzC,OA1BA,SAASgG,EAAOxK,EAAUxI,EAAW/C,EAAMI,EAAI4V,GAC3C,IAAK,IAAIxL,EAAIxK,EAAMwK,EAAIpK,GAAK,CACxB,IAAI6V,EAAYzL,EAAG0L,EAAanT,EAAUyH,GAAI2L,EAAY7G,EAASmG,EAAalK,EAASf,IAEzF,IADAA,IACOA,EAAIpK,EAAIoK,IAAK,CAChB,IAAI4L,EAAW9G,EAASmG,EAAalK,EAASf,IAC9C,GAAI2L,EAAYC,GAAYP,EACxB,MACJM,GAAaC,CACjB,CACA,GAAI5L,GAAKyL,EAAY,EAAG,CACpB,GAAIE,EAAYN,EAAU,CACtB,IAAIQ,EAAO9K,EAAS0K,GACpBF,EAAOM,EAAK9K,SAAU8K,EAAKtT,UAAW,EAAGsT,EAAK9K,SAASlI,OAAQN,EAAUkT,GAAaD,GACtF,QACJ,CACAlG,EAAcjN,KAAK0I,EAAS0K,GAChC,KACK,CACD,IAAI5S,EAASN,EAAUyH,EAAI,GAAKe,EAASf,EAAI,GAAGnH,OAAS6S,EACzDpG,EAAcjN,KAAK+K,EAAa6H,EAAalK,EAAUxI,EAAWkT,EAAWzL,EAAG0L,EAAY7S,EAAQ,KAAMsS,GAC9G,CACA5F,EAAelN,KAAKqT,EAAaF,EAASpH,EAC9C,CACJ,CACAmH,CAAOxK,EAAUxI,EAAW/C,EAAMI,EAAI,IAC9BsV,GAASC,GAAQ7F,EAAeC,EAAgB1M,EAC5D,CAkKA,MAAMiT,EAWF,UAAAC,CAAWC,EAAOC,EAAWC,GAIzB,MAHoB,iBAATF,IACPA,EAAQ,IAAIG,EAAYH,IAC5BE,EAAUA,EAAwCA,EAAOrT,OAASqT,EAAOzS,KAAI0O,GAAK,IAAIlL,EAAMkL,EAAE3S,KAAM2S,EAAEvS,MAAO,CAAC,IAAIqH,EAAM,EAAG,IAAxG,CAAC,IAAIA,EAAM,EAAG+O,EAAMnT,SAChCvB,KAAK8U,YAAYJ,EAAOC,GAAa,GAAIC,EACpD,CAIA,KAAAlU,CAAMgU,EAAOC,EAAWC,GACpB,IAAIlU,EAAQV,KAAKyU,WAAWC,EAAOC,EAAWC,GAC9C,OAAS,CACL,IAAIG,EAAOrU,EAAMsU,UACjB,GAAID,EACA,OAAOA,CACf,CACJ,EAEJ,MAAMF,EACF,WAAAzU,CAAY6U,GACRjV,KAAKiV,OAASA,CAClB,CACA,UAAI1T,GAAW,OAAOvB,KAAKiV,OAAO1T,MAAQ,CAC1C,KAAA2T,CAAMhX,GAAQ,OAAO8B,KAAKiV,OAAOjF,MAAM9R,EAAO,CAC9C,cAAIiX,GAAe,OAAO,CAAO,CACjC,IAAAC,CAAKlX,EAAMI,GAAM,OAAO0B,KAAKiV,OAAOjF,MAAM9R,EAAMI,EAAK,EAuCpC,IAAIsH,EAAS,CAAEE,SAAS,ICrvD7C,MAAMuP,EAIF,WAAAjV,CAIA+R,EAKAO,EAIA4C,EAQAC,EAIArU,EAMAsU,EAOArJ,EASAsJ,EAIAC,EAIA7O,EAAY,EAQZkE,GACI/K,KAAKmS,EAAIA,EACTnS,KAAK0S,MAAQA,EACb1S,KAAKsV,MAAQA,EACbtV,KAAKuV,UAAYA,EACjBvV,KAAKkB,IAAMA,EACXlB,KAAKwV,MAAQA,EACbxV,KAAKmM,OAASA,EACdnM,KAAKyV,WAAaA,EAClBzV,KAAK0V,WAAaA,EAClB1V,KAAK6G,UAAYA,EACjB7G,KAAK+K,OAASA,CAClB,CAIA,QAAA3M,GACI,MAAO,IAAI4B,KAAK0S,MAAMrR,QAAO,CAACsU,EAAGjN,IAAMA,EAAI,GAAK,IAAGyG,OAAOnP,KAAKsV,WAAWtV,KAAKkB,MAAMlB,KAAKwV,MAAQ,IAAMxV,KAAKwV,MAAQ,IACzH,CAKA,YAAO1I,CAAMqF,EAAGmD,EAAOpU,EAAM,GACzB,IAAI0U,EAAKzD,EAAE1R,OAAOrE,QAClB,OAAO,IAAIiZ,EAAMlD,EAAG,GAAImD,EAAOpU,EAAKA,EAAK,EAAG,GAAI,EAAG0U,EAAK,IAAIC,EAAaD,EAAIA,EAAG9I,OAAS,KAAM,EAAG,KACtG,CAOA,WAAI1Q,GAAY,OAAO4D,KAAK0V,WAAa1V,KAAK0V,WAAWtZ,QAAU,IAAM,CAMzE,SAAA0Z,CAAUR,EAAOxI,GACb9M,KAAK0S,MAAM3R,KAAKf,KAAKsV,MAAOxI,EAAO9M,KAAKyV,WAAazV,KAAKmM,OAAO5K,QACjEvB,KAAKsV,MAAQA,CACjB,CAKA,MAAAS,CAAOC,GACH,IAAI9J,EACJ,IAAIW,EAAQmJ,GAAU,GAAkCvZ,EAAgB,MAATuZ,GAC3D,OAAEvV,GAAWT,KAAKmS,EAClBnS,KAAKuV,UAAYvV,KAAKkB,IAAM,IAC5BlB,KAAKiW,aAAajW,KAAKkB,KAC3B,IAAIgV,EAAQzV,EAAO0V,kBAAkB1Z,GAGrC,GAFIyZ,IACAlW,KAAKwV,OAASU,GACL,GAATrJ,EAOA,OANA7M,KAAK8V,UAAUrV,EAAO2V,QAAQpW,KAAKsV,MAAO7Y,GAAM,GAAOuD,KAAKuV,WAGxD9Y,EAAOgE,EAAO4V,eACdrW,KAAKsW,UAAU7Z,EAAMuD,KAAKuV,UAAWvV,KAAKuV,UAAW,GAAG,QAC5DvV,KAAKuW,cAAc9Z,EAAMuD,KAAKuV,WAQlC,IAAIvG,EAAOhP,KAAK0S,MAAMnR,OAAwB,GAAbsL,EAAQ,IAAoB,OAATmJ,EAAwC,EAAI,GAC5FlJ,EAAQkC,EAAOhP,KAAK0S,MAAM1D,EAAO,GAAKhP,KAAKmS,EAAEyC,OAAO,GAAG1W,KAAM8O,EAAOhN,KAAKuV,UAAYzI,EAIrFE,GAAQ,OAAqF,QAA5Cd,EAAKlM,KAAKmS,EAAE1R,OAAO2L,QAAQtD,MAAMrM,UAA0B,IAAPyP,OAAgB,EAASA,EAAG7D,eAC7HyE,GAAS9M,KAAKmS,EAAEqE,uBAChBxW,KAAKmS,EAAEsE,oBACPzW,KAAKmS,EAAEuE,qBAAuB1J,GAEzBhN,KAAKmS,EAAEuE,qBAAuB1J,IACnChN,KAAKmS,EAAEsE,kBAAoB,EAC3BzW,KAAKmS,EAAEqE,sBAAwB1J,EAC/B9M,KAAKmS,EAAEuE,qBAAuB1J,IAGtC,IAAIyI,EAAazG,EAAOhP,KAAK0S,MAAM1D,EAAO,GAAK,EAAG2H,EAAQ3W,KAAKyV,WAAazV,KAAKmM,OAAO5K,OAASkU,EAEjG,GAAIhZ,EAAOgE,EAAO4V,eAA2B,OAATL,EAA0C,CAC1E,IAAI9U,EAAMT,EAAOmW,UAAU5W,KAAKsV,MAAO,GAA6BtV,KAAKkB,IAAMlB,KAAKuV,UACpFvV,KAAKsW,UAAU7Z,EAAMqQ,EAAO5L,EAAKyV,EAAQ,GAAG,EAChD,CACA,GAAa,OAATX,EACAhW,KAAKsV,MAAQtV,KAAK0S,MAAM1D,OAEvB,CACD,IAAI6H,EAAc7W,KAAK0S,MAAM1D,EAAO,GACpChP,KAAKsV,MAAQ7U,EAAO2V,QAAQS,EAAapa,GAAM,EACnD,CACA,KAAOuD,KAAK0S,MAAMnR,OAASyN,GACvBhP,KAAK0S,MAAMzD,MACfjP,KAAKuW,cAAc9Z,EAAMqQ,EAC7B,CAKA,SAAAwJ,CAAUQ,EAAMhK,EAAOC,EAAKC,EAAO,EAAG+J,GAAW,GAC7C,GAAY,GAARD,KACE9W,KAAK0S,MAAMnR,QAAUvB,KAAK0S,MAAM1S,KAAK0S,MAAMnR,OAAS,GAAKvB,KAAKmM,OAAO5K,OAASvB,KAAKyV,YAAa,CAElG,IAAIvD,EAAMlS,KAAM2H,EAAM3H,KAAKmM,OAAO5K,OAKlC,GAJW,GAAPoG,GAAYuK,EAAInH,SAChBpD,EAAMuK,EAAIuD,WAAavD,EAAInH,OAAO0K,WAClCvD,EAAMA,EAAInH,QAEVpD,EAAM,GAA4B,GAAvBuK,EAAI/F,OAAOxE,EAAM,IAA0BuK,EAAI/F,OAAOxE,EAAM,IAAM,EAAG,CAChF,GAAImF,GAASC,EACT,OACJ,GAAImF,EAAI/F,OAAOxE,EAAM,IAAMmF,EAEvB,YADAoF,EAAI/F,OAAOxE,EAAM,GAAKoF,EAG9B,CACJ,CACA,GAAKgK,GAAY/W,KAAKkB,KAAO6L,EAGxB,CACD,IAAI/B,EAAQhL,KAAKmM,OAAO5K,OACxB,GAAIyJ,EAAQ,GAA+B,GAA1BhL,KAAKmM,OAAOnB,EAAQ,GACjC,KAAOA,EAAQ,GAAKhL,KAAKmM,OAAOnB,EAAQ,GAAK+B,GAEzC/M,KAAKmM,OAAOnB,GAAShL,KAAKmM,OAAOnB,EAAQ,GACzChL,KAAKmM,OAAOnB,EAAQ,GAAKhL,KAAKmM,OAAOnB,EAAQ,GAC7ChL,KAAKmM,OAAOnB,EAAQ,GAAKhL,KAAKmM,OAAOnB,EAAQ,GAC7ChL,KAAKmM,OAAOnB,EAAQ,GAAKhL,KAAKmM,OAAOnB,EAAQ,GAC7CA,GAAS,EACLgC,EAAO,IACPA,GAAQ,GAEpBhN,KAAKmM,OAAOnB,GAAS8L,EACrB9W,KAAKmM,OAAOnB,EAAQ,GAAK8B,EACzB9M,KAAKmM,OAAOnB,EAAQ,GAAK+B,EACzB/M,KAAKmM,OAAOnB,EAAQ,GAAKgC,CAC7B,MAnBIhN,KAAKmM,OAAOpL,KAAK+V,EAAMhK,EAAOC,EAAKC,EAoB3C,CAKA,KAAAgK,CAAMhB,EAAQvZ,EAAMqQ,EAAOC,GACvB,GAAa,OAATiJ,EACAhW,KAAK8V,UAAmB,MAATE,EAAuChW,KAAKkB,UAE1D,GAAc,OAAT8U,EAaNhW,KAAKkB,IAAM6L,EACX/M,KAAKiX,aAAaxa,EAAMqQ,GACpBrQ,GAAQuD,KAAKmS,EAAE1R,OAAOyW,SACtBlX,KAAKmM,OAAOpL,KAAKtE,EAAMqQ,EAAOC,EAAK,OAhBY,CACnD,IAAIoK,EAAYnB,GAAQ,OAAEvV,GAAWT,KAAKmS,GACtCpF,EAAM/M,KAAKkB,KAAOzE,GAAQgE,EAAOyW,WACjClX,KAAKkB,IAAM6L,EACNtM,EAAOmW,UAAUO,EAAW,KAC7BnX,KAAKuV,UAAYxI,IAEzB/M,KAAK8V,UAAUqB,EAAWrK,GAC1B9M,KAAKiX,aAAaxa,EAAMqQ,GACpBrQ,GAAQgE,EAAOyW,SACflX,KAAKmM,OAAOpL,KAAKtE,EAAMqQ,EAAOC,EAAK,EAC3C,CAOJ,CAKA,KAAAqK,CAAMpB,EAAQ9I,EAAMmK,EAAWC,GACd,MAATtB,EACAhW,KAAK+V,OAAOC,GAEZhW,KAAKgX,MAAMhB,EAAQ9I,EAAMmK,EAAWC,EAC5C,CAKA,OAAAC,CAAQla,EAAO6P,GACX,IAAIlC,EAAQhL,KAAKmS,EAAE7F,OAAO/K,OAAS,GAC/ByJ,EAAQ,GAAKhL,KAAKmS,EAAE7F,OAAOtB,IAAU3N,KACrC2C,KAAKmS,EAAE7F,OAAOvL,KAAK1D,GACnB2N,KAEJ,IAAI8B,EAAQ9M,KAAKkB,IACjBlB,KAAKuV,UAAYvV,KAAKkB,IAAM4L,EAAQzP,EAAMkE,OAC1CvB,KAAK8V,UAAU5I,EAAMJ,GACrB9M,KAAKmM,OAAOpL,KAAKiK,EAAO8B,EAAO9M,KAAKuV,WAAY,GAC5CvV,KAAK0V,YACL1V,KAAKwX,cAAcxX,KAAK0V,WAAW+B,QAAQC,MAAM1X,KAAK0V,WAAWtZ,QAASiB,EAAO2C,KAAMA,KAAKmS,EAAEwF,OAAOC,MAAM5X,KAAKkB,IAAM7D,EAAMkE,SACpI,CAOA,KAAAiF,GACI,IAAIuE,EAAS/K,KACT6X,EAAM9M,EAAOoB,OAAO5K,OAKxB,KAAOsW,EAAM,GAAK9M,EAAOoB,OAAO0L,EAAM,GAAK9M,EAAOwK,WAC9CsC,GAAO,EACX,IAAI1L,EAASpB,EAAOoB,OAAO6D,MAAM6H,GAAM7I,EAAOjE,EAAO0K,WAAaoC,EAElE,KAAO9M,GAAUiE,GAAQjE,EAAO0K,YAC5B1K,EAASA,EAAOA,OACpB,OAAO,IAAIsK,EAAMrV,KAAKmS,EAAGnS,KAAK0S,MAAM1C,QAAShQ,KAAKsV,MAAOtV,KAAKuV,UAAWvV,KAAKkB,IAAKlB,KAAKwV,MAAOrJ,EAAQ6C,EAAMhP,KAAK0V,WAAY1V,KAAK6G,UAAWkE,EAClJ,CAKA,eAAA+M,CAAgB5K,EAAMoK,GAClB,IAAIS,EAAS7K,GAAQlN,KAAKmS,EAAE1R,OAAOyW,QAC/Ba,GACA/X,KAAKsW,UAAUpJ,EAAMlN,KAAKkB,IAAKoW,EAAS,GAC5CtX,KAAKsW,UAAU,EAAkBtW,KAAKkB,IAAKoW,EAASS,EAAS,EAAI,GACjE/X,KAAKkB,IAAMlB,KAAKuV,UAAY+B,EAC5BtX,KAAKwV,OAAS,GAClB,CAOA,QAAAwC,CAASlB,GACL,IAAK,IAAImB,EAAM,IAAIC,EAAelY,QAAS,CACvC,IAAIgW,EAAShW,KAAKmS,EAAE1R,OAAO0X,UAAUF,EAAI3C,MAAO,IAAqCtV,KAAKmS,EAAE1R,OAAO2X,UAAUH,EAAI3C,MAAOwB,GACxH,GAAc,GAAVd,EACA,OAAO,EACX,KAAc,MAATA,GACD,OAAO,EACXiC,EAAIlC,OAAOC,EACf,CACJ,CAMA,eAAAqC,CAAgBnL,GACZ,GAAIlN,KAAK0S,MAAMnR,QAAU,IACrB,MAAO,GACX,IAAI+W,EAAatY,KAAKmS,EAAE1R,OAAO6X,WAAWtY,KAAKsV,OAC/C,GAAIgD,EAAW/W,OAAS,GAAgCvB,KAAK0S,MAAMnR,QAAU,IAA0C,CACnH,IAAIgX,EAAO,GACX,IAAK,IAAWC,EAAP9P,EAAI,EAAMA,EAAI4P,EAAW/W,OAAQmH,GAAK,GACtC8P,EAAIF,EAAW5P,EAAI,KAAO1I,KAAKsV,OAAStV,KAAKmS,EAAE1R,OAAO2X,UAAUI,EAAGtL,IACpEqL,EAAKxX,KAAKuX,EAAW5P,GAAI8P,GAEjC,GAAIxY,KAAK0S,MAAMnR,OAAS,IACpB,IAAK,IAAImH,EAAI,EAAG6P,EAAKhX,OAAS,GAAgCmH,EAAI4P,EAAW/W,OAAQmH,GAAK,EAAG,CACzF,IAAI8P,EAAIF,EAAW5P,EAAI,GAClB6P,EAAK9E,MAAK,CAACgF,EAAG/P,IAAW,EAAJA,GAAU+P,GAAKD,KACrCD,EAAKxX,KAAKuX,EAAW5P,GAAI8P,EACjC,CACJF,EAAaC,CACjB,CACA,IAAIlS,EAAS,GACb,IAAK,IAAIqC,EAAI,EAAGA,EAAI4P,EAAW/W,QAAU8E,EAAO9E,OAAS,EAAyBmH,GAAK,EAAG,CACtF,IAAI8P,EAAIF,EAAW5P,EAAI,GACvB,GAAI8P,GAAKxY,KAAKsV,MACV,SACJ,IAAI5C,EAAQ1S,KAAKwG,QACjBkM,EAAMoD,UAAU0C,EAAGxY,KAAKkB,KACxBwR,EAAM4D,UAAU,EAAkB5D,EAAMxR,IAAKwR,EAAMxR,IAAK,GAAG,GAC3DwR,EAAMuE,aAAaqB,EAAW5P,GAAI1I,KAAKkB,KACvCwR,EAAM6C,UAAYvV,KAAKkB,IACvBwR,EAAM8C,OAAS,IACfnP,EAAOtF,KAAK2R,EAChB,CACA,OAAOrM,CACX,CAMA,WAAAqS,GACI,IAAI,OAAEjY,GAAWT,KAAKmS,EAClB4D,EAAStV,EAAO0X,UAAUnY,KAAKsV,MAAO,GAC1C,KAAc,MAATS,GACD,OAAO,EACX,IAAKtV,EAAOkY,YAAY3Y,KAAKsV,MAAOS,GAAS,CACzC,IAAIlJ,EAAQkJ,GAAU,GAAkCe,EAAgB,MAATf,EAC3DxZ,EAASyD,KAAK0S,MAAMnR,OAAiB,EAARsL,EACjC,GAAItQ,EAAS,GAAKkE,EAAO2V,QAAQpW,KAAK0S,MAAMnW,GAASua,GAAM,GAAS,EAAG,CACnE,IAAI8B,EAAS5Y,KAAK6Y,sBAClB,GAAc,MAAVD,EACA,OAAO,EACX7C,EAAS6C,CACb,CACA5Y,KAAKsW,UAAU,EAAkBtW,KAAKkB,IAAKlB,KAAKkB,IAAK,GAAG,GACxDlB,KAAKwV,OAAS,GAClB,CAGA,OAFAxV,KAAKuV,UAAYvV,KAAKkB,IACtBlB,KAAK+V,OAAOA,IACL,CACX,CAMA,mBAAA8C,GACI,IAAI,OAAEpY,GAAWT,KAAKmS,EAAG2G,EAAO,GAC5BC,EAAU,CAACzD,EAAOzI,KAClB,IAAIiM,EAAKhY,SAASwU,GAGlB,OADAwD,EAAK/X,KAAKuU,GACH7U,EAAOuY,WAAW1D,GAAQU,IAC7B,GAAa,OAATA,QACC,GAAa,MAATA,EAAwC,CAC7C,IAAIiD,GAAUjD,GAAU,IAAoCnJ,EAC5D,GAAIoM,EAAS,EAAG,CACZ,IAAInC,EAAgB,MAATd,EAAuCzZ,EAASyD,KAAK0S,MAAMnR,OAAkB,EAAT0X,EAC/E,GAAI1c,GAAU,GAAKkE,EAAO2V,QAAQpW,KAAK0S,MAAMnW,GAASua,GAAM,IAAU,EAClE,OAAQmC,GAAU,GAAoC,MAAgCnC,CAC9F,CACJ,KACK,CACD,IAAInO,EAAQoQ,EAAQ/C,EAAQnJ,EAAQ,GACpC,GAAa,MAATlE,EACA,OAAOA,CACf,IACF,EAEN,OAAOoQ,EAAQ/Y,KAAKsV,MAAO,EAC/B,CAIA,QAAA4D,GACI,MAAQlZ,KAAKmS,EAAE1R,OAAOmW,UAAU5W,KAAKsV,MAAO,IACxC,IAAKtV,KAAK0Y,cAAe,CACrB1Y,KAAKsW,UAAU,EAAkBtW,KAAKkB,IAAKlB,KAAKkB,IAAK,GAAG,GACxD,KACJ,CAEJ,OAAOlB,IACX,CAMA,WAAImZ,GACA,GAAyB,GAArBnZ,KAAK0S,MAAMnR,OACX,OAAO,EACX,IAAI,OAAEd,GAAWT,KAAKmS,EACtB,OAAgF,OAAzE1R,EAAOwL,KAAKxL,EAAO0X,UAAUnY,KAAKsV,MAAO,MAC3C7U,EAAO0X,UAAUnY,KAAKsV,MAAO,EACtC,CAMA,OAAA8D,GACIpZ,KAAKsW,UAAU,EAAkBtW,KAAKkB,IAAKlB,KAAKkB,IAAK,GAAG,GACxDlB,KAAKsV,MAAQtV,KAAK0S,MAAM,GACxB1S,KAAK0S,MAAMnR,OAAS,CACxB,CAIA,SAAA8X,CAAUC,GACN,GAAItZ,KAAKsV,OAASgE,EAAMhE,OAAStV,KAAK0S,MAAMnR,QAAU+X,EAAM5G,MAAMnR,OAC9D,OAAO,EACX,IAAK,IAAImH,EAAI,EAAGA,EAAI1I,KAAK0S,MAAMnR,OAAQmH,GAAK,EACxC,GAAI1I,KAAK0S,MAAMhK,IAAM4Q,EAAM5G,MAAMhK,GAC7B,OAAO,EACf,OAAO,CACX,CAIA,UAAIjI,GAAW,OAAOT,KAAKmS,EAAE1R,MAAQ,CAKrC,cAAA8Y,CAAeC,GAAa,OAAOxZ,KAAKmS,EAAE1R,OAAOgZ,QAAQjS,MAAMgS,EAAY,CAC3E,YAAAvC,CAAaH,EAAMhK,GACX9M,KAAK0V,YACL1V,KAAKwX,cAAcxX,KAAK0V,WAAW+B,QAAQT,MAAMhX,KAAK0V,WAAWtZ,QAAS0a,EAAM9W,KAAMA,KAAKmS,EAAEwF,OAAOC,MAAM9K,IAClH,CACA,aAAAyJ,CAAcO,EAAMhK,GACZ9M,KAAK0V,YACL1V,KAAKwX,cAAcxX,KAAK0V,WAAW+B,QAAQ1B,OAAO/V,KAAK0V,WAAWtZ,QAAS0a,EAAM9W,KAAMA,KAAKmS,EAAEwF,OAAOC,MAAM9K,IACnH,CAIA,WAAA4M,GACI,IAAIjL,EAAOzO,KAAKmM,OAAO5K,OAAS,GAC5BkN,EAAO,IAA2B,GAAtBzO,KAAKmM,OAAOsC,KACxBzO,KAAKmM,OAAOpL,KAAKf,KAAK0V,WAAWiE,KAAM3Z,KAAKkB,IAAKlB,KAAKkB,KAAM,EACpE,CAIA,aAAA0Y,GACI,IAAInL,EAAOzO,KAAKmM,OAAO5K,OAAS,GAC5BkN,EAAO,IAA2B,GAAtBzO,KAAKmM,OAAOsC,KACxBzO,KAAKmM,OAAOpL,KAAKf,KAAK6G,UAAW7G,KAAKkB,IAAKlB,KAAKkB,KAAM,EAC9D,CACA,aAAAsW,CAAcpb,GACV,GAAIA,GAAW4D,KAAK0V,WAAWtZ,QAAS,CACpC,IAAIyd,EAAQ,IAAIhE,EAAa7V,KAAK0V,WAAW+B,QAASrb,GAClDyd,EAAMF,MAAQ3Z,KAAK0V,WAAWiE,MAC9B3Z,KAAK0Z,cACT1Z,KAAK0V,WAAamE,CACtB,CACJ,CAIA,YAAA5D,CAAapP,GACLA,EAAY7G,KAAK6G,YACjB7G,KAAK4Z,gBACL5Z,KAAK6G,UAAYA,EAEzB,CAIA,KAAAiT,GACQ9Z,KAAK0V,YAAc1V,KAAK0V,WAAW+B,QAAQsC,QAC3C/Z,KAAK0Z,cACL1Z,KAAK6G,UAAY,GACjB7G,KAAK4Z,eACb,EAEJ,MAAM/D,EACF,WAAAzV,CAAYqX,EAASrb,GACjB4D,KAAKyX,QAAUA,EACfzX,KAAK5D,QAAUA,EACf4D,KAAK2Z,KAAOlC,EAAQsC,OAAStC,EAAQkC,KAAKvd,GAAW,CACzD,EAIJ,MAAM8b,EACF,WAAA9X,CAAY0M,GACR9M,KAAK8M,MAAQA,EACb9M,KAAKsV,MAAQxI,EAAMwI,MACnBtV,KAAK0S,MAAQ5F,EAAM4F,MACnB1S,KAAKgP,KAAOhP,KAAK0S,MAAMnR,MAC3B,CACA,MAAAwU,CAAOC,GACH,IAAIc,EAAgB,MAATd,EAAuCnJ,EAAQmJ,GAAU,GACvD,GAATnJ,GACI7M,KAAK0S,OAAS1S,KAAK8M,MAAM4F,QACzB1S,KAAK0S,MAAQ1S,KAAK0S,MAAM1C,SAC5BhQ,KAAK0S,MAAM3R,KAAKf,KAAKsV,MAAO,EAAG,GAC/BtV,KAAKgP,MAAQ,GAGbhP,KAAKgP,MAAsB,GAAbnC,EAAQ,GAE1B,IAAImN,EAAOha,KAAK8M,MAAMqF,EAAE1R,OAAO2V,QAAQpW,KAAK0S,MAAM1S,KAAKgP,KAAO,GAAI8H,GAAM,GACxE9W,KAAKsV,MAAQ0E,CACjB,EAIJ,MAAMC,EACF,WAAA7Z,CAAYsS,EAAOxR,EAAK8J,GACpBhL,KAAK0S,MAAQA,EACb1S,KAAKkB,IAAMA,EACXlB,KAAKgL,MAAQA,EACbhL,KAAKmM,OAASuG,EAAMvG,OACF,GAAdnM,KAAKgL,OACLhL,KAAKka,WACb,CACA,aAAO5S,CAAOoL,EAAOxR,EAAMwR,EAAM+C,WAAa/C,EAAMvG,OAAO5K,QACvD,OAAO,IAAI0Y,EAAkBvH,EAAOxR,EAAKA,EAAMwR,EAAM+C,WACzD,CACA,SAAAyE,GACI,IAAIhN,EAAOlN,KAAK0S,MAAM3H,OACV,MAARmC,IACAlN,KAAKgL,MAAQhL,KAAK0S,MAAM+C,WAAavI,EAAKuI,WAC1CzV,KAAK0S,MAAQxF,EACblN,KAAKmM,OAASe,EAAKf,OAE3B,CACA,MAAI9Q,GAAO,OAAO2E,KAAKmM,OAAOnM,KAAKgL,MAAQ,EAAI,CAC/C,SAAI8B,GAAU,OAAO9M,KAAKmM,OAAOnM,KAAKgL,MAAQ,EAAI,CAClD,OAAI+B,GAAQ,OAAO/M,KAAKmM,OAAOnM,KAAKgL,MAAQ,EAAI,CAChD,QAAIgC,GAAS,OAAOhN,KAAKmM,OAAOnM,KAAKgL,MAAQ,EAAI,CACjD,IAAAkC,GACIlN,KAAKgL,OAAS,EACdhL,KAAKkB,KAAO,EACM,GAAdlB,KAAKgL,OACLhL,KAAKka,WACb,CACA,IAAA7M,GACI,OAAO,IAAI4M,EAAkBja,KAAK0S,MAAO1S,KAAKkB,IAAKlB,KAAKgL,MAC5D,EAKJ,SAASmP,EAAYzF,EAAO0F,EAAOxM,aAC/B,GAAoB,iBAAT8G,EACP,OAAOA,EACX,IAAI2F,EAAQ,KACZ,IAAK,IAAInZ,EAAM,EAAGoZ,EAAM,EAAGpZ,EAAMwT,EAAMnT,QAAS,CAC5C,IAAIlE,EAAQ,EACZ,OAAS,CACL,IAAI6P,EAAOwH,EAAM6F,WAAWrZ,KAAQsZ,GAAO,EAC3C,GAAY,KAARtN,EAAqC,CACrC7P,EAAQ,MACR,KACJ,CACI6P,GAAQ,IACRA,IACAA,GAAQ,IACRA,IACJ,IAAIuN,EAAQvN,EAAO,GAMnB,GALIuN,GAAS,KACTA,GAAS,GACTD,GAAO,GAEXnd,GAASod,EACLD,EACA,MACJnd,GAAS,EACb,CACIgd,EACAA,EAAMC,KAASjd,EAEfgd,EAAQ,IAAID,EAAK/c,EACzB,CACA,OAAOgd,CACX,CAEA,MAAMK,EACF,WAAAta,GACIJ,KAAK8M,OAAS,EACd9M,KAAK3C,OAAS,EACd2C,KAAK+M,KAAO,EACZ/M,KAAK2a,UAAY,EACjB3a,KAAK6G,UAAY,EACjB7G,KAAK4a,KAAO,EACZ5a,KAAK5D,QAAU,CACnB,EAEJ,MAAMye,EAAY,IAAIH,EAOtB,MAAMI,EAIF,WAAA1a,CAIAsU,EAIAE,GACI5U,KAAK0U,MAAQA,EACb1U,KAAK4U,OAASA,EAId5U,KAAKkV,MAAQ,GAIblV,KAAK+a,SAAW,EAIhB/a,KAAKgb,OAAS,GACdhb,KAAKib,UAAY,EAKjBjb,KAAKkN,MAAQ,EAIblN,KAAKkb,MAAQL,EACb7a,KAAKmb,WAAa,EAClBnb,KAAKkB,IAAMlB,KAAKob,SAAWxG,EAAO,GAAG1W,KACrC8B,KAAKqb,MAAQzG,EAAO,GACpB5U,KAAK+M,IAAM6H,EAAOA,EAAOrT,OAAS,GAAGjD,GACrC0B,KAAKsb,UACT,CAIA,aAAAC,CAAcrH,EAAQsH,GAClB,IAAIH,EAAQrb,KAAKqb,MAAOrQ,EAAQhL,KAAKmb,WACjCja,EAAMlB,KAAKkB,IAAMgT,EACrB,KAAOhT,EAAMma,EAAMnd,MAAM,CACrB,IAAK8M,EACD,OAAO,KACX,IAAIkC,EAAOlN,KAAK4U,SAAS5J,GACzB9J,GAAOma,EAAMnd,KAAOgP,EAAK5O,GACzB+c,EAAQnO,CACZ,CACA,KAAOsO,EAAQ,EAAIta,EAAMma,EAAM/c,GAAK4C,GAAOma,EAAM/c,IAAI,CACjD,GAAI0M,GAAShL,KAAK4U,OAAOrT,OAAS,EAC9B,OAAO,KACX,IAAI2L,EAAOlN,KAAK4U,SAAS5J,GACzB9J,GAAOgM,EAAKhP,KAAOmd,EAAM/c,GACzB+c,EAAQnO,CACZ,CACA,OAAOhM,CACX,CAIA,OAAAua,CAAQva,GACJ,GAAIA,GAAOlB,KAAKqb,MAAMnd,MAAQgD,EAAMlB,KAAKqb,MAAM/c,GAC3C,OAAO4C,EACX,IAAK,IAAIma,KAASrb,KAAK4U,OACnB,GAAIyG,EAAM/c,GAAK4C,EACX,OAAOmP,KAAKC,IAAIpP,EAAKma,EAAMnd,MACnC,OAAO8B,KAAK+M,GAChB,CAYA,IAAA2O,CAAKxH,GACD,IAAkChT,EAAKmF,EAAnCsV,EAAM3b,KAAK+a,SAAW7G,EAC1B,GAAIyH,GAAO,GAAKA,EAAM3b,KAAKkV,MAAM3T,OAC7BL,EAAMlB,KAAKkB,IAAMgT,EACjB7N,EAASrG,KAAKkV,MAAMqF,WAAWoB,OAE9B,CACD,IAAIC,EAAW5b,KAAKub,cAAcrH,EAAQ,GAC1C,GAAgB,MAAZ0H,EACA,OAAQ,EAEZ,GADA1a,EAAM0a,EACF1a,GAAOlB,KAAKib,WAAa/Z,EAAMlB,KAAKib,UAAYjb,KAAKgb,OAAOzZ,OAC5D8E,EAASrG,KAAKgb,OAAOT,WAAWrZ,EAAMlB,KAAKib,eAE1C,CACD,IAAIvS,EAAI1I,KAAKmb,WAAYE,EAAQrb,KAAKqb,MACtC,KAAOA,EAAM/c,IAAM4C,GACfma,EAAQrb,KAAK4U,SAASlM,GAC1B1I,KAAKgb,OAAShb,KAAK0U,MAAMQ,MAAMlV,KAAKib,UAAY/Z,GAC5CA,EAAMlB,KAAKgb,OAAOzZ,OAAS8Z,EAAM/c,KACjC0B,KAAKgb,OAAShb,KAAKgb,OAAOhL,MAAM,EAAGqL,EAAM/c,GAAK4C,IAClDmF,EAASrG,KAAKgb,OAAOT,WAAW,EACpC,CACJ,CAGA,OAFIrZ,GAAOlB,KAAKkb,MAAMrU,YAClB7G,KAAKkb,MAAMrU,UAAY3F,EAAM,GAC1BmF,CACX,CAMA,WAAAwV,CAAYX,EAAOY,EAAY,GAC3B,IAAI/O,EAAM+O,EAAY9b,KAAKub,cAAcO,GAAY,GAAK9b,KAAKkB,IAC/D,GAAW,MAAP6L,GAAeA,EAAM/M,KAAKkb,MAAMpO,MAChC,MAAM,IAAI3G,WAAW,2BACzBnG,KAAKkb,MAAM7d,MAAQ6d,EACnBlb,KAAKkb,MAAMnO,IAAMA,CACrB,CAIA,aAAAgP,CAAcb,EAAOrN,GACjB7N,KAAKkb,MAAM7d,MAAQ6d,EACnBlb,KAAKkb,MAAMnO,IAAMc,CACrB,CACA,QAAAmO,GACI,GAAIhc,KAAKkB,KAAOlB,KAAKib,WAAajb,KAAKkB,IAAMlB,KAAKib,UAAYjb,KAAKgb,OAAOzZ,OAAQ,CAC9E,IAAI,MAAE2T,EAAK,SAAEkG,GAAapb,KAC1BA,KAAKkV,MAAQlV,KAAKgb,OAClBhb,KAAKob,SAAWpb,KAAKib,UACrBjb,KAAKgb,OAAS9F,EACdlV,KAAKib,UAAYG,EACjBpb,KAAK+a,SAAW/a,KAAKkB,IAAMlB,KAAKob,QACpC,KACK,CACDpb,KAAKgb,OAAShb,KAAKkV,MACnBlV,KAAKib,UAAYjb,KAAKob,SACtB,IAAIa,EAAYjc,KAAK0U,MAAMQ,MAAMlV,KAAKkB,KAClC6L,EAAM/M,KAAKkB,IAAM+a,EAAU1a,OAC/BvB,KAAKkV,MAAQnI,EAAM/M,KAAKqb,MAAM/c,GAAK2d,EAAUjM,MAAM,EAAGhQ,KAAKqb,MAAM/c,GAAK0B,KAAKkB,KAAO+a,EAClFjc,KAAKob,SAAWpb,KAAKkB,IACrBlB,KAAK+a,SAAW,CACpB,CACJ,CACA,QAAAO,GACI,OAAItb,KAAK+a,UAAY/a,KAAKkV,MAAM3T,SAC5BvB,KAAKgc,WACDhc,KAAK+a,UAAY/a,KAAKkV,MAAM3T,QACrBvB,KAAKkN,MAAQ,EAErBlN,KAAKkN,KAAOlN,KAAKkV,MAAMqF,WAAWva,KAAK+a,SAClD,CAKA,OAAA/F,CAAQnC,EAAI,GAER,IADA7S,KAAK+a,UAAYlI,EACV7S,KAAKkB,IAAM2R,GAAK7S,KAAKqb,MAAM/c,IAAI,CAClC,GAAI0B,KAAKmb,YAAcnb,KAAK4U,OAAOrT,OAAS,EACxC,OAAOvB,KAAKkc,UAChBrJ,GAAK7S,KAAKqb,MAAM/c,GAAK0B,KAAKkB,IAC1BlB,KAAKqb,MAAQrb,KAAK4U,SAAS5U,KAAKmb,YAChCnb,KAAKkB,IAAMlB,KAAKqb,MAAMnd,IAC1B,CAIA,OAHA8B,KAAKkB,KAAO2R,EACR7S,KAAKkB,KAAOlB,KAAKkb,MAAMrU,YACvB7G,KAAKkb,MAAMrU,UAAY7G,KAAKkB,IAAM,GAC/BlB,KAAKsb,UAChB,CACA,OAAAY,GAII,OAHAlc,KAAKkB,IAAMlB,KAAKob,SAAWpb,KAAK+M,IAChC/M,KAAKqb,MAAQrb,KAAK4U,OAAO5U,KAAKmb,WAAanb,KAAK4U,OAAOrT,OAAS,GAChEvB,KAAKkV,MAAQ,GACNlV,KAAKkN,MAAQ,CACxB,CAIA,KAAA0K,CAAM1W,EAAKga,GAUP,GATIA,GACAlb,KAAKkb,MAAQA,EACbA,EAAMpO,MAAQ5L,EACdga,EAAMrU,UAAY3F,EAAM,EACxBga,EAAM7d,MAAQ6d,EAAMP,UAAY,GAGhC3a,KAAKkb,MAAQL,EAEb7a,KAAKkB,KAAOA,EAAK,CAEjB,GADAlB,KAAKkB,IAAMA,EACPA,GAAOlB,KAAK+M,IAEZ,OADA/M,KAAKkc,UACElc,KAEX,KAAOkB,EAAMlB,KAAKqb,MAAMnd,MACpB8B,KAAKqb,MAAQrb,KAAK4U,SAAS5U,KAAKmb,YACpC,KAAOja,GAAOlB,KAAKqb,MAAM/c,IACrB0B,KAAKqb,MAAQrb,KAAK4U,SAAS5U,KAAKmb,YAChCja,GAAOlB,KAAKob,UAAYla,EAAMlB,KAAKob,SAAWpb,KAAKkV,MAAM3T,OACzDvB,KAAK+a,SAAW7Z,EAAMlB,KAAKob,UAG3Bpb,KAAKkV,MAAQ,GACblV,KAAK+a,SAAW,GAEpB/a,KAAKsb,UACT,CACA,OAAOtb,IACX,CAIA,IAAAoV,CAAKlX,EAAMI,GACP,GAAIJ,GAAQ8B,KAAKob,UAAY9c,GAAM0B,KAAKob,SAAWpb,KAAKkV,MAAM3T,OAC1D,OAAOvB,KAAKkV,MAAMlF,MAAM9R,EAAO8B,KAAKob,SAAU9c,EAAK0B,KAAKob,UAC5D,GAAIld,GAAQ8B,KAAKib,WAAa3c,GAAM0B,KAAKib,UAAYjb,KAAKgb,OAAOzZ,OAC7D,OAAOvB,KAAKgb,OAAOhL,MAAM9R,EAAO8B,KAAKib,UAAW3c,EAAK0B,KAAKib,WAC9D,GAAI/c,GAAQ8B,KAAKqb,MAAMnd,MAAQI,GAAM0B,KAAKqb,MAAM/c,GAC5C,OAAO0B,KAAK0U,MAAMU,KAAKlX,EAAMI,GACjC,IAAI+H,EAAS,GACb,IAAK,IAAIwK,KAAK7Q,KAAK4U,OAAQ,CACvB,GAAI/D,EAAE3S,MAAQI,EACV,MACAuS,EAAEvS,GAAKJ,IACPmI,GAAUrG,KAAK0U,MAAMU,KAAK/E,KAAKC,IAAIO,EAAE3S,KAAMA,GAAOmS,KAAK8L,IAAItL,EAAEvS,GAAIA,IACzE,CACA,OAAO+H,CACX,EAKJ,MAAM+V,EACF,WAAAhc,CAAY6L,EAAM5Q,GACd2E,KAAKiM,KAAOA,EACZjM,KAAK3E,GAAKA,CACd,CACA,KAAA6f,CAAMxG,EAAOhC,GACT,IAAI,OAAEjS,GAAWiS,EAAMP,GA+E/B,SAAmBlG,EAAMyI,EAAOhC,EAAOhM,EAAO2V,EAAWC,GACrD,IAAIhH,EAAQ,EAAGiH,EAAY,GAAK7V,GAAO,QAAE+S,GAAY/G,EAAMP,EAAE1R,OAC7DqK,EAAM,KACGyR,EAAYtQ,EAAKqJ,IADX,CAGX,IAAIkH,EAASvQ,EAAKqJ,EAAQ,GAI1B,IAAK,IAAI5M,EAAI4M,EAAQ,EAAG5M,EAAI8T,EAAQ9T,GAAK,EACrC,IAAKuD,EAAKvD,EAAI,GAAK6T,GAAa,EAAG,CAC/B,IAAIzF,EAAO7K,EAAKvD,GAChB,GAAI+Q,EAAQgD,OAAO3F,MACQ,GAAtBpC,EAAMwG,MAAM7d,OAAeqX,EAAMwG,MAAM7d,OAASyZ,GAC7C4F,EAAU5F,EAAMpC,EAAMwG,MAAM7d,MAAOgf,EAAWC,IAAc,CAChE5H,EAAMmH,YAAY/E,GAClB,KACJ,CACJ,CACJ,IAAI5J,EAAOwH,EAAMxH,KAAMyP,EAAM,EAAGC,EAAO3Q,EAAKqJ,EAAQ,GAEpD,KAAIZ,EAAMxH,KAAO,GAAK0P,EAAOD,GAAsC,OAA/B1Q,EAAKuQ,EAAgB,EAAPI,EAAW,IAA7D,CAKA,KAAOD,EAAMC,GAAO,CAChB,IAAIC,EAAOF,EAAMC,GAAS,EACtB5R,EAAQwR,EAASK,GAAOA,GAAO,GAC/B3e,EAAO+N,EAAKjB,GAAQ1M,EAAK2N,EAAKjB,EAAQ,IAAM,MAChD,GAAIkC,EAAOhP,EACP0e,EAAOC,MACN,MAAI3P,GAAQ5O,GAEZ,CACDgX,EAAQrJ,EAAKjB,EAAQ,GACrB0J,EAAMM,UACN,SAASlK,CACb,CALI6R,EAAME,EAAM,CAKhB,CACJ,CACA,KAhBA,CAFIvH,EAAQrJ,EAAKuQ,EAAgB,EAAPI,EAAW,EAmBzC,CACJ,CAxHQE,CAAU9c,KAAKiM,KAAMyI,EAAOhC,EAAO1S,KAAK3E,GAAIoF,EAAOwL,KAAMxL,EAAOsc,eACpE,EAwHJ,SAASC,EAAW/Q,EAAMa,EAAOgK,GAC7B,IAAK,IAAe5J,EAAXxE,EAAIoE,EAAiC,QAAnBI,EAAOjB,EAAKvD,IAA4BA,IAC/D,GAAIwE,GAAQ4J,EACR,OAAOpO,EAAIoE,EACnB,OAAQ,CACZ,CACA,SAAS4P,EAAUxB,EAAO5H,EAAM2J,EAAWC,GACvC,IAAIC,EAAQH,EAAWC,EAAWC,EAAa5J,GAC/C,OAAO6J,EAAQ,GAAKH,EAAWC,EAAWC,EAAahC,GAASiC,CACpE,CA/HAf,EAAWgB,UAAUC,WAAajB,EAAWgB,UAAUE,SAAWlB,EAAWgB,UAAUrU,QAAS,EA+BzDqT,EAAWgB,UAAUE,SAAWlB,EAAWgB,UAAUrU,QAAS,EAmGrG,MAAMwU,EAA4B,oBAAXC,SAA0BA,QAAQC,KAAO,YAAY9T,KAAK6T,QAAQC,IAAIC,KAC7F,IAAIC,EAAW,KACf,SAASC,EAAM5W,EAAM9F,EAAKiJ,GACtB,IAAIL,EAAS9C,EAAK8C,OAAOP,EAASgC,kBAElC,IADAzB,EAAOO,OAAOnJ,KAEV,KAAMiJ,EAAO,EAAIL,EAAOoH,YAAYhQ,GAAO4I,EAAO1I,WAAWF,IACzD,OAAS,CACL,IAAKiJ,EAAO,EAAIL,EAAOxL,GAAK4C,EAAM4I,EAAO5L,KAAOgD,KAAS4I,EAAOrN,KAAK2L,QACjE,OAAO+B,EAAO,EAAIkG,KAAKC,IAAI,EAAGD,KAAK8L,IAAIrS,EAAOxL,GAAK,EAAG4C,EAAM,KACtDmP,KAAK8L,IAAInV,EAAKzF,OAAQ8O,KAAKC,IAAIxG,EAAO5L,KAAO,EAAGgD,EAAM,KAChE,GAAIiJ,EAAO,EAAIL,EAAOsH,cAAgBtH,EAAO6B,cACzC,MACJ,IAAK7B,EAAOiB,SACR,OAAOZ,EAAO,EAAI,EAAInD,EAAKzF,MACnC,CAEZ,CACA,MAAM,EACF,WAAAnB,CAAYuU,EAAWvI,GACnBpM,KAAK2U,UAAYA,EACjB3U,KAAKoM,QAAUA,EACfpM,KAAK0I,EAAI,EACT1I,KAAK6d,SAAW,KAChB7d,KAAK8d,UAAY,EACjB9d,KAAK+d,QAAU,EACf/d,KAAKge,MAAQ,GACbhe,KAAK8M,MAAQ,GACb9M,KAAKgL,MAAQ,GACbhL,KAAKie,cACT,CACA,YAAAA,GACI,IAAIC,EAAKle,KAAK6d,SAAW7d,KAAK0I,GAAK1I,KAAK2U,UAAUpT,OAAS,KAAOvB,KAAK2U,UAAU3U,KAAK0I,KACtF,GAAIwV,EAAI,CAGJ,IAFAle,KAAK8d,SAAWI,EAAGC,UAAYP,EAAMM,EAAGlX,KAAMkX,EAAGhgB,KAAOggB,EAAGhK,OAAQ,GAAKgK,EAAGhK,OAASgK,EAAGhgB,KACvF8B,KAAK+d,OAASG,EAAGE,QAAUR,EAAMM,EAAGlX,KAAMkX,EAAG5f,GAAK4f,EAAGhK,QAAS,GAAKgK,EAAGhK,OAASgK,EAAG5f,GAC3E0B,KAAKge,MAAMzc,QACdvB,KAAKge,MAAM/O,MACXjP,KAAK8M,MAAMmC,MACXjP,KAAKgL,MAAMiE,MAEfjP,KAAKge,MAAMjd,KAAKmd,EAAGlX,MACnBhH,KAAK8M,MAAM/L,MAAMmd,EAAGhK,QACpBlU,KAAKgL,MAAMjK,KAAK,GAChBf,KAAKqX,UAAYrX,KAAK8d,QAC1B,MAEI9d,KAAKqX,UAAY,GAEzB,CAEA,MAAAgH,CAAOnd,GACH,GAAIA,EAAMlB,KAAKqX,UACX,OAAO,KACX,KAAOrX,KAAK6d,UAAY7d,KAAK+d,QAAU7c,GACnClB,KAAKie,eACT,IAAKje,KAAK6d,SACN,OAAO,KACX,OAAS,CACL,IAAIpP,EAAOzO,KAAKge,MAAMzc,OAAS,EAC/B,GAAIkN,EAAO,EAEP,OADAzO,KAAKie,eACE,KAEX,IAAItW,EAAM3H,KAAKge,MAAMvP,GAAOzD,EAAQhL,KAAKgL,MAAMyD,GAC/C,GAAIzD,GAASrD,EAAI8B,SAASlI,OAAQ,CAC9BvB,KAAKge,MAAM/O,MACXjP,KAAK8M,MAAMmC,MACXjP,KAAKgL,MAAMiE,MACX,QACJ,CACA,IAAI/B,EAAOvF,EAAI8B,SAASuB,GACpB8B,EAAQ9M,KAAK8M,MAAM2B,GAAQ9G,EAAI1G,UAAU+J,GAC7C,GAAI8B,EAAQ5L,EAER,OADAlB,KAAKqX,UAAYvK,EACV,KAEX,GAAII,aAAgB1D,EAAM,CACtB,GAAIsD,GAAS5L,EAAK,CACd,GAAI4L,EAAQ9M,KAAK8d,SACb,OAAO,KACX,IAAI/Q,EAAMD,EAAQI,EAAK3L,OACvB,GAAIwL,GAAO/M,KAAK+d,OAAQ,CACpB,IAAIlX,EAAYqG,EAAKjF,KAAKrC,EAASiB,WACnC,IAAKA,GAAakG,EAAMlG,EAAY7G,KAAK6d,SAASvf,GAC9C,OAAO4O,CACf,CACJ,CACAlN,KAAKgL,MAAMyD,KACP3B,EAAQI,EAAK3L,QAAU8O,KAAKC,IAAItQ,KAAK8d,SAAU5c,KAC/ClB,KAAKge,MAAMjd,KAAKmM,GAChBlN,KAAK8M,MAAM/L,KAAK+L,GAChB9M,KAAKgL,MAAMjK,KAAK,GAExB,MAEIf,KAAKgL,MAAMyD,KACXzO,KAAKqX,UAAYvK,EAAQI,EAAK3L,MAEtC,CACJ,EAEJ,MAAM+c,EACF,WAAAle,CAAYK,EAAQkX,GAChB3X,KAAK2X,OAASA,EACd3X,KAAKue,OAAS,GACdve,KAAKwe,UAAY,KACjBxe,KAAKye,QAAU,GACfze,KAAKue,OAAS9d,EAAOie,WAAWvc,KAAIwT,GAAK,IAAI+E,GACjD,CACA,UAAAiE,CAAWjM,GACP,IAAIkM,EAAc,EACdC,EAAO,MACP,OAAEpe,GAAWiS,EAAMP,GAAG,WAAEuM,GAAeje,EACvCma,EAAOna,EAAO0X,UAAUzF,EAAM4C,MAAO,GACrClZ,EAAUsW,EAAMgD,WAAahD,EAAMgD,WAAWiE,KAAO,EACrD9S,EAAY,EAChB,IAAK,IAAI6B,EAAI,EAAGA,EAAIgW,EAAWnd,OAAQmH,IAAK,CACxC,KAAM,GAAKA,EAAKkS,GACZ,SACJ,IAAIkE,EAAYJ,EAAWhW,GAAIwS,EAAQlb,KAAKue,OAAO7V,GACnD,KAAImW,GAASC,EAAUxB,aAEnBwB,EAAUzB,YAAcnC,EAAMpO,OAAS4F,EAAMxR,KAAOga,EAAMN,MAAQA,GAAQM,EAAM9e,SAAWA,KAC3F4D,KAAK+e,kBAAkB7D,EAAO4D,EAAWpM,GACzCwI,EAAMN,KAAOA,EACbM,EAAM9e,QAAUA,GAEhB8e,EAAMrU,UAAYqU,EAAMnO,IAAM,KAC9BlG,EAAYwJ,KAAKC,IAAI4K,EAAMrU,UAAWA,IACvB,GAAfqU,EAAM7d,OAA2B,CACjC,IAAIgS,EAAauP,EAIjB,GAHI1D,EAAMP,UAAY,IAClBiE,EAAc5e,KAAKgf,WAAWtM,EAAOwI,EAAMP,SAAUO,EAAMnO,IAAK6R,IACpEA,EAAc5e,KAAKgf,WAAWtM,EAAOwI,EAAM7d,MAAO6d,EAAMnO,IAAK6R,IACxDE,EAAU/V,SACX8V,EAAO3D,EACH0D,EAAcvP,GACd,KAEZ,CACJ,CACA,KAAOrP,KAAKye,QAAQld,OAASqd,GACzB5e,KAAKye,QAAQxP,MAUjB,OATIpI,GACA6L,EAAMuD,aAAapP,GAClBgY,GAAQnM,EAAMxR,KAAOlB,KAAK2X,OAAO5K,MAClC8R,EAAO,IAAInE,EACXmE,EAAKxhB,MAAQqV,EAAMP,EAAE1R,OAAOwe,QAC5BJ,EAAK/R,MAAQ+R,EAAK9R,IAAM2F,EAAMxR,IAC9B0d,EAAc5e,KAAKgf,WAAWtM,EAAOmM,EAAKxhB,MAAOwhB,EAAK9R,IAAK6R,IAE/D5e,KAAKwe,UAAYK,EACV7e,KAAKye,OAChB,CACA,YAAAS,CAAaxM,GACT,GAAI1S,KAAKwe,UACL,OAAOxe,KAAKwe,UAChB,IAAIK,EAAO,IAAInE,GAAa,IAAExZ,EAAG,EAAEiR,GAAMO,EAIzC,OAHAmM,EAAK/R,MAAQ5L,EACb2d,EAAK9R,IAAMsD,KAAK8L,IAAIjb,EAAM,EAAGiR,EAAEwF,OAAO5K,KACtC8R,EAAKxhB,MAAQ6D,GAAOiR,EAAEwF,OAAO5K,IAAMoF,EAAE1R,OAAOwe,QAAU,EAC/CJ,CACX,CACA,iBAAAE,CAAkB7D,EAAO4D,EAAWpM,GAChC,IAAI5F,EAAQ9M,KAAK2X,OAAO8D,QAAQ/I,EAAMxR,KAEtC,GADA4d,EAAU5D,MAAMlb,KAAK2X,OAAOC,MAAM9K,EAAOoO,GAAQxI,GAC7CwI,EAAM7d,OAAS,EAAG,CAClB,IAAI,OAAEoD,GAAWiS,EAAMP,EACvB,IAAK,IAAIzJ,EAAI,EAAGA,EAAIjI,EAAO0e,YAAY5d,OAAQmH,IAC3C,GAAIjI,EAAO0e,YAAYzW,IAAMwS,EAAM7d,MAAO,CACtC,IAAIgJ,EAAS5F,EAAO2e,aAAa1W,GAAG1I,KAAK2X,OAAOvC,KAAK8F,EAAMpO,MAAOoO,EAAMnO,KAAM2F,GAC9E,GAAIrM,GAAU,GAAKqM,EAAMP,EAAE1R,OAAOgZ,QAAQgD,OAAOpW,GAAU,GAAI,CAC7C,EAATA,EAGD6U,EAAMP,SAAWtU,GAAU,EAF3B6U,EAAM7d,MAAQgJ,GAAU,EAG5B,KACJ,CACJ,CACR,MAEI6U,EAAM7d,MAAQ,EACd6d,EAAMnO,IAAM/M,KAAK2X,OAAO8D,QAAQ3O,EAAQ,EAEhD,CACA,SAAAuS,CAAUrJ,EAAQkF,EAAOnO,EAAK/B,GAE1B,IAAK,IAAItC,EAAI,EAAGA,EAAIsC,EAAOtC,GAAK,EAC5B,GAAI1I,KAAKye,QAAQ/V,IAAMsN,EACnB,OAAOhL,EAIf,OAHAhL,KAAKye,QAAQzT,KAAWgL,EACxBhW,KAAKye,QAAQzT,KAAWkQ,EACxBlb,KAAKye,QAAQzT,KAAW+B,EACjB/B,CACX,CACA,UAAAgU,CAAWtM,EAAOwI,EAAOnO,EAAK/B,GAC1B,IAAI,MAAEsK,GAAU5C,GAAO,OAAEjS,GAAWiS,EAAMP,GAAG,KAAElG,GAASxL,EACxD,IAAK,IAAIlB,EAAM,EAAGA,EAAM,EAAGA,IACvB,IAAK,IAAImJ,EAAIjI,EAAO0X,UAAU7C,EAAO/V,EAAM,EAA0B,IAA8BmJ,GAAK,EAAG,CACvG,GAAe,OAAXuD,EAAKvD,GAA2B,CAChC,GAAmB,GAAfuD,EAAKvD,EAAI,GAGR,CACY,GAATsC,GAA6B,GAAfiB,EAAKvD,EAAI,KACvBsC,EAAQhL,KAAKqf,UAAUnQ,GAAKjD,EAAMvD,EAAI,GAAIwS,EAAOnO,EAAK/B,IAC1D,KACJ,CANItC,EAAIwG,GAAKjD,EAAMvD,EAAI,EAO3B,CACIuD,EAAKvD,IAAMwS,IACXlQ,EAAQhL,KAAKqf,UAAUnQ,GAAKjD,EAAMvD,EAAI,GAAIwS,EAAOnO,EAAK/B,GAC9D,CAEJ,OAAOA,CACX,EAEJ,MAAMsU,EACF,WAAAlf,CAAYK,EAAQiU,EAAOC,EAAWC,GAClC5U,KAAKS,OAASA,EACdT,KAAK0U,MAAQA,EACb1U,KAAK4U,OAASA,EACd5U,KAAKuf,WAAa,EAClBvf,KAAKwf,YAAc,KACnBxf,KAAKyf,YAAc,EACnBzf,KAAKsM,OAAS,GACdtM,KAAK0f,UAAY,KACjB1f,KAAKwW,uBAAyB,EAC9BxW,KAAK0W,qBAAuB,EAC5B1W,KAAKyW,kBAAoB,EACzBzW,KAAK2X,OAAS,IAAImD,EAAYpG,EAAOE,GACrC5U,KAAKue,OAAS,IAAID,EAAW7d,EAAQT,KAAK2X,QAC1C3X,KAAK2f,QAAUlf,EAAOkH,IAAI,GAC1B,IAAI,KAAEzJ,GAAS0W,EAAO,GACtB5U,KAAK4f,OAAS,CAACvK,EAAMvI,MAAM9M,KAAMS,EAAOkH,IAAI,GAAIzJ,IAChD8B,KAAK2U,UAAYA,EAAUpT,QAAUvB,KAAK2X,OAAO5K,IAAM7O,EAA6B,EAAtBuC,EAAOof,aAC/D,IAAI,EAAelL,EAAWlU,EAAO2L,SAAW,IAC1D,CACA,aAAI0T,GACA,OAAO9f,KAAKyf,WAChB,CAOA,OAAAzK,GACI,IAGI+K,EAASC,EAHTJ,EAAS5f,KAAK4f,OAAQ1e,EAAMlB,KAAKyf,YAEjCQ,EAAYjgB,KAAK4f,OAAS,GAS9B,GAAI5f,KAAKyW,kBAAoB,KAAmE,GAAjBmJ,EAAOre,OAAa,CAC/F,IAAKiX,GAAKoH,EACV,KAAOpH,EAAEE,eAAiBF,EAAE9F,MAAMnR,QAAUiX,EAAE9F,MAAM8F,EAAE9F,MAAMnR,OAAS,IAAMvB,KAAKwW,wBAChFxW,KAAKyW,kBAAoBzW,KAAK0W,qBAAuB,CACzD,CAIA,IAAK,IAAIhO,EAAI,EAAGA,EAAIkX,EAAOre,OAAQmH,IAAK,CACpC,IAAIgK,EAAQkN,EAAOlX,GACnB,OAAS,CAEL,GADA1I,KAAKue,OAAOC,UAAY,KACpB9L,EAAMxR,IAAMA,EACZ+e,EAAUlf,KAAK2R,OAEd,IAAI1S,KAAKkgB,aAAaxN,EAAOuN,EAAWL,GACzC,SAEC,CACIG,IACDA,EAAU,GACVC,EAAgB,IAEpBD,EAAQhf,KAAK2R,GACb,IAAIyN,EAAMngB,KAAKue,OAAOW,aAAaxM,GACnCsN,EAAcjf,KAAKof,EAAI9iB,MAAO8iB,EAAIpT,IACtC,EACA,KACJ,CACJ,CACA,IAAKkT,EAAU1e,OAAQ,CACnB,IAAI6e,EAAWL,GAuhB3B,SAAsBH,GAClB,IAAIrH,EAAO,KACX,IAAK,IAAI7F,KAASkN,EAAQ,CACtB,IAAIG,EAAUrN,EAAMP,EAAEuN,WACjBhN,EAAMxR,KAAOwR,EAAMP,EAAEwF,OAAO5K,KAAkB,MAAXgT,GAAmBrN,EAAMxR,IAAM6e,IACnErN,EAAMP,EAAE1R,OAAOmW,UAAUlE,EAAM4C,MAAO,MACpCiD,GAAQA,EAAK/C,MAAQ9C,EAAM8C,SAC7B+C,EAAO7F,EACf,CACA,OAAO6F,CACX,CAjiBsC8H,CAAaN,GACvC,GAAIK,EAGA,OAFI7C,GACA+C,QAAQC,IAAI,eAAiBvgB,KAAKwgB,QAAQJ,IACvCpgB,KAAKygB,YAAYL,GAE5B,GAAIpgB,KAAKS,OAAOsZ,OAGZ,MAFIwD,GAAWwC,GACXO,QAAQC,IAAI,qBAAuBvgB,KAAKue,OAAOC,UAAYxe,KAAKS,OAAOigB,QAAQ1gB,KAAKue,OAAOC,UAAUnhB,OAAS,SAC5G,IAAIsjB,YAAY,eAAiBzf,GAEtClB,KAAKuf,aACNvf,KAAKuf,WAAa,EAC1B,CACA,GAAIvf,KAAKuf,YAAcQ,EAAS,CAC5B,IAAIK,EAA6B,MAAlBpgB,KAAK0f,WAAqBK,EAAQ,GAAG7e,IAAMlB,KAAK0f,UAAYK,EAAQ,GAC7E/f,KAAK4gB,YAAYb,EAASC,EAAeC,GAC/C,GAAIG,EAGA,OAFI7C,GACA+C,QAAQC,IAAI,gBAAkBvgB,KAAKwgB,QAAQJ,IACxCpgB,KAAKygB,YAAYL,EAASlH,WAEzC,CACA,GAAIlZ,KAAKuf,WAAY,CACjB,IAAIsB,EAAkC,GAAnB7gB,KAAKuf,WAAkB,EAAsB,EAAlBvf,KAAKuf,WACnD,GAAIU,EAAU1e,OAASsf,EAEnB,IADAZ,EAAUxiB,MAAK,CAACC,EAAGC,IAAMA,EAAE6X,MAAQ9X,EAAE8X,QAC9ByK,EAAU1e,OAASsf,GACtBZ,EAAUhR,MAEdgR,EAAUxM,MAAK+E,GAAKA,EAAEjD,UAAYrU,KAClClB,KAAKuf,YACb,MACK,GAAIU,EAAU1e,OAAS,EAAG,CAI3Buf,EAAO,IAAK,IAAIpY,EAAI,EAAGA,EAAIuX,EAAU1e,OAAS,EAAGmH,IAAK,CAClD,IAAIgK,EAAQuN,EAAUvX,GACtB,IAAK,IAAIqG,EAAIrG,EAAI,EAAGqG,EAAIkR,EAAU1e,OAAQwN,IAAK,CAC3C,IAAIuK,EAAQ2G,EAAUlR,GACtB,GAAI2D,EAAM2G,UAAUC,IAChB5G,EAAMvG,OAAO5K,OAAS,KAAsC+X,EAAMnN,OAAO5K,OAAS,IAAoC,CACtH,MAAMmR,EAAM8C,MAAQ8D,EAAM9D,OAAW9C,EAAMvG,OAAO5K,OAAS+X,EAAMnN,OAAO5K,QAAW,GAG9E,CACD0e,EAAUzN,OAAO9J,IAAK,GACtB,SAASoY,CACb,CALIb,EAAUzN,OAAOzD,IAAK,EAM9B,CACJ,CACJ,CACIkR,EAAU1e,OAAS,IACnB0e,EAAUzN,OAAO,GAA4ByN,EAAU1e,OAAS,GACxE,CACAvB,KAAKyf,YAAcQ,EAAU,GAAG/e,IAChC,IAAK,IAAIwH,EAAI,EAAGA,EAAIuX,EAAU1e,OAAQmH,IAC9BuX,EAAUvX,GAAGxH,IAAMlB,KAAKyf,cACxBzf,KAAKyf,YAAcQ,EAAUvX,GAAGxH,KACxC,OAAO,IACX,CACA,MAAA4N,CAAO5N,GACH,GAAsB,MAAlBlB,KAAK0f,WAAqB1f,KAAK0f,UAAYxe,EAC3C,MAAM,IAAIiF,WAAW,gCACzBnG,KAAK0f,UAAYxe,CACrB,CAKA,YAAAgf,CAAaxN,EAAOkN,EAAQpZ,GACxB,IAAIsG,EAAQ4F,EAAMxR,KAAK,OAAET,GAAWT,KAChCgP,EAAOuO,EAAUvd,KAAKwgB,QAAQ9N,GAAS,OAAS,GACpD,GAAsB,MAAlB1S,KAAK0f,WAAqB5S,EAAQ9M,KAAK0f,UACvC,OAAOhN,EAAMgG,cAAgBhG,EAAQ,KACzC,GAAI1S,KAAK2U,UAAW,CAChB,IAAIoM,EAAWrO,EAAMgD,YAAchD,EAAMgD,WAAW+B,QAAQsC,OAAQiH,EAASD,EAAWrO,EAAMgD,WAAWiE,KAAO,EAChH,IAAK,IAAIsH,EAASjhB,KAAK2U,UAAU0J,OAAOvR,GAAQmU,GAAS,CACrD,IAAI/a,EAAQlG,KAAKS,OAAO2L,QAAQtD,MAAMmY,EAAOxkB,KAAKpB,KAAO4lB,EAAOxkB,KAAOgE,EAAO2V,QAAQ1D,EAAM4C,MAAO2L,EAAOxkB,KAAKpB,KAAO,EACtH,GAAI6K,GAAS,GAAK+a,EAAO1f,UAAYwf,IAAaE,EAAOhZ,KAAKrC,EAASgB,cAAgB,IAAMoa,GAIzF,OAHAtO,EAAM6E,QAAQ0J,EAAQ/a,GAClBqX,GACA+C,QAAQC,IAAIvR,EAAOhP,KAAKwgB,QAAQ9N,GAAS,kBAAkBjS,EAAOigB,QAAQO,EAAOxkB,KAAKpB,SACnF,EAEX,KAAM4lB,aAAkBzX,IAAmC,GAA1ByX,EAAOxX,SAASlI,QAAe0f,EAAOhgB,UAAU,GAAK,EAClF,MACJ,IAAI2J,EAAQqW,EAAOxX,SAAS,GAC5B,KAAImB,aAAiBpB,GAA+B,GAAvByX,EAAOhgB,UAAU,IAG1C,MAFAggB,EAASrW,CAGjB,CACJ,CACA,IAAIsW,EAAgBzgB,EAAO0X,UAAUzF,EAAM4C,MAAO,GAClD,GAAI4L,EAAgB,EAIhB,OAHAxO,EAAMqD,OAAOmL,GACT3D,GACA+C,QAAQC,IAAIvR,EAAOhP,KAAKwgB,QAAQ9N,GAAS,uBAAuBjS,EAAOigB,QAAwB,MAAhBQ,QAC5E,EAEX,GAAIxO,EAAMA,MAAMnR,QAAU,KACtB,KAAOmR,EAAMA,MAAMnR,OAAS,KAAwBmR,EAAMgG,gBAE9D,IAAI+F,EAAUze,KAAKue,OAAOI,WAAWjM,GACrC,IAAK,IAAIhK,EAAI,EAAGA,EAAI+V,EAAQld,QAAS,CACjC,IAAIyU,EAASyI,EAAQ/V,KAAMoO,EAAO2H,EAAQ/V,KAAMqE,EAAM0R,EAAQ/V,KAC1D+F,EAAO/F,GAAK+V,EAAQld,SAAWiF,EAC/B2a,EAAa1S,EAAOiE,EAAQA,EAAMlM,QAClCqY,EAAO7e,KAAKue,OAAOC,UAKvB,GAJA2C,EAAW/J,MAAMpB,EAAQc,EAAM+H,EAAOA,EAAK/R,MAAQqU,EAAWjgB,IAAK6L,GAC/DwQ,GACA+C,QAAQC,IAAIvR,EAAOhP,KAAKwgB,QAAQW,GAAc,SAAmB,MAATnL,EAClD,aAAavV,EAAOigB,QAAiB,MAAT1K,KADqE,eACrBvV,EAAOigB,QAAQ5J,QAAWhK,IAAQqU,GAAczO,EAAQ,GAAK,cAC/IjE,EACA,OAAO,EACF0S,EAAWjgB,IAAM4L,EACtB8S,EAAO7e,KAAKogB,GAEZ3a,EAAMzF,KAAKogB,EACnB,CACA,OAAO,CACX,CAIA,YAAAC,CAAa1O,EAAOuN,GAChB,IAAI/e,EAAMwR,EAAMxR,IAChB,OAAS,CACL,IAAKlB,KAAKkgB,aAAaxN,EAAO,KAAM,MAChC,OAAO,EACX,GAAIA,EAAMxR,IAAMA,EAEZ,OADAmgB,EAAe3O,EAAOuN,IACf,CAEf,CACJ,CACA,WAAAW,CAAYhB,EAAQrB,EAAQ0B,GACxB,IAAIG,EAAW,KAAMkB,GAAY,EACjC,IAAK,IAAI5Y,EAAI,EAAGA,EAAIkX,EAAOre,OAAQmH,IAAK,CACpC,IAAIgK,EAAQkN,EAAOlX,GAAIwS,EAAQqD,EAAO7V,GAAK,GAAI6Y,EAAWhD,EAAkB,GAAV7V,GAAK,IACnEsG,EAAOuO,EAAUvd,KAAKwgB,QAAQ9N,GAAS,OAAS,GACpD,GAAIA,EAAMyG,QAAS,CACf,GAAImI,EACA,SAMJ,GALAA,GAAY,EACZ5O,EAAM0G,UACFmE,GACA+C,QAAQC,IAAIvR,EAAOhP,KAAKwgB,QAAQ9N,GAAS,gBAClC1S,KAAKohB,aAAa1O,EAAOuN,GAEhC,QACR,CACA,IAAIuB,EAAQ9O,EAAMlM,QAASib,EAAYzS,EACvC,IAAK,IAAID,EAAI,EAAGyS,EAAM9I,eAAiB3J,EAAI,KACnCwO,GACA+C,QAAQC,IAAIkB,EAAYzhB,KAAKwgB,QAAQgB,GAAS,wBACvCxhB,KAAKohB,aAAaI,EAAOvB,IAHkClR,IAMlEwO,IACAkE,EAAYzhB,KAAKwgB,QAAQgB,GAAS,QAE1C,IAAK,IAAIE,KAAUhP,EAAM2F,gBAAgB6C,GACjCqC,GACA+C,QAAQC,IAAIvR,EAAOhP,KAAKwgB,QAAQkB,GAAU,yBAC9C1hB,KAAKohB,aAAaM,EAAQzB,GAE1BjgB,KAAK2X,OAAO5K,IAAM2F,EAAMxR,KACpBqgB,GAAY7O,EAAMxR,MAClBqgB,IACArG,EAAQ,GAEZxI,EAAMoF,gBAAgBoD,EAAOqG,GACzBhE,GACA+C,QAAQC,IAAIvR,EAAOhP,KAAKwgB,QAAQ9N,GAAS,wBAAwB1S,KAAKS,OAAOigB,QAAQxF,OACzFmG,EAAe3O,EAAOuN,MAEhBG,GAAYA,EAAS5K,MAAQ9C,EAAM8C,SACzC4K,EAAW1N,EAEnB,CACA,OAAO0N,CACX,CAEA,WAAAK,CAAY/N,GAER,OADAA,EAAMoH,QACCtQ,EAAKwC,MAAM,CAAEG,OAAQ8N,EAAkB3S,OAAOoL,GACjDtG,QAASpM,KAAKS,OAAO2L,QACrBkD,MAAOtP,KAAK2f,QACZtT,gBAAiBrM,KAAKS,OAAOof,aAC7BvT,OAAQtM,KAAKsM,OACbQ,MAAO9M,KAAK4U,OAAO,GAAG1W,KACtBqD,OAAQmR,EAAMxR,IAAMlB,KAAK4U,OAAO,GAAG1W,KACnCqO,cAAevM,KAAKS,OAAO4V,eACnC,CACA,OAAAmK,CAAQ9N,GACJ,IAAIrX,GAAMsiB,IAAaA,EAAW,IAAItU,UAAUnC,IAAIwL,GAGpD,OAFKrX,GACDsiB,EAASpe,IAAImT,EAAOrX,EAAK2G,OAAO2f,cAAc3hB,KAAKwf,gBAChDnkB,EAAKqX,CAChB,EAEJ,SAAS2O,EAAe3O,EAAOuN,GAC3B,IAAK,IAAIvX,EAAI,EAAGA,EAAIuX,EAAU1e,OAAQmH,IAAK,CACvC,IAAI4Q,EAAQ2G,EAAUvX,GACtB,GAAI4Q,EAAMpY,KAAOwR,EAAMxR,KAAOoY,EAAMD,UAAU3G,GAG1C,YAFIuN,EAAUvX,GAAG8M,MAAQ9C,EAAM8C,QAC3ByK,EAAUvX,GAAKgK,GAG3B,CACAuN,EAAUlf,KAAK2R,EACnB,CACA,MAAMkP,EACF,WAAAxhB,CAAY8I,EAAQ1B,EAAOqa,GACvB7hB,KAAKkJ,OAASA,EACdlJ,KAAKwH,MAAQA,EACbxH,KAAK6hB,SAAWA,CACpB,CACA,MAAApF,CAAO3F,GAAQ,OAAQ9W,KAAK6hB,UAAmC,GAAvB7hB,KAAK6hB,SAAS/K,EAAY,EAiCtE,MAAMgL,UAAiBtN,EAInB,WAAApU,CAAYsH,GAMR,GALA4J,QAIAtR,KAAK+hB,SAAW,GACI,IAAhBra,EAAKsa,QACL,MAAM,IAAI7b,WAAW,mBAAmBuB,EAAKsa,+CACjD,IAAIC,EAAYva,EAAKua,UAAUzb,MAAM,KACrCxG,KAAKqW,cAAgB4L,EAAU1gB,OAC/B,IAAK,IAAImH,EAAI,EAAGA,EAAIhB,EAAKwa,gBAAiBxZ,IACtCuZ,EAAUlhB,KAAK,IACnB,IAAIohB,EAAW9a,OAAO+a,KAAK1a,EAAK2a,UAAUlgB,KAAI0O,GAAKnJ,EAAK2a,SAASxR,GAAG,KAChEyR,EAAY,GAChB,IAAK,IAAI5Z,EAAI,EAAGA,EAAIuZ,EAAU1gB,OAAQmH,IAClC4Z,EAAUvhB,KAAK,IACnB,SAASwhB,EAAQC,EAAQva,EAAM5K,GAC3BilB,EAAUE,GAAQzhB,KAAK,CAACkH,EAAMA,EAAKlC,YAAY/D,OAAO3E,KAC1D,CACA,GAAIqK,EAAK4a,UACL,IAAK,IAAIG,KAAY/a,EAAK4a,UAAW,CACjC,IAAIra,EAAOwa,EAAS,GACD,iBAARxa,IACPA,EAAOrC,EAASqC,IACpB,IAAK,IAAIS,EAAI,EAAGA,EAAI+Z,EAASlhB,QAAS,CAClC,IAAI2L,EAAOuV,EAAS/Z,KACpB,GAAIwE,GAAQ,EACRqV,EAAQrV,EAAMjF,EAAMwa,EAAS/Z,UAE5B,CACD,IAAIrL,EAAQolB,EAAS/Z,GAAKwE,GAC1B,IAAK,IAAI6B,GAAK7B,EAAM6B,EAAI,EAAGA,IACvBwT,EAAQE,EAAS/Z,KAAMT,EAAM5K,GACjCqL,GACJ,CACJ,CACJ,CACJ1I,KAAKoM,QAAU,IAAIvD,EAAQoZ,EAAU9f,KAAI,CAACoF,EAAMmB,IAAMtC,EAASqB,OAAO,CAClEF,KAAMmB,GAAK1I,KAAKqW,mBAAgBxV,EAAY0G,EAC5ClM,GAAIqN,EACJvB,MAAOmb,EAAU5Z,GACjBf,IAAKwa,EAAS5Z,QAAQG,IAAM,EAC5Bb,MAAY,GAALa,EACPd,QAASF,EAAKgb,cAAgBhb,EAAKgb,aAAana,QAAQG,IAAM,OAE9DhB,EAAKib,cACL3iB,KAAKoM,QAAUpM,KAAKoM,QAAQrD,UAAUrB,EAAKib,cAC/C3iB,KAAK+Z,QAAS,EACd/Z,KAAK6f,aAAepa,EACpB,IAAImd,EAAazI,EAAYzS,EAAKmb,WAClC7iB,KAAK5D,QAAUsL,EAAKtL,QACpB4D,KAAK8iB,iBAAmBpb,EAAKyX,aAAe,GAC5Cnf,KAAKmf,YAAc,IAAIvR,YAAY5N,KAAK8iB,iBAAiBvhB,QACzD,IAAK,IAAImH,EAAI,EAAGA,EAAI1I,KAAK8iB,iBAAiBvhB,OAAQmH,IAC9C1I,KAAKmf,YAAYzW,GAAK1I,KAAK8iB,iBAAiBpa,GAAGoO,KACnD9W,KAAKof,aAAepf,KAAK8iB,iBAAiB3gB,IAAI4gB,IAC9C/iB,KAAKgjB,OAAS7I,EAAYzS,EAAKsb,OAAQC,aACvCjjB,KAAKiM,KAAOkO,EAAYzS,EAAKwb,WAC7BljB,KAAKga,KAAOG,EAAYzS,EAAKsS,MAC7Bha,KAAKmjB,QAAUzb,EAAKyb,QACpBnjB,KAAK0e,WAAahX,EAAKgX,WAAWvc,KAAI9E,GAAyB,iBAATA,EAAoB,IAAI+e,EAAWwG,EAAYvlB,GAASA,IAC9G2C,KAAKqiB,SAAW3a,EAAK2a,SACrBriB,KAAKojB,SAAW1b,EAAK0b,UAAY,CAAC,EAClCpjB,KAAKqjB,mBAAqB3b,EAAK2b,oBAAsB,KACrDrjB,KAAK+c,eAAiBrV,EAAK4b,UAC3BtjB,KAAKujB,UAAY7b,EAAK6b,WAAa,KACnCvjB,KAAKkX,QAAUlX,KAAKoM,QAAQtD,MAAMvH,OAAS,EAC3CvB,KAAKyZ,QAAUzZ,KAAKwjB,eACpBxjB,KAAK2H,IAAM3H,KAAKqiB,SAAShb,OAAO+a,KAAKpiB,KAAKqiB,UAAU,GACxD,CACA,WAAAvN,CAAYJ,EAAOC,EAAWC,GAC1B,IAAIlU,EAAQ,IAAI4e,EAAMtf,KAAM0U,EAAOC,EAAWC,GAC9C,IAAK,IAAI6O,KAAKzjB,KAAK+hB,SACfrhB,EAAQ+iB,EAAE/iB,EAAOgU,EAAOC,EAAWC,GACvC,OAAOlU,CACX,CAIA,OAAA0V,CAAQd,EAAOwB,EAAM4M,GAAQ,GACzB,IAAIC,EAAQ3jB,KAAKga,KACjB,GAAIlD,GAAQ6M,EAAM,GACd,OAAQ,EACZ,IAAK,IAAIziB,EAAMyiB,EAAM7M,EAAO,KAAM,CAC9B,IAAI8M,EAAWD,EAAMziB,KAAQuN,EAAkB,EAAXmV,EAChCrnB,EAASonB,EAAMziB,KACnB,GAAIuN,GAAQiV,EACR,OAAOnnB,EACX,IAAK,IAAIwQ,EAAM7L,GAAO0iB,GAAY,GAAI1iB,EAAM6L,EAAK7L,IAC7C,GAAIyiB,EAAMziB,IAAQoU,EACd,OAAO/Y,EACf,GAAIkS,EACA,OAAQ,CAChB,CACJ,CAIA,SAAA2J,CAAU9C,EAAOuO,GACb,IAAI5X,EAAOjM,KAAKiM,KAChB,IAAK,IAAI1M,EAAM,EAAGA,EAAM,EAAGA,IACvB,IAAK,IAA2F2N,EAAvFxE,EAAI1I,KAAKmY,UAAU7C,EAAO/V,EAAM,EAA0B,IAAoCmJ,GAAK,EAAG,CAC3G,GAAwB,QAAnBwE,EAAOjB,EAAKvD,IAA4B,CACzC,GAAmB,GAAfuD,EAAKvD,EAAI,GAER,IAAmB,GAAfuD,EAAKvD,EAAI,GACd,OAAOwG,GAAKjD,EAAMvD,EAAI,GAEtB,KAAK,CAJLwE,EAAOjB,EAAKvD,EAAIwG,GAAKjD,EAAMvD,EAAI,GAKvC,CACA,GAAIwE,GAAQ2W,GAAoB,GAAR3W,EACpB,OAAOgC,GAAKjD,EAAMvD,EAAI,EAC9B,CAEJ,OAAO,CACX,CAIA,SAAAyP,CAAU7C,EAAOwO,GACb,OAAO9jB,KAAKgjB,OAAgB,EAAR1N,EAAmCwO,EAC3D,CAIA,SAAAlN,CAAUtB,EAAOyO,GACb,OAAQ/jB,KAAKmY,UAAU7C,EAAO,GAA4ByO,GAAQ,CACtE,CAIA,WAAApL,CAAYrD,EAAOU,GACf,QAAShW,KAAKgZ,WAAW1D,GAAO5X,GAAKA,GAAKsY,GAAgB,MAC9D,CAIA,UAAAgD,CAAW1D,EAAOU,GACd,IAAIgO,EAAQhkB,KAAKmY,UAAU7C,EAAO,GAC9BjP,EAAS2d,EAAQhO,EAAOgO,QAASnjB,EACrC,IAAK,IAAI6H,EAAI1I,KAAKmY,UAAU7C,EAAO,GAAuC,MAAVjP,EAAgBqC,GAAK,EAAG,CACpF,GAAoB,OAAhB1I,KAAKiM,KAAKvD,GAA2B,CACrC,GAAwB,GAApB1I,KAAKiM,KAAKvD,EAAI,GAGd,MAFAA,EAAIwG,GAAKlP,KAAKiM,KAAMvD,EAAI,EAGhC,CACArC,EAAS2P,EAAO9G,GAAKlP,KAAKiM,KAAMvD,EAAI,GACxC,CACA,OAAOrC,CACX,CAKA,UAAAiS,CAAWhD,GACP,IAAIjP,EAAS,GACb,IAAK,IAAIqC,EAAI1I,KAAKmY,UAAU7C,EAAO,IAA8B5M,GAAK,EAAG,CACrE,GAAoB,OAAhB1I,KAAKiM,KAAKvD,GAA2B,CACrC,GAAwB,GAApB1I,KAAKiM,KAAKvD,EAAI,GAGd,MAFAA,EAAIwG,GAAKlP,KAAKiM,KAAMvD,EAAI,EAGhC,CACA,KAAwB,EAAnB1I,KAAKiM,KAAKvD,EAAI,IAAkD,CACjE,IAAIrL,EAAQ2C,KAAKiM,KAAKvD,EAAI,GACrBrC,EAAOoN,MAAK,CAACgF,EAAG/P,IAAW,EAAJA,GAAU+P,GAAKpb,KACvCgJ,EAAOtF,KAAKf,KAAKiM,KAAKvD,GAAIrL,EAClC,CACJ,CACA,OAAOgJ,CACX,CAMA,SAAApK,CAAU4J,GAGN,IAAIsK,EAAO9I,OAAO8B,OAAO9B,OAAOC,OAAOwa,EAAS1E,WAAYpd,MAG5D,GAFI6F,EAAOsB,QACPgJ,EAAK/D,QAAUpM,KAAKoM,QAAQrD,UAAUlD,EAAOsB,QAC7CtB,EAAO8B,IAAK,CACZ,IAAIsc,EAAOjkB,KAAKqiB,SAASxc,EAAO8B,KAChC,IAAKsc,EACD,MAAM,IAAI9d,WAAW,yBAAyBN,EAAO8B,OACzDwI,EAAKxI,IAAMsc,CACf,CA2BA,OA1BIpe,EAAO6Y,aACPvO,EAAKuO,WAAa1e,KAAK0e,WAAWvc,KAAI+hB,IAClC,IAAIvb,EAAQ9C,EAAO6Y,WAAWpiB,MAAKuU,GAAKA,EAAE3S,MAAQgmB,IAClD,OAAOvb,EAAQA,EAAMrK,GAAK4lB,CAAC,KAE/Bre,EAAOuZ,eACPjP,EAAKiP,aAAepf,KAAKof,aAAapP,QACtCG,EAAK2S,iBAAmB9iB,KAAK8iB,iBAAiB3gB,KAAI,CAACqW,EAAG9P,KAClD,IAAIC,EAAQ9C,EAAOuZ,aAAa9iB,MAAKuU,GAAKA,EAAE3S,MAAQsa,EAAE2L,WACtD,IAAKxb,EACD,OAAO6P,EACX,IAAI9Q,EAAOL,OAAO8B,OAAO9B,OAAO8B,OAAO,CAAC,EAAGqP,GAAI,CAAE2L,SAAUxb,EAAMrK,KAEjE,OADA6R,EAAKiP,aAAa1W,GAAKqa,GAAerb,GAC/BA,CAAI,KAGf7B,EAAOue,iBACPjU,EAAK/T,QAAUyJ,EAAOue,gBACtBve,EAAO4T,UACPtJ,EAAKsJ,QAAUzZ,KAAKwjB,aAAa3d,EAAO4T,UACvB,MAAjB5T,EAAOkU,SACP5J,EAAK4J,OAASlU,EAAOkU,QACrBlU,EAAOwe,OACPlU,EAAK4R,SAAW5R,EAAK4R,SAAS5S,OAAOtJ,EAAOwe,OACrB,MAAvBxe,EAAOga,eACP1P,EAAK0P,aAAeha,EAAOga,cACxB1P,CACX,CAKA,WAAAmU,GACI,OAAOtkB,KAAK+hB,SAASxgB,OAAS,CAClC,CAOA,OAAAmf,CAAQ5J,GACJ,OAAO9W,KAAKujB,UAAYvjB,KAAKujB,UAAUzM,GAAQ9U,OAAO8U,GAAQ9W,KAAKkX,SAAWlX,KAAKoM,QAAQtD,MAAMgO,GAAMvP,MAAQuP,EACnH,CAKA,WAAImI,GAAY,OAAOjf,KAAKkX,QAAU,CAAG,CAIzC,WAAIjN,GAAY,OAAOjK,KAAKoM,QAAQtD,MAAM9I,KAAK2H,IAAI,GAAK,CAIxD,iBAAAwO,CAAkBW,GACd,IAAIyN,EAAOvkB,KAAKqjB,mBAChB,OAAe,MAARkB,EAAe,EAAIA,EAAKzN,IAAS,CAC5C,CAIA,YAAA0M,CAAa/J,GACT,IAAI+K,EAASnd,OAAO+a,KAAKpiB,KAAKojB,UAAW5b,EAAQgd,EAAOriB,KAAI,KAAM,IAClE,GAAIsX,EACA,IAAK,IAAIgL,KAAQhL,EAAQjT,MAAM,KAAM,CACjC,IAAInL,EAAKmpB,EAAOjc,QAAQkc,GACpBppB,GAAM,IACNmM,EAAMnM,IAAM,EACpB,CACJ,IAAIwmB,EAAW,KACf,IAAK,IAAInZ,EAAI,EAAGA,EAAI8b,EAAOjjB,OAAQmH,IAC/B,IAAKlB,EAAMkB,GACP,IAAK,IAAkCrN,EAA9B0T,EAAI/O,KAAKojB,SAASoB,EAAO9b,IAAkC,QAAxBrN,EAAK2E,KAAKiM,KAAK8C,QACtD8S,IAAaA,EAAW,IAAI6C,WAAW1kB,KAAKmjB,QAAU,KAAK9nB,GAAM,EAE9E,OAAO,IAAIumB,EAAQnI,EAASjS,EAAOqa,EACvC,CAKA,kBAAO9b,CAAY2B,GACf,OAAO,IAAIoa,EAASpa,EACxB,EAEJ,SAASwH,GAAKjD,EAAM4L,GAAO,OAAO5L,EAAK4L,GAAQ5L,EAAK4L,EAAM,IAAM,EAAK,CAYrE,SAASkL,GAAerb,GACpB,GAAIA,EAAKyc,SAAU,CACf,IAAIvJ,EAAOlT,EAAKqB,OAAS,EAA4B,EACrD,MAAO,CAAC1L,EAAOqV,IAAWhL,EAAKyc,SAAS9mB,EAAOqV,IAAU,EAAKkI,CAClE,CACA,OAAOlT,EAAKR,GAChB,CC30DA,MAoCMyd,GAAgB,CACpBC,KArCa,EAsCbC,OArCW,EAsCXC,OArCW,EAsCXC,QArCY,EAsCZC,OArCW,EAsCXC,aApCgB,EAqChBC,YApCe,EAqCfC,cApCiB,EAqCjBC,OApCW,GAqCXlR,OApCW,GAqCXmR,KApCS,GAqCTC,GApCO,GAqCPC,SApCa,GAqCbC,WApCc,GAqCdC,YApCe,GAqCfC,OA/CW,EAgDXC,WArCe,GAsCfC,KArCS,GAsCTC,KArCS,IA4CLC,GAA0B,CAC9BC,GA5CO,GA6CPC,QA5CY,GA6CZC,IA5CQ,GA6CRC,GA5CO,GA6CPC,OA5CW,GA6CXC,IA5CQ,GA6CRC,IA5CQ,GA6CR1P,MA5CU,GA6CVrG,IA5CQ,GA6CR6L,IA5CQ,GA6CRmK,OA5CW,GA6CXC,OA5CW,GA6CXC,QA5CY,GA6CZC,KA5CS,GA6CThpB,KA5CS,GA6CTipB,UA5Cc,IAoDVC,GAAkB,CAACC,UAAU,KAAKC,GAAG,IAAKC,gBAAgB,IAAKC,KAAK,IAAKC,aAAa,IAAKC,gBAAgB,IAAKC,WAAW,IAAKC,cAAc,IAAKC,cAAc,IAAKC,cAAc,IAAKC,cAAc,IAAKC,iBAAiB,IAAKC,iBAAiB,IAAKC,mBAAmB,IAAKC,gBAAgB,IAAKC,eAAe,IAAKC,iBAAiB,IAAKC,MAAM,IAAKC,SAAS,IAAKC,iBAAiB,KACzXtnB,GAASqhB,EAAS/b,YAAY,CAClCic,QAAS,GACTgB,OAAQ,ygGACRE,UAAW,+9KACXlJ,KAAM,wvCACNiI,UAAW,k4CACXkB,QAAS,IACTT,aAAc,CAAC,EAAE,IACjBR,gBAAiB,EACjBW,UAAW,+nDACXnE,WAAY,CAAC,EAAG,GAChB2D,SAAU,CAAC,MAAQ,CAAC,EAAE,KACtBlD,YAAa,CAAC,CAACrI,KAAM,GAAI5P,IAAK,CAAC7J,EAAOqV,IAzCX,CAACrV,GACrBsnB,GAActnB,EAAM2qB,iBAAmB,EAwCGC,CAAqB5qB,IAAU,GAAI,CAACyZ,KAAM,GAAI5P,IAAK,CAAC7J,EAAOqV,IAlBrF,CAACrV,GACjByoB,GAAwBzoB,EAAM2qB,iBAAmB,EAiB+DE,CAAiB7qB,IAAU,EAAK,GAAG,CAACyZ,KAAM,GAAI5P,IAAK7J,GAASspB,GAAgBtpB,KAAW,IAC9MimB,UAAW,IA0CXhiB,GAAW,GAEXI,GAAU,GACVI,GAAa,GAEb,GAAS,GAqDTqmB,GAAa,E,sEC1MXC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBznB,IAAjB0nB,EACH,OAAOA,EAAa1jB,QAGrB,IAAID,EAASwjB,EAAyBE,GAAY,CACjDjtB,GAAIitB,EACJE,QAAQ,EACR3jB,QAAS,CAAC,GAUX,OANA4jB,EAAoBH,GAAUI,KAAK9jB,EAAOC,QAASD,EAAQA,EAAOC,QAASwjB,GAG3EzjB,EAAO4jB,QAAS,EAGT5jB,EAAOC,OACf,C,OAGAwjB,EAAoBM,EAAIF,EC3BxBJ,EAAoBxV,EAAKjO,IACxB,IAAIgkB,EAAShkB,GAAUA,EAAOikB,WAC7B,IAAOjkB,EAAiB,QACxB,IAAM,EAEP,OADAyjB,EAAoBlV,EAAEyV,EAAQ,CAAElrB,EAAGkrB,IAC5BA,CAAM,EvBNV3uB,EAAWoN,OAAOyhB,eAAkBC,GAAS1hB,OAAOyhB,eAAeC,GAASA,GAASA,EAAa,UAQtGV,EAAoBnE,EAAI,SAAS7mB,EAAO0M,GAEvC,GADU,EAAPA,IAAU1M,EAAQ2C,KAAK3C,IAChB,EAAP0M,EAAU,OAAO1M,EACpB,GAAoB,iBAAVA,GAAsBA,EAAO,CACtC,GAAW,EAAP0M,GAAa1M,EAAMwrB,WAAY,OAAOxrB,EAC1C,GAAW,GAAP0M,GAAoC,mBAAf1M,EAAM2rB,KAAqB,OAAO3rB,CAC5D,CACA,IAAI4rB,EAAK5hB,OAAOC,OAAO,MACvB+gB,EAAoBxX,EAAEoY,GACtB,IAAIC,EAAM,CAAC,EACXlvB,EAAiBA,GAAkB,CAAC,KAAMC,EAAS,CAAC,GAAIA,EAAS,IAAKA,EAASA,IAC/E,IAAI,IAAIkvB,EAAiB,EAAPpf,GAAY1M,EAAyB,iBAAX8rB,KAAyBnvB,EAAeuO,QAAQ4gB,GAAUA,EAAUlvB,EAASkvB,GACxH9hB,OAAO+hB,oBAAoBD,GAASE,SAAS9rB,GAAS2rB,EAAI3rB,GAAO,IAAOF,EAAME,KAI/E,OAFA2rB,EAAa,QAAI,IAAM,EACvBb,EAAoBlV,EAAE8V,EAAIC,GACnBD,CACR,EwBxBAZ,EAAoBlV,EAAI,CAACtO,EAASykB,KACjC,IAAI,IAAI/rB,KAAO+rB,EACXjB,EAAoBkB,EAAED,EAAY/rB,KAAS8qB,EAAoBkB,EAAE1kB,EAAStH,IAC5E8J,OAAOmiB,eAAe3kB,EAAStH,EAAK,CAAEksB,YAAY,EAAMviB,IAAKoiB,EAAW/rB,IAE1E,ECND8qB,EAAoBqB,EAAI,CAAC,EAGzBrB,EAAoB7W,EAAKmY,GACjBhvB,QAAQC,IAAIyM,OAAO+a,KAAKiG,EAAoBqB,GAAG3T,QAAO,CAAC6T,EAAUrsB,KACvE8qB,EAAoBqB,EAAEnsB,GAAKosB,EAASC,GAC7BA,IACL,KCNJvB,EAAoBwB,EAAKF,GAEZA,EAAU,MCHvBtB,EAAoByB,EAAI,WACvB,GAA0B,iBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAO/pB,MAAQ,IAAIgqB,SAAS,cAAb,EAChB,CAAE,MAAOxY,GACR,GAAsB,iBAAXyY,OAAqB,OAAOA,MACxC,CACA,CAPuB,GCAxB5B,EAAoBkB,EAAI,CAACR,EAAK9gB,IAAUZ,OAAO+V,UAAU8M,eAAexB,KAAKK,EAAK9gB,G3BA9E/N,EAAa,CAAC,EACdC,EAAoB,2BAExBkuB,EAAoB8B,EAAI,CAACC,EAAKrV,EAAMxX,EAAKosB,KACxC,GAAGzvB,EAAWkwB,GAAQlwB,EAAWkwB,GAAKrpB,KAAKgU,OAA3C,CACA,IAAIsV,EAAQC,EACZ,QAAWzpB,IAARtD,EAEF,IADA,IAAIgtB,EAAUC,SAASC,qBAAqB,UACpC/hB,EAAI,EAAGA,EAAI6hB,EAAQhpB,OAAQmH,IAAK,CACvC,IAAI8P,EAAI+R,EAAQ7hB,GAChB,GAAG8P,EAAEkS,aAAa,QAAUN,GAAO5R,EAAEkS,aAAa,iBAAmBvwB,EAAoBoD,EAAK,CAAE8sB,EAAS7R,EAAG,KAAO,CACpH,CAEG6R,IACHC,GAAa,GACbD,EAASG,SAASG,cAAc,WAEzBC,QAAU,QACjBP,EAAOQ,QAAU,IACbxC,EAAoByC,IACvBT,EAAOU,aAAa,QAAS1C,EAAoByC,IAElDT,EAAOU,aAAa,eAAgB5wB,EAAoBoD,GAExD8sB,EAAOviB,IAAMsiB,GAEdlwB,EAAWkwB,GAAO,CAACrV,GACnB,IAAIiW,EAAmB,CAAC1X,EAAM2X,KAE7BZ,EAAOa,QAAUb,EAAOc,OAAS,KACjCC,aAAaP,GACb,IAAIQ,EAAUnxB,EAAWkwB,GAIzB,UAHOlwB,EAAWkwB,GAClBC,EAAOiB,YAAcjB,EAAOiB,WAAWC,YAAYlB,GACnDgB,GAAWA,EAAQhC,SAASmC,GAAQA,EAAGP,KACpC3X,EAAM,OAAOA,EAAK2X,EAAM,EAExBJ,EAAUY,WAAWT,EAAiBU,KAAK,UAAM7qB,EAAW,CAAEpE,KAAM,UAAWF,OAAQ8tB,IAAW,MACtGA,EAAOa,QAAUF,EAAiBU,KAAK,KAAMrB,EAAOa,SACpDb,EAAOc,OAASH,EAAiBU,KAAK,KAAMrB,EAAOc,QACnDb,GAAcE,SAASmB,KAAKC,YAAYvB,EApCkB,CAoCX,E4BvChDhC,EAAoBxX,EAAKhM,IACH,oBAAXgnB,QAA0BA,OAAOC,aAC1CzkB,OAAOmiB,eAAe3kB,EAASgnB,OAAOC,YAAa,CAAEzuB,MAAO,WAE7DgK,OAAOmiB,eAAe3kB,EAAS,aAAc,CAAExH,OAAO,GAAO,ECL9DgrB,EAAoB0D,IAAOnnB,IAC1BA,EAAOonB,MAAQ,GACVpnB,EAAO6E,WAAU7E,EAAO6E,SAAW,IACjC7E,GCHRyjB,EAAoBlW,EAAI,0C,MCAxBkW,EAAoB1qB,EAAI6sB,SAASyB,SAAWC,KAAK7sB,SAAS8sB,KAK1D,IAAIC,EAAkB,CACrB,IAAK,GAGN/D,EAAoBqB,EAAE3a,EAAI,CAAC4a,EAASC,KAElC,IAAIyC,EAAqBhE,EAAoBkB,EAAE6C,EAAiBzC,GAAWyC,EAAgBzC,QAAW9oB,EACtG,GAA0B,IAAvBwrB,EAGF,GAAGA,EACFzC,EAAS7oB,KAAKsrB,EAAmB,QAC3B,CAGL,IAAIC,EAAU,IAAI3xB,SAAQ,CAAC6P,EAAS+hB,IAAYF,EAAqBD,EAAgBzC,GAAW,CAACnf,EAAS+hB,KAC1G3C,EAAS7oB,KAAKsrB,EAAmB,GAAKC,GAGtC,IAAIlC,EAAM/B,EAAoBlW,EAAIkW,EAAoBwB,EAAEF,GAEpD9hB,EAAQ,IAAI7B,MAgBhBqiB,EAAoB8B,EAAEC,GAfFa,IACnB,GAAG5C,EAAoBkB,EAAE6C,EAAiBzC,KAEf,KAD1B0C,EAAqBD,EAAgBzC,MACRyC,EAAgBzC,QAAW9oB,GACrDwrB,GAAoB,CACtB,IAAIG,EAAYvB,IAAyB,SAAfA,EAAMxuB,KAAkB,UAAYwuB,EAAMxuB,MAChEgwB,EAAUxB,GAASA,EAAM1uB,QAAU0uB,EAAM1uB,OAAOuL,IACpDD,EAAM6kB,QAAU,iBAAmB/C,EAAU,cAAgB6C,EAAY,KAAOC,EAAU,IAC1F5kB,EAAMN,KAAO,iBACbM,EAAMpL,KAAO+vB,EACb3kB,EAAM8kB,QAAUF,EAChBJ,EAAmB,GAAGxkB,EACvB,CACD,GAEwC,SAAW8hB,EAASA,EAE/D,CACD,EAcF,IAAIiD,EAAuB,CAACC,EAA4B5gB,KACvD,IAGIqc,EAAUqB,GAHTmD,EAAUC,EAAaC,GAAW/gB,EAGhBvD,EAAI,EAC3B,GAAGokB,EAASrZ,MAAMpY,GAAgC,IAAxB+wB,EAAgB/wB,KAAa,CACtD,IAAIitB,KAAYyE,EACZ1E,EAAoBkB,EAAEwD,EAAazE,KACrCD,EAAoBM,EAAEL,GAAYyE,EAAYzE,IAG7C0E,GAAsBA,EAAQ3E,EAClC,CAEA,IADGwE,GAA4BA,EAA2B5gB,GACrDvD,EAAIokB,EAASvrB,OAAQmH,IACzBihB,EAAUmD,EAASpkB,GAChB2f,EAAoBkB,EAAE6C,EAAiBzC,IAAYyC,EAAgBzC,IACrEyC,EAAgBzC,GAAS,KAE1ByC,EAAgBzC,GAAW,CAC5B,EAIGsD,EAAqBf,KAA0C,oCAAIA,KAA0C,qCAAK,GACtHe,EAAmB5D,QAAQuD,EAAqBlB,KAAK,KAAM,IAC3DuB,EAAmBlsB,KAAO6rB,EAAqBlB,KAAK,KAAMuB,EAAmBlsB,KAAK2qB,KAAKuB,G,KClF7D5E,EAAoB,K", "sources": ["webpack://grafana-lokiexplore-app/webpack/runtime/create fake namespace object", "webpack://grafana-lokiexplore-app/webpack/runtime/load script", "webpack://grafana-lokiexplore-app/./module.tsx", "webpack://grafana-lokiexplore-app/./services/extensions/links.ts", "webpack://grafana-lokiexplore-app/./services/fieldsTypes.ts", "webpack://grafana-lokiexplore-app/./services/filterTypes.ts", "webpack://grafana-lokiexplore-app/./services/logqlMatchers.ts", "webpack://grafana-lokiexplore-app/./services/variables.ts", "webpack://grafana-lokiexplore-app/external amd \"@emotion/css\"", "webpack://grafana-lokiexplore-app/external amd \"@grafana/data\"", "webpack://grafana-lokiexplore-app/external amd \"@grafana/runtime\"", "webpack://grafana-lokiexplore-app/external amd \"@grafana/ui\"", "webpack://grafana-lokiexplore-app/external amd \"lodash\"", "webpack://grafana-lokiexplore-app/external amd \"react\"", "webpack://grafana-lokiexplore-app/external amd \"react-dom\"", "webpack://grafana-lokiexplore-app/external amd \"react-redux\"", "webpack://grafana-lokiexplore-app/external amd \"react-router-dom\"", "webpack://grafana-lokiexplore-app/external amd \"redux\"", "webpack://grafana-lokiexplore-app/external amd \"rxjs\"", "webpack://grafana-lokiexplore-app/../node_modules/@lezer/common/dist/index.js", "webpack://grafana-lokiexplore-app/../node_modules/@lezer/lr/dist/index.js", "webpack://grafana-lokiexplore-app/../node_modules/@grafana/lezer-logql/index.es.js", "webpack://grafana-lokiexplore-app/webpack/bootstrap", "webpack://grafana-lokiexplore-app/webpack/runtime/compat get default export", "webpack://grafana-lokiexplore-app/webpack/runtime/define property getters", "webpack://grafana-lokiexplore-app/webpack/runtime/ensure chunk", "webpack://grafana-lokiexplore-app/webpack/runtime/get javascript chunk filename", "webpack://grafana-lokiexplore-app/webpack/runtime/global", "webpack://grafana-lokiexplore-app/webpack/runtime/hasOwnProperty shorthand", "webpack://grafana-lokiexplore-app/webpack/runtime/make namespace object", "webpack://grafana-lokiexplore-app/webpack/runtime/node module decorator", "webpack://grafana-lokiexplore-app/webpack/runtime/publicPath", "webpack://grafana-lokiexplore-app/webpack/runtime/jsonp chunk loading", "webpack://grafana-lokiexplore-app/webpack/startup"], "sourcesContent": ["var getProto = Object.getPrototypeOf ? (obj) => (Object.getPrototypeOf(obj)) : (obj) => (obj.__proto__);\nvar leafPrototypes;\n// create a fake namespace object\n// mode & 1: value is a module id, require it\n// mode & 2: merge all properties of value into the ns\n// mode & 4: return value when already ns object\n// mode & 16: return value when it's Promise-like\n// mode & 8|1: behave like require\n__webpack_require__.t = function(value, mode) {\n\tif(mode & 1) value = this(value);\n\tif(mode & 8) return value;\n\tif(typeof value === 'object' && value) {\n\t\tif((mode & 4) && value.__esModule) return value;\n\t\tif((mode & 16) && typeof value.then === 'function') return value;\n\t}\n\tvar ns = Object.create(null);\n\t__webpack_require__.r(ns);\n\tvar def = {};\n\tleafPrototypes = leafPrototypes || [null, getProto({}), getProto([]), getProto(getProto)];\n\tfor(var current = mode & 2 && value; typeof current == 'object' && !~leafPrototypes.indexOf(current); current = getProto(current)) {\n\t\tObject.getOwnPropertyNames(current).forEach((key) => (def[key] = () => (value[key])));\n\t}\n\tdef['default'] = () => (value);\n\t__webpack_require__.d(ns, def);\n\treturn ns;\n};", "var inProgress = {};\nvar dataWebpackPrefix = \"grafana-lokiexplore-app:\";\n// loadScript function to load a script via script tag\n__webpack_require__.l = (url, done, key, chunkId) => {\n\tif(inProgress[url]) { inProgress[url].push(done); return; }\n\tvar script, needAttach;\n\tif(key !== undefined) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tfor(var i = 0; i < scripts.length; i++) {\n\t\t\tvar s = scripts[i];\n\t\t\tif(s.getAttribute(\"src\") == url || s.getAttribute(\"data-webpack\") == dataWebpackPrefix + key) { script = s; break; }\n\t\t}\n\t}\n\tif(!script) {\n\t\tneedAttach = true;\n\t\tscript = document.createElement('script');\n\n\t\tscript.charset = 'utf-8';\n\t\tscript.timeout = 120;\n\t\tif (__webpack_require__.nc) {\n\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n\t\t}\n\t\tscript.setAttribute(\"data-webpack\", dataWebpackPrefix + key);\n\n\t\tscript.src = url;\n\t}\n\tinProgress[url] = [done];\n\tvar onScriptComplete = (prev, event) => {\n\t\t// avoid mem leaks in IE.\n\t\tscript.onerror = script.onload = null;\n\t\tclearTimeout(timeout);\n\t\tvar doneFns = inProgress[url];\n\t\tdelete inProgress[url];\n\t\tscript.parentNode && script.parentNode.removeChild(script);\n\t\tdoneFns && doneFns.forEach((fn) => (fn(event)));\n\t\tif(prev) return prev(event);\n\t}\n\tvar timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);\n\tscript.onerror = onScriptComplete.bind(null, script.onerror);\n\tscript.onload = onScriptComplete.bind(null, script.onload);\n\tneedAttach && document.head.appendChild(script);\n};", "import { lazy } from 'react';\nimport { AppPlugin } from '@grafana/data';\nimport { linkConfigs } from 'services/extensions/links';\n\n// Anything imported in this file is included in the main bundle which is pre-loaded in Grafana\n// Don't add imports to this file without lazy loading\n// Link extensions are the exception as they must be included in the main bundle in order to work in core Grafana\nconst App = lazy(async () => {\n  const { wasmSupported } = await import('services/sorting');\n\n  const { default: initRuntimeDs } = await import('services/datasource');\n  const { default: initChangepoint } = await import('@bsull/augurs/changepoint');\n  const { default: initOutlier } = await import('@bsull/augurs/outlier');\n\n  initRuntimeDs();\n\n  if (wasmSupported()) {\n    await Promise.all([initChangepoint(), initOutlier()]);\n  }\n\n  return import('Components/App');\n});\n\nconst AppConfig = lazy(async () => {\n  return await import('./Components/AppConfig/AppConfig');\n});\n\nexport const plugin = new AppPlugin<{}>().setRootPage(App).addConfigPage({\n  title: 'Configuration',\n  icon: 'cog',\n  body: AppConfig,\n  id: 'configuration',\n});\n\nfor (const linkConfig of linkConfigs) {\n  plugin.addLink(linkConfig);\n}\n", "// Warning: This file (and any imports) are included in the main bundle with Grafana in order to provide link extension support in Grafana core, in an effort to keep Grafana loading quickly, please do not add any unnecessary imports to this file and run the bundle analyzer before committing any changes!\nimport { PluginExtensionLinkConfig, PluginExtensionPanelContext, PluginExtensionPoints } from '@grafana/data';\n\nimport { SERVICE_NAME, VAR_DATASOURCE, VAR_FIELDS, VAR_LABELS } from 'services/variables';\nimport pluginJson from '../../plugin.json';\nimport { LabelType } from '../fieldsTypes';\nimport { getMatcherFromQuery } from '../logqlMatchers';\nimport { LokiQuery } from '../lokiQuery';\nimport { FilterOp } from '../filterTypes';\n\nconst title = 'Open in Explore Logs';\nconst description = 'Open current query in the Explore Logs view';\nconst icon = 'gf-logs';\n\nexport const ExtensionPoints = {\n  MetricExploration: 'grafana-lokiexplore-app/metric-exploration/v1',\n} as const;\n\n// `plugin.addLink` requires these types; unfortunately, the correct `PluginExtensionAddedLinkConfig` type is not exported with 11.2.x\n// TODO: fix this type when we move to `@grafana/data` 11.3.x\nexport const linkConfigs: Array<\n  {\n    targets: string | string[];\n    // eslint-disable-next-line deprecation/deprecation\n  } & Omit<PluginExtensionLinkConfig<PluginExtensionPanelContext>, 'type' | 'extensionPointId'>\n> = [\n  {\n    targets: PluginExtensionPoints.DashboardPanelMenu,\n    title,\n    description,\n    icon,\n    path: createAppUrl(),\n    configure: contextToLink,\n  },\n  {\n    targets: PluginExtensionPoints.ExploreToolbarAction,\n    title,\n    description,\n    icon,\n    path: createAppUrl(),\n    configure: contextToLink,\n  },\n];\n\nfunction contextToLink<T extends PluginExtensionPanelContext>(context?: T) {\n  if (!context) {\n    return undefined;\n  }\n  const lokiQuery = context.targets.find((target) => target.datasource?.type === 'loki') as LokiQuery | undefined;\n  if (!lokiQuery || !lokiQuery.datasource?.uid) {\n    return undefined;\n  }\n\n  const expr = lokiQuery.expr;\n  const labelFilters = getMatcherFromQuery(expr);\n\n  const labelSelector = labelFilters.find((selector) => selector.operator === FilterOp.Equal);\n\n  if (!labelSelector) {\n    return undefined;\n  }\n\n  const labelValue = replaceSlash(labelSelector.value);\n  let labelName = labelSelector.key === SERVICE_NAME ? 'service' : labelSelector.key;\n  // sort `primary label` first\n  labelFilters.sort((a, b) => (a.key === labelName ? -1 : 1));\n\n  let params = setUrlParameter(UrlParameters.DatasourceId, lokiQuery.datasource?.uid);\n  params = setUrlParameter(UrlParameters.TimeRangeFrom, context.timeRange.from.valueOf().toString(), params);\n  params = setUrlParameter(UrlParameters.TimeRangeTo, context.timeRange.to.valueOf().toString(), params);\n\n  for (const labelFilter of labelFilters) {\n    // skip non-indexed filters for now\n    if (labelFilter.type !== LabelType.Indexed) {\n      continue;\n    }\n\n    params = appendUrlParameter(\n      UrlParameters.Labels,\n      `${labelFilter.key}|${labelFilter.operator}|${labelFilter.value}`,\n      params\n    );\n  }\n  return {\n    path: createAppUrl(`/explore/${labelName}/${labelValue}/logs`, params),\n  };\n}\n\nexport function createAppUrl(path = '/explore', urlParams?: URLSearchParams): string {\n  return `/a/${pluginJson.id}${path}${urlParams ? `?${urlParams.toString()}` : ''}`;\n}\n\nexport const UrlParameters = {\n  DatasourceId: `var-${VAR_DATASOURCE}`,\n  TimeRangeFrom: 'from',\n  TimeRangeTo: 'to',\n  Labels: `var-${VAR_LABELS}`,\n  Fields: `var-${VAR_FIELDS}`,\n} as const;\nexport type UrlParameterType = (typeof UrlParameters)[keyof typeof UrlParameters];\n\nexport function setUrlParameter(key: UrlParameterType, value: string, initalParams?: URLSearchParams): URLSearchParams {\n  const searchParams = new URLSearchParams(initalParams?.toString() ?? location.search);\n  searchParams.set(key, value);\n\n  return searchParams;\n}\n\nexport function appendUrlParameter(\n  key: UrlParameterType,\n  value: string,\n  initalParams?: URLSearchParams\n): URLSearchParams {\n  const searchParams = new URLSearchParams(initalParams?.toString() ?? location.search);\n  searchParams.append(key, value);\n\n  return searchParams;\n}\n\nexport function replaceSlash(parameter: string): string {\n  return parameter.replace(/\\//g, '-');\n}\n", "// Warning: This file is included in the main bundle with Grafana in order to provide link extension support in Grafana core, in an effort to keep Grafana loading quickly, please do not add any unnecessary imports to this file and run the bundle analyzer before committing any changes!\n// copied from public/app/plugins/datasource/loki/types.ts\nexport enum LabelType {\n  Indexed = 'I',\n  StructuredMetadata = 'S',\n  Parsed = 'P',\n}\n", "// Warning: This file (and any imports) are included in the main bundle with Grafana in order to provide link extension support in Grafana core, in an effort to keep Grafana loading quickly, please do not add any unnecessary imports to this file and run the bundle analyzer before committing any changes!\nimport { LabelType } from './fieldsTypes';\n\nexport enum FilterOp {\n  Equal = '=',\n  NotEqual = '!=',\n  gt = '>',\n  lt = '<',\n  gte = '>=',\n  lte = '<=',\n}\n\nexport type Filter = {\n  key: string;\n  operator: FilterOp;\n  value: string;\n  type?: LabelType;\n};\n", "// Warning: This file (and any imports) are included in the main bundle with Grafana in order to provide link extension support in Grafana core, in an effort to keep Grafana loading quickly, please do not add any unnecessary imports to this file and run the bundle analyzer before committing any changes!\n\nimport { Identifier, Matcher, parser, Selector, String } from '@grafana/lezer-logql';\nimport { NodeType, SyntaxNode, Tree } from '@lezer/common';\nimport { LabelType } from './fieldsTypes';\nimport { Filter, FilterOp } from './filterTypes';\n\nexport class NodePosition {\n  from: number;\n  to: number;\n  type: NodeType;\n  syntaxNode: SyntaxNode;\n\n  constructor(from: number, to: number, syntaxNode: SyntaxNode, type: NodeType) {\n    this.from = from;\n    this.to = to;\n    this.type = type;\n    this.syntaxNode = syntaxNode;\n  }\n\n  static fromNode(node: SyntaxNode): NodePosition {\n    return new NodePosition(node.from, node.to, node, node.type);\n  }\n\n  contains(position: NodePosition): boolean {\n    return this.from <= position.from && this.to >= position.to;\n  }\n\n  getExpression(query: string): string {\n    return query.substring(this.from, this.to);\n  }\n}\n\nexport function getNodesFromQuery(query: string, nodeTypes?: number[]): SyntaxNode[] {\n  const nodes: SyntaxNode[] = [];\n  const tree: Tree = parser.parse(query);\n  tree.iterate({\n    enter: (node): false | void => {\n      if (nodeTypes === undefined || nodeTypes.includes(node.type.id)) {\n        nodes.push(node.node);\n      }\n    },\n  });\n  return nodes;\n}\n\nfunction getAllPositionsInNodeByType(node: SyntaxNode, type: number): NodePosition[] {\n  if (node.type.id === type) {\n    return [NodePosition.fromNode(node)];\n  }\n\n  const positions: NodePosition[] = [];\n  let pos = 0;\n  let child = node.childAfter(pos);\n  while (child) {\n    positions.push(...getAllPositionsInNodeByType(child, type));\n    pos = child.to;\n    child = node.childAfter(pos);\n  }\n  return positions;\n}\n\nexport function getMatcherFromQuery(query: string): Filter[] {\n  const filter: Filter[] = [];\n  const selector = getNodesFromQuery(query, [Selector]);\n  if (selector.length === 0) {\n    return filter;\n  }\n  const selectorPosition = NodePosition.fromNode(selector[0]);\n\n  const allMatcher = getNodesFromQuery(query, [Matcher]);\n  for (const matcher of allMatcher) {\n    const matcherPosition = NodePosition.fromNode(matcher);\n    const identifierPosition = getAllPositionsInNodeByType(matcher, Identifier);\n    const valuePosition = getAllPositionsInNodeByType(matcher, String);\n    const operation = query.substring(identifierPosition[0].to, valuePosition[0].from);\n    const op = operation === '=' ? FilterOp.Equal : FilterOp.NotEqual;\n    const key = identifierPosition[0].getExpression(query);\n    const value = valuePosition.map((position) => query.substring(position.from + 1, position.to - 1))[0];\n\n    if (!key || !value) {\n      continue;\n    }\n\n    filter.push({\n      key,\n      operator: op,\n      value,\n      type: selectorPosition.contains(matcherPosition) ? LabelType.Indexed : undefined,\n    });\n  }\n\n  return filter;\n}\n\nexport function isQueryWithNode(query: string, nodeType: number): boolean {\n  let isQueryWithNode = false;\n  const tree = parser.parse(query);\n  tree.iterate({\n    enter: ({ type }): false | void => {\n      if (type.id === nodeType) {\n        isQueryWithNode = true;\n        return false;\n      }\n    },\n  });\n  return isQueryWithNode;\n}\n\n/**\n * Parses the query and looks for error nodes. If there is at least one, it returns true.\n * Grafana variables are considered errors, so if you need to validate a query\n * with variables you should interpolate it first.\n */\nexport const ErrorId = 0;\nexport function isValidQuery(query: string): boolean {\n  return isQueryWithNode(query, ErrorId) === false;\n}\n", "// Warning, this file is included in the main module.tsx bundle, and doesn't contain any imports to keep that bundle size small. Don't add imports to this file!\n\nexport interface FieldValue {\n  value: string;\n  parser: ParserType;\n}\n\nexport interface AdHocFieldValue {\n  value?: string;\n  parser?: ParserType;\n}\n\nexport type ParserType = 'logfmt' | 'json' | 'mixed' | 'structuredMetadata';\nexport type DetectedFieldType = 'int' | 'float' | 'duration' | 'bytes' | 'boolean' | 'string';\n\nexport type LogsQueryOptions = {\n  labelExpressionToAdd?: string;\n  structuredMetadataToAdd?: string;\n  fieldExpressionToAdd?: string;\n  parser?: ParserType;\n  fieldType?: DetectedFieldType;\n};\n\nexport const VAR_LABELS = 'filters';\nexport const VAR_LABELS_EXPR = '${filters}';\nexport const VAR_LABELS_REPLICA = 'filters_replica';\nexport const VAR_LABELS_REPLICA_EXPR = '${filters_replica}';\nexport const VAR_FIELDS = 'fields';\nexport const VAR_FIELDS_EXPR = '${fields}';\nexport const PENDING_FIELDS_EXPR = '${pendingFields}';\nexport const VAR_METADATA = 'metadata';\nexport const VAR_METADATA_EXPR = '${metadata}';\nexport const VAR_PATTERNS = 'patterns';\nexport const VAR_PATTERNS_EXPR = '${patterns}';\nexport const VAR_LEVELS = 'levels';\nexport const VAR_LEVELS_EXPR = '${levels}';\nexport const VAR_FIELD_GROUP_BY = 'fieldBy';\nexport const VAR_LABEL_GROUP_BY = 'labelBy';\nexport const VAR_LABEL_GROUP_BY_EXPR = '${labelBy}';\nexport const VAR_PRIMARY_LABEL_SEARCH = 'primary_label_search';\nexport const VAR_PRIMARY_LABEL_SEARCH_EXPR = '${primary_label_search}';\nexport const VAR_PRIMARY_LABEL = 'primary_label';\nexport const VAR_PRIMARY_LABEL_EXPR = '${primary_label}';\nexport const VAR_DATASOURCE = 'ds';\nexport const VAR_DATASOURCE_EXPR = '${ds}';\nexport const MIXED_FORMAT_EXPR = `| json | logfmt | drop __error__, __error_details__`;\nexport const JSON_FORMAT_EXPR = `| json | drop __error__, __error_details__`;\nexport const LOGS_FORMAT_EXPR = `| logfmt`;\n// This variable is hardcoded to the value of MIXED_FORMAT_EXPR. This is a hack to get logs context working, we don't want to use a variable for a value that doesn't change and cannot be updated by the user.\nexport const VAR_LOGS_FORMAT = 'logsFormat';\nexport const VAR_LOGS_FORMAT_EXPR = '${logsFormat}';\nexport const VAR_LINE_FILTER = 'lineFilter';\nexport const VAR_LINE_FILTER_EXPR = '${lineFilter}';\nexport const LOG_STREAM_SELECTOR_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_PATTERNS_EXPR} ${VAR_METADATA_EXPR} ${VAR_LINE_FILTER_EXPR} ${VAR_LEVELS_EXPR} ${VAR_LOGS_FORMAT_EXPR} ${VAR_FIELDS_EXPR}`;\n// Same as the LOG_STREAM_SELECTOR_EXPR, but without the fields as they will need to be built manually to exclude the current filter value\nexport const DETECTED_FIELD_VALUES_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_PATTERNS_EXPR} ${VAR_METADATA_EXPR} ${VAR_LINE_FILTER_EXPR} ${VAR_LEVELS_EXPR} ${VAR_LOGS_FORMAT_EXPR} ${PENDING_FIELDS_EXPR}`;\nexport const DETECTED_METADATA_VALUES_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_PATTERNS_EXPR} ${PENDING_FIELDS_EXPR} ${VAR_LINE_FILTER_EXPR} ${VAR_LEVELS_EXPR} ${VAR_LOGS_FORMAT_EXPR} ${VAR_FIELDS_EXPR}`;\nexport const DETECTED_LEVELS_VALUES_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_PATTERNS_EXPR} ${VAR_METADATA_EXPR} ${VAR_LINE_FILTER_EXPR} ${PENDING_FIELDS_EXPR} ${VAR_LOGS_FORMAT_EXPR} ${VAR_FIELDS_EXPR}`;\nexport const PATTERNS_SAMPLE_SELECTOR_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LOGS_FORMAT_EXPR}`;\nexport const EXPLORATION_DS = { uid: VAR_DATASOURCE_EXPR };\nexport const ALL_VARIABLE_VALUE = '$__all';\nexport const LEVEL_VARIABLE_VALUE = 'detected_level';\nexport const SERVICE_NAME = 'service_name';\nexport const SERVICE_UI_LABEL = 'service';\nexport const VAR_AGGREGATED_METRICS = 'var_aggregated_metrics';\nexport const VAR_AGGREGATED_METRICS_EXPR = '${var_aggregated_metrics}';\nexport const EMPTY_VARIABLE_VALUE = '\"\"';\n", "module.exports = __WEBPACK_EXTERNAL_MODULE__6089__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__7781__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__8531__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__2007__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__3241__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__5959__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__8398__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__200__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__3806__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__7694__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__1269__;", "/**\nThe default maximum length of a `TreeBuffer` node.\n*/\nconst DefaultBufferLength = 1024;\nlet nextPropID = 0;\nclass Range {\n    constructor(from, to) {\n        this.from = from;\n        this.to = to;\n    }\n}\n/**\nEach [node type](#common.NodeType) or [individual tree](#common.Tree)\ncan have metadata associated with it in props. Instances of this\nclass represent prop names.\n*/\nclass NodeProp {\n    /**\n    Create a new node prop type.\n    */\n    constructor(config = {}) {\n        this.id = nextPropID++;\n        this.perNode = !!config.perNode;\n        this.deserialize = config.deserialize || (() => {\n            throw new Error(\"This node type doesn't define a deserialize function\");\n        });\n    }\n    /**\n    This is meant to be used with\n    [`NodeSet.extend`](#common.NodeSet.extend) or\n    [`LRParser.configure`](#lr.ParserConfig.props) to compute\n    prop values for each node type in the set. Takes a [match\n    object](#common.NodeType^match) or function that returns undefined\n    if the node type doesn't get this prop, and the prop's value if\n    it does.\n    */\n    add(match) {\n        if (this.perNode)\n            throw new RangeError(\"Can't add per-node props to node types\");\n        if (typeof match != \"function\")\n            match = NodeType.match(match);\n        return (type) => {\n            let result = match(type);\n            return result === undefined ? null : [this, result];\n        };\n    }\n}\n/**\nProp that is used to describe matching delimiters. For opening\ndelimiters, this holds an array of node names (written as a\nspace-separated string when declaring this prop in a grammar)\nfor the node types of closing delimiters that match it.\n*/\nNodeProp.closedBy = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nThe inverse of [`closedBy`](#common.NodeProp^closedBy). This is\nattached to closing delimiters, holding an array of node names\nof types of matching opening delimiters.\n*/\nNodeProp.openedBy = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nUsed to assign node types to groups (for example, all node\ntypes that represent an expression could be tagged with an\n`\"Expression\"` group).\n*/\nNodeProp.group = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nAttached to nodes to indicate these should be\n[displayed](https://codemirror.net/docs/ref/#language.syntaxTree)\nin a bidirectional text isolate, so that direction-neutral\ncharacters on their sides don't incorrectly get associated with\nsurrounding text. You'll generally want to set this for nodes\nthat contain arbitrary text, like strings and comments, and for\nnodes that appear _inside_ arbitrary text, like HTML tags. When\nnot given a value, in a grammar declaration, defaults to\n`\"auto\"`.\n*/\nNodeProp.isolate = new NodeProp({ deserialize: value => {\n        if (value && value != \"rtl\" && value != \"ltr\" && value != \"auto\")\n            throw new RangeError(\"Invalid value for isolate: \" + value);\n        return value || \"auto\";\n    } });\n/**\nThe hash of the [context](#lr.ContextTracker.constructor)\nthat the node was parsed in, if any. Used to limit reuse of\ncontextual nodes.\n*/\nNodeProp.contextHash = new NodeProp({ perNode: true });\n/**\nThe distance beyond the end of the node that the tokenizer\nlooked ahead for any of the tokens inside the node. (The LR\nparser only stores this when it is larger than 25, for\nefficiency reasons.)\n*/\nNodeProp.lookAhead = new NodeProp({ perNode: true });\n/**\nThis per-node prop is used to replace a given node, or part of a\nnode, with another tree. This is useful to include trees from\ndifferent languages in mixed-language parsers.\n*/\nNodeProp.mounted = new NodeProp({ perNode: true });\n/**\nA mounted tree, which can be [stored](#common.NodeProp^mounted) on\na tree node to indicate that parts of its content are\nrepresented by another tree.\n*/\nclass MountedTree {\n    constructor(\n    /**\n    The inner tree.\n    */\n    tree, \n    /**\n    If this is null, this tree replaces the entire node (it will\n    be included in the regular iteration instead of its host\n    node). If not, only the given ranges are considered to be\n    covered by this tree. This is used for trees that are mixed in\n    a way that isn't strictly hierarchical. Such mounted trees are\n    only entered by [`resolveInner`](#common.Tree.resolveInner)\n    and [`enter`](#common.SyntaxNode.enter).\n    */\n    overlay, \n    /**\n    The parser used to create this subtree.\n    */\n    parser) {\n        this.tree = tree;\n        this.overlay = overlay;\n        this.parser = parser;\n    }\n    /**\n    @internal\n    */\n    static get(tree) {\n        return tree && tree.props && tree.props[NodeProp.mounted.id];\n    }\n}\nconst noProps = Object.create(null);\n/**\nEach node in a syntax tree has a node type associated with it.\n*/\nclass NodeType {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The name of the node type. Not necessarily unique, but if the\n    grammar was written properly, different node types with the\n    same name within a node set should play the same semantic\n    role.\n    */\n    name, \n    /**\n    @internal\n    */\n    props, \n    /**\n    The id of this node in its set. Corresponds to the term ids\n    used in the parser.\n    */\n    id, \n    /**\n    @internal\n    */\n    flags = 0) {\n        this.name = name;\n        this.props = props;\n        this.id = id;\n        this.flags = flags;\n    }\n    /**\n    Define a node type.\n    */\n    static define(spec) {\n        let props = spec.props && spec.props.length ? Object.create(null) : noProps;\n        let flags = (spec.top ? 1 /* NodeFlag.Top */ : 0) | (spec.skipped ? 2 /* NodeFlag.Skipped */ : 0) |\n            (spec.error ? 4 /* NodeFlag.Error */ : 0) | (spec.name == null ? 8 /* NodeFlag.Anonymous */ : 0);\n        let type = new NodeType(spec.name || \"\", props, spec.id, flags);\n        if (spec.props)\n            for (let src of spec.props) {\n                if (!Array.isArray(src))\n                    src = src(type);\n                if (src) {\n                    if (src[0].perNode)\n                        throw new RangeError(\"Can't store a per-node prop on a node type\");\n                    props[src[0].id] = src[1];\n                }\n            }\n        return type;\n    }\n    /**\n    Retrieves a node prop for this type. Will return `undefined` if\n    the prop isn't present on this node.\n    */\n    prop(prop) { return this.props[prop.id]; }\n    /**\n    True when this is the top node of a grammar.\n    */\n    get isTop() { return (this.flags & 1 /* NodeFlag.Top */) > 0; }\n    /**\n    True when this node is produced by a skip rule.\n    */\n    get isSkipped() { return (this.flags & 2 /* NodeFlag.Skipped */) > 0; }\n    /**\n    Indicates whether this is an error node.\n    */\n    get isError() { return (this.flags & 4 /* NodeFlag.Error */) > 0; }\n    /**\n    When true, this node type doesn't correspond to a user-declared\n    named node, for example because it is used to cache repetition.\n    */\n    get isAnonymous() { return (this.flags & 8 /* NodeFlag.Anonymous */) > 0; }\n    /**\n    Returns true when this node's name or one of its\n    [groups](#common.NodeProp^group) matches the given string.\n    */\n    is(name) {\n        if (typeof name == 'string') {\n            if (this.name == name)\n                return true;\n            let group = this.prop(NodeProp.group);\n            return group ? group.indexOf(name) > -1 : false;\n        }\n        return this.id == name;\n    }\n    /**\n    Create a function from node types to arbitrary values by\n    specifying an object whose property names are node or\n    [group](#common.NodeProp^group) names. Often useful with\n    [`NodeProp.add`](#common.NodeProp.add). You can put multiple\n    names, separated by spaces, in a single property name to map\n    multiple node names to a single value.\n    */\n    static match(map) {\n        let direct = Object.create(null);\n        for (let prop in map)\n            for (let name of prop.split(\" \"))\n                direct[name] = map[prop];\n        return (node) => {\n            for (let groups = node.prop(NodeProp.group), i = -1; i < (groups ? groups.length : 0); i++) {\n                let found = direct[i < 0 ? node.name : groups[i]];\n                if (found)\n                    return found;\n            }\n        };\n    }\n}\n/**\nAn empty dummy node type to use when no actual type is available.\n*/\nNodeType.none = new NodeType(\"\", Object.create(null), 0, 8 /* NodeFlag.Anonymous */);\n/**\nA node set holds a collection of node types. It is used to\ncompactly represent trees by storing their type ids, rather than a\nfull pointer to the type object, in a numeric array. Each parser\n[has](#lr.LRParser.nodeSet) a node set, and [tree\nbuffers](#common.TreeBuffer) can only store collections of nodes\nfrom the same set. A set can have a maximum of 2**16 (65536) node\ntypes in it, so that the ids fit into 16-bit typed array slots.\n*/\nclass NodeSet {\n    /**\n    Create a set with the given types. The `id` property of each\n    type should correspond to its position within the array.\n    */\n    constructor(\n    /**\n    The node types in this set, by id.\n    */\n    types) {\n        this.types = types;\n        for (let i = 0; i < types.length; i++)\n            if (types[i].id != i)\n                throw new RangeError(\"Node type ids should correspond to array positions when creating a node set\");\n    }\n    /**\n    Create a copy of this set with some node properties added. The\n    arguments to this method can be created with\n    [`NodeProp.add`](#common.NodeProp.add).\n    */\n    extend(...props) {\n        let newTypes = [];\n        for (let type of this.types) {\n            let newProps = null;\n            for (let source of props) {\n                let add = source(type);\n                if (add) {\n                    if (!newProps)\n                        newProps = Object.assign({}, type.props);\n                    newProps[add[0].id] = add[1];\n                }\n            }\n            newTypes.push(newProps ? new NodeType(type.name, newProps, type.id, type.flags) : type);\n        }\n        return new NodeSet(newTypes);\n    }\n}\nconst CachedNode = new WeakMap(), CachedInnerNode = new WeakMap();\n/**\nOptions that control iteration. Can be combined with the `|`\noperator to enable multiple ones.\n*/\nvar IterMode;\n(function (IterMode) {\n    /**\n    When enabled, iteration will only visit [`Tree`](#common.Tree)\n    objects, not nodes packed into\n    [`TreeBuffer`](#common.TreeBuffer)s.\n    */\n    IterMode[IterMode[\"ExcludeBuffers\"] = 1] = \"ExcludeBuffers\";\n    /**\n    Enable this to make iteration include anonymous nodes (such as\n    the nodes that wrap repeated grammar constructs into a balanced\n    tree).\n    */\n    IterMode[IterMode[\"IncludeAnonymous\"] = 2] = \"IncludeAnonymous\";\n    /**\n    By default, regular [mounted](#common.NodeProp^mounted) nodes\n    replace their base node in iteration. Enable this to ignore them\n    instead.\n    */\n    IterMode[IterMode[\"IgnoreMounts\"] = 4] = \"IgnoreMounts\";\n    /**\n    This option only applies in\n    [`enter`](#common.SyntaxNode.enter)-style methods. It tells the\n    library to not enter mounted overlays if one covers the given\n    position.\n    */\n    IterMode[IterMode[\"IgnoreOverlays\"] = 8] = \"IgnoreOverlays\";\n})(IterMode || (IterMode = {}));\n/**\nA piece of syntax tree. There are two ways to approach these\ntrees: the way they are actually stored in memory, and the\nconvenient way.\n\nSyntax trees are stored as a tree of `Tree` and `TreeBuffer`\nobjects. By packing detail information into `TreeBuffer` leaf\nnodes, the representation is made a lot more memory-efficient.\n\nHowever, when you want to actually work with tree nodes, this\nrepresentation is very awkward, so most client code will want to\nuse the [`TreeCursor`](#common.TreeCursor) or\n[`SyntaxNode`](#common.SyntaxNode) interface instead, which provides\na view on some part of this data structure, and can be used to\nmove around to adjacent nodes.\n*/\nclass Tree {\n    /**\n    Construct a new tree. See also [`Tree.build`](#common.Tree^build).\n    */\n    constructor(\n    /**\n    The type of the top node.\n    */\n    type, \n    /**\n    This node's child nodes.\n    */\n    children, \n    /**\n    The positions (offsets relative to the start of this tree) of\n    the children.\n    */\n    positions, \n    /**\n    The total length of this tree\n    */\n    length, \n    /**\n    Per-node [node props](#common.NodeProp) to associate with this node.\n    */\n    props) {\n        this.type = type;\n        this.children = children;\n        this.positions = positions;\n        this.length = length;\n        /**\n        @internal\n        */\n        this.props = null;\n        if (props && props.length) {\n            this.props = Object.create(null);\n            for (let [prop, value] of props)\n                this.props[typeof prop == \"number\" ? prop : prop.id] = value;\n        }\n    }\n    /**\n    @internal\n    */\n    toString() {\n        let mounted = MountedTree.get(this);\n        if (mounted && !mounted.overlay)\n            return mounted.tree.toString();\n        let children = \"\";\n        for (let ch of this.children) {\n            let str = ch.toString();\n            if (str) {\n                if (children)\n                    children += \",\";\n                children += str;\n            }\n        }\n        return !this.type.name ? children :\n            (/\\W/.test(this.type.name) && !this.type.isError ? JSON.stringify(this.type.name) : this.type.name) +\n                (children.length ? \"(\" + children + \")\" : \"\");\n    }\n    /**\n    Get a [tree cursor](#common.TreeCursor) positioned at the top of\n    the tree. Mode can be used to [control](#common.IterMode) which\n    nodes the cursor visits.\n    */\n    cursor(mode = 0) {\n        return new TreeCursor(this.topNode, mode);\n    }\n    /**\n    Get a [tree cursor](#common.TreeCursor) pointing into this tree\n    at the given position and side (see\n    [`moveTo`](#common.TreeCursor.moveTo).\n    */\n    cursorAt(pos, side = 0, mode = 0) {\n        let scope = CachedNode.get(this) || this.topNode;\n        let cursor = new TreeCursor(scope);\n        cursor.moveTo(pos, side);\n        CachedNode.set(this, cursor._tree);\n        return cursor;\n    }\n    /**\n    Get a [syntax node](#common.SyntaxNode) object for the top of the\n    tree.\n    */\n    get topNode() {\n        return new TreeNode(this, 0, 0, null);\n    }\n    /**\n    Get the [syntax node](#common.SyntaxNode) at the given position.\n    If `side` is -1, this will move into nodes that end at the\n    position. If 1, it'll move into nodes that start at the\n    position. With 0, it'll only enter nodes that cover the position\n    from both sides.\n    \n    Note that this will not enter\n    [overlays](#common.MountedTree.overlay), and you often want\n    [`resolveInner`](#common.Tree.resolveInner) instead.\n    */\n    resolve(pos, side = 0) {\n        let node = resolveNode(CachedNode.get(this) || this.topNode, pos, side, false);\n        CachedNode.set(this, node);\n        return node;\n    }\n    /**\n    Like [`resolve`](#common.Tree.resolve), but will enter\n    [overlaid](#common.MountedTree.overlay) nodes, producing a syntax node\n    pointing into the innermost overlaid tree at the given position\n    (with parent links going through all parent structure, including\n    the host trees).\n    */\n    resolveInner(pos, side = 0) {\n        let node = resolveNode(CachedInnerNode.get(this) || this.topNode, pos, side, true);\n        CachedInnerNode.set(this, node);\n        return node;\n    }\n    /**\n    In some situations, it can be useful to iterate through all\n    nodes around a position, including those in overlays that don't\n    directly cover the position. This method gives you an iterator\n    that will produce all nodes, from small to big, around the given\n    position.\n    */\n    resolveStack(pos, side = 0) {\n        return stackIterator(this, pos, side);\n    }\n    /**\n    Iterate over the tree and its children, calling `enter` for any\n    node that touches the `from`/`to` region (if given) before\n    running over such a node's children, and `leave` (if given) when\n    leaving the node. When `enter` returns `false`, that node will\n    not have its children iterated over (or `leave` called).\n    */\n    iterate(spec) {\n        let { enter, leave, from = 0, to = this.length } = spec;\n        let mode = spec.mode || 0, anon = (mode & IterMode.IncludeAnonymous) > 0;\n        for (let c = this.cursor(mode | IterMode.IncludeAnonymous);;) {\n            let entered = false;\n            if (c.from <= to && c.to >= from && (!anon && c.type.isAnonymous || enter(c) !== false)) {\n                if (c.firstChild())\n                    continue;\n                entered = true;\n            }\n            for (;;) {\n                if (entered && leave && (anon || !c.type.isAnonymous))\n                    leave(c);\n                if (c.nextSibling())\n                    break;\n                if (!c.parent())\n                    return;\n                entered = true;\n            }\n        }\n    }\n    /**\n    Get the value of the given [node prop](#common.NodeProp) for this\n    node. Works with both per-node and per-type props.\n    */\n    prop(prop) {\n        return !prop.perNode ? this.type.prop(prop) : this.props ? this.props[prop.id] : undefined;\n    }\n    /**\n    Returns the node's [per-node props](#common.NodeProp.perNode) in a\n    format that can be passed to the [`Tree`](#common.Tree)\n    constructor.\n    */\n    get propValues() {\n        let result = [];\n        if (this.props)\n            for (let id in this.props)\n                result.push([+id, this.props[id]]);\n        return result;\n    }\n    /**\n    Balance the direct children of this tree, producing a copy of\n    which may have children grouped into subtrees with type\n    [`NodeType.none`](#common.NodeType^none).\n    */\n    balance(config = {}) {\n        return this.children.length <= 8 /* Balance.BranchFactor */ ? this :\n            balanceRange(NodeType.none, this.children, this.positions, 0, this.children.length, 0, this.length, (children, positions, length) => new Tree(this.type, children, positions, length, this.propValues), config.makeTree || ((children, positions, length) => new Tree(NodeType.none, children, positions, length)));\n    }\n    /**\n    Build a tree from a postfix-ordered buffer of node information,\n    or a cursor over such a buffer.\n    */\n    static build(data) { return buildTree(data); }\n}\n/**\nThe empty tree\n*/\nTree.empty = new Tree(NodeType.none, [], [], 0);\nclass FlatBufferCursor {\n    constructor(buffer, index) {\n        this.buffer = buffer;\n        this.index = index;\n    }\n    get id() { return this.buffer[this.index - 4]; }\n    get start() { return this.buffer[this.index - 3]; }\n    get end() { return this.buffer[this.index - 2]; }\n    get size() { return this.buffer[this.index - 1]; }\n    get pos() { return this.index; }\n    next() { this.index -= 4; }\n    fork() { return new FlatBufferCursor(this.buffer, this.index); }\n}\n/**\nTree buffers contain (type, start, end, endIndex) quads for each\nnode. In such a buffer, nodes are stored in prefix order (parents\nbefore children, with the endIndex of the parent indicating which\nchildren belong to it).\n*/\nclass TreeBuffer {\n    /**\n    Create a tree buffer.\n    */\n    constructor(\n    /**\n    The buffer's content.\n    */\n    buffer, \n    /**\n    The total length of the group of nodes in the buffer.\n    */\n    length, \n    /**\n    The node set used in this buffer.\n    */\n    set) {\n        this.buffer = buffer;\n        this.length = length;\n        this.set = set;\n    }\n    /**\n    @internal\n    */\n    get type() { return NodeType.none; }\n    /**\n    @internal\n    */\n    toString() {\n        let result = [];\n        for (let index = 0; index < this.buffer.length;) {\n            result.push(this.childString(index));\n            index = this.buffer[index + 3];\n        }\n        return result.join(\",\");\n    }\n    /**\n    @internal\n    */\n    childString(index) {\n        let id = this.buffer[index], endIndex = this.buffer[index + 3];\n        let type = this.set.types[id], result = type.name;\n        if (/\\W/.test(result) && !type.isError)\n            result = JSON.stringify(result);\n        index += 4;\n        if (endIndex == index)\n            return result;\n        let children = [];\n        while (index < endIndex) {\n            children.push(this.childString(index));\n            index = this.buffer[index + 3];\n        }\n        return result + \"(\" + children.join(\",\") + \")\";\n    }\n    /**\n    @internal\n    */\n    findChild(startIndex, endIndex, dir, pos, side) {\n        let { buffer } = this, pick = -1;\n        for (let i = startIndex; i != endIndex; i = buffer[i + 3]) {\n            if (checkSide(side, pos, buffer[i + 1], buffer[i + 2])) {\n                pick = i;\n                if (dir > 0)\n                    break;\n            }\n        }\n        return pick;\n    }\n    /**\n    @internal\n    */\n    slice(startI, endI, from) {\n        let b = this.buffer;\n        let copy = new Uint16Array(endI - startI), len = 0;\n        for (let i = startI, j = 0; i < endI;) {\n            copy[j++] = b[i++];\n            copy[j++] = b[i++] - from;\n            let to = copy[j++] = b[i++] - from;\n            copy[j++] = b[i++] - startI;\n            len = Math.max(len, to);\n        }\n        return new TreeBuffer(copy, len, this.set);\n    }\n}\nfunction checkSide(side, pos, from, to) {\n    switch (side) {\n        case -2 /* Side.Before */: return from < pos;\n        case -1 /* Side.AtOrBefore */: return to >= pos && from < pos;\n        case 0 /* Side.Around */: return from < pos && to > pos;\n        case 1 /* Side.AtOrAfter */: return from <= pos && to > pos;\n        case 2 /* Side.After */: return to > pos;\n        case 4 /* Side.DontCare */: return true;\n    }\n}\nfunction resolveNode(node, pos, side, overlays) {\n    var _a;\n    // Move up to a node that actually holds the position, if possible\n    while (node.from == node.to ||\n        (side < 1 ? node.from >= pos : node.from > pos) ||\n        (side > -1 ? node.to <= pos : node.to < pos)) {\n        let parent = !overlays && node instanceof TreeNode && node.index < 0 ? null : node.parent;\n        if (!parent)\n            return node;\n        node = parent;\n    }\n    let mode = overlays ? 0 : IterMode.IgnoreOverlays;\n    // Must go up out of overlays when those do not overlap with pos\n    if (overlays)\n        for (let scan = node, parent = scan.parent; parent; scan = parent, parent = scan.parent) {\n            if (scan instanceof TreeNode && scan.index < 0 && ((_a = parent.enter(pos, side, mode)) === null || _a === void 0 ? void 0 : _a.from) != scan.from)\n                node = parent;\n        }\n    for (;;) {\n        let inner = node.enter(pos, side, mode);\n        if (!inner)\n            return node;\n        node = inner;\n    }\n}\nclass BaseNode {\n    cursor(mode = 0) { return new TreeCursor(this, mode); }\n    getChild(type, before = null, after = null) {\n        let r = getChildren(this, type, before, after);\n        return r.length ? r[0] : null;\n    }\n    getChildren(type, before = null, after = null) {\n        return getChildren(this, type, before, after);\n    }\n    resolve(pos, side = 0) {\n        return resolveNode(this, pos, side, false);\n    }\n    resolveInner(pos, side = 0) {\n        return resolveNode(this, pos, side, true);\n    }\n    matchContext(context) {\n        return matchNodeContext(this, context);\n    }\n    enterUnfinishedNodesBefore(pos) {\n        let scan = this.childBefore(pos), node = this;\n        while (scan) {\n            let last = scan.lastChild;\n            if (!last || last.to != scan.to)\n                break;\n            if (last.type.isError && last.from == last.to) {\n                node = scan;\n                scan = last.prevSibling;\n            }\n            else {\n                scan = last;\n            }\n        }\n        return node;\n    }\n    get node() { return this; }\n    get next() { return this.parent; }\n}\nclass TreeNode extends BaseNode {\n    constructor(_tree, from, \n    // Index in parent node, set to -1 if the node is not a direct child of _parent.node (overlay)\n    index, _parent) {\n        super();\n        this._tree = _tree;\n        this.from = from;\n        this.index = index;\n        this._parent = _parent;\n    }\n    get type() { return this._tree.type; }\n    get name() { return this._tree.type.name; }\n    get to() { return this.from + this._tree.length; }\n    nextChild(i, dir, pos, side, mode = 0) {\n        for (let parent = this;;) {\n            for (let { children, positions } = parent._tree, e = dir > 0 ? children.length : -1; i != e; i += dir) {\n                let next = children[i], start = positions[i] + parent.from;\n                if (!checkSide(side, pos, start, start + next.length))\n                    continue;\n                if (next instanceof TreeBuffer) {\n                    if (mode & IterMode.ExcludeBuffers)\n                        continue;\n                    let index = next.findChild(0, next.buffer.length, dir, pos - start, side);\n                    if (index > -1)\n                        return new BufferNode(new BufferContext(parent, next, i, start), null, index);\n                }\n                else if ((mode & IterMode.IncludeAnonymous) || (!next.type.isAnonymous || hasChild(next))) {\n                    let mounted;\n                    if (!(mode & IterMode.IgnoreMounts) && (mounted = MountedTree.get(next)) && !mounted.overlay)\n                        return new TreeNode(mounted.tree, start, i, parent);\n                    let inner = new TreeNode(next, start, i, parent);\n                    return (mode & IterMode.IncludeAnonymous) || !inner.type.isAnonymous ? inner\n                        : inner.nextChild(dir < 0 ? next.children.length - 1 : 0, dir, pos, side);\n                }\n            }\n            if ((mode & IterMode.IncludeAnonymous) || !parent.type.isAnonymous)\n                return null;\n            if (parent.index >= 0)\n                i = parent.index + dir;\n            else\n                i = dir < 0 ? -1 : parent._parent._tree.children.length;\n            parent = parent._parent;\n            if (!parent)\n                return null;\n        }\n    }\n    get firstChild() { return this.nextChild(0, 1, 0, 4 /* Side.DontCare */); }\n    get lastChild() { return this.nextChild(this._tree.children.length - 1, -1, 0, 4 /* Side.DontCare */); }\n    childAfter(pos) { return this.nextChild(0, 1, pos, 2 /* Side.After */); }\n    childBefore(pos) { return this.nextChild(this._tree.children.length - 1, -1, pos, -2 /* Side.Before */); }\n    enter(pos, side, mode = 0) {\n        let mounted;\n        if (!(mode & IterMode.IgnoreOverlays) && (mounted = MountedTree.get(this._tree)) && mounted.overlay) {\n            let rPos = pos - this.from;\n            for (let { from, to } of mounted.overlay) {\n                if ((side > 0 ? from <= rPos : from < rPos) &&\n                    (side < 0 ? to >= rPos : to > rPos))\n                    return new TreeNode(mounted.tree, mounted.overlay[0].from + this.from, -1, this);\n            }\n        }\n        return this.nextChild(0, 1, pos, side, mode);\n    }\n    nextSignificantParent() {\n        let val = this;\n        while (val.type.isAnonymous && val._parent)\n            val = val._parent;\n        return val;\n    }\n    get parent() {\n        return this._parent ? this._parent.nextSignificantParent() : null;\n    }\n    get nextSibling() {\n        return this._parent && this.index >= 0 ? this._parent.nextChild(this.index + 1, 1, 0, 4 /* Side.DontCare */) : null;\n    }\n    get prevSibling() {\n        return this._parent && this.index >= 0 ? this._parent.nextChild(this.index - 1, -1, 0, 4 /* Side.DontCare */) : null;\n    }\n    get tree() { return this._tree; }\n    toTree() { return this._tree; }\n    /**\n    @internal\n    */\n    toString() { return this._tree.toString(); }\n}\nfunction getChildren(node, type, before, after) {\n    let cur = node.cursor(), result = [];\n    if (!cur.firstChild())\n        return result;\n    if (before != null)\n        for (let found = false; !found;) {\n            found = cur.type.is(before);\n            if (!cur.nextSibling())\n                return result;\n        }\n    for (;;) {\n        if (after != null && cur.type.is(after))\n            return result;\n        if (cur.type.is(type))\n            result.push(cur.node);\n        if (!cur.nextSibling())\n            return after == null ? result : [];\n    }\n}\nfunction matchNodeContext(node, context, i = context.length - 1) {\n    for (let p = node.parent; i >= 0; p = p.parent) {\n        if (!p)\n            return false;\n        if (!p.type.isAnonymous) {\n            if (context[i] && context[i] != p.name)\n                return false;\n            i--;\n        }\n    }\n    return true;\n}\nclass BufferContext {\n    constructor(parent, buffer, index, start) {\n        this.parent = parent;\n        this.buffer = buffer;\n        this.index = index;\n        this.start = start;\n    }\n}\nclass BufferNode extends BaseNode {\n    get name() { return this.type.name; }\n    get from() { return this.context.start + this.context.buffer.buffer[this.index + 1]; }\n    get to() { return this.context.start + this.context.buffer.buffer[this.index + 2]; }\n    constructor(context, _parent, index) {\n        super();\n        this.context = context;\n        this._parent = _parent;\n        this.index = index;\n        this.type = context.buffer.set.types[context.buffer.buffer[index]];\n    }\n    child(dir, pos, side) {\n        let { buffer } = this.context;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], dir, pos - this.context.start, side);\n        return index < 0 ? null : new BufferNode(this.context, this, index);\n    }\n    get firstChild() { return this.child(1, 0, 4 /* Side.DontCare */); }\n    get lastChild() { return this.child(-1, 0, 4 /* Side.DontCare */); }\n    childAfter(pos) { return this.child(1, pos, 2 /* Side.After */); }\n    childBefore(pos) { return this.child(-1, pos, -2 /* Side.Before */); }\n    enter(pos, side, mode = 0) {\n        if (mode & IterMode.ExcludeBuffers)\n            return null;\n        let { buffer } = this.context;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], side > 0 ? 1 : -1, pos - this.context.start, side);\n        return index < 0 ? null : new BufferNode(this.context, this, index);\n    }\n    get parent() {\n        return this._parent || this.context.parent.nextSignificantParent();\n    }\n    externalSibling(dir) {\n        return this._parent ? null : this.context.parent.nextChild(this.context.index + dir, dir, 0, 4 /* Side.DontCare */);\n    }\n    get nextSibling() {\n        let { buffer } = this.context;\n        let after = buffer.buffer[this.index + 3];\n        if (after < (this._parent ? buffer.buffer[this._parent.index + 3] : buffer.buffer.length))\n            return new BufferNode(this.context, this._parent, after);\n        return this.externalSibling(1);\n    }\n    get prevSibling() {\n        let { buffer } = this.context;\n        let parentStart = this._parent ? this._parent.index + 4 : 0;\n        if (this.index == parentStart)\n            return this.externalSibling(-1);\n        return new BufferNode(this.context, this._parent, buffer.findChild(parentStart, this.index, -1, 0, 4 /* Side.DontCare */));\n    }\n    get tree() { return null; }\n    toTree() {\n        let children = [], positions = [];\n        let { buffer } = this.context;\n        let startI = this.index + 4, endI = buffer.buffer[this.index + 3];\n        if (endI > startI) {\n            let from = buffer.buffer[this.index + 1];\n            children.push(buffer.slice(startI, endI, from));\n            positions.push(0);\n        }\n        return new Tree(this.type, children, positions, this.to - this.from);\n    }\n    /**\n    @internal\n    */\n    toString() { return this.context.buffer.childString(this.index); }\n}\nfunction iterStack(heads) {\n    if (!heads.length)\n        return null;\n    let pick = 0, picked = heads[0];\n    for (let i = 1; i < heads.length; i++) {\n        let node = heads[i];\n        if (node.from > picked.from || node.to < picked.to) {\n            picked = node;\n            pick = i;\n        }\n    }\n    let next = picked instanceof TreeNode && picked.index < 0 ? null : picked.parent;\n    let newHeads = heads.slice();\n    if (next)\n        newHeads[pick] = next;\n    else\n        newHeads.splice(pick, 1);\n    return new StackIterator(newHeads, picked);\n}\nclass StackIterator {\n    constructor(heads, node) {\n        this.heads = heads;\n        this.node = node;\n    }\n    get next() { return iterStack(this.heads); }\n}\nfunction stackIterator(tree, pos, side) {\n    let inner = tree.resolveInner(pos, side), layers = null;\n    for (let scan = inner instanceof TreeNode ? inner : inner.context.parent; scan; scan = scan.parent) {\n        if (scan.index < 0) { // This is an overlay root\n            let parent = scan.parent;\n            (layers || (layers = [inner])).push(parent.resolve(pos, side));\n            scan = parent;\n        }\n        else {\n            let mount = MountedTree.get(scan.tree);\n            // Relevant overlay branching off\n            if (mount && mount.overlay && mount.overlay[0].from <= pos && mount.overlay[mount.overlay.length - 1].to >= pos) {\n                let root = new TreeNode(mount.tree, mount.overlay[0].from + scan.from, -1, scan);\n                (layers || (layers = [inner])).push(resolveNode(root, pos, side, false));\n            }\n        }\n    }\n    return layers ? iterStack(layers) : inner;\n}\n/**\nA tree cursor object focuses on a given node in a syntax tree, and\nallows you to move to adjacent nodes.\n*/\nclass TreeCursor {\n    /**\n    Shorthand for `.type.name`.\n    */\n    get name() { return this.type.name; }\n    /**\n    @internal\n    */\n    constructor(node, \n    /**\n    @internal\n    */\n    mode = 0) {\n        this.mode = mode;\n        /**\n        @internal\n        */\n        this.buffer = null;\n        this.stack = [];\n        /**\n        @internal\n        */\n        this.index = 0;\n        this.bufferNode = null;\n        if (node instanceof TreeNode) {\n            this.yieldNode(node);\n        }\n        else {\n            this._tree = node.context.parent;\n            this.buffer = node.context;\n            for (let n = node._parent; n; n = n._parent)\n                this.stack.unshift(n.index);\n            this.bufferNode = node;\n            this.yieldBuf(node.index);\n        }\n    }\n    yieldNode(node) {\n        if (!node)\n            return false;\n        this._tree = node;\n        this.type = node.type;\n        this.from = node.from;\n        this.to = node.to;\n        return true;\n    }\n    yieldBuf(index, type) {\n        this.index = index;\n        let { start, buffer } = this.buffer;\n        this.type = type || buffer.set.types[buffer.buffer[index]];\n        this.from = start + buffer.buffer[index + 1];\n        this.to = start + buffer.buffer[index + 2];\n        return true;\n    }\n    /**\n    @internal\n    */\n    yield(node) {\n        if (!node)\n            return false;\n        if (node instanceof TreeNode) {\n            this.buffer = null;\n            return this.yieldNode(node);\n        }\n        this.buffer = node.context;\n        return this.yieldBuf(node.index, node.type);\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return this.buffer ? this.buffer.buffer.childString(this.index) : this._tree.toString();\n    }\n    /**\n    @internal\n    */\n    enterChild(dir, pos, side) {\n        if (!this.buffer)\n            return this.yield(this._tree.nextChild(dir < 0 ? this._tree._tree.children.length - 1 : 0, dir, pos, side, this.mode));\n        let { buffer } = this.buffer;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], dir, pos - this.buffer.start, side);\n        if (index < 0)\n            return false;\n        this.stack.push(this.index);\n        return this.yieldBuf(index);\n    }\n    /**\n    Move the cursor to this node's first child. When this returns\n    false, the node has no child, and the cursor has not been moved.\n    */\n    firstChild() { return this.enterChild(1, 0, 4 /* Side.DontCare */); }\n    /**\n    Move the cursor to this node's last child.\n    */\n    lastChild() { return this.enterChild(-1, 0, 4 /* Side.DontCare */); }\n    /**\n    Move the cursor to the first child that ends after `pos`.\n    */\n    childAfter(pos) { return this.enterChild(1, pos, 2 /* Side.After */); }\n    /**\n    Move to the last child that starts before `pos`.\n    */\n    childBefore(pos) { return this.enterChild(-1, pos, -2 /* Side.Before */); }\n    /**\n    Move the cursor to the child around `pos`. If side is -1 the\n    child may end at that position, when 1 it may start there. This\n    will also enter [overlaid](#common.MountedTree.overlay)\n    [mounted](#common.NodeProp^mounted) trees unless `overlays` is\n    set to false.\n    */\n    enter(pos, side, mode = this.mode) {\n        if (!this.buffer)\n            return this.yield(this._tree.enter(pos, side, mode));\n        return mode & IterMode.ExcludeBuffers ? false : this.enterChild(1, pos, side);\n    }\n    /**\n    Move to the node's parent node, if this isn't the top node.\n    */\n    parent() {\n        if (!this.buffer)\n            return this.yieldNode((this.mode & IterMode.IncludeAnonymous) ? this._tree._parent : this._tree.parent);\n        if (this.stack.length)\n            return this.yieldBuf(this.stack.pop());\n        let parent = (this.mode & IterMode.IncludeAnonymous) ? this.buffer.parent : this.buffer.parent.nextSignificantParent();\n        this.buffer = null;\n        return this.yieldNode(parent);\n    }\n    /**\n    @internal\n    */\n    sibling(dir) {\n        if (!this.buffer)\n            return !this._tree._parent ? false\n                : this.yield(this._tree.index < 0 ? null\n                    : this._tree._parent.nextChild(this._tree.index + dir, dir, 0, 4 /* Side.DontCare */, this.mode));\n        let { buffer } = this.buffer, d = this.stack.length - 1;\n        if (dir < 0) {\n            let parentStart = d < 0 ? 0 : this.stack[d] + 4;\n            if (this.index != parentStart)\n                return this.yieldBuf(buffer.findChild(parentStart, this.index, -1, 0, 4 /* Side.DontCare */));\n        }\n        else {\n            let after = buffer.buffer[this.index + 3];\n            if (after < (d < 0 ? buffer.buffer.length : buffer.buffer[this.stack[d] + 3]))\n                return this.yieldBuf(after);\n        }\n        return d < 0 ? this.yield(this.buffer.parent.nextChild(this.buffer.index + dir, dir, 0, 4 /* Side.DontCare */, this.mode)) : false;\n    }\n    /**\n    Move to this node's next sibling, if any.\n    */\n    nextSibling() { return this.sibling(1); }\n    /**\n    Move to this node's previous sibling, if any.\n    */\n    prevSibling() { return this.sibling(-1); }\n    atLastNode(dir) {\n        let index, parent, { buffer } = this;\n        if (buffer) {\n            if (dir > 0) {\n                if (this.index < buffer.buffer.buffer.length)\n                    return false;\n            }\n            else {\n                for (let i = 0; i < this.index; i++)\n                    if (buffer.buffer.buffer[i + 3] < this.index)\n                        return false;\n            }\n            ({ index, parent } = buffer);\n        }\n        else {\n            ({ index, _parent: parent } = this._tree);\n        }\n        for (; parent; { index, _parent: parent } = parent) {\n            if (index > -1)\n                for (let i = index + dir, e = dir < 0 ? -1 : parent._tree.children.length; i != e; i += dir) {\n                    let child = parent._tree.children[i];\n                    if ((this.mode & IterMode.IncludeAnonymous) ||\n                        child instanceof TreeBuffer ||\n                        !child.type.isAnonymous ||\n                        hasChild(child))\n                        return false;\n                }\n        }\n        return true;\n    }\n    move(dir, enter) {\n        if (enter && this.enterChild(dir, 0, 4 /* Side.DontCare */))\n            return true;\n        for (;;) {\n            if (this.sibling(dir))\n                return true;\n            if (this.atLastNode(dir) || !this.parent())\n                return false;\n        }\n    }\n    /**\n    Move to the next node in a\n    [pre-order](https://en.wikipedia.org/wiki/Tree_traversal#Pre-order,_NLR)\n    traversal, going from a node to its first child or, if the\n    current node is empty or `enter` is false, its next sibling or\n    the next sibling of the first parent node that has one.\n    */\n    next(enter = true) { return this.move(1, enter); }\n    /**\n    Move to the next node in a last-to-first pre-order traveral. A\n    node is followed by its last child or, if it has none, its\n    previous sibling or the previous sibling of the first parent\n    node that has one.\n    */\n    prev(enter = true) { return this.move(-1, enter); }\n    /**\n    Move the cursor to the innermost node that covers `pos`. If\n    `side` is -1, it will enter nodes that end at `pos`. If it is 1,\n    it will enter nodes that start at `pos`.\n    */\n    moveTo(pos, side = 0) {\n        // Move up to a node that actually holds the position, if possible\n        while (this.from == this.to ||\n            (side < 1 ? this.from >= pos : this.from > pos) ||\n            (side > -1 ? this.to <= pos : this.to < pos))\n            if (!this.parent())\n                break;\n        // Then scan down into child nodes as far as possible\n        while (this.enterChild(1, pos, side)) { }\n        return this;\n    }\n    /**\n    Get a [syntax node](#common.SyntaxNode) at the cursor's current\n    position.\n    */\n    get node() {\n        if (!this.buffer)\n            return this._tree;\n        let cache = this.bufferNode, result = null, depth = 0;\n        if (cache && cache.context == this.buffer) {\n            scan: for (let index = this.index, d = this.stack.length; d >= 0;) {\n                for (let c = cache; c; c = c._parent)\n                    if (c.index == index) {\n                        if (index == this.index)\n                            return c;\n                        result = c;\n                        depth = d + 1;\n                        break scan;\n                    }\n                index = this.stack[--d];\n            }\n        }\n        for (let i = depth; i < this.stack.length; i++)\n            result = new BufferNode(this.buffer, result, this.stack[i]);\n        return this.bufferNode = new BufferNode(this.buffer, result, this.index);\n    }\n    /**\n    Get the [tree](#common.Tree) that represents the current node, if\n    any. Will return null when the node is in a [tree\n    buffer](#common.TreeBuffer).\n    */\n    get tree() {\n        return this.buffer ? null : this._tree._tree;\n    }\n    /**\n    Iterate over the current node and all its descendants, calling\n    `enter` when entering a node and `leave`, if given, when leaving\n    one. When `enter` returns `false`, any children of that node are\n    skipped, and `leave` isn't called for it.\n    */\n    iterate(enter, leave) {\n        for (let depth = 0;;) {\n            let mustLeave = false;\n            if (this.type.isAnonymous || enter(this) !== false) {\n                if (this.firstChild()) {\n                    depth++;\n                    continue;\n                }\n                if (!this.type.isAnonymous)\n                    mustLeave = true;\n            }\n            for (;;) {\n                if (mustLeave && leave)\n                    leave(this);\n                mustLeave = this.type.isAnonymous;\n                if (this.nextSibling())\n                    break;\n                if (!depth)\n                    return;\n                this.parent();\n                depth--;\n                mustLeave = true;\n            }\n        }\n    }\n    /**\n    Test whether the current node matches a given context—a sequence\n    of direct parent node names. Empty strings in the context array\n    are treated as wildcards.\n    */\n    matchContext(context) {\n        if (!this.buffer)\n            return matchNodeContext(this.node, context);\n        let { buffer } = this.buffer, { types } = buffer.set;\n        for (let i = context.length - 1, d = this.stack.length - 1; i >= 0; d--) {\n            if (d < 0)\n                return matchNodeContext(this.node, context, i);\n            let type = types[buffer.buffer[this.stack[d]]];\n            if (!type.isAnonymous) {\n                if (context[i] && context[i] != type.name)\n                    return false;\n                i--;\n            }\n        }\n        return true;\n    }\n}\nfunction hasChild(tree) {\n    return tree.children.some(ch => ch instanceof TreeBuffer || !ch.type.isAnonymous || hasChild(ch));\n}\nfunction buildTree(data) {\n    var _a;\n    let { buffer, nodeSet, maxBufferLength = DefaultBufferLength, reused = [], minRepeatType = nodeSet.types.length } = data;\n    let cursor = Array.isArray(buffer) ? new FlatBufferCursor(buffer, buffer.length) : buffer;\n    let types = nodeSet.types;\n    let contextHash = 0, lookAhead = 0;\n    function takeNode(parentStart, minPos, children, positions, inRepeat, depth) {\n        let { id, start, end, size } = cursor;\n        let lookAheadAtStart = lookAhead;\n        while (size < 0) {\n            cursor.next();\n            if (size == -1 /* SpecialRecord.Reuse */) {\n                let node = reused[id];\n                children.push(node);\n                positions.push(start - parentStart);\n                return;\n            }\n            else if (size == -3 /* SpecialRecord.ContextChange */) { // Context change\n                contextHash = id;\n                return;\n            }\n            else if (size == -4 /* SpecialRecord.LookAhead */) {\n                lookAhead = id;\n                return;\n            }\n            else {\n                throw new RangeError(`Unrecognized record size: ${size}`);\n            }\n        }\n        let type = types[id], node, buffer;\n        let startPos = start - parentStart;\n        if (end - start <= maxBufferLength && (buffer = findBufferSize(cursor.pos - minPos, inRepeat))) {\n            // Small enough for a buffer, and no reused nodes inside\n            let data = new Uint16Array(buffer.size - buffer.skip);\n            let endPos = cursor.pos - buffer.size, index = data.length;\n            while (cursor.pos > endPos)\n                index = copyToBuffer(buffer.start, data, index);\n            node = new TreeBuffer(data, end - buffer.start, nodeSet);\n            startPos = buffer.start - parentStart;\n        }\n        else { // Make it a node\n            let endPos = cursor.pos - size;\n            cursor.next();\n            let localChildren = [], localPositions = [];\n            let localInRepeat = id >= minRepeatType ? id : -1;\n            let lastGroup = 0, lastEnd = end;\n            while (cursor.pos > endPos) {\n                if (localInRepeat >= 0 && cursor.id == localInRepeat && cursor.size >= 0) {\n                    if (cursor.end <= lastEnd - maxBufferLength) {\n                        makeRepeatLeaf(localChildren, localPositions, start, lastGroup, cursor.end, lastEnd, localInRepeat, lookAheadAtStart);\n                        lastGroup = localChildren.length;\n                        lastEnd = cursor.end;\n                    }\n                    cursor.next();\n                }\n                else if (depth > 2500 /* CutOff.Depth */) {\n                    takeFlatNode(start, endPos, localChildren, localPositions);\n                }\n                else {\n                    takeNode(start, endPos, localChildren, localPositions, localInRepeat, depth + 1);\n                }\n            }\n            if (localInRepeat >= 0 && lastGroup > 0 && lastGroup < localChildren.length)\n                makeRepeatLeaf(localChildren, localPositions, start, lastGroup, start, lastEnd, localInRepeat, lookAheadAtStart);\n            localChildren.reverse();\n            localPositions.reverse();\n            if (localInRepeat > -1 && lastGroup > 0) {\n                let make = makeBalanced(type);\n                node = balanceRange(type, localChildren, localPositions, 0, localChildren.length, 0, end - start, make, make);\n            }\n            else {\n                node = makeTree(type, localChildren, localPositions, end - start, lookAheadAtStart - end);\n            }\n        }\n        children.push(node);\n        positions.push(startPos);\n    }\n    function takeFlatNode(parentStart, minPos, children, positions) {\n        let nodes = []; // Temporary, inverted array of leaf nodes found, with absolute positions\n        let nodeCount = 0, stopAt = -1;\n        while (cursor.pos > minPos) {\n            let { id, start, end, size } = cursor;\n            if (size > 4) { // Not a leaf\n                cursor.next();\n            }\n            else if (stopAt > -1 && start < stopAt) {\n                break;\n            }\n            else {\n                if (stopAt < 0)\n                    stopAt = end - maxBufferLength;\n                nodes.push(id, start, end);\n                nodeCount++;\n                cursor.next();\n            }\n        }\n        if (nodeCount) {\n            let buffer = new Uint16Array(nodeCount * 4);\n            let start = nodes[nodes.length - 2];\n            for (let i = nodes.length - 3, j = 0; i >= 0; i -= 3) {\n                buffer[j++] = nodes[i];\n                buffer[j++] = nodes[i + 1] - start;\n                buffer[j++] = nodes[i + 2] - start;\n                buffer[j++] = j;\n            }\n            children.push(new TreeBuffer(buffer, nodes[2] - start, nodeSet));\n            positions.push(start - parentStart);\n        }\n    }\n    function makeBalanced(type) {\n        return (children, positions, length) => {\n            let lookAhead = 0, lastI = children.length - 1, last, lookAheadProp;\n            if (lastI >= 0 && (last = children[lastI]) instanceof Tree) {\n                if (!lastI && last.type == type && last.length == length)\n                    return last;\n                if (lookAheadProp = last.prop(NodeProp.lookAhead))\n                    lookAhead = positions[lastI] + last.length + lookAheadProp;\n            }\n            return makeTree(type, children, positions, length, lookAhead);\n        };\n    }\n    function makeRepeatLeaf(children, positions, base, i, from, to, type, lookAhead) {\n        let localChildren = [], localPositions = [];\n        while (children.length > i) {\n            localChildren.push(children.pop());\n            localPositions.push(positions.pop() + base - from);\n        }\n        children.push(makeTree(nodeSet.types[type], localChildren, localPositions, to - from, lookAhead - to));\n        positions.push(from - base);\n    }\n    function makeTree(type, children, positions, length, lookAhead = 0, props) {\n        if (contextHash) {\n            let pair = [NodeProp.contextHash, contextHash];\n            props = props ? [pair].concat(props) : [pair];\n        }\n        if (lookAhead > 25) {\n            let pair = [NodeProp.lookAhead, lookAhead];\n            props = props ? [pair].concat(props) : [pair];\n        }\n        return new Tree(type, children, positions, length, props);\n    }\n    function findBufferSize(maxSize, inRepeat) {\n        // Scan through the buffer to find previous siblings that fit\n        // together in a TreeBuffer, and don't contain any reused nodes\n        // (which can't be stored in a buffer).\n        // If `inRepeat` is > -1, ignore node boundaries of that type for\n        // nesting, but make sure the end falls either at the start\n        // (`maxSize`) or before such a node.\n        let fork = cursor.fork();\n        let size = 0, start = 0, skip = 0, minStart = fork.end - maxBufferLength;\n        let result = { size: 0, start: 0, skip: 0 };\n        scan: for (let minPos = fork.pos - maxSize; fork.pos > minPos;) {\n            let nodeSize = fork.size;\n            // Pretend nested repeat nodes of the same type don't exist\n            if (fork.id == inRepeat && nodeSize >= 0) {\n                // Except that we store the current state as a valid return\n                // value.\n                result.size = size;\n                result.start = start;\n                result.skip = skip;\n                skip += 4;\n                size += 4;\n                fork.next();\n                continue;\n            }\n            let startPos = fork.pos - nodeSize;\n            if (nodeSize < 0 || startPos < minPos || fork.start < minStart)\n                break;\n            let localSkipped = fork.id >= minRepeatType ? 4 : 0;\n            let nodeStart = fork.start;\n            fork.next();\n            while (fork.pos > startPos) {\n                if (fork.size < 0) {\n                    if (fork.size == -3 /* SpecialRecord.ContextChange */)\n                        localSkipped += 4;\n                    else\n                        break scan;\n                }\n                else if (fork.id >= minRepeatType) {\n                    localSkipped += 4;\n                }\n                fork.next();\n            }\n            start = nodeStart;\n            size += nodeSize;\n            skip += localSkipped;\n        }\n        if (inRepeat < 0 || size == maxSize) {\n            result.size = size;\n            result.start = start;\n            result.skip = skip;\n        }\n        return result.size > 4 ? result : undefined;\n    }\n    function copyToBuffer(bufferStart, buffer, index) {\n        let { id, start, end, size } = cursor;\n        cursor.next();\n        if (size >= 0 && id < minRepeatType) {\n            let startIndex = index;\n            if (size > 4) {\n                let endPos = cursor.pos - (size - 4);\n                while (cursor.pos > endPos)\n                    index = copyToBuffer(bufferStart, buffer, index);\n            }\n            buffer[--index] = startIndex;\n            buffer[--index] = end - bufferStart;\n            buffer[--index] = start - bufferStart;\n            buffer[--index] = id;\n        }\n        else if (size == -3 /* SpecialRecord.ContextChange */) {\n            contextHash = id;\n        }\n        else if (size == -4 /* SpecialRecord.LookAhead */) {\n            lookAhead = id;\n        }\n        return index;\n    }\n    let children = [], positions = [];\n    while (cursor.pos > 0)\n        takeNode(data.start || 0, data.bufferStart || 0, children, positions, -1, 0);\n    let length = (_a = data.length) !== null && _a !== void 0 ? _a : (children.length ? positions[0] + children[0].length : 0);\n    return new Tree(types[data.topID], children.reverse(), positions.reverse(), length);\n}\nconst nodeSizeCache = new WeakMap;\nfunction nodeSize(balanceType, node) {\n    if (!balanceType.isAnonymous || node instanceof TreeBuffer || node.type != balanceType)\n        return 1;\n    let size = nodeSizeCache.get(node);\n    if (size == null) {\n        size = 1;\n        for (let child of node.children) {\n            if (child.type != balanceType || !(child instanceof Tree)) {\n                size = 1;\n                break;\n            }\n            size += nodeSize(balanceType, child);\n        }\n        nodeSizeCache.set(node, size);\n    }\n    return size;\n}\nfunction balanceRange(\n// The type the balanced tree's inner nodes.\nbalanceType, \n// The direct children and their positions\nchildren, positions, \n// The index range in children/positions to use\nfrom, to, \n// The start position of the nodes, relative to their parent.\nstart, \n// Length of the outer node\nlength, \n// Function to build the top node of the balanced tree\nmkTop, \n// Function to build internal nodes for the balanced tree\nmkTree) {\n    let total = 0;\n    for (let i = from; i < to; i++)\n        total += nodeSize(balanceType, children[i]);\n    let maxChild = Math.ceil((total * 1.5) / 8 /* Balance.BranchFactor */);\n    let localChildren = [], localPositions = [];\n    function divide(children, positions, from, to, offset) {\n        for (let i = from; i < to;) {\n            let groupFrom = i, groupStart = positions[i], groupSize = nodeSize(balanceType, children[i]);\n            i++;\n            for (; i < to; i++) {\n                let nextSize = nodeSize(balanceType, children[i]);\n                if (groupSize + nextSize >= maxChild)\n                    break;\n                groupSize += nextSize;\n            }\n            if (i == groupFrom + 1) {\n                if (groupSize > maxChild) {\n                    let only = children[groupFrom]; // Only trees can have a size > 1\n                    divide(only.children, only.positions, 0, only.children.length, positions[groupFrom] + offset);\n                    continue;\n                }\n                localChildren.push(children[groupFrom]);\n            }\n            else {\n                let length = positions[i - 1] + children[i - 1].length - groupStart;\n                localChildren.push(balanceRange(balanceType, children, positions, groupFrom, i, groupStart, length, null, mkTree));\n            }\n            localPositions.push(groupStart + offset - start);\n        }\n    }\n    divide(children, positions, from, to, 0);\n    return (mkTop || mkTree)(localChildren, localPositions, length);\n}\n/**\nProvides a way to associate values with pieces of trees. As long\nas that part of the tree is reused, the associated values can be\nretrieved from an updated tree.\n*/\nclass NodeWeakMap {\n    constructor() {\n        this.map = new WeakMap();\n    }\n    setBuffer(buffer, index, value) {\n        let inner = this.map.get(buffer);\n        if (!inner)\n            this.map.set(buffer, inner = new Map);\n        inner.set(index, value);\n    }\n    getBuffer(buffer, index) {\n        let inner = this.map.get(buffer);\n        return inner && inner.get(index);\n    }\n    /**\n    Set the value for this syntax node.\n    */\n    set(node, value) {\n        if (node instanceof BufferNode)\n            this.setBuffer(node.context.buffer, node.index, value);\n        else if (node instanceof TreeNode)\n            this.map.set(node.tree, value);\n    }\n    /**\n    Retrieve value for this syntax node, if it exists in the map.\n    */\n    get(node) {\n        return node instanceof BufferNode ? this.getBuffer(node.context.buffer, node.index)\n            : node instanceof TreeNode ? this.map.get(node.tree) : undefined;\n    }\n    /**\n    Set the value for the node that a cursor currently points to.\n    */\n    cursorSet(cursor, value) {\n        if (cursor.buffer)\n            this.setBuffer(cursor.buffer.buffer, cursor.index, value);\n        else\n            this.map.set(cursor.tree, value);\n    }\n    /**\n    Retrieve the value for the node that a cursor currently points\n    to.\n    */\n    cursorGet(cursor) {\n        return cursor.buffer ? this.getBuffer(cursor.buffer.buffer, cursor.index) : this.map.get(cursor.tree);\n    }\n}\n\n/**\nTree fragments are used during [incremental\nparsing](#common.Parser.startParse) to track parts of old trees\nthat can be reused in a new parse. An array of fragments is used\nto track regions of an old tree whose nodes might be reused in new\nparses. Use the static\n[`applyChanges`](#common.TreeFragment^applyChanges) method to\nupdate fragments for document changes.\n*/\nclass TreeFragment {\n    /**\n    Construct a tree fragment. You'll usually want to use\n    [`addTree`](#common.TreeFragment^addTree) and\n    [`applyChanges`](#common.TreeFragment^applyChanges) instead of\n    calling this directly.\n    */\n    constructor(\n    /**\n    The start of the unchanged range pointed to by this fragment.\n    This refers to an offset in the _updated_ document (as opposed\n    to the original tree).\n    */\n    from, \n    /**\n    The end of the unchanged range.\n    */\n    to, \n    /**\n    The tree that this fragment is based on.\n    */\n    tree, \n    /**\n    The offset between the fragment's tree and the document that\n    this fragment can be used against. Add this when going from\n    document to tree positions, subtract it to go from tree to\n    document positions.\n    */\n    offset, openStart = false, openEnd = false) {\n        this.from = from;\n        this.to = to;\n        this.tree = tree;\n        this.offset = offset;\n        this.open = (openStart ? 1 /* Open.Start */ : 0) | (openEnd ? 2 /* Open.End */ : 0);\n    }\n    /**\n    Whether the start of the fragment represents the start of a\n    parse, or the end of a change. (In the second case, it may not\n    be safe to reuse some nodes at the start, depending on the\n    parsing algorithm.)\n    */\n    get openStart() { return (this.open & 1 /* Open.Start */) > 0; }\n    /**\n    Whether the end of the fragment represents the end of a\n    full-document parse, or the start of a change.\n    */\n    get openEnd() { return (this.open & 2 /* Open.End */) > 0; }\n    /**\n    Create a set of fragments from a freshly parsed tree, or update\n    an existing set of fragments by replacing the ones that overlap\n    with a tree with content from the new tree. When `partial` is\n    true, the parse is treated as incomplete, and the resulting\n    fragment has [`openEnd`](#common.TreeFragment.openEnd) set to\n    true.\n    */\n    static addTree(tree, fragments = [], partial = false) {\n        let result = [new TreeFragment(0, tree.length, tree, 0, false, partial)];\n        for (let f of fragments)\n            if (f.to > tree.length)\n                result.push(f);\n        return result;\n    }\n    /**\n    Apply a set of edits to an array of fragments, removing or\n    splitting fragments as necessary to remove edited ranges, and\n    adjusting offsets for fragments that moved.\n    */\n    static applyChanges(fragments, changes, minGap = 128) {\n        if (!changes.length)\n            return fragments;\n        let result = [];\n        let fI = 1, nextF = fragments.length ? fragments[0] : null;\n        for (let cI = 0, pos = 0, off = 0;; cI++) {\n            let nextC = cI < changes.length ? changes[cI] : null;\n            let nextPos = nextC ? nextC.fromA : 1e9;\n            if (nextPos - pos >= minGap)\n                while (nextF && nextF.from < nextPos) {\n                    let cut = nextF;\n                    if (pos >= cut.from || nextPos <= cut.to || off) {\n                        let fFrom = Math.max(cut.from, pos) - off, fTo = Math.min(cut.to, nextPos) - off;\n                        cut = fFrom >= fTo ? null : new TreeFragment(fFrom, fTo, cut.tree, cut.offset + off, cI > 0, !!nextC);\n                    }\n                    if (cut)\n                        result.push(cut);\n                    if (nextF.to > nextPos)\n                        break;\n                    nextF = fI < fragments.length ? fragments[fI++] : null;\n                }\n            if (!nextC)\n                break;\n            pos = nextC.toA;\n            off = nextC.toA - nextC.toB;\n        }\n        return result;\n    }\n}\n/**\nA superclass that parsers should extend.\n*/\nclass Parser {\n    /**\n    Start a parse, returning a [partial parse](#common.PartialParse)\n    object. [`fragments`](#common.TreeFragment) can be passed in to\n    make the parse incremental.\n    \n    By default, the entire input is parsed. You can pass `ranges`,\n    which should be a sorted array of non-empty, non-overlapping\n    ranges, to parse only those ranges. The tree returned in that\n    case will start at `ranges[0].from`.\n    */\n    startParse(input, fragments, ranges) {\n        if (typeof input == \"string\")\n            input = new StringInput(input);\n        ranges = !ranges ? [new Range(0, input.length)] : ranges.length ? ranges.map(r => new Range(r.from, r.to)) : [new Range(0, 0)];\n        return this.createParse(input, fragments || [], ranges);\n    }\n    /**\n    Run a full parse, returning the resulting tree.\n    */\n    parse(input, fragments, ranges) {\n        let parse = this.startParse(input, fragments, ranges);\n        for (;;) {\n            let done = parse.advance();\n            if (done)\n                return done;\n        }\n    }\n}\nclass StringInput {\n    constructor(string) {\n        this.string = string;\n    }\n    get length() { return this.string.length; }\n    chunk(from) { return this.string.slice(from); }\n    get lineChunks() { return false; }\n    read(from, to) { return this.string.slice(from, to); }\n}\n\n/**\nCreate a parse wrapper that, after the inner parse completes,\nscans its tree for mixed language regions with the `nest`\nfunction, runs the resulting [inner parses](#common.NestedParse),\nand then [mounts](#common.NodeProp^mounted) their results onto the\ntree.\n*/\nfunction parseMixed(nest) {\n    return (parse, input, fragments, ranges) => new MixedParse(parse, nest, input, fragments, ranges);\n}\nclass InnerParse {\n    constructor(parser, parse, overlay, target, from) {\n        this.parser = parser;\n        this.parse = parse;\n        this.overlay = overlay;\n        this.target = target;\n        this.from = from;\n    }\n}\nfunction checkRanges(ranges) {\n    if (!ranges.length || ranges.some(r => r.from >= r.to))\n        throw new RangeError(\"Invalid inner parse ranges given: \" + JSON.stringify(ranges));\n}\nclass ActiveOverlay {\n    constructor(parser, predicate, mounts, index, start, target, prev) {\n        this.parser = parser;\n        this.predicate = predicate;\n        this.mounts = mounts;\n        this.index = index;\n        this.start = start;\n        this.target = target;\n        this.prev = prev;\n        this.depth = 0;\n        this.ranges = [];\n    }\n}\nconst stoppedInner = new NodeProp({ perNode: true });\nclass MixedParse {\n    constructor(base, nest, input, fragments, ranges) {\n        this.nest = nest;\n        this.input = input;\n        this.fragments = fragments;\n        this.ranges = ranges;\n        this.inner = [];\n        this.innerDone = 0;\n        this.baseTree = null;\n        this.stoppedAt = null;\n        this.baseParse = base;\n    }\n    advance() {\n        if (this.baseParse) {\n            let done = this.baseParse.advance();\n            if (!done)\n                return null;\n            this.baseParse = null;\n            this.baseTree = done;\n            this.startInner();\n            if (this.stoppedAt != null)\n                for (let inner of this.inner)\n                    inner.parse.stopAt(this.stoppedAt);\n        }\n        if (this.innerDone == this.inner.length) {\n            let result = this.baseTree;\n            if (this.stoppedAt != null)\n                result = new Tree(result.type, result.children, result.positions, result.length, result.propValues.concat([[stoppedInner, this.stoppedAt]]));\n            return result;\n        }\n        let inner = this.inner[this.innerDone], done = inner.parse.advance();\n        if (done) {\n            this.innerDone++;\n            // This is a somewhat dodgy but super helpful hack where we\n            // patch up nodes created by the inner parse (and thus\n            // presumably not aliased anywhere else) to hold the information\n            // about the inner parse.\n            let props = Object.assign(Object.create(null), inner.target.props);\n            props[NodeProp.mounted.id] = new MountedTree(done, inner.overlay, inner.parser);\n            inner.target.props = props;\n        }\n        return null;\n    }\n    get parsedPos() {\n        if (this.baseParse)\n            return 0;\n        let pos = this.input.length;\n        for (let i = this.innerDone; i < this.inner.length; i++) {\n            if (this.inner[i].from < pos)\n                pos = Math.min(pos, this.inner[i].parse.parsedPos);\n        }\n        return pos;\n    }\n    stopAt(pos) {\n        this.stoppedAt = pos;\n        if (this.baseParse)\n            this.baseParse.stopAt(pos);\n        else\n            for (let i = this.innerDone; i < this.inner.length; i++)\n                this.inner[i].parse.stopAt(pos);\n    }\n    startInner() {\n        let fragmentCursor = new FragmentCursor(this.fragments);\n        let overlay = null;\n        let covered = null;\n        let cursor = new TreeCursor(new TreeNode(this.baseTree, this.ranges[0].from, 0, null), IterMode.IncludeAnonymous | IterMode.IgnoreMounts);\n        scan: for (let nest, isCovered;;) {\n            let enter = true, range;\n            if (this.stoppedAt != null && cursor.from >= this.stoppedAt) {\n                enter = false;\n            }\n            else if (fragmentCursor.hasNode(cursor)) {\n                if (overlay) {\n                    let match = overlay.mounts.find(m => m.frag.from <= cursor.from && m.frag.to >= cursor.to && m.mount.overlay);\n                    if (match)\n                        for (let r of match.mount.overlay) {\n                            let from = r.from + match.pos, to = r.to + match.pos;\n                            if (from >= cursor.from && to <= cursor.to && !overlay.ranges.some(r => r.from < to && r.to > from))\n                                overlay.ranges.push({ from, to });\n                        }\n                }\n                enter = false;\n            }\n            else if (covered && (isCovered = checkCover(covered.ranges, cursor.from, cursor.to))) {\n                enter = isCovered != 2 /* Cover.Full */;\n            }\n            else if (!cursor.type.isAnonymous && (nest = this.nest(cursor, this.input)) &&\n                (cursor.from < cursor.to || !nest.overlay)) {\n                if (!cursor.tree)\n                    materialize(cursor);\n                let oldMounts = fragmentCursor.findMounts(cursor.from, nest.parser);\n                if (typeof nest.overlay == \"function\") {\n                    overlay = new ActiveOverlay(nest.parser, nest.overlay, oldMounts, this.inner.length, cursor.from, cursor.tree, overlay);\n                }\n                else {\n                    let ranges = punchRanges(this.ranges, nest.overlay ||\n                        (cursor.from < cursor.to ? [new Range(cursor.from, cursor.to)] : []));\n                    if (ranges.length)\n                        checkRanges(ranges);\n                    if (ranges.length || !nest.overlay)\n                        this.inner.push(new InnerParse(nest.parser, ranges.length ? nest.parser.startParse(this.input, enterFragments(oldMounts, ranges), ranges)\n                            : nest.parser.startParse(\"\"), nest.overlay ? nest.overlay.map(r => new Range(r.from - cursor.from, r.to - cursor.from)) : null, cursor.tree, ranges.length ? ranges[0].from : cursor.from));\n                    if (!nest.overlay)\n                        enter = false;\n                    else if (ranges.length)\n                        covered = { ranges, depth: 0, prev: covered };\n                }\n            }\n            else if (overlay && (range = overlay.predicate(cursor))) {\n                if (range === true)\n                    range = new Range(cursor.from, cursor.to);\n                if (range.from < range.to)\n                    overlay.ranges.push(range);\n            }\n            if (enter && cursor.firstChild()) {\n                if (overlay)\n                    overlay.depth++;\n                if (covered)\n                    covered.depth++;\n            }\n            else {\n                for (;;) {\n                    if (cursor.nextSibling())\n                        break;\n                    if (!cursor.parent())\n                        break scan;\n                    if (overlay && !--overlay.depth) {\n                        let ranges = punchRanges(this.ranges, overlay.ranges);\n                        if (ranges.length) {\n                            checkRanges(ranges);\n                            this.inner.splice(overlay.index, 0, new InnerParse(overlay.parser, overlay.parser.startParse(this.input, enterFragments(overlay.mounts, ranges), ranges), overlay.ranges.map(r => new Range(r.from - overlay.start, r.to - overlay.start)), overlay.target, ranges[0].from));\n                        }\n                        overlay = overlay.prev;\n                    }\n                    if (covered && !--covered.depth)\n                        covered = covered.prev;\n                }\n            }\n        }\n    }\n}\nfunction checkCover(covered, from, to) {\n    for (let range of covered) {\n        if (range.from >= to)\n            break;\n        if (range.to > from)\n            return range.from <= from && range.to >= to ? 2 /* Cover.Full */ : 1 /* Cover.Partial */;\n    }\n    return 0 /* Cover.None */;\n}\n// Take a piece of buffer and convert it into a stand-alone\n// TreeBuffer.\nfunction sliceBuf(buf, startI, endI, nodes, positions, off) {\n    if (startI < endI) {\n        let from = buf.buffer[startI + 1];\n        nodes.push(buf.slice(startI, endI, from));\n        positions.push(from - off);\n    }\n}\n// This function takes a node that's in a buffer, and converts it, and\n// its parent buffer nodes, into a Tree. This is again acting on the\n// assumption that the trees and buffers have been constructed by the\n// parse that was ran via the mix parser, and thus aren't shared with\n// any other code, making violations of the immutability safe.\nfunction materialize(cursor) {\n    let { node } = cursor, stack = [];\n    let buffer = node.context.buffer;\n    // Scan up to the nearest tree\n    do {\n        stack.push(cursor.index);\n        cursor.parent();\n    } while (!cursor.tree);\n    // Find the index of the buffer in that tree\n    let base = cursor.tree, i = base.children.indexOf(buffer);\n    let buf = base.children[i], b = buf.buffer, newStack = [i];\n    // Split a level in the buffer, putting the nodes before and after\n    // the child that contains `node` into new buffers.\n    function split(startI, endI, type, innerOffset, length, stackPos) {\n        let targetI = stack[stackPos];\n        let children = [], positions = [];\n        sliceBuf(buf, startI, targetI, children, positions, innerOffset);\n        let from = b[targetI + 1], to = b[targetI + 2];\n        newStack.push(children.length);\n        let child = stackPos\n            ? split(targetI + 4, b[targetI + 3], buf.set.types[b[targetI]], from, to - from, stackPos - 1)\n            : node.toTree();\n        children.push(child);\n        positions.push(from - innerOffset);\n        sliceBuf(buf, b[targetI + 3], endI, children, positions, innerOffset);\n        return new Tree(type, children, positions, length);\n    }\n    base.children[i] = split(0, b.length, NodeType.none, 0, buf.length, stack.length - 1);\n    // Move the cursor back to the target node\n    for (let index of newStack) {\n        let tree = cursor.tree.children[index], pos = cursor.tree.positions[index];\n        cursor.yield(new TreeNode(tree, pos + cursor.from, index, cursor._tree));\n    }\n}\nclass StructureCursor {\n    constructor(root, offset) {\n        this.offset = offset;\n        this.done = false;\n        this.cursor = root.cursor(IterMode.IncludeAnonymous | IterMode.IgnoreMounts);\n    }\n    // Move to the first node (in pre-order) that starts at or after `pos`.\n    moveTo(pos) {\n        let { cursor } = this, p = pos - this.offset;\n        while (!this.done && cursor.from < p) {\n            if (cursor.to >= pos && cursor.enter(p, 1, IterMode.IgnoreOverlays | IterMode.ExcludeBuffers)) ;\n            else if (!cursor.next(false))\n                this.done = true;\n        }\n    }\n    hasNode(cursor) {\n        this.moveTo(cursor.from);\n        if (!this.done && this.cursor.from + this.offset == cursor.from && this.cursor.tree) {\n            for (let tree = this.cursor.tree;;) {\n                if (tree == cursor.tree)\n                    return true;\n                if (tree.children.length && tree.positions[0] == 0 && tree.children[0] instanceof Tree)\n                    tree = tree.children[0];\n                else\n                    break;\n            }\n        }\n        return false;\n    }\n}\nclass FragmentCursor {\n    constructor(fragments) {\n        var _a;\n        this.fragments = fragments;\n        this.curTo = 0;\n        this.fragI = 0;\n        if (fragments.length) {\n            let first = this.curFrag = fragments[0];\n            this.curTo = (_a = first.tree.prop(stoppedInner)) !== null && _a !== void 0 ? _a : first.to;\n            this.inner = new StructureCursor(first.tree, -first.offset);\n        }\n        else {\n            this.curFrag = this.inner = null;\n        }\n    }\n    hasNode(node) {\n        while (this.curFrag && node.from >= this.curTo)\n            this.nextFrag();\n        return this.curFrag && this.curFrag.from <= node.from && this.curTo >= node.to && this.inner.hasNode(node);\n    }\n    nextFrag() {\n        var _a;\n        this.fragI++;\n        if (this.fragI == this.fragments.length) {\n            this.curFrag = this.inner = null;\n        }\n        else {\n            let frag = this.curFrag = this.fragments[this.fragI];\n            this.curTo = (_a = frag.tree.prop(stoppedInner)) !== null && _a !== void 0 ? _a : frag.to;\n            this.inner = new StructureCursor(frag.tree, -frag.offset);\n        }\n    }\n    findMounts(pos, parser) {\n        var _a;\n        let result = [];\n        if (this.inner) {\n            this.inner.cursor.moveTo(pos, 1);\n            for (let pos = this.inner.cursor.node; pos; pos = pos.parent) {\n                let mount = (_a = pos.tree) === null || _a === void 0 ? void 0 : _a.prop(NodeProp.mounted);\n                if (mount && mount.parser == parser) {\n                    for (let i = this.fragI; i < this.fragments.length; i++) {\n                        let frag = this.fragments[i];\n                        if (frag.from >= pos.to)\n                            break;\n                        if (frag.tree == this.curFrag.tree)\n                            result.push({\n                                frag,\n                                pos: pos.from - frag.offset,\n                                mount\n                            });\n                    }\n                }\n            }\n        }\n        return result;\n    }\n}\nfunction punchRanges(outer, ranges) {\n    let copy = null, current = ranges;\n    for (let i = 1, j = 0; i < outer.length; i++) {\n        let gapFrom = outer[i - 1].to, gapTo = outer[i].from;\n        for (; j < current.length; j++) {\n            let r = current[j];\n            if (r.from >= gapTo)\n                break;\n            if (r.to <= gapFrom)\n                continue;\n            if (!copy)\n                current = copy = ranges.slice();\n            if (r.from < gapFrom) {\n                copy[j] = new Range(r.from, gapFrom);\n                if (r.to > gapTo)\n                    copy.splice(j + 1, 0, new Range(gapTo, r.to));\n            }\n            else if (r.to > gapTo) {\n                copy[j--] = new Range(gapTo, r.to);\n            }\n            else {\n                copy.splice(j--, 1);\n            }\n        }\n    }\n    return current;\n}\nfunction findCoverChanges(a, b, from, to) {\n    let iA = 0, iB = 0, inA = false, inB = false, pos = -1e9;\n    let result = [];\n    for (;;) {\n        let nextA = iA == a.length ? 1e9 : inA ? a[iA].to : a[iA].from;\n        let nextB = iB == b.length ? 1e9 : inB ? b[iB].to : b[iB].from;\n        if (inA != inB) {\n            let start = Math.max(pos, from), end = Math.min(nextA, nextB, to);\n            if (start < end)\n                result.push(new Range(start, end));\n        }\n        pos = Math.min(nextA, nextB);\n        if (pos == 1e9)\n            break;\n        if (nextA == pos) {\n            if (!inA)\n                inA = true;\n            else {\n                inA = false;\n                iA++;\n            }\n        }\n        if (nextB == pos) {\n            if (!inB)\n                inB = true;\n            else {\n                inB = false;\n                iB++;\n            }\n        }\n    }\n    return result;\n}\n// Given a number of fragments for the outer tree, and a set of ranges\n// to parse, find fragments for inner trees mounted around those\n// ranges, if any.\nfunction enterFragments(mounts, ranges) {\n    let result = [];\n    for (let { pos, mount, frag } of mounts) {\n        let startPos = pos + (mount.overlay ? mount.overlay[0].from : 0), endPos = startPos + mount.tree.length;\n        let from = Math.max(frag.from, startPos), to = Math.min(frag.to, endPos);\n        if (mount.overlay) {\n            let overlay = mount.overlay.map(r => new Range(r.from + pos, r.to + pos));\n            let changes = findCoverChanges(ranges, overlay, from, to);\n            for (let i = 0, pos = from;; i++) {\n                let last = i == changes.length, end = last ? to : changes[i].from;\n                if (end > pos)\n                    result.push(new TreeFragment(pos, end, mount.tree, -startPos, frag.from >= pos || frag.openStart, frag.to <= end || frag.openEnd));\n                if (last)\n                    break;\n                pos = changes[i].to;\n            }\n        }\n        else {\n            result.push(new TreeFragment(from, to, mount.tree, -startPos, frag.from >= startPos || frag.openStart, frag.to <= endPos || frag.openEnd));\n        }\n    }\n    return result;\n}\n\nexport { DefaultBufferLength, IterMode, MountedTree, NodeProp, NodeSet, NodeType, NodeWeakMap, Parser, Tree, TreeBuffer, TreeCursor, TreeFragment, parseMixed };\n", "import { <PERSON><PERSON><PERSON>, NodeProp, NodeSet, NodeType, <PERSON><PERSON>ult<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Tree, IterMode } from '@lezer/common';\n\n/**\nA parse stack. These are used internally by the parser to track\nparsing progress. They also provide some properties and methods\nthat external code such as a tokenizer can use to get information\nabout the parse state.\n*/\nclass Stack {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The parse that this stack is part of @internal\n    */\n    p, \n    /**\n    Holds state, input pos, buffer index triplets for all but the\n    top state @internal\n    */\n    stack, \n    /**\n    The current parse state @internal\n    */\n    state, \n    // The position at which the next reduce should take place. This\n    // can be less than `this.pos` when skipped expressions have been\n    // added to the stack (which should be moved outside of the next\n    // reduction)\n    /**\n    @internal\n    */\n    reducePos, \n    /**\n    The input position up to which this stack has parsed.\n    */\n    pos, \n    /**\n    The dynamic score of the stack, including dynamic precedence\n    and error-recovery penalties\n    @internal\n    */\n    score, \n    // The output buffer. Holds (type, start, end, size) quads\n    // representing nodes created by the parser, where `size` is\n    // amount of buffer array entries covered by this node.\n    /**\n    @internal\n    */\n    buffer, \n    // The base offset of the buffer. When stacks are split, the split\n    // instance shared the buffer history with its parent up to\n    // `bufferBase`, which is the absolute offset (including the\n    // offset of previous splits) into the buffer at which this stack\n    // starts writing.\n    /**\n    @internal\n    */\n    bufferBase, \n    /**\n    @internal\n    */\n    curContext, \n    /**\n    @internal\n    */\n    lookAhead = 0, \n    // A parent stack from which this was split off, if any. This is\n    // set up so that it always points to a stack that has some\n    // additional buffer content, never to a stack with an equal\n    // `bufferBase`.\n    /**\n    @internal\n    */\n    parent) {\n        this.p = p;\n        this.stack = stack;\n        this.state = state;\n        this.reducePos = reducePos;\n        this.pos = pos;\n        this.score = score;\n        this.buffer = buffer;\n        this.bufferBase = bufferBase;\n        this.curContext = curContext;\n        this.lookAhead = lookAhead;\n        this.parent = parent;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return `[${this.stack.filter((_, i) => i % 3 == 0).concat(this.state)}]@${this.pos}${this.score ? \"!\" + this.score : \"\"}`;\n    }\n    // Start an empty stack\n    /**\n    @internal\n    */\n    static start(p, state, pos = 0) {\n        let cx = p.parser.context;\n        return new Stack(p, [], state, pos, pos, 0, [], 0, cx ? new StackContext(cx, cx.start) : null, 0, null);\n    }\n    /**\n    The stack's current [context](#lr.ContextTracker) value, if\n    any. Its type will depend on the context tracker's type\n    parameter, or it will be `null` if there is no context\n    tracker.\n    */\n    get context() { return this.curContext ? this.curContext.context : null; }\n    // Push a state onto the stack, tracking its start position as well\n    // as the buffer base at that point.\n    /**\n    @internal\n    */\n    pushState(state, start) {\n        this.stack.push(this.state, start, this.bufferBase + this.buffer.length);\n        this.state = state;\n    }\n    // Apply a reduce action\n    /**\n    @internal\n    */\n    reduce(action) {\n        var _a;\n        let depth = action >> 19 /* Action.ReduceDepthShift */, type = action & 65535 /* Action.ValueMask */;\n        let { parser } = this.p;\n        if (this.reducePos < this.pos - 25 /* Lookahead.Margin */)\n            this.setLookAhead(this.pos);\n        let dPrec = parser.dynamicPrecedence(type);\n        if (dPrec)\n            this.score += dPrec;\n        if (depth == 0) {\n            this.pushState(parser.getGoto(this.state, type, true), this.reducePos);\n            // Zero-depth reductions are a special case—they add stuff to\n            // the stack without popping anything off.\n            if (type < parser.minRepeatTerm)\n                this.storeNode(type, this.reducePos, this.reducePos, 4, true);\n            this.reduceContext(type, this.reducePos);\n            return;\n        }\n        // Find the base index into `this.stack`, content after which will\n        // be dropped. Note that with `StayFlag` reductions we need to\n        // consume two extra frames (the dummy parent node for the skipped\n        // expression and the state that we'll be staying in, which should\n        // be moved to `this.state`).\n        let base = this.stack.length - ((depth - 1) * 3) - (action & 262144 /* Action.StayFlag */ ? 6 : 0);\n        let start = base ? this.stack[base - 2] : this.p.ranges[0].from, size = this.reducePos - start;\n        // This is a kludge to try and detect overly deep left-associative\n        // trees, which will not increase the parse stack depth and thus\n        // won't be caught by the regular stack-depth limit check.\n        if (size >= 2000 /* Recover.MinBigReduction */ && !((_a = this.p.parser.nodeSet.types[type]) === null || _a === void 0 ? void 0 : _a.isAnonymous)) {\n            if (start == this.p.lastBigReductionStart) {\n                this.p.bigReductionCount++;\n                this.p.lastBigReductionSize = size;\n            }\n            else if (this.p.lastBigReductionSize < size) {\n                this.p.bigReductionCount = 1;\n                this.p.lastBigReductionStart = start;\n                this.p.lastBigReductionSize = size;\n            }\n        }\n        let bufferBase = base ? this.stack[base - 1] : 0, count = this.bufferBase + this.buffer.length - bufferBase;\n        // Store normal terms or `R -> R R` repeat reductions\n        if (type < parser.minRepeatTerm || (action & 131072 /* Action.RepeatFlag */)) {\n            let pos = parser.stateFlag(this.state, 1 /* StateFlag.Skipped */) ? this.pos : this.reducePos;\n            this.storeNode(type, start, pos, count + 4, true);\n        }\n        if (action & 262144 /* Action.StayFlag */) {\n            this.state = this.stack[base];\n        }\n        else {\n            let baseStateID = this.stack[base - 3];\n            this.state = parser.getGoto(baseStateID, type, true);\n        }\n        while (this.stack.length > base)\n            this.stack.pop();\n        this.reduceContext(type, start);\n    }\n    // Shift a value into the buffer\n    /**\n    @internal\n    */\n    storeNode(term, start, end, size = 4, isReduce = false) {\n        if (term == 0 /* Term.Err */ &&\n            (!this.stack.length || this.stack[this.stack.length - 1] < this.buffer.length + this.bufferBase)) {\n            // Try to omit/merge adjacent error nodes\n            let cur = this, top = this.buffer.length;\n            if (top == 0 && cur.parent) {\n                top = cur.bufferBase - cur.parent.bufferBase;\n                cur = cur.parent;\n            }\n            if (top > 0 && cur.buffer[top - 4] == 0 /* Term.Err */ && cur.buffer[top - 1] > -1) {\n                if (start == end)\n                    return;\n                if (cur.buffer[top - 2] >= start) {\n                    cur.buffer[top - 2] = end;\n                    return;\n                }\n            }\n        }\n        if (!isReduce || this.pos == end) { // Simple case, just append\n            this.buffer.push(term, start, end, size);\n        }\n        else { // There may be skipped nodes that have to be moved forward\n            let index = this.buffer.length;\n            if (index > 0 && this.buffer[index - 4] != 0 /* Term.Err */)\n                while (index > 0 && this.buffer[index - 2] > end) {\n                    // Move this record forward\n                    this.buffer[index] = this.buffer[index - 4];\n                    this.buffer[index + 1] = this.buffer[index - 3];\n                    this.buffer[index + 2] = this.buffer[index - 2];\n                    this.buffer[index + 3] = this.buffer[index - 1];\n                    index -= 4;\n                    if (size > 4)\n                        size -= 4;\n                }\n            this.buffer[index] = term;\n            this.buffer[index + 1] = start;\n            this.buffer[index + 2] = end;\n            this.buffer[index + 3] = size;\n        }\n    }\n    // Apply a shift action\n    /**\n    @internal\n    */\n    shift(action, type, start, end) {\n        if (action & 131072 /* Action.GotoFlag */) {\n            this.pushState(action & 65535 /* Action.ValueMask */, this.pos);\n        }\n        else if ((action & 262144 /* Action.StayFlag */) == 0) { // Regular shift\n            let nextState = action, { parser } = this.p;\n            if (end > this.pos || type <= parser.maxNode) {\n                this.pos = end;\n                if (!parser.stateFlag(nextState, 1 /* StateFlag.Skipped */))\n                    this.reducePos = end;\n            }\n            this.pushState(nextState, start);\n            this.shiftContext(type, start);\n            if (type <= parser.maxNode)\n                this.buffer.push(type, start, end, 4);\n        }\n        else { // Shift-and-stay, which means this is a skipped token\n            this.pos = end;\n            this.shiftContext(type, start);\n            if (type <= this.p.parser.maxNode)\n                this.buffer.push(type, start, end, 4);\n        }\n    }\n    // Apply an action\n    /**\n    @internal\n    */\n    apply(action, next, nextStart, nextEnd) {\n        if (action & 65536 /* Action.ReduceFlag */)\n            this.reduce(action);\n        else\n            this.shift(action, next, nextStart, nextEnd);\n    }\n    // Add a prebuilt (reused) node into the buffer.\n    /**\n    @internal\n    */\n    useNode(value, next) {\n        let index = this.p.reused.length - 1;\n        if (index < 0 || this.p.reused[index] != value) {\n            this.p.reused.push(value);\n            index++;\n        }\n        let start = this.pos;\n        this.reducePos = this.pos = start + value.length;\n        this.pushState(next, start);\n        this.buffer.push(index, start, this.reducePos, -1 /* size == -1 means this is a reused value */);\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.reuse(this.curContext.context, value, this, this.p.stream.reset(this.pos - value.length)));\n    }\n    // Split the stack. Due to the buffer sharing and the fact\n    // that `this.stack` tends to stay quite shallow, this isn't very\n    // expensive.\n    /**\n    @internal\n    */\n    split() {\n        let parent = this;\n        let off = parent.buffer.length;\n        // Because the top of the buffer (after this.pos) may be mutated\n        // to reorder reductions and skipped tokens, and shared buffers\n        // should be immutable, this copies any outstanding skipped tokens\n        // to the new buffer, and puts the base pointer before them.\n        while (off > 0 && parent.buffer[off - 2] > parent.reducePos)\n            off -= 4;\n        let buffer = parent.buffer.slice(off), base = parent.bufferBase + off;\n        // Make sure parent points to an actual parent with content, if there is such a parent.\n        while (parent && base == parent.bufferBase)\n            parent = parent.parent;\n        return new Stack(this.p, this.stack.slice(), this.state, this.reducePos, this.pos, this.score, buffer, base, this.curContext, this.lookAhead, parent);\n    }\n    // Try to recover from an error by 'deleting' (ignoring) one token.\n    /**\n    @internal\n    */\n    recoverByDelete(next, nextEnd) {\n        let isNode = next <= this.p.parser.maxNode;\n        if (isNode)\n            this.storeNode(next, this.pos, nextEnd, 4);\n        this.storeNode(0 /* Term.Err */, this.pos, nextEnd, isNode ? 8 : 4);\n        this.pos = this.reducePos = nextEnd;\n        this.score -= 190 /* Recover.Delete */;\n    }\n    /**\n    Check if the given term would be able to be shifted (optionally\n    after some reductions) on this stack. This can be useful for\n    external tokenizers that want to make sure they only provide a\n    given token when it applies.\n    */\n    canShift(term) {\n        for (let sim = new SimulatedStack(this);;) {\n            let action = this.p.parser.stateSlot(sim.state, 4 /* ParseState.DefaultReduce */) || this.p.parser.hasAction(sim.state, term);\n            if (action == 0)\n                return false;\n            if ((action & 65536 /* Action.ReduceFlag */) == 0)\n                return true;\n            sim.reduce(action);\n        }\n    }\n    // Apply up to Recover.MaxNext recovery actions that conceptually\n    // inserts some missing token or rule.\n    /**\n    @internal\n    */\n    recoverByInsert(next) {\n        if (this.stack.length >= 300 /* Recover.MaxInsertStackDepth */)\n            return [];\n        let nextStates = this.p.parser.nextStates(this.state);\n        if (nextStates.length > 4 /* Recover.MaxNext */ << 1 || this.stack.length >= 120 /* Recover.DampenInsertStackDepth */) {\n            let best = [];\n            for (let i = 0, s; i < nextStates.length; i += 2) {\n                if ((s = nextStates[i + 1]) != this.state && this.p.parser.hasAction(s, next))\n                    best.push(nextStates[i], s);\n            }\n            if (this.stack.length < 120 /* Recover.DampenInsertStackDepth */)\n                for (let i = 0; best.length < 4 /* Recover.MaxNext */ << 1 && i < nextStates.length; i += 2) {\n                    let s = nextStates[i + 1];\n                    if (!best.some((v, i) => (i & 1) && v == s))\n                        best.push(nextStates[i], s);\n                }\n            nextStates = best;\n        }\n        let result = [];\n        for (let i = 0; i < nextStates.length && result.length < 4 /* Recover.MaxNext */; i += 2) {\n            let s = nextStates[i + 1];\n            if (s == this.state)\n                continue;\n            let stack = this.split();\n            stack.pushState(s, this.pos);\n            stack.storeNode(0 /* Term.Err */, stack.pos, stack.pos, 4, true);\n            stack.shiftContext(nextStates[i], this.pos);\n            stack.reducePos = this.pos;\n            stack.score -= 200 /* Recover.Insert */;\n            result.push(stack);\n        }\n        return result;\n    }\n    // Force a reduce, if possible. Return false if that can't\n    // be done.\n    /**\n    @internal\n    */\n    forceReduce() {\n        let { parser } = this.p;\n        let reduce = parser.stateSlot(this.state, 5 /* ParseState.ForcedReduce */);\n        if ((reduce & 65536 /* Action.ReduceFlag */) == 0)\n            return false;\n        if (!parser.validAction(this.state, reduce)) {\n            let depth = reduce >> 19 /* Action.ReduceDepthShift */, term = reduce & 65535 /* Action.ValueMask */;\n            let target = this.stack.length - depth * 3;\n            if (target < 0 || parser.getGoto(this.stack[target], term, false) < 0) {\n                let backup = this.findForcedReduction();\n                if (backup == null)\n                    return false;\n                reduce = backup;\n            }\n            this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n            this.score -= 100 /* Recover.Reduce */;\n        }\n        this.reducePos = this.pos;\n        this.reduce(reduce);\n        return true;\n    }\n    /**\n    Try to scan through the automaton to find some kind of reduction\n    that can be applied. Used when the regular ForcedReduce field\n    isn't a valid action. @internal\n    */\n    findForcedReduction() {\n        let { parser } = this.p, seen = [];\n        let explore = (state, depth) => {\n            if (seen.includes(state))\n                return;\n            seen.push(state);\n            return parser.allActions(state, (action) => {\n                if (action & (262144 /* Action.StayFlag */ | 131072 /* Action.GotoFlag */)) ;\n                else if (action & 65536 /* Action.ReduceFlag */) {\n                    let rDepth = (action >> 19 /* Action.ReduceDepthShift */) - depth;\n                    if (rDepth > 1) {\n                        let term = action & 65535 /* Action.ValueMask */, target = this.stack.length - rDepth * 3;\n                        if (target >= 0 && parser.getGoto(this.stack[target], term, false) >= 0)\n                            return (rDepth << 19 /* Action.ReduceDepthShift */) | 65536 /* Action.ReduceFlag */ | term;\n                    }\n                }\n                else {\n                    let found = explore(action, depth + 1);\n                    if (found != null)\n                        return found;\n                }\n            });\n        };\n        return explore(this.state, 0);\n    }\n    /**\n    @internal\n    */\n    forceAll() {\n        while (!this.p.parser.stateFlag(this.state, 2 /* StateFlag.Accepting */)) {\n            if (!this.forceReduce()) {\n                this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n                break;\n            }\n        }\n        return this;\n    }\n    /**\n    Check whether this state has no further actions (assumed to be a direct descendant of the\n    top state, since any other states must be able to continue\n    somehow). @internal\n    */\n    get deadEnd() {\n        if (this.stack.length != 3)\n            return false;\n        let { parser } = this.p;\n        return parser.data[parser.stateSlot(this.state, 1 /* ParseState.Actions */)] == 65535 /* Seq.End */ &&\n            !parser.stateSlot(this.state, 4 /* ParseState.DefaultReduce */);\n    }\n    /**\n    Restart the stack (put it back in its start state). Only safe\n    when this.stack.length == 3 (state is directly below the top\n    state). @internal\n    */\n    restart() {\n        this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n        this.state = this.stack[0];\n        this.stack.length = 0;\n    }\n    /**\n    @internal\n    */\n    sameState(other) {\n        if (this.state != other.state || this.stack.length != other.stack.length)\n            return false;\n        for (let i = 0; i < this.stack.length; i += 3)\n            if (this.stack[i] != other.stack[i])\n                return false;\n        return true;\n    }\n    /**\n    Get the parser used by this stack.\n    */\n    get parser() { return this.p.parser; }\n    /**\n    Test whether a given dialect (by numeric ID, as exported from\n    the terms file) is enabled.\n    */\n    dialectEnabled(dialectID) { return this.p.parser.dialect.flags[dialectID]; }\n    shiftContext(term, start) {\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.shift(this.curContext.context, term, this, this.p.stream.reset(start)));\n    }\n    reduceContext(term, start) {\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.reduce(this.curContext.context, term, this, this.p.stream.reset(start)));\n    }\n    /**\n    @internal\n    */\n    emitContext() {\n        let last = this.buffer.length - 1;\n        if (last < 0 || this.buffer[last] != -3)\n            this.buffer.push(this.curContext.hash, this.pos, this.pos, -3);\n    }\n    /**\n    @internal\n    */\n    emitLookAhead() {\n        let last = this.buffer.length - 1;\n        if (last < 0 || this.buffer[last] != -4)\n            this.buffer.push(this.lookAhead, this.pos, this.pos, -4);\n    }\n    updateContext(context) {\n        if (context != this.curContext.context) {\n            let newCx = new StackContext(this.curContext.tracker, context);\n            if (newCx.hash != this.curContext.hash)\n                this.emitContext();\n            this.curContext = newCx;\n        }\n    }\n    /**\n    @internal\n    */\n    setLookAhead(lookAhead) {\n        if (lookAhead > this.lookAhead) {\n            this.emitLookAhead();\n            this.lookAhead = lookAhead;\n        }\n    }\n    /**\n    @internal\n    */\n    close() {\n        if (this.curContext && this.curContext.tracker.strict)\n            this.emitContext();\n        if (this.lookAhead > 0)\n            this.emitLookAhead();\n    }\n}\nclass StackContext {\n    constructor(tracker, context) {\n        this.tracker = tracker;\n        this.context = context;\n        this.hash = tracker.strict ? tracker.hash(context) : 0;\n    }\n}\n// Used to cheaply run some reductions to scan ahead without mutating\n// an entire stack\nclass SimulatedStack {\n    constructor(start) {\n        this.start = start;\n        this.state = start.state;\n        this.stack = start.stack;\n        this.base = this.stack.length;\n    }\n    reduce(action) {\n        let term = action & 65535 /* Action.ValueMask */, depth = action >> 19 /* Action.ReduceDepthShift */;\n        if (depth == 0) {\n            if (this.stack == this.start.stack)\n                this.stack = this.stack.slice();\n            this.stack.push(this.state, 0, 0);\n            this.base += 3;\n        }\n        else {\n            this.base -= (depth - 1) * 3;\n        }\n        let goto = this.start.p.parser.getGoto(this.stack[this.base - 3], term, true);\n        this.state = goto;\n    }\n}\n// This is given to `Tree.build` to build a buffer, and encapsulates\n// the parent-stack-walking necessary to read the nodes.\nclass StackBufferCursor {\n    constructor(stack, pos, index) {\n        this.stack = stack;\n        this.pos = pos;\n        this.index = index;\n        this.buffer = stack.buffer;\n        if (this.index == 0)\n            this.maybeNext();\n    }\n    static create(stack, pos = stack.bufferBase + stack.buffer.length) {\n        return new StackBufferCursor(stack, pos, pos - stack.bufferBase);\n    }\n    maybeNext() {\n        let next = this.stack.parent;\n        if (next != null) {\n            this.index = this.stack.bufferBase - next.bufferBase;\n            this.stack = next;\n            this.buffer = next.buffer;\n        }\n    }\n    get id() { return this.buffer[this.index - 4]; }\n    get start() { return this.buffer[this.index - 3]; }\n    get end() { return this.buffer[this.index - 2]; }\n    get size() { return this.buffer[this.index - 1]; }\n    next() {\n        this.index -= 4;\n        this.pos -= 4;\n        if (this.index == 0)\n            this.maybeNext();\n    }\n    fork() {\n        return new StackBufferCursor(this.stack, this.pos, this.index);\n    }\n}\n\n// See lezer-generator/src/encode.ts for comments about the encoding\n// used here\nfunction decodeArray(input, Type = Uint16Array) {\n    if (typeof input != \"string\")\n        return input;\n    let array = null;\n    for (let pos = 0, out = 0; pos < input.length;) {\n        let value = 0;\n        for (;;) {\n            let next = input.charCodeAt(pos++), stop = false;\n            if (next == 126 /* Encode.BigValCode */) {\n                value = 65535 /* Encode.BigVal */;\n                break;\n            }\n            if (next >= 92 /* Encode.Gap2 */)\n                next--;\n            if (next >= 34 /* Encode.Gap1 */)\n                next--;\n            let digit = next - 32 /* Encode.Start */;\n            if (digit >= 46 /* Encode.Base */) {\n                digit -= 46 /* Encode.Base */;\n                stop = true;\n            }\n            value += digit;\n            if (stop)\n                break;\n            value *= 46 /* Encode.Base */;\n        }\n        if (array)\n            array[out++] = value;\n        else\n            array = new Type(value);\n    }\n    return array;\n}\n\nclass CachedToken {\n    constructor() {\n        this.start = -1;\n        this.value = -1;\n        this.end = -1;\n        this.extended = -1;\n        this.lookAhead = 0;\n        this.mask = 0;\n        this.context = 0;\n    }\n}\nconst nullToken = new CachedToken;\n/**\n[Tokenizers](#lr.ExternalTokenizer) interact with the input\nthrough this interface. It presents the input as a stream of\ncharacters, tracking lookahead and hiding the complexity of\n[ranges](#common.Parser.parse^ranges) from tokenizer code.\n*/\nclass InputStream {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    input, \n    /**\n    @internal\n    */\n    ranges) {\n        this.input = input;\n        this.ranges = ranges;\n        /**\n        @internal\n        */\n        this.chunk = \"\";\n        /**\n        @internal\n        */\n        this.chunkOff = 0;\n        /**\n        Backup chunk\n        */\n        this.chunk2 = \"\";\n        this.chunk2Pos = 0;\n        /**\n        The character code of the next code unit in the input, or -1\n        when the stream is at the end of the input.\n        */\n        this.next = -1;\n        /**\n        @internal\n        */\n        this.token = nullToken;\n        this.rangeIndex = 0;\n        this.pos = this.chunkPos = ranges[0].from;\n        this.range = ranges[0];\n        this.end = ranges[ranges.length - 1].to;\n        this.readNext();\n    }\n    /**\n    @internal\n    */\n    resolveOffset(offset, assoc) {\n        let range = this.range, index = this.rangeIndex;\n        let pos = this.pos + offset;\n        while (pos < range.from) {\n            if (!index)\n                return null;\n            let next = this.ranges[--index];\n            pos -= range.from - next.to;\n            range = next;\n        }\n        while (assoc < 0 ? pos > range.to : pos >= range.to) {\n            if (index == this.ranges.length - 1)\n                return null;\n            let next = this.ranges[++index];\n            pos += next.from - range.to;\n            range = next;\n        }\n        return pos;\n    }\n    /**\n    @internal\n    */\n    clipPos(pos) {\n        if (pos >= this.range.from && pos < this.range.to)\n            return pos;\n        for (let range of this.ranges)\n            if (range.to > pos)\n                return Math.max(pos, range.from);\n        return this.end;\n    }\n    /**\n    Look at a code unit near the stream position. `.peek(0)` equals\n    `.next`, `.peek(-1)` gives you the previous character, and so\n    on.\n    \n    Note that looking around during tokenizing creates dependencies\n    on potentially far-away content, which may reduce the\n    effectiveness incremental parsing—when looking forward—or even\n    cause invalid reparses when looking backward more than 25 code\n    units, since the library does not track lookbehind.\n    */\n    peek(offset) {\n        let idx = this.chunkOff + offset, pos, result;\n        if (idx >= 0 && idx < this.chunk.length) {\n            pos = this.pos + offset;\n            result = this.chunk.charCodeAt(idx);\n        }\n        else {\n            let resolved = this.resolveOffset(offset, 1);\n            if (resolved == null)\n                return -1;\n            pos = resolved;\n            if (pos >= this.chunk2Pos && pos < this.chunk2Pos + this.chunk2.length) {\n                result = this.chunk2.charCodeAt(pos - this.chunk2Pos);\n            }\n            else {\n                let i = this.rangeIndex, range = this.range;\n                while (range.to <= pos)\n                    range = this.ranges[++i];\n                this.chunk2 = this.input.chunk(this.chunk2Pos = pos);\n                if (pos + this.chunk2.length > range.to)\n                    this.chunk2 = this.chunk2.slice(0, range.to - pos);\n                result = this.chunk2.charCodeAt(0);\n            }\n        }\n        if (pos >= this.token.lookAhead)\n            this.token.lookAhead = pos + 1;\n        return result;\n    }\n    /**\n    Accept a token. By default, the end of the token is set to the\n    current stream position, but you can pass an offset (relative to\n    the stream position) to change that.\n    */\n    acceptToken(token, endOffset = 0) {\n        let end = endOffset ? this.resolveOffset(endOffset, -1) : this.pos;\n        if (end == null || end < this.token.start)\n            throw new RangeError(\"Token end out of bounds\");\n        this.token.value = token;\n        this.token.end = end;\n    }\n    /**\n    Accept a token ending at a specific given position.\n    */\n    acceptTokenTo(token, endPos) {\n        this.token.value = token;\n        this.token.end = endPos;\n    }\n    getChunk() {\n        if (this.pos >= this.chunk2Pos && this.pos < this.chunk2Pos + this.chunk2.length) {\n            let { chunk, chunkPos } = this;\n            this.chunk = this.chunk2;\n            this.chunkPos = this.chunk2Pos;\n            this.chunk2 = chunk;\n            this.chunk2Pos = chunkPos;\n            this.chunkOff = this.pos - this.chunkPos;\n        }\n        else {\n            this.chunk2 = this.chunk;\n            this.chunk2Pos = this.chunkPos;\n            let nextChunk = this.input.chunk(this.pos);\n            let end = this.pos + nextChunk.length;\n            this.chunk = end > this.range.to ? nextChunk.slice(0, this.range.to - this.pos) : nextChunk;\n            this.chunkPos = this.pos;\n            this.chunkOff = 0;\n        }\n    }\n    readNext() {\n        if (this.chunkOff >= this.chunk.length) {\n            this.getChunk();\n            if (this.chunkOff == this.chunk.length)\n                return this.next = -1;\n        }\n        return this.next = this.chunk.charCodeAt(this.chunkOff);\n    }\n    /**\n    Move the stream forward N (defaults to 1) code units. Returns\n    the new value of [`next`](#lr.InputStream.next).\n    */\n    advance(n = 1) {\n        this.chunkOff += n;\n        while (this.pos + n >= this.range.to) {\n            if (this.rangeIndex == this.ranges.length - 1)\n                return this.setDone();\n            n -= this.range.to - this.pos;\n            this.range = this.ranges[++this.rangeIndex];\n            this.pos = this.range.from;\n        }\n        this.pos += n;\n        if (this.pos >= this.token.lookAhead)\n            this.token.lookAhead = this.pos + 1;\n        return this.readNext();\n    }\n    setDone() {\n        this.pos = this.chunkPos = this.end;\n        this.range = this.ranges[this.rangeIndex = this.ranges.length - 1];\n        this.chunk = \"\";\n        return this.next = -1;\n    }\n    /**\n    @internal\n    */\n    reset(pos, token) {\n        if (token) {\n            this.token = token;\n            token.start = pos;\n            token.lookAhead = pos + 1;\n            token.value = token.extended = -1;\n        }\n        else {\n            this.token = nullToken;\n        }\n        if (this.pos != pos) {\n            this.pos = pos;\n            if (pos == this.end) {\n                this.setDone();\n                return this;\n            }\n            while (pos < this.range.from)\n                this.range = this.ranges[--this.rangeIndex];\n            while (pos >= this.range.to)\n                this.range = this.ranges[++this.rangeIndex];\n            if (pos >= this.chunkPos && pos < this.chunkPos + this.chunk.length) {\n                this.chunkOff = pos - this.chunkPos;\n            }\n            else {\n                this.chunk = \"\";\n                this.chunkOff = 0;\n            }\n            this.readNext();\n        }\n        return this;\n    }\n    /**\n    @internal\n    */\n    read(from, to) {\n        if (from >= this.chunkPos && to <= this.chunkPos + this.chunk.length)\n            return this.chunk.slice(from - this.chunkPos, to - this.chunkPos);\n        if (from >= this.chunk2Pos && to <= this.chunk2Pos + this.chunk2.length)\n            return this.chunk2.slice(from - this.chunk2Pos, to - this.chunk2Pos);\n        if (from >= this.range.from && to <= this.range.to)\n            return this.input.read(from, to);\n        let result = \"\";\n        for (let r of this.ranges) {\n            if (r.from >= to)\n                break;\n            if (r.to > from)\n                result += this.input.read(Math.max(r.from, from), Math.min(r.to, to));\n        }\n        return result;\n    }\n}\n/**\n@internal\n*/\nclass TokenGroup {\n    constructor(data, id) {\n        this.data = data;\n        this.id = id;\n    }\n    token(input, stack) {\n        let { parser } = stack.p;\n        readToken(this.data, input, stack, this.id, parser.data, parser.tokenPrecTable);\n    }\n}\nTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\n/**\n@hide\n*/\nclass LocalTokenGroup {\n    constructor(data, precTable, elseToken) {\n        this.precTable = precTable;\n        this.elseToken = elseToken;\n        this.data = typeof data == \"string\" ? decodeArray(data) : data;\n    }\n    token(input, stack) {\n        let start = input.pos, skipped = 0;\n        for (;;) {\n            let atEof = input.next < 0, nextPos = input.resolveOffset(1, 1);\n            readToken(this.data, input, stack, 0, this.data, this.precTable);\n            if (input.token.value > -1)\n                break;\n            if (this.elseToken == null)\n                return;\n            if (!atEof)\n                skipped++;\n            if (nextPos == null)\n                break;\n            input.reset(nextPos, input.token);\n        }\n        if (skipped) {\n            input.reset(start, input.token);\n            input.acceptToken(this.elseToken, skipped);\n        }\n    }\n}\nLocalTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\n/**\n`@external tokens` declarations in the grammar should resolve to\nan instance of this class.\n*/\nclass ExternalTokenizer {\n    /**\n    Create a tokenizer. The first argument is the function that,\n    given an input stream, scans for the types of tokens it\n    recognizes at the stream's position, and calls\n    [`acceptToken`](#lr.InputStream.acceptToken) when it finds\n    one.\n    */\n    constructor(\n    /**\n    @internal\n    */\n    token, options = {}) {\n        this.token = token;\n        this.contextual = !!options.contextual;\n        this.fallback = !!options.fallback;\n        this.extend = !!options.extend;\n    }\n}\n// Tokenizer data is stored a big uint16 array containing, for each\n// state:\n//\n//  - A group bitmask, indicating what token groups are reachable from\n//    this state, so that paths that can only lead to tokens not in\n//    any of the current groups can be cut off early.\n//\n//  - The position of the end of the state's sequence of accepting\n//    tokens\n//\n//  - The number of outgoing edges for the state\n//\n//  - The accepting tokens, as (token id, group mask) pairs\n//\n//  - The outgoing edges, as (start character, end character, state\n//    index) triples, with end character being exclusive\n//\n// This function interprets that data, running through a stream as\n// long as new states with the a matching group mask can be reached,\n// and updating `input.token` when it matches a token.\nfunction readToken(data, input, stack, group, precTable, precOffset) {\n    let state = 0, groupMask = 1 << group, { dialect } = stack.p.parser;\n    scan: for (;;) {\n        if ((groupMask & data[state]) == 0)\n            break;\n        let accEnd = data[state + 1];\n        // Check whether this state can lead to a token in the current group\n        // Accept tokens in this state, possibly overwriting\n        // lower-precedence / shorter tokens\n        for (let i = state + 3; i < accEnd; i += 2)\n            if ((data[i + 1] & groupMask) > 0) {\n                let term = data[i];\n                if (dialect.allows(term) &&\n                    (input.token.value == -1 || input.token.value == term ||\n                        overrides(term, input.token.value, precTable, precOffset))) {\n                    input.acceptToken(term);\n                    break;\n                }\n            }\n        let next = input.next, low = 0, high = data[state + 2];\n        // Special case for EOF\n        if (input.next < 0 && high > low && data[accEnd + high * 3 - 3] == 65535 /* Seq.End */) {\n            state = data[accEnd + high * 3 - 1];\n            continue scan;\n        }\n        // Do a binary search on the state's edges\n        for (; low < high;) {\n            let mid = (low + high) >> 1;\n            let index = accEnd + mid + (mid << 1);\n            let from = data[index], to = data[index + 1] || 0x10000;\n            if (next < from)\n                high = mid;\n            else if (next >= to)\n                low = mid + 1;\n            else {\n                state = data[index + 2];\n                input.advance();\n                continue scan;\n            }\n        }\n        break;\n    }\n}\nfunction findOffset(data, start, term) {\n    for (let i = start, next; (next = data[i]) != 65535 /* Seq.End */; i++)\n        if (next == term)\n            return i - start;\n    return -1;\n}\nfunction overrides(token, prev, tableData, tableOffset) {\n    let iPrev = findOffset(tableData, tableOffset, prev);\n    return iPrev < 0 || findOffset(tableData, tableOffset, token) < iPrev;\n}\n\n// Environment variable used to control console output\nconst verbose = typeof process != \"undefined\" && process.env && /\\bparse\\b/.test(process.env.LOG);\nlet stackIDs = null;\nfunction cutAt(tree, pos, side) {\n    let cursor = tree.cursor(IterMode.IncludeAnonymous);\n    cursor.moveTo(pos);\n    for (;;) {\n        if (!(side < 0 ? cursor.childBefore(pos) : cursor.childAfter(pos)))\n            for (;;) {\n                if ((side < 0 ? cursor.to < pos : cursor.from > pos) && !cursor.type.isError)\n                    return side < 0 ? Math.max(0, Math.min(cursor.to - 1, pos - 25 /* Lookahead.Margin */))\n                        : Math.min(tree.length, Math.max(cursor.from + 1, pos + 25 /* Lookahead.Margin */));\n                if (side < 0 ? cursor.prevSibling() : cursor.nextSibling())\n                    break;\n                if (!cursor.parent())\n                    return side < 0 ? 0 : tree.length;\n            }\n    }\n}\nclass FragmentCursor {\n    constructor(fragments, nodeSet) {\n        this.fragments = fragments;\n        this.nodeSet = nodeSet;\n        this.i = 0;\n        this.fragment = null;\n        this.safeFrom = -1;\n        this.safeTo = -1;\n        this.trees = [];\n        this.start = [];\n        this.index = [];\n        this.nextFragment();\n    }\n    nextFragment() {\n        let fr = this.fragment = this.i == this.fragments.length ? null : this.fragments[this.i++];\n        if (fr) {\n            this.safeFrom = fr.openStart ? cutAt(fr.tree, fr.from + fr.offset, 1) - fr.offset : fr.from;\n            this.safeTo = fr.openEnd ? cutAt(fr.tree, fr.to + fr.offset, -1) - fr.offset : fr.to;\n            while (this.trees.length) {\n                this.trees.pop();\n                this.start.pop();\n                this.index.pop();\n            }\n            this.trees.push(fr.tree);\n            this.start.push(-fr.offset);\n            this.index.push(0);\n            this.nextStart = this.safeFrom;\n        }\n        else {\n            this.nextStart = 1e9;\n        }\n    }\n    // `pos` must be >= any previously given `pos` for this cursor\n    nodeAt(pos) {\n        if (pos < this.nextStart)\n            return null;\n        while (this.fragment && this.safeTo <= pos)\n            this.nextFragment();\n        if (!this.fragment)\n            return null;\n        for (;;) {\n            let last = this.trees.length - 1;\n            if (last < 0) { // End of tree\n                this.nextFragment();\n                return null;\n            }\n            let top = this.trees[last], index = this.index[last];\n            if (index == top.children.length) {\n                this.trees.pop();\n                this.start.pop();\n                this.index.pop();\n                continue;\n            }\n            let next = top.children[index];\n            let start = this.start[last] + top.positions[index];\n            if (start > pos) {\n                this.nextStart = start;\n                return null;\n            }\n            if (next instanceof Tree) {\n                if (start == pos) {\n                    if (start < this.safeFrom)\n                        return null;\n                    let end = start + next.length;\n                    if (end <= this.safeTo) {\n                        let lookAhead = next.prop(NodeProp.lookAhead);\n                        if (!lookAhead || end + lookAhead < this.fragment.to)\n                            return next;\n                    }\n                }\n                this.index[last]++;\n                if (start + next.length >= Math.max(this.safeFrom, pos)) { // Enter this node\n                    this.trees.push(next);\n                    this.start.push(start);\n                    this.index.push(0);\n                }\n            }\n            else {\n                this.index[last]++;\n                this.nextStart = start + next.length;\n            }\n        }\n    }\n}\nclass TokenCache {\n    constructor(parser, stream) {\n        this.stream = stream;\n        this.tokens = [];\n        this.mainToken = null;\n        this.actions = [];\n        this.tokens = parser.tokenizers.map(_ => new CachedToken);\n    }\n    getActions(stack) {\n        let actionIndex = 0;\n        let main = null;\n        let { parser } = stack.p, { tokenizers } = parser;\n        let mask = parser.stateSlot(stack.state, 3 /* ParseState.TokenizerMask */);\n        let context = stack.curContext ? stack.curContext.hash : 0;\n        let lookAhead = 0;\n        for (let i = 0; i < tokenizers.length; i++) {\n            if (((1 << i) & mask) == 0)\n                continue;\n            let tokenizer = tokenizers[i], token = this.tokens[i];\n            if (main && !tokenizer.fallback)\n                continue;\n            if (tokenizer.contextual || token.start != stack.pos || token.mask != mask || token.context != context) {\n                this.updateCachedToken(token, tokenizer, stack);\n                token.mask = mask;\n                token.context = context;\n            }\n            if (token.lookAhead > token.end + 25 /* Lookahead.Margin */)\n                lookAhead = Math.max(token.lookAhead, lookAhead);\n            if (token.value != 0 /* Term.Err */) {\n                let startIndex = actionIndex;\n                if (token.extended > -1)\n                    actionIndex = this.addActions(stack, token.extended, token.end, actionIndex);\n                actionIndex = this.addActions(stack, token.value, token.end, actionIndex);\n                if (!tokenizer.extend) {\n                    main = token;\n                    if (actionIndex > startIndex)\n                        break;\n                }\n            }\n        }\n        while (this.actions.length > actionIndex)\n            this.actions.pop();\n        if (lookAhead)\n            stack.setLookAhead(lookAhead);\n        if (!main && stack.pos == this.stream.end) {\n            main = new CachedToken;\n            main.value = stack.p.parser.eofTerm;\n            main.start = main.end = stack.pos;\n            actionIndex = this.addActions(stack, main.value, main.end, actionIndex);\n        }\n        this.mainToken = main;\n        return this.actions;\n    }\n    getMainToken(stack) {\n        if (this.mainToken)\n            return this.mainToken;\n        let main = new CachedToken, { pos, p } = stack;\n        main.start = pos;\n        main.end = Math.min(pos + 1, p.stream.end);\n        main.value = pos == p.stream.end ? p.parser.eofTerm : 0 /* Term.Err */;\n        return main;\n    }\n    updateCachedToken(token, tokenizer, stack) {\n        let start = this.stream.clipPos(stack.pos);\n        tokenizer.token(this.stream.reset(start, token), stack);\n        if (token.value > -1) {\n            let { parser } = stack.p;\n            for (let i = 0; i < parser.specialized.length; i++)\n                if (parser.specialized[i] == token.value) {\n                    let result = parser.specializers[i](this.stream.read(token.start, token.end), stack);\n                    if (result >= 0 && stack.p.parser.dialect.allows(result >> 1)) {\n                        if ((result & 1) == 0 /* Specialize.Specialize */)\n                            token.value = result >> 1;\n                        else\n                            token.extended = result >> 1;\n                        break;\n                    }\n                }\n        }\n        else {\n            token.value = 0 /* Term.Err */;\n            token.end = this.stream.clipPos(start + 1);\n        }\n    }\n    putAction(action, token, end, index) {\n        // Don't add duplicate actions\n        for (let i = 0; i < index; i += 3)\n            if (this.actions[i] == action)\n                return index;\n        this.actions[index++] = action;\n        this.actions[index++] = token;\n        this.actions[index++] = end;\n        return index;\n    }\n    addActions(stack, token, end, index) {\n        let { state } = stack, { parser } = stack.p, { data } = parser;\n        for (let set = 0; set < 2; set++) {\n            for (let i = parser.stateSlot(state, set ? 2 /* ParseState.Skip */ : 1 /* ParseState.Actions */);; i += 3) {\n                if (data[i] == 65535 /* Seq.End */) {\n                    if (data[i + 1] == 1 /* Seq.Next */) {\n                        i = pair(data, i + 2);\n                    }\n                    else {\n                        if (index == 0 && data[i + 1] == 2 /* Seq.Other */)\n                            index = this.putAction(pair(data, i + 2), token, end, index);\n                        break;\n                    }\n                }\n                if (data[i] == token)\n                    index = this.putAction(pair(data, i + 1), token, end, index);\n            }\n        }\n        return index;\n    }\n}\nclass Parse {\n    constructor(parser, input, fragments, ranges) {\n        this.parser = parser;\n        this.input = input;\n        this.ranges = ranges;\n        this.recovering = 0;\n        this.nextStackID = 0x2654; // ♔, ♕, ♖, ♗, ♘, ♙, ♠, ♡, ♢, ♣, ♤, ♥, ♦, ♧\n        this.minStackPos = 0;\n        this.reused = [];\n        this.stoppedAt = null;\n        this.lastBigReductionStart = -1;\n        this.lastBigReductionSize = 0;\n        this.bigReductionCount = 0;\n        this.stream = new InputStream(input, ranges);\n        this.tokens = new TokenCache(parser, this.stream);\n        this.topTerm = parser.top[1];\n        let { from } = ranges[0];\n        this.stacks = [Stack.start(this, parser.top[0], from)];\n        this.fragments = fragments.length && this.stream.end - from > parser.bufferLength * 4\n            ? new FragmentCursor(fragments, parser.nodeSet) : null;\n    }\n    get parsedPos() {\n        return this.minStackPos;\n    }\n    // Move the parser forward. This will process all parse stacks at\n    // `this.pos` and try to advance them to a further position. If no\n    // stack for such a position is found, it'll start error-recovery.\n    //\n    // When the parse is finished, this will return a syntax tree. When\n    // not, it returns `null`.\n    advance() {\n        let stacks = this.stacks, pos = this.minStackPos;\n        // This will hold stacks beyond `pos`.\n        let newStacks = this.stacks = [];\n        let stopped, stoppedTokens;\n        // If a large amount of reductions happened with the same start\n        // position, force the stack out of that production in order to\n        // avoid creating a tree too deep to recurse through.\n        // (This is an ugly kludge, because unfortunately there is no\n        // straightforward, cheap way to check for this happening, due to\n        // the history of reductions only being available in an\n        // expensive-to-access format in the stack buffers.)\n        if (this.bigReductionCount > 300 /* Rec.MaxLeftAssociativeReductionCount */ && stacks.length == 1) {\n            let [s] = stacks;\n            while (s.forceReduce() && s.stack.length && s.stack[s.stack.length - 2] >= this.lastBigReductionStart) { }\n            this.bigReductionCount = this.lastBigReductionSize = 0;\n        }\n        // Keep advancing any stacks at `pos` until they either move\n        // forward or can't be advanced. Gather stacks that can't be\n        // advanced further in `stopped`.\n        for (let i = 0; i < stacks.length; i++) {\n            let stack = stacks[i];\n            for (;;) {\n                this.tokens.mainToken = null;\n                if (stack.pos > pos) {\n                    newStacks.push(stack);\n                }\n                else if (this.advanceStack(stack, newStacks, stacks)) {\n                    continue;\n                }\n                else {\n                    if (!stopped) {\n                        stopped = [];\n                        stoppedTokens = [];\n                    }\n                    stopped.push(stack);\n                    let tok = this.tokens.getMainToken(stack);\n                    stoppedTokens.push(tok.value, tok.end);\n                }\n                break;\n            }\n        }\n        if (!newStacks.length) {\n            let finished = stopped && findFinished(stopped);\n            if (finished) {\n                if (verbose)\n                    console.log(\"Finish with \" + this.stackID(finished));\n                return this.stackToTree(finished);\n            }\n            if (this.parser.strict) {\n                if (verbose && stopped)\n                    console.log(\"Stuck with token \" + (this.tokens.mainToken ? this.parser.getName(this.tokens.mainToken.value) : \"none\"));\n                throw new SyntaxError(\"No parse at \" + pos);\n            }\n            if (!this.recovering)\n                this.recovering = 5 /* Rec.Distance */;\n        }\n        if (this.recovering && stopped) {\n            let finished = this.stoppedAt != null && stopped[0].pos > this.stoppedAt ? stopped[0]\n                : this.runRecovery(stopped, stoppedTokens, newStacks);\n            if (finished) {\n                if (verbose)\n                    console.log(\"Force-finish \" + this.stackID(finished));\n                return this.stackToTree(finished.forceAll());\n            }\n        }\n        if (this.recovering) {\n            let maxRemaining = this.recovering == 1 ? 1 : this.recovering * 3 /* Rec.MaxRemainingPerStep */;\n            if (newStacks.length > maxRemaining) {\n                newStacks.sort((a, b) => b.score - a.score);\n                while (newStacks.length > maxRemaining)\n                    newStacks.pop();\n            }\n            if (newStacks.some(s => s.reducePos > pos))\n                this.recovering--;\n        }\n        else if (newStacks.length > 1) {\n            // Prune stacks that are in the same state, or that have been\n            // running without splitting for a while, to avoid getting stuck\n            // with multiple successful stacks running endlessly on.\n            outer: for (let i = 0; i < newStacks.length - 1; i++) {\n                let stack = newStacks[i];\n                for (let j = i + 1; j < newStacks.length; j++) {\n                    let other = newStacks[j];\n                    if (stack.sameState(other) ||\n                        stack.buffer.length > 500 /* Rec.MinBufferLengthPrune */ && other.buffer.length > 500 /* Rec.MinBufferLengthPrune */) {\n                        if (((stack.score - other.score) || (stack.buffer.length - other.buffer.length)) > 0) {\n                            newStacks.splice(j--, 1);\n                        }\n                        else {\n                            newStacks.splice(i--, 1);\n                            continue outer;\n                        }\n                    }\n                }\n            }\n            if (newStacks.length > 12 /* Rec.MaxStackCount */)\n                newStacks.splice(12 /* Rec.MaxStackCount */, newStacks.length - 12 /* Rec.MaxStackCount */);\n        }\n        this.minStackPos = newStacks[0].pos;\n        for (let i = 1; i < newStacks.length; i++)\n            if (newStacks[i].pos < this.minStackPos)\n                this.minStackPos = newStacks[i].pos;\n        return null;\n    }\n    stopAt(pos) {\n        if (this.stoppedAt != null && this.stoppedAt < pos)\n            throw new RangeError(\"Can't move stoppedAt forward\");\n        this.stoppedAt = pos;\n    }\n    // Returns an updated version of the given stack, or null if the\n    // stack can't advance normally. When `split` and `stacks` are\n    // given, stacks split off by ambiguous operations will be pushed to\n    // `split`, or added to `stacks` if they move `pos` forward.\n    advanceStack(stack, stacks, split) {\n        let start = stack.pos, { parser } = this;\n        let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n        if (this.stoppedAt != null && start > this.stoppedAt)\n            return stack.forceReduce() ? stack : null;\n        if (this.fragments) {\n            let strictCx = stack.curContext && stack.curContext.tracker.strict, cxHash = strictCx ? stack.curContext.hash : 0;\n            for (let cached = this.fragments.nodeAt(start); cached;) {\n                let match = this.parser.nodeSet.types[cached.type.id] == cached.type ? parser.getGoto(stack.state, cached.type.id) : -1;\n                if (match > -1 && cached.length && (!strictCx || (cached.prop(NodeProp.contextHash) || 0) == cxHash)) {\n                    stack.useNode(cached, match);\n                    if (verbose)\n                        console.log(base + this.stackID(stack) + ` (via reuse of ${parser.getName(cached.type.id)})`);\n                    return true;\n                }\n                if (!(cached instanceof Tree) || cached.children.length == 0 || cached.positions[0] > 0)\n                    break;\n                let inner = cached.children[0];\n                if (inner instanceof Tree && cached.positions[0] == 0)\n                    cached = inner;\n                else\n                    break;\n            }\n        }\n        let defaultReduce = parser.stateSlot(stack.state, 4 /* ParseState.DefaultReduce */);\n        if (defaultReduce > 0) {\n            stack.reduce(defaultReduce);\n            if (verbose)\n                console.log(base + this.stackID(stack) + ` (via always-reduce ${parser.getName(defaultReduce & 65535 /* Action.ValueMask */)})`);\n            return true;\n        }\n        if (stack.stack.length >= 8400 /* Rec.CutDepth */) {\n            while (stack.stack.length > 6000 /* Rec.CutTo */ && stack.forceReduce()) { }\n        }\n        let actions = this.tokens.getActions(stack);\n        for (let i = 0; i < actions.length;) {\n            let action = actions[i++], term = actions[i++], end = actions[i++];\n            let last = i == actions.length || !split;\n            let localStack = last ? stack : stack.split();\n            let main = this.tokens.mainToken;\n            localStack.apply(action, term, main ? main.start : localStack.pos, end);\n            if (verbose)\n                console.log(base + this.stackID(localStack) + ` (via ${(action & 65536 /* Action.ReduceFlag */) == 0 ? \"shift\"\n                    : `reduce of ${parser.getName(action & 65535 /* Action.ValueMask */)}`} for ${parser.getName(term)} @ ${start}${localStack == stack ? \"\" : \", split\"})`);\n            if (last)\n                return true;\n            else if (localStack.pos > start)\n                stacks.push(localStack);\n            else\n                split.push(localStack);\n        }\n        return false;\n    }\n    // Advance a given stack forward as far as it will go. Returns the\n    // (possibly updated) stack if it got stuck, or null if it moved\n    // forward and was given to `pushStackDedup`.\n    advanceFully(stack, newStacks) {\n        let pos = stack.pos;\n        for (;;) {\n            if (!this.advanceStack(stack, null, null))\n                return false;\n            if (stack.pos > pos) {\n                pushStackDedup(stack, newStacks);\n                return true;\n            }\n        }\n    }\n    runRecovery(stacks, tokens, newStacks) {\n        let finished = null, restarted = false;\n        for (let i = 0; i < stacks.length; i++) {\n            let stack = stacks[i], token = tokens[i << 1], tokenEnd = tokens[(i << 1) + 1];\n            let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n            if (stack.deadEnd) {\n                if (restarted)\n                    continue;\n                restarted = true;\n                stack.restart();\n                if (verbose)\n                    console.log(base + this.stackID(stack) + \" (restarted)\");\n                let done = this.advanceFully(stack, newStacks);\n                if (done)\n                    continue;\n            }\n            let force = stack.split(), forceBase = base;\n            for (let j = 0; force.forceReduce() && j < 10 /* Rec.ForceReduceLimit */; j++) {\n                if (verbose)\n                    console.log(forceBase + this.stackID(force) + \" (via force-reduce)\");\n                let done = this.advanceFully(force, newStacks);\n                if (done)\n                    break;\n                if (verbose)\n                    forceBase = this.stackID(force) + \" -> \";\n            }\n            for (let insert of stack.recoverByInsert(token)) {\n                if (verbose)\n                    console.log(base + this.stackID(insert) + \" (via recover-insert)\");\n                this.advanceFully(insert, newStacks);\n            }\n            if (this.stream.end > stack.pos) {\n                if (tokenEnd == stack.pos) {\n                    tokenEnd++;\n                    token = 0 /* Term.Err */;\n                }\n                stack.recoverByDelete(token, tokenEnd);\n                if (verbose)\n                    console.log(base + this.stackID(stack) + ` (via recover-delete ${this.parser.getName(token)})`);\n                pushStackDedup(stack, newStacks);\n            }\n            else if (!finished || finished.score < stack.score) {\n                finished = stack;\n            }\n        }\n        return finished;\n    }\n    // Convert the stack's buffer to a syntax tree.\n    stackToTree(stack) {\n        stack.close();\n        return Tree.build({ buffer: StackBufferCursor.create(stack),\n            nodeSet: this.parser.nodeSet,\n            topID: this.topTerm,\n            maxBufferLength: this.parser.bufferLength,\n            reused: this.reused,\n            start: this.ranges[0].from,\n            length: stack.pos - this.ranges[0].from,\n            minRepeatType: this.parser.minRepeatTerm });\n    }\n    stackID(stack) {\n        let id = (stackIDs || (stackIDs = new WeakMap)).get(stack);\n        if (!id)\n            stackIDs.set(stack, id = String.fromCodePoint(this.nextStackID++));\n        return id + stack;\n    }\n}\nfunction pushStackDedup(stack, newStacks) {\n    for (let i = 0; i < newStacks.length; i++) {\n        let other = newStacks[i];\n        if (other.pos == stack.pos && other.sameState(stack)) {\n            if (newStacks[i].score < stack.score)\n                newStacks[i] = stack;\n            return;\n        }\n    }\n    newStacks.push(stack);\n}\nclass Dialect {\n    constructor(source, flags, disabled) {\n        this.source = source;\n        this.flags = flags;\n        this.disabled = disabled;\n    }\n    allows(term) { return !this.disabled || this.disabled[term] == 0; }\n}\nconst id = x => x;\n/**\nContext trackers are used to track stateful context (such as\nindentation in the Python grammar, or parent elements in the XML\ngrammar) needed by external tokenizers. You declare them in a\ngrammar file as `@context exportName from \"module\"`.\n\nContext values should be immutable, and can be updated (replaced)\non shift or reduce actions.\n\nThe export used in a `@context` declaration should be of this\ntype.\n*/\nclass ContextTracker {\n    /**\n    Define a context tracker.\n    */\n    constructor(spec) {\n        this.start = spec.start;\n        this.shift = spec.shift || id;\n        this.reduce = spec.reduce || id;\n        this.reuse = spec.reuse || id;\n        this.hash = spec.hash || (() => 0);\n        this.strict = spec.strict !== false;\n    }\n}\n/**\nHolds the parse tables for a given grammar, as generated by\n`lezer-generator`, and provides [methods](#common.Parser) to parse\ncontent with.\n*/\nclass LRParser extends Parser {\n    /**\n    @internal\n    */\n    constructor(spec) {\n        super();\n        /**\n        @internal\n        */\n        this.wrappers = [];\n        if (spec.version != 14 /* File.Version */)\n            throw new RangeError(`Parser version (${spec.version}) doesn't match runtime version (${14 /* File.Version */})`);\n        let nodeNames = spec.nodeNames.split(\" \");\n        this.minRepeatTerm = nodeNames.length;\n        for (let i = 0; i < spec.repeatNodeCount; i++)\n            nodeNames.push(\"\");\n        let topTerms = Object.keys(spec.topRules).map(r => spec.topRules[r][1]);\n        let nodeProps = [];\n        for (let i = 0; i < nodeNames.length; i++)\n            nodeProps.push([]);\n        function setProp(nodeID, prop, value) {\n            nodeProps[nodeID].push([prop, prop.deserialize(String(value))]);\n        }\n        if (spec.nodeProps)\n            for (let propSpec of spec.nodeProps) {\n                let prop = propSpec[0];\n                if (typeof prop == \"string\")\n                    prop = NodeProp[prop];\n                for (let i = 1; i < propSpec.length;) {\n                    let next = propSpec[i++];\n                    if (next >= 0) {\n                        setProp(next, prop, propSpec[i++]);\n                    }\n                    else {\n                        let value = propSpec[i + -next];\n                        for (let j = -next; j > 0; j--)\n                            setProp(propSpec[i++], prop, value);\n                        i++;\n                    }\n                }\n            }\n        this.nodeSet = new NodeSet(nodeNames.map((name, i) => NodeType.define({\n            name: i >= this.minRepeatTerm ? undefined : name,\n            id: i,\n            props: nodeProps[i],\n            top: topTerms.indexOf(i) > -1,\n            error: i == 0,\n            skipped: spec.skippedNodes && spec.skippedNodes.indexOf(i) > -1\n        })));\n        if (spec.propSources)\n            this.nodeSet = this.nodeSet.extend(...spec.propSources);\n        this.strict = false;\n        this.bufferLength = DefaultBufferLength;\n        let tokenArray = decodeArray(spec.tokenData);\n        this.context = spec.context;\n        this.specializerSpecs = spec.specialized || [];\n        this.specialized = new Uint16Array(this.specializerSpecs.length);\n        for (let i = 0; i < this.specializerSpecs.length; i++)\n            this.specialized[i] = this.specializerSpecs[i].term;\n        this.specializers = this.specializerSpecs.map(getSpecializer);\n        this.states = decodeArray(spec.states, Uint32Array);\n        this.data = decodeArray(spec.stateData);\n        this.goto = decodeArray(spec.goto);\n        this.maxTerm = spec.maxTerm;\n        this.tokenizers = spec.tokenizers.map(value => typeof value == \"number\" ? new TokenGroup(tokenArray, value) : value);\n        this.topRules = spec.topRules;\n        this.dialects = spec.dialects || {};\n        this.dynamicPrecedences = spec.dynamicPrecedences || null;\n        this.tokenPrecTable = spec.tokenPrec;\n        this.termNames = spec.termNames || null;\n        this.maxNode = this.nodeSet.types.length - 1;\n        this.dialect = this.parseDialect();\n        this.top = this.topRules[Object.keys(this.topRules)[0]];\n    }\n    createParse(input, fragments, ranges) {\n        let parse = new Parse(this, input, fragments, ranges);\n        for (let w of this.wrappers)\n            parse = w(parse, input, fragments, ranges);\n        return parse;\n    }\n    /**\n    Get a goto table entry @internal\n    */\n    getGoto(state, term, loose = false) {\n        let table = this.goto;\n        if (term >= table[0])\n            return -1;\n        for (let pos = table[term + 1];;) {\n            let groupTag = table[pos++], last = groupTag & 1;\n            let target = table[pos++];\n            if (last && loose)\n                return target;\n            for (let end = pos + (groupTag >> 1); pos < end; pos++)\n                if (table[pos] == state)\n                    return target;\n            if (last)\n                return -1;\n        }\n    }\n    /**\n    Check if this state has an action for a given terminal @internal\n    */\n    hasAction(state, terminal) {\n        let data = this.data;\n        for (let set = 0; set < 2; set++) {\n            for (let i = this.stateSlot(state, set ? 2 /* ParseState.Skip */ : 1 /* ParseState.Actions */), next;; i += 3) {\n                if ((next = data[i]) == 65535 /* Seq.End */) {\n                    if (data[i + 1] == 1 /* Seq.Next */)\n                        next = data[i = pair(data, i + 2)];\n                    else if (data[i + 1] == 2 /* Seq.Other */)\n                        return pair(data, i + 2);\n                    else\n                        break;\n                }\n                if (next == terminal || next == 0 /* Term.Err */)\n                    return pair(data, i + 1);\n            }\n        }\n        return 0;\n    }\n    /**\n    @internal\n    */\n    stateSlot(state, slot) {\n        return this.states[(state * 6 /* ParseState.Size */) + slot];\n    }\n    /**\n    @internal\n    */\n    stateFlag(state, flag) {\n        return (this.stateSlot(state, 0 /* ParseState.Flags */) & flag) > 0;\n    }\n    /**\n    @internal\n    */\n    validAction(state, action) {\n        return !!this.allActions(state, a => a == action ? true : null);\n    }\n    /**\n    @internal\n    */\n    allActions(state, action) {\n        let deflt = this.stateSlot(state, 4 /* ParseState.DefaultReduce */);\n        let result = deflt ? action(deflt) : undefined;\n        for (let i = this.stateSlot(state, 1 /* ParseState.Actions */); result == null; i += 3) {\n            if (this.data[i] == 65535 /* Seq.End */) {\n                if (this.data[i + 1] == 1 /* Seq.Next */)\n                    i = pair(this.data, i + 2);\n                else\n                    break;\n            }\n            result = action(pair(this.data, i + 1));\n        }\n        return result;\n    }\n    /**\n    Get the states that can follow this one through shift actions or\n    goto jumps. @internal\n    */\n    nextStates(state) {\n        let result = [];\n        for (let i = this.stateSlot(state, 1 /* ParseState.Actions */);; i += 3) {\n            if (this.data[i] == 65535 /* Seq.End */) {\n                if (this.data[i + 1] == 1 /* Seq.Next */)\n                    i = pair(this.data, i + 2);\n                else\n                    break;\n            }\n            if ((this.data[i + 2] & (65536 /* Action.ReduceFlag */ >> 16)) == 0) {\n                let value = this.data[i + 1];\n                if (!result.some((v, i) => (i & 1) && v == value))\n                    result.push(this.data[i], value);\n            }\n        }\n        return result;\n    }\n    /**\n    Configure the parser. Returns a new parser instance that has the\n    given settings modified. Settings not provided in `config` are\n    kept from the original parser.\n    */\n    configure(config) {\n        // Hideous reflection-based kludge to make it easy to create a\n        // slightly modified copy of a parser.\n        let copy = Object.assign(Object.create(LRParser.prototype), this);\n        if (config.props)\n            copy.nodeSet = this.nodeSet.extend(...config.props);\n        if (config.top) {\n            let info = this.topRules[config.top];\n            if (!info)\n                throw new RangeError(`Invalid top rule name ${config.top}`);\n            copy.top = info;\n        }\n        if (config.tokenizers)\n            copy.tokenizers = this.tokenizers.map(t => {\n                let found = config.tokenizers.find(r => r.from == t);\n                return found ? found.to : t;\n            });\n        if (config.specializers) {\n            copy.specializers = this.specializers.slice();\n            copy.specializerSpecs = this.specializerSpecs.map((s, i) => {\n                let found = config.specializers.find(r => r.from == s.external);\n                if (!found)\n                    return s;\n                let spec = Object.assign(Object.assign({}, s), { external: found.to });\n                copy.specializers[i] = getSpecializer(spec);\n                return spec;\n            });\n        }\n        if (config.contextTracker)\n            copy.context = config.contextTracker;\n        if (config.dialect)\n            copy.dialect = this.parseDialect(config.dialect);\n        if (config.strict != null)\n            copy.strict = config.strict;\n        if (config.wrap)\n            copy.wrappers = copy.wrappers.concat(config.wrap);\n        if (config.bufferLength != null)\n            copy.bufferLength = config.bufferLength;\n        return copy;\n    }\n    /**\n    Tells you whether any [parse wrappers](#lr.ParserConfig.wrap)\n    are registered for this parser.\n    */\n    hasWrappers() {\n        return this.wrappers.length > 0;\n    }\n    /**\n    Returns the name associated with a given term. This will only\n    work for all terms when the parser was generated with the\n    `--names` option. By default, only the names of tagged terms are\n    stored.\n    */\n    getName(term) {\n        return this.termNames ? this.termNames[term] : String(term <= this.maxNode && this.nodeSet.types[term].name || term);\n    }\n    /**\n    The eof term id is always allocated directly after the node\n    types. @internal\n    */\n    get eofTerm() { return this.maxNode + 1; }\n    /**\n    The type of top node produced by the parser.\n    */\n    get topNode() { return this.nodeSet.types[this.top[1]]; }\n    /**\n    @internal\n    */\n    dynamicPrecedence(term) {\n        let prec = this.dynamicPrecedences;\n        return prec == null ? 0 : prec[term] || 0;\n    }\n    /**\n    @internal\n    */\n    parseDialect(dialect) {\n        let values = Object.keys(this.dialects), flags = values.map(() => false);\n        if (dialect)\n            for (let part of dialect.split(\" \")) {\n                let id = values.indexOf(part);\n                if (id >= 0)\n                    flags[id] = true;\n            }\n        let disabled = null;\n        for (let i = 0; i < values.length; i++)\n            if (!flags[i]) {\n                for (let j = this.dialects[values[i]], id; (id = this.data[j++]) != 65535 /* Seq.End */;)\n                    (disabled || (disabled = new Uint8Array(this.maxTerm + 1)))[id] = 1;\n            }\n        return new Dialect(dialect, flags, disabled);\n    }\n    /**\n    Used by the output of the parser generator. Not available to\n    user code. @hide\n    */\n    static deserialize(spec) {\n        return new LRParser(spec);\n    }\n}\nfunction pair(data, off) { return data[off] | (data[off + 1] << 16); }\nfunction findFinished(stacks) {\n    let best = null;\n    for (let stack of stacks) {\n        let stopped = stack.p.stoppedAt;\n        if ((stack.pos == stack.p.stream.end || stopped != null && stack.pos > stopped) &&\n            stack.p.parser.stateFlag(stack.state, 2 /* StateFlag.Accepting */) &&\n            (!best || best.score < stack.score))\n            best = stack;\n    }\n    return best;\n}\nfunction getSpecializer(spec) {\n    if (spec.external) {\n        let mask = spec.extend ? 1 /* Specialize.Extend */ : 0 /* Specialize.Specialize */;\n        return (value, stack) => (spec.external(value, stack) << 1) | mask;\n    }\n    return spec.get;\n}\n\nexport { ContextTracker, ExternalTokenizer, InputStream, LRParser, LocalTokenGroup, Stack };\n", "import { L<PERSON>arser } from '@lezer/lr';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst Json$1 = 1,\n  Logfmt$1 = 2,\n  Unpack$1 = 3,\n  Pattern$1 = 4,\n  Regexp$1 = 5,\n  Unwrap$1 = 6,\n  LabelFormat$1 = 7,\n  LineFormat$1 = 8,\n  LabelReplace$1 = 9,\n  Vector$1 = 10,\n  Offset$1 = 11,\n  Bool$1 = 12,\n  On$1 = 13,\n  Ignoring$1 = 14,\n  GroupLeft$1 = 15,\n  GroupRight$1 = 16,\n  Decolorize$1 = 17,\n  Drop$1 = 18,\n  Keep$1 = 19,\n  By$1 = 20,\n  Without$1 = 21,\n  And$1 = 22,\n  Or$1 = 23,\n  Unless$1 = 24,\n  Sum$1 = 25,\n  Avg$1 = 26,\n  Count$1 = 27,\n  Max$1 = 28,\n  Min$1 = 29,\n  Stddev$1 = 30,\n  Stdvar$1 = 31,\n  Bottomk$1 = 32,\n  Topk$1 = 33,\n  Sort$1 = 34,\n  Sort_Desc$1 = 35;\n\nconst keywordTokens = {\n  json: Json$1,\n  logfmt: Logfmt$1,\n  unpack: Unpack$1,\n  pattern: Pattern$1,\n  regexp: Regexp$1,\n  label_format: LabelFormat$1,\n  line_format: LineFormat$1,\n  label_replace: LabelReplace$1,\n  vector: Vector$1,\n  offset: Offset$1,\n  bool: Bool$1,\n  on: On$1,\n  ignoring: Ignoring$1,\n  group_left: GroupLeft$1,\n  group_right: GroupRight$1,\n  unwrap: Unwrap$1,\n  decolorize: Decolorize$1,\n  drop: Drop$1,\n  keep: Keep$1,\n};\n\nconst specializeIdentifier = (value) => {\n  return keywordTokens[value.toLowerCase()] || -1;\n};\n\nconst contextualKeywordTokens = {\n  by: By$1,\n  without: Without$1,\n  and: And$1,\n  or: Or$1,\n  unless: Unless$1,\n  sum: Sum$1,\n  avg: Avg$1,\n  count: Count$1,\n  max: Max$1,\n  min: Min$1,\n  stddev: Stddev$1,\n  stdvar: Stdvar$1,\n  bottomk: Bottomk$1,\n  topk: Topk$1,\n  sort: Sort$1,\n  sort_desc: Sort_Desc$1,\n};\n\nconst extendIdentifier = (value) => {\n  return contextualKeywordTokens[value.toLowerCase()] || -1;\n};\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst spec_Identifier = {__proto__:null,ip:295, count_over_time:301, rate:303, rate_counter:305, bytes_over_time:307, bytes_rate:309, avg_over_time:311, sum_over_time:313, min_over_time:315, max_over_time:317, stddev_over_time:319, stdvar_over_time:321, quantile_over_time:323, first_over_time:325, last_over_time:327, absent_over_time:329, bytes:335, duration:337, duration_seconds:339};\nconst parser = LRParser.deserialize({\n  version: 14,\n  states: \"EtOYQPOOO#cQPO'#DUOOQO'#ER'#ERO#hQPO'#ERO$}QPO'#DTOYQPO'#DTOOQO'#Ed'#EdO%[QPO'#EcOOQO'#FP'#FPO%aQPO'#FOQ%lQPOOO&mQPO'#F]O&rQPO'#F^OOQO'#Eb'#EbOOQO'#DS'#DSOOQO'#Ee'#EeOOQO'#Ef'#EfOOQO'#Eg'#EgOOQO'#Eh'#EhOOQO'#Ei'#EiOOQO'#Ej'#EjOOQO'#Ek'#EkOOQO'#El'#ElOOQO'#Em'#EmOOQO'#En'#EnOOQO'#Eo'#EoOOQO'#Ep'#EpOOQO'#Eq'#EqOOQO'#Er'#ErOOQO'#Es'#EsO&wQPO'#DWOOQO'#DV'#DVO'VQPO,59pOOQO,5:m,5:mOOQO'#Dc'#DcO'_QPO'#DbO'gQPO'#DaO)lQPO'#D`O*{QPO'#D`OOQO'#D_'#D_O+sQPO,59oO-}QPO,59oO.UQPO,5:|O.]QPO,5:}O.hQPO'#E|O0sQPO,5;jO0zQPO,5;jO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lOYQPO,5;wO3cQPO,5;xO3hQPO,59rO#cQPO,59qOOQO1G/[1G/[OOQO'#Dh'#DhO3mQPO,59|O5^QPO,59|OOQO'#Di'#DiO5cQPO,59{OOQO,59{,59{O5kQPO'#DWO6YQPO'#DlO8PQPO'#DoO9sQPO'#DoOOQO'#Do'#DoOOQO'#Dv'#DvOOQO'#Dt'#DtO+kQPO'#DtO9xQPO,59zO;iQPO'#EVO;nQPO'#EWOOQO'#EZ'#EZO;sQPO'#E[O;xQPO'#E_OOQO,59z,59zOOQO,59y,59yOOQO1G/Z1G/ZOOQO1G0h1G0hO;}QPO'#EtO.`QPO'#EtO<XQPO1G0iO<^QPO1G0iO<cQPO,5;hO=oQPO1G1UO=vQPO1G1UO=}QPO1G1UO>UQPO'#FSO@dQPO'#FRO@nQPO'#FROYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WO@xQPO1G1cOAPQPO1G1dOOQO1G/^1G/^OOQO1G/]1G/]O5cQPO1G/hOAUQPO1G/hOAZQPO'#DjOBzQPO'#DjOOQO1G/g1G/gOCbQPO,59rOCPQPO,5:cOOQO'#Dm'#DmOClQPO,5:WOEcQPO'#DrOOQO'#Dq'#DqOGVQPO,5:_OHvQPO,5:[OOQO,5:Z,5:ZOJgQPO,5:`O+kQPO,5:`O+kQPO,5:`OOQO,5:q,5:qOJuQPO'#EYOOQO'#EX'#EXOJzQPO,5:rOLkQPO'#E^OOQO'#E^'#E^OOQO'#E]'#E]ONbQPO,5:vO!!RQPO'#EaOOQO'#Ea'#EaOOQO'#E`'#E`O!#xQPO,5:yO!%iQPO'#D`O;}QPO,5;`O!%pQPO'#EuO!%uQPO,5;`O!%}QPO,5;`O!&[QPO,5;`O!&iQPO,5;`O!&nQPO7+&TO.`QPO7+&TOOQO'#E}'#E}O!(OQPO1G1SOOQO1G1S1G1SOYQPO7+&pO!(WQPO7+&pO!)hQPO7+&pO!)oQPO7+&pO!)vQQO'#FTOOQO,5;n,5;nO!,UQPO,5;mO!,]QPO,5;mO!-nQPO7+&rO!-uQPO7+&rOOQO7+&r7+&rO!.SQPO7+&rO!.ZQPO7+&rO!/`QPO7+&rO!/pQPO7+&}OOQO7+'O7+'OOOQO7+%S7+%SO!/uQPO7+%SO5cQPO,5:UO!/zQPO,5:UO!0PQPO1G/{OOQO1G/}1G/}OOQO1G0U1G0UOOQO1G0W1G0WOOQO,5:X,5:XO!0UQPO1G/yO!1uQPO,5:^O!1zQPO,5:]OOQO1G/z1G/zO!2PQPO1G/zO!3pQPO,5:tO;nQPO,5:sO;sQPO,5:wO;xQPO,5:zO!3xQPO,5;cO!%uQPO1G0zO!4WQPO1G0zO!4`QPO,5;aO+kQPO,5;cO!4eQPO1G0zO!4oQPO'#EvO!4tQPO1G0zO!4eQPO1G0zO!4|QPO1G0zO!5ZQPO1G0zO!%xQPO1G0zOOQO1G0z1G0zOOQO<<Io<<IoO!5fQPO<<IoO!5kQPO,5;iOOQO7+&n7+&nO!5pQPO<<J[OOQO<<J[<<J[OYQPO<<J[OOQO'#FV'#FVO!5wQPO,5;oOOQO'#FU'#FUOOQO,5;o,5;oOOQO1G1X1G1XO!6PQPO1G1XO!8YQPO<<JiOOQO<<Hn<<HnOOQO1G/p1G/pO!8_QPO1G/pO!8dQPO7+%gOOQO1G/x1G/xOOQO1G/w1G/wOOQO1G0`1G0`OOQO1G0_1G0_OOQO1G0c1G0cOOQO1G0f1G0fOOQO'#Ex'#ExOOQO1G0}1G0}O!8iQPO1G0}OOQO'#Ey'#EyOOQO'#Ez'#EzOOQO'#E{'#E{OOQO7+&f7+&fOOQO1G0{1G0{O!8nQPO1G0}O!9SQPO7+&fOOQO,5;b,5;bO!9[QPO7+&fO!%xQPO7+&fO!9fQPO7+&fO!9qQPOAN?ZOOQO1G1T1G1TO!;RQPOAN?vO!<cQPOAN?vO!<jQQO1G1ZOOQO1G1Z1G1ZOOQO7+&s7+&sO!<rQPOAN@TOOQO7+%[7+%[O!<wQPO<<IRO!<|QPO7+&iO!=RQPO<<JQO!=ZQPO<<JQO!=cQPO'#EwO!=hQPO<<JQOOQOG24uG24uOOQOG25bG25bOOQO1G1[1G1[OOQO7+&u7+&uO!=pQPOG25oOOQOAN>mAN>mO!=uQPO<<JTOOQOAN?lAN?lO!=zQPOAN?lO!>SQPOLD+ZOOQOAN?oAN?oOOQO,5:r,5:rO!>XQPO!$'NuO!>^QPO!)9DaO!>cQPO!.K9{OOQO!4//g!4//gO;nQPO'#EWO!>hQPO'#D`O!?`QPO,59oO!@fQPO'#DTOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO!AqQPO7+&rO!AxQPO7+&rO!BVQPO7+&rO!C_QPO7+&rO!CfQPO7+&rO!B^QPO'#FQ\",\n  stateData: \"!Cs~O$TOStOS~OXZOY[OiWOjWOkWOlWOmWOnWOoWOpWOqWOrWOsWO!vQO!wRO!xRO$UPO$YTO$[_O$]`O$^aO$_bO$`cO$adO$beO$cfO$dgO$ehO$fiO$gjO$hkO$ilO$jmO~O{nO~O!vqO~O!OrO!QrO!WrO!XrO!YrO!ZrOfwXgwXhwX!lwX!nwX!owX!pwX!qwX!wwX!xwX#{wX#|wX#}wX$OwX~O!_vO$RwX$ZwX~P#mO$Y{O~Od|Oe|O$Y}O~Of!QOg!POh!QO!O!UO!l!UO!n!UO!o!UO!p!UO!q!UO!w!RO!x!RO#{!SO#|!SO#}!SO$O!TO~O$Y!VO~O$Y!WO~O|!XO!O!XO!P!XO!Q!XO~O$V!YO$W!ZO~O}!]O$X!_O~Og!`Of!TXh!TX!O!TX!Q!TX!W!TX!X!TX!Y!TX!Z!TX!_!TX!l!TX!n!TX!o!TX!p!TX!q!TX!w!TX!x!TX#{!TX#|!TX#}!TX$O!TX$R!TX$Z!TX$k!TX$V!TX~O!OrO!QrO!WrO!XrO!YrO!ZrO~Of!SXg!SXh!SX!_!SX!l!SX!n!SX!o!SX!p!SX!q!SX!w!SX!x!SX#{!SX#|!SX#}!SX$O!SX$R!SX$Z!SX$k!SX$V!SX~P)WOP!dOQ!cOR!fOS!eOT!eOV!lOW!kOa!mOb!nOc!oO{!bO$Y!iO~O!_vOfwagwahwa!lwa!nwa!owa!pwa!qwa!wwa!xwa#{wa#|wa#}wa$Owa$Rwa$Zwa~P)WOfvXgvXhvX!OvX!lvX!nvX!ovX!pvX!qvX!wvX!xvX#{vX#|vX#}vX$OvX~O$Z!rO~P,|O$Z!sO~P,|O!v!wO$UPO$Y!uO~O$Y!xO~OXZOY[OiWOjWOkWOlWOmWOnWOoWOpWOqWOrWOsWO!wRO!xRO$UPO$YTO$[_O$]`O$^aO$_bO$`cO$adO$beO$cfO$dgO$ehO$fiO$gjO$hkO$ilO$jmO~O!v!yO~P.mO$Y!{O~O[#OO]!|O^!|OX#uPY#uPi#uPj#uPk#uPl#uPm#uPn#uPo#uPp#uPq#uPr#uPs#uP!v#uP!w#uP!x#uP$U#uP$Y#uP$[#uP$]#uP$^#uP$_#uP$`#uP$a#uP$b#uP$c#uP$d#uP$e#uP$f#uP$g#uP$h#uP$i#uP$j#uP~O!v#WO~O}#XO~Og#ZOf!Uah!Ua!O!Ua!Q!Ua!W!Ua!X!Ua!Y!Ua!Z!Ua!_!Ua!l!Ua!n!Ua!o!Ua!p!Ua!q!Ua!w!Ua!x!Ua#{!Ua#|!Ua#}!Ua$O!Ua$R!Ua$Z!Ua$k!Ua$V!Ua~O$Y#[O~O}#]O$X!_O~O|#`O!O#`O!P!XO!Q!XO!l#aO!n#aO!o#aO!p#aO!q#aO~O{#dO!b#bOf!`Xg!`Xh!`X!O!`X!Q!`X!W!`X!X!`X!Y!`X!Z!`X!_!`X!l!`X!n!`X!o!`X!p!`X!q!`X!w!`X!x!`X#{!`X#|!`X#}!`X$O!`X$R!`X$Z!`X$k!`X$V!`X~O{#dOf!cXg!cXh!cX!O!cX!Q!cX!W!cX!X!cX!Y!cX!Z!cX!_!cX!l!cX!n!cX!o!cX!p!cX!q!cX!w!cX!x!cX#{!cX#|!cX#}!cX$O!cX$R!cX$Z!cX$k!cX$V!cX~O}#hO~Of#jOg#kO$V#jOh!Sa!O!Sa!Q!Sa!W!Sa!X!Sa!Y!Sa!Z!Sa!_!Sa!l!Sa!n!Sa!o!Sa!p!Sa!q!Sa!w!Sa!x!Sa#{!Sa#|!Sa#}!Sa$O!Sa$R!Sa$Z!Sa$k!Sa~O}#lO~O{#mO~O{#pO~O{#tO~O!_#xO$k#zO~P)WO$Z$PO~O$V$QO~O{$RO$Z$TO~Of!uXg!uXh!uX!O!uX!l!uX!n!uX!o!uX!p!uX!q!uX!w!uX!x!uX#{!uX#|!uX#}!uX$O!uX$Z!uX~O$V$UO~P<kO$Z$VO~P,|O!v$WO~P.mO$Y$YO~OX#uXY#uXi#uXj#uXk#uXl#uXm#uXn#uXo#uXp#uXq#uXr#uXs#uX!v#uX!w#uX!x#uX$U#uX$Y#uX$[#uX$]#uX$^#uX$_#uX$`#uX$a#uX$b#uX$c#uX$d#uX$e#uX$f#uX$g#uX$h#uX$i#uX$j#uX~O_$[O`$[O~P>ZO]!|O^!|O~P>ZO$V$dO~P,|O$Z$eO~O}$gO~Og$hOf!^Xh!^X!O!^X!Q!^X!W!^X!X!^X!Y!^X!Z!^X!_!^X!l!^X!n!^X!o!^X!p!^X!q!^X!w!^X!x!^X#{!^X#|!^X#}!^X$O!^X$R!^X$Z!^X$k!^X$V!^X~O$Y$iO~O!m$kO!s$lO!vQO!wRO!xRO~O}#XO$X!_O~PCPO{#dO!b$nOf!`ag!`ah!`a!O!`a!Q!`a!W!`a!X!`a!Y!`a!Z!`a!_!`a!l!`a!n!`a!o!`a!p!`a!q!`a!w!`a!x!`a#{!`a#|!`a#}!`a$O!`a$R!`a$Z!`a$k!`a$V!`a~O|$pOf!fXg!fXh!fX!O!fX!Q!fX!W!fX!X!fX!Y!fX!Z!fX!_!fX!l!fX!n!fX!o!fX!p!fX!q!fX!w!fX!x!fX#{!fX#|!fX#}!fX$O!fX$R!fX$V!fX$Z!fX$k!fX~O$V$qOf!gag!gah!ga!O!ga!Q!ga!W!ga!X!ga!Y!ga!Z!ga!_!ga!l!ga!n!ga!o!ga!p!ga!q!ga!w!ga!x!ga#{!ga#|!ga#}!ga$O!ga$R!ga$Z!ga$k!ga~O$V$qOf!dag!dah!da!O!da!Q!da!W!da!X!da!Y!da!Z!da!_!da!l!da!n!da!o!da!p!da!q!da!w!da!x!da#{!da#|!da#}!da$O!da$R!da$Z!da$k!da~Of#jOg#kO$V#jO$Z$rO~O|$tO~O$V$uOf!zag!zah!za!O!za!Q!za!W!za!X!za!Y!za!Z!za!_!za!l!za!n!za!o!za!p!za!q!za!w!za!x!za#{!za#|!za#}!za$O!za$R!za$Z!za$k!za~O|!XO!O!XO!P!XO!Q!XOf#QXg#QXh#QX!W#QX!X#QX!Y#QX!Z#QX!_#QX!l#QX!n#QX!o#QX!p#QX!q#QX!w#QX!x#QX#{#QX#|#QX#}#QX$O#QX$R#QX$V#QX$Z#QX$k#QX~O$V$vOf#Oag#Oah#Oa!O#Oa!Q#Oa!W#Oa!X#Oa!Y#Oa!Z#Oa!_#Oa!l#Oa!n#Oa!o#Oa!p#Oa!q#Oa!w#Oa!x#Oa#{#Oa#|#Oa#}#Oa$O#Oa$R#Oa$Z#Oa$k#Oa~O|!XO!O!XO!P!XO!Q!XOf#TXg#TXh#TX!W#TX!X#TX!Y#TX!Z#TX!_#TX!l#TX!n#TX!o#TX!p#TX!q#TX!w#TX!x#TX#{#TX#|#TX#}#TX$O#TX$R#TX$V#TX$Z#TX$k#TX~O$V$wOf#Rag#Rah#Ra!O#Ra!Q#Ra!W#Ra!X#Ra!Y#Ra!Z#Ra!_#Ra!l#Ra!n#Ra!o#Ra!p#Ra!q#Ra!w#Ra!x#Ra#{#Ra#|#Ra#}#Ra$O#Ra$R#Ra$Z#Ra$k#Ra~OU$xO~P*{O!m${O~O!_$|O$k#zO~OZ%OO!_#xO$Z#ha~P)WO!_#xO$Z%TO$k#zO~P)WO$Z%UO~Od|Oe|Of#Vqg#Vqh#Vq!O#Vq!l#Vq!n#Vq!o#Vq!p#Vq!q#Vq!w#Vq!x#Vq#{#Vq#|#Vq#}#Vq$O#Vq$R#Vq$Z#Vq$V#Vq~O$V%XO$Z%YO~Od|Oe|Of#rqg#rqh#rq!O#rq!l#rq!n#rq!o#rq!p#rq!q#rq!w#rq!x#rq#{#rq#|#rq#}#rq$O#rq$R#rq$Z#rq$V#rq~O$V%]O~P<kO$Z%[O~P,|O#z%^O$Z%aO~OX#uaY#uai#uaj#uak#ual#uam#uan#uao#uap#uaq#uar#uas#ua!v#ua!w#ua!x#ua$U#ua$[#ua$]#ua$^#ua$_#ua$`#ua$a#ua$b#ua$c#ua$d#ua$e#ua$f#ua$g#ua$h#ua$i#ua$j#ua~O$Y$YO~P!*OO_%cO`%cO$Y#ua~P!*OOf!QOh!QO!O!UO!l!UO!n!UO!o!UO!p!UO!q!UO!w!RO!x!RO#{#tq#|#tq#}#tq$O#tq$R#tq$Z#tq~Og#tq~P!,jOf#tqg#tqh#tq~P!,pOg!PO~P!,jO$R#tq$Z#tq~P%lOf#tqg#tqh#tq!O#tq!l#tq!n#tq!o#tq!p#tq!q#tq#{#tq#|#tq#}#tq$O#tq~O!w!RO!x!RO$R#tq$Z#tq~P!.eO}%dO~O$Z%eO~O}%gO~O$Y%hO~O$V$qOf!gig!gih!gi!O!gi!Q!gi!W!gi!X!gi!Y!gi!Z!gi!_!gi!l!gi!n!gi!o!gi!p!gi!q!gi!w!gi!x!gi#{!gi#|!gi#}!gi$O!gi$R!gi$Z!gi$k!gi~O}%iO~O{#dO~Of#jO$V#jOg!hih!hi!O!hi!Q!hi!W!hi!X!hi!Y!hi!Z!hi!_!hi!l!hi!n!hi!o!hi!p!hi!q!hi!w!hi!x!hi#{!hi#|!hi#}!hi$O!hi$R!hi$Z!hi$k!hi~O{%kO}%kO~O{%pO$m%rO$n%sO$o%tO~OZ%OO$Z#hi~O$l%vO~O!_#xO$Z#hi~P)WO!m%yO~O!_$|O$Z#hi~O!_#xO$Z%{O$k#zO~P)WO!_$|O$Z%{O$k#zO~O$Z%}O~O{&OO~O$Z&PO~P,|O$V&RO$Z&SO~O$Y$YOX#uiY#uii#uij#uik#uil#uim#uin#uio#uip#uiq#uir#uis#ui!v#ui!w#ui!x#ui$U#ui$[#ui$]#ui$^#ui$_#ui$`#ui$a#ui$b#ui$c#ui$d#ui$e#ui$f#ui$g#ui$h#ui$i#ui$j#ui~O$V&UO~O$Z&VO~O}&WO~O$Y&XO~Of#jOg#kO$V#jO!_#ki$k#ki$Z#ki~O!_$|O$Z#hq~O!_#xO$Z#hq~P)WOZ%OO!_&[O$Z#hq~Od|Oe|Of#V!Rg#V!Rh#V!R!O#V!R!l#V!R!n#V!R!o#V!R!p#V!R!q#V!R!w#V!R!x#V!R#{#V!R#|#V!R#}#V!R$O#V!R$R#V!R$Z#V!R$V#V!R~Od|Oe|Of#r!Rg#r!Rh#r!R!O#r!R!l#r!R!n#r!R!o#r!R!p#r!R!q#r!R!w#r!R!x#r!R#{#r!R#|#r!R#}#r!R$O#r!R$R#r!R$Z#r!R$V#r!R~O$Z&_O~P,|O#z%^O$Z&aO~O}&bO~O$Z&cO~O{&dO~O!_$|O$Z#hy~OZ%OO$Z#hy~OU$xO~O!_&[O$Z#hy~O$V&gO~O$Z&hO~O!_$|O$Z#h!R~O}&jO~O$V&kO~O}&lO~O$Z&mO~OP!dOQ!cOR!fOS!eOT!eOV&nOW!kOa!mOb!nOc!oO{!bO$Y!iO~O!_&oOfwagwahwa!lwa!nwa!owa!pwa!qwa!wwa!xwa#{wa#|wa#}wa$Owa$Vwa~P)WO!_&oO$VwX~P#mOf&yOh&yO!O&}O!l&}O!n&}O!o&}O!p&}O!q&}O!w&zO!x&zO#{#tq#|#tq#}#tq$O#tq$V#tq~Og#tq~P!@pOf#tqg#tqh#tq~P!@vOg&xO~P!@pOf&yOg&xOh&yO!O&}O!l&}O!n&}O!o&}O!p&}O!q&}O!w&zO!x&zO#{&{O#|&{O#}&{O$O&|O~O$V#tq~P!B^O!w&zO!x&zO$V#tq~P!.eO\",\n  goto: \"1l$RPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPP$S%R%j&Y&]PPPPPP&t'W'h'v(XPPPP(h(p(yP)S)XP)S)S)[)e)S)m*O*O*XPPPPPP*XP*O*bPPP)S)S*{+R)S)S+Y+])S+c+f+l,_,t-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-p-y.^.j/S/V/V/V/Y/i,_/l,_0R0w1Y1c1fPPPPP,_,_[YOT}!{$U%]Q$^#PQ$_#QS$`#R&tQ$a#SQ$b#TQ$c#UQ'O&rQ'P&sQ'Q&uQ'R&vQ'S&wR'T!Vt^O}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wRyTjSOT}!V!{#P#Q#R#S#T#U$U%]S!t{$QQ#}!u]&q&r&s&t&u&v&wRpPQoP^!hv!i#j#k#x$|&oQ#Y!YS#q!n$vT#u!o$wQxSQ#y!tQ$}#|Q%R#}Q%z%QR&p&q[wS!t#|#}%Q&q]!qx#y$}%R%z&piuSx!t#y#|#}$}%Q%R%z&p&qhtSx!t#y#|#}$}%Q%R%z&p&qR!auksSux!t#y#|#}$}%Q%R%z&p&qQ!^sV#^!`#Z$hW![s!`#Z$hR$j#`Q#_!`Q$f#ZR%f$hV!pv#x&oR#c!cQ#f!cQ#g!dR$o#cU#e!c!d#cR%j$qU!jv#x&oQ#i!iQ$r#jQ$s#kR%w$|_!hv!i#j#k#x$|&o_!gv!i#j#k#x$|&ov]OT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wT$m#`#aQ#o!lR&i&nS#n!l&nR%l$uR#s!nQ#r!nR%m$vR#w!oQ#v!oR%n$wj^O#P#Q#R#S#T#U&r&s&t&u&v&wQzTQ!z}Q#V!VQ$X!{Q%Z$UR&Q%]w]OT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wwVOT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wwUOT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wQ!v{Q$O!uR%W$QS#|!t#}W$z#y#{%R%SQ%u$yQ%|%TR&Z%{Q%Q#|Q%u$zQ&]%|R&e&ZQ#{!tS$y#y%RQ%P#|Q%S#}S%x$}%QS&Y%z%|R&f&]R%q$xR%o$xQ!OXQ%V$PQ%[$VQ&^%}R&_&PR$S!xwXOT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wQ#P!PQ#Q!QQ#R!RQ#S!SQ#T!TQ#U!UQ&r&xQ&s&yQ&t&zQ&u&{Q&v&|R&w&}h!}!P!Q!R!S!T!U&x&y&z&{&|&}R$]#OQ$Z!|Q%b$[R&T%cR%_$YQ%`$YR&`&R\",\n  nodeNames: \"⚠ Json Logfmt Unpack Pattern Regexp Unwrap LabelFormat LineFormat LabelReplace Vector Offset Bool On Ignoring GroupLeft GroupRight Decolorize Drop Keep By Without And Or Unless Sum Avg Count Max Min Stddev Stdvar Bottomk Topk Sort Sort_Desc LineComment LogQL Expr LogExpr Selector Matchers Matcher Identifier Eq String Neq Re Nre PipelineExpr PipelineStage LineFilters LineFilter Filter PipeExact PipeMatch PipePattern Npa FilterOp Ip OrFilter Pipe LogfmtParser LogfmtParserFlags ParserFlag LabelParser JsonExpressionParser LabelExtractionExpressionList LabelExtractionExpression LogfmtExpressionParser LabelFilter IpLabelFilter UnitFilter DurationFilter Gtr Duration Gte Lss Lte Eql BytesFilter Bytes NumberFilter LiteralExpr Number Add Sub LineFormatExpr LabelFormatExpr LabelsFormat LabelFormatMatcher DecolorizeExpr DropLabelsExpr DropLabels DropLabel KeepLabelsExpr KeepLabels KeepLabel MetricExpr RangeAggregationExpr RangeOp CountOverTime Rate RateCounter BytesOverTime BytesRate AvgOverTime SumOverTime MinOverTime MaxOverTime StddevOverTime StdvarOverTime QuantileOverTime FirstOverTime LastOverTime AbsentOverTime LogRangeExpr Range OffsetExpr UnwrapExpr ConvOp BytesConv DurationConv DurationSecondsConv Grouping Labels VectorAggregationExpr VectorOp BinOpExpr BinOpModifier OnOrIgnoringModifier GroupingLabels GroupingLabelList GroupingLabel LabelName Mul Div Mod Pow LabelReplaceExpr VectorExpr\",\n  maxTerm: 169,\n  skippedNodes: [0,36],\n  repeatNodeCount: 0,\n  tokenData: \"5r~RvX^#ipq#iqr$^rs$yst%kuv%vxy%{yz&Qz{&V{|&[|}&a}!O&f!O!P(t!P!Q)t!Q!R)y!R![+a![!]2a!^!_2u!_!`3S!`!a3i!c!}3v!}#O4^#P#Q4c#Q#R4h#R#S3v#S#T4m#T#o3v#o#p4y#p#q5O#q#r5m#y#z#i$f$g#i#BY#BZ#i$IS$I_#i$I|$JO#i$JT$JU#i$KV$KW#i&FU&FV#i~#nY$T~X^#ipq#i#y#z#i$f$g#i#BY#BZ#i$IS$I_#i$I|$JO#i$JT$JU#i$KV$KW#i&FU&FV#i~$aR!_!`$j!`!a$o#r#s$t~$oO!O~~$tO!Z~~$yO!Q~~$|UOY$yZr$yrs%`s#O$y#O#P%e#P~$y~%eO}~~%hPO~$y~%pQt~OY%kZ~%k~%{O#}~~&QO$Y~~&VO$Z~~&[O#{~~&aO!w~~&fO$V~~&kP!x~}!O&n~&qQ#_#`&w#g#h(U~&zP#X#Y&}~'QP#X#Y'T~'WP#d#e'Z~'^P}!O'a~'dP#X#Y'g~'jP#a#b'm~'pP#d#e's~'vP#h#i'y~'|P#m#n(P~(UO!b~~(XP#h#i([~(_P#f#g(b~(eP#]#^(h~(kP#V#W(n~(qP#h#i(P~(wP!Q![(z~)PR!v~!Q![(z!g!h)Y#X#Y)Y~)]R{|)f}!O)f!Q![)l~)iP!Q![)l~)qP!v~!Q![)l~)yO#|~~*Oe!v~!O!P(z!Q![+a!g!h,t!i!j-c!m!n-c!o!p-c!r!s-c!v!w-c#U#V-W#W#X-l#X#Y/n#Z#[0Q#[#].T#_#`0Q#a#b0Z#d#e0Q#g#h/]#h#i0Q#k#l0l#l#m1u#m#n1W~+fd!v~!O!P(z!Q![+a!g!h,t!i!j-c!m!n-c!o!p-c!r!s-c!v!w-c#U#V-W#W#X-l#X#Y/n#Z#[0Q#[#].T#_#`0Q#a#b0Z#d#e0Q#g#h/]#h#i0Q#k#l0l#m#n1W~,wT{|)f}!O)f!Q![)l!d!e-W#]#^-]~-]O!s~~-`P#U#V-W~-fQ!d!e-W#]#^-]~-qP!m~!Q![-t~-wS!Q![-t#[#].T#a#b.i#g#h/]~.YP!m~!Q![.]~.`R!Q![.]#a#b.i#g#h/]~.nQ!m~!Q![.t#g#h/W~.wR!Q![.t#a#b/Q#g#h/]~/TP#g#h/W~/]O!m~~/bP!m~!Q![/e~/hQ!Q![/e#a#b/Q~/qT{|)f}!O)f!Q![)l#U#V-W#]#^-]~0TQ#U#V-W#]#^-]~0`S!m~!Q![.t#U#V-W#]#^-]#g#h/W~0qP!m~!Q![0t~0wT!Q![0t#W#X-l#[#].T#a#b.i#g#h/]~1]P!m~!Q![1`~1cU!Q![1`#W#X-l#[#].T#a#b.i#g#h/]#k#l0l~1xR!Q![2R!c!i2R#T#Z2R~2WR!v~!Q![2R!c!i2R#T#Z2RP2fT{P!Q![2a![!]2a!c!}2a#R#S2a#T#o2a~2zP!o~!_!`2}~3SO!p~~3XQ|~!_!`3_#r#s3d~3dO!q~~3iO!P~~3nP!l~!_!`3q~3vO!n~R3}T{P#zQ!Q![3v![!]2a!c!}3v#R#S3v#T#o3v~4cO$k~~4hO$l~~4mO$O~~4pRO#S4m#S#T%`#T~4m~5OO$U~~5TR!_~!_!`5^!`!a5c#r#s5h~5cO!W~~5hO!Y~~5mO!X~~5rO$W~\",\n  tokenizers: [0, 1],\n  topRules: {\"LogQL\":[0,37]},\n  specialized: [{term: 43, get: (value, stack) => (specializeIdentifier(value) << 1)},{term: 43, get: (value, stack) => (extendIdentifier(value) << 1) | 1},{term: 43, get: value => spec_Identifier[value] || -1}],\n  tokenPrec: 0\n});\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst Json = 1,\n  Logfmt = 2,\n  Unpack = 3,\n  Pattern = 4,\n  Regexp = 5,\n  Unwrap = 6,\n  LabelFormat = 7,\n  LineFormat = 8,\n  LabelReplace = 9,\n  Vector = 10,\n  Offset = 11,\n  Bool = 12,\n  On = 13,\n  Ignoring = 14,\n  GroupLeft = 15,\n  GroupRight = 16,\n  Decolorize = 17,\n  Drop = 18,\n  Keep = 19,\n  By = 20,\n  Without = 21,\n  And = 22,\n  Or = 23,\n  Unless = 24,\n  Sum = 25,\n  Avg = 26,\n  Count = 27,\n  Max = 28,\n  Min = 29,\n  Stddev = 30,\n  Stdvar = 31,\n  Bottomk = 32,\n  Topk = 33,\n  Sort = 34,\n  Sort_Desc = 35,\n  LineComment = 36,\n  LogQL = 37,\n  Expr = 38,\n  LogExpr = 39,\n  Selector = 40,\n  Matchers = 41,\n  Matcher = 42,\n  Identifier = 43,\n  Eq = 44,\n  String = 45,\n  Neq = 46,\n  Re = 47,\n  Nre = 48,\n  PipelineExpr = 49,\n  PipelineStage = 50,\n  LineFilters = 51,\n  LineFilter = 52,\n  Filter = 53,\n  PipeExact = 54,\n  PipeMatch = 55,\n  PipePattern = 56,\n  Npa = 57,\n  FilterOp = 58,\n  Ip = 59,\n  OrFilter = 60,\n  Pipe = 61,\n  LogfmtParser = 62,\n  LogfmtParserFlags = 63,\n  ParserFlag = 64,\n  LabelParser = 65,\n  JsonExpressionParser = 66,\n  LabelExtractionExpressionList = 67,\n  LabelExtractionExpression = 68,\n  LogfmtExpressionParser = 69,\n  LabelFilter = 70,\n  IpLabelFilter = 71,\n  UnitFilter = 72,\n  DurationFilter = 73,\n  Gtr = 74,\n  Duration = 75,\n  Gte = 76,\n  Lss = 77,\n  Lte = 78,\n  Eql = 79,\n  BytesFilter = 80,\n  Bytes = 81,\n  NumberFilter = 82,\n  LiteralExpr = 83,\n  Number = 84,\n  Add = 85,\n  Sub = 86,\n  LineFormatExpr = 87,\n  LabelFormatExpr = 88,\n  LabelsFormat = 89,\n  LabelFormatMatcher = 90,\n  DecolorizeExpr = 91,\n  DropLabelsExpr = 92,\n  DropLabels = 93,\n  DropLabel = 94,\n  KeepLabelsExpr = 95,\n  KeepLabels = 96,\n  KeepLabel = 97,\n  MetricExpr = 98,\n  RangeAggregationExpr = 99,\n  RangeOp = 100,\n  CountOverTime = 101,\n  Rate = 102,\n  RateCounter = 103,\n  BytesOverTime = 104,\n  BytesRate = 105,\n  AvgOverTime = 106,\n  SumOverTime = 107,\n  MinOverTime = 108,\n  MaxOverTime = 109,\n  StddevOverTime = 110,\n  StdvarOverTime = 111,\n  QuantileOverTime = 112,\n  FirstOverTime = 113,\n  LastOverTime = 114,\n  AbsentOverTime = 115,\n  LogRangeExpr = 116,\n  Range = 117,\n  OffsetExpr = 118,\n  UnwrapExpr = 119,\n  ConvOp = 120,\n  BytesConv = 121,\n  DurationConv = 122,\n  DurationSecondsConv = 123,\n  Grouping = 124,\n  Labels = 125,\n  VectorAggregationExpr = 126,\n  VectorOp = 127,\n  BinOpExpr = 128,\n  BinOpModifier = 129,\n  OnOrIgnoringModifier = 130,\n  GroupingLabels = 131,\n  GroupingLabelList = 132,\n  GroupingLabel = 133,\n  LabelName = 134,\n  Mul = 135,\n  Div = 136,\n  Mod = 137,\n  Pow = 138,\n  LabelReplaceExpr = 139,\n  VectorExpr = 140;\n\nexport { AbsentOverTime, Add, And, Avg, AvgOverTime, BinOpExpr, BinOpModifier, Bool, Bottomk, By, Bytes, BytesConv, BytesFilter, BytesOverTime, BytesRate, ConvOp, Count, CountOverTime, Decolorize, DecolorizeExpr, Div, Drop, DropLabel, DropLabels, DropLabelsExpr, Duration, DurationConv, DurationFilter, DurationSecondsConv, Eq, Eql, Expr, Filter, FilterOp, FirstOverTime, GroupLeft, GroupRight, Grouping, GroupingLabel, GroupingLabelList, GroupingLabels, Gte, Gtr, Identifier, Ignoring, Ip, IpLabelFilter, Json, JsonExpressionParser, Keep, KeepLabel, KeepLabels, KeepLabelsExpr, LabelExtractionExpression, LabelExtractionExpressionList, LabelFilter, LabelFormat, LabelFormatExpr, LabelFormatMatcher, LabelName, LabelParser, LabelReplace, LabelReplaceExpr, Labels, LabelsFormat, LastOverTime, LineComment, LineFilter, LineFilters, LineFormat, LineFormatExpr, LiteralExpr, LogExpr, LogQL, LogRangeExpr, Logfmt, LogfmtExpressionParser, LogfmtParser, LogfmtParserFlags, Lss, Lte, Matcher, Matchers, Max, MaxOverTime, MetricExpr, Min, MinOverTime, Mod, Mul, Neq, Npa, Nre, Number, NumberFilter, Offset, OffsetExpr, On, OnOrIgnoringModifier, Or, OrFilter, ParserFlag, Pattern, Pipe, PipeExact, PipeMatch, PipePattern, PipelineExpr, PipelineStage, Pow, QuantileOverTime, Range, RangeAggregationExpr, RangeOp, Rate, RateCounter, Re, Regexp, Selector, Sort, Sort_Desc, Stddev, StddevOverTime, Stdvar, StdvarOverTime, String, Sub, Sum, SumOverTime, Topk, UnitFilter, Unless, Unpack, Unwrap, UnwrapExpr, Vector, VectorAggregationExpr, VectorExpr, VectorOp, Without, parser };\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\tloaded: false,\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Flag the module as loaded\n\tmodule.loaded = true;\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.f = {};\n// This file contains only the entry chunk.\n// The chunk loading function for additional chunks\n__webpack_require__.e = (chunkId) => {\n\treturn Promise.all(Object.keys(__webpack_require__.f).reduce((promises, key) => {\n\t\t__webpack_require__.f[key](chunkId, promises);\n\t\treturn promises;\n\t}, []));\n};", "// This function allow to reference async chunks\n__webpack_require__.u = (chunkId) => {\n\t// return url for filenames based on template\n\treturn \"\" + chunkId + \".js\";\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.nmd = (module) => {\n\tmodule.paths = [];\n\tif (!module.children) module.children = [];\n\treturn module;\n};", "__webpack_require__.p = \"public/plugins/grafana-lokiexplore-app/\";", "__webpack_require__.b = document.baseURI || self.location.href;\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t231: 0\n};\n\n__webpack_require__.f.j = (chunkId, promises) => {\n\t\t// JSONP chunk loading for javascript\n\t\tvar installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;\n\t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n\t\t\t// a Promise means \"currently loading\".\n\t\t\tif(installedChunkData) {\n\t\t\t\tpromises.push(installedChunkData[2]);\n\t\t\t} else {\n\t\t\t\tif(true) { // all chunks have JS\n\t\t\t\t\t// setup Promise in chunk cache\n\t\t\t\t\tvar promise = new Promise((resolve, reject) => (installedChunkData = installedChunks[chunkId] = [resolve, reject]));\n\t\t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n\t\t\t\t\t// start chunk loading\n\t\t\t\t\tvar url = __webpack_require__.p + __webpack_require__.u(chunkId);\n\t\t\t\t\t// create error before stack unwound to get useful stacktrace later\n\t\t\t\t\tvar error = new Error();\n\t\t\t\t\tvar loadingEnded = (event) => {\n\t\t\t\t\t\tif(__webpack_require__.o(installedChunks, chunkId)) {\n\t\t\t\t\t\t\tinstalledChunkData = installedChunks[chunkId];\n\t\t\t\t\t\t\tif(installedChunkData !== 0) installedChunks[chunkId] = undefined;\n\t\t\t\t\t\t\tif(installedChunkData) {\n\t\t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n\t\t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n\t\t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n\t\t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n\t\t\t\t\t\t\t\terror.type = errorType;\n\t\t\t\t\t\t\t\terror.request = realSrc;\n\t\t\t\t\t\t\t\tinstalledChunkData[1](error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\t__webpack_require__.l(url, loadingEnded, \"chunk-\" + chunkId, chunkId);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n};\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n// no on chunks loaded\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkgrafana_lokiexplore_app\"] = self[\"webpackChunkgrafana_lokiexplore_app\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(1255);\n"], "names": ["leafPrototypes", "getProto", "inProgress", "dataWebpackPrefix", "App", "lazy", "wasmSupported", "default", "initRuntimeDs", "initChangepoint", "initOutlier", "Promise", "all", "AppConfig", "plugin", "AppPlugin", "setRootPage", "addConfigPage", "title", "icon", "body", "id", "linkConfig", "linkConfigs", "addLink", "description", "ExtensionPoints", "MetricExploration", "targets", "PluginExtensionPoints", "DashboardPanelMenu", "path", "createAppUrl", "configure", "contextToLink", "ExploreToolbarAction", "context", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "find", "target", "datasource", "type", "uid", "expr", "labelFilters", "getMatcherFromQuery", "labelSelector", "selector", "operator", "FilterOp", "Equal", "labelValue", "replaceSlash", "value", "labelName", "key", "SERVICE_NAME", "sort", "a", "b", "params", "setUrlParameter", "UrlParameters", "DatasourceId", "TimeRangeFrom", "timeRange", "from", "valueOf", "toString", "TimeRangeTo", "to", "labelFilter", "LabelType", "Indexed", "appendUrlParameter", "Labels", "urlParams", "pluginJson", "VAR_DATASOURCE", "VAR_LABELS", "Fields", "VAR_FIELDS", "initalParams", "searchParams", "URLSearchParams", "location", "search", "set", "append", "parameter", "replace", "NodePosition", "fromNode", "node", "contains", "position", "this", "getExpression", "query", "substring", "constructor", "syntaxNode", "getNodesFromQuery", "nodeTypes", "nodes", "parser", "parse", "iterate", "enter", "undefined", "includes", "push", "getAllPositionsInNodeByType", "positions", "pos", "child", "childAfter", "filter", "Selector", "length", "selectorPosition", "allMatcher", "Matcher", "matcher", "matcherPosition", "identifierPosition", "Identifier", "valuePosition", "String", "op", "NotEqual", "map", "ErrorId", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType", "isQueryWithNode", "VAR_LABELS_EXPR", "VAR_LABELS_REPLICA", "VAR_LABELS_REPLICA_EXPR", "VAR_FIELDS_EXPR", "PENDING_FIELDS_EXPR", "VAR_METADATA", "VAR_METADATA_EXPR", "VAR_PATTERNS", "VAR_PATTERNS_EXPR", "VAR_LEVELS", "VAR_LEVELS_EXPR", "VAR_FIELD_GROUP_BY", "VAR_LABEL_GROUP_BY", "VAR_LABEL_GROUP_BY_EXPR", "VAR_PRIMARY_LABEL_SEARCH", "VAR_PRIMARY_LABEL", "VAR_PRIMARY_LABEL_EXPR", "VAR_DATASOURCE_EXPR", "MIXED_FORMAT_EXPR", "JSON_FORMAT_EXPR", "LOGS_FORMAT_EXPR", "VAR_LOGS_FORMAT", "VAR_LOGS_FORMAT_EXPR", "VAR_LINE_FILTER", "VAR_LINE_FILTER_EXPR", "LOG_STREAM_SELECTOR_EXPR", "DETECTED_FIELD_VALUES_EXPR", "DETECTED_METADATA_VALUES_EXPR", "DETECTED_LEVELS_VALUES_EXPR", "PATTERNS_SAMPLE_SELECTOR_EXPR", "EXPLORATION_DS", "ALL_VARIABLE_VALUE", "LEVEL_VARIABLE_VALUE", "SERVICE_UI_LABEL", "VAR_AGGREGATED_METRICS", "EMPTY_VARIABLE_VALUE", "module", "exports", "__WEBPACK_EXTERNAL_MODULE__6089__", "__WEBPACK_EXTERNAL_MODULE__7781__", "__WEBPACK_EXTERNAL_MODULE__8531__", "__WEBPACK_EXTERNAL_MODULE__2007__", "__WEBPACK_EXTERNAL_MODULE__3241__", "__WEBPACK_EXTERNAL_MODULE__5959__", "__WEBPACK_EXTERNAL_MODULE__8398__", "__WEBPACK_EXTERNAL_MODULE__200__", "__WEBPACK_EXTERNAL_MODULE__3806__", "__WEBPACK_EXTERNAL_MODULE__7694__", "__WEBPACK_EXTERNAL_MODULE__1269__", "De<PERSON>ult<PERSON><PERSON>er<PERSON><PERSON><PERSON>", "nextPropID", "Range", "NodeProp", "config", "perNode", "deserialize", "Error", "add", "match", "RangeError", "NodeType", "result", "closedBy", "str", "split", "openedBy", "group", "isolate", "contextHash", "lookAhead", "mounted", "MountedTree", "tree", "overlay", "get", "props", "noProps", "Object", "create", "name", "flags", "define", "spec", "top", "skipped", "error", "src", "Array", "isArray", "prop", "isTop", "isSkipped", "isError", "isAnonymous", "is", "indexOf", "direct", "groups", "i", "found", "none", "NodeSet", "types", "extend", "newTypes", "newProps", "source", "assign", "CachedNode", "WeakMap", "CachedInnerNode", "IterMode", "Tree", "children", "ch", "test", "JSON", "stringify", "cursor", "mode", "TreeCursor", "topNode", "cursorAt", "side", "scope", "moveTo", "_tree", "TreeNode", "resolve", "resolveNode", "resolveInner", "resolveStack", "inner", "layers", "scan", "parent", "index", "mount", "root", "iterStack", "stackIterator", "leave", "anon", "IncludeAnonymous", "c", "entered", "<PERSON><PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "<PERSON><PERSON><PERSON><PERSON>", "balance", "balanceRange", "makeTree", "build", "data", "_a", "buffer", "nodeSet", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reused", "minRepeatType", "FlatBufferCursor", "takeNode", "parentStart", "minPos", "inRepeat", "depth", "start", "end", "size", "lookAheadAtStart", "next", "startPos", "maxSize", "fork", "skip", "minStart", "nodeSize", "localSkipped", "nodeStart", "findBufferSize", "Uint16Array", "endPos", "copyToBuffer", "<PERSON><PERSON><PERSON><PERSON>", "localChildren", "localPositions", "localInRepeat", "lastGroup", "lastEnd", "makeRepeatLeaf", "takeFlatNode", "reverse", "make", "last", "lookAheadProp", "lastI", "makeBalanced", "nodeCount", "stopAt", "j", "base", "pop", "pair", "concat", "bufferStart", "startIndex", "topID", "buildTree", "empty", "childString", "join", "endIndex", "<PERSON><PERSON><PERSON><PERSON>", "dir", "pick", "checkSide", "slice", "startI", "endI", "copy", "len", "Math", "max", "overlays", "IgnoreOverlays", "BaseNode", "<PERSON><PERSON><PERSON><PERSON>", "before", "after", "r", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchContext", "matchNodeContext", "enterUnfinishedNodesBefore", "childBefore", "<PERSON><PERSON><PERSON><PERSON>", "prevSibling", "_parent", "super", "<PERSON><PERSON><PERSON><PERSON>", "e", "ExcludeBuffers", "BufferNode", "BufferContext", "<PERSON><PERSON><PERSON><PERSON>", "IgnoreMounts", "rPos", "nextSignificantParent", "val", "toTree", "cur", "p", "externalSibling", "heads", "picked", "newHeads", "splice", "StackIterator", "stack", "bufferNode", "yieldNode", "n", "unshift", "yieldBuf", "yield", "enterChild", "sibling", "d", "atLastNode", "move", "prev", "cache", "mustLeave", "some", "nodeSizeCache", "balanceType", "mkTop", "mkTree", "total", "max<PERSON><PERSON><PERSON>", "ceil", "divide", "offset", "groupFrom", "groupStart", "groupSize", "nextSize", "only", "<PERSON><PERSON><PERSON>", "startParse", "input", "fragments", "ranges", "StringInput", "createParse", "done", "advance", "string", "chunk", "lineChunks", "read", "<PERSON><PERSON>", "state", "reducePos", "score", "bufferBase", "cur<PERSON><PERSON><PERSON><PERSON>", "_", "cx", "StackContext", "pushState", "reduce", "action", "setLookAhead", "dPrec", "dynamicPrecedence", "getGoto", "minRepeatTerm", "storeNode", "reduceContext", "lastBigReductionStart", "bigReductionCount", "lastBigReductionSize", "count", "stateFlag", "baseStateID", "term", "isReduce", "shift", "shiftContext", "maxNode", "nextState", "apply", "nextStart", "nextEnd", "useNode", "updateContext", "tracker", "reuse", "stream", "reset", "off", "recoverByDelete", "isNode", "canShift", "sim", "SimulatedStack", "stateSlot", "hasAction", "recoverByInsert", "nextStates", "best", "s", "v", "forceReduce", "validAction", "backup", "findForcedReduction", "seen", "explore", "allActions", "r<PERSON><PERSON><PERSON>", "forceAll", "deadEnd", "restart", "sameState", "other", "dialectEnabled", "dialectID", "dialect", "emitContext", "hash", "emitLookAhead", "newCx", "close", "strict", "goto", "StackBufferCursor", "maybeNext", "decodeArray", "Type", "array", "out", "charCodeAt", "stop", "digit", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "extended", "mask", "nullToken", "InputStream", "chunkOff", "chunk2", "chunk2Pos", "token", "rangeIndex", "chunkPos", "range", "readNext", "resolveOffset", "assoc", "clipPos", "peek", "idx", "resolved", "acceptToken", "endOffset", "acceptTokenTo", "getChunk", "nextChunk", "setDone", "min", "TokenGroup", "precTable", "precOffset", "groupMask", "accEnd", "allows", "overrides", "low", "high", "mid", "readToken", "tokenPrecTable", "findOffset", "tableData", "tableOffset", "iPrev", "prototype", "contextual", "fallback", "verbose", "process", "env", "LOG", "stackIDs", "cutAt", "fragment", "safeFrom", "safeTo", "trees", "nextFragment", "fr", "openStart", "openEnd", "nodeAt", "TokenCache", "tokens", "mainToken", "actions", "tokenizers", "getActions", "actionIndex", "main", "tokenizer", "updateCachedToken", "addActions", "eofTerm", "getMainToken", "specialized", "specializers", "putAction", "Parse", "recovering", "nextStackID", "minStackPos", "stoppedAt", "topTerm", "stacks", "bufferLength", "parsedPos", "stopped", "stoppedTokens", "newStacks", "advanceStack", "tok", "finished", "findFinished", "console", "log", "stackID", "stackToTree", "getName", "SyntaxError", "runRecovery", "maxRemaining", "outer", "strictCx", "cxHash", "cached", "defaultReduce", "localStack", "advanceFully", "pushStackDedup", "restarted", "tokenEnd", "force", "forceBase", "insert", "fromCodePoint", "Dialect", "disabled", "<PERSON><PERSON><PERSON><PERSON>", "wrappers", "version", "nodeNames", "repeatNodeCount", "topTerms", "keys", "topRules", "nodeProps", "setProp", "nodeID", "propSpec", "skippedNodes", "propSources", "tokenArray", "tokenData", "specializerSpecs", "getSpecializer", "states", "Uint32Array", "stateData", "maxTerm", "dialects", "dynamicPrecedences", "tokenPrec", "termNames", "parseDialect", "w", "loose", "table", "groupTag", "terminal", "slot", "flag", "deflt", "info", "t", "external", "contextTracker", "wrap", "hasWrappers", "prec", "values", "part", "Uint8Array", "keywordTokens", "json", "logfmt", "unpack", "pattern", "regexp", "label_format", "line_format", "label_replace", "vector", "bool", "on", "ignoring", "group_left", "group_right", "unwrap", "decolorize", "drop", "keep", "contextualKeywordTokens", "by", "without", "and", "or", "unless", "sum", "avg", "stddev", "stdvar", "bottomk", "topk", "sort_desc", "spec_Identifier", "__proto__", "ip", "count_over_time", "rate", "rate_counter", "bytes_over_time", "bytes_rate", "avg_over_time", "sum_over_time", "min_over_time", "max_over_time", "stddev_over_time", "stdvar_over_time", "quantile_over_time", "first_over_time", "last_over_time", "absent_over_time", "bytes", "duration", "duration_seconds", "toLowerCase", "specializeIdentifier", "extendIdentifier", "MetricExpr", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "loaded", "__webpack_modules__", "call", "m", "getter", "__esModule", "getPrototypeOf", "obj", "then", "ns", "def", "current", "getOwnPropertyNames", "for<PERSON>ach", "definition", "o", "defineProperty", "enumerable", "f", "chunkId", "promises", "u", "g", "globalThis", "Function", "window", "hasOwnProperty", "l", "url", "script", "<PERSON><PERSON><PERSON><PERSON>", "scripts", "document", "getElementsByTagName", "getAttribute", "createElement", "charset", "timeout", "nc", "setAttribute", "onScriptComplete", "event", "onerror", "onload", "clearTimeout", "doneFns", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "fn", "setTimeout", "bind", "head", "append<PERSON><PERSON><PERSON>", "Symbol", "toStringTag", "nmd", "paths", "baseURI", "self", "href", "installedChunks", "installedChunkData", "promise", "reject", "errorType", "realSrc", "message", "request", "webpackJsonpCallback", "parentChunkLoadingFunction", "chunkIds", "moreModules", "runtime", "chunkLoadingGlobal"], "sourceRoot": ""}