{"version": 3, "file": "module.js", "mappings": ";6MACIA,EADAC,ECAAC,EACAC,E,sECEJ,IACE,KAAiB,QACb,QAAkBC,MAAM,EAAG,QAAkBC,YAAY,KAAO,GAChE,0C,2CCAN,MAAMC,GAA4BC,EAAAA,EAAAA,MAAK,IAAM,+BACvCC,GAA0BD,EAAAA,EAAAA,MAAK,IAAM,iF,wUCM3C,MAAME,GAAMF,EAAAA,EAAAA,MAAK,kBACf,MAAM,cAAEG,SAAwB,yEAExBC,QAASC,SAAwB,yEACjCD,QAASE,SAA0B,+BACnCF,QAASG,SAAsB,8BAQvC,OANAF,IAEIF,YACIK,QAAQC,IAAI,CAACH,IAAmBC,OAGjC,4BACT,EAdiB,IAgBXG,GAAYV,EAAAA,EAAAA,MAAK,kBACrB,aAAa,gFACf,EAFuB,IAIVW,GAAS,IAAIC,EAAAA,WAAgBC,YAAYX,GAAKY,cAAc,CACvEC,KAAML,EACNM,KAAM,MACNC,GAAI,gBACJC,MAAO,kBAGT,IAAK,MAAMC,KAAcC,EAAAA,GACvBT,EAAOU,QAAQF,GAGjBR,EAAOW,gBAAgB,CACrBC,UDpCK,SAA4CC,GACjD,OACE,kBAACC,EAAAA,SAAQA,CACPC,SACE,kBAACC,EAAAA,WAAUA,CAACC,QAAQ,YAAYC,UAAAA,GAAS,2BAK3C,kBAAC9B,EAA8ByB,GAGrC,ECyBEM,YAAa,6DACbb,GAAI,yDACJC,MAAO,kCAGTP,EAAOW,gBAAgB,CACrBC,UD7BK,SAA0CC,GAC/C,OACE,kBAACC,EAAAA,SAAQA,CAACC,SAAU,kBAACK,MAAAA,KAAI,8BACvB,kBAAC9B,EAA4BuB,GAGnC,ECwBEM,YAAa,mGACbb,GAAI,uDACJC,MAAO,6B,0CCvDF,MAAMc,EAAiB,WACjBC,EAAuB,gB,qDCA7B,eAAKC,G,+EAAAA,C,CAAL,C,IAOA,WAAKC,G,mHAAAA,C,CAAL,C,IASA,WAAKC,G,uCAAAA,C,CAAL,C,sOCoBP,MAAMC,EAAe,yBACfnB,EAAQ,WAAWmB,IACnBP,EAAc,6BAA6BO,SAGpCC,EAAkB,CAC7BC,oBAAqB,4CAKVnB,EAA2B,CACtC,CACEoB,QAAS,CACPC,EAAAA,sBAAsBC,mBACtBD,EAAAA,sBAAsBE,qBACtB,yDACA,gDAEFzB,QACAY,cACAd,KAlBS,UAmBT4B,KAAMC,IACNC,UAgHJ,SAA8DC,G,IAMlBC,EAL1C,IAAKD,EACH,OAEF,MAAMC,EAAYD,EAAQP,QAAQS,KAAMC,I,IAAWA,E,MAA4B,UAAX,QAAjBA,EAAAA,EAAOC,kBAAPD,IAAAA,OAAAA,EAAAA,EAAmBE,QAChEC,GAAcC,EAAAA,EAAAA,kBACdC,EAAgBF,EAAYG,QAAQR,SAAqB,QAArBA,EAAAA,EAAWG,kBAAXH,IAAAA,OAAAA,EAAAA,EAAuBS,IAAKV,EAAQW,YAE9E,IAAKV,IAAcO,EACjB,OAIF,KAAKP,aAAAA,EAAAA,EAAWW,MACd,MAAO,CAAEf,KAAMC,KAGjB,MAAMc,EAAON,EAAYG,QAAQR,EAAUW,KAAMZ,EAAQW,WAAYE,IAC/D,OAAEC,EAAM,aAAEC,EAAY,YAAEC,EAAW,eAAEC,IAAmBC,EAAAA,EAAAA,IAAoBN,EAAMZ,EAASC,GAC3FkB,EAAgBJ,EAAab,KAAMkB,IAAaC,EAAAA,EAAAA,IAAoBD,EAASE,WAInF,IAAKH,EACH,MAAO,CACLtB,KAAMC,KAKV,MACMyB,EAAaC,EADGL,EAAcM,MAAMC,MAAM,KAAK,IAErD,IAAIC,EAAYR,EAAcS,MAAQC,EAAAA,GAAe,UAAYV,EAAcS,IAE/Eb,EAAae,KAAMC,GAAOA,EAAEH,MAAQD,GAAa,EAAI,GAErD,IAAIK,EAASC,EAAgBC,EAAcC,aAAc3B,EAAe,IAAI4B,iBAC5EJ,EAASC,EAAgBC,EAAcG,cAAerC,EAAQsC,UAAUC,KAAKC,UAAUC,WAAYT,GACnGA,EAASC,EAAgBC,EAAcQ,YAAa1C,EAAQsC,UAAUK,GAAGH,UAAUC,WAAYT,GAC/FA,EAASY,EAA6B7B,EAAciB,GAEhDhB,IACFgB,EAvEJ,SAAgChB,EAA+BgB,GAC7D,IAAK,MAAMa,KAAc7B,EACvBgB,EAASc,EACPZ,EAAca,YACd,GAAGF,EAAWjB,OAAOoB,EAAoBH,EAAWvB,aAAa0B,EAC/DC,EAAgBJ,EAAWpB,UAE7BO,GAGJ,OAAOA,CACT,CA4DakB,CAAuBlC,EAAagB,KAE3ClB,aAAAA,EAAAA,EAAQqC,UACVnB,EA5HJ,SAAsClB,EAAuBkB,GAC3D,IAAK,MAAMoB,KAAStC,EAClB,GAAIsC,EAAM/C,OAASgD,EAAAA,EAAUC,mBAEzBtB,EADEoB,EAAMxB,MAAQ2B,EAAAA,GACPT,EACPZ,EAAcsB,OACd,GAAGJ,EAAMxB,OAAOwB,EAAM9B,YAAY0B,EAAoBC,EAAgBG,EAAM3B,UAC5EO,GAGOc,EACPZ,EAAcuB,SACd,GAAGL,EAAMxB,OAAOwB,EAAM9B,YAAY0B,EAChCU,EAAqBN,EAAM3B,WACxBuB,EAAoBW,EAAmBP,EAAM3B,UAClDO,OAGC,CACL,MAAM4B,EAA8B,CAClCnC,MAAO2B,EAAM3B,MACboC,OAAQT,EAAMS,QAGVC,EAAuB,GAAGV,EAAMxB,OAAOwB,EAAM9B,YAAY0B,EAC7DU,EAAqBK,KAAKC,UAAUJ,QACjCK,EAA0BL,EAAWnC,SAE1CO,EAASc,EAAmBZ,EAAcgC,OAAQJ,EAAsB9B,EAC1E,CAEF,OAAOA,CACT,CA4FamC,CAA6BrD,EAAQkB,KAE5Cf,aAAAA,EAAAA,EAAgBkC,UAClBnB,EAhEG,SAAkCf,EAAqCe,GAC5E,MAAMoC,EAA6B,GAEnC,IAAK,MAAMhB,KAASnC,EAClBmD,EAASC,KAAK,CACZhE,KAAM+C,EAAM9B,WAAagD,EAAAA,GAAgBC,MAAQ,UAAY,UAC7DC,QAASvB,EAAgBG,EAAM3B,SAInC,IAAIgD,GAAiBC,EAAAA,EAAAA,GAAqBN,GAG1C,OADApC,EAASc,EAAmBZ,EAAcyC,SAAUZ,KAAKC,UAAUI,GAAWpC,GACvEc,EAAmBZ,EAAc0C,iBAAkBH,EAAgBzC,EAC5E,CAkDa6C,CAAyB5D,EAAgBe,IAGpD,MAAO,CACLnC,KAAMC,EAAa,YAAY6B,KAAaJ,SAAmBS,GAEnE,IAlKA,SAASiB,EAAgBxB,GACvB,OAAKA,GACIqD,EAAAA,EAGX,CAGO,SAASnB,EAAmBlC,GACjC,OAAOA,aAAAA,EAAAA,EAAOhB,QAAQ,QAAS,KACjC,CAEO,SAASiD,EAAqBjC,GACnC,OAAKA,GAKEsD,EAAAA,EAAAA,IAA8BpB,EAAmBlC,IAJ/CqD,EAAAA,EAKX,CAEO,SAASb,EAA0BxC,GACxC,OAAKA,EAIEuB,EAAoBW,EAAmBlC,IAHrCqD,EAAAA,EAIX,CAoCA,SAASlC,EAA6B7B,EAAoCiB,GACxE,IAAK,MAAMgD,KAAejE,EAAc,CAEtC,GAAIiE,EAAY3E,OAASgD,EAAAA,EAAU4B,QACjC,SAGF,MAAMC,EAA6B,GAAGF,EAAYpD,OAAOoD,EAAY1D,YAAY0B,EAC/EU,EAAqBsB,EAAYvD,WAC9BuB,EAAoBW,EAAmBqB,EAAYvD,UAExDO,EAASc,EAAmBZ,EAAciD,OAAQD,EAA4BlD,EAChF,CACA,OAAOA,CACT,CAuFO,SAASlC,EAAaD,EAAO,WAAYuF,GAC9C,MAAO,MAAMC,EAAAA,KAAgBxF,IAAOuF,EAAY,IAAIA,EAAU3C,aAAe,IAC/E,CAEO,MAAMP,EAAgB,CAC3BC,aAAc,OAAOmD,EAAAA,KACrBjD,cAAe,OACfK,YAAa,KACbyC,OAAQ,OAAOI,EAAAA,KACfrB,OAAQ,OAAOsB,EAAAA,KACf/B,SAAU,OAAOgC,EAAAA,KACjBjC,OAAQ,OAAOkC,EAAAA,KACf3C,YAAa,OAAO4C,EAAAA,KACpBhB,SAAUiB,EAAAA,GACVhB,iBAAkB,OAAOgB,EAAAA,MAIpB,SAAS3D,EAAgBL,EAAuBH,EAAeoE,G,IAC3BA,EAAzC,MAAMC,EAAe,IAAI1D,gBAAsC,QAAtByD,EAAAA,aAAAA,EAAAA,EAAcpD,kBAAdoD,IAAAA,EAAAA,EAA4BE,EAAAA,gBAAgBC,aAGrF,OAFAF,EAAaG,IAAIrE,EAAKH,GAEfqE,CACT,CAEO,SAAShD,EACdlB,EACAH,EACAoE,GAEA,MAAMK,EAAWH,EAAAA,gBAAgBI,c,IACQN,EAAzC,MAAMC,EAAe,IAAI1D,gBAAsC,QAAtByD,EAAAA,aAAAA,EAAAA,EAAcpD,kBAAdoD,IAAAA,EAAAA,EAA4BK,EAASE,QAG9E,OAFAN,EAAaO,OAAOzE,EAAKH,GAElBqE,CACT,CAEO,SAAStE,EAAa8E,GAC3B,OACEC,EAAAA,EAAAA,IAAgCD,GAE7B7F,QAAQ,MAAO,KACfA,QAAQ,MAAO,IAEtB,CAqBO,SAASuC,EAAoBvB,GAClC,OAnBF,SAAkCA,GAChC,OAAIA,QACK,GAIF,KAAK+E,OAAO/F,SAASgB,EAAO,UACrC,CAYSgF,CAVF,SAAiChF,GACtC,OAAIA,QACK,GAIO,MAAM+E,OAAO/F,SAASgB,EAAO,UAC/C,CAGkCiF,CAAwBjF,GAC1D,CAGO,SAASZ,EAAqBY,EAA2BkF,GAE9D,IAAKA,EAASC,QAAUD,EAASE,WAC/B,OAAOpF,EAGT,GAAqB,iBAAVA,EACT,OAAOqF,EAAAA,EAAAA,GAAgCrF,GAIzC,OADsBsF,EAAAA,EAAAA,KAAUtF,EAAOuF,EAAAA,GAClBC,KAAK,IAC5B,C,iBC5SO,SAASH,EAAgCvF,GAC9C,OAAOA,EAAWd,QAAQ,MAAO,QAAQA,QAAQ,MAAO,OAAOA,QAAQ,KAAM,MAC/E,CAGO,SAASuG,EAA0BvF,GACxC,MAAqB,iBAAVA,EACFA,EAAMhB,QAAQ,MAAO,YAAYA,QAAQ,qBAAsB,UAEjEgB,CACT,C,0DCnBO,eAAK4B,G,2DAAAA,C,CAAL,C,oNCKA,eAAK6D,G,0EAAAA,C,CAAL,C,IAOA,WAAKC,G,mBAAAA,C,CAAL,C,IAIA,WAAKC,G,+CAAAA,C,CAAL,C,IAMA,MAAMC,E,kUAAW,IAAKH,EAAkBE,GA4BxC,eAAKE,G,2EAAAA,C,CAAL,C,IAOA,WAAKhD,G,yCAAAA,C,CAAL,C,IAKA,WAAKiD,G,2EAAAA,C,CAAL,C,ugBCxDP,MAAMC,EAAiB,CACrBC,IAAKpC,EAAAA,GACLqC,Q,UAGWC,EAAS,CACpBC,MAAO,CAACC,EAAsB7H,KAC5B,MAAM8H,EAAM,KAAKN,EAAmBxH,GACpC+H,QAAQH,MAAMC,EAAKC,GACnBE,EAAeH,EAAKC,IAEtBG,KAAM,CAACC,EAAalI,KAClB,MAAM8H,EAAM,KAAKN,EAAmBxH,GAEpCmI,EAAgBD,EAAKJ,IAEvBM,KAAM,CAACF,EAAalI,KAClB,MAAM8H,EAAM,KAAKN,EAAmBxH,GACpC+H,QAAQK,KAAKF,EAAKJ,GAClBO,EAAgBH,EAAKJ,KAInBK,EAAkB,CAACD,EAAalI,KACpC,KACEsI,EAAAA,EAAAA,SAAQJ,EAAKlI,EACf,CAAE,MAAOuI,GACPR,QAAQK,KAAK,4BACf,GAGIC,EAAkB,CAACH,EAAalI,KACpC,KACEwI,EAAAA,EAAAA,YAAWN,EAAKlI,EAClB,CAAE,MAAOuI,GACPR,QAAQK,KAAK,8BAA+B,CAAEpI,UAASkI,OACzD,GAgCF,MAAMF,EAAiB,CAACH,EAAmCY,KACzD,IAAIzI,EAAUyI,EACd,KA3BF,SAAmCZ,EAA2B7H,GAC5D,GAAmB,iBAAR6H,GAA4B,OAARA,KACzBa,EAAAA,EAAAA,IAASb,IACXc,OAAOC,KAAKf,GAAKgB,QAASjH,IACxB,MAAMH,EAAQoG,EAAIjG,GACG,iBAAVH,GAAuC,kBAAVA,GAAwC,iBAAVA,IACpEzB,EAAQ4B,GAAOH,EAAMgB,cAKvBqG,EAAQjB,IACV,GAAwB,iBAAbA,EAAIkB,MAAkC,OAAblB,EAAIkB,KACtC,IACE/I,EAAQ+I,KAAOhF,KAAKC,UAAU6D,EAAIkB,KACpC,CAAE,MAAOR,GAET,KAC6B,iBAAbV,EAAIkB,MAAyC,kBAAblB,EAAIkB,MAA0C,iBAAblB,EAAIkB,OACrF/I,EAAQ+I,KAAOlB,EAAIkB,KAAKtG,WAIhC,CAKIuG,CAA0BnB,EAAK7H,GAE3B6H,aAAeoB,OACjBC,EAAAA,EAAAA,UAASrB,EAAK7H,GACU,iBAAR6H,GAChBqB,EAAAA,EAAAA,UAAS,IAAID,MAAMpB,GAAM7H,GAChB6H,GAAsB,iBAARA,EACnB7H,EAAQkI,KACVgB,EAAAA,EAAAA,UAAS,IAAID,MAAMjJ,EAAQkI,KAAMlI,IAEjCkJ,EAAAA,EAAAA,UAAS,IAAID,MAAM,gBAAiBjJ,IAGtCkJ,EAAAA,EAAAA,UAAS,IAAID,MAAM,iBAAkBjJ,EAEzC,CAAE,MAAOuI,GACPR,QAAQH,MAAM,4BAA6B,CAAE5H,UAAS6H,OACxD,GAGIiB,EAAWrH,GACR,SAAUA,C,yNClDZ,MAAM0H,EAaX,eAAOC,CAASC,GACd,OAAO,IAAIF,EAAaE,EAAK9G,KAAM8G,EAAK1G,GAAI0G,EAAMA,EAAKhJ,KACzD,CAEAiJ,QAAAA,CAASC,GACP,OAAOC,KAAKjH,MAAQgH,EAAShH,MAAQiH,KAAK7G,IAAM4G,EAAS5G,EAC3D,CAEA8G,aAAAA,CAAcC,GACZ,OAAOA,EAAMC,UAAUH,KAAKjH,KAAMiH,KAAK7G,GACzC,CAjBA,WAAAiH,CAAYrH,EAAcI,EAAYkH,EAAyBxJ,GAL/DkC,EAAAA,KAAAA,YAAAA,GACAI,EAAAA,KAAAA,UAAAA,GACAtC,EAAAA,KAAAA,YAAAA,GACAwJ,EAAAA,KAAAA,kBAAAA,GAGEL,KAAKjH,KAAOA,EACZiH,KAAK7G,GAAKA,EACV6G,KAAKnJ,KAAOA,EACZmJ,KAAKK,WAAaA,CACpB,EAeK,SAASC,EAAkBJ,EAAeK,GAC/C,MAAMC,EAAsB,GAS5B,OARmBnG,EAAAA,GAAOoG,MAAMP,GAC3BQ,QAAQ,CACXC,MAAQd,UACYe,IAAdL,GAA2BA,EAAUM,SAAShB,EAAKhJ,KAAKnC,MAC1D8L,EAAM3F,KAAKgF,EAAKA,SAIfW,CACT,CAEA,SAASM,EAA4BjB,EAAkBhJ,GACrD,GAAIgJ,EAAKhJ,KAAKnC,KAAOmC,EACnB,MAAO,CAAC8I,EAAaC,SAASC,IAGhC,MAAMkB,EAA4B,GAClC,IAAIC,EAAM,EACNC,EAAQpB,EAAKqB,WAAWF,GAC5B,KAAOC,GACLF,EAAUlG,QAAQiG,EAA4BG,EAAOpK,IACrDmK,EAAMC,EAAM9H,GACZ8H,EAAQpB,EAAKqB,WAAWF,GAE1B,OAAOD,CACT,CAsCA,SAASI,EACPC,EACAC,EACA7J,EACA8J,EACAxJ,GAEA,MAAMyJ,EAAkBzJ,IAAagG,EAAAA,GAAa0D,OAAS1J,IAAagG,EAAAA,GAAa2D,cAC/EC,EAAoBN,EAAgBP,SAAS,SAAWU,EAI9D,GAAoB,MAAhBF,GAAuBE,EAAiB,CAE1C,MAAMI,EAAsB,IAAIC,OAAO,OAAQ,KAC/CR,EAAkBA,EAAgBnK,QAAQ0K,EAAqB,KACjE,MAAO,GAAoB,MAAhBN,EAAqB,CAE9B,MAAMQ,EAA2B,IAAID,OAAO,QAAU,KACtDR,EAAkBA,EAAgBnK,QAAQ4K,EAA0B,KACpE,MAAMF,EAAsB,IAAIC,OAAO,OAAQ,KAC/CR,EAAkBA,EAAgBnK,QAAQ0K,EAAqB,KACjE,CAeA,OAbID,IAEFN,EAAkBA,EAAgBnK,QAAQ,OAAQ,KAGpDO,EAAYqD,KAAK,CACfzC,IAAKsJ,EACD3D,EAAAA,GAAwB+D,gBAAgB7I,WACxC8E,EAAAA,GAAwBgE,cAAc9I,WAAa,IAAMqI,EAAMrI,WACnEnB,SAAUA,EACVG,MAAOmJ,IAGFA,CACT,CAEA,SAASY,EAAoBZ,EAAyB3J,EAAqCK,GACzF,MAAM+J,EAA2B,IAAID,OAAO,MAAO,KACnDR,EAAkBA,EAAgBnK,QAAQ4K,EAA0B,KACpEpK,EAAeoD,KAAK,CAClB/C,WACAG,MAAOmJ,GAEX,CAuDA,SAASa,EAAwBC,GAC/B,OAAIpB,EAA4BoB,EAASC,EAAAA,IAAKxI,OACrCyI,EAAAA,GAAeC,IACbvB,EAA4BoB,EAASI,EAAAA,IAAK3I,OAC5CyI,EAAAA,GAAeG,GACbzB,EAA4BoB,EAASM,EAAAA,IAAK7I,OAC5CyI,EAAAA,GAAeK,IACb3B,EAA4BoB,EAASQ,EAAAA,IAAK/I,OAC5CyI,EAAAA,GAAeO,QAGxBpE,QAAQK,KAAK,2BAGf,CAEA,SAASgE,EAAuBV,GAC9B,OAAIpB,EAA4BoB,EAASW,EAAAA,IAAIlJ,OACpCyI,EAAAA,GAAeU,MACbhC,EAA4BoB,EAASa,EAAAA,IAAKpJ,OAC5CyI,EAAAA,GAAeY,SACblC,EAA4BoB,EAASe,EAAAA,IAAItJ,OAC3CyI,EAAAA,GAAec,WACbpC,EAA4BoB,EAASiB,EAAAA,IAAKxJ,OAC5CyI,EAAAA,GAAegB,mBADjB,CAKT,CAwFO,SAAS1L,EACdwI,EACA1J,EACAC,GAOA,MAAM4M,EAA+B,GAC/BzL,EAAW0I,EAAkBJ,EAAO,CAACoD,EAAAA,KAE3C,GAAwB,IAApB1L,EAAS+B,OACX,MAAO,CAAEpC,aAAc8L,GAIzB,MACM9L,EAjRR,SAA2B2I,GACzB,MAAMqD,EAAgC,GAEhCC,EAAalD,EAAkBJ,EAAO,CAACuD,EAAAA,KAC7C,IAAK,MAAMvB,KAAWsB,EAAY,CAChC,MAAME,EAAqB5C,EAA4BoB,EAASyB,EAAAA,IAChE,IAAKD,GAAoD,IAA9BA,EAAmB/J,OAC5C,SAGF,MAAMiK,EAAgB9C,EAA4BoB,EAAS2B,EAAAA,IACrD/L,EAAWoI,EAAMC,UAAUuD,EAAmB,GAAGvK,GAAIyK,EAAc,GAAG7K,MACtEX,EAAMsL,EAAmB,GAAGzD,cAAcC,GAC1CjI,EAAQ2L,EAAcE,IAAK/D,GAAaG,EAAMC,UAAUJ,EAAShH,KAAO,EAAGgH,EAAS5G,GAAK,IAAI,GAGhGf,GACAH,IACAH,IAAasK,EAAAA,GAAeY,UAC3BlL,IAAasK,EAAAA,GAAeU,OAC5BhL,IAAasK,EAAAA,GAAec,YAC5BpL,IAAasK,EAAAA,GAAegB,gBAKhCG,EAAQ1I,KAAK,CACXzC,MACAN,WACAjB,KAAMgD,EAAAA,EAAU4B,QAChBxD,SAEJ,CACA,OAAOsL,CACT,CA+OuBQ,CADCjD,EAA4BlJ,EAAS,GAAI0L,EAAAA,IAAU,GAAGrD,cAAcC,IAEpF5I,EA1GR,SAAqB4I,EAAe1J,EAAuCC,G,IAEvDD,EADlB,MAAMc,EAAwB,GACxB0M,EAAYxN,SAAa,QAAbA,EAAAA,EAAS+I,YAAT/I,IAAAA,OAAAA,EAAAA,EAAeyN,OAAOvN,KAAMwN,GAAUA,EAAMC,SAAU1N,aAAAA,EAAAA,EAAW0N,QAE7EC,EAAY9D,EAAkBJ,EAAO,CAACmE,EAAAA,KAC5C,IAAK,MAAMnC,KAAWkC,EAAW,C,IAsBbE,EArBlB,MACMC,EADW5E,EAAaC,SAASsC,GACXjC,cAAcC,GAI1C,GAHqBgC,EAAQsC,SAASH,EAAAA,IAIpC,SAIF,GAAmC,cAA/BE,EAAWpE,UAAU,EAAG,GAC1B,SAKF,MAAMsE,EAAenE,EAAkBJ,EAAMC,UAAU,EAAG+B,EAAQrC,KAAK1G,IAAK,CAACuL,EAAAA,KACvEC,EAAarE,EAAkBJ,EAAMC,UAAU,EAAG+B,EAAQrC,KAAK1G,IAAK,CAACyL,EAAAA,KAIrEC,EAA4B,QAAhBP,EADIxD,EAA4BoB,EAASyB,EAAAA,IAC3B,UAAdW,IAAAA,OAAAA,EAAAA,EAAkBrE,cAAcC,GAG5C4E,EAAmBhE,EAA4BoB,EAAS2B,EAAAA,IACxDkB,EAAmBjE,EAA4BoB,EAAS8C,EAAAA,IACxDC,EAAkBnE,EAA4BoB,EAASgD,EAAAA,IACvDC,EAAqBrE,EAA4BoB,EAASkD,EAAAA,IAEhE,IAAIhL,EAAoBtC,EAmBpBuN,EAlBJ,GAAIP,EAAiBnL,OACnB7B,EAAW8K,EAAuBV,GAElC9H,EAAa8F,EAAMC,UAAU2E,EAAiB,GAAG/L,KAAO,EAAG+L,EAAiB,GAAG3L,GAAK,QAC/E,GAAI4L,EAAiBpL,OAC1BS,EAAa2K,EAAiB,GAAG9E,cAAcC,GAC/CpI,EAAWmK,EAAwBC,QAC9B,GAAIiD,EAAmBxL,OAC5B7B,EAAWmK,EAAwBC,GACnC9H,EAAa+K,EAAmB,GAAGlF,cAAcC,OAC5C,KAAI+E,EAAgBtL,OAIzB,SAHA7B,EAAWmK,EAAwBC,GACnC9H,EAAa6K,EAAgB,GAAGhF,cAAcC,EAGhD,C,IAOcoF,EAGd,GANItB,IAGFqB,EAA6CrB,QAAjCsB,GAAAA,EAAAA,EAAAA,GAAsBT,EAAWb,UAAjCsB,IAAAA,EAAAA,OAA+C1E,GAGzD9I,EAAU,CACZ,IAAIuC,EACAoK,EAAa9K,QAAUgL,EAAWhL,OACpCU,EAAS,QACAoK,EAAa9K,OACtBU,EAAS,SACAsK,EAAWhL,OACpBU,EAAS,OAGTgL,EAAYxL,EAAAA,EAAUC,mBAGxBxC,EAAOuD,KAAK,CACVzC,IAAKyM,EACL/M,SAAUA,EACVuC,SACAxD,KAAMwO,QAAAA,EAAaxL,EAAAA,EAAU0L,OAC7BtN,MAAOmC,GAEX,CACF,CACA,OAAO9C,CACT,CAsBiBkO,CAAYtF,EAAO1J,EAASC,IACrC,YAAEe,EAAW,eAAEC,GA9LvB,SAA0ByI,GACxB,MAAM1I,EAAgC,GAChCC,EAAsC,GACtCgO,EAAiBnF,EAAkBJ,EAAO,CAACwF,EAAAA,KACjD,IAAK,MAAOpE,EAAOY,KAAYuD,EAAeE,UAAW,CACvD,MAAMC,EAAQ9E,EAA4BoB,EAAS2D,EAAAA,IAC7CC,EAAahF,EAA4BoB,EAAS6D,EAAAA,IAClDC,EAAWlF,EAA4BoB,EAASa,EAAAA,IAChDkD,EAAiBnF,EAA4BoB,EAASiB,EAAAA,IACtD+C,EAAiBpF,EAA4BoB,EAASiE,EAAAA,IACtDC,EAAiBtF,EAA4BoB,EAASmE,EAAAA,IAEtDC,EAAuBC,EAAyBrE,GAEtD,IAAK,MAAMsE,KAAuBF,EAAsB,CACtD,MAAMjF,EAAcnB,EAAMC,WAAUqG,aAAAA,EAAAA,EAAqBzN,MAAO,EAAGyN,aAAAA,EAAAA,EAAqBzN,MAGxF,IAAIqI,EAAkBlB,EAAMC,WAAUqG,aAAAA,EAAAA,EAAqBzN,MAAO,GAAGyN,aAAAA,EAAAA,EAAqBrN,IAAK,GAE/F,GAAIiI,EAAgBzH,OAAQ,CAC1B,IAAI7B,EACJ,GAAI8N,EAAMjM,OACR7B,EAAWgG,EAAAA,GAAa/C,WACnB,GAAIiL,EAASrM,OAClB7B,EAAWgG,EAAAA,GAAa2I,mBACnB,GAAIR,EAAetM,OACxB7B,EAAWgG,EAAAA,GAAa2D,mBACnB,GAAIqE,EAAWnM,OACpB7B,EAAWgG,EAAAA,GAAa0D,WACnB,GAAI0E,EAAevM,OACxB7B,EAAWgD,EAAAA,GAAgBC,UACtB,KAAIqL,EAAezM,OAEnB,CACL4E,QAAQK,KAAK,sBAAuB,CAClCsB,MAAOA,EAAMC,UAAU+B,EAAQnJ,KAAMmJ,EAAQ/I,MAG/C,QACF,CAPErB,EAAWgD,EAAAA,GAAgB2L,aAO7B,CAEM3O,IAAagD,EAAAA,GAAgBC,OAASjD,IAAagD,EAAAA,GAAgB2L,cACvEtF,EAAuBC,EAAiBC,EAAa7J,EAAa8J,EAAOxJ,GAEzEkK,EAAoBZ,EAAiB3J,EAAgBK,EAEzD,CACF,CACF,CACA,MAAO,CAAEN,cAAaC,iBACxB,CA2I0CiP,CAAiBxG,GAEzD,MAAO,CAAE5I,SAAQC,eAAcC,cAAaC,iBAC9C,CAqBO,MAAMkP,EAAU,EAChB,SAASC,EAAa1G,GAC3B,OAA2C,IArBtC,SAAyBA,EAAe2G,GAC7C,IAAIC,GAAkB,EAUtB,OATazM,EAAAA,GAAOoG,MAAMP,GACrBQ,QAAQ,CACXC,MAAO,EAAG9J,WACR,GAAIA,EAAKnC,KAAOmS,EAEd,OADAC,GAAkB,GACX,KAINA,CACT,CASSA,CAAgB5G,EAAOyG,EAChC,CAEA,SAASJ,EAAyBlD,GAChC,MAAM7C,EAAsB,GAC5B,IAAIX,EAA0BwD,EAC9B,EAAG,CACD,MAAM0D,EAASlH,EAAK2E,SAASX,EAAAA,IACzBkD,IAAWlH,EAAK2E,SAAS3G,EAAAA,KAC3B2C,EAAM3F,KAAKkM,GAEblH,EAAOA,EAAK2E,SAASwC,EAAAA,GACvB,OAAiB,MAARnH,GAET,OAAOW,CACT,C,wDC7ZO,WAAKyG,G,+DAAAA,C,CAAL,C,IA6BA,SAAS3B,EAAsB4B,EAAkBhD,EAAkB5C,EAAQ,G,IAC9D4C,EAAlB,MAAMiD,EAAwD,QAA5CjD,EAAAA,EAAM5M,OAAOZ,KAAMkD,GAAyB,eAAfA,EAAMwN,aAAnClD,IAAAA,OAAAA,EAAAA,EAA2DmD,OAAO/F,GACpF,IAAK6F,EACH,OAAO,KAET,OAAQA,EAAUD,IAChB,IAAK,IACH,OAAOrN,EAAAA,EAAU4B,QACnB,IAAK,IACH,OAAO5B,EAAAA,EAAUC,mBACnB,IAAK,IACH,OAAOD,EAAAA,EAAU0L,OACnB,QACE,OAAO,KAEb,C,wOCzCA,MAAM+B,EAASC,GAAyC,iBAANA,GAAwB,OAANA,EAE7D,SAASC,EAA+BjI,EAAckI,GAC3D,OAAOA,KAAQlI,CACjB,CAEA,MAAMmI,EAAYC,GAA6B,iBAANA,GAAkBA,GAAM,GAEpDzI,EAAY0I,GAAgE,iBAARA,EAE1E,SAASC,EAAiBtP,GAC/B,IAAIuP,EAAoB,GACxB,GAAIC,MAAMC,QAAQzP,GAChB,IAAK,IAAI0P,EAAI,EAAGA,EAAI1P,EAAEoB,OAAQsO,IAC5BH,EAAQjN,KAAK6M,EAASnP,EAAE0P,KAG5B,OAAOH,CACT,CAEO,SAASI,EAAuBX,GACrC,MAAMY,EAAWb,EAAMC,IAAMC,EAAQD,EAAG,QAAUC,EAAQD,EAAG,OAASA,EAEtE,GAAIY,EAAU,CACZ,MAAMC,EAA8B,iBAAjBD,EAASC,KAAoBD,EAASC,IACnD1T,EAA4B,iBAAhByT,EAASzT,IAAmByT,EAASzT,GACvD,IAAW,IAAPA,IAAwB,IAAR0T,EAClB,MAAO,CAAE1T,KAAI0T,MAEjB,CAEA,OAAO,CACT,CAEO,SAASC,EAA4Bd,GAC1C,MAAoB,iBAANA,IAAyB,SAANA,GAAsB,UAANA,GAAuB,SAANA,IAAiBA,CACrF,CACO,SAASe,EAAoBf,GAClC,MAAiB,iBAANA,GAAkBA,IAAMgB,EAAAA,cAAcC,UAAUvP,WAClDsP,EAAAA,cAAcC,UAGN,iBAANjB,GAAkBA,IAAMgB,EAAAA,cAAcE,WAAWxP,YACnDsP,EAAAA,cAAcE,UAIzB,CAEO,SAASC,EAAiBnB,GAC/B,MAAMY,EAAWb,EAAMC,IAAMC,EAAQD,EAAG,UAAYC,EAAQD,EAAG,WAAaA,EAE5E,GAAIY,EAAU,CACZ,MAAM9N,EACuB,iBAApB8N,EAAS9N,SACK,WAApB8N,EAAS9N,QACY,SAApB8N,EAAS9N,QACW,UAApB8N,EAAS9N,QACW,uBAApB8N,EAAS9N,SACX8N,EAAS9N,OACLpC,EAAkC,iBAAnBkQ,EAASlQ,OAAsBkQ,EAASlQ,MAE7D,IAAe,IAAXoC,IAA8B,IAAVpC,EACtB,MAAO,CAAEoC,SAAQpC,QAErB,CAEA,OAAO,CACT,CAEO,SAAS0Q,EAAyBpB,GACvC,MAAMY,EAAWb,EAAMC,IAAMrI,EAASqI,IAAMA,EAE5C,GAAIY,EAAU,CACZ,MAAM/I,EAAOD,OAAOC,KAAK+I,GACnBS,EAAuC,CAAC,EAC9C,IAAK,IAAIX,EAAI,EAAGA,EAAI7I,EAAKzF,OAAQsO,IAAK,CACpC,MAAM7P,EAAMgH,EAAK6I,GACXhQ,EAAQkQ,EAAS/I,EAAK6I,IACP,iBAAVhQ,IACT2Q,EAAaxQ,GAAOH,EAExB,CAEA,OAAO2Q,CACT,CAEA,OAAO,CACT,CAEO,SAASC,EAAgBC,GAC9B,MAAMC,EAAQzB,EAAMwB,IAAiBtB,EAAQsB,EAAc,OAAStB,EAAQsB,EAAc,SAAWA,EACrG,GAAIC,EAAO,CACT,MAAM5P,EAAKuO,EAASqB,EAAM5P,IACpBJ,EAAO2O,EAASqB,EAAMhQ,MAC5B,GAAII,GAAMJ,EACR,MAAO,CAAEA,OAAMI,KAEnB,CAGF,CAEO,SAAS6P,EAAmBjK,GACjC,MAAML,EAAM4I,EAAMvI,IAAMyI,EAAQzI,EAAG,UAAY2I,EAAS3I,EAAEX,OAC1D,GAAIM,EACF,OAAOA,CAGX,CAEO,SAASuK,EAAqBC,GACnC,OAAQA,GACN,KAAKxL,EAAAA,GAAcoF,MACnB,KAAKpF,EAAAA,GAAcsF,SACnB,KAAKtF,EAAAA,GAAcwF,WACnB,KAAKxF,EAAAA,GAAc0F,cACnB,KAAKxF,EAAAA,GAAgB+E,GACrB,KAAK/E,EAAAA,GAAgB6E,IACrB,KAAK7E,EAAAA,GAAgB2E,GACrB,KAAK3E,EAAAA,GAAgByE,IACnB,OAAO6G,EACT,QACE,MAAM,IAAIC,EAAe,wBAE/B,CAYO,SAASC,EAAsBC,GACpC,OAAOC,EAAeD,IAAUE,EAAgBF,EAClD,CAEO,SAASE,EAAgBF,GAC9B,OAAQA,IAAUxT,EAAAA,GAAW+D,OAASyP,IAAUxT,EAAAA,GAAW2T,QAAUH,CACvE,CAEO,SAASC,EAAeD,GAI7B,MAHqB,iBAAVA,IACTA,EAAQA,EAAMI,gBAGbJ,IAAUzT,EAAAA,GAAU0B,QACnB+R,IAAUzT,EAAAA,GAAU8T,QACpBL,IAAUzT,EAAAA,GAAU+T,MACpBN,IAAUzT,EAAAA,GAAUgF,WACtByO,CAEJ,CAEO,SAASO,EAAqCtN,GACnD,OAAOyL,MAAMC,QAAQ1L,EAAa5G,EAAAA,KAChC4G,EAAa5G,EAAAA,GAAsB,IACc,iBAA1C4G,EAAa5G,EAAAA,GAAsB,GACxC4G,EAAa5G,EAAAA,GAAsB,GACW,iBAAvC4G,EAAa5G,EAAAA,IAAsC4G,EAAa5G,EAAAA,EAC7E,CAEO,SAASmU,EAA+BvN,GAC7C,OAAO8M,EACLrB,MAAMC,QAAQ1L,EAAa7G,EAAAA,IAAmB6G,EAAa7G,EAAAA,GAAgB,GAAK6G,EAAa7G,EAAAA,GAEjG,CAEO,SAASqU,EAAkCC,GAChD,GAAIzC,EAAMyC,IAAYvC,EAAQuC,EAAS,SAAWvC,EAAQuC,EAAS,QAAS,CAG1E,MAAO,CACLC,KAHWtC,EAASqC,EAAQC,MAI5B5C,KAHWM,EAASqC,EAAQ3C,MAKhC,CACA,OAAO,CACT,CAEO,MAAM+B,UAAuB1J,O,qFC/L7B,MAAM5H,EAAuBqR,GAC3BA,IAAOrL,EAAAA,GAASiF,OAASoG,IAAOrL,EAAAA,GAASqF,WAErC+G,EAAuBf,GAC3BA,IAAOrL,EAAAA,GAASmF,UAAYkG,IAAOrL,EAAAA,GAASuF,cAExC8G,EAAmBhB,GACvBA,IAAOrL,EAAAA,GAASqF,YAAcgG,IAAOrL,EAAAA,GAASuF,cAE1C+G,EAAqBjB,GACzBkB,EAAAA,GAAqBvJ,SAASqI,E,+FCVhC,SAASmB,EAAuBnB,GACrC,GAAIA,IAAOrL,EAAAA,GAASmF,SAClB,MAAO,YAET,GAAIkG,IAAOrL,EAAAA,GAASuF,cAClB,MAAO,uBAET,GAAI8F,IAAOrL,EAAAA,GAASiF,MAClB,MAAO,SAET,GAAIoG,IAAOrL,EAAAA,GAASqF,WAClB,MAAO,gBAET,GAAIgG,IAAOrL,EAAAA,GAAS0E,GAClB,MAAO,YAET,GAAI2G,IAAOrL,EAAAA,GAAS8E,GAClB,MAAO,eAET,GAAIuG,IAAOrL,EAAAA,GAAS4E,IAClB,MAAO,2BAET,GAAIyG,IAAOrL,EAAAA,GAASwE,IAClB,MAAO,wBAGT,MAAMjE,EAAQ,IAAIqB,MAAM,qBAExB,MADAtB,EAAAA,EAAOC,MAAMA,EAAO,CAAEM,IAAK,mBAAoB5G,SAAUoR,IACnD9K,CACR,CC3BO,MAAMkM,EAAY,CAACzM,EAAAA,GAASiF,MAAOjF,EAAAA,GAASmF,SAAUnF,EAAAA,GAASqF,WAAYrF,EAAAA,GAASuF,eAAeU,IAExG,CAAC7L,EAAOqJ,EAAOiJ,KACR,CACLhV,YAAa8U,EAAuBpS,GACpCuR,MAAOvR,EACPA,WAISuS,EAAmB,CAAC3M,EAAAA,GAASiF,MAAOjF,EAAAA,GAASqF,YAAYY,IAA8B7L,IAAW,CAC7G1C,YAAa8U,EAAuBpS,GACpCuR,MAAOvR,EACPA,WAGWmS,EAAuB,CAACvM,EAAAA,GAAS8E,GAAI9E,EAAAA,GAAS4E,IAAK5E,EAAAA,GAAS0E,GAAI1E,EAAAA,GAASwE,KAEzEoI,EAAmBL,EAAqBtG,IAA8B7L,IAAW,CAC5F1C,YAAa8U,EAAuBpS,GACpCuR,MAAOvR,EACPA,WAGWyS,EAAyC,CACpD,CAAElB,MAAO,QAASvR,MAAO6F,EAAAA,GAAa/C,OACtC,CAAEyO,MAAO,gBAAiBvR,MAAO6F,EAAAA,GAAa2I,eAC9C,CAAE+C,MAAO,QAASvR,MAAO6F,EAAAA,GAAa0D,OACtC,CAAEgI,MAAO,gBAAiBvR,MAAO6F,EAAAA,GAAa2D,e,gDC7BzC,SAASvG,EAAqBN,GACnC,MACM+P,EADkB/P,EAASyI,OAAQrI,GAA6B,YAAjBA,EAAQnE,MAE1DiN,IAAK8G,GAAM,QAAOtN,EAAAA,EAAAA,GAAgCsN,EAAE5P,aACpDyC,KAAK,KACLoN,OAEGC,EAAkBlQ,EAASyI,OAAQrI,GAA6B,YAAjBA,EAAQnE,MAC7D,IAAIkU,EAAsB,GAU1B,OATID,EAAgBnR,OAAS,IAEzBoR,EAD6B,IAA3BD,EAAgBnR,OACI,QAAO2D,EAAAA,EAAAA,GAAgCwN,EAAgB,GAAG9P,YAE1D,MAAM8P,EACzBhH,IAAK8G,GAAM,KAAItN,EAAAA,EAAAA,GAAgCsN,EAAE5P,aACjDyC,KAAK,WAGL,GAAGkN,KAAuBI,IAAsBF,MACzD,C,2dCSO,MAAM9O,EAAa,UACbiP,EAAkB,aAClBC,EAAqB,kBACrBC,EAA0B,qBAC1BlP,EAAa,SACbmP,EAAkB,YAClBC,EAAsB,mBACtBC,EAAwB,qBACxBC,EAA0B,aAC1BrP,EAAe,WACfsP,EAAoB,cACpBnP,EAAe,WACfoP,EAAoB,cACpBtP,EAAa,SACbuP,EAAkB,YAClBC,EAAqB,UACrBC,EAAqB,UACrBC,EAA0B,aAC1BC,EAA2B,uBAE3BC,EAAoB,gBACpBC,EAAyB,mBACzBjQ,EAAiB,KACjBkQ,EAAsB,QACtBC,EAAkB,aAClBC,EAAuB,gBAEvBC,EAAkB,aAClBC,EAAuB,gBAIvBC,EAAoB,UAAUH,iDAC9BI,EAAmB,UAAUJ,wCAE7BK,EAAmB,WAEnBC,EAAkB,aAClBC,EAAuB,gBAIvBC,EAAkB,eAGlBvQ,EAAmB,cACnBwQ,EAAwB,iBACxBC,EAA2B,IAAI5B,MAAoBS,KAAmBF,KAAqBC,KAAqBmB,YAAgCT,kDAAqEf,KAAmBiB,IAExOS,EAA6B,IAAI7B,MAAoBS,KAAmBF,KAAqBC,KAAqBmB,KAAyBN,KAAqBlB,IAChK2B,EAA0C,IAAI9B,MAAoBS,KAAmBJ,KAAyBG,KAAqBmB,KAnB/F,UAAUT,oDAmBmJd,IAEjM2B,EAA8B,IAAI/B,MAAoBI,KAAuBG,KAAqBC,KAAqBmB,KAAyBF,KAAwBtB,IACxK6B,EAAgC,IAAIhC,MAAoBO,KAAqBC,KAAqBiB,IAClGQ,EAAkC,GAAGjC,KAAmBS,KAAmBF,KAAqBC,KAAqBmB,KAAyBxB,IAC9I+B,EAAiB,CAAEhW,IAAK8U,GACxBmB,EAAqB,SACrBpT,EAAuB,iBACvB1B,EAAe,eACf+U,EAAmB,UACnBC,EAAyB,yBAEzB/R,EAAuB,KAIvBgS,EAAgC,UACtC,SAASvQ,EAAgC9E,EAAQ,IACtD,OAAIA,EAAMsV,WAAWD,GACZrV,EAAMkI,UAAUmN,EAA8B3T,QAEhD1B,CACT,CACO,SAASuV,EAA4BvV,EAAQ,IAClD,OAAOA,EAAMsV,WAAWD,EAC1B,CACO,SAAS/R,GAA8BtD,EAAQ,IACpD,OAAOqV,EAAgCrV,CACzC,C,WC9GAwV,EAAOC,QAAUC,C,WCAjBF,EAAOC,QAAUE,C,WCAjBH,EAAOC,QAAUG,C,WCAjBJ,EAAOC,QAAUI,C,WCAjBL,EAAOC,QAAUK,C,WCAjBN,EAAOC,QAAUM,C,WCAjBP,EAAOC,QAAUO,C,WCAjBR,EAAOC,QAAUQ,C,UCAjBT,EAAOC,QAAUS,C,WCAjBV,EAAOC,QAAUU,C,WCAjBX,EAAOC,QAAUW,C,WCAjBZ,EAAOC,QAAUY,C,wSCGjB,MAAMC,EAAsB,KAC5B,IAAIC,EAAa,EACjB,MAAMC,EACF,WAAArO,CAAYrH,EAAMI,GACd6G,KAAKjH,KAAOA,EACZiH,KAAK7G,GAAKA,CACd,EAOJ,MAAMuV,EAIF,WAAAtO,CAAYuO,EAAS,CAAC,GAClB3O,KAAKtL,GAAK8Z,IACVxO,KAAK4O,UAAYD,EAAOC,QACxB5O,KAAK6O,YAAcF,EAAOE,aAAe,MACrC,MAAM,IAAIpP,MAAM,uDACnB,EACL,CAUA,GAAAqP,CAAI/T,GACA,GAAIiF,KAAK4O,QACL,MAAM,IAAIG,WAAW,0CAGzB,MAFoB,mBAAThU,IACPA,EAAQiU,EAASjU,MAAMA,IACnBlE,IACJ,IAAIoY,EAASlU,EAAMlE,GACnB,YAAkB+J,IAAXqO,EAAuB,KAAO,CAACjP,KAAMiP,GAEpD,EAQJP,EAASQ,SAAW,IAAIR,EAAS,CAAEG,YAAaM,GAAOA,EAAIjX,MAAM,OAMjEwW,EAASU,SAAW,IAAIV,EAAS,CAAEG,YAAaM,GAAOA,EAAIjX,MAAM,OAMjEwW,EAASW,MAAQ,IAAIX,EAAS,CAAEG,YAAaM,GAAOA,EAAIjX,MAAM,OAY9DwW,EAASY,QAAU,IAAIZ,EAAS,CAAEG,YAAa5W,IACvC,GAAIA,GAAkB,OAATA,GAA2B,OAATA,GAA2B,QAATA,EAC7C,MAAM,IAAI8W,WAAW,8BAAgC9W,GACzD,OAAOA,GAAS,UAOxByW,EAASa,YAAc,IAAIb,EAAS,CAAEE,SAAS,IAO/CF,EAASc,UAAY,IAAId,EAAS,CAAEE,SAAS,IAM7CF,EAASe,QAAU,IAAIf,EAAS,CAAEE,SAAS,IAM3C,MAAMc,EACF,WAAAtP,CAIAuP,EAUAC,EAIAvV,GACI2F,KAAK2P,KAAOA,EACZ3P,KAAK4P,QAAUA,EACf5P,KAAK3F,OAASA,CAClB,CAIA,UAAOwV,CAAIF,GACP,OAAOA,GAAQA,EAAK1a,OAAS0a,EAAK1a,MAAMyZ,EAASe,QAAQ/a,GAC7D,EAEJ,MAAMob,EAAU3Q,OAAO4Q,OAAO,MAI9B,MAAMf,EAIF,WAAA5O,CAOAgH,EAIAnS,EAKAP,EAIAsb,EAAQ,GACJhQ,KAAKoH,KAAOA,EACZpH,KAAK/K,MAAQA,EACb+K,KAAKtL,GAAKA,EACVsL,KAAKgQ,MAAQA,CACjB,CAIA,aAAOC,CAAOC,GACV,IAAIjb,EAAQib,EAAKjb,OAASib,EAAKjb,MAAM0E,OAASwF,OAAO4Q,OAAO,MAAQD,EAChEE,GAASE,EAAKC,IAAM,EAAuB,IAAMD,EAAKE,QAAU,EAA2B,IAC1FF,EAAK9R,MAAQ,EAAyB,IAAmB,MAAb8R,EAAK9I,KAAe,EAA6B,GAC9FvQ,EAAO,IAAImY,EAASkB,EAAK9I,MAAQ,GAAInS,EAAOib,EAAKxb,GAAIsb,GACzD,GAAIE,EAAKjb,MACL,IAAK,IAAIob,KAAOH,EAAKjb,MAGjB,GAFK8S,MAAMC,QAAQqI,KACfA,EAAMA,EAAIxZ,IACVwZ,EAAK,CACL,GAAIA,EAAI,GAAGzB,QACP,MAAM,IAAIG,WAAW,8CACzB9Z,EAAMob,EAAI,GAAG3b,IAAM2b,EAAI,EAC3B,CAER,OAAOxZ,CACX,CAKA,IAAA4Q,CAAKA,GAAQ,OAAOzH,KAAK/K,MAAMwS,EAAK/S,GAAK,CAIzC,SAAI4b,GAAU,OAAqB,EAAbtQ,KAAKgQ,OAAgC,CAAG,CAI9D,aAAIO,GAAc,OAAqB,EAAbvQ,KAAKgQ,OAAoC,CAAG,CAItE,WAAIQ,GAAY,OAAqB,EAAbxQ,KAAKgQ,OAAkC,CAAG,CAKlE,eAAIS,GAAgB,OAAqB,EAAbzQ,KAAKgQ,OAAsC,CAAG,CAK1E,EAAAU,CAAGtJ,GACC,GAAmB,iBAARA,EAAkB,CACzB,GAAIpH,KAAKoH,MAAQA,EACb,OAAO,EACX,IAAIiI,EAAQrP,KAAKyH,KAAKiH,EAASW,OAC/B,QAAOA,GAAQA,EAAMsB,QAAQvJ,IAAS,CAC1C,CACA,OAAOpH,KAAKtL,IAAM0S,CACtB,CASA,YAAOrM,CAAM+I,GACT,IAAI8M,EAASzR,OAAO4Q,OAAO,MAC3B,IAAK,IAAItI,KAAQ3D,EACb,IAAK,IAAIsD,KAAQK,EAAKvP,MAAM,KACxB0Y,EAAOxJ,GAAQtD,EAAI2D,GAC3B,OAAQ5H,IACJ,IAAK,IAAIgR,EAAShR,EAAK4H,KAAKiH,EAASW,OAAQpH,GAAK,EAAGA,GAAK4I,EAASA,EAAOlX,OAAS,GAAIsO,IAAK,CACxF,IAAI6I,EAAQF,EAAO3I,EAAI,EAAIpI,EAAKuH,KAAOyJ,EAAO5I,IAC9C,GAAI6I,EACA,OAAOA,CACf,EAER,EAKJ9B,EAAS+B,KAAO,IAAI/B,EAAS,GAAI7P,OAAO4Q,OAAO,MAAO,EAAG,GAUzD,MAAMiB,EAKF,WAAA5Q,CAIA6Q,GACIjR,KAAKiR,MAAQA,EACb,IAAK,IAAIhJ,EAAI,EAAGA,EAAIgJ,EAAMtX,OAAQsO,IAC9B,GAAIgJ,EAAMhJ,GAAGvT,IAAMuT,EACf,MAAM,IAAI8G,WAAW,8EACjC,CAMA,MAAAmC,IAAUjc,GACN,IAAIkc,EAAW,GACf,IAAK,IAAIta,KAAQmJ,KAAKiR,MAAO,CACzB,IAAIG,EAAW,KACf,IAAK,IAAIC,KAAUpc,EAAO,CACtB,IAAI6Z,EAAMuC,EAAOxa,GACbiY,IACKsC,IACDA,EAAWjS,OAAOmS,OAAO,CAAC,EAAGza,EAAK5B,QACtCmc,EAAStC,EAAI,GAAGpa,IAAMoa,EAAI,GAElC,CACAqC,EAAStW,KAAKuW,EAAW,IAAIpC,EAASnY,EAAKuQ,KAAMgK,EAAUva,EAAKnC,GAAImC,EAAKmZ,OAASnZ,EACtF,CACA,OAAO,IAAIma,EAAQG,EACvB,EAEJ,MAAMI,EAAa,IAAIC,QAAWC,EAAkB,IAAID,QAKxD,IAAIE,GACJ,SAAWA,GAMPA,EAASA,EAAyB,eAAI,GAAK,iBAM3CA,EAASA,EAA2B,iBAAI,GAAK,mBAM7CA,EAASA,EAAuB,aAAI,GAAK,eAOzCA,EAASA,EAAyB,eAAI,GAAK,gBAC9C,CA1BD,CA0BGA,IAAaA,EAAW,CAAC,IAiB5B,MAAMC,EAIF,WAAAvR,CAIAvJ,EAIA+a,EAKA7Q,EAIApH,EAIA1E,GASI,GARA+K,KAAKnJ,KAAOA,EACZmJ,KAAK4R,SAAWA,EAChB5R,KAAKe,UAAYA,EACjBf,KAAKrG,OAASA,EAIdqG,KAAK/K,MAAQ,KACTA,GAASA,EAAM0E,OAAQ,CACvBqG,KAAK/K,MAAQkK,OAAO4Q,OAAO,MAC3B,IAAK,IAAKtI,EAAMxP,KAAUhD,EACtB+K,KAAK/K,MAAqB,iBAARwS,EAAmBA,EAAOA,EAAK/S,IAAMuD,CAC/D,CACJ,CAIA,QAAAgB,GACI,IAAIwW,EAAUC,EAAYG,IAAI7P,MAC9B,GAAIyP,IAAYA,EAAQG,QACpB,OAAOH,EAAQE,KAAK1W,WACxB,IAAI2Y,EAAW,GACf,IAAK,IAAIC,KAAM7R,KAAK4R,SAAU,CAC1B,IAAIzC,EAAM0C,EAAG5Y,WACTkW,IACIyC,IACAA,GAAY,KAChBA,GAAYzC,EAEpB,CACA,OAAQnP,KAAKnJ,KAAKuQ,MACb,KAAK0K,KAAK9R,KAAKnJ,KAAKuQ,QAAUpH,KAAKnJ,KAAK2Z,QAAUjW,KAAKC,UAAUwF,KAAKnJ,KAAKuQ,MAAQpH,KAAKnJ,KAAKuQ,OACzFwK,EAASjY,OAAS,IAAMiY,EAAW,IAAM,IAFzBA,CAG7B,CAMA,MAAAG,CAAOC,EAAO,GACV,OAAO,IAAIC,EAAWjS,KAAKkS,QAASF,EACxC,CAMA,QAAAG,CAASnR,EAAKoR,EAAO,EAAGJ,EAAO,GAC3B,IAAIK,EAAQd,EAAW1B,IAAI7P,OAASA,KAAKkS,QACrCH,EAAS,IAAIE,EAAWI,GAG5B,OAFAN,EAAOO,OAAOtR,EAAKoR,GACnBb,EAAW9U,IAAIuD,KAAM+R,EAAOQ,OACrBR,CACX,CAKA,WAAIG,GACA,OAAO,IAAIM,EAASxS,KAAM,EAAG,EAAG,KACpC,CAYA,OAAAyS,CAAQzR,EAAKoR,EAAO,GAChB,IAAIvS,EAAO6S,EAAYnB,EAAW1B,IAAI7P,OAASA,KAAKkS,QAASlR,EAAKoR,GAAM,GAExE,OADAb,EAAW9U,IAAIuD,KAAMH,GACdA,CACX,CAQA,YAAA8S,CAAa3R,EAAKoR,EAAO,GACrB,IAAIvS,EAAO6S,EAAYjB,EAAgB5B,IAAI7P,OAASA,KAAKkS,QAASlR,EAAKoR,GAAM,GAE7E,OADAX,EAAgBhV,IAAIuD,KAAMH,GACnBA,CACX,CAQA,YAAA+S,CAAa5R,EAAKoR,EAAO,GACrB,OAwcR,SAAuBzC,EAAM3O,EAAKoR,GAC9B,IAAIS,EAAQlD,EAAKgD,aAAa3R,EAAKoR,GAAOU,EAAS,KACnD,IAAK,IAAIC,EAAOF,aAAiBL,EAAWK,EAAQA,EAAMrc,QAAQwc,OAAQD,EAAMA,EAAOA,EAAKC,OACxF,GAAID,EAAKzR,MAAQ,EAAG,CAChB,IAAI0R,EAASD,EAAKC,QACjBF,IAAWA,EAAS,CAACD,KAAShY,KAAKmY,EAAOP,QAAQzR,EAAKoR,IACxDW,EAAOC,CACX,KACK,CACD,IAAIC,EAAQvD,EAAYG,IAAIkD,EAAKpD,MAEjC,GAAIsD,GAASA,EAAMrD,SAAWqD,EAAMrD,QAAQ,GAAG7W,MAAQiI,GAAOiS,EAAMrD,QAAQqD,EAAMrD,QAAQjW,OAAS,GAAGR,IAAM6H,EAAK,CAC7G,IAAIkS,EAAO,IAAIV,EAASS,EAAMtD,KAAMsD,EAAMrD,QAAQ,GAAG7W,KAAOga,EAAKha,MAAO,EAAGga,IAC1ED,IAAWA,EAAS,CAACD,KAAShY,KAAK6X,EAAYQ,EAAMlS,EAAKoR,GAAM,GACrE,CACJ,CAEJ,OAAOU,EAASK,EAAUL,GAAUD,CACxC,CA1deO,CAAcpT,KAAMgB,EAAKoR,EACpC,CAQA,OAAA1R,CAAQwP,GACJ,IAAI,MAAEvP,EAAK,MAAE0S,EAAK,KAAEta,EAAO,EAAC,GAAEI,EAAK6G,KAAKrG,QAAWuW,EAC/C8B,EAAO9B,EAAK8B,MAAQ,EAAGsB,GAAQtB,EAAON,EAAS6B,kBAAoB,EACvE,IAAK,IAAIC,EAAIxT,KAAK+R,OAAOC,EAAON,EAAS6B,oBAAqB,CAC1D,IAAIE,GAAU,EACd,GAAID,EAAEza,MAAQI,GAAMqa,EAAEra,IAAMJ,KAAUua,GAAQE,EAAE3c,KAAK4Z,cAA4B,IAAb9P,EAAM6S,IAAe,CACrF,GAAIA,EAAEE,aACF,SACJD,GAAU,CACd,CACA,KACQA,GAAWJ,IAAUC,IAASE,EAAE3c,KAAK4Z,cACrC4C,EAAMG,IACNA,EAAEG,eAHD,CAKL,IAAKH,EAAER,SACH,OACJS,GAAU,CACd,CACJ,CACJ,CAKA,IAAAhM,CAAKA,GACD,OAAQA,EAAKmH,QAAiC5O,KAAK/K,MAAQ+K,KAAK/K,MAAMwS,EAAK/S,SAAMkM,EAA1DZ,KAAKnJ,KAAK4Q,KAAKA,EAC1C,CAMA,cAAImM,GACA,IAAI3E,EAAS,GACb,GAAIjP,KAAK/K,MACL,IAAK,IAAIP,KAAMsL,KAAK/K,MAChBga,EAAOpU,KAAK,EAAEnG,EAAIsL,KAAK/K,MAAMP,KACrC,OAAOua,CACX,CAMA,OAAA4E,CAAQlF,EAAS,CAAC,GACd,OAAO3O,KAAK4R,SAASjY,QAAU,EAA+BqG,KAC1D8T,EAAa9E,EAAS+B,KAAM/Q,KAAK4R,SAAU5R,KAAKe,UAAW,EAAGf,KAAK4R,SAASjY,OAAQ,EAAGqG,KAAKrG,OAAQ,CAACiY,EAAU7Q,EAAWpH,IAAW,IAAIgY,EAAK3R,KAAKnJ,KAAM+a,EAAU7Q,EAAWpH,EAAQqG,KAAK4T,YAAajF,EAAOoF,UAAY,EAAEnC,EAAU7Q,EAAWpH,IAAW,IAAIgY,EAAK3C,EAAS+B,KAAMa,EAAU7Q,EAAWpH,IAClT,CAKA,YAAOqa,CAAMzU,GAAQ,OA4tBzB,SAAmBA,GACf,IAAI0U,EACJ,IAAI,OAAEC,EAAM,QAAEC,EAAO,gBAAEC,EAAkB7F,EAAmB,OAAE8F,EAAS,GAAE,cAAEC,EAAgBH,EAAQlD,MAAMtX,QAAW4F,EAChHwS,EAAShK,MAAMC,QAAQkM,GAAU,IAAIK,EAAiBL,EAAQA,EAAOva,QAAUua,EAC/EjD,EAAQkD,EAAQlD,MAChB1B,EAAc,EAAGC,EAAY,EACjC,SAASgF,EAASC,EAAaC,EAAQ9C,EAAU7Q,EAAW4T,EAAUC,GAClE,IAAI,GAAElgB,EAAE,MAAEmgB,EAAK,IAAEC,EAAG,KAAEC,GAAShD,EAC3BiD,EAAmBxF,EAAWyF,EAAiB1F,EACnD,KAAOwF,EAAO,GAAG,CAEb,GADAhD,EAAOmD,QACM,GAATH,EAAsC,CACtC,IAAIlV,EAAOwU,EAAO3f,GAGlB,OAFAkd,EAAS/W,KAAKgF,QACdkB,EAAUlG,KAAKga,EAAQJ,EAE3B,CACK,IAAa,GAATM,EAEL,YADAxF,EAAc7a,GAGb,IAAa,GAATqgB,EAEL,YADAvF,EAAY9a,GAIZ,MAAM,IAAIqa,WAAW,6BAA6BgG,IAE1D,CACA,IAAsBlV,EAAMqU,EAAxBrd,EAAOoa,EAAMvc,GACbygB,EAAWN,EAAQJ,EACvB,GAAIK,EAAMD,GAAST,IAAoBF,EAASkB,EAAerD,EAAO/Q,IAAM0T,EAAQC,IAAY,CAE5F,IAAIpV,EAAO,IAAI8V,YAAYnB,EAAOa,KAAOb,EAAOoB,MAC5CC,EAASxD,EAAO/Q,IAAMkT,EAAOa,KAAMzT,EAAQ/B,EAAK5F,OACpD,KAAOoY,EAAO/Q,IAAMuU,GAChBjU,EAAQkU,EAAatB,EAAOW,MAAOtV,EAAM+B,GAC7CzB,EAAO,IAAI4V,EAAWlW,EAAMuV,EAAMZ,EAAOW,MAAOV,GAChDgB,EAAWjB,EAAOW,MAAQJ,CAC9B,KACK,CACD,IAAIc,EAASxD,EAAO/Q,IAAM+T,EAC1BhD,EAAOmD,OACP,IAAIQ,EAAgB,GAAIC,EAAiB,GACrCC,EAAgBlhB,GAAM4f,EAAgB5f,GAAM,EAC5CmhB,EAAY,EAAGC,EAAUhB,EAC7B,KAAO/C,EAAO/Q,IAAMuU,GACZK,GAAiB,GAAK7D,EAAOrd,IAAMkhB,GAAiB7D,EAAOgD,MAAQ,GAC/DhD,EAAO+C,KAAOgB,EAAU1B,IACxB2B,EAAeL,EAAeC,EAAgBd,EAAOgB,EAAW9D,EAAO+C,IAAKgB,EAASF,EAAeZ,EAAkBC,GACtHY,EAAYH,EAAc/b,OAC1Bmc,EAAU/D,EAAO+C,KAErB/C,EAAOmD,QAEFN,EAAQ,KACboB,EAAanB,EAAOU,EAAQG,EAAeC,GAG3CnB,EAASK,EAAOU,EAAQG,EAAeC,EAAgBC,EAAehB,EAAQ,GAOtF,GAJIgB,GAAiB,GAAKC,EAAY,GAAKA,EAAYH,EAAc/b,QACjEoc,EAAeL,EAAeC,EAAgBd,EAAOgB,EAAWhB,EAAOiB,EAASF,EAAeZ,EAAkBC,GACrHS,EAAcO,UACdN,EAAeM,UACXL,GAAiB,GAAKC,EAAY,EAAG,CACrC,IAAIK,EAAOC,EAAatf,EAAMoe,GAC9BpV,EAAOiU,EAAajd,EAAM6e,EAAeC,EAAgB,EAAGD,EAAc/b,OAAQ,EAAGmb,EAAMD,EAAOqB,EAAMA,EAC5G,MAEIrW,EAAOkU,EAASld,EAAM6e,EAAeC,EAAgBb,EAAMD,EAAOG,EAAmBF,EAAKG,EAElG,CACArD,EAAS/W,KAAKgF,GACdkB,EAAUlG,KAAKsa,EACnB,CACA,SAASa,EAAavB,EAAaC,EAAQ9C,EAAU7Q,GACjD,IAAIP,EAAQ,GACR4V,EAAY,EAAGC,GAAU,EAC7B,KAAOtE,EAAO/Q,IAAM0T,GAAQ,CACxB,IAAI,GAAEhgB,EAAE,MAAEmgB,EAAK,IAAEC,EAAG,KAAEC,GAAShD,EAC/B,GAAIgD,EAAO,EACPhD,EAAOmD,WAEN,IAAImB,GAAU,GAAKxB,EAAQwB,EAC5B,MAGIA,EAAS,IACTA,EAASvB,EAAMV,GACnB5T,EAAM3F,KAAKnG,EAAImgB,EAAOC,GACtBsB,IACArE,EAAOmD,MACX,CACJ,CACA,GAAIkB,EAAW,CACX,IAAIlC,EAAS,IAAImB,YAAwB,EAAZe,GACzBvB,EAAQrU,EAAMA,EAAM7G,OAAS,GACjC,IAAK,IAAIsO,EAAIzH,EAAM7G,OAAS,EAAG2c,EAAI,EAAGrO,GAAK,EAAGA,GAAK,EAC/CiM,EAAOoC,KAAO9V,EAAMyH,GACpBiM,EAAOoC,KAAO9V,EAAMyH,EAAI,GAAK4M,EAC7BX,EAAOoC,KAAO9V,EAAMyH,EAAI,GAAK4M,EAC7BX,EAAOoC,KAAOA,EAElB1E,EAAS/W,KAAK,IAAI4a,EAAWvB,EAAQ1T,EAAM,GAAKqU,EAAOV,IACvDpT,EAAUlG,KAAKga,EAAQJ,EAC3B,CACJ,CACA,SAAS0B,EAAatf,EAAM0Y,GACxB,MAAO,CAACqC,EAAU7Q,EAAWpH,KACzB,IAAgD4c,EAAMC,EAAlDhH,EAAY,EAAGiH,EAAQ7E,EAASjY,OAAS,EAC7C,GAAI8c,GAAS,IAAMF,EAAO3E,EAAS6E,cAAmB9E,EAAM,CACxD,IAAK8E,GAASF,EAAK1f,MAAQA,GAAQ0f,EAAK5c,QAAUA,EAC9C,OAAO4c,GACPC,EAAgBD,EAAK9O,KAAKiH,EAASc,cACnCA,EAAYzO,EAAU0V,GAASF,EAAK5c,OAAS6c,EACrD,CACA,OAAOzC,EAASld,EAAM+a,EAAU7Q,EAAWpH,EAAQ6V,EAAWD,GAEtE,CACA,SAASwG,EAAenE,EAAU7Q,EAAW2V,EAAMzO,EAAGlP,EAAMI,EAAItC,EAAM2Y,EAAWD,GAC7E,IAAImG,EAAgB,GAAIC,EAAiB,GACzC,KAAO/D,EAASjY,OAASsO,GACrByN,EAAc7a,KAAK+W,EAAS+E,OAC5BhB,EAAe9a,KAAKkG,EAAU4V,MAAQD,EAAO3d,GAEjD6Y,EAAS/W,KAAKkZ,EAASI,EAAQlD,MAAMpa,GAAO6e,EAAeC,EAAgBxc,EAAKJ,EAAMyW,EAAYrW,EAAIoW,IACtGxO,EAAUlG,KAAK9B,EAAO2d,EAC1B,CACA,SAAS3C,EAASld,EAAM+a,EAAU7Q,EAAWpH,EAAQ6V,EAAWD,EAAata,GACzE,GAAIsa,EAAa,CACb,IAAIqH,EAAO,CAAClI,EAASa,YAAaA,GAClCta,EAAQA,EAAQ,CAAC2hB,GAAMC,OAAO5hB,GAAS,CAAC2hB,EAC5C,CACA,GAAIpH,EAAY,GAAI,CAChB,IAAIoH,EAAO,CAAClI,EAASc,UAAWA,GAChCva,EAAQA,EAAQ,CAAC2hB,GAAMC,OAAO5hB,GAAS,CAAC2hB,EAC5C,CACA,OAAO,IAAIjF,EAAK9a,EAAM+a,EAAU7Q,EAAWpH,EAAQ1E,EACvD,CACA,SAASmgB,EAAe0B,EAASnC,GAO7B,IAAIoC,EAAOhF,EAAOgF,OACdhC,EAAO,EAAGF,EAAQ,EAAGS,EAAO,EAAG0B,EAAWD,EAAKjC,IAAMV,EACrDnF,EAAS,CAAE8F,KAAM,EAAGF,MAAO,EAAGS,KAAM,GACxCvC,EAAM,IAAK,IAAI2B,EAASqC,EAAK/V,IAAM8V,EAASC,EAAK/V,IAAM0T,GAAS,CAC5D,IAAIuC,EAAWF,EAAKhC,KAEpB,GAAIgC,EAAKriB,IAAMigB,GAAYsC,GAAY,EAAG,CAGtChI,EAAO8F,KAAOA,EACd9F,EAAO4F,MAAQA,EACf5F,EAAOqG,KAAOA,EACdA,GAAQ,EACRP,GAAQ,EACRgC,EAAK7B,OACL,QACJ,CACA,IAAIC,EAAW4B,EAAK/V,IAAMiW,EAC1B,GAAIA,EAAW,GAAK9B,EAAWT,GAAUqC,EAAKlC,MAAQmC,EAClD,MACJ,IAAIE,EAAeH,EAAKriB,IAAM4f,EAAgB,EAAI,EAC9C6C,EAAYJ,EAAKlC,MAErB,IADAkC,EAAK7B,OACE6B,EAAK/V,IAAMmU,GAAU,CACxB,GAAI4B,EAAKhC,KAAO,EAAG,CACf,IAAkB,GAAdgC,EAAKhC,KAGL,MAAMhC,EAFNmE,GAAgB,CAGxB,MACSH,EAAKriB,IAAM4f,IAChB4C,GAAgB,GAEpBH,EAAK7B,MACT,CACAL,EAAQsC,EACRpC,GAAQkC,EACR3B,GAAQ4B,CACZ,CAMA,OALIvC,EAAW,GAAKI,GAAQ+B,KACxB7H,EAAO8F,KAAOA,EACd9F,EAAO4F,MAAQA,EACf5F,EAAOqG,KAAOA,GAEXrG,EAAO8F,KAAO,EAAI9F,OAASrO,CACtC,CACA,SAAS4U,EAAa4B,EAAalD,EAAQ5S,GACvC,IAAI,GAAE5M,EAAE,MAAEmgB,EAAK,IAAEC,EAAG,KAAEC,GAAShD,EAE/B,GADAA,EAAOmD,OACHH,GAAQ,GAAKrgB,EAAK4f,EAAe,CACjC,IAAI+C,EAAa/V,EACjB,GAAIyT,EAAO,EAAG,CACV,IAAIQ,EAASxD,EAAO/Q,KAAO+T,EAAO,GAClC,KAAOhD,EAAO/Q,IAAMuU,GAChBjU,EAAQkU,EAAa4B,EAAalD,EAAQ5S,EAClD,CACA4S,IAAS5S,GAAS+V,EAClBnD,IAAS5S,GAASwT,EAAMsC,EACxBlD,IAAS5S,GAASuT,EAAQuC,EAC1BlD,IAAS5S,GAAS5M,CACtB,MACkB,GAATqgB,EACLxF,EAAc7a,GAEA,GAATqgB,IACLvF,EAAY9a,GAEhB,OAAO4M,CACX,CACA,IAAIsQ,EAAW,GAAI7Q,EAAY,GAC/B,KAAOgR,EAAO/Q,IAAM,GAChBwT,EAASjV,EAAKsV,OAAS,EAAGtV,EAAK6X,aAAe,EAAGxF,EAAU7Q,GAAY,EAAG,GAC9E,IAAIpH,EAAgC,QAAtBsa,EAAK1U,EAAK5F,cAA2B,IAAPsa,EAAgBA,EAAMrC,EAASjY,OAASoH,EAAU,GAAK6Q,EAAS,GAAGjY,OAAS,EACxH,OAAO,IAAIgY,EAAKV,EAAM1R,EAAK+X,OAAQ1F,EAASqE,UAAWlV,EAAUkV,UAAWtc,EAChF,CA17BgC4d,CAAUhY,EAAO,EAKjDoS,EAAK6F,MAAQ,IAAI7F,EAAK3C,EAAS+B,KAAM,GAAI,GAAI,GAC7C,MAAMwD,EACF,WAAAnU,CAAY8T,EAAQ5S,GAChBtB,KAAKkU,OAASA,EACdlU,KAAKsB,MAAQA,CACjB,CACA,MAAI5M,GAAO,OAAOsL,KAAKkU,OAAOlU,KAAKsB,MAAQ,EAAI,CAC/C,SAAIuT,GAAU,OAAO7U,KAAKkU,OAAOlU,KAAKsB,MAAQ,EAAI,CAClD,OAAIwT,GAAQ,OAAO9U,KAAKkU,OAAOlU,KAAKsB,MAAQ,EAAI,CAChD,QAAIyT,GAAS,OAAO/U,KAAKkU,OAAOlU,KAAKsB,MAAQ,EAAI,CACjD,OAAIN,GAAQ,OAAOhB,KAAKsB,KAAO,CAC/B,IAAA4T,GAASlV,KAAKsB,OAAS,CAAG,CAC1B,IAAAyV,GAAS,OAAO,IAAIxC,EAAiBvU,KAAKkU,OAAQlU,KAAKsB,MAAQ,EAQnE,MAAMmU,EAIF,WAAArV,CAIA8T,EAIAva,EAIA8C,GACIuD,KAAKkU,OAASA,EACdlU,KAAKrG,OAASA,EACdqG,KAAKvD,IAAMA,CACf,CAIA,QAAI5F,GAAS,OAAOmY,EAAS+B,IAAM,CAInC,QAAA9X,GACI,IAAIgW,EAAS,GACb,IAAK,IAAI3N,EAAQ,EAAGA,EAAQtB,KAAKkU,OAAOva,QACpCsV,EAAOpU,KAAKmF,KAAKyX,YAAYnW,IAC7BA,EAAQtB,KAAKkU,OAAO5S,EAAQ,GAEhC,OAAO2N,EAAOxR,KAAK,IACvB,CAIA,WAAAga,CAAYnW,GACR,IAAI5M,EAAKsL,KAAKkU,OAAO5S,GAAQoW,EAAW1X,KAAKkU,OAAO5S,EAAQ,GACxDzK,EAAOmJ,KAAKvD,IAAIwU,MAAMvc,GAAKua,EAASpY,EAAKuQ,KAI7C,GAHI,KAAK0K,KAAK7C,KAAYpY,EAAK2Z,UAC3BvB,EAAS1U,KAAKC,UAAUyU,IAExByI,IADJpW,GAAS,GAEL,OAAO2N,EACX,IAAI2C,EAAW,GACf,KAAOtQ,EAAQoW,GACX9F,EAAS/W,KAAKmF,KAAKyX,YAAYnW,IAC/BA,EAAQtB,KAAKkU,OAAO5S,EAAQ,GAEhC,OAAO2N,EAAS,IAAM2C,EAASnU,KAAK,KAAO,GAC/C,CAIA,SAAAka,CAAUN,EAAYK,EAAUE,EAAK5W,EAAKoR,GACtC,IAAI,OAAE8B,GAAWlU,KAAM6X,GAAQ,EAC/B,IAAK,IAAI5P,EAAIoP,EAAYpP,GAAKyP,KACtBI,EAAU1F,EAAMpR,EAAKkT,EAAOjM,EAAI,GAAIiM,EAAOjM,EAAI,MAC/C4P,EAAO5P,EACH2P,EAAM,IAHsB3P,EAAIiM,EAAOjM,EAAI,IAOvD,OAAO4P,CACX,CAIA,KAAAvkB,CAAMykB,EAAQC,EAAMjf,GAChB,IAAIkf,EAAIjY,KAAKkU,OACTgE,EAAO,IAAI7C,YAAY2C,EAAOD,GAASI,EAAM,EACjD,IAAK,IAAIlQ,EAAI8P,EAAQzB,EAAI,EAAGrO,EAAI+P,GAAO,CACnCE,EAAK5B,KAAO2B,EAAEhQ,KACdiQ,EAAK5B,KAAO2B,EAAEhQ,KAAOlP,EACrB,IAAII,EAAK+e,EAAK5B,KAAO2B,EAAEhQ,KAAOlP,EAC9Bmf,EAAK5B,KAAO2B,EAAEhQ,KAAO8P,EACrBI,EAAMC,KAAKC,IAAIF,EAAKhf,EACxB,CACA,OAAO,IAAIsc,EAAWyC,EAAMC,EAAKnY,KAAKvD,IAC1C,EAEJ,SAASqb,EAAU1F,EAAMpR,EAAKjI,EAAMI,GAChC,OAAQiZ,GACJ,KAAM,EAAqB,OAAOrZ,EAAOiI,EACzC,KAAM,EAAyB,OAAO7H,GAAM6H,GAAOjI,EAAOiI,EAC1D,KAAK,EAAqB,OAAOjI,EAAOiI,GAAO7H,EAAK6H,EACpD,KAAK,EAAwB,OAAOjI,GAAQiI,GAAO7H,EAAK6H,EACxD,KAAK,EAAoB,OAAO7H,EAAK6H,EACrC,KAAK,EAAuB,OAAO,EAE3C,CACA,SAAS0R,EAAY7S,EAAMmB,EAAKoR,EAAMkG,GAGlC,IAFA,IAAIrE,EAEGpU,EAAK9G,MAAQ8G,EAAK1G,KACpBiZ,EAAO,EAAIvS,EAAK9G,MAAQiI,EAAMnB,EAAK9G,KAAOiI,KAC1CoR,GAAQ,EAAIvS,EAAK1G,IAAM6H,EAAMnB,EAAK1G,GAAK6H,IAAM,CAC9C,IAAIgS,GAAUsF,GAAYzY,aAAgB2S,GAAY3S,EAAKyB,MAAQ,EAAI,KAAOzB,EAAKmT,OACnF,IAAKA,EACD,OAAOnT,EACXA,EAAOmT,CACX,CACA,IAAIhB,EAAOsG,EAAW,EAAI5G,EAAS6G,eAEnC,GAAID,EACA,IAAK,IAAIvF,EAAOlT,EAAMmT,EAASD,EAAKC,OAAQA,EAAQD,EAAOC,EAAQA,EAASD,EAAKC,OACzED,aAAgBP,GAAYO,EAAKzR,MAAQ,IAA+C,QAAxC2S,EAAKjB,EAAOrS,MAAMK,EAAKoR,EAAMJ,UAA0B,IAAPiC,OAAgB,EAASA,EAAGlb,OAASga,EAAKha,OAC1I8G,EAAOmT,GAEnB,OAAS,CACL,IAAIH,EAAQhT,EAAKc,MAAMK,EAAKoR,EAAMJ,GAClC,IAAKa,EACD,OAAOhT,EACXA,EAAOgT,CACX,CACJ,CACA,MAAM2F,EACF,MAAAzG,CAAOC,EAAO,GAAK,OAAO,IAAIC,EAAWjS,KAAMgS,EAAO,CACtD,QAAAxN,CAAS3N,EAAM4hB,EAAS,KAAMC,EAAQ,MAClC,IAAIC,EAAIC,EAAY5Y,KAAMnJ,EAAM4hB,EAAQC,GACxC,OAAOC,EAAEhf,OAASgf,EAAE,GAAK,IAC7B,CACA,WAAAC,CAAY/hB,EAAM4hB,EAAS,KAAMC,EAAQ,MACrC,OAAOE,EAAY5Y,KAAMnJ,EAAM4hB,EAAQC,EAC3C,CACA,OAAAjG,CAAQzR,EAAKoR,EAAO,GAChB,OAAOM,EAAY1S,KAAMgB,EAAKoR,GAAM,EACxC,CACA,YAAAO,CAAa3R,EAAKoR,EAAO,GACrB,OAAOM,EAAY1S,KAAMgB,EAAKoR,GAAM,EACxC,CACA,YAAAyG,CAAariB,GACT,OAAOsiB,EAAiB9Y,KAAKgT,OAAQxc,EACzC,CACA,0BAAAuiB,CAA2B/X,GACvB,IAAI+R,EAAO/S,KAAKgZ,YAAYhY,GAAMnB,EAAOG,KACzC,KAAO+S,GAAM,CACT,IAAIwD,EAAOxD,EAAKkG,UAChB,IAAK1C,GAAQA,EAAKpd,IAAM4Z,EAAK5Z,GACzB,MACAod,EAAK1f,KAAK2Z,SAAW+F,EAAKxd,MAAQwd,EAAKpd,IACvC0G,EAAOkT,EACPA,EAAOwD,EAAK2C,aAGZnG,EAAOwD,CAEf,CACA,OAAO1W,CACX,CACA,QAAIA,GAAS,OAAOG,IAAM,CAC1B,QAAIkV,GAAS,OAAOlV,KAAKgT,MAAQ,EAErC,MAAMR,UAAiBgG,EACnB,WAAApY,CAAYmS,EAAOxZ,EAEnBuI,EAAO6X,GACHC,QACApZ,KAAKuS,MAAQA,EACbvS,KAAKjH,KAAOA,EACZiH,KAAKsB,MAAQA,EACbtB,KAAKmZ,QAAUA,CACnB,CACA,QAAItiB,GAAS,OAAOmJ,KAAKuS,MAAM1b,IAAM,CACrC,QAAIuQ,GAAS,OAAOpH,KAAKuS,MAAM1b,KAAKuQ,IAAM,CAC1C,MAAIjO,GAAO,OAAO6G,KAAKjH,KAAOiH,KAAKuS,MAAM5Y,MAAQ,CACjD,SAAA0f,CAAUpR,EAAG2P,EAAK5W,EAAKoR,EAAMJ,EAAO,GAChC,IAAK,IAAIgB,EAAShT,OAAQ,CACtB,IAAK,IAAI,SAAE4R,EAAQ,UAAE7Q,GAAciS,EAAOT,MAAOxT,EAAI6Y,EAAM,EAAIhG,EAASjY,QAAU,EAAGsO,GAAKlJ,EAAGkJ,GAAK2P,EAAK,CACnG,IAAI1C,EAAOtD,EAAS3J,GAAI4M,EAAQ9T,EAAUkH,GAAK+K,EAAOja,KACtD,GAAK+e,EAAU1F,EAAMpR,EAAK6T,EAAOA,EAAQK,EAAKvb,QAE9C,GAAIub,aAAgBO,EAAY,CAC5B,GAAIzD,EAAON,EAAS4H,eAChB,SACJ,IAAIhY,EAAQ4T,EAAKyC,UAAU,EAAGzC,EAAKhB,OAAOva,OAAQie,EAAK5W,EAAM6T,EAAOzC,GACpE,GAAI9Q,GAAS,EACT,OAAO,IAAIiY,EAAW,IAAIC,EAAcxG,EAAQkC,EAAMjN,EAAG4M,GAAQ,KAAMvT,EAC/E,MACK,GAAK0Q,EAAON,EAAS6B,mBAAuB2B,EAAKre,KAAK4Z,aAAegJ,EAASvE,GAAQ,CACvF,IAAIzF,EACJ,KAAMuC,EAAON,EAASgI,gBAAkBjK,EAAUC,EAAYG,IAAIqF,MAAWzF,EAAQG,QACjF,OAAO,IAAI4C,EAAS/C,EAAQE,KAAMkF,EAAO5M,EAAG+K,GAChD,IAAIH,EAAQ,IAAIL,EAAS0C,EAAML,EAAO5M,EAAG+K,GACzC,OAAQhB,EAAON,EAAS6B,mBAAsBV,EAAMhc,KAAK4Z,YAAcoC,EACjEA,EAAMwG,UAAUzB,EAAM,EAAI1C,EAAKtD,SAASjY,OAAS,EAAI,EAAGie,EAAK5W,EAAKoR,EAC5E,CACJ,CACA,GAAKJ,EAAON,EAAS6B,mBAAsBP,EAAOnc,KAAK4Z,YACnD,OAAO,KAMX,GAJIxI,EADA+K,EAAO1R,OAAS,EACZ0R,EAAO1R,MAAQsW,EAEfA,EAAM,GAAK,EAAI5E,EAAOmG,QAAQ5G,MAAMX,SAASjY,OACrDqZ,EAASA,EAAOmG,SACXnG,EACD,OAAO,IACf,CACJ,CACA,cAAIU,GAAe,OAAO1T,KAAKqZ,UAAU,EAAG,EAAG,EAAG,EAAwB,CAC1E,aAAIJ,GAAc,OAAOjZ,KAAKqZ,UAAUrZ,KAAKuS,MAAMX,SAASjY,OAAS,GAAI,EAAG,EAAG,EAAwB,CACvG,UAAAuH,CAAWF,GAAO,OAAOhB,KAAKqZ,UAAU,EAAG,EAAGrY,EAAK,EAAqB,CACxE,WAAAgY,CAAYhY,GAAO,OAAOhB,KAAKqZ,UAAUrZ,KAAKuS,MAAMX,SAASjY,OAAS,GAAI,EAAGqH,GAAM,EAAsB,CACzG,KAAAL,CAAMK,EAAKoR,EAAMJ,EAAO,GACpB,IAAIvC,EACJ,KAAMuC,EAAON,EAAS6G,kBAAoB9I,EAAUC,EAAYG,IAAI7P,KAAKuS,SAAW9C,EAAQG,QAAS,CACjG,IAAI+J,EAAO3Y,EAAMhB,KAAKjH,KACtB,IAAK,IAAI,KAAEA,EAAI,GAAEI,KAAQsW,EAAQG,QAC7B,IAAKwC,EAAO,EAAIrZ,GAAQ4gB,EAAO5gB,EAAO4gB,KACjCvH,EAAO,EAAIjZ,GAAMwgB,EAAOxgB,EAAKwgB,GAC9B,OAAO,IAAInH,EAAS/C,EAAQE,KAAMF,EAAQG,QAAQ,GAAG7W,KAAOiH,KAAKjH,MAAO,EAAGiH,KAEvF,CACA,OAAOA,KAAKqZ,UAAU,EAAG,EAAGrY,EAAKoR,EAAMJ,EAC3C,CACA,qBAAA4H,GACI,IAAIC,EAAM7Z,KACV,KAAO6Z,EAAIhjB,KAAK4Z,aAAeoJ,EAAIV,SAC/BU,EAAMA,EAAIV,QACd,OAAOU,CACX,CACA,UAAI7G,GACA,OAAOhT,KAAKmZ,QAAUnZ,KAAKmZ,QAAQS,wBAA0B,IACjE,CACA,eAAIjG,GACA,OAAO3T,KAAKmZ,SAAWnZ,KAAKsB,OAAS,EAAItB,KAAKmZ,QAAQE,UAAUrZ,KAAKsB,MAAQ,EAAG,EAAG,EAAG,GAAyB,IACnH,CACA,eAAI4X,GACA,OAAOlZ,KAAKmZ,SAAWnZ,KAAKsB,OAAS,EAAItB,KAAKmZ,QAAQE,UAAUrZ,KAAKsB,MAAQ,GAAI,EAAG,EAAG,GAAyB,IACpH,CACA,QAAIqO,GAAS,OAAO3P,KAAKuS,KAAO,CAChC,MAAAuH,GAAW,OAAO9Z,KAAKuS,KAAO,CAI9B,QAAAtZ,GAAa,OAAO+G,KAAKuS,MAAMtZ,UAAY,EAE/C,SAAS2f,EAAY/Y,EAAMhJ,EAAM4hB,EAAQC,GACrC,IAAIqB,EAAMla,EAAKkS,SAAU9C,EAAS,GAClC,IAAK8K,EAAIrG,aACL,OAAOzE,EACX,GAAc,MAAVwJ,EACA,IAAK,IAAI3H,GAAQ,GAAQA,GAErB,GADAA,EAAQiJ,EAAIljB,KAAK6Z,GAAG+H,IACfsB,EAAIpG,cACL,OAAO1E,EAEnB,OAAS,CACL,GAAa,MAATyJ,GAAiBqB,EAAIljB,KAAK6Z,GAAGgI,GAC7B,OAAOzJ,EAGX,GAFI8K,EAAIljB,KAAK6Z,GAAG7Z,IACZoY,EAAOpU,KAAKkf,EAAIla,OACfka,EAAIpG,cACL,OAAgB,MAAT+E,EAAgBzJ,EAAS,EACxC,CACJ,CACA,SAAS6J,EAAiBjZ,EAAMrJ,EAASyR,EAAIzR,EAAQmD,OAAS,GAC1D,IAAK,IAAIiR,EAAI/K,EAAMoI,GAAK,EAAG2C,EAAIA,EAAEoI,OAAQ,CACrC,IAAKpI,EACD,OAAO,EACX,IAAKA,EAAE/T,KAAK4Z,YAAa,CACrB,GAAIja,EAAQyR,IAAMzR,EAAQyR,IAAM2C,EAAExD,KAC9B,OAAO,EACXa,GACJ,CACJ,CACA,OAAO,CACX,CACA,MAAMuR,EACF,WAAApZ,CAAY4S,EAAQkB,EAAQ5S,EAAOuT,GAC/B7U,KAAKgT,OAASA,EACdhT,KAAKkU,OAASA,EACdlU,KAAKsB,MAAQA,EACbtB,KAAK6U,MAAQA,CACjB,EAEJ,MAAM0E,UAAmBf,EACrB,QAAIpR,GAAS,OAAOpH,KAAKnJ,KAAKuQ,IAAM,CACpC,QAAIrO,GAAS,OAAOiH,KAAKxJ,QAAQqe,MAAQ7U,KAAKxJ,QAAQ0d,OAAOA,OAAOlU,KAAKsB,MAAQ,EAAI,CACrF,MAAInI,GAAO,OAAO6G,KAAKxJ,QAAQqe,MAAQ7U,KAAKxJ,QAAQ0d,OAAOA,OAAOlU,KAAKsB,MAAQ,EAAI,CACnF,WAAAlB,CAAY5J,EAAS2iB,EAAS7X,GAC1B8X,QACApZ,KAAKxJ,QAAUA,EACfwJ,KAAKmZ,QAAUA,EACfnZ,KAAKsB,MAAQA,EACbtB,KAAKnJ,KAAOL,EAAQ0d,OAAOzX,IAAIwU,MAAMza,EAAQ0d,OAAOA,OAAO5S,GAC/D,CACA,KAAAL,CAAM2W,EAAK5W,EAAKoR,GACZ,IAAI,OAAE8B,GAAWlU,KAAKxJ,QAClB8K,EAAQ4S,EAAOyD,UAAU3X,KAAKsB,MAAQ,EAAG4S,EAAOA,OAAOlU,KAAKsB,MAAQ,GAAIsW,EAAK5W,EAAMhB,KAAKxJ,QAAQqe,MAAOzC,GAC3G,OAAO9Q,EAAQ,EAAI,KAAO,IAAIiY,EAAWvZ,KAAKxJ,QAASwJ,KAAMsB,EACjE,CACA,cAAIoS,GAAe,OAAO1T,KAAKiB,MAAM,EAAG,EAAG,EAAwB,CACnE,aAAIgY,GAAc,OAAOjZ,KAAKiB,OAAO,EAAG,EAAG,EAAwB,CACnE,UAAAC,CAAWF,GAAO,OAAOhB,KAAKiB,MAAM,EAAGD,EAAK,EAAqB,CACjE,WAAAgY,CAAYhY,GAAO,OAAOhB,KAAKiB,OAAO,EAAGD,GAAM,EAAsB,CACrE,KAAAL,CAAMK,EAAKoR,EAAMJ,EAAO,GACpB,GAAIA,EAAON,EAAS4H,eAChB,OAAO,KACX,IAAI,OAAEpF,GAAWlU,KAAKxJ,QAClB8K,EAAQ4S,EAAOyD,UAAU3X,KAAKsB,MAAQ,EAAG4S,EAAOA,OAAOlU,KAAKsB,MAAQ,GAAI8Q,EAAO,EAAI,GAAK,EAAGpR,EAAMhB,KAAKxJ,QAAQqe,MAAOzC,GACzH,OAAO9Q,EAAQ,EAAI,KAAO,IAAIiY,EAAWvZ,KAAKxJ,QAASwJ,KAAMsB,EACjE,CACA,UAAI0R,GACA,OAAOhT,KAAKmZ,SAAWnZ,KAAKxJ,QAAQwc,OAAO4G,uBAC/C,CACA,eAAAI,CAAgBpC,GACZ,OAAO5X,KAAKmZ,QAAU,KAAOnZ,KAAKxJ,QAAQwc,OAAOqG,UAAUrZ,KAAKxJ,QAAQ8K,MAAQsW,EAAKA,EAAK,EAAG,EACjG,CACA,eAAIjE,GACA,IAAI,OAAEO,GAAWlU,KAAKxJ,QAClBkiB,EAAQxE,EAAOA,OAAOlU,KAAKsB,MAAQ,GACvC,OAAIoX,GAAS1Y,KAAKmZ,QAAUjF,EAAOA,OAAOlU,KAAKmZ,QAAQ7X,MAAQ,GAAK4S,EAAOA,OAAOva,QACvE,IAAI4f,EAAWvZ,KAAKxJ,QAASwJ,KAAKmZ,QAAST,GAC/C1Y,KAAKga,gBAAgB,EAChC,CACA,eAAId,GACA,IAAI,OAAEhF,GAAWlU,KAAKxJ,QAClBie,EAAczU,KAAKmZ,QAAUnZ,KAAKmZ,QAAQ7X,MAAQ,EAAI,EAC1D,OAAItB,KAAKsB,OAASmT,EACPzU,KAAKga,iBAAiB,GAC1B,IAAIT,EAAWvZ,KAAKxJ,QAASwJ,KAAKmZ,QAASjF,EAAOyD,UAAUlD,EAAazU,KAAKsB,OAAQ,EAAG,EAAG,GACvG,CACA,QAAIqO,GAAS,OAAO,IAAM,CAC1B,MAAAmK,GACI,IAAIlI,EAAW,GAAI7Q,EAAY,IAC3B,OAAEmT,GAAWlU,KAAKxJ,QAClBuhB,EAAS/X,KAAKsB,MAAQ,EAAG0W,EAAO9D,EAAOA,OAAOlU,KAAKsB,MAAQ,GAC/D,GAAI0W,EAAOD,EAAQ,CACf,IAAIhf,EAAOmb,EAAOA,OAAOlU,KAAKsB,MAAQ,GACtCsQ,EAAS/W,KAAKqZ,EAAO5gB,MAAMykB,EAAQC,EAAMjf,IACzCgI,EAAUlG,KAAK,EACnB,CACA,OAAO,IAAI8W,EAAK3R,KAAKnJ,KAAM+a,EAAU7Q,EAAWf,KAAK7G,GAAK6G,KAAKjH,KACnE,CAIA,QAAAE,GAAa,OAAO+G,KAAKxJ,QAAQ0d,OAAOuD,YAAYzX,KAAKsB,MAAQ,EAErE,SAAS6R,EAAU8G,GACf,IAAKA,EAAMtgB,OACP,OAAO,KACX,IAAIke,EAAO,EAAGqC,EAASD,EAAM,GAC7B,IAAK,IAAIhS,EAAI,EAAGA,EAAIgS,EAAMtgB,OAAQsO,IAAK,CACnC,IAAIpI,EAAOoa,EAAMhS,IACbpI,EAAK9G,KAAOmhB,EAAOnhB,MAAQ8G,EAAK1G,GAAK+gB,EAAO/gB,MAC5C+gB,EAASra,EACTgY,EAAO5P,EAEf,CACA,IAAIiN,EAAOgF,aAAkB1H,GAAY0H,EAAO5Y,MAAQ,EAAI,KAAO4Y,EAAOlH,OACtEmH,EAAWF,EAAM3mB,QAKrB,OAJI4hB,EACAiF,EAAStC,GAAQ3C,EAEjBiF,EAASC,OAAOvC,EAAM,GACnB,IAAIwC,EAAcF,EAAUD,EACvC,CACA,MAAMG,EACF,WAAAja,CAAY6Z,EAAOpa,GACfG,KAAKia,MAAQA,EACbja,KAAKH,KAAOA,CAChB,CACA,QAAIqV,GAAS,OAAO/B,EAAUnT,KAAKia,MAAQ,EAyB/C,MAAMhI,EAIF,QAAI7K,GAAS,OAAOpH,KAAKnJ,KAAKuQ,IAAM,CAIpC,WAAAhH,CAAYP,EAIZmS,EAAO,GAYH,GAXAhS,KAAKgS,KAAOA,EAIZhS,KAAKkU,OAAS,KACdlU,KAAKsa,MAAQ,GAIbta,KAAKsB,MAAQ,EACbtB,KAAKua,WAAa,KACd1a,aAAgB2S,EAChBxS,KAAKwa,UAAU3a,OAEd,CACDG,KAAKuS,MAAQ1S,EAAKrJ,QAAQwc,OAC1BhT,KAAKkU,OAASrU,EAAKrJ,QACnB,IAAK,IAAIikB,EAAI5a,EAAKsZ,QAASsB,EAAGA,EAAIA,EAAEtB,QAChCnZ,KAAKsa,MAAMI,QAAQD,EAAEnZ,OACzBtB,KAAKua,WAAa1a,EAClBG,KAAK2a,SAAS9a,EAAKyB,MACvB,CACJ,CACA,SAAAkZ,CAAU3a,GACN,QAAKA,IAELG,KAAKuS,MAAQ1S,EACbG,KAAKnJ,KAAOgJ,EAAKhJ,KACjBmJ,KAAKjH,KAAO8G,EAAK9G,KACjBiH,KAAK7G,GAAK0G,EAAK1G,IACR,EACX,CACA,QAAAwhB,CAASrZ,EAAOzK,GACZmJ,KAAKsB,MAAQA,EACb,IAAI,MAAEuT,EAAK,OAAEX,GAAWlU,KAAKkU,OAI7B,OAHAlU,KAAKnJ,KAAOA,GAAQqd,EAAOzX,IAAIwU,MAAMiD,EAAOA,OAAO5S,IACnDtB,KAAKjH,KAAO8b,EAAQX,EAAOA,OAAO5S,EAAQ,GAC1CtB,KAAK7G,GAAK0b,EAAQX,EAAOA,OAAO5S,EAAQ,IACjC,CACX,CAIA,KAAAsZ,CAAM/a,GACF,QAAKA,IAEDA,aAAgB2S,GAChBxS,KAAKkU,OAAS,KACPlU,KAAKwa,UAAU3a,KAE1BG,KAAKkU,OAASrU,EAAKrJ,QACZwJ,KAAK2a,SAAS9a,EAAKyB,MAAOzB,EAAKhJ,OAC1C,CAIA,QAAAoC,GACI,OAAO+G,KAAKkU,OAASlU,KAAKkU,OAAOA,OAAOuD,YAAYzX,KAAKsB,OAAStB,KAAKuS,MAAMtZ,UACjF,CAIA,UAAA4hB,CAAWjD,EAAK5W,EAAKoR,GACjB,IAAKpS,KAAKkU,OACN,OAAOlU,KAAK4a,MAAM5a,KAAKuS,MAAM8G,UAAUzB,EAAM,EAAI5X,KAAKuS,MAAMA,MAAMX,SAASjY,OAAS,EAAI,EAAGie,EAAK5W,EAAKoR,EAAMpS,KAAKgS,OACpH,IAAI,OAAEkC,GAAWlU,KAAKkU,OAClB5S,EAAQ4S,EAAOyD,UAAU3X,KAAKsB,MAAQ,EAAG4S,EAAOA,OAAOlU,KAAKsB,MAAQ,GAAIsW,EAAK5W,EAAMhB,KAAKkU,OAAOW,MAAOzC,GAC1G,QAAI9Q,EAAQ,KAEZtB,KAAKsa,MAAMzf,KAAKmF,KAAKsB,OACdtB,KAAK2a,SAASrZ,GACzB,CAKA,UAAAoS,GAAe,OAAO1T,KAAK6a,WAAW,EAAG,EAAG,EAAwB,CAIpE,SAAA5B,GAAc,OAAOjZ,KAAK6a,YAAY,EAAG,EAAG,EAAwB,CAIpE,UAAA3Z,CAAWF,GAAO,OAAOhB,KAAK6a,WAAW,EAAG7Z,EAAK,EAAqB,CAItE,WAAAgY,CAAYhY,GAAO,OAAOhB,KAAK6a,YAAY,EAAG7Z,GAAM,EAAsB,CAQ1E,KAAAL,CAAMK,EAAKoR,EAAMJ,EAAOhS,KAAKgS,MACzB,OAAKhS,KAAKkU,SAEHlC,EAAON,EAAS4H,iBAAyBtZ,KAAK6a,WAAW,EAAG7Z,EAAKoR,GAD7DpS,KAAK4a,MAAM5a,KAAKuS,MAAM5R,MAAMK,EAAKoR,EAAMJ,GAEtD,CAIA,MAAAgB,GACI,IAAKhT,KAAKkU,OACN,OAAOlU,KAAKwa,UAAWxa,KAAKgS,KAAON,EAAS6B,iBAAoBvT,KAAKuS,MAAM4G,QAAUnZ,KAAKuS,MAAMS,QACpG,GAAIhT,KAAKsa,MAAM3gB,OACX,OAAOqG,KAAK2a,SAAS3a,KAAKsa,MAAM3D,OACpC,IAAI3D,EAAUhT,KAAKgS,KAAON,EAAS6B,iBAAoBvT,KAAKkU,OAAOlB,OAAShT,KAAKkU,OAAOlB,OAAO4G,wBAE/F,OADA5Z,KAAKkU,OAAS,KACPlU,KAAKwa,UAAUxH,EAC1B,CAIA,OAAA8H,CAAQlD,GACJ,IAAK5X,KAAKkU,OACN,QAAQlU,KAAKuS,MAAM4G,SACbnZ,KAAK4a,MAAM5a,KAAKuS,MAAMjR,MAAQ,EAAI,KAC9BtB,KAAKuS,MAAM4G,QAAQE,UAAUrZ,KAAKuS,MAAMjR,MAAQsW,EAAKA,EAAK,EAAG,EAAuB5X,KAAKgS,OACvG,IAAI,OAAEkC,GAAWlU,KAAKkU,OAAQ6G,EAAI/a,KAAKsa,MAAM3gB,OAAS,EACtD,GAAIie,EAAM,EAAG,CACT,IAAInD,EAAcsG,EAAI,EAAI,EAAI/a,KAAKsa,MAAMS,GAAK,EAC9C,GAAI/a,KAAKsB,OAASmT,EACd,OAAOzU,KAAK2a,SAASzG,EAAOyD,UAAUlD,EAAazU,KAAKsB,OAAQ,EAAG,EAAG,GAC9E,KACK,CACD,IAAIoX,EAAQxE,EAAOA,OAAOlU,KAAKsB,MAAQ,GACvC,GAAIoX,GAASqC,EAAI,EAAI7G,EAAOA,OAAOva,OAASua,EAAOA,OAAOlU,KAAKsa,MAAMS,GAAK,IACtE,OAAO/a,KAAK2a,SAASjC,EAC7B,CACA,OAAOqC,EAAI,GAAI/a,KAAK4a,MAAM5a,KAAKkU,OAAOlB,OAAOqG,UAAUrZ,KAAKkU,OAAO5S,MAAQsW,EAAKA,EAAK,EAAG,EAAuB5X,KAAKgS,MACxH,CAIA,WAAA2B,GAAgB,OAAO3T,KAAK8a,QAAQ,EAAI,CAIxC,WAAA5B,GAAgB,OAAOlZ,KAAK8a,SAAS,EAAI,CACzC,UAAAE,CAAWpD,GACP,IAAItW,EAAO0R,GAAQ,OAAEkB,GAAWlU,KAChC,GAAIkU,EAAQ,CACR,GAAI0D,EAAM,GACN,GAAI5X,KAAKsB,MAAQ4S,EAAOA,OAAOA,OAAOva,OAClC,OAAO,OAGX,IAAK,IAAIsO,EAAI,EAAGA,EAAIjI,KAAKsB,MAAO2G,IAC5B,GAAIiM,EAAOA,OAAOA,OAAOjM,EAAI,GAAKjI,KAAKsB,MACnC,OAAO,IAEhBA,QAAO0R,UAAWkB,EACzB,OAEO5S,QAAO6X,QAASnG,GAAWhT,KAAKuS,OAEvC,KAAOS,IAAU1R,QAAO6X,QAASnG,GAAWA,GACxC,GAAI1R,GAAS,EACT,IAAK,IAAI2G,EAAI3G,EAAQsW,EAAK7Y,EAAI6Y,EAAM,GAAK,EAAI5E,EAAOT,MAAMX,SAASjY,OAAQsO,GAAKlJ,EAAGkJ,GAAK2P,EAAK,CACzF,IAAI3W,EAAQ+R,EAAOT,MAAMX,SAAS3J,GAClC,GAAKjI,KAAKgS,KAAON,EAAS6B,kBACtBtS,aAAiBwU,IAChBxU,EAAMpK,KAAK4Z,aACZgJ,EAASxY,GACT,OAAO,CACf,CAER,OAAO,CACX,CACA,IAAAga,CAAKrD,EAAKjX,GACN,GAAIA,GAASX,KAAK6a,WAAWjD,EAAK,EAAG,GACjC,OAAO,EACX,OAAS,CACL,GAAI5X,KAAK8a,QAAQlD,GACb,OAAO,EACX,GAAI5X,KAAKgb,WAAWpD,KAAS5X,KAAKgT,SAC9B,OAAO,CACf,CACJ,CAQA,IAAAkC,CAAKvU,GAAQ,GAAQ,OAAOX,KAAKib,KAAK,EAAGta,EAAQ,CAOjD,IAAAua,CAAKva,GAAQ,GAAQ,OAAOX,KAAKib,MAAM,EAAGta,EAAQ,CAMlD,MAAA2R,CAAOtR,EAAKoR,EAAO,GAEf,MAAOpS,KAAKjH,MAAQiH,KAAK7G,KACpBiZ,EAAO,EAAIpS,KAAKjH,MAAQiI,EAAMhB,KAAKjH,KAAOiI,KAC1CoR,GAAQ,EAAIpS,KAAK7G,IAAM6H,EAAMhB,KAAK7G,GAAK6H,KACnChB,KAAKgT,WAGd,KAAOhT,KAAK6a,WAAW,EAAG7Z,EAAKoR,KAC/B,OAAOpS,IACX,CAKA,QAAIH,GACA,IAAKG,KAAKkU,OACN,OAAOlU,KAAKuS,MAChB,IAAI4I,EAAQnb,KAAKua,WAAYtL,EAAS,KAAM2F,EAAQ,EACpD,GAAIuG,GAASA,EAAM3kB,SAAWwJ,KAAKkU,OAC/BnB,EAAM,IAAK,IAAIzR,EAAQtB,KAAKsB,MAAOyZ,EAAI/a,KAAKsa,MAAM3gB,OAAQohB,GAAK,GAAI,CAC/D,IAAK,IAAIvH,EAAI2H,EAAO3H,EAAGA,EAAIA,EAAE2F,QACzB,GAAI3F,EAAElS,OAASA,EAAO,CAClB,GAAIA,GAAStB,KAAKsB,MACd,OAAOkS,EACXvE,EAASuE,EACToB,EAAQmG,EAAI,EACZ,MAAMhI,CACV,CACJzR,EAAQtB,KAAKsa,QAAQS,EACzB,CAEJ,IAAK,IAAI9S,EAAI2M,EAAO3M,EAAIjI,KAAKsa,MAAM3gB,OAAQsO,IACvCgH,EAAS,IAAIsK,EAAWvZ,KAAKkU,OAAQjF,EAAQjP,KAAKsa,MAAMrS,IAC5D,OAAOjI,KAAKua,WAAa,IAAIhB,EAAWvZ,KAAKkU,OAAQjF,EAAQjP,KAAKsB,MACtE,CAMA,QAAIqO,GACA,OAAO3P,KAAKkU,OAAS,KAAOlU,KAAKuS,MAAMA,KAC3C,CAOA,OAAA7R,CAAQC,EAAO0S,GACX,IAAK,IAAIuB,EAAQ,IAAK,CAClB,IAAIwG,GAAY,EAChB,GAAIpb,KAAKnJ,KAAK4Z,cAA+B,IAAhB9P,EAAMX,MAAiB,CAChD,GAAIA,KAAK0T,aAAc,CACnBkB,IACA,QACJ,CACK5U,KAAKnJ,KAAK4Z,cACX2K,GAAY,EACpB,CACA,OAAS,CAIL,GAHIA,GAAa/H,GACbA,EAAMrT,MACVob,EAAYpb,KAAKnJ,KAAK4Z,aACjBmE,EACD,OACJ,GAAI5U,KAAK2T,cACL,MACJ3T,KAAKgT,SACL4B,IACAwG,GAAY,CAChB,CACJ,CACJ,CAMA,YAAAvC,CAAariB,GACT,IAAKwJ,KAAKkU,OACN,OAAO4E,EAAiB9Y,KAAKH,KAAKmT,OAAQxc,GAC9C,IAAI,OAAE0d,GAAWlU,KAAKkU,QAAQ,MAAEjD,GAAUiD,EAAOzX,IACjD,IAAK,IAAIwL,EAAIzR,EAAQmD,OAAS,EAAGohB,EAAI/a,KAAKsa,MAAM3gB,OAAS,EAAGsO,GAAK,EAAG8S,IAAK,CACrE,GAAIA,EAAI,EACJ,OAAOjC,EAAiB9Y,KAAKuS,MAAO/b,EAASyR,GACjD,IAAIpR,EAAOoa,EAAMiD,EAAOA,OAAOlU,KAAKsa,MAAMS,KAC1C,IAAKlkB,EAAK4Z,YAAa,CACnB,GAAIja,EAAQyR,IAAMzR,EAAQyR,IAAMpR,EAAKuQ,KACjC,OAAO,EACXa,GACJ,CACJ,CACA,OAAO,CACX,EAEJ,SAASwR,EAAS9J,GACd,OAAOA,EAAKiC,SAASyJ,KAAKxJ,GAAMA,aAAc4D,IAAe5D,EAAGhb,KAAK4Z,aAAegJ,EAAS5H,GACjG,CAgOA,MAAMyJ,EAAgB,IAAI9J,QAC1B,SAASyF,EAASsE,EAAa1b,GAC3B,IAAK0b,EAAY9K,aAAe5Q,aAAgB4V,GAAc5V,EAAKhJ,MAAQ0kB,EACvE,OAAO,EACX,IAAIxG,EAAOuG,EAAczL,IAAIhQ,GAC7B,GAAY,MAARkV,EAAc,CACdA,EAAO,EACP,IAAK,IAAI9T,KAASpB,EAAK+R,SAAU,CAC7B,GAAI3Q,EAAMpK,MAAQ0kB,KAAiBta,aAAiB0Q,GAAO,CACvDoD,EAAO,EACP,KACJ,CACAA,GAAQkC,EAASsE,EAAata,EAClC,CACAqa,EAAc7e,IAAIoD,EAAMkV,EAC5B,CACA,OAAOA,CACX,CACA,SAASjB,EAETyH,EAEA3J,EAAU7Q,EAEVhI,EAAMI,EAEN0b,EAEAlb,EAEA6hB,EAEAC,GACI,IAAIC,EAAQ,EACZ,IAAK,IAAIzT,EAAIlP,EAAMkP,EAAI9O,EAAI8O,IACvByT,GAASzE,EAASsE,EAAa3J,EAAS3J,IAC5C,IAAI0T,EAAWvD,KAAKwD,KAAc,IAARF,EAAe,GACrChG,EAAgB,GAAIC,EAAiB,GA2BzC,OA1BA,SAASkG,EAAOjK,EAAU7Q,EAAWhI,EAAMI,EAAI2iB,GAC3C,IAAK,IAAI7T,EAAIlP,EAAMkP,EAAI9O,GAAK,CACxB,IAAI4iB,EAAY9T,EAAG+T,EAAajb,EAAUkH,GAAIgU,EAAYhF,EAASsE,EAAa3J,EAAS3J,IAEzF,IADAA,IACOA,EAAI9O,EAAI8O,IAAK,CAChB,IAAIiU,EAAWjF,EAASsE,EAAa3J,EAAS3J,IAC9C,GAAIgU,EAAYC,GAAYP,EACxB,MACJM,GAAaC,CACjB,CACA,GAAIjU,GAAK8T,EAAY,EAAG,CACpB,GAAIE,EAAYN,EAAU,CACtB,IAAIQ,EAAOvK,EAASmK,GACpBF,EAAOM,EAAKvK,SAAUuK,EAAKpb,UAAW,EAAGob,EAAKvK,SAASjY,OAAQoH,EAAUgb,GAAaD,GACtF,QACJ,CACApG,EAAc7a,KAAK+W,EAASmK,GAChC,KACK,CACD,IAAIpiB,EAASoH,EAAUkH,EAAI,GAAK2J,EAAS3J,EAAI,GAAGtO,OAASqiB,EACzDtG,EAAc7a,KAAKiZ,EAAayH,EAAa3J,EAAU7Q,EAAWgb,EAAW9T,EAAG+T,EAAYriB,EAAQ,KAAM8hB,GAC9G,CACA9F,EAAe9a,KAAKmhB,EAAaF,EAASjH,EAC9C,CACJ,CACAgH,CAAOjK,EAAU7Q,EAAWhI,EAAMI,EAAI,IAC9BqiB,GAASC,GAAQ/F,EAAeC,EAAgBhc,EAC5D,CAkKA,MAAMyiB,EAWF,UAAAC,CAAWhT,EAAOiT,EAAWC,GAIzB,MAHoB,iBAATlT,IACPA,EAAQ,IAAImT,EAAYnT,IAC5BkT,EAAUA,EAAwCA,EAAO5iB,OAAS4iB,EAAOzY,IAAI6U,GAAK,IAAIlK,EAAMkK,EAAE5f,KAAM4f,EAAExf,KAAO,CAAC,IAAIsV,EAAM,EAAG,IAAxG,CAAC,IAAIA,EAAM,EAAGpF,EAAM1P,SAChCqG,KAAKyc,YAAYpT,EAAOiT,GAAa,GAAIC,EACpD,CAIA,KAAA9b,CAAM4I,EAAOiT,EAAWC,GACpB,IAAI9b,EAAQT,KAAKqc,WAAWhT,EAAOiT,EAAWC,GAC9C,OAAS,CACL,IAAIG,EAAOjc,EAAMkc,UACjB,GAAID,EACA,OAAOA,CACf,CACJ,EAEJ,MAAMF,EACF,WAAApc,CAAY2G,GACR/G,KAAK+G,OAASA,CAClB,CACA,UAAIpN,GAAW,OAAOqG,KAAK+G,OAAOpN,MAAQ,CAC1C,KAAAijB,CAAM7jB,GAAQ,OAAOiH,KAAK+G,OAAOzT,MAAMyF,EAAO,CAC9C,cAAI8jB,GAAe,OAAO,CAAO,CACjC,IAAAC,CAAK/jB,EAAMI,GAAM,OAAO6G,KAAK+G,OAAOzT,MAAMyF,EAAMI,EAAK,EAuCpC,IAAIuV,EAAS,CAAEE,SAAS,ICrvD7C,MAAMmO,EAIF,WAAA3c,CAIAwK,EAKA0P,EAIA0C,EAQAC,EAIAjc,EAMAkc,EAOAhJ,EASAiJ,EAIAC,EAIA5N,EAAY,EAQZwD,GACIhT,KAAK4K,EAAIA,EACT5K,KAAKsa,MAAQA,EACbta,KAAKgd,MAAQA,EACbhd,KAAKid,UAAYA,EACjBjd,KAAKgB,IAAMA,EACXhB,KAAKkd,MAAQA,EACbld,KAAKkU,OAASA,EACdlU,KAAKmd,WAAaA,EAClBnd,KAAKod,WAAaA,EAClBpd,KAAKwP,UAAYA,EACjBxP,KAAKgT,OAASA,CAClB,CAIA,QAAA/Z,GACI,MAAO,IAAI+G,KAAKsa,MAAMjX,OAAO,CAACga,EAAGpV,IAAMA,EAAI,GAAK,GAAG4O,OAAO7W,KAAKgd,WAAWhd,KAAKgB,MAAMhB,KAAKkd,MAAQ,IAAMld,KAAKkd,MAAQ,IACzH,CAKA,YAAOrI,CAAMjK,EAAGoS,EAAOhc,EAAM,GACzB,IAAIsc,EAAK1S,EAAEvQ,OAAO7D,QAClB,OAAO,IAAIumB,EAAMnS,EAAG,GAAIoS,EAAOhc,EAAKA,EAAK,EAAG,GAAI,EAAGsc,EAAK,IAAIC,EAAaD,EAAIA,EAAGzI,OAAS,KAAM,EAAG,KACtG,CAOA,WAAIre,GAAY,OAAOwJ,KAAKod,WAAapd,KAAKod,WAAW5mB,QAAU,IAAM,CAMzE,SAAAgnB,CAAUR,EAAOnI,GACb7U,KAAKsa,MAAMzf,KAAKmF,KAAKgd,MAAOnI,EAAO7U,KAAKmd,WAAand,KAAKkU,OAAOva,QACjEqG,KAAKgd,MAAQA,CACjB,CAKA,MAAAS,CAAOC,GACH,IAAIzJ,EACJ,IAAIW,EAAQ8I,GAAU,GAAkC7mB,EAAgB,MAAT6mB,GAC3D,OAAErjB,GAAW2F,KAAK4K,EAClB+S,EAAkB3d,KAAKid,UAAYjd,KAAKgB,IAAM,GAC9C2c,GACA3d,KAAK4d,aAAa5d,KAAKgB,KAC3B,IAAI6c,EAAQxjB,EAAOyjB,kBAAkBjnB,GAGrC,GAFIgnB,IACA7d,KAAKkd,OAASW,GACL,GAATjJ,EAOA,OANA5U,KAAKwd,UAAUnjB,EAAO0jB,QAAQ/d,KAAKgd,MAAOnmB,GAAM,GAAOmJ,KAAKid,WAGxDpmB,EAAOwD,EAAO2jB,eACdhe,KAAKie,UAAUpnB,EAAMmJ,KAAKid,UAAWjd,KAAKid,UAAWU,EAAkB,EAAI,GAAG,QAClF3d,KAAKke,cAAcrnB,EAAMmJ,KAAKid,WAQlC,IAAIvG,EAAO1W,KAAKsa,MAAM3gB,OAAwB,GAAbib,EAAQ,IAAoB,OAAT8I,EAAwC,EAAI,GAC5F7I,EAAQ6B,EAAO1W,KAAKsa,MAAM5D,EAAO,GAAK1W,KAAK4K,EAAE2R,OAAO,GAAGxjB,KAAMgc,EAAO/U,KAAKid,UAAYpI,EAIrFE,GAAQ,OAAqF,QAA5Cd,EAAKjU,KAAK4K,EAAEvQ,OAAO8Z,QAAQlD,MAAMpa,UAA0B,IAAPod,OAAgB,EAASA,EAAGxD,eAC7HoE,GAAS7U,KAAK4K,EAAEuT,uBAChBne,KAAK4K,EAAEwT,oBACPpe,KAAK4K,EAAEyT,qBAAuBtJ,GAEzB/U,KAAK4K,EAAEyT,qBAAuBtJ,IACnC/U,KAAK4K,EAAEwT,kBAAoB,EAC3Bpe,KAAK4K,EAAEuT,sBAAwBtJ,EAC/B7U,KAAK4K,EAAEyT,qBAAuBtJ,IAGtC,IAAIoI,EAAazG,EAAO1W,KAAKsa,MAAM5D,EAAO,GAAK,EAAG4H,EAAQte,KAAKmd,WAAand,KAAKkU,OAAOva,OAASwjB,EAEjG,GAAItmB,EAAOwD,EAAO2jB,eAA2B,OAATN,EAA0C,CAC1E,IAAI1c,EAAM3G,EAAOkkB,UAAUve,KAAKgd,MAAO,GAA6Bhd,KAAKgB,IAAMhB,KAAKid,UACpFjd,KAAKie,UAAUpnB,EAAMge,EAAO7T,EAAKsd,EAAQ,GAAG,EAChD,CACA,GAAa,OAATZ,EACA1d,KAAKgd,MAAQhd,KAAKsa,MAAM5D,OAEvB,CACD,IAAI8H,EAAcxe,KAAKsa,MAAM5D,EAAO,GACpC1W,KAAKgd,MAAQ3iB,EAAO0jB,QAAQS,EAAa3nB,GAAM,EACnD,CACA,KAAOmJ,KAAKsa,MAAM3gB,OAAS+c,GACvB1W,KAAKsa,MAAM3D,MACf3W,KAAKke,cAAcrnB,EAAMge,EAC7B,CAKA,SAAAoJ,CAAUQ,EAAM5J,EAAOC,EAAKC,EAAO,EAAG2J,GAAW,GAC7C,GAAY,GAARD,KACEze,KAAKsa,MAAM3gB,QAAUqG,KAAKsa,MAAMta,KAAKsa,MAAM3gB,OAAS,GAAKqG,KAAKkU,OAAOva,OAASqG,KAAKmd,YAAa,CAElG,IAAIpD,EAAM/Z,KAAMmQ,EAAMnQ,KAAKkU,OAAOva,OAKlC,GAJW,GAAPwW,GAAY4J,EAAI/G,SAChB7C,EAAM4J,EAAIoD,WAAapD,EAAI/G,OAAOmK,WAClCpD,EAAMA,EAAI/G,QAEV7C,EAAM,GAA4B,GAAvB4J,EAAI7F,OAAO/D,EAAM,IAA0B4J,EAAI7F,OAAO/D,EAAM,IAAM,EAAG,CAChF,GAAI0E,GAASC,EACT,OACJ,GAAIiF,EAAI7F,OAAO/D,EAAM,IAAM0E,EAEvB,YADAkF,EAAI7F,OAAO/D,EAAM,GAAK2E,EAG9B,CACJ,CACA,GAAK4J,GAAY1e,KAAKgB,KAAO8T,EAGxB,CACD,IAAIxT,EAAQtB,KAAKkU,OAAOva,OACxB,GAAI2H,EAAQ,GAA+B,GAA1BtB,KAAKkU,OAAO5S,EAAQ,GAAwB,CACzD,IAAIqd,GAAW,EACf,IAAK,IAAI5L,EAAOzR,EAAOyR,EAAO,GAAK/S,KAAKkU,OAAOnB,EAAO,GAAK+B,EAAK/B,GAAQ,EACpE,GAAI/S,KAAKkU,OAAOnB,EAAO,IAAM,EAAG,CAC5B4L,GAAW,EACX,KACJ,CAEJ,GAAIA,EACA,KAAOrd,EAAQ,GAAKtB,KAAKkU,OAAO5S,EAAQ,GAAKwT,GAEzC9U,KAAKkU,OAAO5S,GAAStB,KAAKkU,OAAO5S,EAAQ,GACzCtB,KAAKkU,OAAO5S,EAAQ,GAAKtB,KAAKkU,OAAO5S,EAAQ,GAC7CtB,KAAKkU,OAAO5S,EAAQ,GAAKtB,KAAKkU,OAAO5S,EAAQ,GAC7CtB,KAAKkU,OAAO5S,EAAQ,GAAKtB,KAAKkU,OAAO5S,EAAQ,GAC7CA,GAAS,EACLyT,EAAO,IACPA,GAAQ,EAExB,CACA/U,KAAKkU,OAAO5S,GAASmd,EACrBze,KAAKkU,OAAO5S,EAAQ,GAAKuT,EACzB7U,KAAKkU,OAAO5S,EAAQ,GAAKwT,EACzB9U,KAAKkU,OAAO5S,EAAQ,GAAKyT,CAC7B,MA5BI/U,KAAKkU,OAAOrZ,KAAK4jB,EAAM5J,EAAOC,EAAKC,EA6B3C,CAKA,KAAA6J,CAAMlB,EAAQ7mB,EAAMge,EAAOC,GACvB,GAAa,OAAT4I,EACA1d,KAAKwd,UAAmB,MAATE,EAAuC1d,KAAKgB,UAE1D,GAAc,OAAT0c,EAaN1d,KAAKgB,IAAM8T,EACX9U,KAAK6e,aAAahoB,EAAMge,GACpBhe,GAAQmJ,KAAK4K,EAAEvQ,OAAOykB,SACtB9e,KAAKkU,OAAOrZ,KAAKhE,EAAMge,EAAOC,EAAK,OAhBY,CACnD,IAAIiK,EAAYrB,GAAQ,OAAErjB,GAAW2F,KAAK4K,GACtCkK,EAAM9U,KAAKgB,KAAOnK,GAAQwD,EAAOykB,WACjC9e,KAAKgB,IAAM8T,EACNza,EAAOkkB,UAAUQ,EAAW,KAC7B/e,KAAKid,UAAYnI,IAEzB9U,KAAKwd,UAAUuB,EAAWlK,GAC1B7U,KAAK6e,aAAahoB,EAAMge,GACpBhe,GAAQwD,EAAOykB,SACf9e,KAAKkU,OAAOrZ,KAAKhE,EAAMge,EAAOC,EAAK,EAC3C,CAOJ,CAKA,KAAAkK,CAAMtB,EAAQxI,EAAM+J,EAAWC,GACd,MAATxB,EACA1d,KAAKyd,OAAOC,GAEZ1d,KAAK4e,MAAMlB,EAAQxI,EAAM+J,EAAWC,EAC5C,CAKA,OAAAC,CAAQlnB,EAAOid,GACX,IAAI5T,EAAQtB,KAAK4K,EAAEyJ,OAAO1a,OAAS,GAC/B2H,EAAQ,GAAKtB,KAAK4K,EAAEyJ,OAAO/S,IAAUrJ,KACrC+H,KAAK4K,EAAEyJ,OAAOxZ,KAAK5C,GACnBqJ,KAEJ,IAAIuT,EAAQ7U,KAAKgB,IACjBhB,KAAKid,UAAYjd,KAAKgB,IAAM6T,EAAQ5c,EAAM0B,OAC1CqG,KAAKwd,UAAUtI,EAAML,GACrB7U,KAAKkU,OAAOrZ,KAAKyG,EAAOuT,EAAO7U,KAAKid,WAAY,GAC5Cjd,KAAKod,YACLpd,KAAKof,cAAcpf,KAAKod,WAAWiC,QAAQC,MAAMtf,KAAKod,WAAW5mB,QAASyB,EAAO+H,KAAMA,KAAK4K,EAAE2U,OAAOC,MAAMxf,KAAKgB,IAAM/I,EAAM0B,SACpI,CAOA,KAAAzB,GACI,IAAI8a,EAAShT,KACTyf,EAAMzM,EAAOkB,OAAOva,OAKxB,KAAO8lB,EAAM,GAAKzM,EAAOkB,OAAOuL,EAAM,GAAKzM,EAAOiK,WAC9CwC,GAAO,EACX,IAAIvL,EAASlB,EAAOkB,OAAO5gB,MAAMmsB,GAAM/I,EAAO1D,EAAOmK,WAAasC,EAElE,KAAOzM,GAAU0D,GAAQ1D,EAAOmK,YAC5BnK,EAASA,EAAOA,OACpB,OAAO,IAAI+J,EAAM/c,KAAK4K,EAAG5K,KAAKsa,MAAMhnB,QAAS0M,KAAKgd,MAAOhd,KAAKid,UAAWjd,KAAKgB,IAAKhB,KAAKkd,MAAOhJ,EAAQwC,EAAM1W,KAAKod,WAAYpd,KAAKwP,UAAWwD,EAClJ,CAKA,eAAA0M,CAAgBxK,EAAMgK,GAClB,IAAIS,EAASzK,GAAQlV,KAAK4K,EAAEvQ,OAAOykB,QAC/Ba,GACA3f,KAAKie,UAAU/I,EAAMlV,KAAKgB,IAAKke,EAAS,GAC5Clf,KAAKie,UAAU,EAAkBje,KAAKgB,IAAKke,EAASS,EAAS,EAAI,GACjE3f,KAAKgB,IAAMhB,KAAKid,UAAYiC,EAC5Blf,KAAKkd,OAAS,GAClB,CAOA,QAAA0C,CAASnB,GACL,IAAK,IAAIoB,EAAM,IAAIC,EAAe9f,QAAS,CACvC,IAAI0d,EAAS1d,KAAK4K,EAAEvQ,OAAO0lB,UAAUF,EAAI7C,MAAO,IAAqChd,KAAK4K,EAAEvQ,OAAO2lB,UAAUH,EAAI7C,MAAOyB,GACxH,GAAc,GAAVf,EACA,OAAO,EACX,KAAc,MAATA,GACD,OAAO,EACXmC,EAAIpC,OAAOC,EACf,CACJ,CAMA,eAAAuC,CAAgB/K,GACZ,GAAIlV,KAAKsa,MAAM3gB,QAAU,IACrB,MAAO,GACX,IAAIumB,EAAalgB,KAAK4K,EAAEvQ,OAAO6lB,WAAWlgB,KAAKgd,OAC/C,GAAIkD,EAAWvmB,OAAS,GAAgCqG,KAAKsa,MAAM3gB,QAAU,IAA0C,CACnH,IAAIwmB,EAAO,GACX,IAAK,IAAWxY,EAAPM,EAAI,EAAMA,EAAIiY,EAAWvmB,OAAQsO,GAAK,GACtCN,EAAIuY,EAAWjY,EAAI,KAAOjI,KAAKgd,OAAShd,KAAK4K,EAAEvQ,OAAO2lB,UAAUrY,EAAGuN,IACpEiL,EAAKtlB,KAAKqlB,EAAWjY,GAAIN,GAEjC,GAAI3H,KAAKsa,MAAM3gB,OAAS,IACpB,IAAK,IAAIsO,EAAI,EAAGkY,EAAKxmB,OAAS,GAAgCsO,EAAIiY,EAAWvmB,OAAQsO,GAAK,EAAG,CACzF,IAAIN,EAAIuY,EAAWjY,EAAI,GAClBkY,EAAK9E,KAAK,CAAC+E,EAAGnY,IAAW,EAAJA,GAAUmY,GAAKzY,IACrCwY,EAAKtlB,KAAKqlB,EAAWjY,GAAIN,EACjC,CACJuY,EAAaC,CACjB,CACA,IAAIlR,EAAS,GACb,IAAK,IAAIhH,EAAI,EAAGA,EAAIiY,EAAWvmB,QAAUsV,EAAOtV,OAAS,EAAyBsO,GAAK,EAAG,CACtF,IAAIN,EAAIuY,EAAWjY,EAAI,GACvB,GAAIN,GAAK3H,KAAKgd,MACV,SACJ,IAAI1C,EAAQta,KAAK9H,QACjBoiB,EAAMkD,UAAU7V,EAAG3H,KAAKgB,KACxBsZ,EAAM2D,UAAU,EAAkB3D,EAAMtZ,IAAKsZ,EAAMtZ,IAAK,GAAG,GAC3DsZ,EAAMuE,aAAaqB,EAAWjY,GAAIjI,KAAKgB,KACvCsZ,EAAM2C,UAAYjd,KAAKgB,IACvBsZ,EAAM4C,OAAS,IACfjO,EAAOpU,KAAKyf,EAChB,CACA,OAAOrL,CACX,CAMA,WAAAoR,GACI,IAAI,OAAEhmB,GAAW2F,KAAK4K,EAClB6S,EAASpjB,EAAO0lB,UAAU/f,KAAKgd,MAAO,GAC1C,KAAc,MAATS,GACD,OAAO,EACX,IAAKpjB,EAAOimB,YAAYtgB,KAAKgd,MAAOS,GAAS,CACzC,IAAI7I,EAAQ6I,GAAU,GAAkCgB,EAAgB,MAAThB,EAC3D9mB,EAASqJ,KAAKsa,MAAM3gB,OAAiB,EAARib,EACjC,GAAIje,EAAS,GAAK0D,EAAO0jB,QAAQ/d,KAAKsa,MAAM3jB,GAAS8nB,GAAM,GAAS,EAAG,CACnE,IAAI8B,EAASvgB,KAAKwgB,sBAClB,GAAc,MAAVD,EACA,OAAO,EACX9C,EAAS8C,CACb,CACAvgB,KAAKie,UAAU,EAAkBje,KAAKgB,IAAKhB,KAAKgB,IAAK,GAAG,GACxDhB,KAAKkd,OAAS,GAClB,CAGA,OAFAld,KAAKid,UAAYjd,KAAKgB,IACtBhB,KAAKyd,OAAOA,IACL,CACX,CAMA,mBAAA+C,GACI,IAAI,OAAEnmB,GAAW2F,KAAK4K,EAAG6V,EAAO,GAC5BC,EAAU,CAAC1D,EAAOpI,KAClB,IAAI6L,EAAK5f,SAASmc,GAGlB,OADAyD,EAAK5lB,KAAKmiB,GACH3iB,EAAOsmB,WAAW3D,EAAQU,IAC7B,GAAa,OAATA,QACC,GAAa,MAATA,EAAwC,CAC7C,IAAIkD,GAAUlD,GAAU,IAAoC9I,EAC5D,GAAIgM,EAAS,EAAG,CACZ,IAAInC,EAAgB,MAATf,EAAuC/mB,EAASqJ,KAAKsa,MAAM3gB,OAAkB,EAATinB,EAC/E,GAAIjqB,GAAU,GAAK0D,EAAO0jB,QAAQ/d,KAAKsa,MAAM3jB,GAAS8nB,GAAM,IAAU,EAClE,OAAQmC,GAAU,GAAoC,MAAgCnC,CAC9F,CACJ,KACK,CACD,IAAI3N,EAAQ4P,EAAQhD,EAAQ9I,EAAQ,GACpC,GAAa,MAAT9D,EACA,OAAOA,CACf,KAGR,OAAO4P,EAAQ1gB,KAAKgd,MAAO,EAC/B,CAIA,QAAA6D,GACI,MAAQ7gB,KAAK4K,EAAEvQ,OAAOkkB,UAAUve,KAAKgd,MAAO,IACxC,IAAKhd,KAAKqgB,cAAe,CACrBrgB,KAAKie,UAAU,EAAkBje,KAAKgB,IAAKhB,KAAKgB,IAAK,GAAG,GACxD,KACJ,CAEJ,OAAOhB,IACX,CAMA,WAAI8gB,GACA,GAAyB,GAArB9gB,KAAKsa,MAAM3gB,OACX,OAAO,EACX,IAAI,OAAEU,GAAW2F,KAAK4K,EACtB,OAAgF,OAAzEvQ,EAAOkF,KAAKlF,EAAO0lB,UAAU/f,KAAKgd,MAAO,MAC3C3iB,EAAO0lB,UAAU/f,KAAKgd,MAAO,EACtC,CAMA,OAAA+D,GACI/gB,KAAKie,UAAU,EAAkBje,KAAKgB,IAAKhB,KAAKgB,IAAK,GAAG,GACxDhB,KAAKgd,MAAQhd,KAAKsa,MAAM,GACxBta,KAAKsa,MAAM3gB,OAAS,CACxB,CAIA,SAAAqnB,CAAUC,GACN,GAAIjhB,KAAKgd,OAASiE,EAAMjE,OAAShd,KAAKsa,MAAM3gB,QAAUsnB,EAAM3G,MAAM3gB,OAC9D,OAAO,EACX,IAAK,IAAIsO,EAAI,EAAGA,EAAIjI,KAAKsa,MAAM3gB,OAAQsO,GAAK,EACxC,GAAIjI,KAAKsa,MAAMrS,IAAMgZ,EAAM3G,MAAMrS,GAC7B,OAAO,EACf,OAAO,CACX,CAIA,UAAI5N,GAAW,OAAO2F,KAAK4K,EAAEvQ,MAAQ,CAKrC,cAAA6mB,CAAeC,GAAa,OAAOnhB,KAAK4K,EAAEvQ,OAAO+mB,QAAQpR,MAAMmR,EAAY,CAC3E,YAAAtC,CAAaJ,EAAM5J,GACX7U,KAAKod,YACLpd,KAAKof,cAAcpf,KAAKod,WAAWiC,QAAQT,MAAM5e,KAAKod,WAAW5mB,QAASioB,EAAMze,KAAMA,KAAK4K,EAAE2U,OAAOC,MAAM3K,IAClH,CACA,aAAAqJ,CAAcO,EAAM5J,GACZ7U,KAAKod,YACLpd,KAAKof,cAAcpf,KAAKod,WAAWiC,QAAQ5B,OAAOzd,KAAKod,WAAW5mB,QAASioB,EAAMze,KAAMA,KAAK4K,EAAE2U,OAAOC,MAAM3K,IACnH,CAIA,WAAAwM,GACI,IAAI9K,EAAOvW,KAAKkU,OAAOva,OAAS,GAC5B4c,EAAO,IAA2B,GAAtBvW,KAAKkU,OAAOqC,KACxBvW,KAAKkU,OAAOrZ,KAAKmF,KAAKod,WAAWkE,KAAMthB,KAAKgB,IAAKhB,KAAKgB,KAAM,EACpE,CAIA,aAAAugB,GACI,IAAIhL,EAAOvW,KAAKkU,OAAOva,OAAS,GAC5B4c,EAAO,IAA2B,GAAtBvW,KAAKkU,OAAOqC,KACxBvW,KAAKkU,OAAOrZ,KAAKmF,KAAKwP,UAAWxP,KAAKgB,IAAKhB,KAAKgB,KAAM,EAC9D,CACA,aAAAoe,CAAc5oB,GACV,GAAIA,GAAWwJ,KAAKod,WAAW5mB,QAAS,CACpC,IAAIgrB,EAAQ,IAAIjE,EAAavd,KAAKod,WAAWiC,QAAS7oB,GAClDgrB,EAAMF,MAAQthB,KAAKod,WAAWkE,MAC9BthB,KAAKqhB,cACTrhB,KAAKod,WAAaoE,CACtB,CACJ,CAIA,YAAA5D,CAAapO,GACLA,EAAYxP,KAAKwP,YACjBxP,KAAKuhB,gBACLvhB,KAAKwP,UAAYA,EAEzB,CAIA,KAAAiS,GACQzhB,KAAKod,YAAcpd,KAAKod,WAAWiC,QAAQqC,QAC3C1hB,KAAKqhB,cACLrhB,KAAKwP,UAAY,GACjBxP,KAAKuhB,eACb,EAEJ,MAAMhE,EACF,WAAAnd,CAAYif,EAAS7oB,GACjBwJ,KAAKqf,QAAUA,EACfrf,KAAKxJ,QAAUA,EACfwJ,KAAKshB,KAAOjC,EAAQqC,OAASrC,EAAQiC,KAAK9qB,GAAW,CACzD,EAIJ,MAAMspB,EACF,WAAA1f,CAAYyU,GACR7U,KAAK6U,MAAQA,EACb7U,KAAKgd,MAAQnI,EAAMmI,MACnBhd,KAAKsa,MAAQzF,EAAMyF,MACnBta,KAAK0W,KAAO1W,KAAKsa,MAAM3gB,MAC3B,CACA,MAAA8jB,CAAOC,GACH,IAAIe,EAAgB,MAATf,EAAuC9I,EAAQ8I,GAAU,GACvD,GAAT9I,GACI5U,KAAKsa,OAASta,KAAK6U,MAAMyF,QACzBta,KAAKsa,MAAQta,KAAKsa,MAAMhnB,SAC5B0M,KAAKsa,MAAMzf,KAAKmF,KAAKgd,MAAO,EAAG,GAC/Bhd,KAAK0W,MAAQ,GAGb1W,KAAK0W,MAAsB,GAAb9B,EAAQ,GAE1B,IAAI+M,EAAO3hB,KAAK6U,MAAMjK,EAAEvQ,OAAO0jB,QAAQ/d,KAAKsa,MAAMta,KAAK0W,KAAO,GAAI+H,GAAM,GACxEze,KAAKgd,MAAQ2E,CACjB,EAIJ,MAAMC,EACF,WAAAxhB,CAAYka,EAAOtZ,EAAKM,GACpBtB,KAAKsa,MAAQA,EACbta,KAAKgB,IAAMA,EACXhB,KAAKsB,MAAQA,EACbtB,KAAKkU,OAASoG,EAAMpG,OACF,GAAdlU,KAAKsB,OACLtB,KAAK6hB,WACb,CACA,aAAO9R,CAAOuK,EAAOtZ,EAAMsZ,EAAM6C,WAAa7C,EAAMpG,OAAOva,QACvD,OAAO,IAAIioB,EAAkBtH,EAAOtZ,EAAKA,EAAMsZ,EAAM6C,WACzD,CACA,SAAA0E,GACI,IAAI3M,EAAOlV,KAAKsa,MAAMtH,OACV,MAARkC,IACAlV,KAAKsB,MAAQtB,KAAKsa,MAAM6C,WAAajI,EAAKiI,WAC1Cnd,KAAKsa,MAAQpF,EACblV,KAAKkU,OAASgB,EAAKhB,OAE3B,CACA,MAAIxf,GAAO,OAAOsL,KAAKkU,OAAOlU,KAAKsB,MAAQ,EAAI,CAC/C,SAAIuT,GAAU,OAAO7U,KAAKkU,OAAOlU,KAAKsB,MAAQ,EAAI,CAClD,OAAIwT,GAAQ,OAAO9U,KAAKkU,OAAOlU,KAAKsB,MAAQ,EAAI,CAChD,QAAIyT,GAAS,OAAO/U,KAAKkU,OAAOlU,KAAKsB,MAAQ,EAAI,CACjD,IAAA4T,GACIlV,KAAKsB,OAAS,EACdtB,KAAKgB,KAAO,EACM,GAAdhB,KAAKsB,OACLtB,KAAK6hB,WACb,CACA,IAAA9K,GACI,OAAO,IAAI6K,EAAkB5hB,KAAKsa,MAAOta,KAAKgB,IAAKhB,KAAKsB,MAC5D,EAKJ,SAASwgB,EAAYzY,EAAO0Y,EAAO1M,aAC/B,GAAoB,iBAAThM,EACP,OAAOA,EACX,IAAIkB,EAAQ,KACZ,IAAK,IAAIvJ,EAAM,EAAGghB,EAAM,EAAGhhB,EAAMqI,EAAM1P,QAAS,CAC5C,IAAI1B,EAAQ,EACZ,OAAS,CACL,IAAIid,EAAO7L,EAAM4Y,WAAWjhB,KAAQkhB,GAAO,EAC3C,GAAY,KAARhN,EAAqC,CACrCjd,EAAQ,MACR,KACJ,CACIid,GAAQ,IACRA,IACAA,GAAQ,IACRA,IACJ,IAAIiN,EAAQjN,EAAO,GAMnB,GALIiN,GAAS,KACTA,GAAS,GACTD,GAAO,GAEXjqB,GAASkqB,EACLD,EACA,MACJjqB,GAAS,EACb,CACIsS,EACAA,EAAMyX,KAAS/pB,EAEfsS,EAAQ,IAAIwX,EAAK9pB,EACzB,CACA,OAAOsS,CACX,CAEA,MAAM6X,EACF,WAAAhiB,GACIJ,KAAK6U,OAAS,EACd7U,KAAK/H,OAAS,EACd+H,KAAK8U,KAAO,EACZ9U,KAAKqiB,UAAY,EACjBriB,KAAKwP,UAAY,EACjBxP,KAAKsiB,KAAO,EACZtiB,KAAKxJ,QAAU,CACnB,EAEJ,MAAM+rB,EAAY,IAAIH,EAOtB,MAAMI,EAIF,WAAApiB,CAIAiJ,EAIAkT,GACIvc,KAAKqJ,MAAQA,EACbrJ,KAAKuc,OAASA,EAIdvc,KAAK4c,MAAQ,GAIb5c,KAAKyiB,SAAW,EAIhBziB,KAAK0iB,OAAS,GACd1iB,KAAK2iB,UAAY,EAKjB3iB,KAAKkV,MAAQ,EAIblV,KAAK4iB,MAAQL,EACbviB,KAAK6iB,WAAa,EAClB7iB,KAAKgB,IAAMhB,KAAK8iB,SAAWvG,EAAO,GAAGxjB,KACrCiH,KAAK+I,MAAQwT,EAAO,GACpBvc,KAAK8U,IAAMyH,EAAOA,EAAO5iB,OAAS,GAAGR,GACrC6G,KAAK+iB,UACT,CAIA,aAAAC,CAAclH,EAAQmH,GAClB,IAAIla,EAAQ/I,KAAK+I,MAAOzH,EAAQtB,KAAK6iB,WACjC7hB,EAAMhB,KAAKgB,IAAM8a,EACrB,KAAO9a,EAAM+H,EAAMhQ,MAAM,CACrB,IAAKuI,EACD,OAAO,KACX,IAAI4T,EAAOlV,KAAKuc,SAASjb,GACzBN,GAAO+H,EAAMhQ,KAAOmc,EAAK/b,GACzB4P,EAAQmM,CACZ,CACA,KAAO+N,EAAQ,EAAIjiB,EAAM+H,EAAM5P,GAAK6H,GAAO+H,EAAM5P,IAAI,CACjD,GAAImI,GAAStB,KAAKuc,OAAO5iB,OAAS,EAC9B,OAAO,KACX,IAAIub,EAAOlV,KAAKuc,SAASjb,GACzBN,GAAOkU,EAAKnc,KAAOgQ,EAAM5P,GACzB4P,EAAQmM,CACZ,CACA,OAAOlU,CACX,CAIA,OAAAkiB,CAAQliB,GACJ,GAAIA,GAAOhB,KAAK+I,MAAMhQ,MAAQiI,EAAMhB,KAAK+I,MAAM5P,GAC3C,OAAO6H,EACX,IAAK,IAAI+H,KAAS/I,KAAKuc,OACnB,GAAIxT,EAAM5P,GAAK6H,EACX,OAAOoX,KAAKC,IAAIrX,EAAK+H,EAAMhQ,MACnC,OAAOiH,KAAK8U,GAChB,CAYA,IAAAqO,CAAKrH,GACD,IAAkC9a,EAAKiO,EAAnCmU,EAAMpjB,KAAKyiB,SAAW3G,EAC1B,GAAIsH,GAAO,GAAKA,EAAMpjB,KAAK4c,MAAMjjB,OAC7BqH,EAAMhB,KAAKgB,IAAM8a,EACjB7M,EAASjP,KAAK4c,MAAMqF,WAAWmB,OAE9B,CACD,IAAIC,EAAWrjB,KAAKgjB,cAAclH,EAAQ,GAC1C,GAAgB,MAAZuH,EACA,OAAQ,EAEZ,GADAriB,EAAMqiB,EACFriB,GAAOhB,KAAK2iB,WAAa3hB,EAAMhB,KAAK2iB,UAAY3iB,KAAK0iB,OAAO/oB,OAC5DsV,EAASjP,KAAK0iB,OAAOT,WAAWjhB,EAAMhB,KAAK2iB,eAE1C,CACD,IAAI1a,EAAIjI,KAAK6iB,WAAY9Z,EAAQ/I,KAAK+I,MACtC,KAAOA,EAAM5P,IAAM6H,GACf+H,EAAQ/I,KAAKuc,SAAStU,GAC1BjI,KAAK0iB,OAAS1iB,KAAKqJ,MAAMuT,MAAM5c,KAAK2iB,UAAY3hB,GAC5CA,EAAMhB,KAAK0iB,OAAO/oB,OAASoP,EAAM5P,KACjC6G,KAAK0iB,OAAS1iB,KAAK0iB,OAAOpvB,MAAM,EAAGyV,EAAM5P,GAAK6H,IAClDiO,EAASjP,KAAK0iB,OAAOT,WAAW,EACpC,CACJ,CAGA,OAFIjhB,GAAOhB,KAAK4iB,MAAMpT,YAClBxP,KAAK4iB,MAAMpT,UAAYxO,EAAM,GAC1BiO,CACX,CAMA,WAAAqU,CAAYV,EAAOW,EAAY,GAC3B,IAAIzO,EAAMyO,EAAYvjB,KAAKgjB,cAAcO,GAAY,GAAKvjB,KAAKgB,IAC/D,GAAW,MAAP8T,GAAeA,EAAM9U,KAAK4iB,MAAM/N,MAChC,MAAM,IAAI9F,WAAW,2BACzB/O,KAAK4iB,MAAM3qB,MAAQ2qB,EACnB5iB,KAAK4iB,MAAM9N,IAAMA,CACrB,CAIA,aAAA0O,CAAcZ,EAAOrN,GACjBvV,KAAK4iB,MAAM3qB,MAAQ2qB,EACnB5iB,KAAK4iB,MAAM9N,IAAMS,CACrB,CACA,QAAAkO,GACI,GAAIzjB,KAAKgB,KAAOhB,KAAK2iB,WAAa3iB,KAAKgB,IAAMhB,KAAK2iB,UAAY3iB,KAAK0iB,OAAO/oB,OAAQ,CAC9E,IAAI,MAAEijB,EAAK,SAAEkG,GAAa9iB,KAC1BA,KAAK4c,MAAQ5c,KAAK0iB,OAClB1iB,KAAK8iB,SAAW9iB,KAAK2iB,UACrB3iB,KAAK0iB,OAAS9F,EACd5c,KAAK2iB,UAAYG,EACjB9iB,KAAKyiB,SAAWziB,KAAKgB,IAAMhB,KAAK8iB,QACpC,KACK,CACD9iB,KAAK0iB,OAAS1iB,KAAK4c,MACnB5c,KAAK2iB,UAAY3iB,KAAK8iB,SACtB,IAAIY,EAAY1jB,KAAKqJ,MAAMuT,MAAM5c,KAAKgB,KAClC8T,EAAM9U,KAAKgB,IAAM0iB,EAAU/pB,OAC/BqG,KAAK4c,MAAQ9H,EAAM9U,KAAK+I,MAAM5P,GAAKuqB,EAAUpwB,MAAM,EAAG0M,KAAK+I,MAAM5P,GAAK6G,KAAKgB,KAAO0iB,EAClF1jB,KAAK8iB,SAAW9iB,KAAKgB,IACrBhB,KAAKyiB,SAAW,CACpB,CACJ,CACA,QAAAM,GACI,OAAI/iB,KAAKyiB,UAAYziB,KAAK4c,MAAMjjB,SAC5BqG,KAAKyjB,WACDzjB,KAAKyiB,UAAYziB,KAAK4c,MAAMjjB,QACrBqG,KAAKkV,MAAQ,EAErBlV,KAAKkV,KAAOlV,KAAK4c,MAAMqF,WAAWjiB,KAAKyiB,SAClD,CAKA,OAAA9F,CAAQlC,EAAI,GAER,IADAza,KAAKyiB,UAAYhI,EACVza,KAAKgB,IAAMyZ,GAAKza,KAAK+I,MAAM5P,IAAI,CAClC,GAAI6G,KAAK6iB,YAAc7iB,KAAKuc,OAAO5iB,OAAS,EACxC,OAAOqG,KAAK2jB,UAChBlJ,GAAKza,KAAK+I,MAAM5P,GAAK6G,KAAKgB,IAC1BhB,KAAK+I,MAAQ/I,KAAKuc,SAASvc,KAAK6iB,YAChC7iB,KAAKgB,IAAMhB,KAAK+I,MAAMhQ,IAC1B,CAIA,OAHAiH,KAAKgB,KAAOyZ,EACRza,KAAKgB,KAAOhB,KAAK4iB,MAAMpT,YACvBxP,KAAK4iB,MAAMpT,UAAYxP,KAAKgB,IAAM,GAC/BhB,KAAK+iB,UAChB,CACA,OAAAY,GAII,OAHA3jB,KAAKgB,IAAMhB,KAAK8iB,SAAW9iB,KAAK8U,IAChC9U,KAAK+I,MAAQ/I,KAAKuc,OAAOvc,KAAK6iB,WAAa7iB,KAAKuc,OAAO5iB,OAAS,GAChEqG,KAAK4c,MAAQ,GACN5c,KAAKkV,MAAQ,CACxB,CAIA,KAAAsK,CAAMxe,EAAK4hB,GAUP,GATIA,GACA5iB,KAAK4iB,MAAQA,EACbA,EAAM/N,MAAQ7T,EACd4hB,EAAMpT,UAAYxO,EAAM,EACxB4hB,EAAM3qB,MAAQ2qB,EAAMP,UAAY,GAGhCriB,KAAK4iB,MAAQL,EAEbviB,KAAKgB,KAAOA,EAAK,CAEjB,GADAhB,KAAKgB,IAAMA,EACPA,GAAOhB,KAAK8U,IAEZ,OADA9U,KAAK2jB,UACE3jB,KAEX,KAAOgB,EAAMhB,KAAK+I,MAAMhQ,MACpBiH,KAAK+I,MAAQ/I,KAAKuc,SAASvc,KAAK6iB,YACpC,KAAO7hB,GAAOhB,KAAK+I,MAAM5P,IACrB6G,KAAK+I,MAAQ/I,KAAKuc,SAASvc,KAAK6iB,YAChC7hB,GAAOhB,KAAK8iB,UAAY9hB,EAAMhB,KAAK8iB,SAAW9iB,KAAK4c,MAAMjjB,OACzDqG,KAAKyiB,SAAWzhB,EAAMhB,KAAK8iB,UAG3B9iB,KAAK4c,MAAQ,GACb5c,KAAKyiB,SAAW,GAEpBziB,KAAK+iB,UACT,CACA,OAAO/iB,IACX,CAIA,IAAA8c,CAAK/jB,EAAMI,GACP,GAAIJ,GAAQiH,KAAK8iB,UAAY3pB,GAAM6G,KAAK8iB,SAAW9iB,KAAK4c,MAAMjjB,OAC1D,OAAOqG,KAAK4c,MAAMtpB,MAAMyF,EAAOiH,KAAK8iB,SAAU3pB,EAAK6G,KAAK8iB,UAC5D,GAAI/pB,GAAQiH,KAAK2iB,WAAaxpB,GAAM6G,KAAK2iB,UAAY3iB,KAAK0iB,OAAO/oB,OAC7D,OAAOqG,KAAK0iB,OAAOpvB,MAAMyF,EAAOiH,KAAK2iB,UAAWxpB,EAAK6G,KAAK2iB,WAC9D,GAAI5pB,GAAQiH,KAAK+I,MAAMhQ,MAAQI,GAAM6G,KAAK+I,MAAM5P,GAC5C,OAAO6G,KAAKqJ,MAAMyT,KAAK/jB,EAAMI,GACjC,IAAI8V,EAAS,GACb,IAAK,IAAI0J,KAAK3Y,KAAKuc,OAAQ,CACvB,GAAI5D,EAAE5f,MAAQI,EACV,MACAwf,EAAExf,GAAKJ,IACPkW,GAAUjP,KAAKqJ,MAAMyT,KAAK1E,KAAKC,IAAIM,EAAE5f,KAAMA,GAAOqf,KAAKwL,IAAIjL,EAAExf,GAAIA,IACzE,CACA,OAAO8V,CACX,EAKJ,MAAM4U,EACF,WAAAzjB,CAAYb,EAAM7K,GACdsL,KAAKT,KAAOA,EACZS,KAAKtL,GAAKA,CACd,CACA,KAAAkuB,CAAMvZ,EAAOiR,GACT,IAAI,OAAEjgB,GAAWigB,EAAM1P,EACvBkZ,EAAU9jB,KAAKT,KAAM8J,EAAOiR,EAAOta,KAAKtL,GAAI2F,EAAOkF,KAAMlF,EAAO0pB,eACpE,EAEJF,EAAWG,UAAUC,WAAaJ,EAAWG,UAAU7uB,SAAW0uB,EAAWG,UAAU9S,QAAS,EA+BzD2S,EAAWG,UAAU7uB,SAAW0uB,EAAWG,UAAU9S,QAAS,EA4CrG,SAAS4S,EAAUvkB,EAAM8J,EAAOiR,EAAOjL,EAAO6U,EAAWC,GACrD,IAAInH,EAAQ,EAAGoH,EAAY,GAAK/U,GAAO,QAAE+R,GAAY9G,EAAM1P,EAAEvQ,OAC7D0Y,EAAM,KAC+B,IAA5BqR,EAAY7kB,EAAKyd,KADX,CAGX,IAAIqH,EAAS9kB,EAAKyd,EAAQ,GAI1B,IAAK,IAAI/U,EAAI+U,EAAQ,EAAG/U,EAAIoc,EAAQpc,GAAK,EACrC,IAAK1I,EAAK0I,EAAI,GAAKmc,GAAa,EAAG,CAC/B,IAAI3F,EAAOlf,EAAK0I,GAChB,GAAImZ,EAAQkD,OAAO7F,MACQ,GAAtBpV,EAAMuZ,MAAM3qB,OAAeoR,EAAMuZ,MAAM3qB,OAASwmB,GAC7C8F,EAAU9F,EAAMpV,EAAMuZ,MAAM3qB,MAAOisB,EAAWC,IAAc,CAChE9a,EAAMia,YAAY7E,GAClB,KACJ,CACJ,CACJ,IAAIvJ,EAAO7L,EAAM6L,KAAMsP,EAAM,EAAGC,EAAOllB,EAAKyd,EAAQ,GAEpD,KAAI3T,EAAM6L,KAAO,GAAKuP,EAAOD,GAAsC,OAA/BjlB,EAAK8kB,EAAgB,EAAPI,EAAW,IAA7D,CAKA,KAAOD,EAAMC,GAAO,CAChB,IAAIC,EAAOF,EAAMC,GAAS,EACtBnjB,EAAQ+iB,EAASK,GAAOA,GAAO,GAC/B3rB,EAAOwG,EAAK+B,GAAQnI,EAAKoG,EAAK+B,EAAQ,IAAM,MAChD,GAAI4T,EAAOnc,EACP0rB,EAAOC,MACN,MAAIxP,GAAQ/b,GAEZ,CACD6jB,EAAQzd,EAAK+B,EAAQ,GACrB+H,EAAMsT,UACN,SAAS5J,CACb,CALIyR,EAAME,EAAM,CAKhB,CACJ,CACA,KAhBA,CAFI1H,EAAQzd,EAAK8kB,EAAgB,EAAPI,EAAW,EAmBzC,CACJ,CACA,SAASE,EAAWplB,EAAMsV,EAAO4J,GAC7B,IAAK,IAAevJ,EAAXjN,EAAI4M,EAAiC,QAAnBK,EAAO3V,EAAK0I,IAA4BA,IAC/D,GAAIiN,GAAQuJ,EACR,OAAOxW,EAAI4M,EACnB,OAAQ,CACZ,CACA,SAAS0P,EAAU3B,EAAO1H,EAAM0J,EAAWC,GACvC,IAAIC,EAAQH,EAAWC,EAAWC,EAAa3J,GAC/C,OAAO4J,EAAQ,GAAKH,EAAWC,EAAWC,EAAajC,GAASkC,CACpE,CAGA,MAAMC,EAA4B,oBAAXC,SAA0BA,QAAQC,KAAO,YAAYnT,KAAKkT,QAAQC,IAAIC,KAC7F,IAAIC,EAAW,KACf,SAASC,EAAMzV,EAAM3O,EAAKoR,GACtB,IAAIL,EAASpC,EAAKoC,OAAOL,EAAS6B,kBAElC,IADAxB,EAAOO,OAAOtR,KAEV,KAAMoR,EAAO,EAAIL,EAAOiH,YAAYhY,GAAO+Q,EAAO7Q,WAAWF,IACzD,OAAS,CACL,IAAKoR,EAAO,EAAIL,EAAO5Y,GAAK6H,EAAM+Q,EAAOhZ,KAAOiI,KAAS+Q,EAAOlb,KAAK2Z,QACjE,OAAO4B,EAAO,EAAIgG,KAAKC,IAAI,EAAGD,KAAKwL,IAAI7R,EAAO5Y,GAAK,EAAG6H,EAAM,KACtDoX,KAAKwL,IAAIjU,EAAKhW,OAAQye,KAAKC,IAAItG,EAAOhZ,KAAO,EAAGiI,EAAM,KAChE,GAAIoR,EAAO,EAAIL,EAAOmH,cAAgBnH,EAAO4B,cACzC,MACJ,IAAK5B,EAAOiB,SACR,OAAOZ,EAAO,EAAI,EAAIzC,EAAKhW,MACnC,CAEZ,CACA,MAAM,EACF,WAAAyG,CAAYkc,EAAWnI,GACnBnU,KAAKsc,UAAYA,EACjBtc,KAAKmU,QAAUA,EACfnU,KAAKiI,EAAI,EACTjI,KAAKqlB,SAAW,KAChBrlB,KAAKslB,UAAY,EACjBtlB,KAAKulB,QAAU,EACfvlB,KAAKwlB,MAAQ,GACbxlB,KAAK6U,MAAQ,GACb7U,KAAKsB,MAAQ,GACbtB,KAAKylB,cACT,CACA,YAAAA,GACI,IAAIC,EAAK1lB,KAAKqlB,SAAWrlB,KAAKiI,GAAKjI,KAAKsc,UAAU3iB,OAAS,KAAOqG,KAAKsc,UAAUtc,KAAKiI,KACtF,GAAIyd,EAAI,CAGJ,IAFA1lB,KAAKslB,SAAWI,EAAGC,UAAYP,EAAMM,EAAG/V,KAAM+V,EAAG3sB,KAAO2sB,EAAG5J,OAAQ,GAAK4J,EAAG5J,OAAS4J,EAAG3sB,KACvFiH,KAAKulB,OAASG,EAAGE,QAAUR,EAAMM,EAAG/V,KAAM+V,EAAGvsB,GAAKusB,EAAG5J,QAAS,GAAK4J,EAAG5J,OAAS4J,EAAGvsB,GAC3E6G,KAAKwlB,MAAM7rB,QACdqG,KAAKwlB,MAAM7O,MACX3W,KAAK6U,MAAM8B,MACX3W,KAAKsB,MAAMqV,MAEf3W,KAAKwlB,MAAM3qB,KAAK6qB,EAAG/V,MACnB3P,KAAK6U,MAAMha,MAAM6qB,EAAG5J,QACpB9b,KAAKsB,MAAMzG,KAAK,GAChBmF,KAAKif,UAAYjf,KAAKslB,QAC1B,MAEItlB,KAAKif,UAAY,GAEzB,CAEA,MAAA4G,CAAO7kB,GACH,GAAIA,EAAMhB,KAAKif,UACX,OAAO,KACX,KAAOjf,KAAKqlB,UAAYrlB,KAAKulB,QAAUvkB,GACnChB,KAAKylB,eACT,IAAKzlB,KAAKqlB,SACN,OAAO,KACX,OAAS,CACL,IAAI9O,EAAOvW,KAAKwlB,MAAM7rB,OAAS,EAC/B,GAAI4c,EAAO,EAEP,OADAvW,KAAKylB,eACE,KAEX,IAAItV,EAAMnQ,KAAKwlB,MAAMjP,GAAOjV,EAAQtB,KAAKsB,MAAMiV,GAC/C,GAAIjV,GAAS6O,EAAIyB,SAASjY,OAAQ,CAC9BqG,KAAKwlB,MAAM7O,MACX3W,KAAK6U,MAAM8B,MACX3W,KAAKsB,MAAMqV,MACX,QACJ,CACA,IAAIzB,EAAO/E,EAAIyB,SAAStQ,GACpBuT,EAAQ7U,KAAK6U,MAAM0B,GAAQpG,EAAIpP,UAAUO,GAC7C,GAAIuT,EAAQ7T,EAER,OADAhB,KAAKif,UAAYpK,EACV,KAEX,GAAIK,aAAgBvD,EAAM,CACtB,GAAIkD,GAAS7T,EAAK,CACd,GAAI6T,EAAQ7U,KAAKslB,SACb,OAAO,KACX,IAAIxQ,EAAMD,EAAQK,EAAKvb,OACvB,GAAImb,GAAO9U,KAAKulB,OAAQ,CACpB,IAAI/V,EAAY0F,EAAKzN,KAAKiH,EAASc,WACnC,IAAKA,GAAasF,EAAMtF,EAAYxP,KAAKqlB,SAASlsB,GAC9C,OAAO+b,CACf,CACJ,CACAlV,KAAKsB,MAAMiV,KACP1B,EAAQK,EAAKvb,QAAUye,KAAKC,IAAIrY,KAAKslB,SAAUtkB,KAC/ChB,KAAKwlB,MAAM3qB,KAAKqa,GAChBlV,KAAK6U,MAAMha,KAAKga,GAChB7U,KAAKsB,MAAMzG,KAAK,GAExB,MAEImF,KAAKsB,MAAMiV,KACXvW,KAAKif,UAAYpK,EAAQK,EAAKvb,MAEtC,CACJ,EAEJ,MAAMmsB,EACF,WAAA1lB,CAAY/F,EAAQklB,GAChBvf,KAAKuf,OAASA,EACdvf,KAAK+lB,OAAS,GACd/lB,KAAKgmB,UAAY,KACjBhmB,KAAKimB,QAAU,GACfjmB,KAAK+lB,OAAS1rB,EAAO6rB,WAAWpiB,IAAIuZ,GAAK,IAAI+E,EACjD,CACA,UAAA+D,CAAW7L,GACP,IAAI8L,EAAc,EACdC,EAAO,MACP,OAAEhsB,GAAWigB,EAAM1P,GAAG,WAAEsb,GAAe7rB,EACvCioB,EAAOjoB,EAAO0lB,UAAUzF,EAAM0C,MAAO,GACrCxmB,EAAU8jB,EAAM8C,WAAa9C,EAAM8C,WAAWkE,KAAO,EACrD9R,EAAY,EAChB,IAAK,IAAIvH,EAAI,EAAGA,EAAIie,EAAWvsB,OAAQsO,IAAK,CACxC,KAAM,GAAKA,EAAKqa,GACZ,SACJ,IAAIgE,EAAYJ,EAAWje,GAAI2a,EAAQ5iB,KAAK+lB,OAAO9d,GACnD,KAAIoe,GAASC,EAAUnxB,aAEnBmxB,EAAUrC,YAAcrB,EAAM/N,OAASyF,EAAMtZ,KAAO4hB,EAAMN,MAAQA,GAAQM,EAAMpsB,SAAWA,KAC3FwJ,KAAKumB,kBAAkB3D,EAAO0D,EAAWhM,GACzCsI,EAAMN,KAAOA,EACbM,EAAMpsB,QAAUA,GAEhBosB,EAAMpT,UAAYoT,EAAM9N,IAAM,KAC9BtF,EAAY4I,KAAKC,IAAIuK,EAAMpT,UAAWA,IACvB,GAAfoT,EAAM3qB,OAA2B,CACjC,IAAIof,EAAa+O,EAIjB,GAHIxD,EAAMP,UAAY,IAClB+D,EAAcpmB,KAAKwmB,WAAWlM,EAAOsI,EAAMP,SAAUO,EAAM9N,IAAKsR,IACpEA,EAAcpmB,KAAKwmB,WAAWlM,EAAOsI,EAAM3qB,MAAO2qB,EAAM9N,IAAKsR,IACxDE,EAAUpV,SACXmV,EAAOzD,EACHwD,EAAc/O,GACd,KAEZ,CACJ,CACA,KAAOrX,KAAKimB,QAAQtsB,OAASysB,GACzBpmB,KAAKimB,QAAQtP,MAUjB,OATInH,GACA8K,EAAMsD,aAAapO,GAClB6W,GAAQ/L,EAAMtZ,KAAOhB,KAAKuf,OAAOzK,MAClCuR,EAAO,IAAIjE,EACXiE,EAAKpuB,MAAQqiB,EAAM1P,EAAEvQ,OAAOosB,QAC5BJ,EAAKxR,MAAQwR,EAAKvR,IAAMwF,EAAMtZ,IAC9BolB,EAAcpmB,KAAKwmB,WAAWlM,EAAO+L,EAAKpuB,MAAOouB,EAAKvR,IAAKsR,IAE/DpmB,KAAKgmB,UAAYK,EACVrmB,KAAKimB,OAChB,CACA,YAAAS,CAAapM,GACT,GAAIta,KAAKgmB,UACL,OAAOhmB,KAAKgmB,UAChB,IAAIK,EAAO,IAAIjE,GAAa,IAAEphB,EAAG,EAAE4J,GAAM0P,EAIzC,OAHA+L,EAAKxR,MAAQ7T,EACbqlB,EAAKvR,IAAMsD,KAAKwL,IAAI5iB,EAAM,EAAG4J,EAAE2U,OAAOzK,KACtCuR,EAAKpuB,MAAQ+I,GAAO4J,EAAE2U,OAAOzK,IAAMlK,EAAEvQ,OAAOosB,QAAU,EAC/CJ,CACX,CACA,iBAAAE,CAAkB3D,EAAO0D,EAAWhM,GAChC,IAAIzF,EAAQ7U,KAAKuf,OAAO2D,QAAQ5I,EAAMtZ,KAEtC,GADAslB,EAAU1D,MAAM5iB,KAAKuf,OAAOC,MAAM3K,EAAO+N,GAAQtI,GAC7CsI,EAAM3qB,OAAS,EAAG,CAClB,IAAI,OAAEoC,GAAWigB,EAAM1P,EACvB,IAAK,IAAI3C,EAAI,EAAGA,EAAI5N,EAAOssB,YAAYhtB,OAAQsO,IAC3C,GAAI5N,EAAOssB,YAAY1e,IAAM2a,EAAM3qB,MAAO,CACtC,IAAIgX,EAAS5U,EAAOusB,aAAa3e,GAAGjI,KAAKuf,OAAOzC,KAAK8F,EAAM/N,MAAO+N,EAAM9N,KAAMwF,GAC9E,GAAIrL,GAAU,GAAKqL,EAAM1P,EAAEvQ,OAAO+mB,QAAQkD,OAAOrV,GAAU,GAAI,CAC7C,EAATA,EAGD2T,EAAMP,SAAWpT,GAAU,EAF3B2T,EAAM3qB,MAAQgX,GAAU,EAG5B,KACJ,CACJ,CACR,MAEI2T,EAAM3qB,MAAQ,EACd2qB,EAAM9N,IAAM9U,KAAKuf,OAAO2D,QAAQrO,EAAQ,EAEhD,CACA,SAAAgS,CAAUnJ,EAAQkF,EAAO9N,EAAKxT,GAE1B,IAAK,IAAI2G,EAAI,EAAGA,EAAI3G,EAAO2G,GAAK,EAC5B,GAAIjI,KAAKimB,QAAQhe,IAAMyV,EACnB,OAAOpc,EAIf,OAHAtB,KAAKimB,QAAQ3kB,KAAWoc,EACxB1d,KAAKimB,QAAQ3kB,KAAWshB,EACxB5iB,KAAKimB,QAAQ3kB,KAAWwT,EACjBxT,CACX,CACA,UAAAklB,CAAWlM,EAAOsI,EAAO9N,EAAKxT,GAC1B,IAAI,MAAE0b,GAAU1C,GAAO,OAAEjgB,GAAWigB,EAAM1P,GAAG,KAAErL,GAASlF,EACxD,IAAK,IAAIoC,EAAM,EAAGA,EAAM,EAAGA,IACvB,IAAK,IAAIwL,EAAI5N,EAAO0lB,UAAU/C,EAAOvgB,EAAM,EAA0B,IAA8BwL,GAAK,EAAG,CACvG,GAAe,OAAX1I,EAAK0I,GAA2B,CAChC,GAAmB,GAAf1I,EAAK0I,EAAI,GAGR,CACY,GAAT3G,GAA6B,GAAf/B,EAAK0I,EAAI,KACvB3G,EAAQtB,KAAK6mB,UAAUjQ,GAAKrX,EAAM0I,EAAI,GAAI2a,EAAO9N,EAAKxT,IAC1D,KACJ,CANI2G,EAAI2O,GAAKrX,EAAM0I,EAAI,EAO3B,CACI1I,EAAK0I,IAAM2a,IACXthB,EAAQtB,KAAK6mB,UAAUjQ,GAAKrX,EAAM0I,EAAI,GAAI2a,EAAO9N,EAAKxT,GAC9D,CAEJ,OAAOA,CACX,EAEJ,MAAMwlB,EACF,WAAA1mB,CAAY/F,EAAQgP,EAAOiT,EAAWC,GAClCvc,KAAK3F,OAASA,EACd2F,KAAKqJ,MAAQA,EACbrJ,KAAKuc,OAASA,EACdvc,KAAK+mB,WAAa,EAClB/mB,KAAKgnB,YAAc,KACnBhnB,KAAKinB,YAAc,EACnBjnB,KAAKqU,OAAS,GACdrU,KAAKknB,UAAY,KACjBlnB,KAAKme,uBAAyB,EAC9Bne,KAAKqe,qBAAuB,EAC5Bre,KAAKoe,kBAAoB,EACzBpe,KAAKuf,OAAS,IAAIiD,EAAYnZ,EAAOkT,GACrCvc,KAAK+lB,OAAS,IAAID,EAAWzrB,EAAQ2F,KAAKuf,QAC1Cvf,KAAKmnB,QAAU9sB,EAAO8V,IAAI,GAC1B,IAAI,KAAEpX,GAASwjB,EAAO,GACtBvc,KAAKonB,OAAS,CAACrK,EAAMlI,MAAM7U,KAAM3F,EAAO8V,IAAI,GAAIpX,IAChDiH,KAAKsc,UAAYA,EAAU3iB,QAAUqG,KAAKuf,OAAOzK,IAAM/b,EAA6B,EAAtBsB,EAAOgtB,aAC/D,IAAI,EAAe/K,EAAWjiB,EAAO8Z,SAAW,IAC1D,CACA,aAAImT,GACA,OAAOtnB,KAAKinB,WAChB,CAOA,OAAAtK,GACI,IAGI4K,EAASC,EAHTJ,EAASpnB,KAAKonB,OAAQpmB,EAAMhB,KAAKinB,YAEjCQ,EAAYznB,KAAKonB,OAAS,GAS9B,GAAIpnB,KAAKoe,kBAAoB,KAAmE,GAAjBgJ,EAAOztB,OAAa,CAC/F,IAAKgO,GAAKyf,EACV,KAAOzf,EAAE0Y,eAAiB1Y,EAAE2S,MAAM3gB,QAAUgO,EAAE2S,MAAM3S,EAAE2S,MAAM3gB,OAAS,IAAMqG,KAAKme,wBAChFne,KAAKoe,kBAAoBpe,KAAKqe,qBAAuB,CACzD,CAIA,IAAK,IAAIpW,EAAI,EAAGA,EAAImf,EAAOztB,OAAQsO,IAAK,CACpC,IAAIqS,EAAQ8M,EAAOnf,GACnB,OAAS,CAEL,GADAjI,KAAK+lB,OAAOC,UAAY,KACpB1L,EAAMtZ,IAAMA,EACZymB,EAAU5sB,KAAKyf,OAEd,IAAIta,KAAK0nB,aAAapN,EAAOmN,EAAWL,GACzC,SAEC,CACIG,IACDA,EAAU,GACVC,EAAgB,IAEpBD,EAAQ1sB,KAAKyf,GACb,IAAIqN,EAAM3nB,KAAK+lB,OAAOW,aAAapM,GACnCkN,EAAc3sB,KAAK8sB,EAAI1vB,MAAO0vB,EAAI7S,IACtC,EACA,KACJ,CACJ,CACA,IAAK2S,EAAU9tB,OAAQ,CACnB,IAAIiuB,EAAWL,GAuhB3B,SAAsBH,GAClB,IAAIjH,EAAO,KACX,IAAK,IAAI7F,KAAS8M,EAAQ,CACtB,IAAIG,EAAUjN,EAAM1P,EAAEsc,WACjB5M,EAAMtZ,KAAOsZ,EAAM1P,EAAE2U,OAAOzK,KAAkB,MAAXyS,GAAmBjN,EAAMtZ,IAAMumB,IACnEjN,EAAM1P,EAAEvQ,OAAOkkB,UAAUjE,EAAM0C,MAAO,MACpCmD,GAAQA,EAAKjD,MAAQ5C,EAAM4C,SAC7BiD,EAAO7F,EACf,CACA,OAAO6F,CACX,CAjiBsC0H,CAAaN,GACvC,GAAIK,EAGA,OAAO5nB,KAAK8nB,YAAYF,GAE5B,GAAI5nB,KAAK3F,OAAOqnB,OAGZ,MAAM,IAAIqG,YAAY,eAAiB/mB,GAEtChB,KAAK+mB,aACN/mB,KAAK+mB,WAAa,EAC1B,CACA,GAAI/mB,KAAK+mB,YAAcQ,EAAS,CAC5B,IAAIK,EAA6B,MAAlB5nB,KAAKknB,WAAqBK,EAAQ,GAAGvmB,IAAMhB,KAAKknB,UAAYK,EAAQ,GAC7EvnB,KAAKgoB,YAAYT,EAASC,EAAeC,GAC/C,GAAIG,EAGA,OAAO5nB,KAAK8nB,YAAYF,EAAS/G,WAEzC,CACA,GAAI7gB,KAAK+mB,WAAY,CACjB,IAAIkB,EAAkC,GAAnBjoB,KAAK+mB,WAAkB,EAAsB,EAAlB/mB,KAAK+mB,WACnD,GAAIU,EAAU9tB,OAASsuB,EAEnB,IADAR,EAAUnvB,KAAK,CAACC,EAAG0f,IAAMA,EAAEiF,MAAQ3kB,EAAE2kB,OAC9BuK,EAAU9tB,OAASsuB,GACtBR,EAAU9Q,MAEd8Q,EAAUpM,KAAK1T,GAAKA,EAAEsV,UAAYjc,IAClChB,KAAK+mB,YACb,MACK,GAAIU,EAAU9tB,OAAS,EAAG,CAI3BuuB,EAAO,IAAK,IAAIjgB,EAAI,EAAGA,EAAIwf,EAAU9tB,OAAS,EAAGsO,IAAK,CAClD,IAAIqS,EAAQmN,EAAUxf,GACtB,IAAK,IAAIqO,EAAIrO,EAAI,EAAGqO,EAAImR,EAAU9tB,OAAQ2c,IAAK,CAC3C,IAAI2K,EAAQwG,EAAUnR,GACtB,GAAIgE,EAAM0G,UAAUC,IAChB3G,EAAMpG,OAAOva,OAAS,KAAsCsnB,EAAM/M,OAAOva,OAAS,IAAoC,CACtH,MAAM2gB,EAAM4C,MAAQ+D,EAAM/D,OAAW5C,EAAMpG,OAAOva,OAASsnB,EAAM/M,OAAOva,QAAW,GAG9E,CACD8tB,EAAUrN,OAAOnS,IAAK,GACtB,SAASigB,CACb,CALIT,EAAUrN,OAAO9D,IAAK,EAM9B,CACJ,CACJ,CACImR,EAAU9tB,OAAS,IACnB8tB,EAAUrN,OAAO,GAA4BqN,EAAU9tB,OAAS,GACxE,CACAqG,KAAKinB,YAAcQ,EAAU,GAAGzmB,IAChC,IAAK,IAAIiH,EAAI,EAAGA,EAAIwf,EAAU9tB,OAAQsO,IAC9Bwf,EAAUxf,GAAGjH,IAAMhB,KAAKinB,cACxBjnB,KAAKinB,YAAcQ,EAAUxf,GAAGjH,KACxC,OAAO,IACX,CACA,MAAAqV,CAAOrV,GACH,GAAsB,MAAlBhB,KAAKknB,WAAqBlnB,KAAKknB,UAAYlmB,EAC3C,MAAM,IAAI+N,WAAW,gCACzB/O,KAAKknB,UAAYlmB,CACrB,CAKA,YAAA0mB,CAAapN,EAAO8M,EAAQlvB,GACxB,IAAI2c,EAAQyF,EAAMtZ,KAAK,OAAE3G,GAAW2F,KACzB+kB,GAAU/kB,KAAKmoB,QAAQ7N,GAClC,GAAsB,MAAlBta,KAAKknB,WAAqBrS,EAAQ7U,KAAKknB,UACvC,OAAO5M,EAAM+F,cAAgB/F,EAAQ,KACzC,GAAIta,KAAKsc,UAAW,CAChB,IAAI8L,EAAW9N,EAAM8C,YAAc9C,EAAM8C,WAAWiC,QAAQqC,OAAQ2G,EAASD,EAAW9N,EAAM8C,WAAWkE,KAAO,EAChH,IAAK,IAAIgH,EAAStoB,KAAKsc,UAAUuJ,OAAOhR,GAAQyT,GAAS,CACrD,IAAIvtB,EAAQiF,KAAK3F,OAAO8Z,QAAQlD,MAAMqX,EAAOzxB,KAAKnC,KAAO4zB,EAAOzxB,KAAOwD,EAAO0jB,QAAQzD,EAAM0C,MAAOsL,EAAOzxB,KAAKnC,KAAO,EACtH,GAAIqG,GAAS,GAAKutB,EAAO3uB,UAAYyuB,IAAaE,EAAO7gB,KAAKiH,EAASa,cAAgB,IAAM8Y,GAIzF,OAHA/N,EAAM6E,QAAQmJ,EAAQvtB,IAGf,EAEX,KAAMutB,aAAkB3W,IAAmC,GAA1B2W,EAAO1W,SAASjY,QAAe2uB,EAAOvnB,UAAU,GAAK,EAClF,MACJ,IAAI8R,EAAQyV,EAAO1W,SAAS,GAC5B,KAAIiB,aAAiBlB,GAA+B,GAAvB2W,EAAOvnB,UAAU,IAG1C,MAFAunB,EAASzV,CAGjB,CACJ,CACA,IAAI0V,EAAgBluB,EAAO0lB,UAAUzF,EAAM0C,MAAO,GAClD,GAAIuL,EAAgB,EAIhB,OAHAjO,EAAMmD,OAAO8K,IAGN,EAEX,GAAIjO,EAAMA,MAAM3gB,QAAU,KACtB,KAAO2gB,EAAMA,MAAM3gB,OAAS,KAAwB2gB,EAAM+F,gBAE9D,IAAI4F,EAAUjmB,KAAK+lB,OAAOI,WAAW7L,GACrC,IAAK,IAAIrS,EAAI,EAAGA,EAAIge,EAAQtsB,QAAS,CACjC,IAAI+jB,EAASuI,EAAQhe,KAAMwW,EAAOwH,EAAQhe,KAAM6M,EAAMmR,EAAQhe,KAC1DsO,EAAOtO,GAAKge,EAAQtsB,SAAWzB,EAC/BswB,EAAajS,EAAO+D,EAAQA,EAAMpiB,QAClCmuB,EAAOrmB,KAAK+lB,OAAOC,UAKvB,GAJAwC,EAAWxJ,MAAMtB,EAAQe,EAAM4H,EAAOA,EAAKxR,MAAQ2T,EAAWxnB,IAAK8T,GAI/DyB,EACA,OAAO,EACFiS,EAAWxnB,IAAM6T,EACtBuS,EAAOvsB,KAAK2tB,GAEZtwB,EAAM2C,KAAK2tB,EACnB,CACA,OAAO,CACX,CAIA,YAAAC,CAAanO,EAAOmN,GAChB,IAAIzmB,EAAMsZ,EAAMtZ,IAChB,OAAS,CACL,IAAKhB,KAAK0nB,aAAapN,EAAO,KAAM,MAChC,OAAO,EACX,GAAIA,EAAMtZ,IAAMA,EAEZ,OADA0nB,EAAepO,EAAOmN,IACf,CAEf,CACJ,CACA,WAAAO,CAAYZ,EAAQrB,EAAQ0B,GACxB,IAAIG,EAAW,KAAMe,GAAY,EACjC,IAAK,IAAI1gB,EAAI,EAAGA,EAAImf,EAAOztB,OAAQsO,IAAK,CACpC,IAAIqS,EAAQ8M,EAAOnf,GAAI2a,EAAQmD,EAAO9d,GAAK,GAAI2gB,EAAW7C,EAAkB,GAAV9d,GAAK,IACnEyO,EAAOqO,EAAU/kB,KAAKmoB,QAAQ7N,GAAS,OAAS,GACpD,GAAIA,EAAMwG,QAAS,CACf,GAAI6H,EACA,SAMJ,GALAA,GAAY,EACZrO,EAAMyG,UAGK/gB,KAAKyoB,aAAanO,EAAOmN,GAEhC,QACR,CACA,IAAIoB,EAAQvO,EAAMpiB,QAAS4wB,EAAYpS,EACvC,IAAK,IAAIJ,EAAI,EAAGuS,EAAMxI,eAAiB/J,EAAI,GAA+BA,IAAK,CAI3E,GADWtW,KAAKyoB,aAAaI,EAAOpB,GAEhC,MACA1C,IACA+D,EAAY9oB,KAAKmoB,QAAQU,GAAS,OAC1C,CACA,IAAK,IAAIE,KAAUzO,EAAM2F,gBAAgB2C,GAGrC5iB,KAAKyoB,aAAaM,EAAQtB,GAE1BznB,KAAKuf,OAAOzK,IAAMwF,EAAMtZ,KACpB4nB,GAAYtO,EAAMtZ,MAClB4nB,IACAhG,EAAQ,GAEZtI,EAAMoF,gBAAgBkD,EAAOgG,GAG7BF,EAAepO,EAAOmN,MAEhBG,GAAYA,EAAS1K,MAAQ5C,EAAM4C,SACzC0K,EAAWtN,EAEnB,CACA,OAAOsN,CACX,CAEA,WAAAE,CAAYxN,GAER,OADAA,EAAMmH,QACC9P,EAAKqC,MAAM,CAAEE,OAAQ0N,EAAkB7R,OAAOuK,GACjDnG,QAASnU,KAAK3F,OAAO8Z,QACrBmD,MAAOtX,KAAKmnB,QACZ/S,gBAAiBpU,KAAK3F,OAAOgtB,aAC7BhT,OAAQrU,KAAKqU,OACbQ,MAAO7U,KAAKuc,OAAO,GAAGxjB,KACtBY,OAAQ2gB,EAAMtZ,IAAMhB,KAAKuc,OAAO,GAAGxjB,KACnCub,cAAetU,KAAK3F,OAAO2jB,eACnC,CACA,OAAAmK,CAAQ7N,GACJ,IAAI5lB,GAAMywB,IAAaA,EAAW,IAAI3T,UAAU3B,IAAIyK,GAGpD,OAFK5lB,GACDywB,EAAS1oB,IAAI6d,EAAO5lB,EAAKmP,OAAOmlB,cAAchpB,KAAKgnB,gBAChDtyB,EAAK4lB,CAChB,EAEJ,SAASoO,EAAepO,EAAOmN,GAC3B,IAAK,IAAIxf,EAAI,EAAGA,EAAIwf,EAAU9tB,OAAQsO,IAAK,CACvC,IAAIgZ,EAAQwG,EAAUxf,GACtB,GAAIgZ,EAAMjgB,KAAOsZ,EAAMtZ,KAAOigB,EAAMD,UAAU1G,GAG1C,YAFImN,EAAUxf,GAAGiV,MAAQ5C,EAAM4C,QAC3BuK,EAAUxf,GAAKqS,GAG3B,CACAmN,EAAU5sB,KAAKyf,EACnB,CACA,MAAM2O,EACF,WAAA7oB,CAAYiR,EAAQrB,EAAO1a,GACvB0K,KAAKqR,OAASA,EACdrR,KAAKgQ,MAAQA,EACbhQ,KAAK1K,SAAWA,CACpB,CACA,MAAAgvB,CAAO7F,GAAQ,OAAQze,KAAK1K,UAAmC,GAAvB0K,KAAK1K,SAASmpB,EAAY,EAiCtE,MAAMyK,WAAiB9M,EAInB,WAAAhc,CAAY8P,GAMR,GALAkJ,QAIApZ,KAAKmpB,SAAW,GACI,IAAhBjZ,EAAKhS,QACL,MAAM,IAAI6Q,WAAW,mBAAmBmB,EAAKhS,+CACjD,IAAIkrB,EAAYlZ,EAAKkZ,UAAUlxB,MAAM,KACrC8H,KAAKge,cAAgBoL,EAAUzvB,OAC/B,IAAK,IAAIsO,EAAI,EAAGA,EAAIiI,EAAKmZ,gBAAiBphB,IACtCmhB,EAAUvuB,KAAK,IACnB,IAAIyuB,EAAWnqB,OAAOC,KAAK8Q,EAAKqZ,UAAUzlB,IAAI6U,GAAKzI,EAAKqZ,SAAS5Q,GAAG,IAChE6Q,EAAY,GAChB,IAAK,IAAIvhB,EAAI,EAAGA,EAAImhB,EAAUzvB,OAAQsO,IAClCuhB,EAAU3uB,KAAK,IACnB,SAAS4uB,EAAQC,EAAQjiB,EAAMxP,GAC3BuxB,EAAUE,GAAQ7uB,KAAK,CAAC4M,EAAMA,EAAKoH,YAAYhL,OAAO5L,KAC1D,CACA,GAAIiY,EAAKsZ,UACL,IAAK,IAAIG,KAAYzZ,EAAKsZ,UAAW,CACjC,IAAI/hB,EAAOkiB,EAAS,GACD,iBAARliB,IACPA,EAAOiH,EAASjH,IACpB,IAAK,IAAIQ,EAAI,EAAGA,EAAI0hB,EAAShwB,QAAS,CAClC,IAAIub,EAAOyU,EAAS1hB,KACpB,GAAIiN,GAAQ,EACRuU,EAAQvU,EAAMzN,EAAMkiB,EAAS1hB,UAE5B,CACD,IAAIhQ,EAAQ0xB,EAAS1hB,GAAKiN,GAC1B,IAAK,IAAIoB,GAAKpB,EAAMoB,EAAI,EAAGA,IACvBmT,EAAQE,EAAS1hB,KAAMR,EAAMxP,GACjCgQ,GACJ,CACJ,CACJ,CACJjI,KAAKmU,QAAU,IAAInD,EAAQoY,EAAUtlB,IAAI,CAACsD,EAAMa,IAAM+G,EAASiB,OAAO,CAClE7I,KAAMa,GAAKjI,KAAKge,mBAAgBpd,EAAYwG,EAC5C1S,GAAIuT,EACJhT,MAAOu0B,EAAUvhB,GACjBkI,IAAKmZ,EAAS3Y,QAAQ1I,IAAM,EAC5B7J,MAAY,GAAL6J,EACPmI,QAASF,EAAK0Z,cAAgB1Z,EAAK0Z,aAAajZ,QAAQ1I,IAAM,MAE9DiI,EAAK2Z,cACL7pB,KAAKmU,QAAUnU,KAAKmU,QAAQjD,UAAUhB,EAAK2Z,cAC/C7pB,KAAK0hB,QAAS,EACd1hB,KAAKqnB,aAAe9Y,EACpB,IAAIub,EAAahI,EAAY5R,EAAK6Z,WAClC/pB,KAAKxJ,QAAU0Z,EAAK1Z,QACpBwJ,KAAKgqB,iBAAmB9Z,EAAKyW,aAAe,GAC5C3mB,KAAK2mB,YAAc,IAAItR,YAAYrV,KAAKgqB,iBAAiBrwB,QACzD,IAAK,IAAIsO,EAAI,EAAGA,EAAIjI,KAAKgqB,iBAAiBrwB,OAAQsO,IAC9CjI,KAAK2mB,YAAY1e,GAAKjI,KAAKgqB,iBAAiB/hB,GAAGwW,KACnDze,KAAK4mB,aAAe5mB,KAAKgqB,iBAAiBlmB,IAAImmB,IAC9CjqB,KAAKkqB,OAASpI,EAAY5R,EAAKga,OAAQC,aACvCnqB,KAAKT,KAAOuiB,EAAY5R,EAAKka,WAC7BpqB,KAAK2hB,KAAOG,EAAY5R,EAAKyR,MAC7B3hB,KAAKqqB,QAAUna,EAAKma,QACpBrqB,KAAKkmB,WAAahW,EAAKgW,WAAWpiB,IAAI7L,GAAyB,iBAATA,EAAoB,IAAI4rB,EAAWiG,EAAY7xB,GAASA,GAC9G+H,KAAKupB,SAAWrZ,EAAKqZ,SACrBvpB,KAAKsqB,SAAWpa,EAAKoa,UAAY,CAAC,EAClCtqB,KAAKuqB,mBAAqBra,EAAKqa,oBAAsB,KACrDvqB,KAAK+jB,eAAiB7T,EAAKsa,UAC3BxqB,KAAKyqB,UAAYva,EAAKua,WAAa,KACnCzqB,KAAK8e,QAAU9e,KAAKmU,QAAQlD,MAAMtX,OAAS,EAC3CqG,KAAKohB,QAAUphB,KAAK0qB,eACpB1qB,KAAKmQ,IAAMnQ,KAAKupB,SAASpqB,OAAOC,KAAKY,KAAKupB,UAAU,GACxD,CACA,WAAA9M,CAAYpT,EAAOiT,EAAWC,GAC1B,IAAI9b,EAAQ,IAAIqmB,EAAM9mB,KAAMqJ,EAAOiT,EAAWC,GAC9C,IAAK,IAAIoO,KAAK3qB,KAAKmpB,SACf1oB,EAAQkqB,EAAElqB,EAAO4I,EAAOiT,EAAWC,GACvC,OAAO9b,CACX,CAIA,OAAAsd,CAAQf,EAAOyB,EAAMmM,GAAQ,GACzB,IAAIC,EAAQ7qB,KAAK2hB,KACjB,GAAIlD,GAAQoM,EAAM,GACd,OAAQ,EACZ,IAAK,IAAI7pB,EAAM6pB,EAAMpM,EAAO,KAAM,CAC9B,IAAIqM,EAAWD,EAAM7pB,KAAQuV,EAAkB,EAAXuU,EAChCn0B,EAASk0B,EAAM7pB,KACnB,GAAIuV,GAAQqU,EACR,OAAOj0B,EACX,IAAK,IAAIme,EAAM9T,GAAO8pB,GAAY,GAAI9pB,EAAM8T,EAAK9T,IAC7C,GAAI6pB,EAAM7pB,IAAQgc,EACd,OAAOrmB,EACf,GAAI4f,EACA,OAAQ,CAChB,CACJ,CAIA,SAAAyJ,CAAUhD,EAAO+N,GACb,IAAIxrB,EAAOS,KAAKT,KAChB,IAAK,IAAI9C,EAAM,EAAGA,EAAM,EAAGA,IACvB,IAAK,IAA2FyY,EAAvFjN,EAAIjI,KAAK+f,UAAU/C,EAAOvgB,EAAM,EAA0B,IAAoCwL,GAAK,EAAG,CAC3G,GAAwB,QAAnBiN,EAAO3V,EAAK0I,IAA4B,CACzC,GAAmB,GAAf1I,EAAK0I,EAAI,GAER,IAAmB,GAAf1I,EAAK0I,EAAI,GACd,OAAO2O,GAAKrX,EAAM0I,EAAI,GAEtB,KAAK,CAJLiN,EAAO3V,EAAK0I,EAAI2O,GAAKrX,EAAM0I,EAAI,GAKvC,CACA,GAAIiN,GAAQ6V,GAAoB,GAAR7V,EACpB,OAAO0B,GAAKrX,EAAM0I,EAAI,EAC9B,CAEJ,OAAO,CACX,CAIA,SAAA8X,CAAU/C,EAAOgO,GACb,OAAOhrB,KAAKkqB,OAAgB,EAARlN,EAAmCgO,EAC3D,CAIA,SAAAzM,CAAUvB,EAAOiO,GACb,OAAQjrB,KAAK+f,UAAU/C,EAAO,GAA4BiO,GAAQ,CACtE,CAIA,WAAA3K,CAAYtD,EAAOU,GACf,QAAS1d,KAAK2gB,WAAW3D,EAAOzkB,GAAKA,GAAKmlB,GAAgB,KAC9D,CAIA,UAAAiD,CAAW3D,EAAOU,GACd,IAAIwN,EAAQlrB,KAAK+f,UAAU/C,EAAO,GAC9B/N,EAASic,EAAQxN,EAAOwN,QAAStqB,EACrC,IAAK,IAAIqH,EAAIjI,KAAK+f,UAAU/C,EAAO,GAAuC,MAAV/N,EAAgBhH,GAAK,EAAG,CACpF,GAAoB,OAAhBjI,KAAKT,KAAK0I,GAA2B,CACrC,GAAwB,GAApBjI,KAAKT,KAAK0I,EAAI,GAGd,MAFAA,EAAI2O,GAAK5W,KAAKT,KAAM0I,EAAI,EAGhC,CACAgH,EAASyO,EAAO9G,GAAK5W,KAAKT,KAAM0I,EAAI,GACxC,CACA,OAAOgH,CACX,CAKA,UAAAiR,CAAWlD,GACP,IAAI/N,EAAS,GACb,IAAK,IAAIhH,EAAIjI,KAAK+f,UAAU/C,EAAO,IAA8B/U,GAAK,EAAG,CACrE,GAAoB,OAAhBjI,KAAKT,KAAK0I,GAA2B,CACrC,GAAwB,GAApBjI,KAAKT,KAAK0I,EAAI,GAGd,MAFAA,EAAI2O,GAAK5W,KAAKT,KAAM0I,EAAI,EAGhC,CACA,KAAwB,EAAnBjI,KAAKT,KAAK0I,EAAI,IAAkD,CACjE,IAAIhQ,EAAQ+H,KAAKT,KAAK0I,EAAI,GACrBgH,EAAOoM,KAAK,CAAC+E,EAAGnY,IAAW,EAAJA,GAAUmY,GAAKnoB,IACvCgX,EAAOpU,KAAKmF,KAAKT,KAAK0I,GAAIhQ,EAClC,CACJ,CACA,OAAOgX,CACX,CAMA,SAAA1Y,CAAUoY,GAGN,IAAIuJ,EAAO/Y,OAAOmS,OAAOnS,OAAO4Q,OAAOmZ,GAASlF,WAAYhkB,MAG5D,GAFI2O,EAAO1Z,QACPijB,EAAK/D,QAAUnU,KAAKmU,QAAQjD,UAAUvC,EAAO1Z,QAC7C0Z,EAAOwB,IAAK,CACZ,IAAI1R,EAAOuB,KAAKupB,SAAS5a,EAAOwB,KAChC,IAAK1R,EACD,MAAM,IAAIsQ,WAAW,yBAAyBJ,EAAOwB,OACzD+H,EAAK/H,IAAM1R,CACf,CA2BA,OA1BIkQ,EAAOuX,aACPhO,EAAKgO,WAAalmB,KAAKkmB,WAAWpiB,IAAIqnB,IAClC,IAAIra,EAAQnC,EAAOuX,WAAWxvB,KAAKiiB,GAAKA,EAAE5f,MAAQoyB,GAClD,OAAOra,EAAQA,EAAM3X,GAAKgyB,KAE9Bxc,EAAOiY,eACP1O,EAAK0O,aAAe5mB,KAAK4mB,aAAatzB,QACtC4kB,EAAK8R,iBAAmBhqB,KAAKgqB,iBAAiBlmB,IAAI,CAAC6D,EAAGM,KAClD,IAAI6I,EAAQnC,EAAOiY,aAAalwB,KAAKiiB,GAAKA,EAAE5f,MAAQ4O,EAAEyjB,UACtD,IAAKta,EACD,OAAOnJ,EACX,IAAIuI,EAAO/Q,OAAOmS,OAAOnS,OAAOmS,OAAO,CAAC,EAAG3J,GAAI,CAAEyjB,SAAUta,EAAM3X,KAEjE,OADA+e,EAAK0O,aAAa3e,GAAKgiB,GAAe/Z,GAC/BA,KAGXvB,EAAO0c,iBACPnT,EAAK1hB,QAAUmY,EAAO0c,gBACtB1c,EAAOyS,UACPlJ,EAAKkJ,QAAUphB,KAAK0qB,aAAa/b,EAAOyS,UACvB,MAAjBzS,EAAO+S,SACPxJ,EAAKwJ,OAAS/S,EAAO+S,QACrB/S,EAAO2c,OACPpT,EAAKiR,SAAWjR,EAAKiR,SAAStS,OAAOlI,EAAO2c,OACrB,MAAvB3c,EAAO0Y,eACPnP,EAAKmP,aAAe1Y,EAAO0Y,cACxBnP,CACX,CAKA,WAAAqT,GACI,OAAOvrB,KAAKmpB,SAASxvB,OAAS,CAClC,CAOA,OAAA6xB,CAAQ/M,GACJ,OAAOze,KAAKyqB,UAAYzqB,KAAKyqB,UAAUhM,GAAQ5a,OAAO4a,GAAQze,KAAK8e,SAAW9e,KAAKmU,QAAQlD,MAAMwN,GAAMrX,MAAQqX,EACnH,CAKA,WAAIgI,GAAY,OAAOzmB,KAAK8e,QAAU,CAAG,CAIzC,WAAI5M,GAAY,OAAOlS,KAAKmU,QAAQlD,MAAMjR,KAAKmQ,IAAI,GAAK,CAIxD,iBAAA2N,CAAkBW,GACd,IAAIgN,EAAOzrB,KAAKuqB,mBAChB,OAAe,MAARkB,EAAe,EAAIA,EAAKhN,IAAS,CAC5C,CAIA,YAAAiM,CAAatJ,GACT,IAAI/Z,EAASlI,OAAOC,KAAKY,KAAKsqB,UAAWta,EAAQ3I,EAAOvD,IAAI,KAAM,GAClE,GAAIsd,EACA,IAAK,IAAIsK,KAAQtK,EAAQlpB,MAAM,KAAM,CACjC,IAAIxD,EAAK2S,EAAOsJ,QAAQ+a,GACpBh3B,GAAM,IACNsb,EAAMtb,IAAM,EACpB,CACJ,IAAIY,EAAW,KACf,IAAK,IAAI2S,EAAI,EAAGA,EAAIZ,EAAO1N,OAAQsO,IAC/B,IAAK+H,EAAM/H,GACP,IAAK,IAAkCvT,EAA9B4hB,EAAItW,KAAKsqB,SAASjjB,EAAOY,IAAkC,QAAxBvT,EAAKsL,KAAKT,KAAK+W,QACtDhhB,IAAaA,EAAW,IAAIq2B,WAAW3rB,KAAKqqB,QAAU,KAAK31B,GAAM,EAE9E,OAAO,IAAIu0B,EAAQ7H,EAASpR,EAAO1a,EACvC,CAKA,kBAAOuZ,CAAYqB,GACf,OAAO,IAAIgZ,GAAShZ,EACxB,EAEJ,SAAS0G,GAAKrX,EAAMkgB,GAAO,OAAOlgB,EAAKkgB,GAAQlgB,EAAKkgB,EAAM,IAAM,EAAK,CAYrE,SAASwK,GAAe/Z,GACpB,GAAIA,EAAKkb,SAAU,CACf,IAAI9I,EAAOpS,EAAKgB,OAAS,EAA4B,EACrD,MAAO,CAACjZ,EAAOqiB,IAAWpK,EAAKkb,SAASnzB,EAAOqiB,IAAU,EAAKgI,CAClE,CACA,OAAOpS,EAAKL,GAChB,CCr1DA,MAoCM+b,GAAgB,CACpBC,KArCa,EAsCbC,OArCW,EAsCXC,OArCW,EAsCX/wB,QArCY,EAsCZgxB,OArCW,EAsCXC,aApCgB,EAqChBC,YApCe,EAqCfC,cApCiB,EAqCjBC,OApCW,GAqCXtQ,OApCW,GAqCXuQ,KApCS,GAqCTC,GApCO,GAqCPC,SApCa,GAqCbC,WApCc,GAqCdC,YApCe,GAqCfC,OA/CW,EAgDXC,WArCe,GAsCfC,KArCS,GAsCTC,KArCS,IA4CLC,GAA0B,CAC9BC,GA5CO,GA6CPC,QA5CY,GA6CZC,IA5CQ,GA6CRC,GA5CO,GA6CPC,OA5CW,GA6CXC,IA5CQ,GA6CRC,IA5CQ,GA6CR/O,MA5CU,GA6CVjG,IA5CQ,GA6CRuL,IA5CQ,GA6CR0J,OA5CW,GA6CXC,OA5CW,GA6CXC,QA5CY,GA6CZC,KA5CS,GA6CTn1B,KA5CS,GA6CTo1B,UA5Cc,IAoDVC,GAAkB,CAACC,UAAU,KAAKC,GAAG,IAAKC,gBAAgB,IAAKC,KAAK,IAAKC,aAAa,IAAKC,gBAAgB,IAAKC,WAAW,IAAKC,cAAc,IAAKC,cAAc,IAAKC,cAAc,IAAKC,cAAc,IAAKC,iBAAiB,IAAKC,iBAAiB,IAAKC,mBAAmB,IAAKC,gBAAgB,IAAKC,eAAe,IAAKC,iBAAiB,IAAKC,MAAM,IAAKC,SAAS,IAAKC,iBAAiB,KACzX10B,GAAS6uB,GAASra,YAAY,CAClC3Q,QAAS,GACTgsB,OAAQ,ygGACRE,UAAW,+9KACXzI,KAAM,wvCACNyH,UAAW,k4CACXiB,QAAS,IACTT,aAAc,CAAC,EAAE,IACjBP,gBAAiB,EACjBU,UAAW,4tEACX7D,WAAY,CAAC,EAAG,GAChBqD,SAAU,CAAC,MAAQ,CAAC,EAAE,KACtB5C,YAAa,CAAC,CAAClI,KAAM,GAAI5O,IAAK,CAAC5X,EAAOqiB,IAzCX,CAACriB,GACrB2zB,GAAc3zB,EAAMwR,iBAAmB,EAwCGulB,CAAqB/2B,IAAU,GAAI,CAACwmB,KAAM,GAAI5O,IAAK,CAAC5X,EAAOqiB,IAlBrF,CAACriB,GACjB60B,GAAwB70B,EAAMwR,iBAAmB,EAiB+DwlB,CAAiBh3B,IAAU,EAAK,GAAG,CAACwmB,KAAM,GAAI5O,IAAK5X,GAAS01B,GAAgB11B,KAAW,IAC9MuyB,UAAW,IAGP5lB,GAAO,EACXF,GAAS,EAsCTpB,GAAW,GAEXG,GAAU,GACVE,GAAa,GACbd,GAAK,GACL,GAAS,GACTE,GAAM,GACNE,GAAK,GACLE,GAAM,GAINuC,GAAa,GAEbG,GAAY,GACZE,GAAY,GACZI,GAAc,GACdE,GAAM,GACNxI,GAAW,GAEXmJ,GAAW,GAUX3C,GAAc,GAId3B,GAAM,GACN0C,GAAW,GACX5C,GAAM,GACNF,GAAM,GACNH,GAAM,GAGN+C,GAAQ,GAGR,GAAS,GAcTgqB,GAAa,E,sEC1MXC,EAA2B,CAAC,EAGhC,SAASC,EAAoBC,GAE5B,IAAIC,EAAeH,EAAyBE,GAC5C,QAAqBzuB,IAAjB0uB,EACH,OAAOA,EAAa5hB,QAGrB,IAAID,EAAS0hB,EAAyBE,GAAY,CACjD36B,GAAI26B,EACJE,QAAQ,EACR7hB,QAAS,CAAC,GAUX,OANA8hB,EAAoBH,GAAUI,KAAKhiB,EAAOC,QAASD,EAAQA,EAAOC,QAAS0hB,GAG3E3hB,EAAO8hB,QAAS,EAGT9hB,EAAOC,OACf,C,OAGA0hB,EAAoBM,EAAIF,EC3BxBJ,EAAoB3U,EAAKhN,IACxB,IAAIkiB,EAASliB,GAAUA,EAAOmiB,WAC7B,IAAOniB,EAAiB,QACxB,IAAM,EAEP,OADA2hB,EAAoBrU,EAAE4U,EAAQ,CAAEp3B,EAAGo3B,IAC5BA,GpCNJx8B,EAAWgM,OAAO0wB,eAAkBjoB,GAASzI,OAAO0wB,eAAejoB,GAASA,GAASA,EAAa,UAQtGwnB,EAAoBjE,EAAI,SAASlzB,EAAO+Z,GAEvC,GADU,EAAPA,IAAU/Z,EAAQ+H,KAAK/H,IAChB,EAAP+Z,EAAU,OAAO/Z,EACpB,GAAoB,iBAAVA,GAAsBA,EAAO,CACtC,GAAW,EAAP+Z,GAAa/Z,EAAM23B,WAAY,OAAO33B,EAC1C,GAAW,GAAP+Z,GAAoC,mBAAf/Z,EAAM63B,KAAqB,OAAO73B,CAC5D,CACA,IAAI83B,EAAK5wB,OAAO4Q,OAAO,MACvBqf,EAAoBzW,EAAEoX,GACtB,IAAIC,EAAM,CAAC,EACX98B,EAAiBA,GAAkB,CAAC,KAAMC,EAAS,CAAC,GAAIA,EAAS,IAAKA,EAASA,IAC/E,IAAI,IAAI88B,EAAiB,EAAPje,GAAY/Z,EAAyB,iBAAXg4B,KAAyB/8B,EAAeyd,QAAQsf,GAAUA,EAAU98B,EAAS88B,GACxH9wB,OAAO+wB,oBAAoBD,GAAS5wB,QAASjH,GAAS43B,EAAI53B,GAAO,IAAOH,EAAMG,IAI/E,OAFA43B,EAAa,QAAI,IAAM,EACvBZ,EAAoBrU,EAAEgV,EAAIC,GACnBD,CACR,EqCxBAX,EAAoBrU,EAAI,CAACrN,EAASyiB,KACjC,IAAI,IAAI/3B,KAAO+3B,EACXf,EAAoB7nB,EAAE4oB,EAAY/3B,KAASg3B,EAAoB7nB,EAAEmG,EAAStV,IAC5E+G,OAAOixB,eAAe1iB,EAAStV,EAAK,CAAEi4B,YAAY,EAAMxgB,IAAKsgB,EAAW/3B,MCJ3Eg3B,EAAoBkB,EAAI,CAAC,EAGzBlB,EAAoBrwB,EAAKwxB,GACjBt8B,QAAQC,IAAIiL,OAAOC,KAAKgwB,EAAoBkB,GAAG7S,OAAO,CAAC+S,EAAUp4B,KACvEg3B,EAAoBkB,EAAEl4B,GAAKm4B,EAASC,GAC7BA,GACL,KCNJpB,EAAoBqB,EAAKF,GAEZA,EAAU,cAAgB,CAAC,EAAI,uBAAuB,GAAK,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,uBAAuB,IAAM,wBAAwBA,GCH5bnB,EAAoBsB,EAAI,WACvB,GAA0B,iBAAfC,WAAyB,OAAOA,WAC3C,IACC,OAAO3wB,MAAQ,IAAI4wB,SAAS,cAAb,EAChB,CAAE,MAAO7xB,GACR,GAAsB,iBAAX8xB,OAAqB,OAAOA,MACxC,CACA,CAPuB,GCAxBzB,EAAoB7nB,EAAI,CAACK,EAAKH,IAAUtI,OAAO6kB,UAAU8M,eAAerB,KAAK7nB,EAAKH,GxCA9ErU,EAAa,CAAC,EACdC,EAAoB,2BAExB+7B,EAAoB2B,EAAI,CAACC,EAAKtU,EAAMtkB,EAAKm4B,KACxC,GAAGn9B,EAAW49B,GAAQ59B,EAAW49B,GAAKn2B,KAAK6hB,OAA3C,CACA,IAAIuU,EAAQC,EACZ,QAAWtwB,IAARxI,EAEF,IADA,IAAI+4B,EAAUC,SAASC,qBAAqB,UACpCppB,EAAI,EAAGA,EAAIkpB,EAAQx3B,OAAQsO,IAAK,CACvC,IAAIN,EAAIwpB,EAAQlpB,GAChB,GAAGN,EAAE2pB,aAAa,QAAUN,GAAOrpB,EAAE2pB,aAAa,iBAAmBj+B,EAAoB+E,EAAK,CAAE64B,EAAStpB,EAAG,KAAO,CACpH,CAEGspB,IACHC,GAAa,GACbD,EAASG,SAASG,cAAc,WAEzBC,QAAU,QACjBP,EAAOQ,QAAU,IACbrC,EAAoBsC,IACvBT,EAAOU,aAAa,QAASvC,EAAoBsC,IAElDT,EAAOU,aAAa,eAAgBt+B,EAAoB+E,GAExD64B,EAAO5gB,IAAM2gB,EAC4C,IAArDC,EAAO5gB,IAAIM,QAAQkgB,OAAOn0B,SAASk1B,OAAS,OAC/CX,EAAOY,YAAc,aAEtBZ,EAAOa,UAAY1C,EAAoB2C,UAAUxB,GACjDU,EAAOY,YAAc,aAEtBz+B,EAAW49B,GAAO,CAACtU,GACnB,IAAIsV,EAAmB,CAAC9W,EAAM+W,KAE7BhB,EAAOiB,QAAUjB,EAAOkB,OAAS,KACjCC,aAAaX,GACb,IAAIY,EAAUj/B,EAAW49B,GAIzB,UAHO59B,EAAW49B,GAClBC,EAAOqB,YAAcrB,EAAOqB,WAAWC,YAAYtB,GACnDoB,GAAWA,EAAQhzB,QAASmzB,GAAQA,EAAGP,IACpC/W,EAAM,OAAOA,EAAK+W,IAElBR,EAAUgB,WAAWT,EAAiBU,KAAK,UAAM9xB,EAAW,CAAE/J,KAAM,UAAWF,OAAQs6B,IAAW,MACtGA,EAAOiB,QAAUF,EAAiBU,KAAK,KAAMzB,EAAOiB,SACpDjB,EAAOkB,OAASH,EAAiBU,KAAK,KAAMzB,EAAOkB,QACnDjB,GAAcE,SAASuB,KAAKC,YAAY3B,EAzCkB,GyCH3D7B,EAAoBzW,EAAKjL,IACH,oBAAX1Q,QAA0BA,OAAO61B,aAC1C1zB,OAAOixB,eAAe1iB,EAAS1Q,OAAO61B,YAAa,CAAE56B,MAAO,WAE7DkH,OAAOixB,eAAe1iB,EAAS,aAAc,CAAEzV,OAAO,KCLvDm3B,EAAoB0D,IAAOrlB,IAC1BA,EAAOslB,MAAQ,GACVtlB,EAAOmE,WAAUnE,EAAOmE,SAAW,IACjCnE,GCHR2hB,EAAoBxkB,EAAI,0CCCxBwkB,EAAoB2C,UAAY,CAAC,EAAI,sDAAsD,GAAK,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,sDAAsD,IAAM,uD,MCDhzB3C,EAAoBnX,EAAImZ,SAAS4B,SAAWC,KAAKv2B,SAASsN,KAK1D,IAAIkpB,EAAkB,CACrB,IAAK,GAGN9D,EAAoBkB,EAAEha,EAAI,CAACia,EAASC,KAElC,IAAI2C,EAAqB/D,EAAoB7nB,EAAE2rB,EAAiB3C,GAAW2C,EAAgB3C,QAAW3vB,EACtG,GAA0B,IAAvBuyB,EAGF,GAAGA,EACF3C,EAAS31B,KAAKs4B,EAAmB,QAC3B,CAGL,IAAIC,EAAU,IAAIn/B,QAAQ,CAACwe,EAAS4gB,IAAYF,EAAqBD,EAAgB3C,GAAW,CAAC9d,EAAS4gB,IAC1G7C,EAAS31B,KAAKs4B,EAAmB,GAAKC,GAGtC,IAAIpC,EAAM5B,EAAoBxkB,EAAIwkB,EAAoBqB,EAAEF,GAEpDnyB,EAAQ,IAAIqB,MAgBhB2vB,EAAoB2B,EAAEC,EAfFiB,IACnB,GAAG7C,EAAoB7nB,EAAE2rB,EAAiB3C,KAEf,KAD1B4C,EAAqBD,EAAgB3C,MACR2C,EAAgB3C,QAAW3vB,GACrDuyB,GAAoB,CACtB,IAAIG,EAAYrB,IAAyB,SAAfA,EAAMp7B,KAAkB,UAAYo7B,EAAMp7B,MAChE08B,EAAUtB,GAASA,EAAMt7B,QAAUs7B,EAAMt7B,OAAO0Z,IACpDjS,EAAMo1B,QAAU,iBAAmBjD,EAAU,cAAgB+C,EAAY,KAAOC,EAAU,IAC1Fn1B,EAAMgJ,KAAO,iBACbhJ,EAAMvH,KAAOy8B,EACbl1B,EAAMq1B,QAAUF,EAChBJ,EAAmB,GAAG/0B,EACvB,GAGuC,SAAWmyB,EAASA,EAE/D,GAeH,IAAImD,EAAuB,CAACC,EAA4Bp0B,KACvD,IAGI8vB,EAAUkB,GAHTqD,EAAUC,EAAaC,GAAWv0B,EAGhB0I,EAAI,EAC3B,GAAG2rB,EAASvY,KAAM3mB,GAAgC,IAAxBw+B,EAAgBx+B,IAAa,CACtD,IAAI26B,KAAYwE,EACZzE,EAAoB7nB,EAAEssB,EAAaxE,KACrCD,EAAoBM,EAAEL,GAAYwE,EAAYxE,IAGhD,GAAGyE,EAAsBA,EAAQ1E,EAClC,CAEA,IADGuE,GAA4BA,EAA2Bp0B,GACrD0I,EAAI2rB,EAASj6B,OAAQsO,IACzBsoB,EAAUqD,EAAS3rB,GAChBmnB,EAAoB7nB,EAAE2rB,EAAiB3C,IAAY2C,EAAgB3C,IACrE2C,EAAgB3C,GAAS,KAE1B2C,EAAgB3C,GAAW,GAKzBwD,EAAqBd,KAA0C,oCAAIA,KAA0C,qCAAK,GACtHc,EAAmB10B,QAAQq0B,EAAqBhB,KAAK,KAAM,IAC3DqB,EAAmBl5B,KAAO64B,EAAqBhB,KAAK,KAAMqB,EAAmBl5B,KAAK63B,KAAKqB,G,KClF7D3E,EAAoB,K", "sources": ["webpack://grafana-lokiexplore-app/webpack/runtime/create fake namespace object", "webpack://grafana-lokiexplore-app/webpack/runtime/load script", "webpack://grafana-lokiexplore-app/./node_modules/grafana-public-path.js", "webpack://grafana-lokiexplore-app/./services/extensions/exposedComponents.tsx", "webpack://grafana-lokiexplore-app/./module.tsx", "webpack://grafana-lokiexplore-app/./Components/ServiceScene/ServiceSceneConstants.ts", "webpack://grafana-lokiexplore-app/./services/enums.ts", "webpack://grafana-lokiexplore-app/./services/extensions/links.ts", "webpack://grafana-lokiexplore-app/./services/extensions/scenesMethods.ts", "webpack://grafana-lokiexplore-app/./services/fieldsTypes.ts", "webpack://grafana-lokiexplore-app/./services/filterTypes.ts", "webpack://grafana-lokiexplore-app/./services/logger.ts", "webpack://grafana-lokiexplore-app/./services/logqlMatchers.ts", "webpack://grafana-lokiexplore-app/./services/lokiQuery.ts", "webpack://grafana-lokiexplore-app/./services/narrowing.ts", "webpack://grafana-lokiexplore-app/./services/operatorHelpers.ts", "webpack://grafana-lokiexplore-app/./services/getOperatorDescription.ts", "webpack://grafana-lokiexplore-app/./services/operators.ts", "webpack://grafana-lokiexplore-app/./services/renderPatternFilters.ts", "webpack://grafana-lokiexplore-app/./services/variables.ts", "webpack://grafana-lokiexplore-app/external amd \"@emotion/css\"", "webpack://grafana-lokiexplore-app/external amd \"@grafana/data\"", "webpack://grafana-lokiexplore-app/external amd \"@grafana/runtime\"", "webpack://grafana-lokiexplore-app/external amd \"@grafana/ui\"", "webpack://grafana-lokiexplore-app/external amd \"lodash\"", "webpack://grafana-lokiexplore-app/external amd \"module\"", "webpack://grafana-lokiexplore-app/external amd \"react\"", "webpack://grafana-lokiexplore-app/external amd \"react-dom\"", "webpack://grafana-lokiexplore-app/external amd \"react-redux\"", "webpack://grafana-lokiexplore-app/external amd \"react-router\"", "webpack://grafana-lokiexplore-app/external amd \"redux\"", "webpack://grafana-lokiexplore-app/external amd \"rxjs\"", "webpack://grafana-lokiexplore-app/../node_modules/@lezer/common/dist/index.js", "webpack://grafana-lokiexplore-app/../node_modules/@lezer/lr/dist/index.js", "webpack://grafana-lokiexplore-app/../node_modules/@grafana/lezer-logql/index.es.js", "webpack://grafana-lokiexplore-app/webpack/bootstrap", "webpack://grafana-lokiexplore-app/webpack/runtime/compat get default export", "webpack://grafana-lokiexplore-app/webpack/runtime/define property getters", "webpack://grafana-lokiexplore-app/webpack/runtime/ensure chunk", "webpack://grafana-lokiexplore-app/webpack/runtime/get javascript chunk filename", "webpack://grafana-lokiexplore-app/webpack/runtime/global", "webpack://grafana-lokiexplore-app/webpack/runtime/hasOwnProperty shorthand", "webpack://grafana-lokiexplore-app/webpack/runtime/make namespace object", "webpack://grafana-lokiexplore-app/webpack/runtime/node module decorator", "webpack://grafana-lokiexplore-app/webpack/runtime/publicPath", "webpack://grafana-lokiexplore-app/webpack/runtime/compat", "webpack://grafana-lokiexplore-app/webpack/runtime/jsonp chunk loading", "webpack://grafana-lokiexplore-app/webpack/startup"], "sourcesContent": ["var getProto = Object.getPrototypeOf ? (obj) => (Object.getPrototypeOf(obj)) : (obj) => (obj.__proto__);\nvar leafPrototypes;\n// create a fake namespace object\n// mode & 1: value is a module id, require it\n// mode & 2: merge all properties of value into the ns\n// mode & 4: return value when already ns object\n// mode & 16: return value when it's Promise-like\n// mode & 8|1: behave like require\n__webpack_require__.t = function(value, mode) {\n\tif(mode & 1) value = this(value);\n\tif(mode & 8) return value;\n\tif(typeof value === 'object' && value) {\n\t\tif((mode & 4) && value.__esModule) return value;\n\t\tif((mode & 16) && typeof value.then === 'function') return value;\n\t}\n\tvar ns = Object.create(null);\n\t__webpack_require__.r(ns);\n\tvar def = {};\n\tleafPrototypes = leafPrototypes || [null, getProto({}), getProto([]), getProto(getProto)];\n\tfor(var current = mode & 2 && value; typeof current == 'object' && !~leafPrototypes.indexOf(current); current = getProto(current)) {\n\t\tObject.getOwnPropertyNames(current).forEach((key) => (def[key] = () => (value[key])));\n\t}\n\tdef['default'] = () => (value);\n\t__webpack_require__.d(ns, def);\n\treturn ns;\n};", "var inProgress = {};\nvar dataWebpackPrefix = \"grafana-lokiexplore-app:\";\n// loadScript function to load a script via script tag\n__webpack_require__.l = (url, done, key, chunkId) => {\n\tif(inProgress[url]) { inProgress[url].push(done); return; }\n\tvar script, needAttach;\n\tif(key !== undefined) {\n\t\tvar scripts = document.getElementsByTagName(\"script\");\n\t\tfor(var i = 0; i < scripts.length; i++) {\n\t\t\tvar s = scripts[i];\n\t\t\tif(s.getAttribute(\"src\") == url || s.getAttribute(\"data-webpack\") == dataWebpackPrefix + key) { script = s; break; }\n\t\t}\n\t}\n\tif(!script) {\n\t\tneedAttach = true;\n\t\tscript = document.createElement('script');\n\n\t\tscript.charset = 'utf-8';\n\t\tscript.timeout = 120;\n\t\tif (__webpack_require__.nc) {\n\t\t\tscript.setAttribute(\"nonce\", __webpack_require__.nc);\n\t\t}\n\t\tscript.setAttribute(\"data-webpack\", dataWebpackPrefix + key);\n\n\t\tscript.src = url;\n\t\tif (script.src.indexOf(window.location.origin + '/') !== 0) {\n\t\t\tscript.crossOrigin = \"anonymous\";\n\t\t}\n\t\tscript.integrity = __webpack_require__.sriHashes[chunkId];\n\t\tscript.crossOrigin = \"anonymous\";\n\t}\n\tinProgress[url] = [done];\n\tvar onScriptComplete = (prev, event) => {\n\t\t// avoid mem leaks in IE.\n\t\tscript.onerror = script.onload = null;\n\t\tclearTimeout(timeout);\n\t\tvar doneFns = inProgress[url];\n\t\tdelete inProgress[url];\n\t\tscript.parentNode && script.parentNode.removeChild(script);\n\t\tdoneFns && doneFns.forEach((fn) => (fn(event)));\n\t\tif(prev) return prev(event);\n\t}\n\tvar timeout = setTimeout(onScriptComplete.bind(null, undefined, { type: 'timeout', target: script }), 120000);\n\tscript.onerror = onScriptComplete.bind(null, script.onerror);\n\tscript.onload = onScriptComplete.bind(null, script.onload);\n\tneedAttach && document.head.appendChild(script);\n};", "\nimport amdMetaModule from 'amd-module';\n\n__webpack_public_path__ =\n  amdMetaModule && amdMetaModule.uri\n    ? amdMetaModule.uri.slice(0, amdMetaModule.uri.lastIndexOf('/') + 1)\n    : 'public/plugins/grafana-lokiexplore-app/';\n", "import React, { lazy, Suspense } from 'react';\n\nimport { LinkButton } from '@grafana/ui';\n\nimport { EmbeddedLogsExplorationProps } from 'Components/EmbeddedLogsExploration/types';\nimport { OpenInLogsDrilldownButtonProps } from 'Components/OpenInLogsDrilldownButton/types';\nconst OpenInLogsDrilldownButton = lazy(() => import('Components/OpenInLogsDrilldownButton/OpenInLogsDrilldownButton'));\nconst EmbeddedLogsExploration = lazy(() => import('Components/EmbeddedLogsExploration/EmbeddedLogs'));\n\nexport function SuspendedOpenInLogsDrilldownButton(props: OpenInLogsDrilldownButtonProps) {\n  return (\n    <Suspense\n      fallback={\n        <LinkButton variant=\"secondary\" disabled>\n          Open in Logs Drilldown\n        </LinkButton>\n      }\n    >\n      <OpenInLogsDrilldownButton {...props} />\n    </Suspense>\n  );\n}\n\nexport function SuspendedEmbeddedLogsExploration(props: EmbeddedLogsExplorationProps) {\n  return (\n    <Suspense fallback={<div>Loading Logs Drilldown...</div>}>\n      <EmbeddedLogsExploration {...props} />\n    </Suspense>\n  );\n}\n", "import { lazy } from 'react';\n\nimport { AppPlugin } from '@grafana/data';\n\nimport {\n  SuspendedEmbeddedLogsExploration,\n  SuspendedOpenInLogsDrilldownButton,\n} from 'services/extensions/exposedComponents';\nimport { linkConfigs } from 'services/extensions/links';\n\n// Anything imported in this file is included in the main bundle which is pre-loaded in Grafana\n// Don't add imports to this file without lazy loading\n// Link extensions are the exception as they must be included in the main bundle in order to work in core Grafana\nconst App = lazy(async () => {\n  const { wasmSupported } = await import('services/sorting');\n\n  const { default: initRuntimeDs } = await import('services/datasource');\n  const { default: initChangepoint } = await import('@bsull/augurs/changepoint');\n  const { default: initOutlier } = await import('@bsull/augurs/outlier');\n\n  initRuntimeDs();\n\n  if (wasmSupported()) {\n    await Promise.all([initChangepoint(), initOutlier()]);\n  }\n\n  return import('Components/App');\n});\n\nconst AppConfig = lazy(async () => {\n  return await import('./Components/AppConfig/AppConfig');\n});\n\nexport const plugin = new AppPlugin<{}>().setRootPage(App).addConfigPage({\n  body: AppConfig,\n  icon: 'cog',\n  id: 'configuration',\n  title: 'Configuration',\n});\n\nfor (const linkConfig of linkConfigs) {\n  plugin.addLink(linkConfig);\n}\n\nplugin.exposeComponent({\n  component: SuspendedOpenInLogsDrilldownButton,\n  description: 'A button that opens a logs view in the Logs Drilldown app.',\n  id: `grafana-lokiexplore-app/open-in-explore-logs-button/v1`,\n  title: 'Open in Logs Drilldown button',\n});\n\nplugin.exposeComponent({\n  component: SuspendedEmbeddedLogsExploration,\n  description: 'A component that renders a logs exploration view that can be embedded in other parts of Grafana.',\n  id: `grafana-lokiexplore-app/embedded-logs-exploration/v1`,\n  title: 'Embedded Logs Exploration',\n});\n", "export const pageSlugUrlKey = 'pageSlug';\nexport const drilldownLabelUrlKey = 'drillDownLabel';\n", "// Circular dependencies can cause enums to return as undefined in jest tests, moving enums here\nexport enum TabNames {\n  logs = 'Logs',\n  labels = 'Labels',\n  fields = 'Fields',\n  patterns = 'Patterns',\n}\n\nexport enum PageSlugs {\n  explore = 'explore',\n  logs = 'logs',\n  labels = 'labels',\n  patterns = 'patterns',\n  fields = 'fields',\n  embed = 'embed',\n}\n\nexport enum ValueSlugs {\n  field = 'field',\n  label = 'label',\n}\n", "// Warning: This file (and any imports) are included in the main bundle with Grafana in order to provide link extension support in Grafana core, in an effort to keep Grafana loading quickly, please do not add any unnecessary imports to this file and run the bundle analyzer before committing any changes!\nimport { map as lodashMap } from 'lodash';\n\nimport {\n  CustomVariableModel,\n  PluginExtensionAddedLinkConfig,\n  PluginExtensionPanelContext,\n  PluginExtensionPoints,\n  QueryVariableModel,\n} from '@grafana/data';\nimport { getTemplateSrv, locationService } from '@grafana/runtime';\n\nimport pluginJson from '../../plugin.json';\nimport { LabelType } from '../fieldsTypes';\nimport { FieldFilter, IndexedLabelFilter, LineFilterType, PatternFilterOp, PatternFilterType } from '../filterTypes';\nimport { getMatcherFromQuery } from '../logqlMatchers';\nimport { LokiQuery } from '../lokiQuery';\nimport { isOperatorInclusive } from '../operatorHelpers';\nimport { renderPatternFilters } from '../renderPatternFilters';\nimport { escapeLabelValueInExactSelector, lokiSpecialRegexEscape } from './scenesMethods';\nimport {\n  addAdHocFilterUserInputPrefix,\n  AdHocFieldValue,\n  AppliedPattern,\n  EMPTY_VARIABLE_VALUE,\n  LEVEL_VARIABLE_VALUE,\n  SERVICE_NAME,\n  stripAdHocFilterUserInputPrefix,\n  VAR_DATASOURCE,\n  VAR_FIELDS,\n  VAR_LABELS,\n  VAR_LEVELS,\n  VAR_LINE_FILTERS,\n  VAR_METADATA,\n  VAR_PATTERNS,\n} from 'services/variables';\n\nconst PRODUCT_NAME = 'Grafana Logs Drilldown';\nconst title = `Open in ${PRODUCT_NAME}`;\nconst description = `Open current query in the ${PRODUCT_NAME} view`;\nconst icon = 'gf-logs';\n\nexport const ExtensionPoints = {\n  MetricInvestigation: 'grafana-lokiexplore-app/investigation/v1',\n} as const;\n\nexport type LinkConfigs = Array<PluginExtensionAddedLinkConfig<PluginExtensionPanelContext>>;\n\nexport const linkConfigs: LinkConfigs = [\n  {\n    targets: [\n      PluginExtensionPoints.DashboardPanelMenu,\n      PluginExtensionPoints.ExploreToolbarAction,\n      'grafana-metricsdrilldown-app/open-in-logs-drilldown/v1',\n      'grafana-assistant-app/navigateToDrilldown/v1',\n    ],\n    title,\n    description,\n    icon,\n    path: createAppUrl(),\n    configure: contextToLink,\n  },\n];\n\nfunction stringifyValues(value?: string): string {\n  if (!value) {\n    return EMPTY_VARIABLE_VALUE;\n  }\n  return value;\n}\n\n// Why are there twice as many escape chars in the url as expected?\nexport function replaceEscapeChars(value?: string): string | undefined {\n  return value?.replace(/\\\\\\\\/g, '\\\\');\n}\n\nexport function stringifyAdHocValues(value?: string): string {\n  if (!value) {\n    return EMPTY_VARIABLE_VALUE;\n  }\n\n  // All label values from explore are already escaped, so we mark them as custom values to prevent them from getting escaped again when rendering the LogQL\n  return addAdHocFilterUserInputPrefix(replaceEscapeChars(value));\n}\n\nexport function stringifyAdHocValueLabels(value?: string): string {\n  if (!value) {\n    return EMPTY_VARIABLE_VALUE;\n  }\n\n  return escapeURLDelimiters(replaceEscapeChars(value));\n}\n\nfunction setUrlParamsFromFieldFilters(fields: FieldFilter[], params: URLSearchParams) {\n  for (const field of fields) {\n    if (field.type === LabelType.StructuredMetadata) {\n      if (field.key === LEVEL_VARIABLE_VALUE) {\n        params = appendUrlParameter(\n          UrlParameters.Levels,\n          `${field.key}|${field.operator}|${escapeURLDelimiters(stringifyValues(field.value))}`,\n          params\n        );\n      } else {\n        params = appendUrlParameter(\n          UrlParameters.Metadata,\n          `${field.key}|${field.operator}|${escapeURLDelimiters(\n            stringifyAdHocValues(field.value)\n          )},${escapeURLDelimiters(replaceEscapeChars(field.value))}`,\n          params\n        );\n      }\n    } else {\n      const fieldValue: AdHocFieldValue = {\n        value: field.value,\n        parser: field.parser,\n      };\n\n      const adHocFilterURLString = `${field.key}|${field.operator}|${escapeURLDelimiters(\n        stringifyAdHocValues(JSON.stringify(fieldValue))\n      )},${stringifyAdHocValueLabels(fieldValue.value)}`;\n\n      params = appendUrlParameter(UrlParameters.Fields, adHocFilterURLString, params);\n    }\n  }\n  return params;\n}\n\nfunction setUrlParamsFromLabelFilters(labelFilters: IndexedLabelFilter[], params: URLSearchParams) {\n  for (const labelFilter of labelFilters) {\n    // skip non-indexed filters for now\n    if (labelFilter.type !== LabelType.Indexed) {\n      continue;\n    }\n\n    const labelsAdHocFilterURLString = `${labelFilter.key}|${labelFilter.operator}|${escapeURLDelimiters(\n      stringifyAdHocValues(labelFilter.value)\n    )},${escapeURLDelimiters(replaceEscapeChars(labelFilter.value))}`;\n\n    params = appendUrlParameter(UrlParameters.Labels, labelsAdHocFilterURLString, params);\n  }\n  return params;\n}\n\nfunction setLineFilterUrlParams(lineFilters: LineFilterType[], params: URLSearchParams) {\n  for (const lineFilter of lineFilters) {\n    params = appendUrlParameter(\n      UrlParameters.LineFilters,\n      `${lineFilter.key}|${escapeURLDelimiters(lineFilter.operator)}|${escapeURLDelimiters(\n        stringifyValues(lineFilter.value)\n      )}`,\n      params\n    );\n  }\n  return params;\n}\n\nexport function setUrlParamsFromPatterns(patternFilters: PatternFilterType[], params: URLSearchParams) {\n  const patterns: AppliedPattern[] = [];\n\n  for (const field of patternFilters) {\n    patterns.push({\n      type: field.operator === PatternFilterOp.match ? 'include' : 'exclude',\n      pattern: stringifyValues(field.value),\n    });\n  }\n\n  let patternsString = renderPatternFilters(patterns);\n\n  params = appendUrlParameter(UrlParameters.Patterns, JSON.stringify(patterns), params);\n  return appendUrlParameter(UrlParameters.PatternsVariable, patternsString, params);\n}\n\nfunction contextToLink<T extends PluginExtensionPanelContext>(context?: T) {\n  if (!context) {\n    return undefined;\n  }\n  const lokiQuery = context.targets.find((target) => target.datasource?.type === 'loki') as LokiQuery | undefined;\n  const templateSrv = getTemplateSrv();\n  const dataSourceUid = templateSrv.replace(lokiQuery?.datasource?.uid, context.scopedVars);\n\n  if (!lokiQuery || !dataSourceUid) {\n    return undefined;\n  }\n\n  // if there is no loki expression but the datasource is loki, then return createAppUrl()\n  if (!lokiQuery?.expr) {\n    return { path: createAppUrl() };\n  }\n\n  const expr = templateSrv.replace(lokiQuery.expr, context.scopedVars, interpolateQueryExpr);\n  const { fields, labelFilters, lineFilters, patternFilters } = getMatcherFromQuery(expr, context, lokiQuery);\n  const labelSelector = labelFilters.find((selector) => isOperatorInclusive(selector.operator));\n\n  // If there's no label selector, return a link to the service selection\n  // @todo it would be better if we could change the button copy (or tooltip) depending on the link destination\n  if (!labelSelector) {\n    return {\n      path: createAppUrl(),\n    };\n  }\n\n  // If there are a bunch of values for the same field, the value slug can get really long, let's just use the first one in the URL\n  const urlLabelValue = labelSelector.value.split('|')[0];\n  const labelValue = replaceSlash(urlLabelValue);\n  let labelName = labelSelector.key === SERVICE_NAME ? 'service' : labelSelector.key;\n  // sort `primary label` first\n  labelFilters.sort((a) => (a.key === labelName ? -1 : 1));\n\n  let params = setUrlParameter(UrlParameters.DatasourceId, dataSourceUid, new URLSearchParams());\n  params = setUrlParameter(UrlParameters.TimeRangeFrom, context.timeRange.from.valueOf().toString(), params);\n  params = setUrlParameter(UrlParameters.TimeRangeTo, context.timeRange.to.valueOf().toString(), params);\n  params = setUrlParamsFromLabelFilters(labelFilters, params);\n\n  if (lineFilters) {\n    params = setLineFilterUrlParams(lineFilters, params);\n  }\n  if (fields?.length) {\n    params = setUrlParamsFromFieldFilters(fields, params);\n  }\n  if (patternFilters?.length) {\n    params = setUrlParamsFromPatterns(patternFilters, params);\n  }\n\n  return {\n    path: createAppUrl(`/explore/${labelName}/${labelValue}/logs`, params),\n  };\n}\n\nexport function createAppUrl(path = '/explore', urlParams?: URLSearchParams): string {\n  return `/a/${pluginJson.id}${path}${urlParams ? `?${urlParams.toString()}` : ''}`;\n}\n\nexport const UrlParameters = {\n  DatasourceId: `var-${VAR_DATASOURCE}`,\n  TimeRangeFrom: 'from',\n  TimeRangeTo: 'to',\n  Labels: `var-${VAR_LABELS}`,\n  Fields: `var-${VAR_FIELDS}`,\n  Metadata: `var-${VAR_METADATA}`,\n  Levels: `var-${VAR_LEVELS}`,\n  LineFilters: `var-${VAR_LINE_FILTERS}`,\n  Patterns: VAR_PATTERNS,\n  PatternsVariable: `var-${VAR_PATTERNS}`,\n} as const;\nexport type UrlParameterType = (typeof UrlParameters)[keyof typeof UrlParameters];\n\nexport function setUrlParameter(key: UrlParameterType, value: string, initalParams?: URLSearchParams): URLSearchParams {\n  const searchParams = new URLSearchParams(initalParams?.toString() ?? locationService.getSearch());\n  searchParams.set(key, value);\n\n  return searchParams;\n}\n\nexport function appendUrlParameter(\n  key: UrlParameterType,\n  value: string,\n  initalParams?: URLSearchParams\n): URLSearchParams {\n  const location = locationService.getLocation();\n  const searchParams = new URLSearchParams(initalParams?.toString() ?? location.search);\n  searchParams.append(key, value);\n\n  return searchParams;\n}\n\nexport function replaceSlash(parameter: string): string {\n  return (\n    stripAdHocFilterUserInputPrefix(parameter)\n      // back-slash is converted to forward-slash in the URL, replace that char\n      .replace(/\\//g, '-')\n      .replace(/\\\\/g, '-')\n  );\n}\n\n// Manually copied over from @grafana/scenes so we don't need to import scenes to build links\nfunction escapeUrlCommaDelimiters(value: string | undefined): string {\n  if (value === null || value === undefined) {\n    return '';\n  }\n\n  // Replace the comma due to using it as a value/label separator\n  return /,/g[Symbol.replace](value, '__gfc__');\n}\n\nexport function escapeUrlPipeDelimiters(value: string | undefined): string {\n  if (value === null || value === undefined) {\n    return '';\n  }\n\n  // Replace the pipe due to using it as a filter separator\n  return (value = /\\|/g[Symbol.replace](value, '__gfp__'));\n}\n\nexport function escapeURLDelimiters(value: string | undefined): string {\n  return escapeUrlCommaDelimiters(escapeUrlPipeDelimiters(value));\n}\n\n// Copied from interpolateQueryExpr in loki datasource, as we can't return a promise in the link extension config we can't fetch the datasource from the datasource srv, so we're forced to duplicate this method\nexport function interpolateQueryExpr(value: string | unknown[], variable: QueryVariableModel | CustomVariableModel) {\n  // if no multi or include all do not regexEscape\n  if (!variable.multi && !variable.includeAll) {\n    return value;\n  }\n\n  if (typeof value === 'string') {\n    return escapeLabelValueInExactSelector(value);\n  }\n\n  const escapedValues = lodashMap(value, lokiSpecialRegexEscape);\n  return escapedValues.join('|');\n}\n", "// Warning, this file is included in the main module.tsx bundle, and doesn't contain any imports to keep that bundle size small. Don't add imports to this file!\n\n/**\n * Methods copied from scenes that we want in the module (to generate links which cannot be lazy loaded), without including all of scenes.\n * See https://github.com/grafana/scenes/issues/1046\n */\n// based on the openmetrics-documentation, the 3 symbols we have to handle are:\n// - \\n ... the newline character\n// - \\  ... the backslash character\n// - \"  ... the double-quote character\nexport function escapeLabelValueInExactSelector(labelValue: string): string {\n  return labelValue.replace(/\\\\/g, '\\\\\\\\').replace(/\\n/g, '\\\\n').replace(/\"/g, '\\\\\"');\n}\n\n// Pulled from loki datasource\nexport function lokiSpecialRegexEscape<T>(value: T) {\n  if (typeof value === 'string') {\n    return value.replace(/\\\\/g, '\\\\\\\\\\\\\\\\').replace(/[$^*{}\\[\\]+?.()|]/g, '\\\\\\\\$&');\n  }\n  return value;\n}\n", "// copied from public/app/plugins/datasource/loki/types.ts\nexport enum LabelType {\n  Indexed = 'I',\n  StructuredMetadata = 'S',\n  Parsed = 'P',\n}\n", "// Warning: This file (and any imports) are included in the main bundle with Grafana in order to provide link extension support in Grafana core, in an effort to keep Grafana loading quickly, please do not add any unnecessary imports to this file and run the bundle analyzer before committing any changes!\n\nimport { LabelType } from './fieldsTypes';\nimport { ParserType } from './variables';\n\nexport type FilterOpType = LabelFilterOp | NumericFilterOp;\nexport enum LabelFilterOp {\n  Equal = '=',\n  NotEqual = '!=',\n  RegexEqual = '=~',\n  RegexNotEqual = '!~',\n}\n// Line filter doesn't have an operator, so we add an empty space to keep it in URL state\nexport enum LineFormatFilterOp {\n  Empty = ' ',\n}\n\nexport enum NumericFilterOp {\n  gt = '>',\n  lt = '<',\n  gte = '>=',\n  lte = '<=',\n}\nexport const FilterOp = { ...LabelFilterOp, ...NumericFilterOp };\n\nexport type IndexedLabelFilter = {\n  key: string;\n  operator: FilterOpType;\n  type?: LabelType;\n  value: string;\n};\n\nexport type FieldFilter = {\n  key: string;\n  operator: FilterOpType;\n  parser?: ParserType;\n  type?: LabelType;\n  value: string;\n};\n\nexport type LineFilterType = {\n  key: string;\n  operator: LineFilterOp;\n  value: string;\n};\n\nexport type PatternFilterType = {\n  operator: PatternFilterOp;\n  value: string;\n};\n\nexport enum LineFilterOp {\n  match = '|=',\n  negativeMatch = `!=`,\n  regex = '|~',\n  negativeRegex = `!~`,\n}\n\nexport enum PatternFilterOp {\n  match = '|>',\n  negativeMatch = '!>',\n}\n\nexport enum LineFilterCaseSensitive {\n  caseSensitive = 'caseSensitive',\n  caseInsensitive = 'caseInsensitive',\n}\n", "import { LogContext } from '@grafana/faro-web-sdk';\nimport { FetchError, logError, logInfo, logWarning } from '@grafana/runtime';\n\nimport packageJson from '../../package.json';\nimport pluginJson from '../plugin.json';\nimport { isRecord } from './narrowing';\n\nconst defaultContext = {\n  app: pluginJson.id,\n  version: packageJson.version,\n};\n\nexport const logger = {\n  error: (err: Error | unknown, context?: LogContext) => {\n    const ctx = { ...defaultContext, ...context };\n    console.error(err, ctx);\n    attemptFaroErr(err, ctx);\n  },\n  info: (msg: string, context?: LogContext) => {\n    const ctx = { ...defaultContext, ...context };\n    console.log(msg, ctx);\n    attemptFaroInfo(msg, ctx);\n  },\n  warn: (msg: string, context?: LogContext) => {\n    const ctx = { ...defaultContext, ...context };\n    console.warn(msg, ctx);\n    attemptFaroWarn(msg, ctx);\n  },\n};\n\nconst attemptFaroInfo = (msg: string, context?: LogContext) => {\n  try {\n    logInfo(msg, context);\n  } catch (e) {\n    console.warn('Failed to log faro event!');\n  }\n};\n\nconst attemptFaroWarn = (msg: string, context?: LogContext) => {\n  try {\n    logWarning(msg, context);\n  } catch (e) {\n    console.warn('Failed to log faro warning!', { context, msg });\n  }\n};\n/**\n * Checks unknown error for properties from Records like FetchError and adds them to the context\n * @param err\n * @param context\n */\nfunction populateFetchErrorContext(err: unknown | FetchError, context: LogContext) {\n  if (typeof err === 'object' && err !== null) {\n    if (isRecord(err)) {\n      Object.keys(err).forEach((key: string) => {\n        const value = err[key];\n        if (typeof value === 'string' || typeof value === 'boolean' || typeof value === 'number') {\n          context[key] = value.toString();\n        }\n      });\n    }\n\n    if (hasData(err)) {\n      if (typeof err.data === 'object' && err.data !== null) {\n        try {\n          context.data = JSON.stringify(err.data);\n        } catch (e) {\n          // do nothing\n        }\n      } else if (typeof err.data === 'string' || typeof err.data === 'boolean' || typeof err.data === 'number') {\n        context.data = err.data.toString();\n      }\n    }\n  }\n}\n\nconst attemptFaroErr = (err: Error | FetchError | unknown, context2: LogContext) => {\n  let context = context2;\n  try {\n    populateFetchErrorContext(err, context);\n\n    if (err instanceof Error) {\n      logError(err, context);\n    } else if (typeof err === 'string') {\n      logError(new Error(err), context);\n    } else if (err && typeof err === 'object') {\n      if (context.msg) {\n        logError(new Error(context.msg), context);\n      } else {\n        logError(new Error('error object'), context);\n      }\n    } else {\n      logError(new Error('unknown error'), context);\n    }\n  } catch (e) {\n    console.error('Failed to log faro error!', { context, err });\n  }\n};\n\nconst hasData = (value: object): value is { data: unknown } => {\n  return 'data' in value;\n};\n", "// Warning: This file (and any imports) are included in the main bundle with Grafana in order to provide link extension support in Grafana core, in an effort to keep Grafana loading quickly, please do not add any unnecessary imports to this file and run the bundle analyzer before committing any changes!\n\nimport { NodeType, SyntaxNode, Tree } from '@lezer/common';\n\nimport { PluginExtensionPanelContext } from '@grafana/data';\nimport {\n  Bytes,\n  Duration,\n  Eq,\n  FilterOp,\n  Gte,\n  Gtr,\n  Identifier,\n  <PERSON>son,\n  LabelFilter,\n  LineFilter,\n  Logfmt,\n  Lss,\n  Lte,\n  Matcher,\n  Neq,\n  Npa,\n  Nre,\n  Number,\n  OrFilter,\n  parser,\n  PipeExact,\n  PipeMatch,\n  PipePattern,\n  Re,\n  Selector,\n  String,\n} from '@grafana/lezer-logql';\n\nimport { LabelType } from './fieldsTypes';\nimport {\n  FieldFilter,\n  FilterOp as FilterOperator,\n  FilterOpType,\n  IndexedLabelFilter,\n  LineFilterCaseSensitive,\n  LineFilterOp,\n  LineFilterType,\n  PatternFilterOp,\n  PatternFilterType,\n} from './filterTypes';\nimport { getLabelType<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>Query } from './lokiQuery';\nimport { ParserType } from './variables';\n\nexport class NodePosition {\n  from: number;\n  to: number;\n  type?: NodeType;\n  syntaxNode?: SyntaxNode;\n\n  constructor(from: number, to: number, syntaxNode?: SyntaxNode, type?: NodeType) {\n    this.from = from;\n    this.to = to;\n    this.type = type;\n    this.syntaxNode = syntaxNode;\n  }\n\n  static fromNode(node: SyntaxNode): NodePosition {\n    return new NodePosition(node.from, node.to, node, node.type);\n  }\n\n  contains(position: NodePosition): boolean {\n    return this.from <= position.from && this.to >= position.to;\n  }\n\n  getExpression(query: string): string {\n    return query.substring(this.from, this.to);\n  }\n}\n\nexport function getNodesFromQuery(query: string, nodeTypes?: number[]): SyntaxNode[] {\n  const nodes: SyntaxNode[] = [];\n  const tree: Tree = parser.parse(query);\n  tree.iterate({\n    enter: (node): false | void => {\n      if (nodeTypes === undefined || nodeTypes.includes(node.type.id)) {\n        nodes.push(node.node);\n      }\n    },\n  });\n  return nodes;\n}\n\nfunction getAllPositionsInNodeByType(node: SyntaxNode, type: number): NodePosition[] {\n  if (node.type.id === type) {\n    return [NodePosition.fromNode(node)];\n  }\n\n  const positions: NodePosition[] = [];\n  let pos = 0;\n  let child = node.childAfter(pos);\n  while (child) {\n    positions.push(...getAllPositionsInNodeByType(child, type));\n    pos = child.to;\n    child = node.childAfter(pos);\n  }\n  return positions;\n}\n\nfunction parseLabelFilters(query: string) {\n  const filters: IndexedLabelFilter[] = [];\n  // `Matcher` will select field filters as well as indexed label filters\n  const allMatcher = getNodesFromQuery(query, [Matcher]);\n  for (const matcher of allMatcher) {\n    const identifierPosition = getAllPositionsInNodeByType(matcher, Identifier);\n    if (!identifierPosition || identifierPosition.length === 0) {\n      continue;\n    }\n\n    const valuePosition = getAllPositionsInNodeByType(matcher, String);\n    const operator = query.substring(identifierPosition[0].to, valuePosition[0].from);\n    const key = identifierPosition[0].getExpression(query);\n    const value = valuePosition.map((position) => query.substring(position.from + 1, position.to - 1))[0];\n\n    if (\n      !key ||\n      !value ||\n      (operator !== FilterOperator.NotEqual &&\n        operator !== FilterOperator.Equal &&\n        operator !== FilterOperator.RegexEqual &&\n        operator !== FilterOperator.RegexNotEqual)\n    ) {\n      continue;\n    }\n\n    filters.push({\n      key,\n      operator,\n      type: LabelType.Indexed,\n      value,\n    });\n  }\n  return filters;\n}\n\nfunction parseNonPatternFilters(\n  lineFilterValue: string,\n  quoteString: string,\n  lineFilters: LineFilterType[],\n  index: number,\n  operator: LineFilterOp\n) {\n  const isRegexSelector = operator === LineFilterOp.regex || operator === LineFilterOp.negativeRegex;\n  const isCaseInsensitive = lineFilterValue.includes('(?i)') && isRegexSelector;\n\n  // If quoteString is `, we shouldn't need to un-escape anything\n  // But if the quoteString is \", we'll need to remove double escape chars, as these values are re-escaped when building the query expression (but not stored in the value/url)\n  if (quoteString === '\"' && isRegexSelector) {\n    // replace \\\\ with \\\n    const replaceDoubleEscape = new RegExp(/\\\\\\\\/, 'g');\n    lineFilterValue = lineFilterValue.replace(replaceDoubleEscape, '\\\\');\n  } else if (quoteString === '\"') {\n    // replace \\\\\\\" => \"\n    const replaceDoubleQuoteEscape = new RegExp(`\\\\\\\\\\\"`, 'g');\n    lineFilterValue = lineFilterValue.replace(replaceDoubleQuoteEscape, '\"');\n    const replaceDoubleEscape = new RegExp(/\\\\\\\\/, 'g');\n    lineFilterValue = lineFilterValue.replace(replaceDoubleEscape, '\\\\');\n  }\n\n  if (isCaseInsensitive) {\n    // If `(?i)` exists in a regex it would need to be escaped to match log lines containing `(?i)`, so it should be safe to replace all instances of `(?i)` in the line filter?\n    lineFilterValue = lineFilterValue.replace('(?i)', '');\n  }\n\n  lineFilters.push({\n    key: isCaseInsensitive\n      ? LineFilterCaseSensitive.caseInsensitive.toString()\n      : LineFilterCaseSensitive.caseSensitive.toString() + ',' + index.toString(),\n    operator: operator,\n    value: lineFilterValue,\n  });\n\n  return lineFilterValue;\n}\n\nfunction parsePatternFilters(lineFilterValue: string, patternFilters: PatternFilterType[], operator: PatternFilterOp) {\n  const replaceDoubleQuoteEscape = new RegExp(/\\\\\"/, 'g');\n  lineFilterValue = lineFilterValue.replace(replaceDoubleQuoteEscape, '\"');\n  patternFilters.push({\n    operator,\n    value: lineFilterValue,\n  });\n}\n\nfunction parseLineFilters(query: string) {\n  const lineFilters: LineFilterType[] = [];\n  const patternFilters: PatternFilterType[] = [];\n  const allLineFilters = getNodesFromQuery(query, [LineFilter]);\n  for (const [index, matcher] of allLineFilters.entries()) {\n    const equal = getAllPositionsInNodeByType(matcher, PipeExact);\n    const pipeRegExp = getAllPositionsInNodeByType(matcher, PipeMatch);\n    const notEqual = getAllPositionsInNodeByType(matcher, Neq);\n    const notEqualRegExp = getAllPositionsInNodeByType(matcher, Nre);\n    const patternInclude = getAllPositionsInNodeByType(matcher, PipePattern);\n    const patternExclude = getAllPositionsInNodeByType(matcher, Npa);\n\n    const lineFilterValueNodes = getStringsFromLineFilter(matcher);\n\n    for (const lineFilterValueNode of lineFilterValueNodes) {\n      const quoteString = query.substring(lineFilterValueNode?.from + 1, lineFilterValueNode?.from);\n\n      // Remove quotes\n      let lineFilterValue = query.substring(lineFilterValueNode?.from + 1, lineFilterValueNode?.to - 1);\n\n      if (lineFilterValue.length) {\n        let operator;\n        if (equal.length) {\n          operator = LineFilterOp.match;\n        } else if (notEqual.length) {\n          operator = LineFilterOp.negativeMatch;\n        } else if (notEqualRegExp.length) {\n          operator = LineFilterOp.negativeRegex;\n        } else if (pipeRegExp.length) {\n          operator = LineFilterOp.regex;\n        } else if (patternInclude.length) {\n          operator = PatternFilterOp.match;\n        } else if (patternExclude.length) {\n          operator = PatternFilterOp.negativeMatch;\n        } else {\n          console.warn('unknown line filter', {\n            query: query.substring(matcher.from, matcher.to),\n          });\n\n          continue;\n        }\n\n        if (!(operator === PatternFilterOp.match || operator === PatternFilterOp.negativeMatch)) {\n          parseNonPatternFilters(lineFilterValue, quoteString, lineFilters, index, operator);\n        } else {\n          parsePatternFilters(lineFilterValue, patternFilters, operator);\n        }\n      }\n    }\n  }\n  return { lineFilters, patternFilters };\n}\n\nfunction getNumericFieldOperator(matcher: SyntaxNode) {\n  if (getAllPositionsInNodeByType(matcher, Lte).length) {\n    return FilterOperator.lte;\n  } else if (getAllPositionsInNodeByType(matcher, Lss).length) {\n    return FilterOperator.lt;\n  } else if (getAllPositionsInNodeByType(matcher, Gte).length) {\n    return FilterOperator.gte;\n  } else if (getAllPositionsInNodeByType(matcher, Gtr).length) {\n    return FilterOperator.gt;\n  }\n\n  console.warn('unknown numeric operator');\n\n  return undefined;\n}\n\nfunction getStringFieldOperator(matcher: SyntaxNode) {\n  if (getAllPositionsInNodeByType(matcher, Eq).length) {\n    return FilterOperator.Equal; // =\n  } else if (getAllPositionsInNodeByType(matcher, Neq).length) {\n    return FilterOperator.NotEqual; // !=\n  } else if (getAllPositionsInNodeByType(matcher, Re).length) {\n    return FilterOperator.RegexEqual; // =~\n  } else if (getAllPositionsInNodeByType(matcher, Nre).length) {\n    return FilterOperator.RegexNotEqual; // !~\n  }\n\n  return undefined;\n}\n\nfunction parseFields(query: string, context?: PluginExtensionPanelContext, lokiQuery?: LokiQuery) {\n  const fields: FieldFilter[] = [];\n  const dataFrame = context?.data?.series.find((frame) => frame.refId === lokiQuery?.refId);\n  // We do not currently support \"or\" in Grafana Logs Drilldown, so grab the left hand side LabelFilter leaf nodes as this will be the first filter expression in a given pipeline stage\n  const allFields = getNodesFromQuery(query, [LabelFilter]);\n  for (const matcher of allFields) {\n    const position = NodePosition.fromNode(matcher);\n    const expression = position.getExpression(query);\n    const isParentNode = matcher.getChild(LabelFilter);\n\n    // If the Label filter contains other Label Filter nodes, we want to skip this node so we only add the leaf LabelFilter nodes\n    if (isParentNode) {\n      continue;\n    }\n\n    // Skip error expression, it will get added automatically when Grafana Logs Drilldown adds a parser\n    if (expression.substring(0, 9) === `__error__`) {\n      continue;\n    }\n\n    // @todo we need to use detected_fields API to get the \"right\" parser for a specific field\n    // Currently we just check to see if there is a parser before the current node, this means that queries that are placing metadata filters after the parser will query the metadata field as a parsed field, which will lead to degraded performance\n    const logFmtParser = getNodesFromQuery(query.substring(0, matcher.node.to), [Logfmt]);\n    const jsonParser = getNodesFromQuery(query.substring(0, matcher.node.to), [Json]);\n\n    // field filter key\n    const fieldNameNode = getAllPositionsInNodeByType(matcher, Identifier);\n    const fieldName = fieldNameNode[0]?.getExpression(query);\n\n    // field filter value\n    const fieldStringValue = getAllPositionsInNodeByType(matcher, String);\n    const fieldNumberValue = getAllPositionsInNodeByType(matcher, Number);\n    const fieldBytesValue = getAllPositionsInNodeByType(matcher, Bytes);\n    const fieldDurationValue = getAllPositionsInNodeByType(matcher, Duration);\n\n    let fieldValue: string, operator: FilterOpType | undefined;\n    if (fieldStringValue.length) {\n      operator = getStringFieldOperator(matcher);\n      // Strip out quotes\n      fieldValue = query.substring(fieldStringValue[0].from + 1, fieldStringValue[0].to - 1);\n    } else if (fieldNumberValue.length) {\n      fieldValue = fieldNumberValue[0].getExpression(query);\n      operator = getNumericFieldOperator(matcher);\n    } else if (fieldDurationValue.length) {\n      operator = getNumericFieldOperator(matcher);\n      fieldValue = fieldDurationValue[0].getExpression(query);\n    } else if (fieldBytesValue.length) {\n      operator = getNumericFieldOperator(matcher);\n      fieldValue = fieldBytesValue[0].getExpression(query);\n    } else {\n      continue;\n    }\n\n    // Label type\n    let labelType: LabelType | undefined;\n    if (dataFrame) {\n      // @todo if the field label is not in the first line, we'll always add this filter as a field filter\n      // Also negative filters that exclude all values of a field will always fail to get a label type for that exclusion filter?\n      labelType = getLabelTypeFromFrame(fieldName, dataFrame) ?? undefined;\n    }\n\n    if (operator) {\n      let parser: ParserType | undefined;\n      if (logFmtParser.length && jsonParser.length) {\n        parser = 'mixed';\n      } else if (logFmtParser.length) {\n        parser = 'logfmt';\n      } else if (jsonParser.length) {\n        parser = 'json';\n      } else {\n        // If there is no parser in the query, the field would have to be metadata or an invalid query?\n        labelType = LabelType.StructuredMetadata;\n      }\n\n      fields.push({\n        key: fieldName,\n        operator: operator,\n        parser,\n        type: labelType ?? LabelType.Parsed,\n        value: fieldValue,\n      });\n    }\n  }\n  return fields;\n}\n\nexport function getMatcherFromQuery(\n  query: string,\n  context?: PluginExtensionPanelContext,\n  lokiQuery?: LokiQuery\n): {\n  fields?: FieldFilter[];\n  labelFilters: IndexedLabelFilter[];\n  lineFilters?: LineFilterType[];\n  patternFilters?: PatternFilterType[];\n} {\n  const filter: IndexedLabelFilter[] = [];\n  const selector = getNodesFromQuery(query, [Selector]);\n\n  if (selector.length === 0) {\n    return { labelFilters: filter };\n  }\n\n  // Get the stream selector portion of the query\n  const selectorQuery = getAllPositionsInNodeByType(selector[0], Selector)[0].getExpression(query);\n  const labelFilters = parseLabelFilters(selectorQuery);\n  const fields = parseFields(query, context, lokiQuery);\n  const { lineFilters, patternFilters } = parseLineFilters(query);\n\n  return { fields, labelFilters, lineFilters, patternFilters };\n}\n\nexport function isQueryWithNode(query: string, nodeType: number): boolean {\n  let isQueryWithNode = false;\n  const tree = parser.parse(query);\n  tree.iterate({\n    enter: ({ type }): false | void => {\n      if (type.id === nodeType) {\n        isQueryWithNode = true;\n        return false;\n      }\n    },\n  });\n  return isQueryWithNode;\n}\n\n/**\n * Parses the query and looks for error nodes. If there is at least one, it returns true.\n * Grafana variables are considered errors, so if you need to validate a query\n * with variables you should interpolate it first.\n */\nexport const ErrorId = 0;\nexport function isValidQuery(query: string): boolean {\n  return isQueryWithNode(query, ErrorId) === false;\n}\n\nfunction getStringsFromLineFilter(filter: SyntaxNode): SyntaxNode[] {\n  const nodes: SyntaxNode[] = [];\n  let node: SyntaxNode | null = filter;\n  do {\n    const string = node.getChild(String);\n    if (string && !node.getChild(FilterOp)) {\n      nodes.push(string);\n    }\n    node = node.getChild(OrFilter);\n  } while (node != null);\n\n  return nodes;\n}\n", "// Warning: This file (and any imports) are included in the main bundle with Grafana in order to provide link extension support in Grafana core, in an effort to keep Grafana loading quickly, please do not add any unnecessary imports to this file and run the bundle analyzer before committing any changes!\nimport { DataFrame, DataSourceJsonData, ScopedVars, TimeRange } from '@grafana/data';\nimport { DataSourceWithBackend } from '@grafana/runtime';\nimport { DataSourceRef } from '@grafana/schema';\n\nimport { LabelType } from './fieldsTypes';\n\nexport enum LokiQueryDirection {\n  Backward = 'backward',\n  Forward = 'forward',\n  Scan = 'scan',\n}\n\nexport type LokiQuery = {\n  datasource?: DataSourceRef;\n  direction?: LokiQueryDirection;\n  editorMode?: string;\n  expr: string;\n  legendFormat?: string;\n  maxLines?: number;\n  queryType?: LokiQueryType;\n  refId: string;\n  step?: string;\n  supportingQueryType?: string;\n};\n\nexport type LokiQueryType = 'instant' | 'range' | 'stream' | string;\n\nexport type LokiDatasource = DataSourceWithBackend<LokiQuery, DataSourceJsonData> & {\n  maxLines?: number;\n} & {\n  getTimeRangeParams: (timeRange: TimeRange) => { end: number; start: number };\n  // @todo delete after min supported grafana is upgraded to >=11.6\n  interpolateString?: (string: string, scopedVars?: ScopedVars) => string;\n};\n\nexport function getLabelTypeFromFrame(labelKey: string, frame: DataFrame, index = 0): null | LabelType {\n  const typeField = frame.fields.find((field) => field.name === 'labelTypes')?.values[index];\n  if (!typeField) {\n    return null;\n  }\n  switch (typeField[labelKey]) {\n    case 'I':\n      return LabelType.Indexed;\n    case 'S':\n      return LabelType.StructuredMetadata;\n    case 'P':\n      return LabelType.Parsed;\n    default:\n      return null;\n  }\n}\n", "import { LogsSortOrder, RawTimeRange, UrlQueryMap } from '@grafana/data';\n\nimport { drilldownLabelUrlKey, pageSlugUrlKey } from '../Components/ServiceScene/ServiceSceneConstants';\nimport { SelectedTableRow } from '../Components/Table/LogLineCellComponent';\nimport { JSONDerivedFieldLink } from './derivedFields';\nimport { PageSlugs, TabNames, ValueSlugs } from './enums';\nimport { LabelFilterOp, NumericFilterOp } from './filterTypes';\nimport { LogsVisualizationType } from './store';\nimport { FieldValue, ParserType } from './variables';\n\nconst isObj = (o: unknown): o is object => typeof o === 'object' && o !== null;\n\nexport function hasProp<K extends PropertyKey>(data: object, prop: K): data is Record<K, unknown> {\n  return prop in data;\n}\n\nconst isString = (s: unknown) => (typeof s === 'string' && s) || '';\n\nexport const isRecord = (obj: unknown): obj is Record<string, unknown> => typeof obj === 'object';\n\nexport function unknownToStrings(a: unknown): string[] {\n  let strings: string[] = [];\n  if (Array.isArray(a)) {\n    for (let i = 0; i < a.length; i++) {\n      strings.push(isString(a[i]));\n    }\n  }\n  return strings;\n}\n\nexport function narrowSelectedTableRow(o: unknown): SelectedTableRow | false {\n  const narrowed = isObj(o) && hasProp(o, 'row') && hasProp(o, 'id') && o;\n\n  if (narrowed) {\n    const row = typeof narrowed.row === 'number' && narrowed.row;\n    const id = typeof narrowed.id === 'string' && narrowed.id;\n    if (id !== false && row !== false) {\n      return { id, row };\n    }\n  }\n\n  return false;\n}\n\nexport function narrowLogsVisualizationType(o: unknown): LogsVisualizationType | false {\n  return typeof o === 'string' && (o === 'logs' || o === 'table' || o === 'json') && o;\n}\nexport function narrowLogsSortOrder(o: unknown): LogsSortOrder | false {\n  if (typeof o === 'string' && o === LogsSortOrder.Ascending.toString()) {\n    return LogsSortOrder.Ascending;\n  }\n\n  if (typeof o === 'string' && o === LogsSortOrder.Descending.toString()) {\n    return LogsSortOrder.Descending;\n  }\n\n  return false;\n}\n\nexport function narrowFieldValue(o: unknown): FieldValue | false {\n  const narrowed = isObj(o) && hasProp(o, 'value') && hasProp(o, 'parser') && o;\n\n  if (narrowed) {\n    const parser: ParserType | false =\n      typeof narrowed.parser === 'string' &&\n      (narrowed.parser === 'logfmt' ||\n        narrowed.parser === 'json' ||\n        narrowed.parser === 'mixed' ||\n        narrowed.parser === 'structuredMetadata') &&\n      narrowed.parser;\n    const value = typeof narrowed.value === 'string' && narrowed.value;\n\n    if (parser !== false && value !== false) {\n      return { parser, value };\n    }\n  }\n\n  return false;\n}\n\nexport function narrowRecordStringNumber(o: unknown): Record<string, number> | false {\n  const narrowed = isObj(o) && isRecord(o) && o;\n\n  if (narrowed) {\n    const keys = Object.keys(narrowed);\n    const returnRecord: Record<string, number> = {};\n    for (let i = 0; i < keys.length; i++) {\n      const key = keys[i];\n      const value = narrowed[keys[i]];\n      if (typeof value === 'number') {\n        returnRecord[key] = value;\n      }\n    }\n\n    return returnRecord;\n  }\n\n  return false;\n}\n\nexport function narrowTimeRange(unknownRange: unknown): RawTimeRange | undefined {\n  const range = isObj(unknownRange) && hasProp(unknownRange, 'to') && hasProp(unknownRange, 'from') && unknownRange;\n  if (range) {\n    const to = isString(range.to);\n    const from = isString(range.from);\n    if (to && from) {\n      return { from, to };\n    }\n  }\n\n  return undefined;\n}\n\nexport function narrowErrorMessage(e: unknown): string | undefined {\n  const msg = isObj(e) && hasProp(e, 'error') && isString(e.error);\n  if (msg) {\n    return msg;\n  }\n  return undefined;\n}\n\nexport function narrowFilterOperator(op: string): LabelFilterOp | NumericFilterOp {\n  switch (op) {\n    case LabelFilterOp.Equal:\n    case LabelFilterOp.NotEqual:\n    case LabelFilterOp.RegexEqual:\n    case LabelFilterOp.RegexNotEqual:\n    case NumericFilterOp.gt:\n    case NumericFilterOp.gte:\n    case NumericFilterOp.lt:\n    case NumericFilterOp.lte:\n      return op;\n    default:\n      throw new NarrowingError('operator is invalid!');\n  }\n}\n\nexport function narrowTabName(input: unknown): TabNames | false {\n  return (\n    (input === TabNames.fields ||\n      input === TabNames.labels ||\n      input === TabNames.logs ||\n      input === TabNames.patterns) &&\n    input\n  );\n}\n\nexport function narrowPageOrValueSlug(input: unknown): ValueSlugs | PageSlugs | false {\n  return narrowPageSlug(input) || narrowValueSlug(input);\n}\n\nexport function narrowValueSlug(input: unknown): ValueSlugs | false {\n  return (input === ValueSlugs.field || input === ValueSlugs.label) && input;\n}\n\nexport function narrowPageSlug(input: unknown): PageSlugs | false {\n  if (typeof input === 'string') {\n    input = input.toLowerCase();\n  }\n  return (\n    (input === PageSlugs.fields ||\n      input === PageSlugs.labels ||\n      input === PageSlugs.logs ||\n      input === PageSlugs.patterns) &&\n    input\n  );\n}\n\nexport function narrowDrilldownLabelFromSearchParams(searchParams: UrlQueryMap) {\n  return Array.isArray(searchParams[drilldownLabelUrlKey]) &&\n    searchParams[drilldownLabelUrlKey][0] &&\n    typeof searchParams[drilldownLabelUrlKey][0] === 'string'\n    ? searchParams[drilldownLabelUrlKey][0]\n    : typeof searchParams[drilldownLabelUrlKey] === 'string' && searchParams[drilldownLabelUrlKey];\n}\n\nexport function narrowPageSlugFromSearchParams(searchParams: UrlQueryMap) {\n  return narrowPageOrValueSlug(\n    Array.isArray(searchParams[pageSlugUrlKey]) ? searchParams[pageSlugUrlKey][0] : searchParams[pageSlugUrlKey]\n  );\n}\n\nexport function narrowJsonDerivedFieldLinkPayload(payload: unknown): JSONDerivedFieldLink | false {\n  if (isObj(payload) && hasProp(payload, 'href') && hasProp(payload, 'name')) {\n    const href = isString(payload.href);\n    const name = isString(payload.name);\n    return {\n      href,\n      name,\n    };\n  }\n  return false;\n}\n\nexport class NarrowingError extends Error {}\n", "import { FilterOp, FilterOpType, NumericFilterOp } from './filterTypes';\nimport { numericOperatorArray } from './operators';\n\nexport const isOperatorInclusive = (op: string | FilterOpType): boolean => {\n  return op === FilterOp.Equal || op === FilterOp.RegexEqual;\n};\nexport const isOperatorExclusive = (op: string | FilterOpType): boolean => {\n  return op === FilterOp.NotEqual || op === FilterOp.RegexNotEqual;\n};\nexport const isOperatorRegex = (op: string | FilterOpType): boolean => {\n  return op === FilterOp.RegexEqual || op === FilterOp.RegexNotEqual;\n};\nexport const isOperatorNumeric = (op: string | NumericFilterOp): boolean => {\n  return numericOperatorArray.includes(op);\n};\n", "import { FilterOp, FilterOpType } from './filterTypes';\nimport { logger } from './logger';\n\nexport function getOperatorDescription(op: FilterOpType): string {\n  if (op === FilterOp.NotEqual) {\n    return 'Not equal';\n  }\n  if (op === FilterOp.RegexNotEqual) {\n    return 'Does not match regex';\n  }\n  if (op === FilterOp.Equal) {\n    return 'Equals';\n  }\n  if (op === FilterOp.RegexEqual) {\n    return 'Matches regex';\n  }\n  if (op === FilterOp.lt) {\n    return 'Less than';\n  }\n  if (op === FilterOp.gt) {\n    return 'Greater than';\n  }\n  if (op === FilterOp.gte) {\n    return 'Greater than or equal to';\n  }\n  if (op === FilterOp.lte) {\n    return 'Less than or equal to';\n  }\n\n  const error = new Error('Invalid operator!');\n  logger.error(error, { msg: 'Invalid operator', operator: op });\n  throw error;\n}\n", "import { SelectableValue } from '@grafana/data';\n\nimport { FilterOp, LineFilterOp } from './filterTypes';\nimport { getOperatorDescription } from './getOperatorDescription';\n\nexport const operators = [FilterOp.Equal, FilterOp.NotEqual, FilterOp.RegexEqual, FilterOp.RegexNotEqual].map<\n  SelectableValue<string>\n>((value, index, array) => {\n  return {\n    description: getOperatorDescription(value),\n    label: value,\n    value,\n  };\n});\n\nexport const includeOperators = [FilterOp.Equal, FilterOp.RegexEqual].map<SelectableValue<string>>((value) => ({\n  description: getOperatorDescription(value),\n  label: value,\n  value,\n}));\n\nexport const numericOperatorArray = [FilterOp.gt, FilterOp.gte, FilterOp.lt, FilterOp.lte];\n\nexport const numericOperators = numericOperatorArray.map<SelectableValue<string>>((value) => ({\n  description: getOperatorDescription(value),\n  label: value,\n  value,\n}));\n\nexport const lineFilterOperators: SelectableValue[] = [\n  { label: 'match', value: LineFilterOp.match },\n  { label: 'negativeMatch', value: LineFilterOp.negativeMatch },\n  { label: 'regex', value: LineFilterOp.regex },\n  { label: 'negativeRegex', value: LineFilterOp.negativeRegex },\n];\n", "// Warning, this file is included in the main module.tsx bundle, and doesn't contain many imports to keep that bundle size small. Don't add imports to this file!\nimport { escapeLabelValueInExactSelector } from './extensions/scenesMethods';\nimport { AppliedPattern } from './variables';\n\nexport function renderPatternFilters(patterns: AppliedPattern[]) {\n  const excludePatterns = patterns.filter((pattern) => pattern.type === 'exclude');\n  const excludePatternsLine = excludePatterns\n    .map((p) => `!> \"${escapeLabelValueInExactSelector(p.pattern)}\"`)\n    .join(' ')\n    .trim();\n\n  const includePatterns = patterns.filter((pattern) => pattern.type === 'include');\n  let includePatternsLine = '';\n  if (includePatterns.length > 0) {\n    if (includePatterns.length === 1) {\n      includePatternsLine = `|> \"${escapeLabelValueInExactSelector(includePatterns[0].pattern)}\"`;\n    } else {\n      includePatternsLine = `|> ${includePatterns\n        .map((p) => `\"${escapeLabelValueInExactSelector(p.pattern)}\"`)\n        .join(' or ')}`;\n    }\n  }\n  return `${excludePatternsLine} ${includePatternsLine}`.trim();\n}\n", "// Warning, this file is included in the main module.tsx bundle, and doesn't contain any imports to keep that bundle size small. Don't add imports to this file!\n\nimport { AdHocFilterWithLabels } from '@grafana/scenes';\n\nexport interface FieldValue {\n  parser: ParserType;\n  value: string;\n}\n\nexport interface AdHocFieldValue {\n  parser?: ParserType;\n  value?: string;\n}\nexport interface AppliedPattern {\n  pattern: string;\n  type: 'exclude' | 'include';\n}\n\nexport type ParserType = 'json' | 'logfmt' | 'mixed' | 'structuredMetadata';\nexport type DetectedFieldType = 'boolean' | 'bytes' | 'duration' | 'float' | 'int' | 'string';\nexport type AdHocFilterWithLabelsMeta = { parser?: ParserType; type?: DetectedFieldType };\nexport type AdHocFiltersWithLabelsAndMeta = AdHocFilterWithLabels<AdHocFilterWithLabelsMeta>;\n\nexport type LogsQueryOptions = {\n  fieldExpressionToAdd?: string;\n  fieldType?: DetectedFieldType;\n  jsonParserPropToAdd?: string;\n  labelExpressionToAdd?: string;\n  parser?: ParserType;\n  structuredMetadataToAdd?: string;\n};\n\nexport const VAR_LABELS = 'filters';\nexport const VAR_LABELS_EXPR = '${filters}';\nexport const VAR_LABELS_REPLICA = 'filters_replica';\nexport const VAR_LABELS_REPLICA_EXPR = '${filters_replica}';\nexport const VAR_FIELDS = 'fields';\nexport const VAR_FIELDS_EXPR = '${fields}';\nexport const PENDING_FIELDS_EXPR = '${pendingFields}';\nexport const PENDING_METADATA_EXPR = '${pendingMetadata}';\nexport const VAR_FIELDS_AND_METADATA = 'all-fields';\nexport const VAR_METADATA = 'metadata';\nexport const VAR_METADATA_EXPR = '${metadata}';\nexport const VAR_PATTERNS = 'patterns';\nexport const VAR_PATTERNS_EXPR = '${patterns}';\nexport const VAR_LEVELS = 'levels';\nexport const VAR_LEVELS_EXPR = '${levels}';\nexport const VAR_FIELD_GROUP_BY = 'fieldBy';\nexport const VAR_LABEL_GROUP_BY = 'labelBy';\nexport const VAR_LABEL_GROUP_BY_EXPR = '${labelBy}';\nexport const VAR_PRIMARY_LABEL_SEARCH = 'primary_label_search';\nexport const VAR_PRIMARY_LABEL_SEARCH_EXPR = '${primary_label_search}';\nexport const VAR_PRIMARY_LABEL = 'primary_label';\nexport const VAR_PRIMARY_LABEL_EXPR = '${primary_label}';\nexport const VAR_DATASOURCE = 'ds';\nexport const VAR_DATASOURCE_EXPR = '${ds}';\nexport const VAR_JSON_FIELDS = 'jsonFields';\nexport const VAR_JSON_FIELDS_EXPR = '${jsonFields}';\n\nexport const VAR_LINE_FORMAT = 'lineFormat';\nexport const VAR_LINE_FORMAT_EXPR = '${lineFormat}';\n\nexport const DETECTED_FIELDS_MIXED_FORMAT_EXPR_NO_JSON_FIELDS = `| json | logfmt | drop __error__, __error_details__`;\nexport const DETECTED_FIELDS_MIXED_FORMAT_EXPR = `| json ${VAR_JSON_FIELDS_EXPR} | logfmt | drop __error__, __error_details__`;\nexport const MIXED_FORMAT_EXPR = `| json ${VAR_JSON_FIELDS_EXPR} | logfmt | drop __error__, __error_details__`;\nexport const JSON_FORMAT_EXPR = `| json ${VAR_JSON_FIELDS_EXPR} | drop __error__, __error_details__`;\n// export const JSON_FORMAT_EXPR_NO_JSON_FIELDS = `| json | drop __error__, __error_details__`;\nexport const LOGS_FORMAT_EXPR = `| logfmt`;\n// This variable is hardcoded to the value of MIXED_FORMAT_EXPR. This is a hack to get logs context working, we don't want to use a variable for a value that doesn't change and cannot be updated by the user.\nexport const VAR_LOGS_FORMAT = 'logsFormat';\nexport const VAR_LOGS_FORMAT_EXPR = '${logsFormat}';\n// The deprecated line filter (custom variable)\nexport const VAR_LINE_FILTER_DEPRECATED = 'lineFilter';\n// The new single value line filter (ad-hoc variable), results are added to VAR_LINE_FILTER_AD_HOC when \"submitted\"\nexport const VAR_LINE_FILTER = 'lineFilterV2';\nexport const VAR_LINE_FILTER_EXPR = '${lineFilterV2}';\n// The new multi value line filter (ad-hoc variable)\nexport const VAR_LINE_FILTERS = 'lineFilters';\nexport const VAR_LINE_FILTERS_EXPR = '${lineFilters}';\nexport const LOG_STREAM_SELECTOR_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_LEVELS_EXPR} ${VAR_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} | json ${VAR_JSON_FIELDS_EXPR} | logfmt | drop __error__, __error_details__ ${VAR_FIELDS_EXPR} ${VAR_LINE_FORMAT_EXPR}`;\n// Same as the LOG_STREAM_SELECTOR_EXPR, but without the fields as they will need to be built manually to exclude the current filter value\nexport const DETECTED_FIELD_VALUES_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_LEVELS_EXPR} ${VAR_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} ${MIXED_FORMAT_EXPR} ${VAR_FIELDS_EXPR}`;\nexport const DETECTED_FIELD_AND_METADATA_VALUES_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_LEVELS_EXPR} ${PENDING_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} ${DETECTED_FIELDS_MIXED_FORMAT_EXPR} ${PENDING_FIELDS_EXPR}`;\nexport const DETECTED_METADATA_VALUES_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_LEVELS_EXPR} ${PENDING_FIELDS_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} ${VAR_LOGS_FORMAT_EXPR} ${VAR_FIELDS_EXPR}`;\nexport const DETECTED_LEVELS_VALUES_EXPR = `{${VAR_LABELS_EXPR}} ${PENDING_FIELDS_EXPR} ${VAR_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} ${VAR_LOGS_FORMAT_EXPR} ${VAR_FIELDS_EXPR}`;\nexport const PATTERNS_SAMPLE_SELECTOR_EXPR = `{${VAR_LABELS_EXPR}} ${VAR_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LOGS_FORMAT_EXPR}`;\nexport const PRETTY_LOG_STREAM_SELECTOR_EXPR = `${VAR_LABELS_EXPR} ${VAR_LEVELS_EXPR} ${VAR_METADATA_EXPR} ${VAR_PATTERNS_EXPR} ${VAR_LINE_FILTERS_EXPR} ${VAR_FIELDS_EXPR}`;\nexport const EXPLORATION_DS = { uid: VAR_DATASOURCE_EXPR };\nexport const ALL_VARIABLE_VALUE = '$__all';\nexport const LEVEL_VARIABLE_VALUE = 'detected_level';\nexport const SERVICE_NAME = 'service_name';\nexport const SERVICE_UI_LABEL = 'service';\nexport const VAR_AGGREGATED_METRICS = 'var_aggregated_metrics';\nexport const VAR_AGGREGATED_METRICS_EXPR = '${var_aggregated_metrics}';\nexport const EMPTY_VARIABLE_VALUE = '\"\"';\n\n// Delimiter used at the start of a label value to denote user input that should not be escaped\n// @todo we need ad-hoc-filter meta that is persisted in the URL so we can clean this up.\nexport const USER_INPUT_ADHOC_VALUE_PREFIX = '__CVΩ__';\nexport function stripAdHocFilterUserInputPrefix(value = '') {\n  if (value.startsWith(USER_INPUT_ADHOC_VALUE_PREFIX)) {\n    return value.substring(USER_INPUT_ADHOC_VALUE_PREFIX.length);\n  }\n  return value;\n}\nexport function isAdHocFilterValueUserInput(value = '') {\n  return value.startsWith(USER_INPUT_ADHOC_VALUE_PREFIX);\n}\nexport function addAdHocFilterUserInputPrefix(value = '') {\n  return USER_INPUT_ADHOC_VALUE_PREFIX + value;\n}\n", "module.exports = __WEBPACK_EXTERNAL_MODULE__6089__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__7781__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__8531__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__2007__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__3241__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__1308__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__5959__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__8398__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__200__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__1159__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__7694__;", "module.exports = __WEBPACK_EXTERNAL_MODULE__1269__;", "/**\nThe default maximum length of a `TreeBuffer` node.\n*/\nconst DefaultBufferLength = 1024;\nlet nextPropID = 0;\nclass Range {\n    constructor(from, to) {\n        this.from = from;\n        this.to = to;\n    }\n}\n/**\nEach [node type](#common.NodeType) or [individual tree](#common.Tree)\ncan have metadata associated with it in props. Instances of this\nclass represent prop names.\n*/\nclass NodeProp {\n    /**\n    Create a new node prop type.\n    */\n    constructor(config = {}) {\n        this.id = nextPropID++;\n        this.perNode = !!config.perNode;\n        this.deserialize = config.deserialize || (() => {\n            throw new Error(\"This node type doesn't define a deserialize function\");\n        });\n    }\n    /**\n    This is meant to be used with\n    [`NodeSet.extend`](#common.NodeSet.extend) or\n    [`LRParser.configure`](#lr.ParserConfig.props) to compute\n    prop values for each node type in the set. Takes a [match\n    object](#common.NodeType^match) or function that returns undefined\n    if the node type doesn't get this prop, and the prop's value if\n    it does.\n    */\n    add(match) {\n        if (this.perNode)\n            throw new RangeError(\"Can't add per-node props to node types\");\n        if (typeof match != \"function\")\n            match = NodeType.match(match);\n        return (type) => {\n            let result = match(type);\n            return result === undefined ? null : [this, result];\n        };\n    }\n}\n/**\nProp that is used to describe matching delimiters. For opening\ndelimiters, this holds an array of node names (written as a\nspace-separated string when declaring this prop in a grammar)\nfor the node types of closing delimiters that match it.\n*/\nNodeProp.closedBy = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nThe inverse of [`closedBy`](#common.NodeProp^closedBy). This is\nattached to closing delimiters, holding an array of node names\nof types of matching opening delimiters.\n*/\nNodeProp.openedBy = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nUsed to assign node types to groups (for example, all node\ntypes that represent an expression could be tagged with an\n`\"Expression\"` group).\n*/\nNodeProp.group = new NodeProp({ deserialize: str => str.split(\" \") });\n/**\nAttached to nodes to indicate these should be\n[displayed](https://codemirror.net/docs/ref/#language.syntaxTree)\nin a bidirectional text isolate, so that direction-neutral\ncharacters on their sides don't incorrectly get associated with\nsurrounding text. You'll generally want to set this for nodes\nthat contain arbitrary text, like strings and comments, and for\nnodes that appear _inside_ arbitrary text, like HTML tags. When\nnot given a value, in a grammar declaration, defaults to\n`\"auto\"`.\n*/\nNodeProp.isolate = new NodeProp({ deserialize: value => {\n        if (value && value != \"rtl\" && value != \"ltr\" && value != \"auto\")\n            throw new RangeError(\"Invalid value for isolate: \" + value);\n        return value || \"auto\";\n    } });\n/**\nThe hash of the [context](#lr.ContextTracker.constructor)\nthat the node was parsed in, if any. Used to limit reuse of\ncontextual nodes.\n*/\nNodeProp.contextHash = new NodeProp({ perNode: true });\n/**\nThe distance beyond the end of the node that the tokenizer\nlooked ahead for any of the tokens inside the node. (The LR\nparser only stores this when it is larger than 25, for\nefficiency reasons.)\n*/\nNodeProp.lookAhead = new NodeProp({ perNode: true });\n/**\nThis per-node prop is used to replace a given node, or part of a\nnode, with another tree. This is useful to include trees from\ndifferent languages in mixed-language parsers.\n*/\nNodeProp.mounted = new NodeProp({ perNode: true });\n/**\nA mounted tree, which can be [stored](#common.NodeProp^mounted) on\na tree node to indicate that parts of its content are\nrepresented by another tree.\n*/\nclass MountedTree {\n    constructor(\n    /**\n    The inner tree.\n    */\n    tree, \n    /**\n    If this is null, this tree replaces the entire node (it will\n    be included in the regular iteration instead of its host\n    node). If not, only the given ranges are considered to be\n    covered by this tree. This is used for trees that are mixed in\n    a way that isn't strictly hierarchical. Such mounted trees are\n    only entered by [`resolveInner`](#common.Tree.resolveInner)\n    and [`enter`](#common.SyntaxNode.enter).\n    */\n    overlay, \n    /**\n    The parser used to create this subtree.\n    */\n    parser) {\n        this.tree = tree;\n        this.overlay = overlay;\n        this.parser = parser;\n    }\n    /**\n    @internal\n    */\n    static get(tree) {\n        return tree && tree.props && tree.props[NodeProp.mounted.id];\n    }\n}\nconst noProps = Object.create(null);\n/**\nEach node in a syntax tree has a node type associated with it.\n*/\nclass NodeType {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The name of the node type. Not necessarily unique, but if the\n    grammar was written properly, different node types with the\n    same name within a node set should play the same semantic\n    role.\n    */\n    name, \n    /**\n    @internal\n    */\n    props, \n    /**\n    The id of this node in its set. Corresponds to the term ids\n    used in the parser.\n    */\n    id, \n    /**\n    @internal\n    */\n    flags = 0) {\n        this.name = name;\n        this.props = props;\n        this.id = id;\n        this.flags = flags;\n    }\n    /**\n    Define a node type.\n    */\n    static define(spec) {\n        let props = spec.props && spec.props.length ? Object.create(null) : noProps;\n        let flags = (spec.top ? 1 /* NodeFlag.Top */ : 0) | (spec.skipped ? 2 /* NodeFlag.Skipped */ : 0) |\n            (spec.error ? 4 /* NodeFlag.Error */ : 0) | (spec.name == null ? 8 /* NodeFlag.Anonymous */ : 0);\n        let type = new NodeType(spec.name || \"\", props, spec.id, flags);\n        if (spec.props)\n            for (let src of spec.props) {\n                if (!Array.isArray(src))\n                    src = src(type);\n                if (src) {\n                    if (src[0].perNode)\n                        throw new RangeError(\"Can't store a per-node prop on a node type\");\n                    props[src[0].id] = src[1];\n                }\n            }\n        return type;\n    }\n    /**\n    Retrieves a node prop for this type. Will return `undefined` if\n    the prop isn't present on this node.\n    */\n    prop(prop) { return this.props[prop.id]; }\n    /**\n    True when this is the top node of a grammar.\n    */\n    get isTop() { return (this.flags & 1 /* NodeFlag.Top */) > 0; }\n    /**\n    True when this node is produced by a skip rule.\n    */\n    get isSkipped() { return (this.flags & 2 /* NodeFlag.Skipped */) > 0; }\n    /**\n    Indicates whether this is an error node.\n    */\n    get isError() { return (this.flags & 4 /* NodeFlag.Error */) > 0; }\n    /**\n    When true, this node type doesn't correspond to a user-declared\n    named node, for example because it is used to cache repetition.\n    */\n    get isAnonymous() { return (this.flags & 8 /* NodeFlag.Anonymous */) > 0; }\n    /**\n    Returns true when this node's name or one of its\n    [groups](#common.NodeProp^group) matches the given string.\n    */\n    is(name) {\n        if (typeof name == 'string') {\n            if (this.name == name)\n                return true;\n            let group = this.prop(NodeProp.group);\n            return group ? group.indexOf(name) > -1 : false;\n        }\n        return this.id == name;\n    }\n    /**\n    Create a function from node types to arbitrary values by\n    specifying an object whose property names are node or\n    [group](#common.NodeProp^group) names. Often useful with\n    [`NodeProp.add`](#common.NodeProp.add). You can put multiple\n    names, separated by spaces, in a single property name to map\n    multiple node names to a single value.\n    */\n    static match(map) {\n        let direct = Object.create(null);\n        for (let prop in map)\n            for (let name of prop.split(\" \"))\n                direct[name] = map[prop];\n        return (node) => {\n            for (let groups = node.prop(NodeProp.group), i = -1; i < (groups ? groups.length : 0); i++) {\n                let found = direct[i < 0 ? node.name : groups[i]];\n                if (found)\n                    return found;\n            }\n        };\n    }\n}\n/**\nAn empty dummy node type to use when no actual type is available.\n*/\nNodeType.none = new NodeType(\"\", Object.create(null), 0, 8 /* NodeFlag.Anonymous */);\n/**\nA node set holds a collection of node types. It is used to\ncompactly represent trees by storing their type ids, rather than a\nfull pointer to the type object, in a numeric array. Each parser\n[has](#lr.LRParser.nodeSet) a node set, and [tree\nbuffers](#common.TreeBuffer) can only store collections of nodes\nfrom the same set. A set can have a maximum of 2**16 (65536) node\ntypes in it, so that the ids fit into 16-bit typed array slots.\n*/\nclass NodeSet {\n    /**\n    Create a set with the given types. The `id` property of each\n    type should correspond to its position within the array.\n    */\n    constructor(\n    /**\n    The node types in this set, by id.\n    */\n    types) {\n        this.types = types;\n        for (let i = 0; i < types.length; i++)\n            if (types[i].id != i)\n                throw new RangeError(\"Node type ids should correspond to array positions when creating a node set\");\n    }\n    /**\n    Create a copy of this set with some node properties added. The\n    arguments to this method can be created with\n    [`NodeProp.add`](#common.NodeProp.add).\n    */\n    extend(...props) {\n        let newTypes = [];\n        for (let type of this.types) {\n            let newProps = null;\n            for (let source of props) {\n                let add = source(type);\n                if (add) {\n                    if (!newProps)\n                        newProps = Object.assign({}, type.props);\n                    newProps[add[0].id] = add[1];\n                }\n            }\n            newTypes.push(newProps ? new NodeType(type.name, newProps, type.id, type.flags) : type);\n        }\n        return new NodeSet(newTypes);\n    }\n}\nconst CachedNode = new WeakMap(), CachedInnerNode = new WeakMap();\n/**\nOptions that control iteration. Can be combined with the `|`\noperator to enable multiple ones.\n*/\nvar IterMode;\n(function (IterMode) {\n    /**\n    When enabled, iteration will only visit [`Tree`](#common.Tree)\n    objects, not nodes packed into\n    [`TreeBuffer`](#common.TreeBuffer)s.\n    */\n    IterMode[IterMode[\"ExcludeBuffers\"] = 1] = \"ExcludeBuffers\";\n    /**\n    Enable this to make iteration include anonymous nodes (such as\n    the nodes that wrap repeated grammar constructs into a balanced\n    tree).\n    */\n    IterMode[IterMode[\"IncludeAnonymous\"] = 2] = \"IncludeAnonymous\";\n    /**\n    By default, regular [mounted](#common.NodeProp^mounted) nodes\n    replace their base node in iteration. Enable this to ignore them\n    instead.\n    */\n    IterMode[IterMode[\"IgnoreMounts\"] = 4] = \"IgnoreMounts\";\n    /**\n    This option only applies in\n    [`enter`](#common.SyntaxNode.enter)-style methods. It tells the\n    library to not enter mounted overlays if one covers the given\n    position.\n    */\n    IterMode[IterMode[\"IgnoreOverlays\"] = 8] = \"IgnoreOverlays\";\n})(IterMode || (IterMode = {}));\n/**\nA piece of syntax tree. There are two ways to approach these\ntrees: the way they are actually stored in memory, and the\nconvenient way.\n\nSyntax trees are stored as a tree of `Tree` and `TreeBuffer`\nobjects. By packing detail information into `TreeBuffer` leaf\nnodes, the representation is made a lot more memory-efficient.\n\nHowever, when you want to actually work with tree nodes, this\nrepresentation is very awkward, so most client code will want to\nuse the [`TreeCursor`](#common.TreeCursor) or\n[`SyntaxNode`](#common.SyntaxNode) interface instead, which provides\na view on some part of this data structure, and can be used to\nmove around to adjacent nodes.\n*/\nclass Tree {\n    /**\n    Construct a new tree. See also [`Tree.build`](#common.Tree^build).\n    */\n    constructor(\n    /**\n    The type of the top node.\n    */\n    type, \n    /**\n    This node's child nodes.\n    */\n    children, \n    /**\n    The positions (offsets relative to the start of this tree) of\n    the children.\n    */\n    positions, \n    /**\n    The total length of this tree\n    */\n    length, \n    /**\n    Per-node [node props](#common.NodeProp) to associate with this node.\n    */\n    props) {\n        this.type = type;\n        this.children = children;\n        this.positions = positions;\n        this.length = length;\n        /**\n        @internal\n        */\n        this.props = null;\n        if (props && props.length) {\n            this.props = Object.create(null);\n            for (let [prop, value] of props)\n                this.props[typeof prop == \"number\" ? prop : prop.id] = value;\n        }\n    }\n    /**\n    @internal\n    */\n    toString() {\n        let mounted = MountedTree.get(this);\n        if (mounted && !mounted.overlay)\n            return mounted.tree.toString();\n        let children = \"\";\n        for (let ch of this.children) {\n            let str = ch.toString();\n            if (str) {\n                if (children)\n                    children += \",\";\n                children += str;\n            }\n        }\n        return !this.type.name ? children :\n            (/\\W/.test(this.type.name) && !this.type.isError ? JSON.stringify(this.type.name) : this.type.name) +\n                (children.length ? \"(\" + children + \")\" : \"\");\n    }\n    /**\n    Get a [tree cursor](#common.TreeCursor) positioned at the top of\n    the tree. Mode can be used to [control](#common.IterMode) which\n    nodes the cursor visits.\n    */\n    cursor(mode = 0) {\n        return new TreeCursor(this.topNode, mode);\n    }\n    /**\n    Get a [tree cursor](#common.TreeCursor) pointing into this tree\n    at the given position and side (see\n    [`moveTo`](#common.TreeCursor.moveTo).\n    */\n    cursorAt(pos, side = 0, mode = 0) {\n        let scope = CachedNode.get(this) || this.topNode;\n        let cursor = new TreeCursor(scope);\n        cursor.moveTo(pos, side);\n        CachedNode.set(this, cursor._tree);\n        return cursor;\n    }\n    /**\n    Get a [syntax node](#common.SyntaxNode) object for the top of the\n    tree.\n    */\n    get topNode() {\n        return new TreeNode(this, 0, 0, null);\n    }\n    /**\n    Get the [syntax node](#common.SyntaxNode) at the given position.\n    If `side` is -1, this will move into nodes that end at the\n    position. If 1, it'll move into nodes that start at the\n    position. With 0, it'll only enter nodes that cover the position\n    from both sides.\n    \n    Note that this will not enter\n    [overlays](#common.MountedTree.overlay), and you often want\n    [`resolveInner`](#common.Tree.resolveInner) instead.\n    */\n    resolve(pos, side = 0) {\n        let node = resolveNode(CachedNode.get(this) || this.topNode, pos, side, false);\n        CachedNode.set(this, node);\n        return node;\n    }\n    /**\n    Like [`resolve`](#common.Tree.resolve), but will enter\n    [overlaid](#common.MountedTree.overlay) nodes, producing a syntax node\n    pointing into the innermost overlaid tree at the given position\n    (with parent links going through all parent structure, including\n    the host trees).\n    */\n    resolveInner(pos, side = 0) {\n        let node = resolveNode(CachedInnerNode.get(this) || this.topNode, pos, side, true);\n        CachedInnerNode.set(this, node);\n        return node;\n    }\n    /**\n    In some situations, it can be useful to iterate through all\n    nodes around a position, including those in overlays that don't\n    directly cover the position. This method gives you an iterator\n    that will produce all nodes, from small to big, around the given\n    position.\n    */\n    resolveStack(pos, side = 0) {\n        return stackIterator(this, pos, side);\n    }\n    /**\n    Iterate over the tree and its children, calling `enter` for any\n    node that touches the `from`/`to` region (if given) before\n    running over such a node's children, and `leave` (if given) when\n    leaving the node. When `enter` returns `false`, that node will\n    not have its children iterated over (or `leave` called).\n    */\n    iterate(spec) {\n        let { enter, leave, from = 0, to = this.length } = spec;\n        let mode = spec.mode || 0, anon = (mode & IterMode.IncludeAnonymous) > 0;\n        for (let c = this.cursor(mode | IterMode.IncludeAnonymous);;) {\n            let entered = false;\n            if (c.from <= to && c.to >= from && (!anon && c.type.isAnonymous || enter(c) !== false)) {\n                if (c.firstChild())\n                    continue;\n                entered = true;\n            }\n            for (;;) {\n                if (entered && leave && (anon || !c.type.isAnonymous))\n                    leave(c);\n                if (c.nextSibling())\n                    break;\n                if (!c.parent())\n                    return;\n                entered = true;\n            }\n        }\n    }\n    /**\n    Get the value of the given [node prop](#common.NodeProp) for this\n    node. Works with both per-node and per-type props.\n    */\n    prop(prop) {\n        return !prop.perNode ? this.type.prop(prop) : this.props ? this.props[prop.id] : undefined;\n    }\n    /**\n    Returns the node's [per-node props](#common.NodeProp.perNode) in a\n    format that can be passed to the [`Tree`](#common.Tree)\n    constructor.\n    */\n    get propValues() {\n        let result = [];\n        if (this.props)\n            for (let id in this.props)\n                result.push([+id, this.props[id]]);\n        return result;\n    }\n    /**\n    Balance the direct children of this tree, producing a copy of\n    which may have children grouped into subtrees with type\n    [`NodeType.none`](#common.NodeType^none).\n    */\n    balance(config = {}) {\n        return this.children.length <= 8 /* Balance.BranchFactor */ ? this :\n            balanceRange(NodeType.none, this.children, this.positions, 0, this.children.length, 0, this.length, (children, positions, length) => new Tree(this.type, children, positions, length, this.propValues), config.makeTree || ((children, positions, length) => new Tree(NodeType.none, children, positions, length)));\n    }\n    /**\n    Build a tree from a postfix-ordered buffer of node information,\n    or a cursor over such a buffer.\n    */\n    static build(data) { return buildTree(data); }\n}\n/**\nThe empty tree\n*/\nTree.empty = new Tree(NodeType.none, [], [], 0);\nclass FlatBufferCursor {\n    constructor(buffer, index) {\n        this.buffer = buffer;\n        this.index = index;\n    }\n    get id() { return this.buffer[this.index - 4]; }\n    get start() { return this.buffer[this.index - 3]; }\n    get end() { return this.buffer[this.index - 2]; }\n    get size() { return this.buffer[this.index - 1]; }\n    get pos() { return this.index; }\n    next() { this.index -= 4; }\n    fork() { return new FlatBufferCursor(this.buffer, this.index); }\n}\n/**\nTree buffers contain (type, start, end, endIndex) quads for each\nnode. In such a buffer, nodes are stored in prefix order (parents\nbefore children, with the endIndex of the parent indicating which\nchildren belong to it).\n*/\nclass TreeBuffer {\n    /**\n    Create a tree buffer.\n    */\n    constructor(\n    /**\n    The buffer's content.\n    */\n    buffer, \n    /**\n    The total length of the group of nodes in the buffer.\n    */\n    length, \n    /**\n    The node set used in this buffer.\n    */\n    set) {\n        this.buffer = buffer;\n        this.length = length;\n        this.set = set;\n    }\n    /**\n    @internal\n    */\n    get type() { return NodeType.none; }\n    /**\n    @internal\n    */\n    toString() {\n        let result = [];\n        for (let index = 0; index < this.buffer.length;) {\n            result.push(this.childString(index));\n            index = this.buffer[index + 3];\n        }\n        return result.join(\",\");\n    }\n    /**\n    @internal\n    */\n    childString(index) {\n        let id = this.buffer[index], endIndex = this.buffer[index + 3];\n        let type = this.set.types[id], result = type.name;\n        if (/\\W/.test(result) && !type.isError)\n            result = JSON.stringify(result);\n        index += 4;\n        if (endIndex == index)\n            return result;\n        let children = [];\n        while (index < endIndex) {\n            children.push(this.childString(index));\n            index = this.buffer[index + 3];\n        }\n        return result + \"(\" + children.join(\",\") + \")\";\n    }\n    /**\n    @internal\n    */\n    findChild(startIndex, endIndex, dir, pos, side) {\n        let { buffer } = this, pick = -1;\n        for (let i = startIndex; i != endIndex; i = buffer[i + 3]) {\n            if (checkSide(side, pos, buffer[i + 1], buffer[i + 2])) {\n                pick = i;\n                if (dir > 0)\n                    break;\n            }\n        }\n        return pick;\n    }\n    /**\n    @internal\n    */\n    slice(startI, endI, from) {\n        let b = this.buffer;\n        let copy = new Uint16Array(endI - startI), len = 0;\n        for (let i = startI, j = 0; i < endI;) {\n            copy[j++] = b[i++];\n            copy[j++] = b[i++] - from;\n            let to = copy[j++] = b[i++] - from;\n            copy[j++] = b[i++] - startI;\n            len = Math.max(len, to);\n        }\n        return new TreeBuffer(copy, len, this.set);\n    }\n}\nfunction checkSide(side, pos, from, to) {\n    switch (side) {\n        case -2 /* Side.Before */: return from < pos;\n        case -1 /* Side.AtOrBefore */: return to >= pos && from < pos;\n        case 0 /* Side.Around */: return from < pos && to > pos;\n        case 1 /* Side.AtOrAfter */: return from <= pos && to > pos;\n        case 2 /* Side.After */: return to > pos;\n        case 4 /* Side.DontCare */: return true;\n    }\n}\nfunction resolveNode(node, pos, side, overlays) {\n    var _a;\n    // Move up to a node that actually holds the position, if possible\n    while (node.from == node.to ||\n        (side < 1 ? node.from >= pos : node.from > pos) ||\n        (side > -1 ? node.to <= pos : node.to < pos)) {\n        let parent = !overlays && node instanceof TreeNode && node.index < 0 ? null : node.parent;\n        if (!parent)\n            return node;\n        node = parent;\n    }\n    let mode = overlays ? 0 : IterMode.IgnoreOverlays;\n    // Must go up out of overlays when those do not overlap with pos\n    if (overlays)\n        for (let scan = node, parent = scan.parent; parent; scan = parent, parent = scan.parent) {\n            if (scan instanceof TreeNode && scan.index < 0 && ((_a = parent.enter(pos, side, mode)) === null || _a === void 0 ? void 0 : _a.from) != scan.from)\n                node = parent;\n        }\n    for (;;) {\n        let inner = node.enter(pos, side, mode);\n        if (!inner)\n            return node;\n        node = inner;\n    }\n}\nclass BaseNode {\n    cursor(mode = 0) { return new TreeCursor(this, mode); }\n    getChild(type, before = null, after = null) {\n        let r = getChildren(this, type, before, after);\n        return r.length ? r[0] : null;\n    }\n    getChildren(type, before = null, after = null) {\n        return getChildren(this, type, before, after);\n    }\n    resolve(pos, side = 0) {\n        return resolveNode(this, pos, side, false);\n    }\n    resolveInner(pos, side = 0) {\n        return resolveNode(this, pos, side, true);\n    }\n    matchContext(context) {\n        return matchNodeContext(this.parent, context);\n    }\n    enterUnfinishedNodesBefore(pos) {\n        let scan = this.childBefore(pos), node = this;\n        while (scan) {\n            let last = scan.lastChild;\n            if (!last || last.to != scan.to)\n                break;\n            if (last.type.isError && last.from == last.to) {\n                node = scan;\n                scan = last.prevSibling;\n            }\n            else {\n                scan = last;\n            }\n        }\n        return node;\n    }\n    get node() { return this; }\n    get next() { return this.parent; }\n}\nclass TreeNode extends BaseNode {\n    constructor(_tree, from, \n    // Index in parent node, set to -1 if the node is not a direct child of _parent.node (overlay)\n    index, _parent) {\n        super();\n        this._tree = _tree;\n        this.from = from;\n        this.index = index;\n        this._parent = _parent;\n    }\n    get type() { return this._tree.type; }\n    get name() { return this._tree.type.name; }\n    get to() { return this.from + this._tree.length; }\n    nextChild(i, dir, pos, side, mode = 0) {\n        for (let parent = this;;) {\n            for (let { children, positions } = parent._tree, e = dir > 0 ? children.length : -1; i != e; i += dir) {\n                let next = children[i], start = positions[i] + parent.from;\n                if (!checkSide(side, pos, start, start + next.length))\n                    continue;\n                if (next instanceof TreeBuffer) {\n                    if (mode & IterMode.ExcludeBuffers)\n                        continue;\n                    let index = next.findChild(0, next.buffer.length, dir, pos - start, side);\n                    if (index > -1)\n                        return new BufferNode(new BufferContext(parent, next, i, start), null, index);\n                }\n                else if ((mode & IterMode.IncludeAnonymous) || (!next.type.isAnonymous || hasChild(next))) {\n                    let mounted;\n                    if (!(mode & IterMode.IgnoreMounts) && (mounted = MountedTree.get(next)) && !mounted.overlay)\n                        return new TreeNode(mounted.tree, start, i, parent);\n                    let inner = new TreeNode(next, start, i, parent);\n                    return (mode & IterMode.IncludeAnonymous) || !inner.type.isAnonymous ? inner\n                        : inner.nextChild(dir < 0 ? next.children.length - 1 : 0, dir, pos, side);\n                }\n            }\n            if ((mode & IterMode.IncludeAnonymous) || !parent.type.isAnonymous)\n                return null;\n            if (parent.index >= 0)\n                i = parent.index + dir;\n            else\n                i = dir < 0 ? -1 : parent._parent._tree.children.length;\n            parent = parent._parent;\n            if (!parent)\n                return null;\n        }\n    }\n    get firstChild() { return this.nextChild(0, 1, 0, 4 /* Side.DontCare */); }\n    get lastChild() { return this.nextChild(this._tree.children.length - 1, -1, 0, 4 /* Side.DontCare */); }\n    childAfter(pos) { return this.nextChild(0, 1, pos, 2 /* Side.After */); }\n    childBefore(pos) { return this.nextChild(this._tree.children.length - 1, -1, pos, -2 /* Side.Before */); }\n    enter(pos, side, mode = 0) {\n        let mounted;\n        if (!(mode & IterMode.IgnoreOverlays) && (mounted = MountedTree.get(this._tree)) && mounted.overlay) {\n            let rPos = pos - this.from;\n            for (let { from, to } of mounted.overlay) {\n                if ((side > 0 ? from <= rPos : from < rPos) &&\n                    (side < 0 ? to >= rPos : to > rPos))\n                    return new TreeNode(mounted.tree, mounted.overlay[0].from + this.from, -1, this);\n            }\n        }\n        return this.nextChild(0, 1, pos, side, mode);\n    }\n    nextSignificantParent() {\n        let val = this;\n        while (val.type.isAnonymous && val._parent)\n            val = val._parent;\n        return val;\n    }\n    get parent() {\n        return this._parent ? this._parent.nextSignificantParent() : null;\n    }\n    get nextSibling() {\n        return this._parent && this.index >= 0 ? this._parent.nextChild(this.index + 1, 1, 0, 4 /* Side.DontCare */) : null;\n    }\n    get prevSibling() {\n        return this._parent && this.index >= 0 ? this._parent.nextChild(this.index - 1, -1, 0, 4 /* Side.DontCare */) : null;\n    }\n    get tree() { return this._tree; }\n    toTree() { return this._tree; }\n    /**\n    @internal\n    */\n    toString() { return this._tree.toString(); }\n}\nfunction getChildren(node, type, before, after) {\n    let cur = node.cursor(), result = [];\n    if (!cur.firstChild())\n        return result;\n    if (before != null)\n        for (let found = false; !found;) {\n            found = cur.type.is(before);\n            if (!cur.nextSibling())\n                return result;\n        }\n    for (;;) {\n        if (after != null && cur.type.is(after))\n            return result;\n        if (cur.type.is(type))\n            result.push(cur.node);\n        if (!cur.nextSibling())\n            return after == null ? result : [];\n    }\n}\nfunction matchNodeContext(node, context, i = context.length - 1) {\n    for (let p = node; i >= 0; p = p.parent) {\n        if (!p)\n            return false;\n        if (!p.type.isAnonymous) {\n            if (context[i] && context[i] != p.name)\n                return false;\n            i--;\n        }\n    }\n    return true;\n}\nclass BufferContext {\n    constructor(parent, buffer, index, start) {\n        this.parent = parent;\n        this.buffer = buffer;\n        this.index = index;\n        this.start = start;\n    }\n}\nclass BufferNode extends BaseNode {\n    get name() { return this.type.name; }\n    get from() { return this.context.start + this.context.buffer.buffer[this.index + 1]; }\n    get to() { return this.context.start + this.context.buffer.buffer[this.index + 2]; }\n    constructor(context, _parent, index) {\n        super();\n        this.context = context;\n        this._parent = _parent;\n        this.index = index;\n        this.type = context.buffer.set.types[context.buffer.buffer[index]];\n    }\n    child(dir, pos, side) {\n        let { buffer } = this.context;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], dir, pos - this.context.start, side);\n        return index < 0 ? null : new BufferNode(this.context, this, index);\n    }\n    get firstChild() { return this.child(1, 0, 4 /* Side.DontCare */); }\n    get lastChild() { return this.child(-1, 0, 4 /* Side.DontCare */); }\n    childAfter(pos) { return this.child(1, pos, 2 /* Side.After */); }\n    childBefore(pos) { return this.child(-1, pos, -2 /* Side.Before */); }\n    enter(pos, side, mode = 0) {\n        if (mode & IterMode.ExcludeBuffers)\n            return null;\n        let { buffer } = this.context;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], side > 0 ? 1 : -1, pos - this.context.start, side);\n        return index < 0 ? null : new BufferNode(this.context, this, index);\n    }\n    get parent() {\n        return this._parent || this.context.parent.nextSignificantParent();\n    }\n    externalSibling(dir) {\n        return this._parent ? null : this.context.parent.nextChild(this.context.index + dir, dir, 0, 4 /* Side.DontCare */);\n    }\n    get nextSibling() {\n        let { buffer } = this.context;\n        let after = buffer.buffer[this.index + 3];\n        if (after < (this._parent ? buffer.buffer[this._parent.index + 3] : buffer.buffer.length))\n            return new BufferNode(this.context, this._parent, after);\n        return this.externalSibling(1);\n    }\n    get prevSibling() {\n        let { buffer } = this.context;\n        let parentStart = this._parent ? this._parent.index + 4 : 0;\n        if (this.index == parentStart)\n            return this.externalSibling(-1);\n        return new BufferNode(this.context, this._parent, buffer.findChild(parentStart, this.index, -1, 0, 4 /* Side.DontCare */));\n    }\n    get tree() { return null; }\n    toTree() {\n        let children = [], positions = [];\n        let { buffer } = this.context;\n        let startI = this.index + 4, endI = buffer.buffer[this.index + 3];\n        if (endI > startI) {\n            let from = buffer.buffer[this.index + 1];\n            children.push(buffer.slice(startI, endI, from));\n            positions.push(0);\n        }\n        return new Tree(this.type, children, positions, this.to - this.from);\n    }\n    /**\n    @internal\n    */\n    toString() { return this.context.buffer.childString(this.index); }\n}\nfunction iterStack(heads) {\n    if (!heads.length)\n        return null;\n    let pick = 0, picked = heads[0];\n    for (let i = 1; i < heads.length; i++) {\n        let node = heads[i];\n        if (node.from > picked.from || node.to < picked.to) {\n            picked = node;\n            pick = i;\n        }\n    }\n    let next = picked instanceof TreeNode && picked.index < 0 ? null : picked.parent;\n    let newHeads = heads.slice();\n    if (next)\n        newHeads[pick] = next;\n    else\n        newHeads.splice(pick, 1);\n    return new StackIterator(newHeads, picked);\n}\nclass StackIterator {\n    constructor(heads, node) {\n        this.heads = heads;\n        this.node = node;\n    }\n    get next() { return iterStack(this.heads); }\n}\nfunction stackIterator(tree, pos, side) {\n    let inner = tree.resolveInner(pos, side), layers = null;\n    for (let scan = inner instanceof TreeNode ? inner : inner.context.parent; scan; scan = scan.parent) {\n        if (scan.index < 0) { // This is an overlay root\n            let parent = scan.parent;\n            (layers || (layers = [inner])).push(parent.resolve(pos, side));\n            scan = parent;\n        }\n        else {\n            let mount = MountedTree.get(scan.tree);\n            // Relevant overlay branching off\n            if (mount && mount.overlay && mount.overlay[0].from <= pos && mount.overlay[mount.overlay.length - 1].to >= pos) {\n                let root = new TreeNode(mount.tree, mount.overlay[0].from + scan.from, -1, scan);\n                (layers || (layers = [inner])).push(resolveNode(root, pos, side, false));\n            }\n        }\n    }\n    return layers ? iterStack(layers) : inner;\n}\n/**\nA tree cursor object focuses on a given node in a syntax tree, and\nallows you to move to adjacent nodes.\n*/\nclass TreeCursor {\n    /**\n    Shorthand for `.type.name`.\n    */\n    get name() { return this.type.name; }\n    /**\n    @internal\n    */\n    constructor(node, \n    /**\n    @internal\n    */\n    mode = 0) {\n        this.mode = mode;\n        /**\n        @internal\n        */\n        this.buffer = null;\n        this.stack = [];\n        /**\n        @internal\n        */\n        this.index = 0;\n        this.bufferNode = null;\n        if (node instanceof TreeNode) {\n            this.yieldNode(node);\n        }\n        else {\n            this._tree = node.context.parent;\n            this.buffer = node.context;\n            for (let n = node._parent; n; n = n._parent)\n                this.stack.unshift(n.index);\n            this.bufferNode = node;\n            this.yieldBuf(node.index);\n        }\n    }\n    yieldNode(node) {\n        if (!node)\n            return false;\n        this._tree = node;\n        this.type = node.type;\n        this.from = node.from;\n        this.to = node.to;\n        return true;\n    }\n    yieldBuf(index, type) {\n        this.index = index;\n        let { start, buffer } = this.buffer;\n        this.type = type || buffer.set.types[buffer.buffer[index]];\n        this.from = start + buffer.buffer[index + 1];\n        this.to = start + buffer.buffer[index + 2];\n        return true;\n    }\n    /**\n    @internal\n    */\n    yield(node) {\n        if (!node)\n            return false;\n        if (node instanceof TreeNode) {\n            this.buffer = null;\n            return this.yieldNode(node);\n        }\n        this.buffer = node.context;\n        return this.yieldBuf(node.index, node.type);\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return this.buffer ? this.buffer.buffer.childString(this.index) : this._tree.toString();\n    }\n    /**\n    @internal\n    */\n    enterChild(dir, pos, side) {\n        if (!this.buffer)\n            return this.yield(this._tree.nextChild(dir < 0 ? this._tree._tree.children.length - 1 : 0, dir, pos, side, this.mode));\n        let { buffer } = this.buffer;\n        let index = buffer.findChild(this.index + 4, buffer.buffer[this.index + 3], dir, pos - this.buffer.start, side);\n        if (index < 0)\n            return false;\n        this.stack.push(this.index);\n        return this.yieldBuf(index);\n    }\n    /**\n    Move the cursor to this node's first child. When this returns\n    false, the node has no child, and the cursor has not been moved.\n    */\n    firstChild() { return this.enterChild(1, 0, 4 /* Side.DontCare */); }\n    /**\n    Move the cursor to this node's last child.\n    */\n    lastChild() { return this.enterChild(-1, 0, 4 /* Side.DontCare */); }\n    /**\n    Move the cursor to the first child that ends after `pos`.\n    */\n    childAfter(pos) { return this.enterChild(1, pos, 2 /* Side.After */); }\n    /**\n    Move to the last child that starts before `pos`.\n    */\n    childBefore(pos) { return this.enterChild(-1, pos, -2 /* Side.Before */); }\n    /**\n    Move the cursor to the child around `pos`. If side is -1 the\n    child may end at that position, when 1 it may start there. This\n    will also enter [overlaid](#common.MountedTree.overlay)\n    [mounted](#common.NodeProp^mounted) trees unless `overlays` is\n    set to false.\n    */\n    enter(pos, side, mode = this.mode) {\n        if (!this.buffer)\n            return this.yield(this._tree.enter(pos, side, mode));\n        return mode & IterMode.ExcludeBuffers ? false : this.enterChild(1, pos, side);\n    }\n    /**\n    Move to the node's parent node, if this isn't the top node.\n    */\n    parent() {\n        if (!this.buffer)\n            return this.yieldNode((this.mode & IterMode.IncludeAnonymous) ? this._tree._parent : this._tree.parent);\n        if (this.stack.length)\n            return this.yieldBuf(this.stack.pop());\n        let parent = (this.mode & IterMode.IncludeAnonymous) ? this.buffer.parent : this.buffer.parent.nextSignificantParent();\n        this.buffer = null;\n        return this.yieldNode(parent);\n    }\n    /**\n    @internal\n    */\n    sibling(dir) {\n        if (!this.buffer)\n            return !this._tree._parent ? false\n                : this.yield(this._tree.index < 0 ? null\n                    : this._tree._parent.nextChild(this._tree.index + dir, dir, 0, 4 /* Side.DontCare */, this.mode));\n        let { buffer } = this.buffer, d = this.stack.length - 1;\n        if (dir < 0) {\n            let parentStart = d < 0 ? 0 : this.stack[d] + 4;\n            if (this.index != parentStart)\n                return this.yieldBuf(buffer.findChild(parentStart, this.index, -1, 0, 4 /* Side.DontCare */));\n        }\n        else {\n            let after = buffer.buffer[this.index + 3];\n            if (after < (d < 0 ? buffer.buffer.length : buffer.buffer[this.stack[d] + 3]))\n                return this.yieldBuf(after);\n        }\n        return d < 0 ? this.yield(this.buffer.parent.nextChild(this.buffer.index + dir, dir, 0, 4 /* Side.DontCare */, this.mode)) : false;\n    }\n    /**\n    Move to this node's next sibling, if any.\n    */\n    nextSibling() { return this.sibling(1); }\n    /**\n    Move to this node's previous sibling, if any.\n    */\n    prevSibling() { return this.sibling(-1); }\n    atLastNode(dir) {\n        let index, parent, { buffer } = this;\n        if (buffer) {\n            if (dir > 0) {\n                if (this.index < buffer.buffer.buffer.length)\n                    return false;\n            }\n            else {\n                for (let i = 0; i < this.index; i++)\n                    if (buffer.buffer.buffer[i + 3] < this.index)\n                        return false;\n            }\n            ({ index, parent } = buffer);\n        }\n        else {\n            ({ index, _parent: parent } = this._tree);\n        }\n        for (; parent; { index, _parent: parent } = parent) {\n            if (index > -1)\n                for (let i = index + dir, e = dir < 0 ? -1 : parent._tree.children.length; i != e; i += dir) {\n                    let child = parent._tree.children[i];\n                    if ((this.mode & IterMode.IncludeAnonymous) ||\n                        child instanceof TreeBuffer ||\n                        !child.type.isAnonymous ||\n                        hasChild(child))\n                        return false;\n                }\n        }\n        return true;\n    }\n    move(dir, enter) {\n        if (enter && this.enterChild(dir, 0, 4 /* Side.DontCare */))\n            return true;\n        for (;;) {\n            if (this.sibling(dir))\n                return true;\n            if (this.atLastNode(dir) || !this.parent())\n                return false;\n        }\n    }\n    /**\n    Move to the next node in a\n    [pre-order](https://en.wikipedia.org/wiki/Tree_traversal#Pre-order,_NLR)\n    traversal, going from a node to its first child or, if the\n    current node is empty or `enter` is false, its next sibling or\n    the next sibling of the first parent node that has one.\n    */\n    next(enter = true) { return this.move(1, enter); }\n    /**\n    Move to the next node in a last-to-first pre-order traversal. A\n    node is followed by its last child or, if it has none, its\n    previous sibling or the previous sibling of the first parent\n    node that has one.\n    */\n    prev(enter = true) { return this.move(-1, enter); }\n    /**\n    Move the cursor to the innermost node that covers `pos`. If\n    `side` is -1, it will enter nodes that end at `pos`. If it is 1,\n    it will enter nodes that start at `pos`.\n    */\n    moveTo(pos, side = 0) {\n        // Move up to a node that actually holds the position, if possible\n        while (this.from == this.to ||\n            (side < 1 ? this.from >= pos : this.from > pos) ||\n            (side > -1 ? this.to <= pos : this.to < pos))\n            if (!this.parent())\n                break;\n        // Then scan down into child nodes as far as possible\n        while (this.enterChild(1, pos, side)) { }\n        return this;\n    }\n    /**\n    Get a [syntax node](#common.SyntaxNode) at the cursor's current\n    position.\n    */\n    get node() {\n        if (!this.buffer)\n            return this._tree;\n        let cache = this.bufferNode, result = null, depth = 0;\n        if (cache && cache.context == this.buffer) {\n            scan: for (let index = this.index, d = this.stack.length; d >= 0;) {\n                for (let c = cache; c; c = c._parent)\n                    if (c.index == index) {\n                        if (index == this.index)\n                            return c;\n                        result = c;\n                        depth = d + 1;\n                        break scan;\n                    }\n                index = this.stack[--d];\n            }\n        }\n        for (let i = depth; i < this.stack.length; i++)\n            result = new BufferNode(this.buffer, result, this.stack[i]);\n        return this.bufferNode = new BufferNode(this.buffer, result, this.index);\n    }\n    /**\n    Get the [tree](#common.Tree) that represents the current node, if\n    any. Will return null when the node is in a [tree\n    buffer](#common.TreeBuffer).\n    */\n    get tree() {\n        return this.buffer ? null : this._tree._tree;\n    }\n    /**\n    Iterate over the current node and all its descendants, calling\n    `enter` when entering a node and `leave`, if given, when leaving\n    one. When `enter` returns `false`, any children of that node are\n    skipped, and `leave` isn't called for it.\n    */\n    iterate(enter, leave) {\n        for (let depth = 0;;) {\n            let mustLeave = false;\n            if (this.type.isAnonymous || enter(this) !== false) {\n                if (this.firstChild()) {\n                    depth++;\n                    continue;\n                }\n                if (!this.type.isAnonymous)\n                    mustLeave = true;\n            }\n            for (;;) {\n                if (mustLeave && leave)\n                    leave(this);\n                mustLeave = this.type.isAnonymous;\n                if (!depth)\n                    return;\n                if (this.nextSibling())\n                    break;\n                this.parent();\n                depth--;\n                mustLeave = true;\n            }\n        }\n    }\n    /**\n    Test whether the current node matches a given context—a sequence\n    of direct parent node names. Empty strings in the context array\n    are treated as wildcards.\n    */\n    matchContext(context) {\n        if (!this.buffer)\n            return matchNodeContext(this.node.parent, context);\n        let { buffer } = this.buffer, { types } = buffer.set;\n        for (let i = context.length - 1, d = this.stack.length - 1; i >= 0; d--) {\n            if (d < 0)\n                return matchNodeContext(this._tree, context, i);\n            let type = types[buffer.buffer[this.stack[d]]];\n            if (!type.isAnonymous) {\n                if (context[i] && context[i] != type.name)\n                    return false;\n                i--;\n            }\n        }\n        return true;\n    }\n}\nfunction hasChild(tree) {\n    return tree.children.some(ch => ch instanceof TreeBuffer || !ch.type.isAnonymous || hasChild(ch));\n}\nfunction buildTree(data) {\n    var _a;\n    let { buffer, nodeSet, maxBufferLength = DefaultBufferLength, reused = [], minRepeatType = nodeSet.types.length } = data;\n    let cursor = Array.isArray(buffer) ? new FlatBufferCursor(buffer, buffer.length) : buffer;\n    let types = nodeSet.types;\n    let contextHash = 0, lookAhead = 0;\n    function takeNode(parentStart, minPos, children, positions, inRepeat, depth) {\n        let { id, start, end, size } = cursor;\n        let lookAheadAtStart = lookAhead, contextAtStart = contextHash;\n        while (size < 0) {\n            cursor.next();\n            if (size == -1 /* SpecialRecord.Reuse */) {\n                let node = reused[id];\n                children.push(node);\n                positions.push(start - parentStart);\n                return;\n            }\n            else if (size == -3 /* SpecialRecord.ContextChange */) { // Context change\n                contextHash = id;\n                return;\n            }\n            else if (size == -4 /* SpecialRecord.LookAhead */) {\n                lookAhead = id;\n                return;\n            }\n            else {\n                throw new RangeError(`Unrecognized record size: ${size}`);\n            }\n        }\n        let type = types[id], node, buffer;\n        let startPos = start - parentStart;\n        if (end - start <= maxBufferLength && (buffer = findBufferSize(cursor.pos - minPos, inRepeat))) {\n            // Small enough for a buffer, and no reused nodes inside\n            let data = new Uint16Array(buffer.size - buffer.skip);\n            let endPos = cursor.pos - buffer.size, index = data.length;\n            while (cursor.pos > endPos)\n                index = copyToBuffer(buffer.start, data, index);\n            node = new TreeBuffer(data, end - buffer.start, nodeSet);\n            startPos = buffer.start - parentStart;\n        }\n        else { // Make it a node\n            let endPos = cursor.pos - size;\n            cursor.next();\n            let localChildren = [], localPositions = [];\n            let localInRepeat = id >= minRepeatType ? id : -1;\n            let lastGroup = 0, lastEnd = end;\n            while (cursor.pos > endPos) {\n                if (localInRepeat >= 0 && cursor.id == localInRepeat && cursor.size >= 0) {\n                    if (cursor.end <= lastEnd - maxBufferLength) {\n                        makeRepeatLeaf(localChildren, localPositions, start, lastGroup, cursor.end, lastEnd, localInRepeat, lookAheadAtStart, contextAtStart);\n                        lastGroup = localChildren.length;\n                        lastEnd = cursor.end;\n                    }\n                    cursor.next();\n                }\n                else if (depth > 2500 /* CutOff.Depth */) {\n                    takeFlatNode(start, endPos, localChildren, localPositions);\n                }\n                else {\n                    takeNode(start, endPos, localChildren, localPositions, localInRepeat, depth + 1);\n                }\n            }\n            if (localInRepeat >= 0 && lastGroup > 0 && lastGroup < localChildren.length)\n                makeRepeatLeaf(localChildren, localPositions, start, lastGroup, start, lastEnd, localInRepeat, lookAheadAtStart, contextAtStart);\n            localChildren.reverse();\n            localPositions.reverse();\n            if (localInRepeat > -1 && lastGroup > 0) {\n                let make = makeBalanced(type, contextAtStart);\n                node = balanceRange(type, localChildren, localPositions, 0, localChildren.length, 0, end - start, make, make);\n            }\n            else {\n                node = makeTree(type, localChildren, localPositions, end - start, lookAheadAtStart - end, contextAtStart);\n            }\n        }\n        children.push(node);\n        positions.push(startPos);\n    }\n    function takeFlatNode(parentStart, minPos, children, positions) {\n        let nodes = []; // Temporary, inverted array of leaf nodes found, with absolute positions\n        let nodeCount = 0, stopAt = -1;\n        while (cursor.pos > minPos) {\n            let { id, start, end, size } = cursor;\n            if (size > 4) { // Not a leaf\n                cursor.next();\n            }\n            else if (stopAt > -1 && start < stopAt) {\n                break;\n            }\n            else {\n                if (stopAt < 0)\n                    stopAt = end - maxBufferLength;\n                nodes.push(id, start, end);\n                nodeCount++;\n                cursor.next();\n            }\n        }\n        if (nodeCount) {\n            let buffer = new Uint16Array(nodeCount * 4);\n            let start = nodes[nodes.length - 2];\n            for (let i = nodes.length - 3, j = 0; i >= 0; i -= 3) {\n                buffer[j++] = nodes[i];\n                buffer[j++] = nodes[i + 1] - start;\n                buffer[j++] = nodes[i + 2] - start;\n                buffer[j++] = j;\n            }\n            children.push(new TreeBuffer(buffer, nodes[2] - start, nodeSet));\n            positions.push(start - parentStart);\n        }\n    }\n    function makeBalanced(type, contextHash) {\n        return (children, positions, length) => {\n            let lookAhead = 0, lastI = children.length - 1, last, lookAheadProp;\n            if (lastI >= 0 && (last = children[lastI]) instanceof Tree) {\n                if (!lastI && last.type == type && last.length == length)\n                    return last;\n                if (lookAheadProp = last.prop(NodeProp.lookAhead))\n                    lookAhead = positions[lastI] + last.length + lookAheadProp;\n            }\n            return makeTree(type, children, positions, length, lookAhead, contextHash);\n        };\n    }\n    function makeRepeatLeaf(children, positions, base, i, from, to, type, lookAhead, contextHash) {\n        let localChildren = [], localPositions = [];\n        while (children.length > i) {\n            localChildren.push(children.pop());\n            localPositions.push(positions.pop() + base - from);\n        }\n        children.push(makeTree(nodeSet.types[type], localChildren, localPositions, to - from, lookAhead - to, contextHash));\n        positions.push(from - base);\n    }\n    function makeTree(type, children, positions, length, lookAhead, contextHash, props) {\n        if (contextHash) {\n            let pair = [NodeProp.contextHash, contextHash];\n            props = props ? [pair].concat(props) : [pair];\n        }\n        if (lookAhead > 25) {\n            let pair = [NodeProp.lookAhead, lookAhead];\n            props = props ? [pair].concat(props) : [pair];\n        }\n        return new Tree(type, children, positions, length, props);\n    }\n    function findBufferSize(maxSize, inRepeat) {\n        // Scan through the buffer to find previous siblings that fit\n        // together in a TreeBuffer, and don't contain any reused nodes\n        // (which can't be stored in a buffer).\n        // If `inRepeat` is > -1, ignore node boundaries of that type for\n        // nesting, but make sure the end falls either at the start\n        // (`maxSize`) or before such a node.\n        let fork = cursor.fork();\n        let size = 0, start = 0, skip = 0, minStart = fork.end - maxBufferLength;\n        let result = { size: 0, start: 0, skip: 0 };\n        scan: for (let minPos = fork.pos - maxSize; fork.pos > minPos;) {\n            let nodeSize = fork.size;\n            // Pretend nested repeat nodes of the same type don't exist\n            if (fork.id == inRepeat && nodeSize >= 0) {\n                // Except that we store the current state as a valid return\n                // value.\n                result.size = size;\n                result.start = start;\n                result.skip = skip;\n                skip += 4;\n                size += 4;\n                fork.next();\n                continue;\n            }\n            let startPos = fork.pos - nodeSize;\n            if (nodeSize < 0 || startPos < minPos || fork.start < minStart)\n                break;\n            let localSkipped = fork.id >= minRepeatType ? 4 : 0;\n            let nodeStart = fork.start;\n            fork.next();\n            while (fork.pos > startPos) {\n                if (fork.size < 0) {\n                    if (fork.size == -3 /* SpecialRecord.ContextChange */)\n                        localSkipped += 4;\n                    else\n                        break scan;\n                }\n                else if (fork.id >= minRepeatType) {\n                    localSkipped += 4;\n                }\n                fork.next();\n            }\n            start = nodeStart;\n            size += nodeSize;\n            skip += localSkipped;\n        }\n        if (inRepeat < 0 || size == maxSize) {\n            result.size = size;\n            result.start = start;\n            result.skip = skip;\n        }\n        return result.size > 4 ? result : undefined;\n    }\n    function copyToBuffer(bufferStart, buffer, index) {\n        let { id, start, end, size } = cursor;\n        cursor.next();\n        if (size >= 0 && id < minRepeatType) {\n            let startIndex = index;\n            if (size > 4) {\n                let endPos = cursor.pos - (size - 4);\n                while (cursor.pos > endPos)\n                    index = copyToBuffer(bufferStart, buffer, index);\n            }\n            buffer[--index] = startIndex;\n            buffer[--index] = end - bufferStart;\n            buffer[--index] = start - bufferStart;\n            buffer[--index] = id;\n        }\n        else if (size == -3 /* SpecialRecord.ContextChange */) {\n            contextHash = id;\n        }\n        else if (size == -4 /* SpecialRecord.LookAhead */) {\n            lookAhead = id;\n        }\n        return index;\n    }\n    let children = [], positions = [];\n    while (cursor.pos > 0)\n        takeNode(data.start || 0, data.bufferStart || 0, children, positions, -1, 0);\n    let length = (_a = data.length) !== null && _a !== void 0 ? _a : (children.length ? positions[0] + children[0].length : 0);\n    return new Tree(types[data.topID], children.reverse(), positions.reverse(), length);\n}\nconst nodeSizeCache = new WeakMap;\nfunction nodeSize(balanceType, node) {\n    if (!balanceType.isAnonymous || node instanceof TreeBuffer || node.type != balanceType)\n        return 1;\n    let size = nodeSizeCache.get(node);\n    if (size == null) {\n        size = 1;\n        for (let child of node.children) {\n            if (child.type != balanceType || !(child instanceof Tree)) {\n                size = 1;\n                break;\n            }\n            size += nodeSize(balanceType, child);\n        }\n        nodeSizeCache.set(node, size);\n    }\n    return size;\n}\nfunction balanceRange(\n// The type the balanced tree's inner nodes.\nbalanceType, \n// The direct children and their positions\nchildren, positions, \n// The index range in children/positions to use\nfrom, to, \n// The start position of the nodes, relative to their parent.\nstart, \n// Length of the outer node\nlength, \n// Function to build the top node of the balanced tree\nmkTop, \n// Function to build internal nodes for the balanced tree\nmkTree) {\n    let total = 0;\n    for (let i = from; i < to; i++)\n        total += nodeSize(balanceType, children[i]);\n    let maxChild = Math.ceil((total * 1.5) / 8 /* Balance.BranchFactor */);\n    let localChildren = [], localPositions = [];\n    function divide(children, positions, from, to, offset) {\n        for (let i = from; i < to;) {\n            let groupFrom = i, groupStart = positions[i], groupSize = nodeSize(balanceType, children[i]);\n            i++;\n            for (; i < to; i++) {\n                let nextSize = nodeSize(balanceType, children[i]);\n                if (groupSize + nextSize >= maxChild)\n                    break;\n                groupSize += nextSize;\n            }\n            if (i == groupFrom + 1) {\n                if (groupSize > maxChild) {\n                    let only = children[groupFrom]; // Only trees can have a size > 1\n                    divide(only.children, only.positions, 0, only.children.length, positions[groupFrom] + offset);\n                    continue;\n                }\n                localChildren.push(children[groupFrom]);\n            }\n            else {\n                let length = positions[i - 1] + children[i - 1].length - groupStart;\n                localChildren.push(balanceRange(balanceType, children, positions, groupFrom, i, groupStart, length, null, mkTree));\n            }\n            localPositions.push(groupStart + offset - start);\n        }\n    }\n    divide(children, positions, from, to, 0);\n    return (mkTop || mkTree)(localChildren, localPositions, length);\n}\n/**\nProvides a way to associate values with pieces of trees. As long\nas that part of the tree is reused, the associated values can be\nretrieved from an updated tree.\n*/\nclass NodeWeakMap {\n    constructor() {\n        this.map = new WeakMap();\n    }\n    setBuffer(buffer, index, value) {\n        let inner = this.map.get(buffer);\n        if (!inner)\n            this.map.set(buffer, inner = new Map);\n        inner.set(index, value);\n    }\n    getBuffer(buffer, index) {\n        let inner = this.map.get(buffer);\n        return inner && inner.get(index);\n    }\n    /**\n    Set the value for this syntax node.\n    */\n    set(node, value) {\n        if (node instanceof BufferNode)\n            this.setBuffer(node.context.buffer, node.index, value);\n        else if (node instanceof TreeNode)\n            this.map.set(node.tree, value);\n    }\n    /**\n    Retrieve value for this syntax node, if it exists in the map.\n    */\n    get(node) {\n        return node instanceof BufferNode ? this.getBuffer(node.context.buffer, node.index)\n            : node instanceof TreeNode ? this.map.get(node.tree) : undefined;\n    }\n    /**\n    Set the value for the node that a cursor currently points to.\n    */\n    cursorSet(cursor, value) {\n        if (cursor.buffer)\n            this.setBuffer(cursor.buffer.buffer, cursor.index, value);\n        else\n            this.map.set(cursor.tree, value);\n    }\n    /**\n    Retrieve the value for the node that a cursor currently points\n    to.\n    */\n    cursorGet(cursor) {\n        return cursor.buffer ? this.getBuffer(cursor.buffer.buffer, cursor.index) : this.map.get(cursor.tree);\n    }\n}\n\n/**\nTree fragments are used during [incremental\nparsing](#common.Parser.startParse) to track parts of old trees\nthat can be reused in a new parse. An array of fragments is used\nto track regions of an old tree whose nodes might be reused in new\nparses. Use the static\n[`applyChanges`](#common.TreeFragment^applyChanges) method to\nupdate fragments for document changes.\n*/\nclass TreeFragment {\n    /**\n    Construct a tree fragment. You'll usually want to use\n    [`addTree`](#common.TreeFragment^addTree) and\n    [`applyChanges`](#common.TreeFragment^applyChanges) instead of\n    calling this directly.\n    */\n    constructor(\n    /**\n    The start of the unchanged range pointed to by this fragment.\n    This refers to an offset in the _updated_ document (as opposed\n    to the original tree).\n    */\n    from, \n    /**\n    The end of the unchanged range.\n    */\n    to, \n    /**\n    The tree that this fragment is based on.\n    */\n    tree, \n    /**\n    The offset between the fragment's tree and the document that\n    this fragment can be used against. Add this when going from\n    document to tree positions, subtract it to go from tree to\n    document positions.\n    */\n    offset, openStart = false, openEnd = false) {\n        this.from = from;\n        this.to = to;\n        this.tree = tree;\n        this.offset = offset;\n        this.open = (openStart ? 1 /* Open.Start */ : 0) | (openEnd ? 2 /* Open.End */ : 0);\n    }\n    /**\n    Whether the start of the fragment represents the start of a\n    parse, or the end of a change. (In the second case, it may not\n    be safe to reuse some nodes at the start, depending on the\n    parsing algorithm.)\n    */\n    get openStart() { return (this.open & 1 /* Open.Start */) > 0; }\n    /**\n    Whether the end of the fragment represents the end of a\n    full-document parse, or the start of a change.\n    */\n    get openEnd() { return (this.open & 2 /* Open.End */) > 0; }\n    /**\n    Create a set of fragments from a freshly parsed tree, or update\n    an existing set of fragments by replacing the ones that overlap\n    with a tree with content from the new tree. When `partial` is\n    true, the parse is treated as incomplete, and the resulting\n    fragment has [`openEnd`](#common.TreeFragment.openEnd) set to\n    true.\n    */\n    static addTree(tree, fragments = [], partial = false) {\n        let result = [new TreeFragment(0, tree.length, tree, 0, false, partial)];\n        for (let f of fragments)\n            if (f.to > tree.length)\n                result.push(f);\n        return result;\n    }\n    /**\n    Apply a set of edits to an array of fragments, removing or\n    splitting fragments as necessary to remove edited ranges, and\n    adjusting offsets for fragments that moved.\n    */\n    static applyChanges(fragments, changes, minGap = 128) {\n        if (!changes.length)\n            return fragments;\n        let result = [];\n        let fI = 1, nextF = fragments.length ? fragments[0] : null;\n        for (let cI = 0, pos = 0, off = 0;; cI++) {\n            let nextC = cI < changes.length ? changes[cI] : null;\n            let nextPos = nextC ? nextC.fromA : 1e9;\n            if (nextPos - pos >= minGap)\n                while (nextF && nextF.from < nextPos) {\n                    let cut = nextF;\n                    if (pos >= cut.from || nextPos <= cut.to || off) {\n                        let fFrom = Math.max(cut.from, pos) - off, fTo = Math.min(cut.to, nextPos) - off;\n                        cut = fFrom >= fTo ? null : new TreeFragment(fFrom, fTo, cut.tree, cut.offset + off, cI > 0, !!nextC);\n                    }\n                    if (cut)\n                        result.push(cut);\n                    if (nextF.to > nextPos)\n                        break;\n                    nextF = fI < fragments.length ? fragments[fI++] : null;\n                }\n            if (!nextC)\n                break;\n            pos = nextC.toA;\n            off = nextC.toA - nextC.toB;\n        }\n        return result;\n    }\n}\n/**\nA superclass that parsers should extend.\n*/\nclass Parser {\n    /**\n    Start a parse, returning a [partial parse](#common.PartialParse)\n    object. [`fragments`](#common.TreeFragment) can be passed in to\n    make the parse incremental.\n    \n    By default, the entire input is parsed. You can pass `ranges`,\n    which should be a sorted array of non-empty, non-overlapping\n    ranges, to parse only those ranges. The tree returned in that\n    case will start at `ranges[0].from`.\n    */\n    startParse(input, fragments, ranges) {\n        if (typeof input == \"string\")\n            input = new StringInput(input);\n        ranges = !ranges ? [new Range(0, input.length)] : ranges.length ? ranges.map(r => new Range(r.from, r.to)) : [new Range(0, 0)];\n        return this.createParse(input, fragments || [], ranges);\n    }\n    /**\n    Run a full parse, returning the resulting tree.\n    */\n    parse(input, fragments, ranges) {\n        let parse = this.startParse(input, fragments, ranges);\n        for (;;) {\n            let done = parse.advance();\n            if (done)\n                return done;\n        }\n    }\n}\nclass StringInput {\n    constructor(string) {\n        this.string = string;\n    }\n    get length() { return this.string.length; }\n    chunk(from) { return this.string.slice(from); }\n    get lineChunks() { return false; }\n    read(from, to) { return this.string.slice(from, to); }\n}\n\n/**\nCreate a parse wrapper that, after the inner parse completes,\nscans its tree for mixed language regions with the `nest`\nfunction, runs the resulting [inner parses](#common.NestedParse),\nand then [mounts](#common.NodeProp^mounted) their results onto the\ntree.\n*/\nfunction parseMixed(nest) {\n    return (parse, input, fragments, ranges) => new MixedParse(parse, nest, input, fragments, ranges);\n}\nclass InnerParse {\n    constructor(parser, parse, overlay, target, from) {\n        this.parser = parser;\n        this.parse = parse;\n        this.overlay = overlay;\n        this.target = target;\n        this.from = from;\n    }\n}\nfunction checkRanges(ranges) {\n    if (!ranges.length || ranges.some(r => r.from >= r.to))\n        throw new RangeError(\"Invalid inner parse ranges given: \" + JSON.stringify(ranges));\n}\nclass ActiveOverlay {\n    constructor(parser, predicate, mounts, index, start, target, prev) {\n        this.parser = parser;\n        this.predicate = predicate;\n        this.mounts = mounts;\n        this.index = index;\n        this.start = start;\n        this.target = target;\n        this.prev = prev;\n        this.depth = 0;\n        this.ranges = [];\n    }\n}\nconst stoppedInner = new NodeProp({ perNode: true });\nclass MixedParse {\n    constructor(base, nest, input, fragments, ranges) {\n        this.nest = nest;\n        this.input = input;\n        this.fragments = fragments;\n        this.ranges = ranges;\n        this.inner = [];\n        this.innerDone = 0;\n        this.baseTree = null;\n        this.stoppedAt = null;\n        this.baseParse = base;\n    }\n    advance() {\n        if (this.baseParse) {\n            let done = this.baseParse.advance();\n            if (!done)\n                return null;\n            this.baseParse = null;\n            this.baseTree = done;\n            this.startInner();\n            if (this.stoppedAt != null)\n                for (let inner of this.inner)\n                    inner.parse.stopAt(this.stoppedAt);\n        }\n        if (this.innerDone == this.inner.length) {\n            let result = this.baseTree;\n            if (this.stoppedAt != null)\n                result = new Tree(result.type, result.children, result.positions, result.length, result.propValues.concat([[stoppedInner, this.stoppedAt]]));\n            return result;\n        }\n        let inner = this.inner[this.innerDone], done = inner.parse.advance();\n        if (done) {\n            this.innerDone++;\n            // This is a somewhat dodgy but super helpful hack where we\n            // patch up nodes created by the inner parse (and thus\n            // presumably not aliased anywhere else) to hold the information\n            // about the inner parse.\n            let props = Object.assign(Object.create(null), inner.target.props);\n            props[NodeProp.mounted.id] = new MountedTree(done, inner.overlay, inner.parser);\n            inner.target.props = props;\n        }\n        return null;\n    }\n    get parsedPos() {\n        if (this.baseParse)\n            return 0;\n        let pos = this.input.length;\n        for (let i = this.innerDone; i < this.inner.length; i++) {\n            if (this.inner[i].from < pos)\n                pos = Math.min(pos, this.inner[i].parse.parsedPos);\n        }\n        return pos;\n    }\n    stopAt(pos) {\n        this.stoppedAt = pos;\n        if (this.baseParse)\n            this.baseParse.stopAt(pos);\n        else\n            for (let i = this.innerDone; i < this.inner.length; i++)\n                this.inner[i].parse.stopAt(pos);\n    }\n    startInner() {\n        let fragmentCursor = new FragmentCursor(this.fragments);\n        let overlay = null;\n        let covered = null;\n        let cursor = new TreeCursor(new TreeNode(this.baseTree, this.ranges[0].from, 0, null), IterMode.IncludeAnonymous | IterMode.IgnoreMounts);\n        scan: for (let nest, isCovered;;) {\n            let enter = true, range;\n            if (this.stoppedAt != null && cursor.from >= this.stoppedAt) {\n                enter = false;\n            }\n            else if (fragmentCursor.hasNode(cursor)) {\n                if (overlay) {\n                    let match = overlay.mounts.find(m => m.frag.from <= cursor.from && m.frag.to >= cursor.to && m.mount.overlay);\n                    if (match)\n                        for (let r of match.mount.overlay) {\n                            let from = r.from + match.pos, to = r.to + match.pos;\n                            if (from >= cursor.from && to <= cursor.to && !overlay.ranges.some(r => r.from < to && r.to > from))\n                                overlay.ranges.push({ from, to });\n                        }\n                }\n                enter = false;\n            }\n            else if (covered && (isCovered = checkCover(covered.ranges, cursor.from, cursor.to))) {\n                enter = isCovered != 2 /* Cover.Full */;\n            }\n            else if (!cursor.type.isAnonymous && (nest = this.nest(cursor, this.input)) &&\n                (cursor.from < cursor.to || !nest.overlay)) {\n                if (!cursor.tree)\n                    materialize(cursor);\n                let oldMounts = fragmentCursor.findMounts(cursor.from, nest.parser);\n                if (typeof nest.overlay == \"function\") {\n                    overlay = new ActiveOverlay(nest.parser, nest.overlay, oldMounts, this.inner.length, cursor.from, cursor.tree, overlay);\n                }\n                else {\n                    let ranges = punchRanges(this.ranges, nest.overlay ||\n                        (cursor.from < cursor.to ? [new Range(cursor.from, cursor.to)] : []));\n                    if (ranges.length)\n                        checkRanges(ranges);\n                    if (ranges.length || !nest.overlay)\n                        this.inner.push(new InnerParse(nest.parser, ranges.length ? nest.parser.startParse(this.input, enterFragments(oldMounts, ranges), ranges)\n                            : nest.parser.startParse(\"\"), nest.overlay ? nest.overlay.map(r => new Range(r.from - cursor.from, r.to - cursor.from)) : null, cursor.tree, ranges.length ? ranges[0].from : cursor.from));\n                    if (!nest.overlay)\n                        enter = false;\n                    else if (ranges.length)\n                        covered = { ranges, depth: 0, prev: covered };\n                }\n            }\n            else if (overlay && (range = overlay.predicate(cursor))) {\n                if (range === true)\n                    range = new Range(cursor.from, cursor.to);\n                if (range.from < range.to) {\n                    let last = overlay.ranges.length - 1;\n                    if (last >= 0 && overlay.ranges[last].to == range.from)\n                        overlay.ranges[last] = { from: overlay.ranges[last].from, to: range.to };\n                    else\n                        overlay.ranges.push(range);\n                }\n            }\n            if (enter && cursor.firstChild()) {\n                if (overlay)\n                    overlay.depth++;\n                if (covered)\n                    covered.depth++;\n            }\n            else {\n                for (;;) {\n                    if (cursor.nextSibling())\n                        break;\n                    if (!cursor.parent())\n                        break scan;\n                    if (overlay && !--overlay.depth) {\n                        let ranges = punchRanges(this.ranges, overlay.ranges);\n                        if (ranges.length) {\n                            checkRanges(ranges);\n                            this.inner.splice(overlay.index, 0, new InnerParse(overlay.parser, overlay.parser.startParse(this.input, enterFragments(overlay.mounts, ranges), ranges), overlay.ranges.map(r => new Range(r.from - overlay.start, r.to - overlay.start)), overlay.target, ranges[0].from));\n                        }\n                        overlay = overlay.prev;\n                    }\n                    if (covered && !--covered.depth)\n                        covered = covered.prev;\n                }\n            }\n        }\n    }\n}\nfunction checkCover(covered, from, to) {\n    for (let range of covered) {\n        if (range.from >= to)\n            break;\n        if (range.to > from)\n            return range.from <= from && range.to >= to ? 2 /* Cover.Full */ : 1 /* Cover.Partial */;\n    }\n    return 0 /* Cover.None */;\n}\n// Take a piece of buffer and convert it into a stand-alone\n// TreeBuffer.\nfunction sliceBuf(buf, startI, endI, nodes, positions, off) {\n    if (startI < endI) {\n        let from = buf.buffer[startI + 1];\n        nodes.push(buf.slice(startI, endI, from));\n        positions.push(from - off);\n    }\n}\n// This function takes a node that's in a buffer, and converts it, and\n// its parent buffer nodes, into a Tree. This is again acting on the\n// assumption that the trees and buffers have been constructed by the\n// parse that was ran via the mix parser, and thus aren't shared with\n// any other code, making violations of the immutability safe.\nfunction materialize(cursor) {\n    let { node } = cursor, stack = [];\n    let buffer = node.context.buffer;\n    // Scan up to the nearest tree\n    do {\n        stack.push(cursor.index);\n        cursor.parent();\n    } while (!cursor.tree);\n    // Find the index of the buffer in that tree\n    let base = cursor.tree, i = base.children.indexOf(buffer);\n    let buf = base.children[i], b = buf.buffer, newStack = [i];\n    // Split a level in the buffer, putting the nodes before and after\n    // the child that contains `node` into new buffers.\n    function split(startI, endI, type, innerOffset, length, stackPos) {\n        let targetI = stack[stackPos];\n        let children = [], positions = [];\n        sliceBuf(buf, startI, targetI, children, positions, innerOffset);\n        let from = b[targetI + 1], to = b[targetI + 2];\n        newStack.push(children.length);\n        let child = stackPos\n            ? split(targetI + 4, b[targetI + 3], buf.set.types[b[targetI]], from, to - from, stackPos - 1)\n            : node.toTree();\n        children.push(child);\n        positions.push(from - innerOffset);\n        sliceBuf(buf, b[targetI + 3], endI, children, positions, innerOffset);\n        return new Tree(type, children, positions, length);\n    }\n    base.children[i] = split(0, b.length, NodeType.none, 0, buf.length, stack.length - 1);\n    // Move the cursor back to the target node\n    for (let index of newStack) {\n        let tree = cursor.tree.children[index], pos = cursor.tree.positions[index];\n        cursor.yield(new TreeNode(tree, pos + cursor.from, index, cursor._tree));\n    }\n}\nclass StructureCursor {\n    constructor(root, offset) {\n        this.offset = offset;\n        this.done = false;\n        this.cursor = root.cursor(IterMode.IncludeAnonymous | IterMode.IgnoreMounts);\n    }\n    // Move to the first node (in pre-order) that starts at or after `pos`.\n    moveTo(pos) {\n        let { cursor } = this, p = pos - this.offset;\n        while (!this.done && cursor.from < p) {\n            if (cursor.to >= pos && cursor.enter(p, 1, IterMode.IgnoreOverlays | IterMode.ExcludeBuffers)) ;\n            else if (!cursor.next(false))\n                this.done = true;\n        }\n    }\n    hasNode(cursor) {\n        this.moveTo(cursor.from);\n        if (!this.done && this.cursor.from + this.offset == cursor.from && this.cursor.tree) {\n            for (let tree = this.cursor.tree;;) {\n                if (tree == cursor.tree)\n                    return true;\n                if (tree.children.length && tree.positions[0] == 0 && tree.children[0] instanceof Tree)\n                    tree = tree.children[0];\n                else\n                    break;\n            }\n        }\n        return false;\n    }\n}\nclass FragmentCursor {\n    constructor(fragments) {\n        var _a;\n        this.fragments = fragments;\n        this.curTo = 0;\n        this.fragI = 0;\n        if (fragments.length) {\n            let first = this.curFrag = fragments[0];\n            this.curTo = (_a = first.tree.prop(stoppedInner)) !== null && _a !== void 0 ? _a : first.to;\n            this.inner = new StructureCursor(first.tree, -first.offset);\n        }\n        else {\n            this.curFrag = this.inner = null;\n        }\n    }\n    hasNode(node) {\n        while (this.curFrag && node.from >= this.curTo)\n            this.nextFrag();\n        return this.curFrag && this.curFrag.from <= node.from && this.curTo >= node.to && this.inner.hasNode(node);\n    }\n    nextFrag() {\n        var _a;\n        this.fragI++;\n        if (this.fragI == this.fragments.length) {\n            this.curFrag = this.inner = null;\n        }\n        else {\n            let frag = this.curFrag = this.fragments[this.fragI];\n            this.curTo = (_a = frag.tree.prop(stoppedInner)) !== null && _a !== void 0 ? _a : frag.to;\n            this.inner = new StructureCursor(frag.tree, -frag.offset);\n        }\n    }\n    findMounts(pos, parser) {\n        var _a;\n        let result = [];\n        if (this.inner) {\n            this.inner.cursor.moveTo(pos, 1);\n            for (let pos = this.inner.cursor.node; pos; pos = pos.parent) {\n                let mount = (_a = pos.tree) === null || _a === void 0 ? void 0 : _a.prop(NodeProp.mounted);\n                if (mount && mount.parser == parser) {\n                    for (let i = this.fragI; i < this.fragments.length; i++) {\n                        let frag = this.fragments[i];\n                        if (frag.from >= pos.to)\n                            break;\n                        if (frag.tree == this.curFrag.tree)\n                            result.push({\n                                frag,\n                                pos: pos.from - frag.offset,\n                                mount\n                            });\n                    }\n                }\n            }\n        }\n        return result;\n    }\n}\nfunction punchRanges(outer, ranges) {\n    let copy = null, current = ranges;\n    for (let i = 1, j = 0; i < outer.length; i++) {\n        let gapFrom = outer[i - 1].to, gapTo = outer[i].from;\n        for (; j < current.length; j++) {\n            let r = current[j];\n            if (r.from >= gapTo)\n                break;\n            if (r.to <= gapFrom)\n                continue;\n            if (!copy)\n                current = copy = ranges.slice();\n            if (r.from < gapFrom) {\n                copy[j] = new Range(r.from, gapFrom);\n                if (r.to > gapTo)\n                    copy.splice(j + 1, 0, new Range(gapTo, r.to));\n            }\n            else if (r.to > gapTo) {\n                copy[j--] = new Range(gapTo, r.to);\n            }\n            else {\n                copy.splice(j--, 1);\n            }\n        }\n    }\n    return current;\n}\nfunction findCoverChanges(a, b, from, to) {\n    let iA = 0, iB = 0, inA = false, inB = false, pos = -1e9;\n    let result = [];\n    for (;;) {\n        let nextA = iA == a.length ? 1e9 : inA ? a[iA].to : a[iA].from;\n        let nextB = iB == b.length ? 1e9 : inB ? b[iB].to : b[iB].from;\n        if (inA != inB) {\n            let start = Math.max(pos, from), end = Math.min(nextA, nextB, to);\n            if (start < end)\n                result.push(new Range(start, end));\n        }\n        pos = Math.min(nextA, nextB);\n        if (pos == 1e9)\n            break;\n        if (nextA == pos) {\n            if (!inA)\n                inA = true;\n            else {\n                inA = false;\n                iA++;\n            }\n        }\n        if (nextB == pos) {\n            if (!inB)\n                inB = true;\n            else {\n                inB = false;\n                iB++;\n            }\n        }\n    }\n    return result;\n}\n// Given a number of fragments for the outer tree, and a set of ranges\n// to parse, find fragments for inner trees mounted around those\n// ranges, if any.\nfunction enterFragments(mounts, ranges) {\n    let result = [];\n    for (let { pos, mount, frag } of mounts) {\n        let startPos = pos + (mount.overlay ? mount.overlay[0].from : 0), endPos = startPos + mount.tree.length;\n        let from = Math.max(frag.from, startPos), to = Math.min(frag.to, endPos);\n        if (mount.overlay) {\n            let overlay = mount.overlay.map(r => new Range(r.from + pos, r.to + pos));\n            let changes = findCoverChanges(ranges, overlay, from, to);\n            for (let i = 0, pos = from;; i++) {\n                let last = i == changes.length, end = last ? to : changes[i].from;\n                if (end > pos)\n                    result.push(new TreeFragment(pos, end, mount.tree, -startPos, frag.from >= pos || frag.openStart, frag.to <= end || frag.openEnd));\n                if (last)\n                    break;\n                pos = changes[i].to;\n            }\n        }\n        else {\n            result.push(new TreeFragment(from, to, mount.tree, -startPos, frag.from >= startPos || frag.openStart, frag.to <= endPos || frag.openEnd));\n        }\n    }\n    return result;\n}\n\nexport { DefaultBufferLength, IterMode, MountedTree, NodeProp, NodeSet, NodeType, NodeWeakMap, Parser, Tree, TreeBuffer, TreeCursor, TreeFragment, parseMixed };\n", "import { <PERSON><PERSON><PERSON>, NodeProp, NodeSet, NodeType, <PERSON><PERSON>ult<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Tree, IterMode } from '@lezer/common';\n\n/**\nA parse stack. These are used internally by the parser to track\nparsing progress. They also provide some properties and methods\nthat external code such as a tokenizer can use to get information\nabout the parse state.\n*/\nclass Stack {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    The parse that this stack is part of @internal\n    */\n    p, \n    /**\n    Holds state, input pos, buffer index triplets for all but the\n    top state @internal\n    */\n    stack, \n    /**\n    The current parse state @internal\n    */\n    state, \n    // The position at which the next reduce should take place. This\n    // can be less than `this.pos` when skipped expressions have been\n    // added to the stack (which should be moved outside of the next\n    // reduction)\n    /**\n    @internal\n    */\n    reducePos, \n    /**\n    The input position up to which this stack has parsed.\n    */\n    pos, \n    /**\n    The dynamic score of the stack, including dynamic precedence\n    and error-recovery penalties\n    @internal\n    */\n    score, \n    // The output buffer. Holds (type, start, end, size) quads\n    // representing nodes created by the parser, where `size` is\n    // amount of buffer array entries covered by this node.\n    /**\n    @internal\n    */\n    buffer, \n    // The base offset of the buffer. When stacks are split, the split\n    // instance shared the buffer history with its parent up to\n    // `bufferBase`, which is the absolute offset (including the\n    // offset of previous splits) into the buffer at which this stack\n    // starts writing.\n    /**\n    @internal\n    */\n    bufferBase, \n    /**\n    @internal\n    */\n    curContext, \n    /**\n    @internal\n    */\n    lookAhead = 0, \n    // A parent stack from which this was split off, if any. This is\n    // set up so that it always points to a stack that has some\n    // additional buffer content, never to a stack with an equal\n    // `bufferBase`.\n    /**\n    @internal\n    */\n    parent) {\n        this.p = p;\n        this.stack = stack;\n        this.state = state;\n        this.reducePos = reducePos;\n        this.pos = pos;\n        this.score = score;\n        this.buffer = buffer;\n        this.bufferBase = bufferBase;\n        this.curContext = curContext;\n        this.lookAhead = lookAhead;\n        this.parent = parent;\n    }\n    /**\n    @internal\n    */\n    toString() {\n        return `[${this.stack.filter((_, i) => i % 3 == 0).concat(this.state)}]@${this.pos}${this.score ? \"!\" + this.score : \"\"}`;\n    }\n    // Start an empty stack\n    /**\n    @internal\n    */\n    static start(p, state, pos = 0) {\n        let cx = p.parser.context;\n        return new Stack(p, [], state, pos, pos, 0, [], 0, cx ? new StackContext(cx, cx.start) : null, 0, null);\n    }\n    /**\n    The stack's current [context](#lr.ContextTracker) value, if\n    any. Its type will depend on the context tracker's type\n    parameter, or it will be `null` if there is no context\n    tracker.\n    */\n    get context() { return this.curContext ? this.curContext.context : null; }\n    // Push a state onto the stack, tracking its start position as well\n    // as the buffer base at that point.\n    /**\n    @internal\n    */\n    pushState(state, start) {\n        this.stack.push(this.state, start, this.bufferBase + this.buffer.length);\n        this.state = state;\n    }\n    // Apply a reduce action\n    /**\n    @internal\n    */\n    reduce(action) {\n        var _a;\n        let depth = action >> 19 /* Action.ReduceDepthShift */, type = action & 65535 /* Action.ValueMask */;\n        let { parser } = this.p;\n        let lookaheadRecord = this.reducePos < this.pos - 25 /* Lookahead.Margin */;\n        if (lookaheadRecord)\n            this.setLookAhead(this.pos);\n        let dPrec = parser.dynamicPrecedence(type);\n        if (dPrec)\n            this.score += dPrec;\n        if (depth == 0) {\n            this.pushState(parser.getGoto(this.state, type, true), this.reducePos);\n            // Zero-depth reductions are a special case—they add stuff to\n            // the stack without popping anything off.\n            if (type < parser.minRepeatTerm)\n                this.storeNode(type, this.reducePos, this.reducePos, lookaheadRecord ? 8 : 4, true);\n            this.reduceContext(type, this.reducePos);\n            return;\n        }\n        // Find the base index into `this.stack`, content after which will\n        // be dropped. Note that with `StayFlag` reductions we need to\n        // consume two extra frames (the dummy parent node for the skipped\n        // expression and the state that we'll be staying in, which should\n        // be moved to `this.state`).\n        let base = this.stack.length - ((depth - 1) * 3) - (action & 262144 /* Action.StayFlag */ ? 6 : 0);\n        let start = base ? this.stack[base - 2] : this.p.ranges[0].from, size = this.reducePos - start;\n        // This is a kludge to try and detect overly deep left-associative\n        // trees, which will not increase the parse stack depth and thus\n        // won't be caught by the regular stack-depth limit check.\n        if (size >= 2000 /* Recover.MinBigReduction */ && !((_a = this.p.parser.nodeSet.types[type]) === null || _a === void 0 ? void 0 : _a.isAnonymous)) {\n            if (start == this.p.lastBigReductionStart) {\n                this.p.bigReductionCount++;\n                this.p.lastBigReductionSize = size;\n            }\n            else if (this.p.lastBigReductionSize < size) {\n                this.p.bigReductionCount = 1;\n                this.p.lastBigReductionStart = start;\n                this.p.lastBigReductionSize = size;\n            }\n        }\n        let bufferBase = base ? this.stack[base - 1] : 0, count = this.bufferBase + this.buffer.length - bufferBase;\n        // Store normal terms or `R -> R R` repeat reductions\n        if (type < parser.minRepeatTerm || (action & 131072 /* Action.RepeatFlag */)) {\n            let pos = parser.stateFlag(this.state, 1 /* StateFlag.Skipped */) ? this.pos : this.reducePos;\n            this.storeNode(type, start, pos, count + 4, true);\n        }\n        if (action & 262144 /* Action.StayFlag */) {\n            this.state = this.stack[base];\n        }\n        else {\n            let baseStateID = this.stack[base - 3];\n            this.state = parser.getGoto(baseStateID, type, true);\n        }\n        while (this.stack.length > base)\n            this.stack.pop();\n        this.reduceContext(type, start);\n    }\n    // Shift a value into the buffer\n    /**\n    @internal\n    */\n    storeNode(term, start, end, size = 4, mustSink = false) {\n        if (term == 0 /* Term.Err */ &&\n            (!this.stack.length || this.stack[this.stack.length - 1] < this.buffer.length + this.bufferBase)) {\n            // Try to omit/merge adjacent error nodes\n            let cur = this, top = this.buffer.length;\n            if (top == 0 && cur.parent) {\n                top = cur.bufferBase - cur.parent.bufferBase;\n                cur = cur.parent;\n            }\n            if (top > 0 && cur.buffer[top - 4] == 0 /* Term.Err */ && cur.buffer[top - 1] > -1) {\n                if (start == end)\n                    return;\n                if (cur.buffer[top - 2] >= start) {\n                    cur.buffer[top - 2] = end;\n                    return;\n                }\n            }\n        }\n        if (!mustSink || this.pos == end) { // Simple case, just append\n            this.buffer.push(term, start, end, size);\n        }\n        else { // There may be skipped nodes that have to be moved forward\n            let index = this.buffer.length;\n            if (index > 0 && this.buffer[index - 4] != 0 /* Term.Err */) {\n                let mustMove = false;\n                for (let scan = index; scan > 0 && this.buffer[scan - 2] > end; scan -= 4) {\n                    if (this.buffer[scan - 1] >= 0) {\n                        mustMove = true;\n                        break;\n                    }\n                }\n                if (mustMove)\n                    while (index > 0 && this.buffer[index - 2] > end) {\n                        // Move this record forward\n                        this.buffer[index] = this.buffer[index - 4];\n                        this.buffer[index + 1] = this.buffer[index - 3];\n                        this.buffer[index + 2] = this.buffer[index - 2];\n                        this.buffer[index + 3] = this.buffer[index - 1];\n                        index -= 4;\n                        if (size > 4)\n                            size -= 4;\n                    }\n            }\n            this.buffer[index] = term;\n            this.buffer[index + 1] = start;\n            this.buffer[index + 2] = end;\n            this.buffer[index + 3] = size;\n        }\n    }\n    // Apply a shift action\n    /**\n    @internal\n    */\n    shift(action, type, start, end) {\n        if (action & 131072 /* Action.GotoFlag */) {\n            this.pushState(action & 65535 /* Action.ValueMask */, this.pos);\n        }\n        else if ((action & 262144 /* Action.StayFlag */) == 0) { // Regular shift\n            let nextState = action, { parser } = this.p;\n            if (end > this.pos || type <= parser.maxNode) {\n                this.pos = end;\n                if (!parser.stateFlag(nextState, 1 /* StateFlag.Skipped */))\n                    this.reducePos = end;\n            }\n            this.pushState(nextState, start);\n            this.shiftContext(type, start);\n            if (type <= parser.maxNode)\n                this.buffer.push(type, start, end, 4);\n        }\n        else { // Shift-and-stay, which means this is a skipped token\n            this.pos = end;\n            this.shiftContext(type, start);\n            if (type <= this.p.parser.maxNode)\n                this.buffer.push(type, start, end, 4);\n        }\n    }\n    // Apply an action\n    /**\n    @internal\n    */\n    apply(action, next, nextStart, nextEnd) {\n        if (action & 65536 /* Action.ReduceFlag */)\n            this.reduce(action);\n        else\n            this.shift(action, next, nextStart, nextEnd);\n    }\n    // Add a prebuilt (reused) node into the buffer.\n    /**\n    @internal\n    */\n    useNode(value, next) {\n        let index = this.p.reused.length - 1;\n        if (index < 0 || this.p.reused[index] != value) {\n            this.p.reused.push(value);\n            index++;\n        }\n        let start = this.pos;\n        this.reducePos = this.pos = start + value.length;\n        this.pushState(next, start);\n        this.buffer.push(index, start, this.reducePos, -1 /* size == -1 means this is a reused value */);\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.reuse(this.curContext.context, value, this, this.p.stream.reset(this.pos - value.length)));\n    }\n    // Split the stack. Due to the buffer sharing and the fact\n    // that `this.stack` tends to stay quite shallow, this isn't very\n    // expensive.\n    /**\n    @internal\n    */\n    split() {\n        let parent = this;\n        let off = parent.buffer.length;\n        // Because the top of the buffer (after this.pos) may be mutated\n        // to reorder reductions and skipped tokens, and shared buffers\n        // should be immutable, this copies any outstanding skipped tokens\n        // to the new buffer, and puts the base pointer before them.\n        while (off > 0 && parent.buffer[off - 2] > parent.reducePos)\n            off -= 4;\n        let buffer = parent.buffer.slice(off), base = parent.bufferBase + off;\n        // Make sure parent points to an actual parent with content, if there is such a parent.\n        while (parent && base == parent.bufferBase)\n            parent = parent.parent;\n        return new Stack(this.p, this.stack.slice(), this.state, this.reducePos, this.pos, this.score, buffer, base, this.curContext, this.lookAhead, parent);\n    }\n    // Try to recover from an error by 'deleting' (ignoring) one token.\n    /**\n    @internal\n    */\n    recoverByDelete(next, nextEnd) {\n        let isNode = next <= this.p.parser.maxNode;\n        if (isNode)\n            this.storeNode(next, this.pos, nextEnd, 4);\n        this.storeNode(0 /* Term.Err */, this.pos, nextEnd, isNode ? 8 : 4);\n        this.pos = this.reducePos = nextEnd;\n        this.score -= 190 /* Recover.Delete */;\n    }\n    /**\n    Check if the given term would be able to be shifted (optionally\n    after some reductions) on this stack. This can be useful for\n    external tokenizers that want to make sure they only provide a\n    given token when it applies.\n    */\n    canShift(term) {\n        for (let sim = new SimulatedStack(this);;) {\n            let action = this.p.parser.stateSlot(sim.state, 4 /* ParseState.DefaultReduce */) || this.p.parser.hasAction(sim.state, term);\n            if (action == 0)\n                return false;\n            if ((action & 65536 /* Action.ReduceFlag */) == 0)\n                return true;\n            sim.reduce(action);\n        }\n    }\n    // Apply up to Recover.MaxNext recovery actions that conceptually\n    // inserts some missing token or rule.\n    /**\n    @internal\n    */\n    recoverByInsert(next) {\n        if (this.stack.length >= 300 /* Recover.MaxInsertStackDepth */)\n            return [];\n        let nextStates = this.p.parser.nextStates(this.state);\n        if (nextStates.length > 4 /* Recover.MaxNext */ << 1 || this.stack.length >= 120 /* Recover.DampenInsertStackDepth */) {\n            let best = [];\n            for (let i = 0, s; i < nextStates.length; i += 2) {\n                if ((s = nextStates[i + 1]) != this.state && this.p.parser.hasAction(s, next))\n                    best.push(nextStates[i], s);\n            }\n            if (this.stack.length < 120 /* Recover.DampenInsertStackDepth */)\n                for (let i = 0; best.length < 4 /* Recover.MaxNext */ << 1 && i < nextStates.length; i += 2) {\n                    let s = nextStates[i + 1];\n                    if (!best.some((v, i) => (i & 1) && v == s))\n                        best.push(nextStates[i], s);\n                }\n            nextStates = best;\n        }\n        let result = [];\n        for (let i = 0; i < nextStates.length && result.length < 4 /* Recover.MaxNext */; i += 2) {\n            let s = nextStates[i + 1];\n            if (s == this.state)\n                continue;\n            let stack = this.split();\n            stack.pushState(s, this.pos);\n            stack.storeNode(0 /* Term.Err */, stack.pos, stack.pos, 4, true);\n            stack.shiftContext(nextStates[i], this.pos);\n            stack.reducePos = this.pos;\n            stack.score -= 200 /* Recover.Insert */;\n            result.push(stack);\n        }\n        return result;\n    }\n    // Force a reduce, if possible. Return false if that can't\n    // be done.\n    /**\n    @internal\n    */\n    forceReduce() {\n        let { parser } = this.p;\n        let reduce = parser.stateSlot(this.state, 5 /* ParseState.ForcedReduce */);\n        if ((reduce & 65536 /* Action.ReduceFlag */) == 0)\n            return false;\n        if (!parser.validAction(this.state, reduce)) {\n            let depth = reduce >> 19 /* Action.ReduceDepthShift */, term = reduce & 65535 /* Action.ValueMask */;\n            let target = this.stack.length - depth * 3;\n            if (target < 0 || parser.getGoto(this.stack[target], term, false) < 0) {\n                let backup = this.findForcedReduction();\n                if (backup == null)\n                    return false;\n                reduce = backup;\n            }\n            this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n            this.score -= 100 /* Recover.Reduce */;\n        }\n        this.reducePos = this.pos;\n        this.reduce(reduce);\n        return true;\n    }\n    /**\n    Try to scan through the automaton to find some kind of reduction\n    that can be applied. Used when the regular ForcedReduce field\n    isn't a valid action. @internal\n    */\n    findForcedReduction() {\n        let { parser } = this.p, seen = [];\n        let explore = (state, depth) => {\n            if (seen.includes(state))\n                return;\n            seen.push(state);\n            return parser.allActions(state, (action) => {\n                if (action & (262144 /* Action.StayFlag */ | 131072 /* Action.GotoFlag */)) ;\n                else if (action & 65536 /* Action.ReduceFlag */) {\n                    let rDepth = (action >> 19 /* Action.ReduceDepthShift */) - depth;\n                    if (rDepth > 1) {\n                        let term = action & 65535 /* Action.ValueMask */, target = this.stack.length - rDepth * 3;\n                        if (target >= 0 && parser.getGoto(this.stack[target], term, false) >= 0)\n                            return (rDepth << 19 /* Action.ReduceDepthShift */) | 65536 /* Action.ReduceFlag */ | term;\n                    }\n                }\n                else {\n                    let found = explore(action, depth + 1);\n                    if (found != null)\n                        return found;\n                }\n            });\n        };\n        return explore(this.state, 0);\n    }\n    /**\n    @internal\n    */\n    forceAll() {\n        while (!this.p.parser.stateFlag(this.state, 2 /* StateFlag.Accepting */)) {\n            if (!this.forceReduce()) {\n                this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n                break;\n            }\n        }\n        return this;\n    }\n    /**\n    Check whether this state has no further actions (assumed to be a direct descendant of the\n    top state, since any other states must be able to continue\n    somehow). @internal\n    */\n    get deadEnd() {\n        if (this.stack.length != 3)\n            return false;\n        let { parser } = this.p;\n        return parser.data[parser.stateSlot(this.state, 1 /* ParseState.Actions */)] == 65535 /* Seq.End */ &&\n            !parser.stateSlot(this.state, 4 /* ParseState.DefaultReduce */);\n    }\n    /**\n    Restart the stack (put it back in its start state). Only safe\n    when this.stack.length == 3 (state is directly below the top\n    state). @internal\n    */\n    restart() {\n        this.storeNode(0 /* Term.Err */, this.pos, this.pos, 4, true);\n        this.state = this.stack[0];\n        this.stack.length = 0;\n    }\n    /**\n    @internal\n    */\n    sameState(other) {\n        if (this.state != other.state || this.stack.length != other.stack.length)\n            return false;\n        for (let i = 0; i < this.stack.length; i += 3)\n            if (this.stack[i] != other.stack[i])\n                return false;\n        return true;\n    }\n    /**\n    Get the parser used by this stack.\n    */\n    get parser() { return this.p.parser; }\n    /**\n    Test whether a given dialect (by numeric ID, as exported from\n    the terms file) is enabled.\n    */\n    dialectEnabled(dialectID) { return this.p.parser.dialect.flags[dialectID]; }\n    shiftContext(term, start) {\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.shift(this.curContext.context, term, this, this.p.stream.reset(start)));\n    }\n    reduceContext(term, start) {\n        if (this.curContext)\n            this.updateContext(this.curContext.tracker.reduce(this.curContext.context, term, this, this.p.stream.reset(start)));\n    }\n    /**\n    @internal\n    */\n    emitContext() {\n        let last = this.buffer.length - 1;\n        if (last < 0 || this.buffer[last] != -3)\n            this.buffer.push(this.curContext.hash, this.pos, this.pos, -3);\n    }\n    /**\n    @internal\n    */\n    emitLookAhead() {\n        let last = this.buffer.length - 1;\n        if (last < 0 || this.buffer[last] != -4)\n            this.buffer.push(this.lookAhead, this.pos, this.pos, -4);\n    }\n    updateContext(context) {\n        if (context != this.curContext.context) {\n            let newCx = new StackContext(this.curContext.tracker, context);\n            if (newCx.hash != this.curContext.hash)\n                this.emitContext();\n            this.curContext = newCx;\n        }\n    }\n    /**\n    @internal\n    */\n    setLookAhead(lookAhead) {\n        if (lookAhead > this.lookAhead) {\n            this.emitLookAhead();\n            this.lookAhead = lookAhead;\n        }\n    }\n    /**\n    @internal\n    */\n    close() {\n        if (this.curContext && this.curContext.tracker.strict)\n            this.emitContext();\n        if (this.lookAhead > 0)\n            this.emitLookAhead();\n    }\n}\nclass StackContext {\n    constructor(tracker, context) {\n        this.tracker = tracker;\n        this.context = context;\n        this.hash = tracker.strict ? tracker.hash(context) : 0;\n    }\n}\n// Used to cheaply run some reductions to scan ahead without mutating\n// an entire stack\nclass SimulatedStack {\n    constructor(start) {\n        this.start = start;\n        this.state = start.state;\n        this.stack = start.stack;\n        this.base = this.stack.length;\n    }\n    reduce(action) {\n        let term = action & 65535 /* Action.ValueMask */, depth = action >> 19 /* Action.ReduceDepthShift */;\n        if (depth == 0) {\n            if (this.stack == this.start.stack)\n                this.stack = this.stack.slice();\n            this.stack.push(this.state, 0, 0);\n            this.base += 3;\n        }\n        else {\n            this.base -= (depth - 1) * 3;\n        }\n        let goto = this.start.p.parser.getGoto(this.stack[this.base - 3], term, true);\n        this.state = goto;\n    }\n}\n// This is given to `Tree.build` to build a buffer, and encapsulates\n// the parent-stack-walking necessary to read the nodes.\nclass StackBufferCursor {\n    constructor(stack, pos, index) {\n        this.stack = stack;\n        this.pos = pos;\n        this.index = index;\n        this.buffer = stack.buffer;\n        if (this.index == 0)\n            this.maybeNext();\n    }\n    static create(stack, pos = stack.bufferBase + stack.buffer.length) {\n        return new StackBufferCursor(stack, pos, pos - stack.bufferBase);\n    }\n    maybeNext() {\n        let next = this.stack.parent;\n        if (next != null) {\n            this.index = this.stack.bufferBase - next.bufferBase;\n            this.stack = next;\n            this.buffer = next.buffer;\n        }\n    }\n    get id() { return this.buffer[this.index - 4]; }\n    get start() { return this.buffer[this.index - 3]; }\n    get end() { return this.buffer[this.index - 2]; }\n    get size() { return this.buffer[this.index - 1]; }\n    next() {\n        this.index -= 4;\n        this.pos -= 4;\n        if (this.index == 0)\n            this.maybeNext();\n    }\n    fork() {\n        return new StackBufferCursor(this.stack, this.pos, this.index);\n    }\n}\n\n// See lezer-generator/src/encode.ts for comments about the encoding\n// used here\nfunction decodeArray(input, Type = Uint16Array) {\n    if (typeof input != \"string\")\n        return input;\n    let array = null;\n    for (let pos = 0, out = 0; pos < input.length;) {\n        let value = 0;\n        for (;;) {\n            let next = input.charCodeAt(pos++), stop = false;\n            if (next == 126 /* Encode.BigValCode */) {\n                value = 65535 /* Encode.BigVal */;\n                break;\n            }\n            if (next >= 92 /* Encode.Gap2 */)\n                next--;\n            if (next >= 34 /* Encode.Gap1 */)\n                next--;\n            let digit = next - 32 /* Encode.Start */;\n            if (digit >= 46 /* Encode.Base */) {\n                digit -= 46 /* Encode.Base */;\n                stop = true;\n            }\n            value += digit;\n            if (stop)\n                break;\n            value *= 46 /* Encode.Base */;\n        }\n        if (array)\n            array[out++] = value;\n        else\n            array = new Type(value);\n    }\n    return array;\n}\n\nclass CachedToken {\n    constructor() {\n        this.start = -1;\n        this.value = -1;\n        this.end = -1;\n        this.extended = -1;\n        this.lookAhead = 0;\n        this.mask = 0;\n        this.context = 0;\n    }\n}\nconst nullToken = new CachedToken;\n/**\n[Tokenizers](#lr.ExternalTokenizer) interact with the input\nthrough this interface. It presents the input as a stream of\ncharacters, tracking lookahead and hiding the complexity of\n[ranges](#common.Parser.parse^ranges) from tokenizer code.\n*/\nclass InputStream {\n    /**\n    @internal\n    */\n    constructor(\n    /**\n    @internal\n    */\n    input, \n    /**\n    @internal\n    */\n    ranges) {\n        this.input = input;\n        this.ranges = ranges;\n        /**\n        @internal\n        */\n        this.chunk = \"\";\n        /**\n        @internal\n        */\n        this.chunkOff = 0;\n        /**\n        Backup chunk\n        */\n        this.chunk2 = \"\";\n        this.chunk2Pos = 0;\n        /**\n        The character code of the next code unit in the input, or -1\n        when the stream is at the end of the input.\n        */\n        this.next = -1;\n        /**\n        @internal\n        */\n        this.token = nullToken;\n        this.rangeIndex = 0;\n        this.pos = this.chunkPos = ranges[0].from;\n        this.range = ranges[0];\n        this.end = ranges[ranges.length - 1].to;\n        this.readNext();\n    }\n    /**\n    @internal\n    */\n    resolveOffset(offset, assoc) {\n        let range = this.range, index = this.rangeIndex;\n        let pos = this.pos + offset;\n        while (pos < range.from) {\n            if (!index)\n                return null;\n            let next = this.ranges[--index];\n            pos -= range.from - next.to;\n            range = next;\n        }\n        while (assoc < 0 ? pos > range.to : pos >= range.to) {\n            if (index == this.ranges.length - 1)\n                return null;\n            let next = this.ranges[++index];\n            pos += next.from - range.to;\n            range = next;\n        }\n        return pos;\n    }\n    /**\n    @internal\n    */\n    clipPos(pos) {\n        if (pos >= this.range.from && pos < this.range.to)\n            return pos;\n        for (let range of this.ranges)\n            if (range.to > pos)\n                return Math.max(pos, range.from);\n        return this.end;\n    }\n    /**\n    Look at a code unit near the stream position. `.peek(0)` equals\n    `.next`, `.peek(-1)` gives you the previous character, and so\n    on.\n    \n    Note that looking around during tokenizing creates dependencies\n    on potentially far-away content, which may reduce the\n    effectiveness incremental parsing—when looking forward—or even\n    cause invalid reparses when looking backward more than 25 code\n    units, since the library does not track lookbehind.\n    */\n    peek(offset) {\n        let idx = this.chunkOff + offset, pos, result;\n        if (idx >= 0 && idx < this.chunk.length) {\n            pos = this.pos + offset;\n            result = this.chunk.charCodeAt(idx);\n        }\n        else {\n            let resolved = this.resolveOffset(offset, 1);\n            if (resolved == null)\n                return -1;\n            pos = resolved;\n            if (pos >= this.chunk2Pos && pos < this.chunk2Pos + this.chunk2.length) {\n                result = this.chunk2.charCodeAt(pos - this.chunk2Pos);\n            }\n            else {\n                let i = this.rangeIndex, range = this.range;\n                while (range.to <= pos)\n                    range = this.ranges[++i];\n                this.chunk2 = this.input.chunk(this.chunk2Pos = pos);\n                if (pos + this.chunk2.length > range.to)\n                    this.chunk2 = this.chunk2.slice(0, range.to - pos);\n                result = this.chunk2.charCodeAt(0);\n            }\n        }\n        if (pos >= this.token.lookAhead)\n            this.token.lookAhead = pos + 1;\n        return result;\n    }\n    /**\n    Accept a token. By default, the end of the token is set to the\n    current stream position, but you can pass an offset (relative to\n    the stream position) to change that.\n    */\n    acceptToken(token, endOffset = 0) {\n        let end = endOffset ? this.resolveOffset(endOffset, -1) : this.pos;\n        if (end == null || end < this.token.start)\n            throw new RangeError(\"Token end out of bounds\");\n        this.token.value = token;\n        this.token.end = end;\n    }\n    /**\n    Accept a token ending at a specific given position.\n    */\n    acceptTokenTo(token, endPos) {\n        this.token.value = token;\n        this.token.end = endPos;\n    }\n    getChunk() {\n        if (this.pos >= this.chunk2Pos && this.pos < this.chunk2Pos + this.chunk2.length) {\n            let { chunk, chunkPos } = this;\n            this.chunk = this.chunk2;\n            this.chunkPos = this.chunk2Pos;\n            this.chunk2 = chunk;\n            this.chunk2Pos = chunkPos;\n            this.chunkOff = this.pos - this.chunkPos;\n        }\n        else {\n            this.chunk2 = this.chunk;\n            this.chunk2Pos = this.chunkPos;\n            let nextChunk = this.input.chunk(this.pos);\n            let end = this.pos + nextChunk.length;\n            this.chunk = end > this.range.to ? nextChunk.slice(0, this.range.to - this.pos) : nextChunk;\n            this.chunkPos = this.pos;\n            this.chunkOff = 0;\n        }\n    }\n    readNext() {\n        if (this.chunkOff >= this.chunk.length) {\n            this.getChunk();\n            if (this.chunkOff == this.chunk.length)\n                return this.next = -1;\n        }\n        return this.next = this.chunk.charCodeAt(this.chunkOff);\n    }\n    /**\n    Move the stream forward N (defaults to 1) code units. Returns\n    the new value of [`next`](#lr.InputStream.next).\n    */\n    advance(n = 1) {\n        this.chunkOff += n;\n        while (this.pos + n >= this.range.to) {\n            if (this.rangeIndex == this.ranges.length - 1)\n                return this.setDone();\n            n -= this.range.to - this.pos;\n            this.range = this.ranges[++this.rangeIndex];\n            this.pos = this.range.from;\n        }\n        this.pos += n;\n        if (this.pos >= this.token.lookAhead)\n            this.token.lookAhead = this.pos + 1;\n        return this.readNext();\n    }\n    setDone() {\n        this.pos = this.chunkPos = this.end;\n        this.range = this.ranges[this.rangeIndex = this.ranges.length - 1];\n        this.chunk = \"\";\n        return this.next = -1;\n    }\n    /**\n    @internal\n    */\n    reset(pos, token) {\n        if (token) {\n            this.token = token;\n            token.start = pos;\n            token.lookAhead = pos + 1;\n            token.value = token.extended = -1;\n        }\n        else {\n            this.token = nullToken;\n        }\n        if (this.pos != pos) {\n            this.pos = pos;\n            if (pos == this.end) {\n                this.setDone();\n                return this;\n            }\n            while (pos < this.range.from)\n                this.range = this.ranges[--this.rangeIndex];\n            while (pos >= this.range.to)\n                this.range = this.ranges[++this.rangeIndex];\n            if (pos >= this.chunkPos && pos < this.chunkPos + this.chunk.length) {\n                this.chunkOff = pos - this.chunkPos;\n            }\n            else {\n                this.chunk = \"\";\n                this.chunkOff = 0;\n            }\n            this.readNext();\n        }\n        return this;\n    }\n    /**\n    @internal\n    */\n    read(from, to) {\n        if (from >= this.chunkPos && to <= this.chunkPos + this.chunk.length)\n            return this.chunk.slice(from - this.chunkPos, to - this.chunkPos);\n        if (from >= this.chunk2Pos && to <= this.chunk2Pos + this.chunk2.length)\n            return this.chunk2.slice(from - this.chunk2Pos, to - this.chunk2Pos);\n        if (from >= this.range.from && to <= this.range.to)\n            return this.input.read(from, to);\n        let result = \"\";\n        for (let r of this.ranges) {\n            if (r.from >= to)\n                break;\n            if (r.to > from)\n                result += this.input.read(Math.max(r.from, from), Math.min(r.to, to));\n        }\n        return result;\n    }\n}\n/**\n@internal\n*/\nclass TokenGroup {\n    constructor(data, id) {\n        this.data = data;\n        this.id = id;\n    }\n    token(input, stack) {\n        let { parser } = stack.p;\n        readToken(this.data, input, stack, this.id, parser.data, parser.tokenPrecTable);\n    }\n}\nTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\n/**\n@hide\n*/\nclass LocalTokenGroup {\n    constructor(data, precTable, elseToken) {\n        this.precTable = precTable;\n        this.elseToken = elseToken;\n        this.data = typeof data == \"string\" ? decodeArray(data) : data;\n    }\n    token(input, stack) {\n        let start = input.pos, skipped = 0;\n        for (;;) {\n            let atEof = input.next < 0, nextPos = input.resolveOffset(1, 1);\n            readToken(this.data, input, stack, 0, this.data, this.precTable);\n            if (input.token.value > -1)\n                break;\n            if (this.elseToken == null)\n                return;\n            if (!atEof)\n                skipped++;\n            if (nextPos == null)\n                break;\n            input.reset(nextPos, input.token);\n        }\n        if (skipped) {\n            input.reset(start, input.token);\n            input.acceptToken(this.elseToken, skipped);\n        }\n    }\n}\nLocalTokenGroup.prototype.contextual = TokenGroup.prototype.fallback = TokenGroup.prototype.extend = false;\n/**\n`@external tokens` declarations in the grammar should resolve to\nan instance of this class.\n*/\nclass ExternalTokenizer {\n    /**\n    Create a tokenizer. The first argument is the function that,\n    given an input stream, scans for the types of tokens it\n    recognizes at the stream's position, and calls\n    [`acceptToken`](#lr.InputStream.acceptToken) when it finds\n    one.\n    */\n    constructor(\n    /**\n    @internal\n    */\n    token, options = {}) {\n        this.token = token;\n        this.contextual = !!options.contextual;\n        this.fallback = !!options.fallback;\n        this.extend = !!options.extend;\n    }\n}\n// Tokenizer data is stored a big uint16 array containing, for each\n// state:\n//\n//  - A group bitmask, indicating what token groups are reachable from\n//    this state, so that paths that can only lead to tokens not in\n//    any of the current groups can be cut off early.\n//\n//  - The position of the end of the state's sequence of accepting\n//    tokens\n//\n//  - The number of outgoing edges for the state\n//\n//  - The accepting tokens, as (token id, group mask) pairs\n//\n//  - The outgoing edges, as (start character, end character, state\n//    index) triples, with end character being exclusive\n//\n// This function interprets that data, running through a stream as\n// long as new states with the a matching group mask can be reached,\n// and updating `input.token` when it matches a token.\nfunction readToken(data, input, stack, group, precTable, precOffset) {\n    let state = 0, groupMask = 1 << group, { dialect } = stack.p.parser;\n    scan: for (;;) {\n        if ((groupMask & data[state]) == 0)\n            break;\n        let accEnd = data[state + 1];\n        // Check whether this state can lead to a token in the current group\n        // Accept tokens in this state, possibly overwriting\n        // lower-precedence / shorter tokens\n        for (let i = state + 3; i < accEnd; i += 2)\n            if ((data[i + 1] & groupMask) > 0) {\n                let term = data[i];\n                if (dialect.allows(term) &&\n                    (input.token.value == -1 || input.token.value == term ||\n                        overrides(term, input.token.value, precTable, precOffset))) {\n                    input.acceptToken(term);\n                    break;\n                }\n            }\n        let next = input.next, low = 0, high = data[state + 2];\n        // Special case for EOF\n        if (input.next < 0 && high > low && data[accEnd + high * 3 - 3] == 65535 /* Seq.End */) {\n            state = data[accEnd + high * 3 - 1];\n            continue scan;\n        }\n        // Do a binary search on the state's edges\n        for (; low < high;) {\n            let mid = (low + high) >> 1;\n            let index = accEnd + mid + (mid << 1);\n            let from = data[index], to = data[index + 1] || 0x10000;\n            if (next < from)\n                high = mid;\n            else if (next >= to)\n                low = mid + 1;\n            else {\n                state = data[index + 2];\n                input.advance();\n                continue scan;\n            }\n        }\n        break;\n    }\n}\nfunction findOffset(data, start, term) {\n    for (let i = start, next; (next = data[i]) != 65535 /* Seq.End */; i++)\n        if (next == term)\n            return i - start;\n    return -1;\n}\nfunction overrides(token, prev, tableData, tableOffset) {\n    let iPrev = findOffset(tableData, tableOffset, prev);\n    return iPrev < 0 || findOffset(tableData, tableOffset, token) < iPrev;\n}\n\n// Environment variable used to control console output\nconst verbose = typeof process != \"undefined\" && process.env && /\\bparse\\b/.test(process.env.LOG);\nlet stackIDs = null;\nfunction cutAt(tree, pos, side) {\n    let cursor = tree.cursor(IterMode.IncludeAnonymous);\n    cursor.moveTo(pos);\n    for (;;) {\n        if (!(side < 0 ? cursor.childBefore(pos) : cursor.childAfter(pos)))\n            for (;;) {\n                if ((side < 0 ? cursor.to < pos : cursor.from > pos) && !cursor.type.isError)\n                    return side < 0 ? Math.max(0, Math.min(cursor.to - 1, pos - 25 /* Lookahead.Margin */))\n                        : Math.min(tree.length, Math.max(cursor.from + 1, pos + 25 /* Lookahead.Margin */));\n                if (side < 0 ? cursor.prevSibling() : cursor.nextSibling())\n                    break;\n                if (!cursor.parent())\n                    return side < 0 ? 0 : tree.length;\n            }\n    }\n}\nclass FragmentCursor {\n    constructor(fragments, nodeSet) {\n        this.fragments = fragments;\n        this.nodeSet = nodeSet;\n        this.i = 0;\n        this.fragment = null;\n        this.safeFrom = -1;\n        this.safeTo = -1;\n        this.trees = [];\n        this.start = [];\n        this.index = [];\n        this.nextFragment();\n    }\n    nextFragment() {\n        let fr = this.fragment = this.i == this.fragments.length ? null : this.fragments[this.i++];\n        if (fr) {\n            this.safeFrom = fr.openStart ? cutAt(fr.tree, fr.from + fr.offset, 1) - fr.offset : fr.from;\n            this.safeTo = fr.openEnd ? cutAt(fr.tree, fr.to + fr.offset, -1) - fr.offset : fr.to;\n            while (this.trees.length) {\n                this.trees.pop();\n                this.start.pop();\n                this.index.pop();\n            }\n            this.trees.push(fr.tree);\n            this.start.push(-fr.offset);\n            this.index.push(0);\n            this.nextStart = this.safeFrom;\n        }\n        else {\n            this.nextStart = 1e9;\n        }\n    }\n    // `pos` must be >= any previously given `pos` for this cursor\n    nodeAt(pos) {\n        if (pos < this.nextStart)\n            return null;\n        while (this.fragment && this.safeTo <= pos)\n            this.nextFragment();\n        if (!this.fragment)\n            return null;\n        for (;;) {\n            let last = this.trees.length - 1;\n            if (last < 0) { // End of tree\n                this.nextFragment();\n                return null;\n            }\n            let top = this.trees[last], index = this.index[last];\n            if (index == top.children.length) {\n                this.trees.pop();\n                this.start.pop();\n                this.index.pop();\n                continue;\n            }\n            let next = top.children[index];\n            let start = this.start[last] + top.positions[index];\n            if (start > pos) {\n                this.nextStart = start;\n                return null;\n            }\n            if (next instanceof Tree) {\n                if (start == pos) {\n                    if (start < this.safeFrom)\n                        return null;\n                    let end = start + next.length;\n                    if (end <= this.safeTo) {\n                        let lookAhead = next.prop(NodeProp.lookAhead);\n                        if (!lookAhead || end + lookAhead < this.fragment.to)\n                            return next;\n                    }\n                }\n                this.index[last]++;\n                if (start + next.length >= Math.max(this.safeFrom, pos)) { // Enter this node\n                    this.trees.push(next);\n                    this.start.push(start);\n                    this.index.push(0);\n                }\n            }\n            else {\n                this.index[last]++;\n                this.nextStart = start + next.length;\n            }\n        }\n    }\n}\nclass TokenCache {\n    constructor(parser, stream) {\n        this.stream = stream;\n        this.tokens = [];\n        this.mainToken = null;\n        this.actions = [];\n        this.tokens = parser.tokenizers.map(_ => new CachedToken);\n    }\n    getActions(stack) {\n        let actionIndex = 0;\n        let main = null;\n        let { parser } = stack.p, { tokenizers } = parser;\n        let mask = parser.stateSlot(stack.state, 3 /* ParseState.TokenizerMask */);\n        let context = stack.curContext ? stack.curContext.hash : 0;\n        let lookAhead = 0;\n        for (let i = 0; i < tokenizers.length; i++) {\n            if (((1 << i) & mask) == 0)\n                continue;\n            let tokenizer = tokenizers[i], token = this.tokens[i];\n            if (main && !tokenizer.fallback)\n                continue;\n            if (tokenizer.contextual || token.start != stack.pos || token.mask != mask || token.context != context) {\n                this.updateCachedToken(token, tokenizer, stack);\n                token.mask = mask;\n                token.context = context;\n            }\n            if (token.lookAhead > token.end + 25 /* Lookahead.Margin */)\n                lookAhead = Math.max(token.lookAhead, lookAhead);\n            if (token.value != 0 /* Term.Err */) {\n                let startIndex = actionIndex;\n                if (token.extended > -1)\n                    actionIndex = this.addActions(stack, token.extended, token.end, actionIndex);\n                actionIndex = this.addActions(stack, token.value, token.end, actionIndex);\n                if (!tokenizer.extend) {\n                    main = token;\n                    if (actionIndex > startIndex)\n                        break;\n                }\n            }\n        }\n        while (this.actions.length > actionIndex)\n            this.actions.pop();\n        if (lookAhead)\n            stack.setLookAhead(lookAhead);\n        if (!main && stack.pos == this.stream.end) {\n            main = new CachedToken;\n            main.value = stack.p.parser.eofTerm;\n            main.start = main.end = stack.pos;\n            actionIndex = this.addActions(stack, main.value, main.end, actionIndex);\n        }\n        this.mainToken = main;\n        return this.actions;\n    }\n    getMainToken(stack) {\n        if (this.mainToken)\n            return this.mainToken;\n        let main = new CachedToken, { pos, p } = stack;\n        main.start = pos;\n        main.end = Math.min(pos + 1, p.stream.end);\n        main.value = pos == p.stream.end ? p.parser.eofTerm : 0 /* Term.Err */;\n        return main;\n    }\n    updateCachedToken(token, tokenizer, stack) {\n        let start = this.stream.clipPos(stack.pos);\n        tokenizer.token(this.stream.reset(start, token), stack);\n        if (token.value > -1) {\n            let { parser } = stack.p;\n            for (let i = 0; i < parser.specialized.length; i++)\n                if (parser.specialized[i] == token.value) {\n                    let result = parser.specializers[i](this.stream.read(token.start, token.end), stack);\n                    if (result >= 0 && stack.p.parser.dialect.allows(result >> 1)) {\n                        if ((result & 1) == 0 /* Specialize.Specialize */)\n                            token.value = result >> 1;\n                        else\n                            token.extended = result >> 1;\n                        break;\n                    }\n                }\n        }\n        else {\n            token.value = 0 /* Term.Err */;\n            token.end = this.stream.clipPos(start + 1);\n        }\n    }\n    putAction(action, token, end, index) {\n        // Don't add duplicate actions\n        for (let i = 0; i < index; i += 3)\n            if (this.actions[i] == action)\n                return index;\n        this.actions[index++] = action;\n        this.actions[index++] = token;\n        this.actions[index++] = end;\n        return index;\n    }\n    addActions(stack, token, end, index) {\n        let { state } = stack, { parser } = stack.p, { data } = parser;\n        for (let set = 0; set < 2; set++) {\n            for (let i = parser.stateSlot(state, set ? 2 /* ParseState.Skip */ : 1 /* ParseState.Actions */);; i += 3) {\n                if (data[i] == 65535 /* Seq.End */) {\n                    if (data[i + 1] == 1 /* Seq.Next */) {\n                        i = pair(data, i + 2);\n                    }\n                    else {\n                        if (index == 0 && data[i + 1] == 2 /* Seq.Other */)\n                            index = this.putAction(pair(data, i + 2), token, end, index);\n                        break;\n                    }\n                }\n                if (data[i] == token)\n                    index = this.putAction(pair(data, i + 1), token, end, index);\n            }\n        }\n        return index;\n    }\n}\nclass Parse {\n    constructor(parser, input, fragments, ranges) {\n        this.parser = parser;\n        this.input = input;\n        this.ranges = ranges;\n        this.recovering = 0;\n        this.nextStackID = 0x2654; // ♔, ♕, ♖, ♗, ♘, ♙, ♠, ♡, ♢, ♣, ♤, ♥, ♦, ♧\n        this.minStackPos = 0;\n        this.reused = [];\n        this.stoppedAt = null;\n        this.lastBigReductionStart = -1;\n        this.lastBigReductionSize = 0;\n        this.bigReductionCount = 0;\n        this.stream = new InputStream(input, ranges);\n        this.tokens = new TokenCache(parser, this.stream);\n        this.topTerm = parser.top[1];\n        let { from } = ranges[0];\n        this.stacks = [Stack.start(this, parser.top[0], from)];\n        this.fragments = fragments.length && this.stream.end - from > parser.bufferLength * 4\n            ? new FragmentCursor(fragments, parser.nodeSet) : null;\n    }\n    get parsedPos() {\n        return this.minStackPos;\n    }\n    // Move the parser forward. This will process all parse stacks at\n    // `this.pos` and try to advance them to a further position. If no\n    // stack for such a position is found, it'll start error-recovery.\n    //\n    // When the parse is finished, this will return a syntax tree. When\n    // not, it returns `null`.\n    advance() {\n        let stacks = this.stacks, pos = this.minStackPos;\n        // This will hold stacks beyond `pos`.\n        let newStacks = this.stacks = [];\n        let stopped, stoppedTokens;\n        // If a large amount of reductions happened with the same start\n        // position, force the stack out of that production in order to\n        // avoid creating a tree too deep to recurse through.\n        // (This is an ugly kludge, because unfortunately there is no\n        // straightforward, cheap way to check for this happening, due to\n        // the history of reductions only being available in an\n        // expensive-to-access format in the stack buffers.)\n        if (this.bigReductionCount > 300 /* Rec.MaxLeftAssociativeReductionCount */ && stacks.length == 1) {\n            let [s] = stacks;\n            while (s.forceReduce() && s.stack.length && s.stack[s.stack.length - 2] >= this.lastBigReductionStart) { }\n            this.bigReductionCount = this.lastBigReductionSize = 0;\n        }\n        // Keep advancing any stacks at `pos` until they either move\n        // forward or can't be advanced. Gather stacks that can't be\n        // advanced further in `stopped`.\n        for (let i = 0; i < stacks.length; i++) {\n            let stack = stacks[i];\n            for (;;) {\n                this.tokens.mainToken = null;\n                if (stack.pos > pos) {\n                    newStacks.push(stack);\n                }\n                else if (this.advanceStack(stack, newStacks, stacks)) {\n                    continue;\n                }\n                else {\n                    if (!stopped) {\n                        stopped = [];\n                        stoppedTokens = [];\n                    }\n                    stopped.push(stack);\n                    let tok = this.tokens.getMainToken(stack);\n                    stoppedTokens.push(tok.value, tok.end);\n                }\n                break;\n            }\n        }\n        if (!newStacks.length) {\n            let finished = stopped && findFinished(stopped);\n            if (finished) {\n                if (verbose)\n                    console.log(\"Finish with \" + this.stackID(finished));\n                return this.stackToTree(finished);\n            }\n            if (this.parser.strict) {\n                if (verbose && stopped)\n                    console.log(\"Stuck with token \" + (this.tokens.mainToken ? this.parser.getName(this.tokens.mainToken.value) : \"none\"));\n                throw new SyntaxError(\"No parse at \" + pos);\n            }\n            if (!this.recovering)\n                this.recovering = 5 /* Rec.Distance */;\n        }\n        if (this.recovering && stopped) {\n            let finished = this.stoppedAt != null && stopped[0].pos > this.stoppedAt ? stopped[0]\n                : this.runRecovery(stopped, stoppedTokens, newStacks);\n            if (finished) {\n                if (verbose)\n                    console.log(\"Force-finish \" + this.stackID(finished));\n                return this.stackToTree(finished.forceAll());\n            }\n        }\n        if (this.recovering) {\n            let maxRemaining = this.recovering == 1 ? 1 : this.recovering * 3 /* Rec.MaxRemainingPerStep */;\n            if (newStacks.length > maxRemaining) {\n                newStacks.sort((a, b) => b.score - a.score);\n                while (newStacks.length > maxRemaining)\n                    newStacks.pop();\n            }\n            if (newStacks.some(s => s.reducePos > pos))\n                this.recovering--;\n        }\n        else if (newStacks.length > 1) {\n            // Prune stacks that are in the same state, or that have been\n            // running without splitting for a while, to avoid getting stuck\n            // with multiple successful stacks running endlessly on.\n            outer: for (let i = 0; i < newStacks.length - 1; i++) {\n                let stack = newStacks[i];\n                for (let j = i + 1; j < newStacks.length; j++) {\n                    let other = newStacks[j];\n                    if (stack.sameState(other) ||\n                        stack.buffer.length > 500 /* Rec.MinBufferLengthPrune */ && other.buffer.length > 500 /* Rec.MinBufferLengthPrune */) {\n                        if (((stack.score - other.score) || (stack.buffer.length - other.buffer.length)) > 0) {\n                            newStacks.splice(j--, 1);\n                        }\n                        else {\n                            newStacks.splice(i--, 1);\n                            continue outer;\n                        }\n                    }\n                }\n            }\n            if (newStacks.length > 12 /* Rec.MaxStackCount */)\n                newStacks.splice(12 /* Rec.MaxStackCount */, newStacks.length - 12 /* Rec.MaxStackCount */);\n        }\n        this.minStackPos = newStacks[0].pos;\n        for (let i = 1; i < newStacks.length; i++)\n            if (newStacks[i].pos < this.minStackPos)\n                this.minStackPos = newStacks[i].pos;\n        return null;\n    }\n    stopAt(pos) {\n        if (this.stoppedAt != null && this.stoppedAt < pos)\n            throw new RangeError(\"Can't move stoppedAt forward\");\n        this.stoppedAt = pos;\n    }\n    // Returns an updated version of the given stack, or null if the\n    // stack can't advance normally. When `split` and `stacks` are\n    // given, stacks split off by ambiguous operations will be pushed to\n    // `split`, or added to `stacks` if they move `pos` forward.\n    advanceStack(stack, stacks, split) {\n        let start = stack.pos, { parser } = this;\n        let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n        if (this.stoppedAt != null && start > this.stoppedAt)\n            return stack.forceReduce() ? stack : null;\n        if (this.fragments) {\n            let strictCx = stack.curContext && stack.curContext.tracker.strict, cxHash = strictCx ? stack.curContext.hash : 0;\n            for (let cached = this.fragments.nodeAt(start); cached;) {\n                let match = this.parser.nodeSet.types[cached.type.id] == cached.type ? parser.getGoto(stack.state, cached.type.id) : -1;\n                if (match > -1 && cached.length && (!strictCx || (cached.prop(NodeProp.contextHash) || 0) == cxHash)) {\n                    stack.useNode(cached, match);\n                    if (verbose)\n                        console.log(base + this.stackID(stack) + ` (via reuse of ${parser.getName(cached.type.id)})`);\n                    return true;\n                }\n                if (!(cached instanceof Tree) || cached.children.length == 0 || cached.positions[0] > 0)\n                    break;\n                let inner = cached.children[0];\n                if (inner instanceof Tree && cached.positions[0] == 0)\n                    cached = inner;\n                else\n                    break;\n            }\n        }\n        let defaultReduce = parser.stateSlot(stack.state, 4 /* ParseState.DefaultReduce */);\n        if (defaultReduce > 0) {\n            stack.reduce(defaultReduce);\n            if (verbose)\n                console.log(base + this.stackID(stack) + ` (via always-reduce ${parser.getName(defaultReduce & 65535 /* Action.ValueMask */)})`);\n            return true;\n        }\n        if (stack.stack.length >= 8400 /* Rec.CutDepth */) {\n            while (stack.stack.length > 6000 /* Rec.CutTo */ && stack.forceReduce()) { }\n        }\n        let actions = this.tokens.getActions(stack);\n        for (let i = 0; i < actions.length;) {\n            let action = actions[i++], term = actions[i++], end = actions[i++];\n            let last = i == actions.length || !split;\n            let localStack = last ? stack : stack.split();\n            let main = this.tokens.mainToken;\n            localStack.apply(action, term, main ? main.start : localStack.pos, end);\n            if (verbose)\n                console.log(base + this.stackID(localStack) + ` (via ${(action & 65536 /* Action.ReduceFlag */) == 0 ? \"shift\"\n                    : `reduce of ${parser.getName(action & 65535 /* Action.ValueMask */)}`} for ${parser.getName(term)} @ ${start}${localStack == stack ? \"\" : \", split\"})`);\n            if (last)\n                return true;\n            else if (localStack.pos > start)\n                stacks.push(localStack);\n            else\n                split.push(localStack);\n        }\n        return false;\n    }\n    // Advance a given stack forward as far as it will go. Returns the\n    // (possibly updated) stack if it got stuck, or null if it moved\n    // forward and was given to `pushStackDedup`.\n    advanceFully(stack, newStacks) {\n        let pos = stack.pos;\n        for (;;) {\n            if (!this.advanceStack(stack, null, null))\n                return false;\n            if (stack.pos > pos) {\n                pushStackDedup(stack, newStacks);\n                return true;\n            }\n        }\n    }\n    runRecovery(stacks, tokens, newStacks) {\n        let finished = null, restarted = false;\n        for (let i = 0; i < stacks.length; i++) {\n            let stack = stacks[i], token = tokens[i << 1], tokenEnd = tokens[(i << 1) + 1];\n            let base = verbose ? this.stackID(stack) + \" -> \" : \"\";\n            if (stack.deadEnd) {\n                if (restarted)\n                    continue;\n                restarted = true;\n                stack.restart();\n                if (verbose)\n                    console.log(base + this.stackID(stack) + \" (restarted)\");\n                let done = this.advanceFully(stack, newStacks);\n                if (done)\n                    continue;\n            }\n            let force = stack.split(), forceBase = base;\n            for (let j = 0; force.forceReduce() && j < 10 /* Rec.ForceReduceLimit */; j++) {\n                if (verbose)\n                    console.log(forceBase + this.stackID(force) + \" (via force-reduce)\");\n                let done = this.advanceFully(force, newStacks);\n                if (done)\n                    break;\n                if (verbose)\n                    forceBase = this.stackID(force) + \" -> \";\n            }\n            for (let insert of stack.recoverByInsert(token)) {\n                if (verbose)\n                    console.log(base + this.stackID(insert) + \" (via recover-insert)\");\n                this.advanceFully(insert, newStacks);\n            }\n            if (this.stream.end > stack.pos) {\n                if (tokenEnd == stack.pos) {\n                    tokenEnd++;\n                    token = 0 /* Term.Err */;\n                }\n                stack.recoverByDelete(token, tokenEnd);\n                if (verbose)\n                    console.log(base + this.stackID(stack) + ` (via recover-delete ${this.parser.getName(token)})`);\n                pushStackDedup(stack, newStacks);\n            }\n            else if (!finished || finished.score < stack.score) {\n                finished = stack;\n            }\n        }\n        return finished;\n    }\n    // Convert the stack's buffer to a syntax tree.\n    stackToTree(stack) {\n        stack.close();\n        return Tree.build({ buffer: StackBufferCursor.create(stack),\n            nodeSet: this.parser.nodeSet,\n            topID: this.topTerm,\n            maxBufferLength: this.parser.bufferLength,\n            reused: this.reused,\n            start: this.ranges[0].from,\n            length: stack.pos - this.ranges[0].from,\n            minRepeatType: this.parser.minRepeatTerm });\n    }\n    stackID(stack) {\n        let id = (stackIDs || (stackIDs = new WeakMap)).get(stack);\n        if (!id)\n            stackIDs.set(stack, id = String.fromCodePoint(this.nextStackID++));\n        return id + stack;\n    }\n}\nfunction pushStackDedup(stack, newStacks) {\n    for (let i = 0; i < newStacks.length; i++) {\n        let other = newStacks[i];\n        if (other.pos == stack.pos && other.sameState(stack)) {\n            if (newStacks[i].score < stack.score)\n                newStacks[i] = stack;\n            return;\n        }\n    }\n    newStacks.push(stack);\n}\nclass Dialect {\n    constructor(source, flags, disabled) {\n        this.source = source;\n        this.flags = flags;\n        this.disabled = disabled;\n    }\n    allows(term) { return !this.disabled || this.disabled[term] == 0; }\n}\nconst id = x => x;\n/**\nContext trackers are used to track stateful context (such as\nindentation in the Python grammar, or parent elements in the XML\ngrammar) needed by external tokenizers. You declare them in a\ngrammar file as `@context exportName from \"module\"`.\n\nContext values should be immutable, and can be updated (replaced)\non shift or reduce actions.\n\nThe export used in a `@context` declaration should be of this\ntype.\n*/\nclass ContextTracker {\n    /**\n    Define a context tracker.\n    */\n    constructor(spec) {\n        this.start = spec.start;\n        this.shift = spec.shift || id;\n        this.reduce = spec.reduce || id;\n        this.reuse = spec.reuse || id;\n        this.hash = spec.hash || (() => 0);\n        this.strict = spec.strict !== false;\n    }\n}\n/**\nHolds the parse tables for a given grammar, as generated by\n`lezer-generator`, and provides [methods](#common.Parser) to parse\ncontent with.\n*/\nclass LRParser extends Parser {\n    /**\n    @internal\n    */\n    constructor(spec) {\n        super();\n        /**\n        @internal\n        */\n        this.wrappers = [];\n        if (spec.version != 14 /* File.Version */)\n            throw new RangeError(`Parser version (${spec.version}) doesn't match runtime version (${14 /* File.Version */})`);\n        let nodeNames = spec.nodeNames.split(\" \");\n        this.minRepeatTerm = nodeNames.length;\n        for (let i = 0; i < spec.repeatNodeCount; i++)\n            nodeNames.push(\"\");\n        let topTerms = Object.keys(spec.topRules).map(r => spec.topRules[r][1]);\n        let nodeProps = [];\n        for (let i = 0; i < nodeNames.length; i++)\n            nodeProps.push([]);\n        function setProp(nodeID, prop, value) {\n            nodeProps[nodeID].push([prop, prop.deserialize(String(value))]);\n        }\n        if (spec.nodeProps)\n            for (let propSpec of spec.nodeProps) {\n                let prop = propSpec[0];\n                if (typeof prop == \"string\")\n                    prop = NodeProp[prop];\n                for (let i = 1; i < propSpec.length;) {\n                    let next = propSpec[i++];\n                    if (next >= 0) {\n                        setProp(next, prop, propSpec[i++]);\n                    }\n                    else {\n                        let value = propSpec[i + -next];\n                        for (let j = -next; j > 0; j--)\n                            setProp(propSpec[i++], prop, value);\n                        i++;\n                    }\n                }\n            }\n        this.nodeSet = new NodeSet(nodeNames.map((name, i) => NodeType.define({\n            name: i >= this.minRepeatTerm ? undefined : name,\n            id: i,\n            props: nodeProps[i],\n            top: topTerms.indexOf(i) > -1,\n            error: i == 0,\n            skipped: spec.skippedNodes && spec.skippedNodes.indexOf(i) > -1\n        })));\n        if (spec.propSources)\n            this.nodeSet = this.nodeSet.extend(...spec.propSources);\n        this.strict = false;\n        this.bufferLength = DefaultBufferLength;\n        let tokenArray = decodeArray(spec.tokenData);\n        this.context = spec.context;\n        this.specializerSpecs = spec.specialized || [];\n        this.specialized = new Uint16Array(this.specializerSpecs.length);\n        for (let i = 0; i < this.specializerSpecs.length; i++)\n            this.specialized[i] = this.specializerSpecs[i].term;\n        this.specializers = this.specializerSpecs.map(getSpecializer);\n        this.states = decodeArray(spec.states, Uint32Array);\n        this.data = decodeArray(spec.stateData);\n        this.goto = decodeArray(spec.goto);\n        this.maxTerm = spec.maxTerm;\n        this.tokenizers = spec.tokenizers.map(value => typeof value == \"number\" ? new TokenGroup(tokenArray, value) : value);\n        this.topRules = spec.topRules;\n        this.dialects = spec.dialects || {};\n        this.dynamicPrecedences = spec.dynamicPrecedences || null;\n        this.tokenPrecTable = spec.tokenPrec;\n        this.termNames = spec.termNames || null;\n        this.maxNode = this.nodeSet.types.length - 1;\n        this.dialect = this.parseDialect();\n        this.top = this.topRules[Object.keys(this.topRules)[0]];\n    }\n    createParse(input, fragments, ranges) {\n        let parse = new Parse(this, input, fragments, ranges);\n        for (let w of this.wrappers)\n            parse = w(parse, input, fragments, ranges);\n        return parse;\n    }\n    /**\n    Get a goto table entry @internal\n    */\n    getGoto(state, term, loose = false) {\n        let table = this.goto;\n        if (term >= table[0])\n            return -1;\n        for (let pos = table[term + 1];;) {\n            let groupTag = table[pos++], last = groupTag & 1;\n            let target = table[pos++];\n            if (last && loose)\n                return target;\n            for (let end = pos + (groupTag >> 1); pos < end; pos++)\n                if (table[pos] == state)\n                    return target;\n            if (last)\n                return -1;\n        }\n    }\n    /**\n    Check if this state has an action for a given terminal @internal\n    */\n    hasAction(state, terminal) {\n        let data = this.data;\n        for (let set = 0; set < 2; set++) {\n            for (let i = this.stateSlot(state, set ? 2 /* ParseState.Skip */ : 1 /* ParseState.Actions */), next;; i += 3) {\n                if ((next = data[i]) == 65535 /* Seq.End */) {\n                    if (data[i + 1] == 1 /* Seq.Next */)\n                        next = data[i = pair(data, i + 2)];\n                    else if (data[i + 1] == 2 /* Seq.Other */)\n                        return pair(data, i + 2);\n                    else\n                        break;\n                }\n                if (next == terminal || next == 0 /* Term.Err */)\n                    return pair(data, i + 1);\n            }\n        }\n        return 0;\n    }\n    /**\n    @internal\n    */\n    stateSlot(state, slot) {\n        return this.states[(state * 6 /* ParseState.Size */) + slot];\n    }\n    /**\n    @internal\n    */\n    stateFlag(state, flag) {\n        return (this.stateSlot(state, 0 /* ParseState.Flags */) & flag) > 0;\n    }\n    /**\n    @internal\n    */\n    validAction(state, action) {\n        return !!this.allActions(state, a => a == action ? true : null);\n    }\n    /**\n    @internal\n    */\n    allActions(state, action) {\n        let deflt = this.stateSlot(state, 4 /* ParseState.DefaultReduce */);\n        let result = deflt ? action(deflt) : undefined;\n        for (let i = this.stateSlot(state, 1 /* ParseState.Actions */); result == null; i += 3) {\n            if (this.data[i] == 65535 /* Seq.End */) {\n                if (this.data[i + 1] == 1 /* Seq.Next */)\n                    i = pair(this.data, i + 2);\n                else\n                    break;\n            }\n            result = action(pair(this.data, i + 1));\n        }\n        return result;\n    }\n    /**\n    Get the states that can follow this one through shift actions or\n    goto jumps. @internal\n    */\n    nextStates(state) {\n        let result = [];\n        for (let i = this.stateSlot(state, 1 /* ParseState.Actions */);; i += 3) {\n            if (this.data[i] == 65535 /* Seq.End */) {\n                if (this.data[i + 1] == 1 /* Seq.Next */)\n                    i = pair(this.data, i + 2);\n                else\n                    break;\n            }\n            if ((this.data[i + 2] & (65536 /* Action.ReduceFlag */ >> 16)) == 0) {\n                let value = this.data[i + 1];\n                if (!result.some((v, i) => (i & 1) && v == value))\n                    result.push(this.data[i], value);\n            }\n        }\n        return result;\n    }\n    /**\n    Configure the parser. Returns a new parser instance that has the\n    given settings modified. Settings not provided in `config` are\n    kept from the original parser.\n    */\n    configure(config) {\n        // Hideous reflection-based kludge to make it easy to create a\n        // slightly modified copy of a parser.\n        let copy = Object.assign(Object.create(LRParser.prototype), this);\n        if (config.props)\n            copy.nodeSet = this.nodeSet.extend(...config.props);\n        if (config.top) {\n            let info = this.topRules[config.top];\n            if (!info)\n                throw new RangeError(`Invalid top rule name ${config.top}`);\n            copy.top = info;\n        }\n        if (config.tokenizers)\n            copy.tokenizers = this.tokenizers.map(t => {\n                let found = config.tokenizers.find(r => r.from == t);\n                return found ? found.to : t;\n            });\n        if (config.specializers) {\n            copy.specializers = this.specializers.slice();\n            copy.specializerSpecs = this.specializerSpecs.map((s, i) => {\n                let found = config.specializers.find(r => r.from == s.external);\n                if (!found)\n                    return s;\n                let spec = Object.assign(Object.assign({}, s), { external: found.to });\n                copy.specializers[i] = getSpecializer(spec);\n                return spec;\n            });\n        }\n        if (config.contextTracker)\n            copy.context = config.contextTracker;\n        if (config.dialect)\n            copy.dialect = this.parseDialect(config.dialect);\n        if (config.strict != null)\n            copy.strict = config.strict;\n        if (config.wrap)\n            copy.wrappers = copy.wrappers.concat(config.wrap);\n        if (config.bufferLength != null)\n            copy.bufferLength = config.bufferLength;\n        return copy;\n    }\n    /**\n    Tells you whether any [parse wrappers](#lr.ParserConfig.wrap)\n    are registered for this parser.\n    */\n    hasWrappers() {\n        return this.wrappers.length > 0;\n    }\n    /**\n    Returns the name associated with a given term. This will only\n    work for all terms when the parser was generated with the\n    `--names` option. By default, only the names of tagged terms are\n    stored.\n    */\n    getName(term) {\n        return this.termNames ? this.termNames[term] : String(term <= this.maxNode && this.nodeSet.types[term].name || term);\n    }\n    /**\n    The eof term id is always allocated directly after the node\n    types. @internal\n    */\n    get eofTerm() { return this.maxNode + 1; }\n    /**\n    The type of top node produced by the parser.\n    */\n    get topNode() { return this.nodeSet.types[this.top[1]]; }\n    /**\n    @internal\n    */\n    dynamicPrecedence(term) {\n        let prec = this.dynamicPrecedences;\n        return prec == null ? 0 : prec[term] || 0;\n    }\n    /**\n    @internal\n    */\n    parseDialect(dialect) {\n        let values = Object.keys(this.dialects), flags = values.map(() => false);\n        if (dialect)\n            for (let part of dialect.split(\" \")) {\n                let id = values.indexOf(part);\n                if (id >= 0)\n                    flags[id] = true;\n            }\n        let disabled = null;\n        for (let i = 0; i < values.length; i++)\n            if (!flags[i]) {\n                for (let j = this.dialects[values[i]], id; (id = this.data[j++]) != 65535 /* Seq.End */;)\n                    (disabled || (disabled = new Uint8Array(this.maxTerm + 1)))[id] = 1;\n            }\n        return new Dialect(dialect, flags, disabled);\n    }\n    /**\n    Used by the output of the parser generator. Not available to\n    user code. @hide\n    */\n    static deserialize(spec) {\n        return new LRParser(spec);\n    }\n}\nfunction pair(data, off) { return data[off] | (data[off + 1] << 16); }\nfunction findFinished(stacks) {\n    let best = null;\n    for (let stack of stacks) {\n        let stopped = stack.p.stoppedAt;\n        if ((stack.pos == stack.p.stream.end || stopped != null && stack.pos > stopped) &&\n            stack.p.parser.stateFlag(stack.state, 2 /* StateFlag.Accepting */) &&\n            (!best || best.score < stack.score))\n            best = stack;\n    }\n    return best;\n}\nfunction getSpecializer(spec) {\n    if (spec.external) {\n        let mask = spec.extend ? 1 /* Specialize.Extend */ : 0 /* Specialize.Specialize */;\n        return (value, stack) => (spec.external(value, stack) << 1) | mask;\n    }\n    return spec.get;\n}\n\nexport { ContextTracker, ExternalTokenizer, InputStream, LRParser, LocalTokenGroup, Stack };\n", "import { L<PERSON>arser } from '@lezer/lr';\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst Json$1 = 1,\n  Logfmt$1 = 2,\n  Unpack$1 = 3,\n  Pattern$1 = 4,\n  Regexp$1 = 5,\n  Unwrap$1 = 6,\n  LabelFormat$1 = 7,\n  LineFormat$1 = 8,\n  LabelReplace$1 = 9,\n  Vector$1 = 10,\n  Offset$1 = 11,\n  Bool$1 = 12,\n  On$1 = 13,\n  Ignoring$1 = 14,\n  GroupLeft$1 = 15,\n  GroupRight$1 = 16,\n  Decolorize$1 = 17,\n  Drop$1 = 18,\n  Keep$1 = 19,\n  By$1 = 20,\n  Without$1 = 21,\n  And$1 = 22,\n  Or$1 = 23,\n  Unless$1 = 24,\n  Sum$1 = 25,\n  Avg$1 = 26,\n  Count$1 = 27,\n  Max$1 = 28,\n  Min$1 = 29,\n  Stddev$1 = 30,\n  Stdvar$1 = 31,\n  Bottomk$1 = 32,\n  Topk$1 = 33,\n  Sort$1 = 34,\n  Sort_Desc$1 = 35;\n\nconst keywordTokens = {\n  json: Json$1,\n  logfmt: Logfmt$1,\n  unpack: Unpack$1,\n  pattern: Pattern$1,\n  regexp: Regexp$1,\n  label_format: LabelFormat$1,\n  line_format: LineFormat$1,\n  label_replace: LabelReplace$1,\n  vector: Vector$1,\n  offset: Offset$1,\n  bool: Bool$1,\n  on: On$1,\n  ignoring: Ignoring$1,\n  group_left: GroupLeft$1,\n  group_right: GroupRight$1,\n  unwrap: Unwrap$1,\n  decolorize: Decolorize$1,\n  drop: Drop$1,\n  keep: Keep$1,\n};\n\nconst specializeIdentifier = (value) => {\n  return keywordTokens[value.toLowerCase()] || -1;\n};\n\nconst contextualKeywordTokens = {\n  by: By$1,\n  without: Without$1,\n  and: And$1,\n  or: Or$1,\n  unless: Unless$1,\n  sum: Sum$1,\n  avg: Avg$1,\n  count: Count$1,\n  max: Max$1,\n  min: Min$1,\n  stddev: Stddev$1,\n  stdvar: Stdvar$1,\n  bottomk: Bottomk$1,\n  topk: Topk$1,\n  sort: Sort$1,\n  sort_desc: Sort_Desc$1,\n};\n\nconst extendIdentifier = (value) => {\n  return contextualKeywordTokens[value.toLowerCase()] || -1;\n};\n\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst spec_Identifier = {__proto__:null,ip:295, count_over_time:301, rate:303, rate_counter:305, bytes_over_time:307, bytes_rate:309, avg_over_time:311, sum_over_time:313, min_over_time:315, max_over_time:317, stddev_over_time:319, stdvar_over_time:321, quantile_over_time:323, first_over_time:325, last_over_time:327, absent_over_time:329, bytes:335, duration:337, duration_seconds:339};\nconst parser = LRParser.deserialize({\n  version: 14,\n  states: \"EtOYQPOOO#cQPO'#DUOOQO'#ER'#ERO#hQPO'#ERO$}QPO'#DTOYQPO'#DTOOQO'#Ed'#EdO%[QPO'#EcOOQO'#FP'#FPO%aQPO'#FOQ%lQPOOO&mQPO'#F]O&rQPO'#F^OOQO'#Eb'#EbOOQO'#DS'#DSOOQO'#Ee'#EeOOQO'#Ef'#EfOOQO'#Eg'#EgOOQO'#Eh'#EhOOQO'#Ei'#EiOOQO'#Ej'#EjOOQO'#Ek'#EkOOQO'#El'#ElOOQO'#Em'#EmOOQO'#En'#EnOOQO'#Eo'#EoOOQO'#Ep'#EpOOQO'#Eq'#EqOOQO'#Er'#ErOOQO'#Es'#EsO&wQPO'#DWOOQO'#DV'#DVO'VQPO,59pOOQO,5:m,5:mOOQO'#Dc'#DcO'_QPO'#DbO'gQPO'#DaO)lQPO'#D`O*{QPO'#D`OOQO'#D_'#D_O+sQPO,59oO-}QPO,59oO.UQPO,5:|O.]QPO,5:}O.hQPO'#E|O0sQPO,5;jO0zQPO,5;jO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lOYQPO,5;wO3cQPO,5;xO3hQPO,59rO#cQPO,59qOOQO1G/[1G/[OOQO'#Dh'#DhO3mQPO,59|O5^QPO,59|OOQO'#Di'#DiO5cQPO,59{OOQO,59{,59{O5kQPO'#DWO6YQPO'#DlO8PQPO'#DoO9sQPO'#DoOOQO'#Do'#DoOOQO'#Dv'#DvOOQO'#Dt'#DtO+kQPO'#DtO9xQPO,59zO;iQPO'#EVO;nQPO'#EWOOQO'#EZ'#EZO;sQPO'#E[O;xQPO'#E_OOQO,59z,59zOOQO,59y,59yOOQO1G/Z1G/ZOOQO1G0h1G0hO;}QPO'#EtO.`QPO'#EtO<XQPO1G0iO<^QPO1G0iO<cQPO,5;hO=oQPO1G1UO=vQPO1G1UO=}QPO1G1UO>UQPO'#FSO@dQPO'#FRO@nQPO'#FROYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WO@xQPO1G1cOAPQPO1G1dOOQO1G/^1G/^OOQO1G/]1G/]O5cQPO1G/hOAUQPO1G/hOAZQPO'#DjOBzQPO'#DjOOQO1G/g1G/gOCbQPO,59rOCPQPO,5:cOOQO'#Dm'#DmOClQPO,5:WOEcQPO'#DrOOQO'#Dq'#DqOGVQPO,5:_OHvQPO,5:[OOQO,5:Z,5:ZOJgQPO,5:`O+kQPO,5:`O+kQPO,5:`OOQO,5:q,5:qOJuQPO'#EYOOQO'#EX'#EXOJzQPO,5:rOLkQPO'#E^OOQO'#E^'#E^OOQO'#E]'#E]ONbQPO,5:vO!!RQPO'#EaOOQO'#Ea'#EaOOQO'#E`'#E`O!#xQPO,5:yO!%iQPO'#D`O;}QPO,5;`O!%pQPO'#EuO!%uQPO,5;`O!%}QPO,5;`O!&[QPO,5;`O!&iQPO,5;`O!&nQPO7+&TO.`QPO7+&TOOQO'#E}'#E}O!(OQPO1G1SOOQO1G1S1G1SOYQPO7+&pO!(WQPO7+&pO!)hQPO7+&pO!)oQPO7+&pO!)vQQO'#FTOOQO,5;n,5;nO!,UQPO,5;mO!,]QPO,5;mO!-nQPO7+&rO!-uQPO7+&rOOQO7+&r7+&rO!.SQPO7+&rO!.ZQPO7+&rO!/`QPO7+&rO!/pQPO7+&}OOQO7+'O7+'OOOQO7+%S7+%SO!/uQPO7+%SO5cQPO,5:UO!/zQPO,5:UO!0PQPO1G/{OOQO1G/}1G/}OOQO1G0U1G0UOOQO1G0W1G0WOOQO,5:X,5:XO!0UQPO1G/yO!1uQPO,5:^O!1zQPO,5:]OOQO1G/z1G/zO!2PQPO1G/zO!3pQPO,5:tO;nQPO,5:sO;sQPO,5:wO;xQPO,5:zO!3xQPO,5;cO!%uQPO1G0zO!4WQPO1G0zO!4`QPO,5;aO+kQPO,5;cO!4eQPO1G0zO!4oQPO'#EvO!4tQPO1G0zO!4eQPO1G0zO!4|QPO1G0zO!5ZQPO1G0zO!%xQPO1G0zOOQO1G0z1G0zOOQO<<Io<<IoO!5fQPO<<IoO!5kQPO,5;iOOQO7+&n7+&nO!5pQPO<<J[OOQO<<J[<<J[OYQPO<<J[OOQO'#FV'#FVO!5wQPO,5;oOOQO'#FU'#FUOOQO,5;o,5;oOOQO1G1X1G1XO!6PQPO1G1XO!8YQPO<<JiOOQO<<Hn<<HnOOQO1G/p1G/pO!8_QPO1G/pO!8dQPO7+%gOOQO1G/x1G/xOOQO1G/w1G/wOOQO1G0`1G0`OOQO1G0_1G0_OOQO1G0c1G0cOOQO1G0f1G0fOOQO'#Ex'#ExOOQO1G0}1G0}O!8iQPO1G0}OOQO'#Ey'#EyOOQO'#Ez'#EzOOQO'#E{'#E{OOQO7+&f7+&fOOQO1G0{1G0{O!8nQPO1G0}O!9SQPO7+&fOOQO,5;b,5;bO!9[QPO7+&fO!%xQPO7+&fO!9fQPO7+&fO!9qQPOAN?ZOOQO1G1T1G1TO!;RQPOAN?vO!<cQPOAN?vO!<jQQO1G1ZOOQO1G1Z1G1ZOOQO7+&s7+&sO!<rQPOAN@TOOQO7+%[7+%[O!<wQPO<<IRO!<|QPO7+&iO!=RQPO<<JQO!=ZQPO<<JQO!=cQPO'#EwO!=hQPO<<JQOOQOG24uG24uOOQOG25bG25bOOQO1G1[1G1[OOQO7+&u7+&uO!=pQPOG25oOOQOAN>mAN>mO!=uQPO<<JTOOQOAN?lAN?lO!=zQPOAN?lO!>SQPOLD+ZOOQOAN?oAN?oOOQO,5:r,5:rO!>XQPO!$'NuO!>^QPO!)9DaO!>cQPO!.K9{OOQO!4//g!4//gO;nQPO'#EWO!>hQPO'#D`O!?`QPO,59oO!@fQPO'#DTOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WOYQPO1G1WO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO1PQPO,5;lO!AqQPO7+&rO!AxQPO7+&rO!BVQPO7+&rO!C_QPO7+&rO!CfQPO7+&rO!B^QPO'#FQ\",\n  stateData: \"!Cs~O$TOStOS~OXZOY[OiWOjWOkWOlWOmWOnWOoWOpWOqWOrWOsWO!vQO!wRO!xRO$UPO$YTO$[_O$]`O$^aO$_bO$`cO$adO$beO$cfO$dgO$ehO$fiO$gjO$hkO$ilO$jmO~O{nO~O!vqO~O!OrO!QrO!WrO!XrO!YrO!ZrOfwXgwXhwX!lwX!nwX!owX!pwX!qwX!wwX!xwX#{wX#|wX#}wX$OwX~O!_vO$RwX$ZwX~P#mO$Y{O~Od|Oe|O$Y}O~Of!QOg!POh!QO!O!UO!l!UO!n!UO!o!UO!p!UO!q!UO!w!RO!x!RO#{!SO#|!SO#}!SO$O!TO~O$Y!VO~O$Y!WO~O|!XO!O!XO!P!XO!Q!XO~O$V!YO$W!ZO~O}!]O$X!_O~Og!`Of!TXh!TX!O!TX!Q!TX!W!TX!X!TX!Y!TX!Z!TX!_!TX!l!TX!n!TX!o!TX!p!TX!q!TX!w!TX!x!TX#{!TX#|!TX#}!TX$O!TX$R!TX$Z!TX$k!TX$V!TX~O!OrO!QrO!WrO!XrO!YrO!ZrO~Of!SXg!SXh!SX!_!SX!l!SX!n!SX!o!SX!p!SX!q!SX!w!SX!x!SX#{!SX#|!SX#}!SX$O!SX$R!SX$Z!SX$k!SX$V!SX~P)WOP!dOQ!cOR!fOS!eOT!eOV!lOW!kOa!mOb!nOc!oO{!bO$Y!iO~O!_vOfwagwahwa!lwa!nwa!owa!pwa!qwa!wwa!xwa#{wa#|wa#}wa$Owa$Rwa$Zwa~P)WOfvXgvXhvX!OvX!lvX!nvX!ovX!pvX!qvX!wvX!xvX#{vX#|vX#}vX$OvX~O$Z!rO~P,|O$Z!sO~P,|O!v!wO$UPO$Y!uO~O$Y!xO~OXZOY[OiWOjWOkWOlWOmWOnWOoWOpWOqWOrWOsWO!wRO!xRO$UPO$YTO$[_O$]`O$^aO$_bO$`cO$adO$beO$cfO$dgO$ehO$fiO$gjO$hkO$ilO$jmO~O!v!yO~P.mO$Y!{O~O[#OO]!|O^!|OX#uPY#uPi#uPj#uPk#uPl#uPm#uPn#uPo#uPp#uPq#uPr#uPs#uP!v#uP!w#uP!x#uP$U#uP$Y#uP$[#uP$]#uP$^#uP$_#uP$`#uP$a#uP$b#uP$c#uP$d#uP$e#uP$f#uP$g#uP$h#uP$i#uP$j#uP~O!v#WO~O}#XO~Og#ZOf!Uah!Ua!O!Ua!Q!Ua!W!Ua!X!Ua!Y!Ua!Z!Ua!_!Ua!l!Ua!n!Ua!o!Ua!p!Ua!q!Ua!w!Ua!x!Ua#{!Ua#|!Ua#}!Ua$O!Ua$R!Ua$Z!Ua$k!Ua$V!Ua~O$Y#[O~O}#]O$X!_O~O|#`O!O#`O!P!XO!Q!XO!l#aO!n#aO!o#aO!p#aO!q#aO~O{#dO!b#bOf!`Xg!`Xh!`X!O!`X!Q!`X!W!`X!X!`X!Y!`X!Z!`X!_!`X!l!`X!n!`X!o!`X!p!`X!q!`X!w!`X!x!`X#{!`X#|!`X#}!`X$O!`X$R!`X$Z!`X$k!`X$V!`X~O{#dOf!cXg!cXh!cX!O!cX!Q!cX!W!cX!X!cX!Y!cX!Z!cX!_!cX!l!cX!n!cX!o!cX!p!cX!q!cX!w!cX!x!cX#{!cX#|!cX#}!cX$O!cX$R!cX$Z!cX$k!cX$V!cX~O}#hO~Of#jOg#kO$V#jOh!Sa!O!Sa!Q!Sa!W!Sa!X!Sa!Y!Sa!Z!Sa!_!Sa!l!Sa!n!Sa!o!Sa!p!Sa!q!Sa!w!Sa!x!Sa#{!Sa#|!Sa#}!Sa$O!Sa$R!Sa$Z!Sa$k!Sa~O}#lO~O{#mO~O{#pO~O{#tO~O!_#xO$k#zO~P)WO$Z$PO~O$V$QO~O{$RO$Z$TO~Of!uXg!uXh!uX!O!uX!l!uX!n!uX!o!uX!p!uX!q!uX!w!uX!x!uX#{!uX#|!uX#}!uX$O!uX$Z!uX~O$V$UO~P<kO$Z$VO~P,|O!v$WO~P.mO$Y$YO~OX#uXY#uXi#uXj#uXk#uXl#uXm#uXn#uXo#uXp#uXq#uXr#uXs#uX!v#uX!w#uX!x#uX$U#uX$Y#uX$[#uX$]#uX$^#uX$_#uX$`#uX$a#uX$b#uX$c#uX$d#uX$e#uX$f#uX$g#uX$h#uX$i#uX$j#uX~O_$[O`$[O~P>ZO]!|O^!|O~P>ZO$V$dO~P,|O$Z$eO~O}$gO~Og$hOf!^Xh!^X!O!^X!Q!^X!W!^X!X!^X!Y!^X!Z!^X!_!^X!l!^X!n!^X!o!^X!p!^X!q!^X!w!^X!x!^X#{!^X#|!^X#}!^X$O!^X$R!^X$Z!^X$k!^X$V!^X~O$Y$iO~O!m$kO!s$lO!vQO!wRO!xRO~O}#XO$X!_O~PCPO{#dO!b$nOf!`ag!`ah!`a!O!`a!Q!`a!W!`a!X!`a!Y!`a!Z!`a!_!`a!l!`a!n!`a!o!`a!p!`a!q!`a!w!`a!x!`a#{!`a#|!`a#}!`a$O!`a$R!`a$Z!`a$k!`a$V!`a~O|$pOf!fXg!fXh!fX!O!fX!Q!fX!W!fX!X!fX!Y!fX!Z!fX!_!fX!l!fX!n!fX!o!fX!p!fX!q!fX!w!fX!x!fX#{!fX#|!fX#}!fX$O!fX$R!fX$V!fX$Z!fX$k!fX~O$V$qOf!gag!gah!ga!O!ga!Q!ga!W!ga!X!ga!Y!ga!Z!ga!_!ga!l!ga!n!ga!o!ga!p!ga!q!ga!w!ga!x!ga#{!ga#|!ga#}!ga$O!ga$R!ga$Z!ga$k!ga~O$V$qOf!dag!dah!da!O!da!Q!da!W!da!X!da!Y!da!Z!da!_!da!l!da!n!da!o!da!p!da!q!da!w!da!x!da#{!da#|!da#}!da$O!da$R!da$Z!da$k!da~Of#jOg#kO$V#jO$Z$rO~O|$tO~O$V$uOf!zag!zah!za!O!za!Q!za!W!za!X!za!Y!za!Z!za!_!za!l!za!n!za!o!za!p!za!q!za!w!za!x!za#{!za#|!za#}!za$O!za$R!za$Z!za$k!za~O|!XO!O!XO!P!XO!Q!XOf#QXg#QXh#QX!W#QX!X#QX!Y#QX!Z#QX!_#QX!l#QX!n#QX!o#QX!p#QX!q#QX!w#QX!x#QX#{#QX#|#QX#}#QX$O#QX$R#QX$V#QX$Z#QX$k#QX~O$V$vOf#Oag#Oah#Oa!O#Oa!Q#Oa!W#Oa!X#Oa!Y#Oa!Z#Oa!_#Oa!l#Oa!n#Oa!o#Oa!p#Oa!q#Oa!w#Oa!x#Oa#{#Oa#|#Oa#}#Oa$O#Oa$R#Oa$Z#Oa$k#Oa~O|!XO!O!XO!P!XO!Q!XOf#TXg#TXh#TX!W#TX!X#TX!Y#TX!Z#TX!_#TX!l#TX!n#TX!o#TX!p#TX!q#TX!w#TX!x#TX#{#TX#|#TX#}#TX$O#TX$R#TX$V#TX$Z#TX$k#TX~O$V$wOf#Rag#Rah#Ra!O#Ra!Q#Ra!W#Ra!X#Ra!Y#Ra!Z#Ra!_#Ra!l#Ra!n#Ra!o#Ra!p#Ra!q#Ra!w#Ra!x#Ra#{#Ra#|#Ra#}#Ra$O#Ra$R#Ra$Z#Ra$k#Ra~OU$xO~P*{O!m${O~O!_$|O$k#zO~OZ%OO!_#xO$Z#ha~P)WO!_#xO$Z%TO$k#zO~P)WO$Z%UO~Od|Oe|Of#Vqg#Vqh#Vq!O#Vq!l#Vq!n#Vq!o#Vq!p#Vq!q#Vq!w#Vq!x#Vq#{#Vq#|#Vq#}#Vq$O#Vq$R#Vq$Z#Vq$V#Vq~O$V%XO$Z%YO~Od|Oe|Of#rqg#rqh#rq!O#rq!l#rq!n#rq!o#rq!p#rq!q#rq!w#rq!x#rq#{#rq#|#rq#}#rq$O#rq$R#rq$Z#rq$V#rq~O$V%]O~P<kO$Z%[O~P,|O#z%^O$Z%aO~OX#uaY#uai#uaj#uak#ual#uam#uan#uao#uap#uaq#uar#uas#ua!v#ua!w#ua!x#ua$U#ua$[#ua$]#ua$^#ua$_#ua$`#ua$a#ua$b#ua$c#ua$d#ua$e#ua$f#ua$g#ua$h#ua$i#ua$j#ua~O$Y$YO~P!*OO_%cO`%cO$Y#ua~P!*OOf!QOh!QO!O!UO!l!UO!n!UO!o!UO!p!UO!q!UO!w!RO!x!RO#{#tq#|#tq#}#tq$O#tq$R#tq$Z#tq~Og#tq~P!,jOf#tqg#tqh#tq~P!,pOg!PO~P!,jO$R#tq$Z#tq~P%lOf#tqg#tqh#tq!O#tq!l#tq!n#tq!o#tq!p#tq!q#tq#{#tq#|#tq#}#tq$O#tq~O!w!RO!x!RO$R#tq$Z#tq~P!.eO}%dO~O$Z%eO~O}%gO~O$Y%hO~O$V$qOf!gig!gih!gi!O!gi!Q!gi!W!gi!X!gi!Y!gi!Z!gi!_!gi!l!gi!n!gi!o!gi!p!gi!q!gi!w!gi!x!gi#{!gi#|!gi#}!gi$O!gi$R!gi$Z!gi$k!gi~O}%iO~O{#dO~Of#jO$V#jOg!hih!hi!O!hi!Q!hi!W!hi!X!hi!Y!hi!Z!hi!_!hi!l!hi!n!hi!o!hi!p!hi!q!hi!w!hi!x!hi#{!hi#|!hi#}!hi$O!hi$R!hi$Z!hi$k!hi~O{%kO}%kO~O{%pO$m%rO$n%sO$o%tO~OZ%OO$Z#hi~O$l%vO~O!_#xO$Z#hi~P)WO!m%yO~O!_$|O$Z#hi~O!_#xO$Z%{O$k#zO~P)WO!_$|O$Z%{O$k#zO~O$Z%}O~O{&OO~O$Z&PO~P,|O$V&RO$Z&SO~O$Y$YOX#uiY#uii#uij#uik#uil#uim#uin#uio#uip#uiq#uir#uis#ui!v#ui!w#ui!x#ui$U#ui$[#ui$]#ui$^#ui$_#ui$`#ui$a#ui$b#ui$c#ui$d#ui$e#ui$f#ui$g#ui$h#ui$i#ui$j#ui~O$V&UO~O$Z&VO~O}&WO~O$Y&XO~Of#jOg#kO$V#jO!_#ki$k#ki$Z#ki~O!_$|O$Z#hq~O!_#xO$Z#hq~P)WOZ%OO!_&[O$Z#hq~Od|Oe|Of#V!Rg#V!Rh#V!R!O#V!R!l#V!R!n#V!R!o#V!R!p#V!R!q#V!R!w#V!R!x#V!R#{#V!R#|#V!R#}#V!R$O#V!R$R#V!R$Z#V!R$V#V!R~Od|Oe|Of#r!Rg#r!Rh#r!R!O#r!R!l#r!R!n#r!R!o#r!R!p#r!R!q#r!R!w#r!R!x#r!R#{#r!R#|#r!R#}#r!R$O#r!R$R#r!R$Z#r!R$V#r!R~O$Z&_O~P,|O#z%^O$Z&aO~O}&bO~O$Z&cO~O{&dO~O!_$|O$Z#hy~OZ%OO$Z#hy~OU$xO~O!_&[O$Z#hy~O$V&gO~O$Z&hO~O!_$|O$Z#h!R~O}&jO~O$V&kO~O}&lO~O$Z&mO~OP!dOQ!cOR!fOS!eOT!eOV&nOW!kOa!mOb!nOc!oO{!bO$Y!iO~O!_&oOfwagwahwa!lwa!nwa!owa!pwa!qwa!wwa!xwa#{wa#|wa#}wa$Owa$Vwa~P)WO!_&oO$VwX~P#mOf&yOh&yO!O&}O!l&}O!n&}O!o&}O!p&}O!q&}O!w&zO!x&zO#{#tq#|#tq#}#tq$O#tq$V#tq~Og#tq~P!@pOf#tqg#tqh#tq~P!@vOg&xO~P!@pOf&yOg&xOh&yO!O&}O!l&}O!n&}O!o&}O!p&}O!q&}O!w&zO!x&zO#{&{O#|&{O#}&{O$O&|O~O$V#tq~P!B^O!w&zO!x&zO$V#tq~P!.eO\",\n  goto: \"1l$RPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPPP$S%R%j&Y&]PPPPPP&t'W'h'v(XPPPP(h(p(yP)S)XP)S)S)[)e)S)m*O*O*XPPPPPP*XP*O*bPPP)S)S*{+R)S)S+Y+])S+c+f+l,_,t-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-Z-p-y.^.j/S/V/V/V/Y/i,_/l,_0R0w1Y1c1fPPPPP,_,_[YOT}!{$U%]Q$^#PQ$_#QS$`#R&tQ$a#SQ$b#TQ$c#UQ'O&rQ'P&sQ'Q&uQ'R&vQ'S&wR'T!Vt^O}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wRyTjSOT}!V!{#P#Q#R#S#T#U$U%]S!t{$QQ#}!u]&q&r&s&t&u&v&wRpPQoP^!hv!i#j#k#x$|&oQ#Y!YS#q!n$vT#u!o$wQxSQ#y!tQ$}#|Q%R#}Q%z%QR&p&q[wS!t#|#}%Q&q]!qx#y$}%R%z&piuSx!t#y#|#}$}%Q%R%z&p&qhtSx!t#y#|#}$}%Q%R%z&p&qR!auksSux!t#y#|#}$}%Q%R%z&p&qQ!^sV#^!`#Z$hW![s!`#Z$hR$j#`Q#_!`Q$f#ZR%f$hV!pv#x&oR#c!cQ#f!cQ#g!dR$o#cU#e!c!d#cR%j$qU!jv#x&oQ#i!iQ$r#jQ$s#kR%w$|_!hv!i#j#k#x$|&o_!gv!i#j#k#x$|&ov]OT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wT$m#`#aQ#o!lR&i&nS#n!l&nR%l$uR#s!nQ#r!nR%m$vR#w!oQ#v!oR%n$wj^O#P#Q#R#S#T#U&r&s&t&u&v&wQzTQ!z}Q#V!VQ$X!{Q%Z$UR&Q%]w]OT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wwVOT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wwUOT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wQ!v{Q$O!uR%W$QS#|!t#}W$z#y#{%R%SQ%u$yQ%|%TR&Z%{Q%Q#|Q%u$zQ&]%|R&e&ZQ#{!tS$y#y%RQ%P#|Q%S#}S%x$}%QS&Y%z%|R&f&]R%q$xR%o$xQ!OXQ%V$PQ%[$VQ&^%}R&_&PR$S!xwXOT}!V!{#P#Q#R#S#T#U$U%]&r&s&t&u&v&wQ#P!PQ#Q!QQ#R!RQ#S!SQ#T!TQ#U!UQ&r&xQ&s&yQ&t&zQ&u&{Q&v&|R&w&}h!}!P!Q!R!S!T!U&x&y&z&{&|&}R$]#OQ$Z!|Q%b$[R&T%cR%_$YQ%`$YR&`&R\",\n  nodeNames: \"⚠ Json Logfmt Unpack Pattern Regexp Unwrap LabelFormat LineFormat LabelReplace Vector Offset Bool On Ignoring GroupLeft GroupRight Decolorize Drop Keep By Without And Or Unless Sum Avg Count Max Min Stddev Stdvar Bottomk Topk Sort Sort_Desc LineComment LogQL Expr LogExpr Selector Matchers Matcher Identifier Eq String Neq Re Nre PipelineExpr PipelineStage LineFilters LineFilter Filter PipeExact PipeMatch PipePattern Npa FilterOp Ip OrFilter Pipe LogfmtParser LogfmtParserFlags ParserFlag LabelParser JsonExpressionParser LabelExtractionExpressionList LabelExtractionExpression LogfmtExpressionParser LabelFilter IpLabelFilter UnitFilter DurationFilter Gtr Duration Gte Lss Lte Eql BytesFilter Bytes NumberFilter LiteralExpr Number Add Sub LineFormatExpr LabelFormatExpr LabelsFormat LabelFormatMatcher DecolorizeExpr DropLabelsExpr DropLabels DropLabel KeepLabelsExpr KeepLabels KeepLabel MetricExpr RangeAggregationExpr RangeOp CountOverTime Rate RateCounter BytesOverTime BytesRate AvgOverTime SumOverTime MinOverTime MaxOverTime StddevOverTime StdvarOverTime QuantileOverTime FirstOverTime LastOverTime AbsentOverTime LogRangeExpr Range OffsetExpr UnwrapExpr ConvOp BytesConv DurationConv DurationSecondsConv Grouping Labels VectorAggregationExpr VectorOp BinOpExpr BinOpModifier OnOrIgnoringModifier GroupingLabels GroupingLabelList GroupingLabel LabelName Mul Div Mod Pow LabelReplaceExpr VectorExpr\",\n  maxTerm: 169,\n  skippedNodes: [0,36],\n  repeatNodeCount: 0,\n  tokenData: \"<n~RvX^#ipq#iqr$^rs$yst%kuv%vxy%{yz&Qz{&V{|&[|}&a}!O&f!O!P2v!P!Q3v!Q!R3{!R![7^![!]9]!^!_9q!_!`:O!`!a:e!c!}:r!}#O;Y#P#Q;_#Q#R;d#R#S:r#S#T;i#T#o:r#o#p;u#p#q;z#q#r<i#y#z#i$f$g#i#BY#BZ#i$IS$I_#i$I|$JO#i$JT$JU#i$KV$KW#i&FU&FV#i~#nY$T~X^#ipq#i#y#z#i$f$g#i#BY#BZ#i$IS$I_#i$I|$JO#i$JT$JU#i$KV$KW#i&FU&FV#i~$aR!_!`$j!`!a$o#r#s$t~$oO!O~~$tO!Z~~$yO!Q~~$|UOY$yZr$yrs%`s#O$y#O#P%e#P~$y~%eO}~~%hPO~$y~%pQt~OY%kZ~%k~%{O#}~~&QO$Y~~&VO$Z~~&[O#{~~&aO!w~~&fO$V~~&kQ!x~}!O&q!Q![(w~&tQ#_#`&z#g#h(X~&}P#X#Y'Q~'TP#X#Y'W~'ZP#d#e'^~'aP}!O'd~'gP#X#Y'j~'mP#a#b'p~'sP#d#e'v~'yP#h#i'|~(PP#m#n(S~(XO!b~~([P#h#i(_~(bP#f#g(e~(hP#]#^(k~(nP#V#W(q~(tP#h#i(S~(zZ!O!P)m!Q![(w#W#X0`#[#]*]#a#b+j#b#c.R#g#h/X#i#j.^#k#l1T#m#n1{${$|.^~)pP!Q![)s~)vV!Q![)s#[#]*]#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~*bP!m~!Q![*e~*hV!O!P*}!Q![*e#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~+QP!Q![+T~+WU!Q![+T#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~+oQ!m~!Q![+u#g#h-Q~+xV!O!P,_!Q![+u#a#b,z#b#c.R#g#h/X#i#j.^${$|.^~,bP!Q![,e~,hU!Q![,e#a#b,z#b#c.R#g#h/X#i#j.^${$|.^~,}P#g#h-Q~-VP!m~!Q![-Y~-]T!O!P-l!Q![-Y#b#c.R#i#j.^${$|.^~-oP!Q![-r~-uS!Q![-r#b#c.R#i#j.^${$|.^~.UP#g#h.X~.^O!m~~.aP#g#h.d~.iP!m~!Q![.l~.oR!O!P.x!Q![.l#b#c.R~.{P!Q![/O~/RQ!Q![/O#b#c.R~/^P!m~!Q![/a~/dU!O!P/v!Q![/a#a#b,z#b#c.R#i#j.^${$|.^~/yP!Q![/|~0PT!Q![/|#a#b,z#b#c.R#i#j.^${$|.^~0eP!m~!Q![0h~0kW!O!P)m!Q![0h#[#]*]#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~1YP!m~!Q![1]~1`X!O!P)m!Q![1]#W#X0`#[#]*]#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~2QP!m~!Q![2T~2WY!O!P)m!Q![2T#W#X0`#[#]*]#a#b+j#b#c.R#g#h/X#i#j.^#k#l1T${$|.^~2yP!Q![2|~3RR!v~!Q![2|!g!h3[#X#Y3[~3_R{|3h}!O3h!Q![3n~3kP!Q![3n~3sP!v~!Q![3n~3{O#|~~4Qe!v~!O!P5c!Q![7^!d!e6x!g!h3[!i!j6}!m!n6}!o!p6}!r!s6}!v!w6}#W#X0`#X#Y3[#[#]*]#_#`7W#a#b+j#b#c.R#g#h/X#i#j.^#k#l1T#l#m8q#m#n1{${$|.^~5hR!v~!Q![5q!g!h3[#X#Y3[~5v`!v~!Q![5q!d!e6x!g!h3[!i!j6}!m!n6}!o!p6}!r!s6}!v!w6}#X#Y3[#[#]*]#_#`7W#a#b+j#b#c.R#g#h/X#i#j.^${$|.^~6}O!s~~7QQ!d!e6x#]#^7W~7ZP!d!e6x~7cd!v~!O!P5c!Q![7^!d!e6x!g!h3[!i!j6}!m!n6}!o!p6}!r!s6}!v!w6}#W#X0`#X#Y3[#[#]*]#_#`7W#a#b+j#b#c.R#g#h/X#i#j.^#k#l1T#m#n1{${$|.^~8tR!Q![8}!c!i8}#T#Z8}~9SR!v~!Q![8}!c!i8}#T#Z8}P9bT{P!Q![9]![!]9]!c!}9]#R#S9]#T#o9]~9vP!o~!_!`9y~:OO!p~~:TQ|~!_!`:Z#r#s:`~:`O!q~~:eO!P~~:jP!l~!_!`:m~:rO!n~R:yT{P#zQ!Q![:r![!]9]!c!}:r#R#S:r#T#o:r~;_O$k~~;dO$l~~;iO$O~~;lRO#S;i#S#T%`#T~;i~;zO$U~~<PR!_~!_!`<Y!`!a<_#r#s<d~<_O!W~~<dO!Y~~<iO!X~~<nO$W~\",\n  tokenizers: [0, 1],\n  topRules: {\"LogQL\":[0,37]},\n  specialized: [{term: 43, get: (value, stack) => (specializeIdentifier(value) << 1)},{term: 43, get: (value, stack) => (extendIdentifier(value) << 1) | 1},{term: 43, get: value => spec_Identifier[value] || -1}],\n  tokenPrec: 0\n});\n// This file was generated by lezer-generator. You probably shouldn't edit it.\nconst Json = 1,\n  Logfmt = 2,\n  Unpack = 3,\n  Pattern = 4,\n  Regexp = 5,\n  Unwrap = 6,\n  LabelFormat = 7,\n  LineFormat = 8,\n  LabelReplace = 9,\n  Vector = 10,\n  Offset = 11,\n  Bool = 12,\n  On = 13,\n  Ignoring = 14,\n  GroupLeft = 15,\n  GroupRight = 16,\n  Decolorize = 17,\n  Drop = 18,\n  Keep = 19,\n  By = 20,\n  Without = 21,\n  And = 22,\n  Or = 23,\n  Unless = 24,\n  Sum = 25,\n  Avg = 26,\n  Count = 27,\n  Max = 28,\n  Min = 29,\n  Stddev = 30,\n  Stdvar = 31,\n  Bottomk = 32,\n  Topk = 33,\n  Sort = 34,\n  Sort_Desc = 35,\n  LineComment = 36,\n  LogQL = 37,\n  Expr = 38,\n  LogExpr = 39,\n  Selector = 40,\n  Matchers = 41,\n  Matcher = 42,\n  Identifier = 43,\n  Eq = 44,\n  String = 45,\n  Neq = 46,\n  Re = 47,\n  Nre = 48,\n  PipelineExpr = 49,\n  PipelineStage = 50,\n  LineFilters = 51,\n  LineFilter = 52,\n  Filter = 53,\n  PipeExact = 54,\n  PipeMatch = 55,\n  PipePattern = 56,\n  Npa = 57,\n  FilterOp = 58,\n  Ip = 59,\n  OrFilter = 60,\n  Pipe = 61,\n  LogfmtParser = 62,\n  LogfmtParserFlags = 63,\n  ParserFlag = 64,\n  LabelParser = 65,\n  JsonExpressionParser = 66,\n  LabelExtractionExpressionList = 67,\n  LabelExtractionExpression = 68,\n  LogfmtExpressionParser = 69,\n  LabelFilter = 70,\n  IpLabelFilter = 71,\n  UnitFilter = 72,\n  DurationFilter = 73,\n  Gtr = 74,\n  Duration = 75,\n  Gte = 76,\n  Lss = 77,\n  Lte = 78,\n  Eql = 79,\n  BytesFilter = 80,\n  Bytes = 81,\n  NumberFilter = 82,\n  LiteralExpr = 83,\n  Number = 84,\n  Add = 85,\n  Sub = 86,\n  LineFormatExpr = 87,\n  LabelFormatExpr = 88,\n  LabelsFormat = 89,\n  LabelFormatMatcher = 90,\n  DecolorizeExpr = 91,\n  DropLabelsExpr = 92,\n  DropLabels = 93,\n  DropLabel = 94,\n  KeepLabelsExpr = 95,\n  KeepLabels = 96,\n  KeepLabel = 97,\n  MetricExpr = 98,\n  RangeAggregationExpr = 99,\n  RangeOp = 100,\n  CountOverTime = 101,\n  Rate = 102,\n  RateCounter = 103,\n  BytesOverTime = 104,\n  BytesRate = 105,\n  AvgOverTime = 106,\n  SumOverTime = 107,\n  MinOverTime = 108,\n  MaxOverTime = 109,\n  StddevOverTime = 110,\n  StdvarOverTime = 111,\n  QuantileOverTime = 112,\n  FirstOverTime = 113,\n  LastOverTime = 114,\n  AbsentOverTime = 115,\n  LogRangeExpr = 116,\n  Range = 117,\n  OffsetExpr = 118,\n  UnwrapExpr = 119,\n  ConvOp = 120,\n  BytesConv = 121,\n  DurationConv = 122,\n  DurationSecondsConv = 123,\n  Grouping = 124,\n  Labels = 125,\n  VectorAggregationExpr = 126,\n  VectorOp = 127,\n  BinOpExpr = 128,\n  BinOpModifier = 129,\n  OnOrIgnoringModifier = 130,\n  GroupingLabels = 131,\n  GroupingLabelList = 132,\n  GroupingLabel = 133,\n  LabelName = 134,\n  Mul = 135,\n  Div = 136,\n  Mod = 137,\n  Pow = 138,\n  LabelReplaceExpr = 139,\n  VectorExpr = 140;\n\nexport { AbsentOverTime, Add, And, Avg, AvgOverTime, BinOpExpr, BinOpModifier, Bool, Bottomk, By, Bytes, BytesConv, BytesFilter, BytesOverTime, BytesRate, ConvOp, Count, CountOverTime, Decolorize, DecolorizeExpr, Div, Drop, DropLabel, DropLabels, DropLabelsExpr, Duration, DurationConv, DurationFilter, DurationSecondsConv, Eq, Eql, Expr, Filter, FilterOp, FirstOverTime, GroupLeft, GroupRight, Grouping, GroupingLabel, GroupingLabelList, GroupingLabels, Gte, Gtr, Identifier, Ignoring, Ip, IpLabelFilter, Json, JsonExpressionParser, Keep, KeepLabel, KeepLabels, KeepLabelsExpr, LabelExtractionExpression, LabelExtractionExpressionList, LabelFilter, LabelFormat, LabelFormatExpr, LabelFormatMatcher, LabelName, LabelParser, LabelReplace, LabelReplaceExpr, Labels, LabelsFormat, LastOverTime, LineComment, LineFilter, LineFilters, LineFormat, LineFormatExpr, LiteralExpr, LogExpr, LogQL, LogRangeExpr, Logfmt, LogfmtExpressionParser, LogfmtParser, LogfmtParserFlags, Lss, Lte, Matcher, Matchers, Max, MaxOverTime, MetricExpr, Min, MinOverTime, Mod, Mul, Neq, Npa, Nre, Number, NumberFilter, Offset, OffsetExpr, On, OnOrIgnoringModifier, Or, OrFilter, ParserFlag, Pattern, Pipe, PipeExact, PipeMatch, PipePattern, PipelineExpr, PipelineStage, Pow, QuantileOverTime, Range, RangeAggregationExpr, RangeOp, Rate, RateCounter, Re, Regexp, Selector, Sort, Sort_Desc, Stddev, StddevOverTime, Stdvar, StdvarOverTime, String, Sub, Sum, SumOverTime, Topk, UnitFilter, Unless, Unpack, Unwrap, UnwrapExpr, Vector, VectorAggregationExpr, VectorExpr, VectorOp, Without, parser };\n", "// The module cache\nvar __webpack_module_cache__ = {};\n\n// The require function\nfunction __webpack_require__(moduleId) {\n\t// Check if module is in cache\n\tvar cachedModule = __webpack_module_cache__[moduleId];\n\tif (cachedModule !== undefined) {\n\t\treturn cachedModule.exports;\n\t}\n\t// Create a new module (and put it into the cache)\n\tvar module = __webpack_module_cache__[moduleId] = {\n\t\tid: moduleId,\n\t\tloaded: false,\n\t\texports: {}\n\t};\n\n\t// Execute the module function\n\t__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);\n\n\t// Flag the module as loaded\n\tmodule.loaded = true;\n\n\t// Return the exports of the module\n\treturn module.exports;\n}\n\n// expose the modules object (__webpack_modules__)\n__webpack_require__.m = __webpack_modules__;\n\n", "// getDefaultExport function for compatibility with non-harmony modules\n__webpack_require__.n = (module) => {\n\tvar getter = module && module.__esModule ?\n\t\t() => (module['default']) :\n\t\t() => (module);\n\t__webpack_require__.d(getter, { a: getter });\n\treturn getter;\n};", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.f = {};\n// This file contains only the entry chunk.\n// The chunk loading function for additional chunks\n__webpack_require__.e = (chunkId) => {\n\treturn Promise.all(Object.keys(__webpack_require__.f).reduce((promises, key) => {\n\t\t__webpack_require__.f[key](chunkId, promises);\n\t\treturn promises;\n\t}, []));\n};", "// This function allow to reference async chunks\n__webpack_require__.u = (chunkId) => {\n\t// return url for filenames based on template\n\treturn \"\" + chunkId + \".js?_cache=\" + {\"7\":\"617e09944eff4d2b85bc\",\"82\":\"938c92610b5cdf2d00ef\",\"328\":\"94a7be8c8fc30e97adee\",\"470\":\"29dd26bce42815d67980\",\"546\":\"8e0beb2f22d2cbf6b122\",\"677\":\"8e3b2953355d121a7060\",\"727\":\"9e9b1715d3dac8b90007\",\"767\":\"ca8114e32d5cb06e2cdd\",\"826\":\"91e39090c5611938563c\",\"854\":\"9da793b3efc18875808d\",\"864\":\"c7042e4fc7e1fc7aad94\",\"906\":\"2f24943902165ccd0731\",\"919\":\"728718e594379dd03c81\",\"944\":\"c9770b9500ce5eb4bbe7\"}[chunkId] + \"\";\n};", "__webpack_require__.g = (function() {\n\tif (typeof globalThis === 'object') return globalThis;\n\ttry {\n\t\treturn this || new Function('return this')();\n\t} catch (e) {\n\t\tif (typeof window === 'object') return window;\n\t}\n})();", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "__webpack_require__.nmd = (module) => {\n\tmodule.paths = [];\n\tif (!module.children) module.children = [];\n\treturn module;\n};", "__webpack_require__.p = \"public/plugins/grafana-lokiexplore-app/\";", "\n__webpack_require__.sriHashes = {\"7\":\"*-*-*-CHUNK-SRI-HASH-7F8zVqWsNOHcsmIWQKVJ6PROg7jSM=\",\"82\":\"*-*-*-CHUNK-SRI-HASH-EQQjepNKyKKHggNWDI+9nHc8T6ovg=\",\"328\":\"*-*-*-CHUNK-SRI-HASH-HP0RIrpXqzYxxeeTwj5+o/GRsLRxE=\",\"470\":\"*-*-*-CHUNK-SRI-HASH-DLC92l8qdycD4Ua87OV1W1EmkNCnU=\",\"546\":\"*-*-*-CHUNK-SRI-HASH-SGxEKWjXIi8J7KnveGHhrAGvn0JRw=\",\"677\":\"*-*-*-CHUNK-SRI-HASH-5AAWXVXE9/tolnBeqrK3+9cHOkFO8=\",\"727\":\"*-*-*-CHUNK-SRI-HASH-64vLUAncV763v/pYKf4avM0q+3oYk=\",\"767\":\"*-*-*-CHUNK-SRI-HASH-jEdaJjIgLKHmuiiv6R1fFNJ1L0eGE=\",\"826\":\"*-*-*-CHUNK-SRI-HASH-TD9ZyoD08pRdcHFno3E1sZN/8NxwE=\",\"854\":\"*-*-*-CHUNK-SRI-HASH-CO8yIHolx/3g3nglKHNgio0QWCR/E=\",\"864\":\"*-*-*-CHUNK-SRI-HASH-yWxbkjxSn+1LO1sy5eo44f5jeHdaE=\",\"906\":\"*-*-*-CHUNK-SRI-HASH-XoyJpYxqaHodAXTB6hiTmznYmkdwA=\",\"919\":\"*-*-*-CHUNK-SRI-HASH-E0zoKbm1MOaKexO/750XsMkg7RxhQ=\",\"944\":\"*-*-*-CHUNK-SRI-HASH-9Nvrh568fe5XpRS5Cb19/W6QCSzAA=\"};", "__webpack_require__.b = document.baseURI || self.location.href;\n\n// object to store loaded and loading chunks\n// undefined = chunk not loaded, null = chunk preloaded/prefetched\n// [resolve, reject, Promise] = chunk loading, 0 = chunk loaded\nvar installedChunks = {\n\t231: 0\n};\n\n__webpack_require__.f.j = (chunkId, promises) => {\n\t\t// JSONP chunk loading for javascript\n\t\tvar installedChunkData = __webpack_require__.o(installedChunks, chunkId) ? installedChunks[chunkId] : undefined;\n\t\tif(installedChunkData !== 0) { // 0 means \"already installed\".\n\n\t\t\t// a Promise means \"currently loading\".\n\t\t\tif(installedChunkData) {\n\t\t\t\tpromises.push(installedChunkData[2]);\n\t\t\t} else {\n\t\t\t\tif(true) { // all chunks have JS\n\t\t\t\t\t// setup Promise in chunk cache\n\t\t\t\t\tvar promise = new Promise((resolve, reject) => (installedChunkData = installedChunks[chunkId] = [resolve, reject]));\n\t\t\t\t\tpromises.push(installedChunkData[2] = promise);\n\n\t\t\t\t\t// start chunk loading\n\t\t\t\t\tvar url = __webpack_require__.p + __webpack_require__.u(chunkId);\n\t\t\t\t\t// create error before stack unwound to get useful stacktrace later\n\t\t\t\t\tvar error = new Error();\n\t\t\t\t\tvar loadingEnded = (event) => {\n\t\t\t\t\t\tif(__webpack_require__.o(installedChunks, chunkId)) {\n\t\t\t\t\t\t\tinstalledChunkData = installedChunks[chunkId];\n\t\t\t\t\t\t\tif(installedChunkData !== 0) installedChunks[chunkId] = undefined;\n\t\t\t\t\t\t\tif(installedChunkData) {\n\t\t\t\t\t\t\t\tvar errorType = event && (event.type === 'load' ? 'missing' : event.type);\n\t\t\t\t\t\t\t\tvar realSrc = event && event.target && event.target.src;\n\t\t\t\t\t\t\t\terror.message = 'Loading chunk ' + chunkId + ' failed.\\n(' + errorType + ': ' + realSrc + ')';\n\t\t\t\t\t\t\t\terror.name = 'ChunkLoadError';\n\t\t\t\t\t\t\t\terror.type = errorType;\n\t\t\t\t\t\t\t\terror.request = realSrc;\n\t\t\t\t\t\t\t\tinstalledChunkData[1](error);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t};\n\t\t\t\t\t__webpack_require__.l(url, loadingEnded, \"chunk-\" + chunkId, chunkId);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n};\n\n// no prefetching\n\n// no preloaded\n\n// no HMR\n\n// no HMR manifest\n\n// no on chunks loaded\n\n// install a JSONP callback for chunk loading\nvar webpackJsonpCallback = (parentChunkLoadingFunction, data) => {\n\tvar [chunkIds, moreModules, runtime] = data;\n\t// add \"moreModules\" to the modules object,\n\t// then flag all \"chunkIds\" as loaded and fire callback\n\tvar moduleId, chunkId, i = 0;\n\tif(chunkIds.some((id) => (installedChunks[id] !== 0))) {\n\t\tfor(moduleId in moreModules) {\n\t\t\tif(__webpack_require__.o(moreModules, moduleId)) {\n\t\t\t\t__webpack_require__.m[moduleId] = moreModules[moduleId];\n\t\t\t}\n\t\t}\n\t\tif(runtime) var result = runtime(__webpack_require__);\n\t}\n\tif(parentChunkLoadingFunction) parentChunkLoadingFunction(data);\n\tfor(;i < chunkIds.length; i++) {\n\t\tchunkId = chunkIds[i];\n\t\tif(__webpack_require__.o(installedChunks, chunkId) && installedChunks[chunkId]) {\n\t\t\tinstalledChunks[chunkId][0]();\n\t\t}\n\t\tinstalledChunks[chunkId] = 0;\n\t}\n\n}\n\nvar chunkLoadingGlobal = self[\"webpackChunkgrafana_lokiexplore_app\"] = self[\"webpackChunkgrafana_lokiexplore_app\"] || [];\nchunkLoadingGlobal.forEach(webpackJsonpCallback.bind(null, 0));\nchunkLoadingGlobal.push = webpackJsonpCallback.bind(null, chunkLoadingGlobal.push.bind(chunkLoadingGlobal));", "// startup\n// Load entry module and return exports\n// This entry module is referenced by other modules so it can't be inlined\nvar __webpack_exports__ = __webpack_require__(6709);\n"], "names": ["leafPrototypes", "getProto", "inProgress", "dataWebpackPrefix", "slice", "lastIndexOf", "OpenInLogsDrilldownButton", "lazy", "EmbeddedLogsExploration", "App", "wasmSupported", "default", "initRuntimeDs", "initChangepoint", "initOutlier", "Promise", "all", "AppConfig", "plugin", "AppPlugin", "setRootPage", "addConfigPage", "body", "icon", "id", "title", "linkConfig", "linkConfigs", "addLink", "exposeComponent", "component", "props", "Suspense", "fallback", "LinkButton", "variant", "disabled", "description", "div", "pageSlugUrlKey", "drilldownLabelUrlKey", "TabNames", "PageSlugs", "ValueSlugs", "PRODUCT_NAME", "ExtensionPoints", "MetricInvestigation", "targets", "PluginExtensionPoints", "DashboardPanelMenu", "ExploreToolbarAction", "path", "createAppUrl", "configure", "context", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "find", "target", "datasource", "type", "templateSrv", "getTemplateSrv", "dataSourceUid", "replace", "uid", "scopedVars", "expr", "interpolateQueryExpr", "fields", "labelFilters", "lineFilters", "patternFilters", "getMatcherFromQuery", "labelSelector", "selector", "isOperatorInclusive", "operator", "labelValue", "replaceSlash", "value", "split", "labelName", "key", "SERVICE_NAME", "sort", "a", "params", "setUrlParameter", "UrlParameters", "DatasourceId", "URLSearchParams", "TimeRangeFrom", "timeRange", "from", "valueOf", "toString", "TimeRangeTo", "to", "setUrlParamsFromLabelFilters", "lineFilter", "appendUrlParameter", "LineFilters", "escapeURLDelimiters", "stringifyValues", "setLineFilterUrlParams", "length", "field", "LabelType", "StructuredMetadata", "LEVEL_VARIABLE_VALUE", "Levels", "<PERSON><PERSON><PERSON>", "stringifyAdHocValues", "replaceEscapeChars", "fieldValue", "parser", "adHocFilterURLString", "JSON", "stringify", "stringifyAdHocValueLabels", "Fields", "setUrlParamsFromFieldFilters", "patterns", "push", "PatternFilterOp", "match", "pattern", "patternsString", "renderPatternFilters", "Patterns", "PatternsVariable", "setUrlParamsFromPatterns", "EMPTY_VARIABLE_VALUE", "addAdHocFilterUserInputPrefix", "labelFilter", "Indexed", "labelsAdHocFilterURLString", "Labels", "urlParams", "pluginJson", "VAR_DATASOURCE", "VAR_LABELS", "VAR_FIELDS", "VAR_METADATA", "VAR_LEVELS", "VAR_LINE_FILTERS", "VAR_PATTERNS", "initalParams", "searchParams", "locationService", "getSearch", "set", "location", "getLocation", "search", "append", "parameter", "stripAdHocFilterUserInputPrefix", "Symbol", "escapeUrlCommaDelimiters", "escapeUrlPipeDelimiters", "variable", "multi", "includeAll", "escapeLabelValueInExactSelector", "lodashMap", "lokiSpecialRegexEscape", "join", "LabelFilterOp", "LineFormatFilterOp", "NumericFilterOp", "FilterOp", "LineFilterOp", "LineFilterCaseSensitive", "defaultContext", "app", "version", "logger", "error", "err", "ctx", "console", "attemptFaroErr", "info", "msg", "attemptFaroInfo", "warn", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "logInfo", "e", "logWarning", "context2", "isRecord", "Object", "keys", "for<PERSON>ach", "hasData", "data", "populateFetchErrorContext", "Error", "logError", "NodePosition", "fromNode", "node", "contains", "position", "this", "getExpression", "query", "substring", "constructor", "syntaxNode", "getNodesFromQuery", "nodeTypes", "nodes", "parse", "iterate", "enter", "undefined", "includes", "getAllPositionsInNodeByType", "positions", "pos", "child", "childAfter", "parseNonPatternFilters", "lineFilterValue", "quoteString", "index", "isRegexSelector", "regex", "negativeRegex", "isCaseInsensitive", "replaceDoubleEscape", "RegExp", "replaceDoubleQuoteEscape", "caseInsensitive", "caseSensitive", "parsePatternFilters", "getNumericFieldOperator", "matcher", "<PERSON><PERSON>", "FilterOperator", "lte", "Lss", "lt", "Gte", "gte", "Gtr", "gt", "getStringFieldOperator", "Eq", "Equal", "Neq", "NotEqual", "Re", "RegexEqual", "Nre", "RegexNotEqual", "filter", "Selector", "filters", "allMatcher", "Matcher", "identifierPosition", "Identifier", "valuePosition", "String", "map", "parseLabelFilters", "dataFrame", "series", "frame", "refId", "allFields", "LabelFilter", "fieldNameNode", "expression", "<PERSON><PERSON><PERSON><PERSON>", "logFmtParser", "Logfmt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Json", "fieldName", "fieldStringValue", "fieldNumberValue", "Number", "fieldBytesValue", "Bytes", "fieldDurationValue", "Duration", "labelType", "getLabelTypeFromFrame", "Parsed", "parseFields", "allLineFilters", "LineFilter", "entries", "equal", "PipeExact", "pipeRegExp", "PipeMatch", "notEqual", "notEqualRegExp", "patternInclude", "PipePattern", "patternExclude", "Npa", "lineFilterValueNodes", "getStringsFromLineFilter", "lineFilterValueNode", "negativeMatch", "parseLineFilters", "ErrorId", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "nodeType", "isQueryWithNode", "string", "<PERSON><PERSON><PERSON><PERSON>", "LokiQueryDirection", "labelKey", "typeField", "name", "values", "isObj", "o", "hasProp", "prop", "isString", "s", "obj", "unknownToStrings", "strings", "Array", "isArray", "i", "narrowSelectedTableRow", "narrowed", "row", "narrowLogsVisualizationType", "narrowLogsSortOrder", "LogsSortOrder", "Ascending", "Descending", "narrowFieldValue", "narrowRecordStringNumber", "returnRecord", "narrowTimeRange", "<PERSON><PERSON><PERSON><PERSON>", "range", "narrowErrorMessage", "narrowFilterOperator", "op", "NarrowingError", "narrowPageOrValueSlug", "input", "narrowPageSlug", "narrowValueSlug", "label", "toLowerCase", "labels", "logs", "narrowDrilldownLabelFromSearchParams", "narrowPageSlugFromSearchParams", "narrowJsonDerivedFieldLinkPayload", "payload", "href", "isOperatorExclusive", "isOperatorRegex", "isOperatorNumeric", "numericOperatorArray", "getOperatorDescription", "operators", "array", "includeOperators", "numericOperators", "lineFilterOperators", "excludePatternsLine", "p", "trim", "includePatterns", "includePatternsLine", "VAR_LABELS_EXPR", "VAR_LABELS_REPLICA", "VAR_LABELS_REPLICA_EXPR", "VAR_FIELDS_EXPR", "PENDING_FIELDS_EXPR", "PENDING_METADATA_EXPR", "VAR_FIELDS_AND_METADATA", "VAR_METADATA_EXPR", "VAR_PATTERNS_EXPR", "VAR_LEVELS_EXPR", "VAR_FIELD_GROUP_BY", "VAR_LABEL_GROUP_BY", "VAR_LABEL_GROUP_BY_EXPR", "VAR_PRIMARY_LABEL_SEARCH", "VAR_PRIMARY_LABEL", "VAR_PRIMARY_LABEL_EXPR", "VAR_DATASOURCE_EXPR", "VAR_JSON_FIELDS", "VAR_JSON_FIELDS_EXPR", "VAR_LINE_FORMAT", "VAR_LINE_FORMAT_EXPR", "MIXED_FORMAT_EXPR", "JSON_FORMAT_EXPR", "LOGS_FORMAT_EXPR", "VAR_LOGS_FORMAT", "VAR_LOGS_FORMAT_EXPR", "VAR_LINE_FILTER", "VAR_LINE_FILTERS_EXPR", "LOG_STREAM_SELECTOR_EXPR", "DETECTED_FIELD_VALUES_EXPR", "DETECTED_FIELD_AND_METADATA_VALUES_EXPR", "DETECTED_LEVELS_VALUES_EXPR", "PATTERNS_SAMPLE_SELECTOR_EXPR", "PRETTY_LOG_STREAM_SELECTOR_EXPR", "EXPLORATION_DS", "ALL_VARIABLE_VALUE", "SERVICE_UI_LABEL", "VAR_AGGREGATED_METRICS", "USER_INPUT_ADHOC_VALUE_PREFIX", "startsWith", "isAdHocFilterValueUserInput", "module", "exports", "__WEBPACK_EXTERNAL_MODULE__6089__", "__WEBPACK_EXTERNAL_MODULE__7781__", "__WEBPACK_EXTERNAL_MODULE__8531__", "__WEBPACK_EXTERNAL_MODULE__2007__", "__WEBPACK_EXTERNAL_MODULE__3241__", "__WEBPACK_EXTERNAL_MODULE__1308__", "__WEBPACK_EXTERNAL_MODULE__5959__", "__WEBPACK_EXTERNAL_MODULE__8398__", "__WEBPACK_EXTERNAL_MODULE__200__", "__WEBPACK_EXTERNAL_MODULE__1159__", "__WEBPACK_EXTERNAL_MODULE__7694__", "__WEBPACK_EXTERNAL_MODULE__1269__", "De<PERSON>ult<PERSON><PERSON>er<PERSON><PERSON><PERSON>", "nextPropID", "Range", "NodeProp", "config", "perNode", "deserialize", "add", "RangeError", "NodeType", "result", "closedBy", "str", "openedBy", "group", "isolate", "contextHash", "lookAhead", "mounted", "MountedTree", "tree", "overlay", "get", "noProps", "create", "flags", "define", "spec", "top", "skipped", "src", "isTop", "isSkipped", "isError", "isAnonymous", "is", "indexOf", "direct", "groups", "found", "none", "NodeSet", "types", "extend", "newTypes", "newProps", "source", "assign", "CachedNode", "WeakMap", "CachedInnerNode", "IterMode", "Tree", "children", "ch", "test", "cursor", "mode", "TreeCursor", "topNode", "cursorAt", "side", "scope", "moveTo", "_tree", "TreeNode", "resolve", "resolveNode", "resolveInner", "resolveStack", "inner", "layers", "scan", "parent", "mount", "root", "iterStack", "stackIterator", "leave", "anon", "IncludeAnonymous", "c", "entered", "<PERSON><PERSON><PERSON><PERSON>", "nextS<PERSON>ling", "<PERSON><PERSON><PERSON><PERSON>", "balance", "balanceRange", "makeTree", "build", "_a", "buffer", "nodeSet", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reused", "minRepeatType", "FlatBufferCursor", "takeNode", "parentStart", "minPos", "inRepeat", "depth", "start", "end", "size", "lookAheadAtStart", "contextAtStart", "next", "startPos", "findBufferSize", "Uint16Array", "skip", "endPos", "copyToBuffer", "<PERSON><PERSON><PERSON><PERSON>", "localChildren", "localPositions", "localInRepeat", "lastGroup", "lastEnd", "makeRepeatLeaf", "takeFlatNode", "reverse", "make", "makeBalanced", "nodeCount", "stopAt", "j", "last", "lookAheadProp", "lastI", "base", "pop", "pair", "concat", "maxSize", "fork", "minStart", "nodeSize", "localSkipped", "nodeStart", "bufferStart", "startIndex", "topID", "buildTree", "empty", "childString", "endIndex", "<PERSON><PERSON><PERSON><PERSON>", "dir", "pick", "checkSide", "startI", "endI", "b", "copy", "len", "Math", "max", "overlays", "IgnoreOverlays", "BaseNode", "before", "after", "r", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "matchContext", "matchNodeContext", "enterUnfinishedNodesBefore", "childBefore", "<PERSON><PERSON><PERSON><PERSON>", "prevSibling", "_parent", "super", "<PERSON><PERSON><PERSON><PERSON>", "ExcludeBuffers", "BufferNode", "BufferContext", "<PERSON><PERSON><PERSON><PERSON>", "IgnoreMounts", "rPos", "nextSignificantParent", "val", "toTree", "cur", "externalSibling", "heads", "picked", "newHeads", "splice", "StackIterator", "stack", "bufferNode", "yieldNode", "n", "unshift", "yieldBuf", "yield", "enterChild", "sibling", "d", "atLastNode", "move", "prev", "cache", "mustLeave", "some", "nodeSizeCache", "balanceType", "mkTop", "mkTree", "total", "max<PERSON><PERSON><PERSON>", "ceil", "divide", "offset", "groupFrom", "groupStart", "groupSize", "nextSize", "only", "<PERSON><PERSON><PERSON>", "startParse", "fragments", "ranges", "StringInput", "createParse", "done", "advance", "chunk", "lineChunks", "read", "<PERSON><PERSON>", "state", "reducePos", "score", "bufferBase", "cur<PERSON><PERSON><PERSON><PERSON>", "_", "cx", "StackContext", "pushState", "reduce", "action", "lookaheadRecord", "setLookAhead", "dPrec", "dynamicPrecedence", "getGoto", "minRepeatTerm", "storeNode", "reduceContext", "lastBigReductionStart", "bigReductionCount", "lastBigReductionSize", "count", "stateFlag", "baseStateID", "term", "mustSink", "mustMove", "shift", "shiftContext", "maxNode", "nextState", "apply", "nextStart", "nextEnd", "useNode", "updateContext", "tracker", "reuse", "stream", "reset", "off", "recoverByDelete", "isNode", "canShift", "sim", "SimulatedStack", "stateSlot", "hasAction", "recoverByInsert", "nextStates", "best", "v", "forceReduce", "validAction", "backup", "findForcedReduction", "seen", "explore", "allActions", "r<PERSON><PERSON><PERSON>", "forceAll", "deadEnd", "restart", "sameState", "other", "dialectEnabled", "dialectID", "dialect", "emitContext", "hash", "emitLookAhead", "newCx", "close", "strict", "goto", "StackBufferCursor", "maybeNext", "decodeArray", "Type", "out", "charCodeAt", "stop", "digit", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "extended", "mask", "nullToken", "InputStream", "chunkOff", "chunk2", "chunk2Pos", "token", "rangeIndex", "chunkPos", "readNext", "resolveOffset", "assoc", "clipPos", "peek", "idx", "resolved", "acceptToken", "endOffset", "acceptTokenTo", "getChunk", "nextChunk", "setDone", "min", "TokenGroup", "readToken", "tokenPrecTable", "prototype", "contextual", "precTable", "precOffset", "groupMask", "accEnd", "allows", "overrides", "low", "high", "mid", "findOffset", "tableData", "tableOffset", "iPrev", "verbose", "process", "env", "LOG", "stackIDs", "cutAt", "fragment", "safeFrom", "safeTo", "trees", "nextFragment", "fr", "openStart", "openEnd", "nodeAt", "TokenCache", "tokens", "mainToken", "actions", "tokenizers", "getActions", "actionIndex", "main", "tokenizer", "updateCachedToken", "addActions", "eofTerm", "getMainToken", "specialized", "specializers", "putAction", "Parse", "recovering", "nextStackID", "minStackPos", "stoppedAt", "topTerm", "stacks", "bufferLength", "parsedPos", "stopped", "stoppedTokens", "newStacks", "advanceStack", "tok", "finished", "findFinished", "stackToTree", "SyntaxError", "runRecovery", "maxRemaining", "outer", "stackID", "strictCx", "cxHash", "cached", "defaultReduce", "localStack", "advanceFully", "pushStackDedup", "restarted", "tokenEnd", "force", "forceBase", "insert", "fromCodePoint", "Dialect", "<PERSON><PERSON><PERSON><PERSON>", "wrappers", "nodeNames", "repeatNodeCount", "topTerms", "topRules", "nodeProps", "setProp", "nodeID", "propSpec", "skippedNodes", "propSources", "tokenArray", "tokenData", "specializerSpecs", "getSpecializer", "states", "Uint32Array", "stateData", "maxTerm", "dialects", "dynamicPrecedences", "tokenPrec", "termNames", "parseDialect", "w", "loose", "table", "groupTag", "terminal", "slot", "flag", "deflt", "t", "external", "contextTracker", "wrap", "hasWrappers", "getName", "prec", "part", "Uint8Array", "keywordTokens", "json", "logfmt", "unpack", "regexp", "label_format", "line_format", "label_replace", "vector", "bool", "on", "ignoring", "group_left", "group_right", "unwrap", "decolorize", "drop", "keep", "contextualKeywordTokens", "by", "without", "and", "or", "unless", "sum", "avg", "stddev", "stdvar", "bottomk", "topk", "sort_desc", "spec_Identifier", "__proto__", "ip", "count_over_time", "rate", "rate_counter", "bytes_over_time", "bytes_rate", "avg_over_time", "sum_over_time", "min_over_time", "max_over_time", "stddev_over_time", "stdvar_over_time", "quantile_over_time", "first_over_time", "last_over_time", "absent_over_time", "bytes", "duration", "duration_seconds", "specializeIdentifier", "extendIdentifier", "MetricExpr", "__webpack_module_cache__", "__webpack_require__", "moduleId", "cachedModule", "loaded", "__webpack_modules__", "call", "m", "getter", "__esModule", "getPrototypeOf", "then", "ns", "def", "current", "getOwnPropertyNames", "definition", "defineProperty", "enumerable", "f", "chunkId", "promises", "u", "g", "globalThis", "Function", "window", "hasOwnProperty", "l", "url", "script", "<PERSON><PERSON><PERSON><PERSON>", "scripts", "document", "getElementsByTagName", "getAttribute", "createElement", "charset", "timeout", "nc", "setAttribute", "origin", "crossOrigin", "integrity", "sri<PERSON><PERSON><PERSON>", "onScriptComplete", "event", "onerror", "onload", "clearTimeout", "doneFns", "parentNode", "<PERSON><PERSON><PERSON><PERSON>", "fn", "setTimeout", "bind", "head", "append<PERSON><PERSON><PERSON>", "toStringTag", "nmd", "paths", "baseURI", "self", "installedChunks", "installedChunkData", "promise", "reject", "errorType", "realSrc", "message", "request", "webpackJsonpCallback", "parentChunkLoadingFunction", "chunkIds", "moreModules", "runtime", "chunkLoadingGlobal"], "sourceRoot": ""}