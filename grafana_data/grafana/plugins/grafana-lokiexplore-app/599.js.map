{"version": 3, "file": "599.js", "mappings": "msBAmBA,MAwEMA,EAAaC,IAA0B,CAC3CC,UAAWC,EAAAA,GAAG;aACHF,EAAMG,OAAOC,KAAKC;IAE7BC,UAAWJ,EAAAA,GAAG;kBACEF,EAAMO,QAAQ;IAE9BC,YAAaN,EAAAA,GAAG;kBACAF,EAAMO,QAAQ;IAE9BE,OAAOP,EAAAA,EAAAA,KAAI,CACTQ,QAAS,OACTC,WAAY,SACZC,aAAcZ,EAAMO,QAAQ,OAE9BM,MAAMX,EAAAA,EAAAA,KAAI,CACRY,WAAYd,EAAMO,QAAQ,OAIxBQ,G,EAAwB,aAAOC,EAAkBC,GACrD,UACQC,EAAaF,EAAUC,GAI7BE,EAAAA,gBAAgBC,QAClB,CAAE,MAAOC,GACPC,EAAAA,EAAOC,MAAM,kCACf,CACF,I,SAVqCP,EAAkBC,G,iCAAzB,I,EAY9B,MAAMO,EACO,CACTC,UAAW,2BACXC,SAAU,gCACVC,OAAQ,8BAICT,EAAe,W,MAAA,aAAOF,EAAkBC,GACnD,MAAMW,GAAWC,EAAAA,EAAAA,iBAAgBC,MAAM,CACrCC,IAAK,gBAAgBf,aACrBgB,OAAQ,OACRf,SAKF,aAF2BgB,EAAAA,EAAAA,eAAcL,IAErBX,IACtB,I,gBAVmCD,EAAkBC,G,gCAAzB,GAYtBiB,EAAWR,IACf,IACE,GAAIA,EAAU,CACZ,MAAMS,EAAUC,EAAAA,UAAUC,kBAAkBX,GAC5C,OAAOY,EAAAA,EAAAA,UAASH,IAAYA,GAhIL,IAiIzB,CAEE,OAAO,CAEX,CAAE,MAAOd,GAAI,CAEb,OAAO,CAAK,EAGd,EAtIkB,EAAGkB,aACnB,MAAMC,GAASC,EAAAA,EAAAA,YAAW1C,IACpB,QAAE2C,EAAO,OAAEC,EAAM,SAAEC,GAAaL,EAAOM,K,IAGjCD,EACOA,EAFnB,MAAOE,EAAOC,IAAYC,EAAAA,EAAAA,UAAgB,CACxCtB,SAA4B,QAAlBkB,EAAAA,aAAAA,EAAAA,EAAUlB,gBAAVkB,IAAAA,EAAAA,EAAsB,GAChCV,QAASA,EAA0B,QAAlBU,EAAAA,aAAAA,EAAAA,EAAUlB,gBAAVkB,IAAAA,EAAAA,EAAsB,MAYzC,OACE,kBAACK,MAAAA,CAAIC,cAAa1B,EAAkBC,WAClC,kBAAC0B,EAAAA,SAAQA,CAAC1C,MAAM,YACd,kBAAC2C,EAAAA,MAAKA,CACJC,SAAUnB,EAAQY,EAAMpB,UACxBH,MAAO,2FACP+B,YACE,kBAACC,OAAAA,KAAK,iKAEgD,kBAACC,KAAAA,MAAK,+BAI9D/C,MAAO,+BACPgD,UAAWjB,EAAOlC,WAElB,kBAACoD,EAAAA,MAAKA,CACJC,MAAO,GACPC,GAAG,WACHV,cAAa1B,EAAkBE,SAC/BjB,MAAO,eACPoD,MAAOf,aAAAA,EAAAA,EAAOpB,SACdoC,YAAa,KACbC,SAhCgBC,IACxB,MAAMtC,EAAWsC,EAAMC,OAAOJ,MAAMK,O,QACpCnB,G,wUAAS,IACJD,G,WAAAA,CACHpB,WACAQ,QAASA,EAAQR,K,sVA+Bf,kBAACuB,MAAAA,CAAIQ,UAAWjB,EAAOlC,WACrB,kBAAC6D,EAAAA,OAAMA,CACLC,KAAK,SACLlB,cAAa1B,EAAkBG,OAC/B0C,QAAS,IACPtD,EAAsBwB,EAAOM,KAAKe,GAAI,CACpClB,UACAC,SACAC,SAAU,CACRlB,SAAUoB,EAAMpB,YAItB4C,UAAWpC,EAAQY,EAAMpB,WAC1B,oB,gfC7EX,MAAM6C,EAAiB,CACrBC,I,QAAKC,GACLC,Q,SAGWpD,EAAS,CACpBqD,KAAM,CAACC,EAAaC,KAClB,MAAMC,EAAM,KAAKP,EAAmBM,GACpCE,QAAQC,IAAIJ,EAAKE,GACjBG,EAAgBL,EAAKE,EAAI,EAE3BI,KAAM,CAACN,EAAaC,KAClB,MAAMC,EAAM,KAAKP,EAAmBM,GACpCE,QAAQG,KAAKN,EAAKE,GAClBK,EAAgBP,EAAKE,EAAI,EAE3BvD,MAAO,CAAC6D,EAAsBP,KAC5B,MAAMC,EAAM,KAAKP,EAAmBM,GACpCE,QAAQxD,MAAM6D,EAAKN,GACnBO,EAAeD,EAAKN,EAAI,GAItBG,EAAkB,CAACL,EAAaC,KACpC,KACES,EAAAA,EAAAA,SAAQV,EAAKC,EACf,CAAE,MAAOxD,GACP0D,QAAQG,KAAK,4BACf,GAGIC,EAAkB,CAACP,EAAaC,KACpC,KACEU,EAAAA,EAAAA,YAAWX,EAAKC,EAClB,CAAE,MAAOxD,GACP0D,QAAQG,KAAK,8BAA+B,CAAEN,MAAKC,WACrD,GAkCIQ,EAAiB,CAACD,EAAmCI,KACzD,IAAIX,EAAUW,EACd,KA3BF,SAAmCJ,EAA2BP,GAC5D,GAAmB,iBAARO,GAA4B,OAARA,IAPiD,iBAQjEA,GACXK,OAAOC,KAAKN,GAAKO,SAASC,IACxB,MAAM/B,EAAQuB,EAAIQ,GACG,iBAAV/B,GAAuC,kBAAVA,GAAwC,iBAAVA,IACpEgB,EAAQe,GAAO/B,EAAMgC,WACvB,IAIAC,EAAQV,IACV,GAAwB,iBAAbA,EAAInE,MAAkC,OAAbmE,EAAInE,KACtC,IACE4D,EAAQ5D,KAAO8E,KAAKC,UAAUZ,EAAInE,KACpC,CAAE,MAAOI,GAET,KAC6B,iBAAb+D,EAAInE,MAAyC,kBAAbmE,EAAInE,MAA0C,iBAAbmE,EAAInE,OACrF4D,EAAQ5D,KAAOmE,EAAInE,KAAK4E,WAIhC,CAKII,CAA0Bb,EAAKP,GAE3BO,aAAec,OACjBC,EAAAA,EAAAA,UAASf,EAAKP,GACU,iBAARO,GAChBe,EAAAA,EAAAA,UAAS,IAAID,MAAMd,GAAMP,GAChBO,GAAsB,iBAARA,EACnBP,EAAQD,KACVuB,EAAAA,EAAAA,UAAS,IAAID,MAAMrB,EAAQD,KAAMC,IAEjCsB,EAAAA,EAAAA,UAAS,IAAID,MAAM,gBAAiBrB,IAGtCsB,EAAAA,EAAAA,UAAS,IAAID,MAAM,iBAAkBrB,EAEzC,CAAE,MAAOxD,GACP0D,QAAQxD,MAAM,4BAA6B,CAAE6D,MAAKP,WACpD,GAGIiB,EAAWjC,GACR,SAAUA,C", "sources": ["webpack://grafana-lokiexplore-app/./Components/AppConfig/AppConfig.tsx", "webpack://grafana-lokiexplore-app/./services/logger.ts"], "sourcesContent": ["import { getBackendSrv, locationService } from '@grafana/runtime';\nimport { AppPluginMeta, GrafanaTheme2, PluginConfigPageProps, PluginMeta, rangeUtil } from '@grafana/data';\nimport { lastValueFrom } from 'rxjs';\nimport { css } from '@emotion/css';\nimport { Button, Field, FieldSet, Input, useStyles2 } from '@grafana/ui';\nimport React, { ChangeEvent, useState } from 'react';\nimport { isNumber } from 'lodash';\nimport { logger } from '../../services/logger';\n\nexport type JsonData = {\n  interval?: string;\n};\n\ntype State = {\n  interval: string;\n  isValid: boolean;\n};\n\n// 1 hour minimum\nconst MIN_INTERVAL_SECONDS = 3600;\n\ninterface Props extends PluginConfigPageProps<AppPluginMeta<JsonData>> {}\n\nconst AppConfig = ({ plugin }: Props) => {\n  const styles = useStyles2(getStyles);\n  const { enabled, pinned, jsonData } = plugin.meta;\n\n  const [state, setState] = useState<State>({\n    interval: jsonData?.interval ?? '',\n    isValid: isValid(jsonData?.interval ?? ''),\n  });\n\n  const onChangeInterval = (event: ChangeEvent<HTMLInputElement>) => {\n    const interval = event.target.value.trim();\n    setState({\n      ...state,\n      interval,\n      isValid: isValid(interval),\n    });\n  };\n\n  return (\n    <div data-testid={testIds.appConfig.container}>\n      <FieldSet label=\"Settings\">\n        <Field\n          invalid={!isValid(state.interval)}\n          error={'Interval is invalid. Please enter an interval longer then \"60m\". For example: 3d, 1w, 1m'}\n          description={\n            <span>\n              The maximum interval that can be selected in the time picker within the Explore Logs app. If empty, users\n              can select any time range interval in Explore Logs. <br />\n              Example values: 7d, 24h, 2w\n            </span>\n          }\n          label={'Maximum time picker interval'}\n          className={styles.marginTop}\n        >\n          <Input\n            width={60}\n            id=\"interval\"\n            data-testid={testIds.appConfig.interval}\n            label={`Max interval`}\n            value={state?.interval}\n            placeholder={`7d`}\n            onChange={onChangeInterval}\n          />\n        </Field>\n\n        <div className={styles.marginTop}>\n          <Button\n            type=\"submit\"\n            data-testid={testIds.appConfig.submit}\n            onClick={() =>\n              updatePluginAndReload(plugin.meta.id, {\n                enabled,\n                pinned,\n                jsonData: {\n                  interval: state.interval,\n                },\n              })\n            }\n            disabled={!isValid(state.interval)}\n          >\n            Save settings\n          </Button>\n        </div>\n      </FieldSet>\n    </div>\n  );\n};\n\nconst getStyles = (theme: GrafanaTheme2) => ({\n  colorWeak: css`\n    color: ${theme.colors.text.secondary};\n  `,\n  marginTop: css`\n    margin-top: ${theme.spacing(3)};\n  `,\n  marginTopXl: css`\n    margin-top: ${theme.spacing(6)};\n  `,\n  label: css({\n    display: 'flex',\n    alignItems: 'center',\n    marginBottom: theme.spacing(0.75),\n  }),\n  icon: css({\n    marginLeft: theme.spacing(1),\n  }),\n});\n\nconst updatePluginAndReload = async (pluginId: string, data: Partial<PluginMeta<JsonData>>) => {\n  try {\n    await updatePlugin(pluginId, data);\n\n    // Reloading the page as the changes made here wouldn't be propagated to the actual plugin otherwise.\n    // This is not ideal, however unfortunately currently there is no supported way for updating the plugin state.\n    locationService.reload();\n  } catch (e) {\n    logger.error('Error while updating the plugin');\n  }\n};\n\nconst testIds = {\n  appConfig: {\n    container: 'data-testid ac-container',\n    interval: 'data-testid ac-interval-input',\n    submit: 'data-testid ac-submit-form',\n  },\n};\n\nexport const updatePlugin = async (pluginId: string, data: Partial<PluginMeta>) => {\n  const response = getBackendSrv().fetch({\n    url: `/api/plugins/${pluginId}/settings`,\n    method: 'POST',\n    data,\n  });\n\n  const dataResponse = await lastValueFrom(response);\n\n  return dataResponse.data;\n};\n\nconst isValid = (interval: string): boolean => {\n  try {\n    if (interval) {\n      const seconds = rangeUtil.intervalToSeconds(interval);\n      return isNumber(seconds) && seconds >= MIN_INTERVAL_SECONDS;\n    } else {\n      // Empty strings are fine\n      return true;\n    }\n  } catch (e) {}\n\n  return false;\n};\n\nexport default AppConfig;\n", "import { LogContext } from '@grafana/faro-web-sdk';\nimport { FetchError, logError, logInfo, logWarning } from '@grafana/runtime';\nimport pluginJson from '../plugin.json';\nimport packageJson from '../../package.json';\n\nconst defaultContext = {\n  app: pluginJson.id,\n  version: packageJson.version,\n};\n\nexport const logger = {\n  info: (msg: string, context?: LogContext) => {\n    const ctx = { ...defaultContext, ...context };\n    console.log(msg, ctx);\n    attemptFaroInfo(msg, ctx);\n  },\n  warn: (msg: string, context?: LogContext) => {\n    const ctx = { ...defaultContext, ...context };\n    console.warn(msg, ctx);\n    attemptFaroWarn(msg, ctx);\n  },\n  error: (err: Error | unknown, context?: LogContext) => {\n    const ctx = { ...defaultContext, ...context };\n    console.error(err, ctx);\n    attemptFaroErr(err, ctx);\n  },\n};\n\nconst attemptFaroInfo = (msg: string, context?: LogContext) => {\n  try {\n    logInfo(msg, context);\n  } catch (e) {\n    console.warn('Failed to log faro event!');\n  }\n};\n\nconst attemptFaroWarn = (msg: string, context?: LogContext) => {\n  try {\n    logWarning(msg, context);\n  } catch (e) {\n    console.warn('Failed to log faro warning!', { msg, context });\n  }\n};\n\nconst isRecord = (obj: unknown): obj is Record<string, unknown> => typeof obj === 'object';\n/**\n * Checks unknown error for properties from Records like FetchError and adds them to the context\n * @param err\n * @param context\n */\nfunction populateFetchErrorContext(err: unknown | FetchError, context: LogContext) {\n  if (typeof err === 'object' && err !== null) {\n    if (isRecord(err)) {\n      Object.keys(err).forEach((key: string) => {\n        const value = err[key];\n        if (typeof value === 'string' || typeof value === 'boolean' || typeof value === 'number') {\n          context[key] = value.toString();\n        }\n      });\n    }\n\n    if (hasData(err)) {\n      if (typeof err.data === 'object' && err.data !== null) {\n        try {\n          context.data = JSON.stringify(err.data);\n        } catch (e) {\n          // do nothing\n        }\n      } else if (typeof err.data === 'string' || typeof err.data === 'boolean' || typeof err.data === 'number') {\n        context.data = err.data.toString();\n      }\n    }\n  }\n}\n\nconst attemptFaroErr = (err: Error | FetchError | unknown, context2: LogContext) => {\n  let context = context2;\n  try {\n    populateFetchErrorContext(err, context);\n\n    if (err instanceof Error) {\n      logError(err, context);\n    } else if (typeof err === 'string') {\n      logError(new Error(err), context);\n    } else if (err && typeof err === 'object') {\n      if (context.msg) {\n        logError(new Error(context.msg), context);\n      } else {\n        logError(new Error('error object'), context);\n      }\n    } else {\n      logError(new Error('unknown error'), context);\n    }\n  } catch (e) {\n    console.error('Failed to log faro error!', { err, context });\n  }\n};\n\nconst hasData = (value: object): value is { data: unknown } => {\n  return 'data' in value;\n};\n"], "names": ["getStyles", "theme", "colorWeak", "css", "colors", "text", "secondary", "marginTop", "spacing", "marginTopXl", "label", "display", "alignItems", "marginBottom", "icon", "marginLeft", "updatePluginAndReload", "pluginId", "data", "updatePlugin", "locationService", "reload", "e", "logger", "error", "testIds", "container", "interval", "submit", "response", "getBackendSrv", "fetch", "url", "method", "lastValueFrom", "<PERSON><PERSON><PERSON><PERSON>", "seconds", "rangeUtil", "intervalToSeconds", "isNumber", "plugin", "styles", "useStyles2", "enabled", "pinned", "jsonData", "meta", "state", "setState", "useState", "div", "data-testid", "FieldSet", "Field", "invalid", "description", "span", "br", "className", "Input", "width", "id", "value", "placeholder", "onChange", "event", "target", "trim", "<PERSON><PERSON>", "type", "onClick", "disabled", "defaultContext", "app", "pluginJson", "version", "info", "msg", "context", "ctx", "console", "log", "attemptFaroInfo", "warn", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "err", "attemptFaroErr", "logInfo", "logWarning", "context2", "Object", "keys", "for<PERSON>ach", "key", "toString", "hasData", "JSON", "stringify", "populateFetchErrorContext", "Error", "logError"], "sourceRoot": ""}