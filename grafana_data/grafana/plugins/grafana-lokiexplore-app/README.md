# Explore Logs

Explore Logs offers a query-less experience for browsing Loki logs without the need for writing complex queries. Discover or narrow down your search using by volume and text patterns. Uncover related logs and understand patterns—all with just a few clicks.

## Requirements

Requires Grafana 11.3.0 or newer.

## Getting Started

See the [getting started](https://grafana.com/docs/grafana-cloud/visualizations/simplified-exploration/logs/get-started/) for more info using Explore Logs.
For instructions installing, see the [access and installation instructions](https://grafana.com/docs/grafana-cloud/visualizations/simplified-exploration/logs/access/)

## Documentation

- [DOCS](https://grafana.com/docs/grafana-cloud/visualizations/simplified-exploration/logs/)
- [CHANGELOG](https://github.com/grafana/explore-logs/releases)
- [GITHUB](https://github.com/grafana/explore-logs/)

## Contributing

We love accepting contributions!
If your change is minor, please feel free submit
a [pull request](https://help.github.com/articles/about-pull-requests/).
If your change is larger, or adds a feature, please file an issue beforehand so
that we can discuss the change. You're welcome to file an implementation pull
request immediately as well, although we generally lean towards discussing the
change and then reviewing the implementation separately.

### Bugs

If your issue is a bug, please open one [here](https://github.com/grafana/explore-logs/issues/new).

### Changes

We do not have a formal proposal process for changes or feature requests. If you have a change you would like to see in
Explore Logs, please [file an issue](https://github.com/grafana/explore-logs/issues/new) with the necessary details.
