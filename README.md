# Flink SQL Real-Time IoT Data Processing Demo

A comprehensive Apache Flink streaming data processing demonstration that showcases real-time analytics on IoT station data. This project simulates a charging station rental service with complete data pipeline from IoT devices to analytics dashboards.

## 🏗️ Architecture Overview

```
IoT Generator (Python) → Kafka → Flink SQL → ClickHouse → Grafana
                    ↗                    ↗
PostgreSQL (CDC) → Debezium → Kafka → Flink SQL
```

### Core Components

- **Apache Flink**: Stream processing engine with SQL interface
- **PostgreSQL**: Operational database with PostGIS for geospatial data
- **Redpanda (Kafka)**: High-performance message streaming
- **ClickHouse**: Analytics database for processed results
- **Debezium**: Change Data Capture (CDC) connector
- **Grafana**: Real-time visualization dashboards
- **Python IoT Generator**: Simulates charging station status events

## 🚀 Quick Start

### Prerequisites

- Docker & Docker Compose
- 8GB+ RAM recommended
- Ports available: 3000, 5432, 8081, 8083, 8123, 9000, 18081, 19092

### 1. Start All Services

```bash
# Start the complete stack
docker-compose up -d

# Verify all services are running
docker-compose ps
```

### 2. Submit Flink Jobs

```bash
# Submit CDC processing job
docker-compose exec jobmanager flink run /opt/flink/output/flink-job.jar cdc

# Submit IoT analytics job (5-second windows)
docker-compose exec jobmanager flink run /opt/flink/output/flink-job.jar iot 5
```

### 3. Verify Data Flow

```bash
# Check ClickHouse tables
docker-compose exec clickhouse clickhouse-client --query "SHOW TABLES FROM cf"

# View processed data
docker-compose exec clickhouse clickhouse-client --query "SELECT COUNT(*) FROM cf.station_status_merge_tree"

# See latest records
docker-compose exec clickhouse clickhouse-client --query "SELECT * FROM cf.station_status_merge_tree ORDER BY event_date DESC LIMIT 10"
```

## 📊 Access Points

| Service | URL | Purpose |
|---------|-----|---------|
| Flink Web UI | http://localhost:8081 | Monitor streaming jobs |
| Grafana | http://localhost:3000 | Visualization dashboards |
| ClickHouse | http://localhost:8123 | Query analytics data |
| Debezium | http://localhost:8083 | CDC connector status |

## 🔧 Configuration

### IoT Generator Modes

The Python IoT generator supports two modes via environment variables:

```yaml
# docker-compose.yml
environment:
  MODE: RANDOM_GEN  # or ONE_GEN
  DELAY: 200        # milliseconds between messages
  KAFKA_TOPIC: cf.iot.station.status
```

- **RANDOM_GEN**: Sends random on/off status for multiple stations (25% on, 75% off)
- **ONE_GEN**: Sends "off" status for a single station repeatedly

### Database Configuration

```yaml
environment:
  DATABASE_HOST: postgres
  DATABASE_NAME: service-rental-demo
  DATABASE_USERNAME: admin
  DATABASE_PASSWORD: admin
```

## 📈 Data Pipeline Details

### 1. IoT Data Flow

```
Python Generator → Kafka Topic → Flink SQL → ClickHouse
```

**Message Format:**
```json
{
  "id": "station-uuid",
  "status": "on" | "off"
}
```

### 2. CDC Data Flow

```
PostgreSQL Changes → Debezium → Kafka → Flink SQL → ClickHouse
```

**Processed Data:**
- Station information with geospatial coordinates
- Address data joined with station locations
- Real-time synchronization of operational changes

## 🔍 Monitoring & Debugging

### Check Service Status

```bash
# View all container status
docker-compose ps

# Check specific service logs
docker-compose logs fake-iot-generator -f
docker-compose logs jobmanager --tail=50
docker-compose logs taskmanager --tail=50
```

### Verify Data Generation

```bash
# Monitor IoT generator output
docker-compose logs fake-iot-generator --tail=20

# Check Kafka topics
docker-compose exec redpanda rpk topic list
docker-compose exec redpanda rpk topic consume cf.iot.station.status --num 5
```

### Query Analytics Data

```bash
# Connect to ClickHouse
docker-compose exec clickhouse clickhouse-client

# Sample queries
SELECT COUNT(*) FROM cf.station_status_merge_tree;
SELECT station_id, COUNT(*) as events FROM cf.station_status_merge_tree GROUP BY station_id;
SELECT * FROM cf.station_status_merge_tree WHERE event_date > now() - INTERVAL 1 MINUTE;
```

## 🛠️ Development

### Modify IoT Generator

The Python IoT generator is located in `fake-iot-generator-python/main.py`. Key functions:

- `random_payload_generator_situation()`: Multi-station random data
- `one_station_payload_generator_situation()`: Single station data
- `twenty_five_percent_is_true()`: Status probability logic

### Rebuild Services

```bash
# Rebuild specific service
docker-compose build fake-iot-generator

# Restart with new build
docker-compose up fake-iot-generator -d
```

### Flink Job Development

Flink jobs are in `flink-sql/src/main/java/org/myorg/quickstart/DataStreamJob.java`:

- `debeziumPostgresqlJob()`: CDC processing
- `countOffline()`: IoT window aggregation

## 🔧 Troubleshooting

### Common Issues

1. **Services not starting**: Check port conflicts and available memory
2. **No data in ClickHouse**: Verify Flink jobs are submitted and running
3. **IoT generator failing**: Check PostgreSQL connection and Kafka availability

### Reset Environment

```bash
# Stop all services
docker-compose down

# Remove volumes (WARNING: deletes all data)
docker-compose down -v

# Clean rebuild
docker-compose build --no-cache
docker-compose up -d
```

## 📚 Learn More

- [Apache Flink Documentation](https://flink.apache.org/docs/)
- [ClickHouse Documentation](https://clickhouse.com/docs/)
- [Debezium Documentation](https://debezium.io/documentation/)
- [Redpanda Documentation](https://docs.redpanda.com/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test the complete pipeline
5. Submit a pull request

## 🎯 Use Cases Demonstrated

### Real-Time Stream Processing
- **IoT Data Ingestion**: Continuous processing of device status messages
- **Window Aggregations**: Time-based analytics with tumbling windows
- **Event-Time Processing**: Handling out-of-order events and late arrivals

### Change Data Capture (CDC)
- **Database Synchronization**: Real-time replication of operational changes
- **Data Lake Integration**: Streaming database changes to analytics systems
- **Geospatial Processing**: Converting PostGIS geometry to coordinate points

### Analytics and Visualization
- **Real-Time Dashboards**: Live metrics and KPIs in Grafana
- **OLAP Queries**: Fast analytical queries on ClickHouse
- **Time Series Analysis**: Historical trend analysis and monitoring

## 🔄 Data Processing Patterns

### 1. Stream-to-Stream Joins
```sql
-- Example: Join IoT events with station metadata
SELECT s.id, s.status, m.location
FROM station_status s
JOIN station_metadata m ON s.id = m.station_id
```

### 2. Windowed Aggregations
```sql
-- Example: Count offline events per 5-minute window
SELECT
  TUMBLE_START(ts, INTERVAL '5' MINUTE) as window_start,
  COUNT(*) as offline_count
FROM station_status
WHERE status = 'off'
GROUP BY TUMBLE(ts, INTERVAL '5' MINUTE)
```

### 3. Complex Event Processing
```sql
-- Example: Detect stations offline for more than 10 minutes
SELECT station_id, COUNT(*) as consecutive_offline
FROM station_status
WHERE status = 'off'
GROUP BY station_id, SESSION(ts, INTERVAL '10' MINUTE)
HAVING COUNT(*) > 2
```

## 📊 Sample Queries

### ClickHouse Analytics Queries

```sql
-- Total events by station
SELECT station_id, SUM(offline_count) as total_offline_events
FROM cf.station_status_merge_tree
GROUP BY station_id
ORDER BY total_offline_events DESC;

-- Hourly offline patterns
SELECT
  toHour(event_date) as hour,
  AVG(offline_count) as avg_offline_events
FROM cf.station_status_merge_tree
WHERE event_date >= now() - INTERVAL 1 DAY
GROUP BY hour
ORDER BY hour;

-- Station availability percentage
SELECT
  station_id,
  (1 - SUM(offline_count) / COUNT(*)) * 100 as availability_pct
FROM cf.station_status_merge_tree
GROUP BY station_id;
```

## 🔧 Advanced Configuration

### Scaling the System

**Horizontal Scaling**:
```yaml
# docker-compose.yml
taskmanager:
  scale: 3  # Run 3 TaskManager instances
```

**Kafka Partitioning**:
```bash
# Create topic with multiple partitions
docker-compose exec redpanda rpk topic create cf.iot.station.status --partitions 4
```

**ClickHouse Clustering**:
```yaml
# Add ClickHouse cluster configuration
clickhouse:
  environment:
    CLICKHOUSE_CONFIG: |
      <clickhouse>
        <remote_servers>
          <cluster_name>
            <shard><replica><host>clickhouse1</host></replica></shard>
            <shard><replica><host>clickhouse2</host></replica></shard>
          </cluster_name>
        </remote_servers>
      </clickhouse>
```

### Performance Tuning

**Flink Configuration**:
```yaml
environment:
  - |
    FLINK_PROPERTIES=
    taskmanager.memory.process.size: 2gb
    taskmanager.numberOfTaskSlots: 4
    parallelism.default: 4
    state.backend: rocksdb
    state.checkpoints.dir: file:///tmp/flink-checkpoints
```

**ClickHouse Optimization**:
```sql
-- Optimize table for time-series data
CREATE TABLE cf.station_status_optimized (
    station_id String,
    event_date DateTime,
    offline_count UInt32
) ENGINE = MergeTree()
PARTITION BY toYYYYMM(event_date)
ORDER BY (station_id, event_date)
SETTINGS index_granularity = 8192;
```

## 📄 License

This project is for educational and demonstration purposes.
