#!/usr/bin/env python3
"""
Main application for fake data generator.
Orchestrates the generation and sending of fake data to Kafka topics.
"""

import json
import os
import random
import time
import uuid
from datetime import datetime
from typing import List, Tuple

import psycopg2
from kafka import KafkaProducer

# Import from our modular structure
from generators.station_generator import StationGenerator
from utils.kafka_utils import prepare_producer_config, send_message
from utils.db_utils import prepare_db_connection, get_station_ids
from utils.common_utils import twenty_five_percent_is_true, get_current_timestamp


def random_payload_generator_situation(connection: psycopg2.extensions.connection,
                                       producer: KafkaProducer) -> None:
    """
    Generate random station status messages for multiple stations.

    Args:
        connection: PostgreSQL database connection
        producer: Kafka producer instance
    """
    # Fetch station IDs from database
    station_ids = get_station_ids(connection, limit=100)

    topic = os.getenv("KAFKA_TOPIC", "cf.iot.station.status")
    delay = int(os.getenv("DELAY", "300")) / 1000.0  # Convert ms to seconds

    print(
        f"Start sending station log with [topic={topic}, delay={delay*1000}ms]")

    # Use the station generator
    station_generator = StationGenerator()

    for iteration in range(1000):
        random.shuffle(station_ids)
        for station_id in station_ids:
            print(f"Start sending data to topic {topic}")

            # Generate station status with customer field
            station_status = station_generator.generate_station_status(
                str(station_id))
            message = station_status.to_json()

            # Send to Kafka
            send_message(producer, topic, message, key=str(station_id))

            print(
                f"Send station[id={station_status.id}, status={station_status.status}, customer={station_status.customer}] successfully")
            time.sleep(delay)

    print("End sending station log")
    producer.flush()


def one_station_payload_generator_situation(connection: psycopg2.extensions.connection,
                                            producer: KafkaProducer) -> None:
    """
    Generate status messages for a single station.

    Args:
        connection: PostgreSQL database connection
        producer: Kafka producer instance
    """
    # Fetch one station ID from database
    station_ids = get_station_ids(connection, limit=1)
    if not station_ids:
        print("No stations found in database")
        return

    station_id = str(station_ids[0])

    topic = os.getenv("KAFKA_TOPIC", "cf.iot.station.status")
    delay = int(os.getenv("DELAY", "300")) / 1000.0  # Convert ms to seconds

    print(
        f"Start sending station log with [topic={topic}, delay={delay*1000}ms]")

    # Use the station generator
    station_generator = StationGenerator()

    for i in range(10):
        print(f"Start sending data to topic {topic}")

        # Generate station status with customer field
        station_status = station_generator.generate_station_status(
            station_id, status="off")
        message = station_status.to_json()

        # Send to Kafka
        send_message(producer, topic, message, key=station_id)

        timestamp = get_current_timestamp()
        print(
            f"Send station[id={station_status.id}, status={station_status.status}, customer={station_status.customer}], timestamp={timestamp} successfully")
        time.sleep(0.5)  # 500ms delay

    print("End sending station log")
    producer.flush()


def main():
    """Main function - entry point of the application."""
    kafka_host = os.getenv("KAFKA_HOST", "localhost")
    kafka_port = os.getenv("KAFKA_PORT", "19092")

    # Create Kafka producer
    producer = prepare_producer_config(kafka_host, kafka_port)

    # Create database connection
    connection, jdbc_url = prepare_db_connection()

    print(f"Connect {jdbc_url} successfully")
    print(f"Connect {kafka_host}:{kafka_port} successfully")

    # Determine mode and run appropriate generator
    mode = os.getenv("MODE", "RANDOM_GEN")

    if mode == "RANDOM_GEN":
        random_payload_generator_situation(connection, producer)
    elif mode == "ONE_GEN":
        one_station_payload_generator_situation(connection, producer)
    else:
        print(f"We do not have mode {mode}")

    # Clean up
    connection.close()
    producer.close()


if __name__ == "__main__":
    main()
