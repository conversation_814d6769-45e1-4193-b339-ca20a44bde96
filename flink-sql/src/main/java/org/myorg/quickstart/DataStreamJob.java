/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.myorg.quickstart;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.table.api.*;
import org.apache.flink.table.functions.ScalarFunction;

import java.nio.ByteBuffer;
import java.nio.ByteOrder;

/**
 * Skeleton for a Flink DataStream Job.
 *
 * <p>For a tutorial how to write a Flink application, check the
 * tutorials and examples on the <a href="https://flink.apache.org">Flink Website</a>.
 *
 * <p>To package your application into a JAR file for execution, run
 * 'mvn clean package' on the command line.
 *
 * <p>If you change the name of the main class (with the public static void main(String[] args))
 * method, change the respective entry in the POM.xml file (simply search for 'mainClass').
 */
public class DataStreamJob {

	public static class ConvertStringToGeoPoint extends ScalarFunction {

		private static String parseWKB(ByteBuffer buffer){
			int byteOrder = buffer.get(); // The first byte indicates byte order (0 for big endian, 1 for little endian)
			buffer.order(byteOrder == 0 ? ByteOrder.BIG_ENDIAN : ByteOrder.LITTLE_ENDIAN);

			int wkbType = buffer.getInt();
			int srid = buffer.getInt(); // Get SRID

			if (srid == 4326) { // WKBType 1 is Point
				double x = buffer.getDouble(); // Longitude (or X)
				double y = buffer.getDouble(); // Latitude (or Y)
				return "(" + x + "," + y + ")";
			} else {
				throw new RuntimeException("The provided WKB does not represent a Point with SRID 4326. expected SRID = " + srid);
			}
		}

		// Utility method to convert hex string to byte array
		private static byte[] hexStringToByteArray(String s) {
			int len = s.length();
			byte[] data = new byte[len / 2];
			for (int i = 0; i < len; i += 2) {
				data[i / 2] = (byte) ((Character.digit(s.charAt(i), 16) << 4)
						+ Character.digit(s.charAt(i+1), 16));
			}
			return data;
		}
		public String eval(String geoString) {
			if (geoString == null) return null;
			byte[] wkb = hexStringToByteArray(geoString);

			ByteBuffer wkbBuffer = ByteBuffer.wrap(wkb);
			wkbBuffer.order(ByteOrder.LITTLE_ENDIAN); // WKB is little endian

			return parseWKB(wkbBuffer);
		}
	}

	public static void debeziumPostgresqlJob() {
		// Sets up the execution environment, which is the main entry point
		// to building Flink applications.
		Configuration configuration = new Configuration();
		configuration.setString("table.exec.sink.not-null-enforcer", "DROP");
		EnvironmentSettings settings = EnvironmentSettings
				.newInstance()
				.withConfiguration(configuration)
				.inStreamingMode()
				.build();


		TableEnvironment tEnv = TableEnvironment.create(settings);

		tEnv.createTemporarySystemFunction("ConvertStringToGeoPoint", ConvertStringToGeoPoint.class);
		tEnv.executeSql("CREATE TABLE view_addresses (\n" +
				"  `id` STRING NOT NULL,\n" +
				"  `city` STRING,\n" +
				"   `location` STRING\n" +
				") WITH (\n" +
				"    'connector' = 'jdbc',\n" +
				"    'url' = '***************************************************',\n" +
				"    'username' = 'admin',\n" +
				"    'password' = 'admin',\n" +
				"    'table-name' = 'public.view_addresses'\n" +
				");");
		tEnv.executeSql("CREATE TABLE stations(\n" +
				"  id STRING,\n" +
				"  imei STRING,\n" +
				"  serial_number STRING,\n" +
				"  address_id STRING\n" +
				") WITH (\n" +
				"  'connector' = 'kafka',\n" +
				"  'topic' = 'cf.debezium.public.stations',\n" +
				"  'properties.bootstrap.servers' = 'redpanda:9092',\n" +
				"  'properties.group.id' = 'test-consumer-group',\n" +
				"  'format' = 'debezium-json',\n" +
				"  'scan.startup.mode' = 'latest-offset'\n" +
				" );");
		tEnv.executeSql("CREATE TABLE cdc_station (\n" +
				"  `id` STRING,\n" +
				"  `imei` STRING,\n" +
				"  `serial_number` STRING, \n" +
				"  `location` STRING, \n" +
				"  PRIMARY KEY (id) NOT ENFORCED\n" +
				") WITH (\n" +
				"  'connector' = 'upsert-kafka',\n" +
				"  'topic' = 'cf.data_platform.debezium.station',\n" +
				"  'properties.bootstrap.servers' = 'redpanda:9092',\n" +
				"  'key.format' = 'json',\n" +
				"  'value.format' = 'json'\n" +
				");");
		Table station_address = tEnv.sqlQuery("SELECT stations.id, stations.imei, stations.serial_number, ConvertStringToGeoPoint(view_addresses.location) \n" +
				"FROM stations LEFT JOIN view_addresses ON stations.address_id = view_addresses.id");
		station_address.executeInsert("cdc_station");
	}

	public static void countOffline(Integer interval) {
		// Sets up the execution environment, which is the main entry point
		// to building Flink applications.
		Configuration configuration = new Configuration();
		configuration.setString("table.exec.sink.not-null-enforcer", "DROP");
		EnvironmentSettings settings = EnvironmentSettings
				.newInstance()
				.withConfiguration(configuration)
				.inStreamingMode()
				.build();

		TableEnvironment tEnv = TableEnvironment.create(settings);
		tEnv.executeSql("CREATE TABLE station_status (\n" +
				"  `id` STRING,\n" +
				"  `status` STRING,\n" +
				"   `ts` TIMESTAMP(3) METADATA FROM 'timestamp',\n" +
				"    WATERMARK FOR ts AS ts - INTERVAL '0' SECOND\n" +
				") WITH (\n" +
				"  'connector' = 'kafka',\n" +
				"  'topic' = 'cf.iot.station.status',\n" +
				"  'properties.bootstrap.servers' = 'redpanda:9092',\n" +
				"  'properties.group.id' = 'test-group',\n" +
				"  'scan.startup.mode' = 'latest-offset',\n" +
				"  'format' = 'json'\n" +
				");");
		tEnv.executeSql("CREATE VIEW station_status_view AS select id, ts, status from (\n" +
				"    select\n" +
				"        *,\n" +
				"        lag(status, 1) over (partition by id order by ts) as previous_status\n" +
				"    from station_status\n" +
				")\n" +
				"where status = 'on' or (status = 'off' and (previous_status is null or previous_status = 'on'));");

		tEnv.executeSql("CREATE TABLE station_offline_status (\n" +
				"  `window_start` TIMESTAMP(3),\n" +
				"  `window_end` TIMESTAMP(3),\n" +
				"  `station_id` STRING,\n" +
				"  `the_number_of_offline_status` BIGINT,\n" +
				"  PRIMARY KEY (station_id) NOT ENFORCED\n" +
				") WITH (\n" +
				"  'connector' = 'upsert-kafka',\n" +
				"  'topic' = 'cf.data_platform.station.status',\n" +
				"  'properties.bootstrap.servers' = 'redpanda:9092',\n" +
				"  'key.format' = 'json',\n" +
				"  'value.format' = 'json'\n" +
				");");
		Table offlineResult = tEnv.sqlQuery("SELECT window_start, window_end, id as station_id, COUNT(1) as the_number_of_offline_status\n" +
				"from table(\n" +
				"   TUMBLE(TABLE station_status_view, DESCRIPTOR(ts), INTERVAL '" + interval + "' SECONDS))\n" +
				" WHERE status = 'off'\n" +
				" GROUP BY window_start, window_end, GROUPING SETS ((id), ());");
		offlineResult.executeInsert("station_offline_status");
	}

	public static void main(String[] args) {
		String mode = args[0];

		if (mode.equals("iot")) {
			Integer interval = 2;
			if (args.length > 1) {
				interval = Integer.valueOf(args[1]);
			}
			System.out.println("" + interval);
			countOffline(interval);
		} else {
			debeziumPostgresqlJob();
		}
	}
}


