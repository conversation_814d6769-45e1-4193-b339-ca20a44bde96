#!/usr/bin/env python3
"""
Python version of the fake IoT generator for Flink SQL demo.
Generates station status messages and sends them to Kafka.
"""

import json
import os
import random
import time
import uuid
from datetime import datetime
from typing import List, Tuple

import psycopg2
from kafka import KafkaProducer


class StationStatus:
    """Data class representing a station status message."""
    
    def __init__(self, station_id: str = None, status: str = "off"):
        self.id = station_id if station_id else str(uuid.uuid4())
        self.status = status
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization."""
        return {
            "id": self.id,
            "status": self.status
        }


def twenty_five_percent_is_true() -> bool:
    """
    Returns True approximately 25% of the time.
    Equivalent to <PERSON><PERSON><PERSON>'s `25PercentageIsTrue()` function.
    """
    return not (random.choice([True, False]) and random.choice([True, False]))


def prepare_producer_config(kafka_host: str, kafka_port: str) -> KafkaProducer:
    """
    Create and configure Kafka producer.
    
    Args:
        kafka_host: Kafka broker hostname
        kafka_port: Kafka broker port
        
    Returns:
        Configured KafkaProducer instance
    """
    producer_config = {
        'bootstrap_servers': f'{kafka_host}:{kafka_port}',
        'key_serializer': lambda x: x.encode('utf-8') if x else None,
        'value_serializer': lambda x: x.encode('utf-8')
    }
    return KafkaProducer(**producer_config)


def prepare_db_connection() -> Tuple[psycopg2.extensions.connection, str]:
    """
    Create PostgreSQL database connection using environment variables.
    
    Returns:
        Tuple of (connection, jdbc_url)
    """
    db_host = os.getenv("DATABASE_HOST", "localhost")
    db_port = os.getenv("DATABASE_PORT", "5432")
    db_name = os.getenv("DATABASE_NAME", "service-rental-demo")
    db_username = os.getenv("DATABASE_USERNAME", "admin")
    db_password = os.getenv("DATABASE_PASSWORD", "admin")
    
    jdbc_url = f"postgresql://{db_host}:{db_port}/{db_name}"
    
    connection = psycopg2.connect(
        host=db_host,
        port=db_port,
        database=db_name,
        user=db_username,
        password=db_password
    )
    
    return connection, jdbc_url


def random_payload_generator_situation(connection: psycopg2.extensions.connection, 
                                     producer: KafkaProducer) -> None:
    """
    Generate random station status messages for multiple stations.
    
    Args:
        connection: PostgreSQL database connection
        producer: Kafka producer instance
    """
    # Fetch station IDs from database
    cursor = connection.cursor()
    cursor.execute("SELECT id FROM stations LIMIT 100")
    station_ids = [row[0] for row in cursor.fetchall()]
    cursor.close()
    
    topic = os.getenv("KAFKA_TOPIC", "cf.iot.station.status")
    delay = int(os.getenv("DELAY", "300")) / 1000.0  # Convert ms to seconds
    
    print(f"Start sending station log with [topic={topic}, delay={delay*1000}ms]")
    
    for iteration in range(1000):
        random.shuffle(station_ids)
        for station_id in station_ids:
            print(f"Start sending data to topic {topic}")
            
            station_status = StationStatus(
                station_id=str(station_id),
                status="on" if twenty_five_percent_is_true() else "off"
            )
            
            message = json.dumps(station_status.to_dict())
            producer.send(topic, value=message)
            
            print(f"Send station[id={station_status.id},status={station_status.status}] successfully")
            time.sleep(delay)
    
    print("End sending station log")
    producer.flush()
    producer.close()


def one_station_payload_generator_situation(connection: psycopg2.extensions.connection,
                                          producer: KafkaProducer) -> None:
    """
    Generate status messages for a single station.
    
    Args:
        connection: PostgreSQL database connection
        producer: Kafka producer instance
    """
    # Fetch one station ID from database
    cursor = connection.cursor()
    cursor.execute("SELECT id FROM stations LIMIT 1")
    station_id = str(cursor.fetchone()[0])
    cursor.close()
    
    topic = os.getenv("KAFKA_TOPIC", "cf.iot.station.status")
    delay = int(os.getenv("DELAY", "300")) / 1000.0  # Convert ms to seconds
    
    print(f"Start sending station log with [topic={topic}, delay={delay*1000}ms]")
    
    for i in range(10):
        print(f"Start sending data to topic {topic}")
        
        station_status = StationStatus(
            station_id=station_id,
            status="off"
        )
        
        message = json.dumps(station_status.to_dict())
        producer.send(topic, value=message)
        
        timestamp = datetime.now().isoformat()
        print(f"Send station[id={station_status.id},status={station_status.status}], timestamp={timestamp} successfully")
        time.sleep(0.5)  # 500ms delay as in Kotlin version
    
    print("End sending station log")
    producer.flush()
    producer.close()


def main():
    """Main function - entry point of the application."""
    kafka_host = os.getenv("KAFKA_HOST", "localhost")
    kafka_port = os.getenv("KAFKA_PORT", "19092")
    
    # Create Kafka producer
    producer = prepare_producer_config(kafka_host, kafka_port)
    
    # Create database connection
    connection, jdbc_url = prepare_db_connection()
    
    print(f"Connect {jdbc_url} successfully")
    print(f"Connect {kafka_host}:{kafka_port} successfully")
    
    # Determine mode and run appropriate generator
    mode = os.getenv("MODE", "RANDOM_GEN")
    
    if mode == "RANDOM_GEN":
        random_payload_generator_situation(connection, producer)
    elif mode == "ONE_GEN":
        one_station_payload_generator_situation(connection, producer)
    else:
        print(f"We do not have mode {mode}")
    
    # Clean up
    connection.close()


if __name__ == "__main__":
    main()
