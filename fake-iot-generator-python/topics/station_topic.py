"""
Station topic handler for the fake IoT generator.
"""

import json
import os
from kafka import KafkaProducer
from generators.station_generator import StationStatus


class StationTopic:
    """Handler for station status topic."""

    def __init__(self, producer: KafkaProducer):
        self.producer = producer
        self.topic_name = os.getenv("KAFKA_TOPIC", "cf.iot.station.status")

    def send_message(self, station_status: StationStatus) -> None:
        """
        Send a station status message to the topic.

        Args:
            station_status: StationStatus object to send
        """
        message = json.dumps(station_status.to_dict())
        self.producer.send(self.topic_name, value=message)

    def close(self) -> None:
        """Close the producer connection."""
        self.producer.flush()
        self.producer.close()
