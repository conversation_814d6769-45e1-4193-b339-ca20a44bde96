"""
Customer topic handler for the fake IoT generator.
"""

import json
import os
from kafka import KafkaProducer
from generators.customer_generator import CustomerActivity


class CustomerTopic:
    """Handler for customer activity topic."""

    def __init__(self, producer: KafkaProducer):
        self.producer = producer
        self.topic_name = os.getenv(
            "KAFKA_CUSTOMER_TOPIC", "cf.iot.customer.activity")

    def send_message(self, customer_activity: CustomerActivity) -> None:
        """
        Send a customer activity message to the topic.

        Args:
            customer_activity: CustomerActivity object to send
        """
        message = json.dumps(customer_activity.to_dict())
        self.producer.send(self.topic_name, value=message)

    def close(self) -> None:
        """Close the producer connection."""
        self.producer.flush()
        self.producer.close()
