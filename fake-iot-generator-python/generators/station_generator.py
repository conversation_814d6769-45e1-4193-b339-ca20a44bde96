"""
Station data generator for the fake IoT generator.
"""

import json
import os
import random
import time
import uuid
from datetime import datetime
from typing import List

import psycopg2
from utils.random_utils import twenty_five_percent_is_true
from topics.station_topic import StationTopic


class StationStatus:
    """Data class representing a station status message."""

    def __init__(self, station_id: str = None, status: str = "off"):
        self.id = station_id if station_id else str(uuid.uuid4())
        self.status = status

    def to_dict(self):
        """Convert to dictionary for JSON serialization."""
        return {
            "id": self.id,
            "status": self.status
        }


class StationGenerator:
    """Generator for station status messages."""

    def __init__(self, connection: psycopg2.extensions.connection):
        self.connection = connection

    def generate_random_payloads(self, topic: StationTopic) -> None:
        """
        Generate random station status messages for multiple stations.

        Args:
            topic: Station topic instance for sending messages
        """
        # Fetch station IDs from database
        cursor = self.connection.cursor()
        cursor.execute("SELECT id FROM stations LIMIT 100")
        station_ids = [row[0] for row in cursor.fetchall()]
        cursor.close()

        delay = int(os.getenv("DELAY", "300")) / \
            1000.0  # Convert ms to seconds

        print(f"Start sending station log with [delay={delay*1000}ms]")

        for iteration in range(1000):
            random.shuffle(station_ids)
            for station_id in station_ids:
                print(f"Start sending data to topic {topic.topic_name}")

                station_status = StationStatus(
                    station_id=str(station_id),
                    status="on" if twenty_five_percent_is_true() else "off"
                )

                topic.send_message(station_status)
                print(
                    f"Send station[id={station_status.id},status={station_status.status}] successfully")
                time.sleep(delay)

        print("End sending station log")
        topic.close()

    def generate_one_station_payloads(self, topic: StationTopic) -> None:
        """
        Generate status messages for a single station.

        Args:
            topic: Station topic instance for sending messages
        """
        # Fetch one station ID from database
        cursor = self.connection.cursor()
        cursor.execute("SELECT id FROM stations LIMIT 1")
        station_id = str(cursor.fetchone()[0])
        cursor.close()

        delay = int(os.getenv("DELAY", "300")) / \
            1000.0  # Convert ms to seconds

        print(f"Start sending station log with [delay={delay*1000}ms]")

        for i in range(10):
            print(f"Start sending data to topic {topic.topic_name}")

            station_status = StationStatus(
                station_id=station_id,
                status="off"
            )

            topic.send_message(station_status)
            timestamp = datetime.now().isoformat()
            print(
                f"Send station[id={station_status.id},status={station_status.status}], timestamp={timestamp} successfully")
            time.sleep(0.5)  # 500ms delay as in Kotlin version

        print("End sending station log")
        topic.close()
