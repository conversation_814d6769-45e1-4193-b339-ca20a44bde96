"""
Customer data generator for the fake IoT generator.
"""

import json
import os
import random
import time
import uuid
from datetime import datetime
from typing import List

import psycopg2
from utils.random_utils import twenty_five_percent_is_true
from topics.customer_topic import CustomerTopic


class CustomerActivity:
    """Data class representing a customer activity message."""

    def __init__(self, customer_id: str = None, activity_type: str = "login", station_id: str = None):
        self.id = customer_id if customer_id else str(uuid.uuid4())
        self.activity_type = activity_type
        self.station_id = station_id
        self.timestamp = datetime.now().isoformat()

    def to_dict(self):
        """Convert to dictionary for JSON serialization."""
        return {
            "id": self.id,
            "activity_type": self.activity_type,
            "station_id": self.station_id,
            "timestamp": self.timestamp
        }


class CustomerGenerator:
    """Generator for customer activity messages."""

    def __init__(self, connection: psycopg2.extensions.connection):
        self.connection = connection

    def generate_random_customer_activities(self, topic: CustomerTopic) -> None:
        """
        Generate random customer activity messages.

        Args:
            topic: Customer topic instance for sending messages
        """
        # Fetch customer IDs from database
        cursor = self.connection.cursor()
        cursor.execute("SELECT id FROM customers LIMIT 50")
        customer_ids = [row[0] for row in cursor.fetchall()]
        cursor.close()

        # Fetch station IDs for customer activities
        cursor = self.connection.cursor()
        cursor.execute("SELECT id FROM stations LIMIT 20")
        station_ids = [row[0] for row in cursor.fetchall()]
        cursor.close()

        activity_types = ["login", "logout",
                          "rental_start", "rental_end", "payment"]
        delay = int(os.getenv("DELAY", "500")) / \
            1000.0  # Convert ms to seconds

        print(
            f"Start sending customer activity log with [delay={delay*1000}ms]")

        for iteration in range(500):
            random.shuffle(customer_ids)
            for customer_id in customer_ids:
                print(
                    f"Start sending customer data to topic {topic.topic_name}")

                customer_activity = CustomerActivity(
                    customer_id=str(customer_id),
                    activity_type=random.choice(activity_types),
                    station_id=random.choice(station_ids) if random.choice(
                        [True, False]) else None
                )

                topic.send_message(customer_activity)
                print(
                    f"Send customer[id={customer_activity.id},activity={customer_activity.activity_type}] successfully")
                time.sleep(delay)

        print("End sending customer activity log")
        topic.close()

    def generate_single_customer_activity(self, topic: CustomerTopic) -> None:
        """
        Generate activity messages for a single customer.

        Args:
            topic: Customer topic instance for sending messages
        """
        # Fetch one customer ID from database
        cursor = self.connection.cursor()
        cursor.execute("SELECT id FROM customers LIMIT 1")
        customer_id = str(cursor.fetchone()[0])
        cursor.close()

        delay = int(os.getenv("DELAY", "500")) / \
            1000.0  # Convert ms to seconds

        print(
            f"Start sending single customer activity log with [delay={delay*1000}ms]")

        for i in range(10):
            print(f"Start sending customer data to topic {topic.topic_name}")

            customer_activity = CustomerActivity(
                customer_id=customer_id,
                activity_type="login"
            )

            topic.send_message(customer_activity)
            timestamp = datetime.now().isoformat()
            print(
                f"Send customer[id={customer_activity.id},activity={customer_activity.activity_type}], timestamp={timestamp} successfully")
            time.sleep(0.5)  # 500ms delay

        print("End sending single customer activity log")
        topic.close()
