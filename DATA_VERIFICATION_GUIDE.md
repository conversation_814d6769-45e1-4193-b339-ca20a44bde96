# Data Verification Guide

This guide provides step-by-step instructions to verify that data is flowing correctly through the entire pipeline from the Python IoT generator to ClickHouse.

## 🔍 Step-by-Step Verification Process

### Step 1: Verify All Services Are Running

```bash
# Check all containers are up and healthy
docker-compose ps

# Expected output should show all services as "Up" or "Up (healthy)"
# - postgres: Up
# - redpanda: Up (healthy)  
# - jobmanager: Up
# - taskmanager: Up
# - fake-iot-generator: Up
# - clickhouse: Up
# - debezium: Up
# - grafana: Up
```

### Step 2: Verify Python IoT Generator is Producing Data

```bash
# Monitor the Python generator logs in real-time
docker-compose logs fake-iot-generator -f

# You should see output like:
# fake-iot-generator  | Connect postgresql://postgres:5432/service-rental-demo successfully
# fake-iot-generator  | Connect redpanda:9092 successfully
# fake-iot-generator  | Start sending station log with [topic=cf.iot.station.status, delay=200.0ms]
# fake-iot-generator  | Send station[id=6043580e-8a25-3389-9548-247eddefddf5,status=on] successfully
# fake-iot-generator  | Send station[id=1217a8be-112f-36f2-b58b-f432bb8b03c4,status=off] successfully
```

**What to Look For:**
- ✅ Successful database connection
- ✅ Successful Kafka connection  
- ✅ Continuous message sending every 200ms
- ✅ Station IDs are UUIDs
- ✅ Status values are "on" or "off"

### Step 3: Verify Kafka is Receiving Messages

```bash
# List all Kafka topics
docker-compose exec redpanda rpk topic list

# You should see:
# cf.iot.station.status

# Consume messages from the IoT topic (Ctrl+C to stop)
docker-compose exec redpanda rpk topic consume cf.iot.station.status --num 10

# Expected output:
# {
#   "partition": 0,
#   "offset": 1234,
#   "value": "{\"id\":\"6043580e-8a25-3389-9548-247eddefddf5\",\"status\":\"on\"}"
# }
```

**What to Look For:**
- ✅ Topic `cf.iot.station.status` exists
- ✅ Messages contain JSON with "id" and "status" fields
- ✅ Station IDs match those from the generator logs
- ✅ Status values are "on" or "off"

### Step 4: Verify Flink Jobs Are Running

```bash
# Check Flink job status via REST API
curl -s http://localhost:8081/jobs | jq '.jobs[] | {id: .id, status: .status}'

# Or visit Flink Web UI
open http://localhost:8081
```

**Expected Output:**
```json
{
  "id": "395c90d10dd3077337e22433cce13a4b",
  "status": "RUNNING"
}
{
  "id": "4317459edfd9cda7684a44235776d1b0", 
  "status": "RUNNING"
}
```

**What to Look For:**
- ✅ Two jobs should be running (CDC and IoT)
- ✅ Both jobs status should be "RUNNING"
- ✅ No failed or cancelled jobs

### Step 5: Verify ClickHouse Database and Tables

```bash
# Check ClickHouse is accessible
docker-compose exec clickhouse clickhouse-client --query "SELECT version()"

# List all databases
docker-compose exec clickhouse clickhouse-client --query "SHOW DATABASES"

# Expected output should include:
# cf
# default
# system

# List tables in the 'cf' database
docker-compose exec clickhouse clickhouse-client --query "SHOW TABLES FROM cf"

# Expected output:
# station_status
# station_status_merge_tree  
# station_status_view
```

**What to Look For:**
- ✅ ClickHouse responds to queries
- ✅ Database `cf` exists
- ✅ Tables related to station_status exist

### Step 6: Verify Data is Being Written to ClickHouse

```bash
# Check total record count (should be increasing)
docker-compose exec clickhouse clickhouse-client --query "SELECT COUNT(*) FROM cf.station_status_merge_tree"

# Wait 30 seconds and check again - count should increase
sleep 30
docker-compose exec clickhouse clickhouse-client --query "SELECT COUNT(*) FROM cf.station_status_merge_tree"

# Check table structure
docker-compose exec clickhouse clickhouse-client --query "DESCRIBE cf.station_status_merge_tree"

# Expected output:
# station_id      String
# event_date      DateTime  
# offline_count   UInt32
```

**What to Look For:**
- ✅ Record count is greater than 0
- ✅ Record count increases over time
- ✅ Table has expected columns

### Step 7: Verify Data Content and Freshness

```bash
# View latest 10 records
docker-compose exec clickhouse clickhouse-client --query "SELECT * FROM cf.station_status_merge_tree ORDER BY event_date DESC LIMIT 10"

# Expected output format:
# station_id                              event_date           offline_count
# 0fe8d0c8-2076-3de6-95c8-ee9672c192bd   2025-09-01 11:09:40  2
# 1217a8be-112f-36f2-b58b-f432bb8b03c4   2025-09-01 11:09:40  1

# Check data freshness (should be within last few minutes)
docker-compose exec clickhouse clickhouse-client --query "SELECT MAX(event_date) as latest_data, COUNT(*) as total_records FROM cf.station_status_merge_tree"

# Check unique stations
docker-compose exec clickhouse clickhouse-client --query "SELECT COUNT(DISTINCT station_id) as unique_stations FROM cf.station_status_merge_tree"
```

**What to Look For:**
- ✅ Station IDs match those from generator logs
- ✅ Event dates are recent (within last few minutes)
- ✅ Multiple unique station IDs (should be 6-7 stations)
- ✅ Offline counts are positive integers

## 🚨 Troubleshooting Common Issues

### Issue 1: No Data in ClickHouse

**Symptoms:**
```bash
docker-compose exec clickhouse clickhouse-client --query "SELECT COUNT(*) FROM cf.station_status_merge_tree"
# Returns: 0
```

**Diagnosis Steps:**
```bash
# 1. Check if Flink jobs are running
curl -s http://localhost:8081/jobs | jq '.jobs[] | {status: .status}'

# 2. Check Flink job logs for errors
docker-compose logs jobmanager --tail=50 | grep ERROR
docker-compose logs taskmanager --tail=50 | grep ERROR

# 3. Verify Kafka has messages
docker-compose exec redpanda rpk topic consume cf.iot.station.status --num 5
```

**Solutions:**
- Restart Flink jobs if they're not running
- Check network connectivity between services
- Verify ClickHouse is accessible from Flink

### Issue 2: Python Generator Not Sending Data

**Symptoms:**
```bash
docker-compose logs fake-iot-generator --tail=10
# Shows connection errors or no recent messages
```

**Diagnosis Steps:**
```bash
# 1. Check container status
docker-compose ps fake-iot-generator

# 2. Check database connectivity
docker-compose exec fake-iot-generator python -c "import psycopg2; print('DB OK')"

# 3. Check Kafka connectivity  
docker-compose exec fake-iot-generator python -c "from kafka import KafkaProducer; print('Kafka OK')"
```

**Solutions:**
- Restart the generator: `docker-compose restart fake-iot-generator`
- Check PostgreSQL is running and accessible
- Verify Kafka/Redpanda is healthy

### Issue 3: Flink Jobs Not Processing Data

**Symptoms:**
- Jobs show as "RUNNING" but no data in ClickHouse
- High backpressure or low throughput in Flink UI

**Diagnosis Steps:**
```bash
# 1. Check Flink Web UI for backpressure
open http://localhost:8081

# 2. Check TaskManager logs
docker-compose logs taskmanager --tail=100

# 3. Verify Kafka consumer lag
docker-compose exec redpanda rpk group describe test-group
```

**Solutions:**
- Increase TaskManager resources
- Check for serialization issues
- Restart Flink cluster if needed

## ✅ Success Criteria Checklist

Use this checklist to verify your system is working correctly:

- [ ] All 8 Docker containers are running
- [ ] Python generator shows successful connections and continuous message sending
- [ ] Kafka topic `cf.iot.station.status` contains JSON messages
- [ ] Two Flink jobs are in "RUNNING" status
- [ ] ClickHouse database `cf` contains 3 tables
- [ ] `cf.station_status_merge_tree` has increasing record count
- [ ] Latest data in ClickHouse is within last 5 minutes
- [ ] Multiple unique station IDs are present in data
- [ ] Flink Web UI shows healthy job metrics
- [ ] No ERROR messages in service logs

## 📊 Performance Benchmarks

**Expected Performance (on 8GB RAM system):**

| Metric | Expected Value |
|--------|----------------|
| Message Generation Rate | ~5 messages/second |
| Kafka Throughput | ~300 messages/minute |
| Flink Processing Latency | <1 second |
| ClickHouse Write Rate | ~50 records/minute |
| End-to-End Latency | <5 seconds |

**Monitoring Commands:**
```bash
# Monitor message rate
docker-compose logs fake-iot-generator | grep "Send station" | tail -20

# Check Kafka lag
docker-compose exec redpanda rpk group describe test-group

# Monitor ClickHouse growth
watch -n 5 'docker-compose exec clickhouse clickhouse-client --query "SELECT COUNT(*) FROM cf.station_status_merge_tree"'
```

This verification guide ensures your Flink SQL real-time data processing pipeline is working correctly from end to end.
